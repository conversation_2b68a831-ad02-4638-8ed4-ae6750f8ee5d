// UI Controls Management
class UIControls {
    constructor(weatherLayers, legendManager, timelineManager) {
        this.weatherLayers = weatherLayers;
        this.legendManager = legendManager;
        this.timelineManager = timelineManager;
    }

    // Show status message
    showStatus(message, type = 'info', duration = 3000) {
        const statusEl = document.getElementById('statusMessage');
        if (statusEl) {
            statusEl.textContent = message;
            statusEl.className = `status-message show ${type}`;
            
            setTimeout(() => {
                statusEl.classList.remove('show');
            }, duration);
        }
    }

    // Check if mobile screen
    isMobile() {
        return window.matchMedia("(max-width: 575.98px)").matches;
    }

    // Setup layer controls
    setupLayerControls(map) {
        const layerButtons = document.querySelectorAll('.layer-btn');
        
        layerButtons.forEach(button => {
            button.addEventListener('click', () => {
                const layerType = button.dataset.layer;
                
                // Mobile label animation
                if (this.isMobile()) {
                    button.classList.add('show-label');
                    setTimeout(() => {
                        button.classList.remove('show-label');
                    }, 3000);
                }
                
                // Remove active class from all buttons
                layerButtons.forEach(btn => btn.classList.remove('active'));
                
                // Add active class to clicked button
                button.classList.add('active');
                
                // Toggle layer in weather layers manager
                this.weatherLayers.toggleLayer(map, layerType);
                
                // Update current layer display
                const currentLayerEl = document.getElementById('currentLayer');
                if (currentLayerEl) {
                    currentLayerEl.textContent = layerType.charAt(0).toUpperCase() + layerType.slice(1);
                }
                
                // Update legend
                this.legendManager.updateLegendScale(layerType);
                
                // Update opacity control
                this.updateOpacityControl(layerType);
                
                // Update timeline
                this.timelineManager.updateTimeDisplay();
                this.timelineManager.syncTimelineWithActiveLayer();
                
                this.showStatus(`✅ Đã chọn lớp ${layerType}`, 'success', 2000);
            });
        });
        
        // Initialize with wind layer
        this.weatherLayers.activeLayers.add('wind');
        
        // Delay to ensure wind layer is created
        setTimeout(() => {
            this.legendManager.updateLegendScale('wind');
            this.updateOpacityControl('wind');
        }, 500);
        
        // Update button states
        document.querySelectorAll('.layer-btn').forEach(btn => btn.classList.remove('active'));
        const windButton = document.querySelector('[data-layer="wind"]');
        if (windButton) {
            windButton.classList.add('active');
        }
        
        // Add and setup initial wind layer
        setTimeout(() => {
            const windLayer = this.weatherLayers.getLayer('wind');
            if (windLayer && map) {
                this.weatherLayers.addLayerToMap(map, 'wind');
                this.weatherLayers.showLayer('wind', CONFIG.DEFAULT_OPACITY);
                
                // Setup timeline with current time
                this.timelineManager.setupTimelineWithCurrentTime(windLayer);
            }
        }, 1000);
    }

    // Setup opacity controls
    setupOpacityControls() {
        const opacitySlider = document.getElementById('opacitySlider');
        
        if (!opacitySlider) {
            console.warn('Opacity slider not found');
            return;
        }
        
        // Update opacity when slider changes
        opacitySlider.addEventListener('input', (e) => {
            const opacity = parseInt(e.target.value) / 100;
            
            // Apply opacity to active layer
            const activeLayerType = this.weatherLayers.getActiveLayerType();
            if (activeLayerType) {
                this.weatherLayers.setLayerOpacity(activeLayerType, opacity);
                this.showStatus(`🎨 ${activeLayerType} opacity: ${e.target.value}%`, 'info', 1000);
            }
        });
    }

    // Update opacity control for active layer
    updateOpacityControl(layerType) {
        const opacitySlider = document.getElementById('opacitySlider');
        
        if (layerType && opacitySlider) {
            // Reset to 75% for new layer
            opacitySlider.value = 75;
            
            // Apply 75% opacity immediately to the layer
            this.weatherLayers.setLayerOpacity(layerType, CONFIG.DEFAULT_OPACITY);
        }
    }

    // Setup all UI controls
    setupAllControls(map) {
        this.setupLayerControls(map);
        this.setupOpacityControls();
        this.legendManager.setupUnitsToggle();
        this.timelineManager.setupAnimationControls();
        
        // Make showStatus available globally
        window.showStatus = this.showStatus.bind(this);
        
        console.log('✅ All UI controls setup complete');
    }

    // Handle window resize
    handleResize() {
        // UI controls are responsive via CSS, no JavaScript needed
        console.log('UI controls resize handled by CSS');
    }
}

// Export
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UIControls;
} else {
    window.UIControls = UIControls;
} 