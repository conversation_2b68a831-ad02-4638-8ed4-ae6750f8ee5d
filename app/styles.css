/* Weather App Vietnam - Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f8f9fa;
    color: #333;
    overflow: hidden;
    margin: 0;
    padding: 0;
}

.app-container {
    position: relative;
    height: 100vh;
    width: 100vw;
}

/* ===== RESPONSIVE DESIGN - MapTiler Style ===== */

/* ===== CSS VARIABLES FOR DYNAMIC POSITIONING ===== */
:root {
    --timeline-height: 80px;
    --scale-bottom: 105px;
    --scale-height: 250px;
    --control-gap: 10px;
    --control-width: 45px;
    --unit-height: 40px;
    --opacity-height: 120px;
}

/* ===== BASE DESIGN - FIXED 10PX FROM EDGES ===== */
.layer-controls {
    position: absolute;
    left: 10px;
    bottom: 70px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.layer-btn {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    padding: 6px 10px;
    min-width: 100px;
    height: 45px;
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 1px 6px rgba(0,0,0,0.1);
    backdrop-filter: blur(8px);
    font-size: 12px;
    font-weight: 500;
}

.layer-btn.active {
    background: #3174ff;
    color: white;
    border-color: transparent;
}

.layer-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 10px rgba(0,0,0,0.15);
}

.layer-btn .icon {
    font-size: 14px;
    flex-shrink: 0;
}

.layer-btn .text {
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    transition: all 0.2s ease;
}

/* Opacity Control - Computed position using CSS calc() */
.opacity-control {
    position: absolute;
    right: 10px;
    bottom: calc(var(--scale-bottom) + var(--scale-height) + var(--control-gap) + var(--unit-height) + var(--control-gap));
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    padding: 10px;
    border-radius: 6px;
    backdrop-filter: blur(8px);
    box-shadow: 0 1px 6px rgba(0,0,0,0.1);
    width: var(--control-width);
    height: var(--opacity-height);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

/* Opacity Icon - Top of control */
.opacity-icon {
    font-size: 16px;
    color: #3174ff;
    flex-shrink: 0;
}

/* Opacity Slider - Vertical */
.opacity-slider {
    writing-mode: bt-lr; /* For Firefox */
    -webkit-appearance: slider-vertical; /* For Chrome/Safari */
    width: 20px;
    height: 80px;
    background: transparent;
    outline: none;
    flex: 1;
}

/* Vertical slider track for WebKit browsers */
.opacity-slider::-webkit-slider-track {
    width: 4px;
    height: 80px;
    background: #ddd;
    border-radius: 2px;
    border: none;
}

/* Vertical slider thumb for WebKit browsers */
.opacity-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 14px;
    height: 14px;
    background: #3174ff;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 1px 3px rgba(0,0,0,0.3);
}

/* Firefox vertical slider */
.opacity-slider::-moz-range-track {
    width: 4px;
    height: 80px;
    background: #ddd;
    border-radius: 2px;
    border: none;
}

.opacity-slider::-moz-range-thumb {
    width: 14px;
    height: 14px;
    background: #3174ff;
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 1px 3px rgba(0,0,0,0.3);
}

/* Legend Container - Ẩn vì đã tách thành các component riêng */
.legend-container {
    display: none;
}

.legend {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 6px;
}

/* Legend Canvas - Kích thước cố định cho mọi thiết bị */
.legend canvas {
    width: 40px !important;
    height: 160px !important;
}

/* Timeline Container - Cách màn hình 2 bên 150px */
.timeline-container {
    position: absolute;
    left: 150px;
    right: 150px;
    bottom: 10px;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    padding: 16px;
    border-radius: 8px;
    backdrop-filter: blur(12px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    height: 80px;
    display: grid;
    grid-template-columns: auto 1fr auto;
    align-items: center;
    gap: 16px;
}

/* ===== SIMPLIFIED 2-BREAKPOINT RESPONSIVE SYSTEM ===== */

/* BREAKPOINT 0.5: Mobile nhỏ (≤480px) - Margin tối thiểu */
@media (max-width: 480px) {
    .timeline-container {
        left: 10px;
        right: 10px;
    }
}

/* BREAKPOINT 1: Mobile (≤768px) - Square Icon Buttons */
@media (max-width: 768px) {
    /* Layer controls - FIXED left: 10px, no jumping */
    .layer-controls {
        left: 10px;
        bottom: 105px;
    }
    
    /* Layer buttons become squares with icons only */
    .layer-btn {
        min-width: var(--control-width);
        width: var(--control-width);
        height: var(--control-width);
        padding: 11px;
        justify-content: center;
    }
    
    .layer-btn .text {
        display: none; /* Hide text on mobile */
    }
    
    .layer-btn .icon {
        font-size: 16px;
    }
    
    /* Controls sử dụng CSS variables tự động - không cần override */
    

    
    .legend-text {
        font-size: 10px;
        padding: 10px 0;
    }
    
    /* Timeline Container responsive trên mobile - Giảm margin trên mobile */
    .timeline-container {
        left: 20px;
        right: 20px;
        padding: 12px;
        gap: 12px;
        grid-template-columns: auto 1fr auto;
        /* Fix dropdown position on mobile */
        transform: translateZ(0);
        contain: layout;
    }
    
    .timeline-play-btn {
        width: 36px;
        height: 36px;
        font-size: 14px;
    }
    
    .timeline-track {
        height: 36px;
    }
    
    .marker-dot {
        width: 8px;
        height: 8px;
        top: 0;
    }
    
    .marker-label {
        font-size: 9px;
        top: 16px;
    }
    
        .timeline-slider::-webkit-slider-thumb {
        width: 18px;
        height: 18px;
        /* Mobile thumb alignment */
        margin-top: -7px;
    }

    .timeline-slider::-moz-range-thumb {
        width: 18px;
        height: 18px;
        /* Mobile thumb alignment */
        margin-top: -7px;
    }
    
    .time-tooltip {
        padding: 6px 8px;
        margin-bottom: 6px;
    }
    
    .tooltip-content {
        font-size: 11px;
    }
    
    /* Mobile time step control styles đã được thay thế bằng custom dropdown */
    
    .timeline-buttons {
        gap: 4px;
    }
    
    .timeline-btn {
        padding: 4px 6px;
        font-size: 11px;
        height: 24px;
    }
}

/* BREAKPOINT 1.5: Tablet (481px - 768px) - Medium margin cho timeline */
@media (min-width: 481px) and (max-width: 768px) {
    .timeline-container {
        left: 50px;
        right: 50px;
    }
}

/* BREAKPOINT 2: Desktop (≥769px) - Full Buttons with Text */
@media (min-width: 769px) {
    /* Layer controls - FIXED left: 10px, no jumping */
    .layer-controls {
        left: 10px;
        bottom: 105px;
    }
    
    /* Layer buttons show text */
    .layer-btn {
        min-width: 100px;
        height: 45px;
        padding: 11px 15px;
        justify-content: flex-start;
    }
    
    .layer-btn .text {
        display: block;
    }
    
    /* Controls sử dụng CSS variables tự động - không cần override */
    
    .legend {
        padding: 12px;
    }
    
    .legend-text {
        font-size: 11px;
        padding: 12px 0;
    }
    
    /* Timeline Container responsive trên desktop - Giữ nguyên 150px margin */
    .timeline-container {
        left: 150px;
        right: 150px;
        height: 80px;
        padding: 16px;
        gap: 16px;
        grid-template-columns: auto 1fr auto;
    }
}

/* ===== ANIMATIONS & TRANSITIONS ===== */

/* Smooth transitions for all responsive changes */
.layer-btn,
.legend,
.legend-container,
.opacity-control,
.unit-control,
.scale-control,
.timeline-control {
    transition: all 0.2s ease;
}

/* Current Layer Display */
.current-layer {
    position: fixed;
    top: 15px;
    left: 15px;
    z-index: 1000;
    color: white;
    font-size: 20px;
    font-weight: 500;
    text-shadow: 0 0 10px rgba(0,0,0,0.7);
    text-transform: capitalize;
}

/* Weather Data Tooltip - Below cursor */
.hover-tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    z-index: 1000;
    pointer-events: none;
    white-space: nowrap;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    transform: translate(10px, 10px); /* Below cursor */
    opacity: 0;
    transition: opacity 0.2s ease;
}

.hover-tooltip.visible {
    opacity: 1;
}

/* Static Marker Labels */
.static-marker-label {
    white-space: nowrap;
    transition: transform 0.2s ease;
}

.static-marker-label:hover {
    transform: scale(1.05);
}

/* Popup Override */
.maplibregl-popup {
    z-index: 1003 !important;
}

/* Map Controls - Sử dụng control-width variable */
.maplibregl-ctrl-group button {
    width: var(--control-width) !important;
    height: var(--control-width) !important;
}

.maplibregl-ctrl-group {
    border-radius: 6px !important;
}

/* Animation Controls */
.animation-controls {
    text-align: center;
}

.play-button {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 25px;
    width: 50px;
    height: 50px;
    font-size: 18px;
    cursor: pointer;
    margin: 10px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    transition: transform 0.2s ease;
}

.play-button:hover {
    transform: scale(1.1);
}

.radar-prev-btn, .radar-next-btn {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    margin: 0 2px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(240, 147, 251, 0.4);
}

.radar-prev-btn:hover, .radar-next-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(240, 147, 251, 0.6);
}

.speed-control {
    margin: 10px 0;
}

.speed-control input {
    width: 100%;
    margin: 5px 0;
}

/* Timeline Controls */
.timeline-controls {
    background: rgba(255,255,255,0.9);
    padding: 15px;
    border-radius: 8px;
    margin-top: 10px;
}

.timeline-slider {
    width: 100%;
    margin: 10px 0;
}

.time-display {
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 10px;
}

/* Units Toggle Button */
.units-toggle {
    background: rgba(255, 255, 255, 0.95);
    color: #6c757d;
    border: none;
    border-radius: 4px;
    padding: 10px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    width: var(--control-width);
    height: var(--control-width);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    backdrop-filter: blur(10px);
}

.units-toggle:hover {
    background: rgba(255, 255, 255, 1);
    color: #3174ff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.scale-bar {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 2px;
    padding: 1px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    backdrop-filter: blur(10px);
}

/* Timeline Content */
.timeline-content {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;
}

.timeline-play-btn {
    background: #3174ff;
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.timeline-play-btn:hover {
    background: #275dcc;
    transform: scale(1.05);
}

.time-display {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    min-width: 120px;
    text-align: center;
    flex-shrink: 0;
}

/* Mobile styles được định nghĩa trong media query ở trên */





/* Play/Pause Button */
.timeline-play-btn {
    background: #3174ff;
    color: white;
    border: none;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 12px rgba(49, 116, 255, 0.3);
    grid-column: 1;
}

.timeline-play-btn:hover {
    background: #275dcc;
    transform: scale(1.05);
    box-shadow: 0 4px 16px rgba(49, 116, 255, 0.4);
}

/* Timeline Track - CONTAINER CHÍNH */
.timeline-track {
    position: relative;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 48px;
    grid-column: 2;
}

/* 1. THANH TIMELINE-PROGRESS-LINE - ALIGN GIỮA CONTAINER */
.timeline-progress-line {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(to right, #e9ecef 0%, #e9ecef 100%);
    border-radius: 2px;
    transform: translateY(-50%);
    z-index: 1;
}

/* 2. NHÓM MARKER DOTS - ALIGN GIỮA CONTAINER */
.timeline-markers {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    width: 100%;
    height: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transform: translateY(-50%);
    z-index: 2;
}

.timeline-marker {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* Marker Dots - CHẤM TRÒN XANH */
.marker-dot {
    width: 12px;
    height: 12px;
    background: #3174ff;
    border: 2px solid white;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    position: relative;
    top: 0;
}

/* 3. NHÓM MARKER LABELS - DỊCH XUỐNG DƯỚI */
.marker-label {
    font-size: 11px;
    font-weight: 500;
    color: #666;
    white-space: nowrap;
    text-align: center;
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
}

/* 4. CONTAINER CHỨA THUMB - ALIGN GIỮA CONTAINER */
.timeline-slider {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    width: 100%;
    height: 4px;
    background: transparent;
    outline: none;
    cursor: pointer;
    appearance: none;
    transform: translateY(-50%);
    z-index: 3;
    /* Fix slider alignment */
    margin: 0;
    padding: 0;
    border: none;
    vertical-align: middle;
}

.timeline-slider::-webkit-slider-track {
    background: transparent;
    height: 4px;
    border: none;
    margin: 0;
    padding: 0;
}

.timeline-slider::-moz-range-track {
    background: transparent;
    height: 4px;
    border: none;
    margin: 0;
    padding: 0;
}

.timeline-slider::-webkit-slider-thumb {
    appearance: none;
    width: 24px;
    height: 24px;
    background: #3174ff;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(49, 116, 255, 0.4);
    border: 3px solid white;
    transition: all 0.2s ease;
    /* Reset thumb position để test alignment */
    margin-top: 0;
}

.timeline-slider::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(49, 116, 255, 0.5);
}

.timeline-slider::-moz-range-thumb {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #3174ff;
    cursor: pointer;
    border: 3px solid white;
    box-shadow: 0 4px 12px rgba(49, 116, 255, 0.4);
    transition: all 0.2s ease;
    /* Reset thumb position để test alignment */
    margin-top: 0;
}

.timeline-slider::-moz-range-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(49, 116, 255, 0.5);
}

/* 5. CONTAINER CHỨA VALUE NGÀY GIỜ - DỊCH LÊN TRÊN */
.time-tooltip {
    position: absolute;
    top: 50%;
    left: 20%;
    width: 100%;
    transform: translate(-50%, -150%);
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    padding: 8px 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    backdrop-filter: blur(8px);
    z-index: 4;
    opacity: 1;
    transition: opacity 0.2s ease;
    pointer-events: none;
    white-space: nowrap;
    width: auto;
}

.tooltip-content {
    font-size: 12px;
    font-weight: 600;
    color: #333;
    white-space: nowrap;
}

.tooltip-arrow {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid rgba(255, 255, 255, 0.95);
}

/* Time Step Control - Responsive Grid Position */
@media (min-width: 769px) {
    .time-step-control {
        grid-column: 3;
    }
}

/* Common styles for time step control container */
.time-step-control {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    height: 100%;
}



/* Map Container */
.map-container {
    position: relative;
    height: 100vh;
    width: 100vw;
}

#map {
    width: 100%;
    height: 100%;
}

/* Weather Info Display */
.weather-info {
    position: absolute;
    top: 20px;
    left: 20px;
    background: rgba(255,255,255,0.9);
    padding: 10px 15px;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    z-index: 500;
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
}

.pointer-data {
    position: absolute;
    top: 60px;
    left: 20px;
    background: rgba(255,255,255,0.9);
    padding: 8px 12px;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    z-index: 500;
    font-size: 14px;
    font-weight: 500;
    color: #4299e1;
    min-width: 120px;
    white-space: pre-line;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255,255,255,0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Status Messages */
.status-message {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(255,255,255,0.95);
    padding: 10px 15px;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    z-index: 1000;
    max-width: 300px;
    transform: translateX(320px);
    transition: transform 0.3s ease;
}

.status-message.show {
    transform: translateX(0);
}

.status-message.success {
    border-left: 4px solid #48bb78;
}

.status-message.error {
    border-left: 4px solid #f56565;
}

.status-message.info {
    border-left: 4px solid #4299e1;
}

/* ===== CUSTOM SELECT DROPDOWN STYLES ===== */

/* Container: Rất quan trọng để định vị menu options */
.custom-select-container {
    position: relative;
    width: 100px; /* Chiều rộng có thể điều chỉnh */
}

/* Nút bấm chính, trông giống như một ô select */
.custom-select-trigger {
    width: 100%;
    height: 100%;
    padding: 8px 12px;
    font-size: 12px;
    font-weight: 500;
    background: white;
    color: #333;
    border: 1px solid #ddd;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.custom-select-trigger:hover {
    border-color: #3174ff;
    box-shadow: 0 2px 8px rgba(49, 116, 255, 0.2);
}

/* Icon mũi tên */
.custom-select-trigger .fa-chevron-down {
    transition: transform 0.3s ease;
}

/* Xoay mũi tên khi menu mở */
.custom-select-container.open .custom-select-trigger .fa-chevron-down {
    transform: rotate(180deg);
}

/* Menu options: ẩn mặc định và định vị tuyệt đối */
.custom-select-options {
    position: absolute;
    left: 0;
    right: 0;
    bottom: calc(100% + 5px); /* Hiển thị menu BÊN TRÊN nút bấm */
    background: white;
    border: 1px solid #ddd;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 2000; /* Đảm bảo nằm trên các phần tử khác */
    
    /* Hiệu ứng */
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.2s ease;
}

/* Hiển thị menu khi container có class 'open' */
.custom-select-container.open .custom-select-options {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Từng option trong menu */
.custom-select-option {
    padding: 10px 12px;
    font-size: 12px;
    color: #333;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.custom-select-option:hover {
    background-color: #f0f4ff;
}

.custom-select-option:not(:last-child) {
    border-bottom: 1px solid #f0f0f0;
}

/* Unit Control - Chỉ container không có nền, button units-toggle tự có nền */
.unit-control {
    position: absolute;
    right: 10px;
    bottom: calc(var(--scale-bottom) + var(--scale-height) + var(--control-gap));
    z-index: 1000;
    width: var(--control-width);
    height: var(--unit-height);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

/* Scale Control - Nền trắng 45px chuẩn, canvas bên trong nhỏ hơn */
.scale-control {
    position: absolute;
    right: 10px;
    bottom: var(--scale-bottom);
    z-index: 1000;
    background: rgba(255, 255, 255, 0.15);
    padding: 2px;
    border-radius: 3px;
    backdrop-filter: blur(4px);
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    width: var(--control-width);
    height: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* Scale bar trong scale control - kích thước cố định */
.scale-control .scale-bar {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.scale-control .scale-bar canvas {
    width: calc(var(--control-width) - 4px) !important;
    height: var(--scale-height) !important;
} 