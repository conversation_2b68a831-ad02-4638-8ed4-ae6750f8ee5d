// Timeline and Animation Management
class TimelineManager {
    constructor(weatherLayers) {
        this.weatherLayers = weatherLayers;
        this.isPlaying = false;
        this.timeStep = 'hour';
        this.animationInterval = null;
        this.timelineInitialized = false;
    }

    // Setup timeline controls - ONLY ONCE
    setupTimelineControls(layer) {
        try {
            const timelineSlider = document.getElementById('timelineSlider');
            
            // Only setup event listener once to avoid conflicts
            if (!this.timelineInitialized) {
                console.log('Setting up timeline event listener...');
                
                // Clear any existing listeners
                timelineSlider.replaceWith(timelineSlider.cloneNode(true));
                const newTimelineSlider = document.getElementById('timelineSlider');
                
                // Simple event listener - only on change (not during drag)
                newTimelineSlider.addEventListener('change', (evt) => {
                    const sliderValue = parseFloat(evt.target.value);
                    
                    // Convert slider value to actual timestamp for weather API
                    const weatherTime = this.convertSliderValueToTimestamp(sliderValue);
                    
                    // Get active layer and update weather data
                    const activeLayer = this.weatherLayers.getActiveLayer();
                    if (activeLayer && activeLayer.setAnimationTime) {
                        // LOG 4: Call API khi user drag slider
                        console.log('📡 API Call (User Drag): setAnimationTime(' + weatherTime + ') = ' + new Date(weatherTime * 1000).toUTCString());
                        activeLayer.setAnimationTime(weatherTime);
                    }
                });

                // Update tooltip during input (smooth UI)
                newTimelineSlider.addEventListener('input', (evt) => {
                    const sliderValue = parseFloat(evt.target.value);
                    this.updateTooltipPosition(evt.target);
                    this.updateTooltipContent(sliderValue);
                });
                
                // Show/hide tooltip on mouse enter/leave
                newTimelineSlider.addEventListener('mouseenter', () => {
                    const tooltip = document.getElementById('timeTooltip');
                    if (tooltip) tooltip.style.opacity = '1';
                });
                
                newTimelineSlider.addEventListener('mouseleave', () => {
                    const tooltip = document.getElementById('timeTooltip');
                    if (tooltip) tooltip.style.opacity = '1'; // Keep visible always
                });
                
                this.timelineInitialized = true;
            }
            
            // Setup 4-day timeline (ignore layer dates)
            console.log('Setting up 4-day timeline...');
            
            // Always update day labels for 4-day timeline
            this.updateDayLabels(null, null);
            
            // Set slider to current time (not just today)
            this.resetTimelineToCurrentTime();
            
            this.refreshTime();
            
        } catch (error) {
            console.error('Timeline setup error:', error);
            if (window.showStatus) {
                window.showStatus('❌ Lỗi setup timeline', 'error', 2000);
            }
        }
    }

    // Setup timeline with CURRENT time (not end time)
    setupTimelineWithCurrentTime(layer) {
        try {
            const timelineSlider = document.getElementById('timelineSlider');
            if (!layer || !timelineSlider) return;
            
            const startDate = layer.getAnimationStartDate();
            const endDate = layer.getAnimationEndDate();
            
            if (startDate && endDate) {
                timelineSlider.min = +startDate;
                timelineSlider.max = +endDate;
                
                // Set to CURRENT time (gần nhất với now), không phải end time
                const now = new Date();
                let currentTime;
                
                if (now < startDate) {
                    currentTime = +startDate;
                } else if (now > endDate) {
                    currentTime = +endDate;
                } else {
                    currentTime = +now;
                }
                
                timelineSlider.value = currentTime;
                
                // Set layer time to current time
                layer.setAnimationTime(parseInt(currentTime / 1000));
                
                console.log('Timeline setup with current time:', new Date(currentTime));
                
                // Update display
                this.refreshTime();
            }
        } catch (error) {
            console.error('Setup timeline with current time error:', error);
        }
    }

    // Update time display for 4-day timeline
    refreshTime() {
        try {
            const timelineSlider = document.getElementById('timelineSlider');
            
            if (timelineSlider) {
                // Only set default if slider has no value (initial setup)
                if (!timelineSlider.value || timelineSlider.value === '' || timelineSlider.value === '0') {
                    this.resetTimelineToCurrentTime();
                } else {
                    // Keep current slider value, just update tooltip position
                    // KHÔNG GỌI updateTooltipContent ở đây vì có thể dùng giá trị cũ
                    this.updateTooltipPosition(timelineSlider);
                    // updateTooltipContent sẽ được gọi từ input event listener với giá trị đúng
                }
            }
        } catch (error) {
            console.error('RefreshTime error:', error);
        }
    }

    // Update tooltip position based on slider value
    updateTooltipPosition(slider) {
        try {
            const tooltip = document.getElementById('timeTooltip');
            if (!tooltip || !slider) return;

            const sliderRect = slider.getBoundingClientRect();
            const sliderTrack = slider.parentElement;
            const trackRect = sliderTrack.getBoundingClientRect();
            
            // Calculate percentage position
            const min = parseFloat(slider.min);
            const max = parseFloat(slider.max);
            const value = parseFloat(slider.value);
            const percentage = ((value - min) / (max - min)) * 100;
            
            // Update tooltip position
            tooltip.style.left = `${percentage}%`;
            
        } catch (error) {
            console.error('Update tooltip position error:', error);
        }
    }

    // Update tooltip position in real-time based on mouse position
    updateTooltipPositionRealtime(evt) {
        try {
            const tooltip = document.getElementById('timeTooltip');
            const slider = document.getElementById('timelineSlider');
            if (!tooltip || !slider) return;

            const sliderRect = slider.getBoundingClientRect();
            const mouseX = evt.clientX;
            
            // Calculate percentage based on mouse position
            const sliderLeft = sliderRect.left;
            const sliderWidth = sliderRect.width;
            const relativeX = mouseX - sliderLeft;
            const percentage = Math.max(0, Math.min(100, (relativeX / sliderWidth) * 100));
            
            // Update tooltip position immediately
            tooltip.style.left = `${percentage}%`;
            
            // Also calculate and update tooltip content based on mouse position
            const min = parseFloat(slider.min);
            const max = parseFloat(slider.max);
            const timeValue = min + ((max - min) * (percentage / 100));
            const previewDate = new Date(timeValue);
            
            // Update tooltip content with preview value
            this.updateTooltipContent(timeValue);
            
        } catch (error) {
            console.error('Update tooltip position realtime error:', error);
        }
    }

    // Convert current time to slider value (0-3) - BASED ON LOCAL TIME
    convertCurrentTimeToSliderValue() {
        try {
            const now = new Date();
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            
            // Calculate day offset from today (LOCAL TIME)
            const dayDiff = Math.floor((now.getTime() - today.getTime()) / (24 * 60 * 60 * 1000));
            
            // Calculate hour fraction within the day (LOCAL TIME)
            const hours = now.getHours();
            const minutes = now.getMinutes();
            const seconds = now.getSeconds();
            const hourFraction = (hours + minutes / 60 + seconds / 3600) / 24;
            
            // Convert to slider value (0-3)
            const sliderValue = (dayDiff + 1) + hourFraction;
            const clampedValue = Math.max(0, Math.min(3, sliderValue));
            
            return clampedValue;
            
        } catch (error) {
            console.error('Convert current time to slider value error:', error);
            return 1; // Fallback to today
        }
    }

    // Convert slider value (0-3) to timestamp for weather API
    convertSliderValueToTimestamp(sliderValue) {
        try {
            // Tính LOCAL TIME trước (giống tooltip)
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            
            const dayOffset = parseFloat(sliderValue) - 1; // Convert to -1, 0, 1, 2
            const targetDateLocal = new Date(today.getTime() + dayOffset * 24 * 60 * 60 * 1000);
            
            const fractionalPart = parseFloat(sliderValue) % 1;
            const hoursInDay = fractionalPart * 24;
            const hours = Math.floor(hoursInDay);
            const minutes = Math.floor((hoursInDay % 1) * 60);
            const seconds = Math.floor(((hoursInDay % 1) * 60 % 1) * 60);
            
            targetDateLocal.setHours(hours, minutes, seconds, 0);
            
            // CONVERT LOCAL TIME SANG UTC CHO API
            const targetDateUTC = new Date(targetDateLocal.getTime());
            
            // Return timestamp in seconds (weather API format)
            const timestamp = Math.floor(targetDateUTC.getTime() / 1000);
            
            // LOG 3: Time để làm input get data (UTC converted from local)
            console.log('🌦️ Weather API Time (UTC):', new Date(timestamp * 1000));
            
            return timestamp;
            
        } catch (error) {
            console.error('Convert slider value to timestamp error:', error);
            return Math.floor(Date.now() / 1000); // Fallback to current time
        }
    }

    // Convert timestamp (seconds) to slider value (0-3)
    convertTimestampToSliderValue(timestamp) {
        try {
            // Convert timestamp to Date object
            const date = new Date(timestamp * 1000);
            
            // Get today at 0h local time
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            
            // Calculate day difference from today
            const dayDiff = Math.floor((date.getTime() - today.getTime()) / (24 * 60 * 60 * 1000));
            
            // Calculate hour fraction within the day
            const hours = date.getHours();
            const minutes = date.getMinutes();
            const seconds = date.getSeconds();
            const hourFraction = (hours + minutes / 60 + seconds / 3600) / 24;
            
            // Convert to slider value (0-3)
            const sliderValue = (dayDiff + 1) + hourFraction;
            
            return sliderValue;
            
        } catch (error) {
            console.error('Convert timestamp to slider value error:', error);
            return 1; // Fallback to today
        }
    }

    // Reset timeline to current time (when switching layers)
    resetTimelineToCurrentTime() {
        try {
            const timelineSlider = document.getElementById('timelineSlider');
            if (!timelineSlider) return;
            
            // Calculate current time slider value
            const currentSliderValue = this.convertCurrentTimeToSliderValue();
            
            // LOG 1: Time lúc reset hoặc reload layer
            const now = new Date();
            console.log('🔄 Reset Time:', now);
            
            // SET SLIDER VALUE FIRST - này sẽ trigger input event với giá trị đúng
            timelineSlider.value = currentSliderValue;
            
            // Update tooltip với chính xác cùng giá trị
            this.updateTooltipPosition(timelineSlider);
            this.updateTooltipContent(currentSliderValue); // ← Pass cùng giá trị
            
            // Load weather data for current time với chính xác cùng giá trị
            const weatherTime = this.convertSliderValueToTimestamp(currentSliderValue); // ← Pass cùng giá trị
            const activeLayer = this.weatherLayers.getActiveLayer();
            if (activeLayer && activeLayer.setAnimationTime) {
                // LOG 4: Call API khi reset timeline
                console.log('📡 API Call (Reset): setAnimationTime(' + weatherTime + ') = ' + new Date(weatherTime * 1000).toUTCString());
                activeLayer.setAnimationTime(weatherTime);
            }
            
        } catch (error) {
            console.error('Reset timeline to current time error:', error);
        }
    }

    // Update tooltip content with formatted time - HIỂN THỊ LOCAL TIME CHO USER
    updateTooltipContent(sliderValue) {
        try {
            const tooltipContent = document.querySelector('#timeTooltip .tooltip-content');
            if (!tooltipContent) return;
            
            // Validate slider value
            const numValue = parseFloat(sliderValue);
            if (isNaN(numValue)) {
                tooltipContent.textContent = 'Invalid time';
                return;
            }

            let targetDate;
            
            // Kiểm tra xem sliderValue là timestamp (lớn) hay slider value (0-3)
            if (numValue > 1000000000000) { // Timestamp (milliseconds)
                targetDate = new Date(numValue);
            } else { // Slider value (0-3)
                // Get current date (today at 0h) - LOCAL TIME CHO HIỂN THỊ
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                
                // Calculate date based on slider value (0-3) - LOCAL TIME
                const dayOffset = numValue - 1; // Convert to -1, 0, 1, 2
                targetDate = new Date(today.getTime() + dayOffset * 24 * 60 * 60 * 1000);
                
                // Calculate hours within the day - LOCAL TIME
                const fractionalPart = numValue % 1;
                const hoursInDay = fractionalPart * 24;
                const hours = Math.floor(hoursInDay);
                const minutes = Math.floor((hoursInDay % 1) * 60);
                const seconds = Math.floor(((hoursInDay % 1) * 60 % 1) * 60);
                
                targetDate.setHours(hours, minutes, seconds, 0);
            }

            // LOG 2: Time của Thumb (LOCAL TIME)
            console.log('👆 Thumb Time (Local):', targetDate);

            const days = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'];
            const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            
            const dayName = days[targetDate.getDay()];
            const day = targetDate.getDate();
            const monthName = months[targetDate.getMonth()];
            const hours = targetDate.getHours();
            const minutes = targetDate.getMinutes();
            const hoursStr = hours.toString().padStart(2, '0');
            const minutesStr = minutes.toString().padStart(2, '0');
            
            const formattedTime = `${dayName}, ${day} ${monthName}, ${hoursStr}:${minutesStr}`;
            tooltipContent.textContent = formattedTime;
            
        } catch (error) {
            console.error('Update tooltip content error:', error);
        }
    }

    // Update day labels - FIXED FOR 4 DAYS (1 trước + 3 tiếp theo)
    updateDayLabels(startDate, endDate) {
        try {
            const markers = document.querySelectorAll('.timeline-marker');
            if (!markers.length) return;

            // Get current date (today)
            const today = new Date();
            today.setHours(0, 0, 0, 0); // Set to 0h
            
            // Calculate 4 dates: yesterday, today, tomorrow, day after tomorrow
            const dates = [
                new Date(today.getTime() - 24 * 60 * 60 * 1000), // Yesterday
                new Date(today.getTime()),                        // Today  
                new Date(today.getTime() + 24 * 60 * 60 * 1000), // Tomorrow
                new Date(today.getTime() + 2 * 24 * 60 * 60 * 1000) // Day after tomorrow
            ];
            
            markers.forEach((marker, index) => {
                if (index < dates.length) {
                    const markerDate = dates[index];
                    const day = markerDate.getDate();
                    const month = markerDate.getMonth() + 1;
                    
                    const label = marker.querySelector('.marker-label');
                    if (label) {
                        label.textContent = `${day}/${month}`;
                    }
                    
                    // Store date for reference
                    marker.setAttribute('data-date', markerDate.toISOString());
                    marker.setAttribute('data-day', index - 1); // -1, 0, 1, 2
                }
            });
            
        } catch (error) {
            console.error('Update day labels error:', error);
        }
    }

    // Setup timeline for specific layer (wind2.html style)
    setupTimelineForActiveLayer(layer) {
        try {
            const timelineSlider = document.getElementById('timelineSlider');
            if (!layer || !timelineSlider) return;
            
            const startDate = layer.getAnimationStartDate();
            const endDate = layer.getAnimationEndDate();
            const currentDate = layer.getAnimationTimeDate();
            
            if (startDate && endDate && currentDate) {
                timelineSlider.min = +startDate;
                timelineSlider.max = +endDate;
                timelineSlider.value = +currentDate;
                
                // Update display
                this.refreshTime();
            }
        } catch (error) {
            console.error('Setup timeline for active layer error:', error);
        }
    }

    // Sync timeline with active layer - RESET TO CURRENT TIME
    syncTimelineWithActiveLayer() {
        try {
            const activeLayerType = this.weatherLayers.getActiveLayerType();
            console.log('Syncing timeline with:', activeLayerType);
            
            if (!activeLayerType) return;
            
            const layer = this.weatherLayers.getLayer(activeLayerType);
            
            if (layer) {
                // Wait a bit for layer to be fully ready
                setTimeout(() => {
                    // Reset timeline to current time when switching layers
                    console.log('Layer switched, resetting timeline to current time');
                    this.resetTimelineToCurrentTime();
                    
                    if (window.showStatus) {
                        window.showStatus(`📅 Timeline synced with ${activeLayerType}`, 'success', 1500);
                    }
                }, 500);
            }
        } catch (error) {
            console.error('Timeline sync error:', error);
        }
    }

    // Setup animation controls
    setupAnimationControls() {
        const playButton = document.getElementById('playButton');
        
        if (!playButton) {
            console.warn('Animation control elements not found');
            return;
        }
        
        playButton.addEventListener('click', () => {
            this.isPlaying = !this.isPlaying;
            playButton.innerHTML = this.isPlaying ? '<i class="fas fa-pause"></i>' : '<i class="fas fa-play"></i>';
            
            if (this.isPlaying) {
                if (window.showStatus) {
                    window.showStatus(`▶️ Bắt đầu animation theo ${this.timeStep}`, 'info', 2000);
                }
                this.startCustomAnimation();
            } else {
                if (window.showStatus) {
                    window.showStatus('⏸️ Dừng animation', 'info', 2000);
                }
                this.stopCustomAnimation();
            }
        });
        
        // Setup custom dropdown
        this.setupCustomTimeStepSelect();
    }

    // ===== CUSTOM TIME STEP SELECT LOGIC =====
    setupCustomTimeStepSelect() {
        const container = document.getElementById('customTimeStepSelect');
        if (!container) {
            console.warn('Custom time step select not found');
            return;
        }

        const trigger = container.querySelector('.custom-select-trigger');
        const triggerText = trigger.querySelector('span');
        const optionsContainer = container.querySelector('.custom-select-options');
        const options = optionsContainer.querySelectorAll('.custom-select-option');

        // Hàm xử lý khi một option được chọn
        const handleOptionSelect = (option) => {
            const selectedValue = option.getAttribute('data-value');
            const selectedText = option.textContent;

            // 1. Cập nhật text của nút bấm
            triggerText.textContent = selectedText;

            // 2. Đóng menu
            container.classList.remove('open');

            // 3. THỰC THI HÀNH ĐỘNG CHÍNH - Cập nhật timeStep và restart animation
            this.timeStep = selectedValue;
            console.log(`Time step changed to: ${selectedValue}`);
            
            if (window.showStatus) {
                window.showStatus(`⏱️ Thay đổi bước thời gian: ${this.timeStep}`, 'info', 1500);
            }
            
            // Restart animation with new time step if playing
            if (this.isPlaying) {
                this.stopCustomAnimation();
                this.startCustomAnimation();
            }
        };

        // Mở/đóng menu khi click vào nút bấm
        trigger.addEventListener('click', (e) => {
            e.stopPropagation();
            container.classList.toggle('open');
        });

        // Xử lý khi click vào một option
        options.forEach(option => {
            option.addEventListener('click', () => {
                handleOptionSelect(option);
            });
        });

        // Đóng menu khi click ra ngoài
        document.addEventListener('click', (e) => {
            if (!container.contains(e.target)) {
                container.classList.remove('open');
            }
        });

        // Thiết lập giá trị mặc định (Giờ)
        const defaultOption = optionsContainer.querySelector('[data-value="hour"]');
        if (defaultOption) {
            triggerText.textContent = defaultOption.textContent;
            this.timeStep = 'hour';
        }

        console.log('✅ Custom time step select setup complete');
    }

    // Custom animation system with time steps
    startCustomAnimation() {
        this.stopCustomAnimation(); // Clear any existing animation
        
        const activeLayer = this.weatherLayers.getActiveLayer();
        if (!activeLayer) return;
        
        // Get timeline slider to determine current position
        const timelineSlider = document.getElementById('timelineSlider');
        if (!timelineSlider) return;
        
        // B1: Check menu time step setting (đã có trong this.timeStep)
        console.log(`🎮 Starting animation with time step: ${this.timeStep}`);
        
        // B2: Lấy vị trí thumb hiện tại và convert sang timestamp
        let currentSliderValue = parseFloat(timelineSlider.value);
        let currentTimestamp = this.convertSliderValueToTimestamp(currentSliderValue);
        let currentTime = new Date(currentTimestamp * 1000); // Convert to Date object
        
        console.log(`📍 Starting from: ${currentTime.toString()}`);
        
        // Calculate time increment based on step setting
        let timeIncrement;
        switch (this.timeStep) {
            case 'minute':
                timeIncrement = 60 * 1000; // 1 minute
                break;
            case 'hour':
                timeIncrement = 60 * 60 * 1000; // 1 hour
                break;
            case 'day':
                timeIncrement = 24 * 60 * 60 * 1000; // 1 day
                break;
            default:
                timeIncrement = 60 * 60 * 1000; // Default 1 hour
        }
        
        // Animation interval
        this.animationInterval = setInterval(() => {
            if (!this.isPlaying) return;
            
            // Tăng thời gian theo time step
            currentTime = new Date(currentTime.getTime() + timeIncrement);
            
            console.log(`⏭️ Next time: ${currentTime.toString()}`);
            
            // Convert back to slider value for timeline
            const newSliderValue = this.convertTimestampToSliderValue(currentTime.getTime() / 1000);
            
            // Check if reached end of timeline (value > 3)
            if (newSliderValue > 3) {
                // Loop back to start (yesterday)
                currentSliderValue = 0;
                currentTimestamp = this.convertSliderValueToTimestamp(0);
                currentTime = new Date(currentTimestamp * 1000);
                console.log(`🔄 Looping back to start: ${currentTime.toString()}`);
            } else {
                currentSliderValue = newSliderValue;
            }
            
            // Update active layer with timestamp
            const currentActiveLayer = this.weatherLayers.getActiveLayer();
            if (currentActiveLayer) {
                const weatherTimestamp = Math.floor(currentTime.getTime() / 1000);
                console.log('📡 API Call (Animation): setAnimationTime(' + weatherTimestamp + ') = ' + new Date(weatherTimestamp * 1000).toUTCString());
                currentActiveLayer.setAnimationTime(weatherTimestamp);
            }
            
            // Cập nhật thumb position trên timeline slider
            const currentTimelineSlider = document.getElementById('timelineSlider');
            if (currentTimelineSlider) {
                currentTimelineSlider.value = currentSliderValue;
                
                // Cập nhật tooltip position và content
                this.updateTooltipPosition(currentTimelineSlider);
                this.updateTooltipContent(currentSliderValue);
            }
        }, 1000); // Update every 1 second
    }

    // Stop custom animation
    stopCustomAnimation() {
        if (this.animationInterval) {
            clearInterval(this.animationInterval);
            this.animationInterval = null;
        }
    }

    // Update time display (backward compatibility)
    updateTimeDisplay() {
        this.refreshTime();
    }
}

// Export
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TimelineManager;
} else {
    window.TimelineManager = TimelineManager;
} 