// Weather App Configuration
const CONFIG = {
    // MapTiler API Key
    MAPTILER_API_KEY: 'oinoatcrNmdCL1524DOl',
    
    // Map Settings
    MAP_CENTER: [105.8542, 21.0285], // Vietnam center
    MAP_ZOOM: 6,
    MAP_STYLE: 'BACKDROP',
    MAP_PROJECTION: 'mercator',
    
    // Animation Settings
    DEFAULT_OPACITY: 0.75,
    ANIMATION_SPEED: 1000,
    
    // Static Markers Data
    STATIC_MARKERS: [
        {
            "name": "QĐ. Hoàng Sa",
            "lat": 16.5,
            "lon": 112.0,
            "label": "QĐ. Hoàng Sa",
            "popupHtml": "<div style='min-width:180px; background:#fff; border-radius:8px; padding:0; color:#222; text-align:center;'><div style='width:100%;height:38px;background:#da251d;border-radius:6px 6px 6px 6px;display:flex;align-items:center;justify-content:center;'><svg width='32' height='32' viewBox='0 0 32 32'><polygon points='16,5 17.8,13 26,13 19.5,17.5 21.5,25 16,20.5 10.5,25 12.5,17.5 6,13 14.2,13' fill='#ff0'/></svg></div><div style='padding:10px 8px 8px 8px;'><b style='color:#d32;'>QĐ. Hoàng Sa</b> - Là của Việt Nam</div></div>"
        },
        {
            "name": "QĐ. Trường Sa",
            "lat": 9.166666666666666,
            "lon": 114.41666666666667,
            "label": "QĐ. Trường Sa",
            "popupHtml": "<div style='min-width:180px; background:#fff; border-radius:8px; padding:0; color:#222; text-align:center;'><div style='width:100%;height:38px;background:#da251d;border-radius:6px 6px 6px 6px;display:flex;align-items:center;justify-content:center;'><svg width='32' height='32' viewBox='0 0 32 32'><polygon points='16,5 17.8,13 26,13 19.5,17.5 21.5,25 16,20.5 10.5,25 12.5,17.5 6,13 14.2,13' fill='#ff0'/></svg></div><div style='padding:10px 8px 8px 8px;'><b style='color:#d32;'>QĐ. Trường Sa</b> - Là của Việt Nam</div></div>"
        }
    ]
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
} else {
    window.CONFIG = CONFIG;
} 