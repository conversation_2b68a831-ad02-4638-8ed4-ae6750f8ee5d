// Weather Layers Management
class WeatherLayers {
    constructor() {
        this.layers = {};
        this.activeLayers = new Set();
        this.addedLayers = new Set();
        this.currentUnits = {
            temperature: 'celsius',
            wind: 'speedMetersPerSecond',
            precipitation: 'millimetersPerHour',
            pressure: 'hectopascals',
            radar: 'decibelsZ'
        };
    }

    // MapTiler Official Legend Configurations
    static get LEGEND_CONFIGS() {
        return {
            wind: {
                colorramp: 'VIRIDIS',
                scale: [0, 40],
                values: [40, 30, 20, 15, 10, 5, 0],
                units: {
                    speedMetersPerSecond: "m/s",
                    speedKilometersPerHour: "km/h", 
                    speedMilesPerHour: "mph",
                    speedFeetPerSecond: "ft/s",
                    speedKnots: "knots"
                },
                defaultUnit: 'speedKilometersPerHour'
            },
            temperature: {
                colorramp: 'TEMPERATURE_2',
                scale: [-50, 50],
                values: [40, 20, 10, 0, -10, -20, -40],
                units: {
                    celsius: "°C",
                    fahrenheit: "°F"
                },
                defaultUnit: 'celsius'
            },
            precipitation: {
                colorramp: 'PRECIPITATION',
                values: [40, 30, 20, 15, 10, 5, 0],
                units: {
                    millimetersPerHour: "mm/h",
                    inchesPerHour: "in/h"
                },
                defaultUnit: 'millimetersPerHour'
            },
            pressure: {
                colorramp: 'PRESSURE_2',
                values: [1050, 1030, 1010, 990, 970, 950, 930],
                units: {
                    hectopascals: "hPa",
                    millibars: "mbar",
                    inchesOfMercury: "inHg",
                    kiloPascals: "kPa"
                },
                defaultUnit: 'hectopascals'
            },
            radar: {
                colorramp: 'RADAR',
                values: [60, 50, 40, 25, 15, 10, 0],
                units: {
                    decibelsZ: "dBZ",
                    millimetersPerHour: "mm/h",
                    rainIntensity: "Light/Moderate/Heavy"
                },
                defaultUnit: 'decibelsZ'
            }
        };
    }

    // Initialize all weather layers
    initializeLayers() {
        try {
            if (typeof maptilerweather === 'undefined') {
                throw new Error('MapTiler Weather SDK not loaded');
            }
            
            // Initialize all weather layers with EXACT MapTiler built-in colorramps
            this.layers.wind = new maptilerweather.WindLayer({
                colorramp: maptilerweather.ColorRamp.builtin.VIRIDIS.scale(0, 40)
            });
            
            this.layers.temperature = new maptilerweather.TemperatureLayer({
                colorramp: maptilerweather.ColorRamp.builtin.TEMPERATURE_2.scale(-50, 50)
            });
            
            this.layers.precipitation = new maptilerweather.PrecipitationLayer({
                colorramp: maptilerweather.ColorRamp.builtin.PRECIPITATION
            });
            
            this.layers.pressure = new maptilerweather.PressureLayer({
                colorramp: maptilerweather.ColorRamp.builtin.PRESSURE_2
            });
            
            this.layers.radar = new maptilerweather.RadarLayer({
                colorramp: maptilerweather.ColorRamp.builtin.RADAR
            });
            
            console.log('🌤️ Weather layers created:', Object.keys(this.layers));
            return true;
            
        } catch (error) {
            console.error('Weather layers initialization error:', error);
            return false;
        }
    }

    // Setup layer events
    setupLayerEvents(onSourceReady, onTick, onAnimationTimeSet) {
        Object.entries(this.layers).forEach(([layerType, layer]) => {
            if (layer) {
                layer.on("sourceReady", () => {
                    console.log(`✅ ${layerType} layer ready`);
                    if (onSourceReady) onSourceReady(layerType, layer);
                });
                
                layer.on("tick", () => {
                    if (onTick) onTick(layerType, layer);
                });
                
                layer.on("animationTimeSet", () => {
                    if (onAnimationTimeSet) onAnimationTimeSet(layerType, layer);
                });
            }
        });
    }

    // Get layer by type
    getLayer(layerType) {
        return this.layers[layerType];
    }

    // Get active layer
    getActiveLayer() {
        const activeLayerType = Array.from(this.activeLayers)[0];
        return activeLayerType ? this.layers[activeLayerType] : null;
    }

    // Get active layer type
    getActiveLayerType() {
        return Array.from(this.activeLayers)[0];
    }

    // Add layer to map
    addLayerToMap(map, layerType) {
        const layer = this.layers[layerType];
        if (layer && map && !this.addedLayers.has(layerType)) {
            map.addLayer(layer, 'Water');
            this.addedLayers.add(layerType);
            console.log(`➕ Added ${layerType} layer to map`);
        }
    }

    // Set layer opacity
    setLayerOpacity(layerType, opacity) {
        const layer = this.layers[layerType];
        if (layer && this.addedLayers.has(layerType) && layer.setOpacity) {
            layer.setOpacity(opacity);
        }
    }

    // Hide all layers
    hideAllLayers() {
        Object.entries(this.layers).forEach(([layerType, layer]) => {
            if (layer && this.addedLayers.has(layerType) && layer.setOpacity) {
                layer.setOpacity(0);
            }
        });
    }

    // Show specific layer
    showLayer(layerType, opacity = 0.75) {
        const layer = this.layers[layerType];
        if (layer && layer.setOpacity) {
            layer.setOpacity(opacity);
        }
    }

    // Toggle layer (single selection mode)
    toggleLayer(map, layerType) {
        // Clear previous active layers
        this.activeLayers.clear();
        this.activeLayers.add(layerType);
        
        // Add layer to map if not already added
        this.addLayerToMap(map, layerType);
        
        // Hide all layers first, then show selected one
        this.hideAllLayers();
        this.showLayer(layerType, CONFIG.DEFAULT_OPACITY);
        
        console.log(`🔄 Toggled to ${layerType} layer`);
    }

    // Get current unit for layer
    getCurrentUnit(layerType) {
        const config = WeatherLayers.LEGEND_CONFIGS[layerType];
        if (!config) return '';
        
        if (config.units) {
            const currentUnitKey = this.currentUnits[layerType];
            return config.units[currentUnitKey] || Object.values(config.units)[0];
        }
        
        return config.unit || '';
    }

    // Cycle units for layer
    cycleUnits(layerType) {
        const config = WeatherLayers.LEGEND_CONFIGS[layerType];
        
        if (config && config.units && Object.keys(config.units).length > 1) {
            const unitKeys = Object.keys(config.units);
            const currentIndex = unitKeys.indexOf(this.currentUnits[layerType]);
            const nextIndex = (currentIndex + 1) % unitKeys.length;
            const nextUnitKey = unitKeys[nextIndex];
            
            this.currentUnits[layerType] = nextUnitKey;
            return config.units[nextUnitKey];
        }
        
        return null;
    }

    // Get data at point
    getDataAtPoint(layerType, lng, lat) {
        const layer = this.layers[layerType];
        if (layer && layer.pickAt && this.addedLayers.has(layerType)) {
            try {
                return layer.pickAt(lng, lat);
            } catch (error) {
                console.warn(`Error getting data for ${layerType}:`, error);
                return null;
            }
        }
        return null;
    }
}

// Export
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WeatherLayers;
} else {
    window.WeatherLayers = WeatherLayers;
} 