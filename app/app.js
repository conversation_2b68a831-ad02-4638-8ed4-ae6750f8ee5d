// Main Weather App - Orchestrates all modules
class WeatherApp {
    constructor() {
        this.mapManager = null;
        this.weatherLayers = null;
        this.legendManager = null;
        this.timelineManager = null;
        this.uiControls = null;
        this.tooltipTimer = null;
        this.tooltipPinned = false;
        this.pinMarker = null; // MapTiler marker for pin
        this.pinnedLngLat = null; // Store pinned location for tooltip updates
    }

    // Initialize all modules and start the app
    async init() {
        try {
            console.log('🚀 Starting Weather App Vietnam with MapTiler SDK...');
            
            // Initialize core modules
            this.mapManager = new MapManager();
            this.weatherLayers = new WeatherLayers();
            
            // Initialize map first
            const map = await this.mapManager.initializeMap();
            
            // Initialize weather layers
            const layersInitialized = this.weatherLayers.initializeLayers();
            if (!layersInitialized) {
                throw new Error('Failed to initialize weather layers');
            }
            
            // Setup layer events
            this.weatherLayers.setupLayerEvents(
                (layerType, layer) => this.onLayerSourceReady(layerType, layer),
                (layerType, layer) => this.onLayerTick(layerType, layer),
                (layerType, layer) => this.onLayerAnimationTimeSet(layerType, layer)
            );
            
            // Initialize dependent modules
            this.legendManager = new LegendManager(this.weatherLayers);
            this.timelineManager = new TimelineManager(this.weatherLayers);
            this.uiControls = new UIControls(this.weatherLayers, this.legendManager, this.timelineManager);
            
            // Setup UI controls
            this.uiControls.setupAllControls(map);
            
            // Setup mouse events for hover tooltip
            this.setupMouseEvents(map);
            
            // Setup map move listener for pinned tooltip position updates
            this.setupPinnedTooltipUpdates(map);
            
            // Setup window resize handler
            this.setupWindowResize();
            
            console.log('🎉 Weather App initialization complete!');
            this.uiControls.showStatus('🚀 Weather App đã sẵn sàng!', 'success');
            
        } catch (error) {
            console.error('Failed to initialize app:', error);
            if (this.uiControls) {
                this.uiControls.showStatus('❌ Không thể khởi tạo ứng dụng', 'error');
            }
        }
    }

    // Handle layer source ready event
    onLayerSourceReady(layerType, layer) {
        console.log(`✅ ${layerType} layer source ready`);
        
        // Setup timeline for the first layer that becomes ready
        if (layerType === 'wind') {
            this.timelineManager.setupTimelineControls(layer);
        }
    }

    // Handle layer tick event
    onLayerTick(layerType, layer) {
        // Update time display when any layer ticks
        this.timelineManager.refreshTime();
    }

    // Handle layer animation time set event
    onLayerAnimationTimeSet(layerType, layer) {
        // Update time display when animation time is set
        this.timelineManager.refreshTime();
    }

    // Setup enhanced mouse events with weather data
    setupMouseEvents(map) {
        const mapInstance = this.mapManager.getMap();
        
        // Override the basic mouse events with weather-aware ones
        mapInstance.off('mousemove'); // Remove basic events
        mapInstance.off('mouseout');
        mapInstance.off('click');
        
        // Auto-hide timer
        this.tooltipTimer = null;
        
        // Hover to show tooltip
        mapInstance.on('mousemove', (e) => {
            this.updateHoverTooltipWithWeatherData(e.lngLat, e.point);
        });
        
        // Click to pin tooltip for 5 seconds
        mapInstance.on('click', (e) => {
            this.pinTooltipForDuration(e.lngLat, e.point, 5000);
        });
        
        // Hide on mouse out (unless pinned)
        mapInstance.on('mouseout', (e) => {
            if (!this.tooltipPinned) {
                this.mapManager.hideHoverTooltip();
            }
        });
    }

    // Setup pinned tooltip position updates when map moves
    setupPinnedTooltipUpdates(map) {
        const mapInstance = this.mapManager.getMap();
        
        mapInstance.on('move', () => {
            if (this.tooltipPinned && this.pinnedLngLat) {
                this.updatePinnedTooltipPosition();
            }
        });
        
        mapInstance.on('zoom', () => {
            if (this.tooltipPinned && this.pinnedLngLat) {
                this.updatePinnedTooltipPosition();
            }
        });
    }

    // Update hover tooltip with weather data conversion
    updateHoverTooltipWithWeatherData(lngLat, point) {
        try {
            if (!lngLat || !point) return;
            
            // Don't update if tooltip is pinned
            if (this.tooltipPinned) return;
            
            const tooltip = document.getElementById('hoverTooltip');
            if (!tooltip) return;
            
            // Don't update if static marker tooltip is showing
            if (tooltip.dataset.staticMarker === 'true') return;
            
            // Get active layer and data
            const activeLayerType = this.weatherLayers.getActiveLayerType();
            let dataText = '';
            
            if (activeLayerType) {
                const value = this.weatherLayers.getDataAtPoint(activeLayerType, lngLat.lng, lngLat.lat);
                
                if (value) {
                    dataText = this.formatWeatherDataForTooltip(activeLayerType, value);
                }
            }
            
            if (dataText) {
                tooltip.textContent = dataText;
                tooltip.style.left = point.x + 'px';
                tooltip.style.top = point.y + 'px';
                tooltip.classList.add('visible');
            } else {
                tooltip.classList.remove('visible');
            }
            
        } catch (error) {
            console.error('Hover tooltip update error:', error);
        }
    }

    // Pin tooltip for a specific duration
    async pinTooltipForDuration(lngLat, point, duration) {
        try {
            // Clear any existing timer
            if (this.tooltipTimer) {
                clearTimeout(this.tooltipTimer);
            }
            
            // Remove any existing pin marker
            this.removePinIcon();
            
            // Store pinned location for position updates
            this.pinnedLngLat = lngLat;
            
            // Update tooltip content and position (await async call)
            await this.updateTooltipContent(lngLat, point);
            
            // Mark as pinned
            this.tooltipPinned = true;
            
            // Set timer to auto-hide
            this.tooltipTimer = setTimeout(() => {
                this.tooltipPinned = false;
                this.pinnedLngLat = null;
                this.mapManager.hideHoverTooltip();
                this.removePinIcon();
                this.tooltipTimer = null;
            }, duration);
            
        } catch (error) {
            console.error('Pin tooltip error:', error);
        }
    }

    // Update pinned tooltip position when map moves
    updatePinnedTooltipPosition() {
        try {
            if (!this.pinnedLngLat || !this.tooltipPinned) return;
            
            const tooltip = document.getElementById('hoverTooltip');
            if (!tooltip) return;
            
            // Convert geographic coordinates to screen coordinates
            const point = this.mapManager.getMap().project(this.pinnedLngLat);
            
            // Update tooltip position
            tooltip.style.left = (point.x + 15) + 'px'; // Offset to right of pin
            tooltip.style.top = (point.y - 10) + 'px'; // Offset above pin
            
        } catch (error) {
            console.error('Update pinned tooltip position error:', error);
        }
    }

    // Update tooltip content without checking pinned state
    async updateTooltipContent(lngLat, point) {
        try {
            if (!lngLat || !point) return;
            
            const tooltip = document.getElementById('hoverTooltip');
            if (!tooltip) return;
            
            // Get active layer and data
            const activeLayerType = this.weatherLayers.getActiveLayerType();
            let weatherDataText = '';
            
            if (activeLayerType) {
                const value = this.weatherLayers.getDataAtPoint(activeLayerType, lngLat.lng, lngLat.lat);
                
                if (value) {
                    weatherDataText = this.formatWeatherDataForTooltip(activeLayerType, value);
                }
            }
            
            if (weatherDataText) {
                // Hiển thị tọa độ và weather data ngay lập tức
                const coordinateText = this.formatCoordinates(lngLat.lat, lngLat.lng);
                
                // Hiển thị tooltip tạm thời với tọa độ và weather data
                tooltip.textContent = `${coordinateText}\n${weatherDataText}`;
                
                // Position tooltip near pin with offset
                tooltip.style.left = (point.x + 15) + 'px'; // Offset to right
                tooltip.style.top = (point.y - 10) + 'px'; // Offset above
                tooltip.style.whiteSpace = 'pre-line';
                tooltip.classList.add('visible');
                
                // Tạo pin marker ở vị trí geographic
                this.createPinIcon(lngLat);
                
                // Gọi API để lấy location name (async)
                try {
                    const locationText = await this.getLocationName(lngLat.lat, lngLat.lng);
                    
                    // Cập nhật tooltip với location name nếu có
                    if (locationText && tooltip.classList.contains('visible')) {
                        const fullTooltipText = `${locationText}\n${coordinateText}\n${weatherDataText}`;
                        tooltip.textContent = fullTooltipText;
                    }
                } catch (error) {
                    console.error('Error getting location name:', error);
                    // Tooltip vẫn hiển thị với tọa độ và weather data
                }
            } else {
                tooltip.classList.remove('visible');
            }
            
        } catch (error) {
            console.error('Update tooltip content error:', error);
        }
    }

    // Format weather data for tooltip display
    formatWeatherDataForTooltip(layerType, value) {
        const unitText = this.weatherLayers.getCurrentUnit(layerType);
        let convertedValue = null;
        let rawValue = null;
        
        // Extract raw value based on layer type
        switch(layerType) {
            case 'wind':
                rawValue = value.speedMetersPerSecond;
                break;
            case 'temperature':
                rawValue = value.celsius !== undefined ? value.celsius : value.value;
                break;
            case 'precipitation':
                rawValue = value.millimeters !== undefined ? value.millimeters : value.value;
                break;
            case 'pressure':
                rawValue = value.hectoPascals !== undefined ? value.hectoPascals : value.value;
                break;
            case 'radar':
                rawValue = value.dbz !== undefined ? value.dbz : value.value;
                break;
        }
        
        if (rawValue === null || rawValue === undefined) return '';
        
        // Convert value using legend manager
        const currentUnitKey = this.weatherLayers.currentUnits[layerType];
        convertedValue = this.legendManager.convertValue(rawValue, layerType, currentUnitKey);
        
        // Format based on layer type
        const icons = {
            wind: '💨',
            temperature: '🌡️',
            precipitation: '🌧️',
            pressure: '🌀',
            radar: '📡'
        };
        
        const icon = icons[layerType] || '📊';
        
        // Special handling for radar intensity
        if (layerType === 'radar' && currentUnitKey === 'rainIntensity') {
            return `${icon} ${convertedValue}`;
        }
        
        // Number formatting
        let formattedValue;
        if (layerType === 'pressure') {
            formattedValue = convertedValue.toFixed(0);
        } else {
            formattedValue = convertedValue.toFixed(1);
        }
        
        return `${icon} ${formattedValue} ${unitText}`;
    }

    // Create pin marker at geographic position
    createPinIcon(lngLat) {
        try {
            // Remove any existing pin marker
            this.removePinIcon();
            
            // Create pin icon element
            const pinIcon = document.createElement('div');
            pinIcon.innerHTML = '📌';
            pinIcon.style.fontSize = '20px';
            pinIcon.style.cursor = 'pointer';
            pinIcon.style.textShadow = '0 0 3px rgba(0,0,0,0.8)';
            
            // Create MapTiler SDK marker at geographic coordinates
            this.pinMarker = new maptilersdk.Marker({
                element: pinIcon,
                anchor: 'bottom'
            })
            .setLngLat([lngLat.lng, lngLat.lat])
            .addTo(this.mapManager.getMap());
            
        } catch (error) {
            console.error('Create pin marker error:', error);
        }
    }

    // Remove pin marker
    removePinIcon() {
        try {
            if (this.pinMarker) {
                this.pinMarker.remove();
                this.pinMarker = null;
            }
        } catch (error) {
            console.error('Remove pin marker error:', error);
        }
    }

    // Convert decimal degrees to degrees, minutes, seconds
    formatCoordinates(lat, lng) {
        try {
            const formatDegree = (degree, isLatitude) => {
                const abs = Math.abs(degree);
                const deg = Math.floor(abs);
                const min = Math.floor((abs - deg) * 60);
                const sec = Math.round(((abs - deg) * 60 - min) * 60);
                
                let direction;
                if (isLatitude) {
                    direction = degree >= 0 ? 'N' : 'S';
                } else {
                    direction = degree >= 0 ? 'E' : 'W';
                }
                
                return `${deg}°${min.toString().padStart(2, '0')}'${sec.toString().padStart(2, '0')}"${direction}`;
            };
            
            const latFormatted = formatDegree(lat, true);
            const lngFormatted = formatDegree(lng, false);
            
            return `${latFormatted}, ${lngFormatted}`;
        } catch (error) {
            console.error('Format coordinates error:', error);
            return `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
        }
    }

    // Get location name using MapTiler Geocoding API with Vietnam sovereignty protection
    async getLocationName(lat, lng) {
        try {
            // Tọa độ 2 quần đảo Việt Nam
            const paracelIslands = { lat: 16.5, lng: 112.0 };    // QĐ. Hoàng Sa
            const spratlyIslands = { lat: 9.166666666666666, lng: 114.41666666666667 }; // QĐ. Trường Sa
            
            // Hàm tính khoảng cách giữa 2 điểm (Haversine formula)
            const calculateDistance = (lat1, lng1, lat2, lng2) => {
                const R = 6371; // Bán kính Trái Đất (km)
                const dLat = (lat2 - lat1) * Math.PI / 180;
                const dLng = (lng2 - lng1) * Math.PI / 180;
                const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                         Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                         Math.sin(dLng/2) * Math.sin(dLng/2);
                const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
                return R * c; // Khoảng cách tính bằng km
            };
            
            // Kiểm tra xem điểm có trong bán kính 100km quanh 2 quần đảo không
            const distanceToParacel = calculateDistance(lat, lng, paracelIslands.lat, paracelIslands.lng);
            const distanceToSpratly = calculateDistance(lat, lng, spratlyIslands.lat, spratlyIslands.lng);
            
            // Nếu trong bán kính 100km quanh 2 quần đảo → Hiển thị "Biển Đông, Việt Nam"
            if (distanceToParacel <= 100 || distanceToSpratly <= 100) {
                console.log(`Điểm trong vùng chủ quyền Việt Nam - Paracel: ${distanceToParacel.toFixed(1)}km, Spratly: ${distanceToSpratly.toFixed(1)}km`);
                return 'Biển Đông, Việt Nam';
            }
            
            // Nếu ngoài vùng 100km → Call API MapTiler bình thường
            const apiUrl = `https://api.maptiler.com/geocoding/${lng}%2C${lat}.json?key=${CONFIG.MAPTILER_API_KEY}`;
            
            // Call API
            const response = await fetch(apiUrl);
            
            if (!response.ok) {
                throw new Error(`API call failed: ${response.status}`);
            }
            
            const data = await response.json();
            
            // Lấy tên đầu tiên từ features array
            if (data.features && data.features.length > 0) {
                const firstFeature = data.features[0];
                if (firstFeature.place_name) {
                    return firstFeature.place_name;
                }
                if (firstFeature.text) {
                    return firstFeature.text;
                }
            }
            
            // Fallback nếu không có kết quả
            return null;
            
        } catch (error) {
            console.error('MapTiler geocoding error:', error);
            return null;
        }
    }

    // Setup window resize handler
    setupWindowResize() {
        window.addEventListener('resize', () => {
            if (this.mapManager) {
                this.mapManager.handleResize();
            }
            if (this.uiControls) {
                this.uiControls.handleResize();
            }
        });
    }

    // Get app components (for debugging/external access)
    getComponents() {
        return {
            mapManager: this.mapManager,
            weatherLayers: this.weatherLayers,
            legendManager: this.legendManager,
            timelineManager: this.timelineManager,
            uiControls: this.uiControls
        };
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const app = new WeatherApp();
    app.init();
    
    // Make app available globally for debugging
    window.weatherApp = app;
});

// Export
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WeatherApp;
} else {
    window.WeatherApp = WeatherApp;
} 