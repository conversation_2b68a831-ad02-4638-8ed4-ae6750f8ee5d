<!DOCTYPE html>
<html>
<head>
    <title>3D Cloud From PNG - No Map (Fixed)</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { margin: 0; overflow: hidden; background-color: #000; }
        canvas { display: block; }
        #info {
            position: absolute; top: 10px; left: 10px;
            background: rgba(0, 0, 0, 0.7); color: white;
            padding: 10px; border-radius: 5px; font-family: monospace;
        }
    </style>
</head>
<body>

    <div id="info">
        <h1>3D Cloud Standalone</h1>
        <p>G<PERSON><PERSON> chuột trái để xoay, chuột phải để di chuyển, lăn chuột để zoom.</p>
    </div>

    <!-- Tải thư viện THREE.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>

    <!-- Tải <PERSON>ontrols và sửa lỗi kế thừa EventDispatcher -->
    <script>
        var THREE; // Để tránh lỗi "THREE is not defined"
        (function() {
            // --- Mã nguồn OrbitControls từ CDN ---
            function OrbitControls(object, domElement) {
                // ... (toàn bộ mã của OrbitControls như trước) ...
                this.object = object;
                this.domElement = domElement;
                this.enabled = true;
                this.target = new THREE.Vector3();
                this.minDistance = 0;
                this.maxDistance = Infinity;
                this.minZoom = 0;
                this.maxZoom = Infinity;
                this.minPolarAngle = 0;
                this.maxPolarAngle = Math.PI;
                this.minAzimuthAngle = -Infinity;
                this.maxAzimuthAngle = Infinity;
                this.enableDamping = false;
                this.dampingFactor = 0.05;
                this.enableZoom = true;
                this.zoomSpeed = 1.0;
                this.enableRotate = true;
                this.rotateSpeed = 1.0;
                this.enablePan = true;
                this.panSpeed = 1.0;
                this.screenSpacePanning = true;
                this.keyPanSpeed = 7.0;
                this.autoRotate = false;
                this.autoRotateSpeed = 2.0;
                this.keys = { LEFT: 'ArrowLeft', UP: 'ArrowUp', RIGHT: 'ArrowRight', BOTTOM: 'ArrowDown' };
                this.mouseButtons = { LEFT: THREE.MOUSE.ROTATE, MIDDLE: THREE.MOUSE.DOLLY, RIGHT: THREE.MOUSE.PAN };
                this.touches = { ONE: THREE.TOUCH.ROTATE, TWO: THREE.TOUCH.DOLLY_PAN };
                this.target0 = this.target.clone();
                this.position0 = this.object.position.clone();
                this.zoom0 = this.object.zoom;
                this.getPolarAngle = function() { return spherical.phi; };
                this.getAzimuthalAngle = function() { return spherical.theta; };
                this.saveState = function() { this.target0.copy(this.target); this.position0.copy(this.object.position); this.zoom0 = this.object.zoom; };
                this.reset = function() { this.target.copy(this.target0); this.object.position.copy(this.position0); this.object.zoom = this.zoom0; this.object.updateProjectionMatrix(); this.dispatchEvent({ type: 'change' }); this.update(); state = STATE.NONE; };
                this.update = function() {
                    var offset = new THREE.Vector3();
                    var quat = new THREE.Quaternion().setFromUnitVectors(object.up, new THREE.Vector3(0, 1, 0));
                    var quatInverse = quat.clone().invert();
                    var lastPosition = new THREE.Vector3();
                    var lastQuaternion = new THREE.Quaternion();
                    var twoPI = 2 * Math.PI;
                    return function update() {
                        var position = this.object.position;
                        offset.copy(position).sub(this.target);
                        offset.applyQuaternion(quat);
                        spherical.setFromVector3(offset);
                        if (this.autoRotate && state === STATE.NONE) { rotateLeft(getAutoRotationAngle()); }
                        if (this.enableDamping) {
                            spherical.theta += sphericalDelta.theta * this.dampingFactor;
                            spherical.phi += sphericalDelta.phi * this.dampingFactor;
                        } else {
                            spherical.theta += sphericalDelta.theta;
                            spherical.phi += sphericalDelta.phi;
                        }
                        var min = this.minAzimuthAngle;
                        var max = this.maxAzimuthAngle;
                        if (isFinite(min) && isFinite(max)) {
                            if (min < -Math.PI) min += twoPI;
                            else if (min > Math.PI) min -= twoPI;
                            if (max < -Math.PI) max += twoPI;
                            else if (max > Math.PI) max -= twoPI;
                            if (min <= max) { spherical.theta = Math.max(min, Math.min(max, spherical.theta)); } else { spherical.theta = (spherical.theta > (min + max) / 2) ? Math.max(min, spherical.theta) : Math.min(max, spherical.theta); }
                        }
                        spherical.phi = Math.max(this.minPolarAngle, Math.min(this.maxPolarAngle, spherical.phi));
                        spherical.makeSafe();
                        spherical.radius *= scale;
                        spherical.radius = Math.max(this.minDistance, Math.min(this.maxDistance, spherical.radius));
                        this.target.add(panOffset);
                        offset.setFromSpherical(spherical);
                        offset.applyQuaternion(quatInverse);
                        position.copy(this.target).add(offset);
                        this.object.lookAt(this.target);
                        if (this.enableDamping === true) {
                            sphericalDelta.theta *= (1 - this.dampingFactor);
                            sphericalDelta.phi *= (1 - this.dampingFactor);
                            panOffset.multiplyScalar(1 - this.dampingFactor);
                        } else {
                            sphericalDelta.set(0, 0, 0);
                            panOffset.set(0, 0, 0);
                        }
                        scale = 1;
                        if (zoomChanged || lastPosition.distanceToSquared(this.object.position) > EPS || 8 * (1 - lastQuaternion.dot(this.object.quaternion)) > EPS) {
                            this.dispatchEvent({ type: 'change' }); // Lỗi xảy ra ở đây
                            lastPosition.copy(this.object.position);
                            lastQuaternion.copy(this.object.quaternion);
                            zoomChanged = false;
                            return true;
                        }
                        return false;
                    };
                }();
                this.dispose = function() { this.domElement.removeEventListener('contextmenu', onContextMenu); this.domElement.removeEventListener('pointerdown', onPointerDown); this.domElement.removeEventListener('pointercancel', onPointerUp); this.domElement.removeEventListener('wheel', onMouseWheel); this.domElement.removeEventListener('pointermove', onPointerMove); this.domElement.removeEventListener('pointerup', onPointerUp); if (this._domElementKeyEvents !== null) { this._domElementKeyEvents.removeEventListener('keydown', onKeyDown); } };
                var scope = this;
                var STATE = { NONE: -1, ROTATE: 0, DOLLY: 1, PAN: 2, TOUCH_ROTATE: 3, TOUCH_PAN: 4, TOUCH_DOLLY_PAN: 5, TOUCH_DOLLY_ROTATE: 6 };
                var state = STATE.NONE;
                var EPS = 0.000001;
                var spherical = new THREE.Spherical();
                var sphericalDelta = new THREE.Spherical();
                var scale = 1;
                var panOffset = new THREE.Vector3();
                var zoomChanged = false;
                var rotateStart = new THREE.Vector2();
                var rotateEnd = new THREE.Vector2();
                var rotateDelta = new THREE.Vector2();
                var panStart = new THREE.Vector2();
                var panEnd = new THREE.Vector2();
                var panDelta = new THREE.Vector2();
                var dollyStart = new THREE.Vector2();
                var dollyEnd = new THREE.Vector2();
                var dollyDelta = new THREE.Vector2();
                function getAutoRotationAngle() { return 2 * Math.PI / 60 / 60 * scope.autoRotateSpeed; }
                function getZoomScale() { return Math.pow(0.95, scope.zoomSpeed); }
                function rotateLeft(angle) { sphericalDelta.theta -= angle; }
                function rotateUp(angle) { sphericalDelta.phi -= angle; }
                var panLeft = function() { var v = new THREE.Vector3(); return function panLeft(distance, objectMatrix) { v.setFromMatrixColumn(objectMatrix, 0); v.multiplyScalar(-distance); panOffset.add(v); }; }();
                var panUp = function() { var v = new THREE.Vector3(); return function panUp(distance, objectMatrix) { if (scope.screenSpacePanning === true) { v.setFromMatrixColumn(objectMatrix, 1); } else { v.setFromMatrixColumn(objectMatrix, 0); v.crossVectors(scope.object.up, v); } v.multiplyScalar(distance); panOffset.add(v); }; }();
                var pan = function() { var offset = new THREE.Vector3(); return function pan(deltaX, deltaY) { var element = scope.domElement; if (scope.object.isPerspectiveCamera) { var position = scope.object.position; offset.copy(position).sub(scope.target); var targetDistance = offset.length(); targetDistance *= Math.tan((scope.object.fov / 2) * Math.PI / 180.0); panLeft(2 * deltaX * targetDistance / element.clientHeight, scope.object.matrix); panUp(2 * deltaY * targetDistance / element.clientHeight, scope.object.matrix); } else if (scope.object.isOrthographicCamera) { panLeft(deltaX * (scope.object.right - scope.object.left) / scope.object.zoom / element.clientWidth, scope.object.matrix); panUp(deltaY * (scope.object.top - scope.object.bottom) / scope.object.zoom / element.clientHeight, scope.object.matrix); } else { console.warn('WARNING: OrbitControls.js encountered an unknown camera type - pan is not enabled.'); scope.enablePan = false; } }; }();
                function dollyOut(dollyScale) { if (scope.object.isPerspectiveCamera) { scale /= dollyScale; } else if (scope.object.isOrthographicCamera) { scope.object.zoom = Math.max(scope.minZoom, Math.min(scope.maxZoom, scope.object.zoom * dollyScale)); scope.object.updateProjectionMatrix(); zoomChanged = true; } else { console.warn('WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom is not enabled.'); scope.enableZoom = false; } }
                function dollyIn(dollyScale) { if (scope.object.isPerspectiveCamera) { scale *= dollyScale; } else if (scope.object.isOrthographicCamera) { scope.object.zoom = Math.max(scope.minZoom, Math.min(scope.maxZoom, scope.object.zoom / dollyScale)); scope.object.updateProjectionMatrix(); zoomChanged = true; } else { console.warn('WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom is not enabled.'); scope.enableZoom = false; } }
                function handleMouseDownRotate(event) { rotateStart.set(event.clientX, event.clientY); }
                function handleMouseDownDolly(event) { dollyStart.set(event.clientX, event.clientY); }
                function handleMouseDownPan(event) { panStart.set(event.clientX, event.clientY); }
                function handleMouseMoveRotate(event) { rotateEnd.set(event.clientX, event.clientY); rotateDelta.subVectors(rotateEnd, rotateStart).multiplyScalar(scope.rotateSpeed); var element = scope.domElement; rotateLeft(2 * Math.PI * rotateDelta.x / element.clientHeight); rotateUp(2 * Math.PI * rotateDelta.y / element.clientHeight); rotateStart.copy(rotateEnd); scope.update(); }
                function handleMouseMoveDolly(event) { dollyEnd.set(event.clientX, event.clientY); dollyDelta.subVectors(dollyEnd, dollyStart); if (dollyDelta.y > 0) { dollyIn(getZoomScale()); } else if (dollyDelta.y < 0) { dollyOut(getZoomScale()); } dollyStart.copy(dollyEnd); scope.update(); }
                function handleMouseMovePan(event) { panEnd.set(event.clientX, event.clientY); panDelta.subVectors(panEnd, panStart).multiplyScalar(scope.panSpeed); pan(panDelta.x, panDelta.y); panStart.copy(panEnd); scope.update(); }
                function handleMouseUp() {}
                function handleMouseWheel(event) { if (scope.enabled === false || scope.enableZoom === false || (state !== STATE.NONE && state !== STATE.ROTATE)) return; event.preventDefault(); scope.dispatchEvent({ type: 'start' }); handleMouseWheel(event); scope.dispatchEvent({ type: 'end' }); }
                function handleKeyDown(event) {
                    var needsUpdate = false;
                    switch (event.code) {
                        case scope.keys.UP: pan(0, scope.keyPanSpeed); needsUpdate = true; break;
                        case scope.keys.BOTTOM: pan(0, -scope.keyPanSpeed); needsUpdate = true; break;
                        case scope.keys.LEFT: pan(scope.keyPanSpeed, 0); needsUpdate = true; break;
                        case scope.keys.RIGHT: pan(-scope.keyPanSpeed, 0); needsUpdate = true; break;
                    }
                    if (needsUpdate) { event.preventDefault(); scope.update(); }
                }
                function handleTouchStartRotate() {
                    if (pointers.length === 1) { rotateStart.set(pointers[0].pageX, pointers[0].pageY); } else {
                        var x = 0.5 * (pointers[0].pageX + pointers[1].pageX);
                        var y = 0.5 * (pointers[0].pageY + pointers[1].pageY);
                        rotateStart.set(x, y);
                    }
                }
                function handleTouchStartPan() {
                    if (pointers.length === 1) { panStart.set(pointers[0].pageX, pointers[0].pageY); } else {
                        var x = 0.5 * (pointers[0].pageX + pointers[1].pageX);
                        var y = 0.5 * (pointers[0].pageY + pointers[1].pageY);
                        panStart.set(x, y);
                    }
                }
                function handleTouchStartDolly() {
                    var dx = pointers[0].pageX - pointers[1].pageX;
                    var dy = pointers[0].pageY - pointers[1].pageY;
                    var distance = Math.sqrt(dx * dx + dy * dy);
                    dollyStart.set(0, distance);
                }
                function handleTouchStartDollyPan() { if (scope.enableZoom) handleTouchStartDolly(); if (scope.enablePan) handleTouchStartPan(); }
                function handleTouchStartDollyRotate() { if (scope.enableZoom) handleTouchStartDolly(); if (scope.enableRotate) handleTouchStartRotate(); }
                function handleTouchMoveRotate(event) {
                    if (pointers.length == 1) { rotateEnd.set(event.pageX, event.pageY); } else {
                        var x = 0.5 * (event.pageX + pointers[1].pageX);
                        var y = 0.5 * (event.pageY + pointers[1].pageY);
                        rotateEnd.set(x, y);
                    }
                    rotateDelta.subVectors(rotateEnd, rotateStart).multiplyScalar(scope.rotateSpeed);
                    var element = scope.domElement;
                    rotateLeft(2 * Math.PI * rotateDelta.x / element.clientHeight);
                    rotateUp(2 * Math.PI * rotateDelta.y / element.clientHeight);
                    rotateStart.copy(rotateEnd);
                }
                function handleTouchMovePan(event) {
                    if (pointers.length === 1) { panEnd.set(event.pageX, event.pageY); } else {
                        var x = 0.5 * (event.pageX + pointers[1].pageX);
                        var y = 0.5 * (event.pageY + pointers[1].pageY);
                        panEnd.set(x, y);
                    }
                    panDelta.subVectors(panEnd, panStart).multiplyScalar(scope.panSpeed);
                    pan(panDelta.x, panDelta.y);
                    panStart.copy(panEnd);
                }
                function handleTouchMoveDolly(event) {
                    var dx = event.pageX - pointers[1].pageX;
                    var dy = event.pageY - pointers[1].pageY;
                    var distance = Math.sqrt(dx * dx + dy * dy);
                    dollyEnd.set(0, distance);
                    dollyDelta.set(0, Math.pow(dollyEnd.y / dollyStart.y, scope.zoomSpeed));
                    dollyOut(dollyDelta.y);
                    dollyStart.copy(dollyEnd);
                }
                function handleTouchMoveDollyPan(event) { if (scope.enableZoom) handleTouchMoveDolly(event); if (scope.enablePan) handleTouchMovePan(event); }
                function handleTouchMoveDollyRotate(event) { if (scope.enableZoom) handleTouchMoveDolly(event); if (scope.enableRotate) handleTouchMoveRotate(event); }
                function handleTouchEnd() {}
                function onPointerDown(event) {
                    if (scope.enabled === false) return;
                    if (pointers.length === 0) { scope.domElement.setPointerCapture(event.pointerId); scope.domElement.addEventListener('pointermove', onPointerMove); scope.domElement.addEventListener('pointerup', onPointerUp); }
                    addPointer(event);
                    if (event.pointerType === 'touch') { onTouchStart(event); } else { onMouseDown(event); }
                }
                function onPointerMove(event) { if (scope.enabled === false) return; if (event.pointerType === 'touch') { onTouchMove(event); } else { onMouseMove(event); } }
                function onPointerUp(event) {
                    removePointer(event);
                    if (pointers.length === 0) { scope.domElement.releasePointerCapture(event.pointerId); scope.domElement.removeEventListener('pointermove', onPointerMove); scope.domElement.removeEventListener('pointerup', onPointerUp); }
                    scope.dispatchEvent({ type: 'end' }); // Lỗi xảy ra ở đây
                    state = STATE.NONE;
                }
                function onMouseDown(event) {
                    var button = -1;
                    if (event.button === 0) { button = scope.mouseButtons.LEFT; } else if (event.button === 1) { button = scope.mouseButtons.MIDDLE; } else if (event.button === 2) { button = scope.mouseButtons.RIGHT; }
                    if (button !== -1) {
                        switch (button) {
                            case THREE.MOUSE.DOLLY: if (scope.enableZoom === false) return; handleMouseDownDolly(event); state = STATE.DOLLY; break;
                            case THREE.MOUSE.ROTATE: if (event.ctrlKey || event.metaKey || event.shiftKey) { if (scope.enablePan === false) return; handleMouseDownPan(event); state = STATE.PAN; } else { if (scope.enableRotate === false) return; handleMouseDownRotate(event); state = STATE.ROTATE; } break;
                            case THREE.MOUSE.PAN: if (event.ctrlKey || event.metaKey || event.shiftKey) { if (scope.enableRotate === false) return; handleMouseDownRotate(event); state = STATE.ROTATE; } else { if (scope.enablePan === false) return; handleMouseDownPan(event); state = STATE.PAN; } break;
                        }
                        if (state !== STATE.NONE) { scope.dispatchEvent({ type: 'start' }); } // Lỗi xảy ra ở đây
                    }
                }
                function onMouseMove(event) {
                    if (scope.enabled === false) return;
                    switch (state) {
                        case STATE.ROTATE: if (scope.enableRotate === false) return; handleMouseMoveRotate(event); break;
                        case STATE.DOLLY: if (scope.enableZoom === false) return; handleMouseMoveDolly(event); break;
                        case STATE.PAN: if (scope.enablePan === false) return; handleMouseMovePan(event); break;
                    }
                }
                function onMouseWheel(event) { if (scope.enabled === false || scope.enableZoom === false || (state !== STATE.NONE && state !== STATE.ROTATE)) return; event.preventDefault(); scope.dispatchEvent({ type: 'start' }); handleMouseWheel(event); scope.dispatchEvent({ type: 'end' }); }
                function onKeyDown(event) { if (scope.enabled === false || scope.enablePan === false) return; handleKeyDown(event); }
                function onTouchStart(event) {
                    trackPointer(event);
                    switch (pointers.length) {
                        case 1:
                            switch (scope.touches.ONE) {
                                case THREE.TOUCH.ROTATE: if (scope.enableRotate === false) return; handleTouchStartRotate(); state = STATE.TOUCH_ROTATE; break;
                                case THREE.TOUCH.PAN: if (scope.enablePan === false) return; handleTouchStartPan(); state = STATE.TOUCH_PAN; break;
                                default: state = STATE.NONE;
                            }
                            break;
                        case 2:
                            switch (scope.touches.TWO) {
                                case THREE.TOUCH.DOLLY_PAN: if (scope.enableZoom === false && scope.enablePan === false) return; handleTouchStartDollyPan(); state = STATE.TOUCH_DOLLY_PAN; break;
                                case THREE.TOUCH.DOLLY_ROTATE: if (scope.enableZoom === false && scope.enableRotate === false) return; handleTouchStartDollyRotate(); state = STATE.TOUCH_DOLLY_ROTATE; break;
                                default: state = STATE.NONE;
                            }
                            break;
                        default: state = STATE.NONE;
                    }
                    if (state !== STATE.NONE) { scope.dispatchEvent({ type: 'start' }); }
                }
                function onTouchMove(event) {
                    trackPointer(event);
                    switch (state) {
                        case STATE.TOUCH_ROTATE: if (scope.enableRotate === false) return; handleTouchMoveRotate(event); scope.update(); break;
                        case STATE.TOUCH_PAN: if (scope.enablePan === false) return; handleTouchMovePan(event); scope.update(); break;
                        case STATE.TOUCH_DOLLY_PAN: if (scope.enableZoom === false && scope.enablePan === false) return; handleTouchMoveDollyPan(event); scope.update(); break;
                        case STATE.TOUCH_DOLLY_ROTATE: if (scope.enableZoom === false && scope.enableRotate === false) return; handleTouchMoveDollyRotate(event); scope.update(); break;
                        default: state = STATE.NONE;
                    }
                }
                function onContextMenu(event) { if (scope.enabled === false) return; event.preventDefault(); }
                var pointers = [];
                var pointerPositions = {};
                function addPointer(event) { pointers.push(event); }
                function removePointer(event) { delete pointerPositions[event.pointerId]; for (var i = 0; i < pointers.length; i++) { if (pointers[i].pointerId == event.pointerId) { pointers.splice(i, 1); return; } } }
                function trackPointer(event) { var position = pointerPositions[event.pointerId]; if (position === undefined) { position = new THREE.Vector2(); pointerPositions[event.pointerId] = position; } position.set(event.pageX, event.pageY); }
                this.domElement.addEventListener('contextmenu', onContextMenu);
                this.domElement.addEventListener('pointerdown', onPointerDown);
                this.domElement.addEventListener('pointercancel', onPointerUp);
                this.domElement.addEventListener('wheel', onMouseWheel, { passive: false });
                this.update();
            }
            
            //  ↓↓↓ SỬA LỖI Ở ĐÂY ↓↓↓
            //  Thêm dòng này để thực hiện kế thừa từ THREE.EventDispatcher
            OrbitControls.prototype = Object.create(THREE.EventDispatcher.prototype);
            OrbitControls.prototype.constructor = OrbitControls;
            //  ↑↑↑ SỬA LỖI Ở ĐÂY ↑↑↑

            THREE.OrbitControls = OrbitControls;
        })();
    </script>

    <!-- Tải file script 3D của bạn -->
    <script src="./3d-radar-layer.js"></script>

    <!-- Script khởi tạo -->
    <script>
        // --- CẤU HÌNH ---
        const RADAR_IMAGE_URL = './1.png'; 
        const IMAGE_DIMENSIONS = { width: 1024, height: 1024 }; // Giảm kích thước để nhẹ hơn

        // --- BIẾN TOÀN CỤC ---
        let scene, camera, renderer, controls, cloudMesh;

        const params = {
            startHeight: 50,
            maxCloudHeight: 500,
            opacity: 1.5,
            verticalExaggeration: 1.0,
        };

        init();
        
        async function init() {
            // 1. THIẾT LẬP SCENE, CAMERA, RENDERER
            scene = new THREE.Scene();
            camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 1, 5000);
            camera.position.set(0, 700, 1200);

            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setPixelRatio(window.devicePixelRatio);
            renderer.setSize(window.innerWidth, window.innerHeight);
            document.body.appendChild(renderer.domElement);

            controls = new THREE.OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.target.set(0, params.maxCloudHeight / 3, 0);

            // 2. TẠO NỀN SÀN VÀ LƯỚI
            const floorGeometry = new THREE.PlaneGeometry(IMAGE_DIMENSIONS.width, IMAGE_DIMENSIONS.height);
            const floorMaterial = new THREE.MeshBasicMaterial({ color: 0x000000, side: THREE.DoubleSide });
            const floorMesh = new THREE.Mesh(floorGeometry, floorMaterial);
            floorMesh.rotation.x = -Math.PI / 2;
            scene.add(floorMesh);

            const gridHelper = new THREE.GridHelper(IMAGE_DIMENSIONS.width, 20, 0x444444, 0x444444);
            gridHelper.position.y = 0.1;
            scene.add(gridHelper);

            // 3. TẢI ẢNH RADAR VÀ TẠO LỚP MÂY
            try {
                const radarTexture = await new THREE.TextureLoader().loadAsync(RADAR_IMAGE_URL);
                floorMesh.material.map = radarTexture;
                floorMesh.material.needsUpdate = true;
                
                // Khởi tạo Lớp mây 3D
                cloudMesh = new window.Custom3DLayer(
                    '3d-radar-cloud',
                    {
                        bounds: [-180, -90, 180, 90],
                        altitudeMin: params.startHeight,
                        altitudeMax: params.maxCloudHeight
                    },
                    new window.CustomColoring.OpacityColoringFragment({
                        decode: { min: 0.1, max: 1.0, channel: 'r' },
                        color: [220, 225, 240],
                        opacity: params.opacity
                    })
                );
                
                // Giả lập onAdd để khởi tạo renderer bên trong lớp
                const mockMap = { getCanvas: () => renderer.domElement };
                cloudMesh.onAdd(mockMap, renderer.getContext());
                cloudMesh.addSource(RADAR_IMAGE_URL);

                document.getElementById('info').style.display = 'block';

            } catch (error) {
                console.error("Không thể tải ảnh radar:", error);
                document.getElementById('info').innerHTML = `<h1 style="color:red;">Lỗi tải ảnh. Hãy chắc chắn file '${RADAR_IMAGE_URL}' tồn tại và kiểm tra Console.</h1>`;
            }
            
            animate();
            window.addEventListener('resize', onWindowResize);
        }

        function animate() {
            requestAnimationFrame(animate);
            controls.update();

            // Hàm render của lớp 3D cần một ma trận chiếu
            if (cloudMesh) {
                const projectionMatrix = new THREE.Matrix4().multiplyMatrices(
                    camera.projectionMatrix,
                    camera.matrixWorldInverse
                );
                // Cập nhật các tham số từ GUI (nếu có)
                cloudMesh.verticalExaggeration = params.verticalExaggeration;
                cloudMesh.options.altitudeMin = params.startHeight;
                cloudMesh.options.altitudeMax = params.maxCloudHeight;
                cloudMesh.setOpacity(params.opacity);
                
                // Giả lập việc render của Mapbox/MapLibre
                cloudMesh.render(renderer.getContext(), projectionMatrix.toArray());
            }

            // Chúng ta không gọi renderer.render(scene, camera) vì lớp custom đã tự render
        }

        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }
    </script>
</body>
</html>