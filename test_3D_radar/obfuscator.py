import subprocess
import sys
import os
import base64
import re

def obfuscate_js(input_file, output_file):
    # Kiểm tra xem javascript-obfuscator có cài chưa
    try:
        subprocess.run(['javascript-obfuscator', '--version'], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    except FileNotFoundError:
        print("Lỗi: javascript-obfuscator chưa được cài. Hãy cài Node.js và chạy 'npm install -g javascript-obfuscator' trước.")
        sys.exit(1)

    # Lệnh CLI để obfuscate + minify (compact=true để minify)
    command = [
        'javascript-obfuscator',
        input_file,
        '--output', output_file,
        '--compact', 'true',  # Minification
        '--self-defending', 'false',  # Tùy chọn, để tránh code tự bảo vệ có thể break ở một số trường hợp
        '--string-array', 'true',  # Thêm string obfuscation
        '--string-array-encoding', 'base64',  # Encode strings
        '--string-array-threshold', '0.75'  # Threshold cho string obfuscation
    ]

    try:
        result = subprocess.run(command, check=True, capture_output=True, text=True)
        print(f"Đã obfuscate thành công! File output: {output_file}")
        print(result.stdout)
    except subprocess.CalledProcessError as e:
        print(f"Lỗi khi chạy: {e.stderr}")
        sys.exit(1)

def process_imports_and_code(input_file):
    """Tách import statements và code logic"""
    with open(input_file, 'r') as f:
        content = f.read()
    
    # Tách import statements
    import_pattern = r'import\s+[^;]+;'
    imports = re.findall(import_pattern, content)
    
    # Loại bỏ import statements khỏi code
    code_without_imports = re.sub(import_pattern, '', content)
    
    return imports, code_without_imports

def create_final_code(imports, obfuscated_logic_code):
    """Tạo code cuối cùng với imports nguyên gốc và logic encrypted"""
    
    # Giữ nguyên import statements (không obfuscate)
    original_imports = '\n'.join(imports)
    
    # Encode logic code
    encoded_logic = base64.b64encode(obfuscated_logic_code.encode('utf-8')).decode('utf-8')
    
    # Tạo final code
    final_code = f"""
{original_imports}

// Protected logic code
(async function() {{
    try {{
        const code = atob('{encoded_logic}');
        eval(code);
    }} catch (error) {{
        console.error('Logic execution error:', error);
    }}
}})();
"""
    
    return final_code

if __name__ == "__main__":
    input_file = "core_original.js"
    output_file = "core_temp.js"
    output_file_final = "core.js"

    if not os.path.exists(input_file):
        print(f"Lỗi: File input '{input_file}' không tồn tại.")
        sys.exit(1)

    # Tách imports và code
    imports, code_without_imports = process_imports_and_code(input_file)
    
    # Tạo file tạm chỉ chứa code logic (không có imports)
    temp_file = "temp_code.js"
    with open(temp_file, 'w') as f:
        f.write(code_without_imports)
    
    # Obfuscate code logic
    obfuscate_js(temp_file, output_file)
    
    # Đọc obfuscated code
    with open(output_file, 'r') as file:
        obfuscated_code = file.read()
    
    # Tạo final code với imports obfuscated và logic encrypted
    final_code = create_final_code(imports, obfuscated_code)
    with open(output_file_final, 'w') as file:
        file.write(final_code)
    
    # Xóa file temp
    os.remove(output_file)
    os.remove(temp_file)
    print(f"✅ Đã tạo file {output_file_final} thành công!")
    print("📝 Import statements: Obfuscated")
    print("🔒 Logic code: Obfuscated + Encrypted")