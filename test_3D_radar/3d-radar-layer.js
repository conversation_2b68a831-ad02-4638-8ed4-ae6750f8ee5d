/**
 * 3d-radar-layer.js (FIXED 3)
 * 
 * <PERSON><PERSON><PERSON> viện này cung cấp một lớp tùy chỉnh (VolumeLayer) cho MapLibre GL JS 
 * để hiển thị dữ liệu từ một ảnh PNG dưới dạng khối mây 3D.
 */

const EventEmitter = class {
    constructor() { this._events = {}; }
    on(event, listener) {
        (this._events[event] = this._events[event] || []).push(listener);
    }
    emit(event, ...args) {
        (this._events[event] || []).forEach(listener => listener.apply(this, args));
    }
};

const THREE = window.THREE;

class ColoringFragmentBase {
    constructor(fragmentShaderCode, decodeOptions) {
        this.fragmentShaderCode = fragmentShaderCode;
        this.decodeOptions = {
            min: decodeOptions.min !== undefined ? decodeOptions.min : 0,
            max: decodeOptions.max !== undefined ? decodeOptions.max : 1,
            channel: decodeOptions.channel || 'r'
        };
    }
    
    getShaderCode(functionName) {
        const valueCalculation = `float v = ${this.decodeOptions.min.toFixed(5)} + data.${this.decodeOptions.channel} * ${(this.decodeOptions.max - this.decodeOptions.min).toFixed(5)};`;
        
        return `
            vec4 ${functionName}(vec4 data) {
                ${valueCalculation}
                ${this.fragmentShaderCode}
                return c;
            }
        `;
    }
}

class OpacityColoringFragment extends ColoringFragmentBase {
    constructor(options) {
        const { decode, color = [255, 255, 255], opacity = 1.0 } = options;
        const colorVec4 = `vec4(${(color[0]/255).toFixed(3)}, ${(color[1]/255).toFixed(3)}, ${(color[2]/255).toFixed(3)}, 1.0)`;
        const shaderCode = `
            vec4 c = ${colorVec4};
            c.a = v * ${opacity.toFixed(3)};
        `;
        super(shaderCode, decode);
    }
}

class VolumeLayer extends EventEmitter {
    constructor(id, options, coloring) {
        super();
        this.id = id;
        this.type = 'custom';
        this.renderingMode = '3d';
        
        this.options = {
            performance: options.performance || {},
            bounds: options.bounds || [-180, -85.05, 180, 85.05],
            altitudeMin: options.altitudeMin || 0,
            altitudeMax: options.altitudeMax || 15000,
        };
        
        this.coloring = coloring;
        this.verticalExaggeration = 1.0;
        
        this._camera = new THREE.OrthographicCamera(-0.5, 0.5, 0.5, -0.5, 0, 1);
        this._scene = new THREE.Scene();
        this._renderer = null;
        
        const planeGeometry = new THREE.PlaneGeometry(1, 1);
        this._createMaterial();
        this._mesh = new THREE.Mesh(planeGeometry, this._material);
        this._scene.add(this._mesh);

        this._setupTransforms();
    }

    _createMaterial() {
        // --- SỬA LỖI Ở ĐÂY ---
        // Xóa dòng '#extension' khỏi shader string
        const fragmentShader = `
            uniform sampler2D u_texture;
            uniform float u_altitude_min_norm;
            uniform float u_altitude_max_norm;
            uniform float u_opacity_multiplier;

            varying vec2 v_tex_coord;

            ${this.coloring.getShaderCode('_colorize')}

            void main() {
                float data_value = texture2D(u_texture, v_tex_coord).r;
                
                if (data_value < 0.05) {
                    discard;
                }

                // Sử dụng gl_FragDepthEXT để ghi độ sâu tùy chỉnh
                gl_FragDepthEXT = u_altitude_min_norm + data_value * (u_altitude_max_norm - u_altitude_min_norm);
                
                vec4 color = _colorize(vec4(data_value));
                color.a *= u_opacity_multiplier;
                
                gl_FragColor = color;
            }
        `;

        this._material = new THREE.ShaderMaterial({
            uniforms: {
                u_texture: { value: null },
                u_altitude_min_norm: { value: 0 },
                u_altitude_max_norm: { value: 0 },
                u_opacity_multiplier: { value: 1.0 },
            },
            vertexShader: `
                varying vec2 v_tex_coord;
                void main() {
                    v_tex_coord = uv;
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `,
            fragmentShader: fragmentShader,
            transparent: true,
            depthTest: true,
            depthWrite: false,
            // --- SỬA LỖI Ở ĐÂY ---
            // Thêm thuộc tính 'extensions' để Three.js tự động xử lý
            extensions: {
                fragDepth: true
            }
        });
    }

    _setupTransforms() {
        // ... (Giữ nguyên)
        const mercatorToNormal = (lon, lat) => {
            const x = (lon + 180) / 360;
            const y = (180 - (180 / Math.PI * Math.log(Math.tan(Math.PI / 4 + lat * Math.PI / 360)))) / 360;
            return [x, y];
        };
        
        const [minX, minY] = mercatorToNormal(this.options.bounds[0], this.options.bounds[3]);
        const [maxX, maxY] = mercatorToNormal(this.options.bounds[2], this.options.bounds[1]);
        
        const scaleX = maxX - minX;
        const scaleY = maxY - minY;

        this._mesh.position.set(minX + scaleX / 2, minY + scaleY / 2, 0);
        this._mesh.scale.set(scaleX, scaleY, 1);
    }
    
    addSource(imageUrl) {
        new THREE.TextureLoader().load(imageUrl, (texture) => {
            this._material.uniforms.u_texture.value = texture;
            this.emit('source-loaded');
        }, undefined, (error) => {
            console.error("Lỗi tải ảnh:", error);
            this.emit('error', error);
        });
    }

    setOpacity(opacity) {
        this._material.uniforms.u_opacity_multiplier.value = opacity;
    }

    onAdd(map, gl) {
        this.map = map;
        this._renderer = new THREE.WebGLRenderer({
            canvas: map.getCanvas(),
            context: gl,
            antialias: true,
        });
        this._renderer.autoClear = false;
    }

    render(gl, matrix) {
        this._camera.projectionMatrix.fromArray(matrix);
        
        const earthCircumference = 2 * Math.PI * 6378137;
        const altitudeScale = (this.options.altitudeMax - this.options.altitudeMin) / earthCircumference;
        const modelMatrix = new THREE.Matrix4()
             .makeScale(this._mesh.scale.x, this._mesh.scale.y, altitudeScale * this.verticalExaggeration)
             .setPosition(this._mesh.position.x, this._mesh.position.y, this.options.altitudeMin / earthCircumference);

        this._mesh.matrixWorld.copy(modelMatrix);
        this._mesh.modelViewMatrix.multiplyMatrices(this._camera.matrixWorldInverse, this._mesh.matrixWorld);
        
        const vecMin = new THREE.Vector3(0, 0, 0);
        const vecMax = new THREE.Vector3(0, 0, 1);

        vecMin.applyMatrix4(this._mesh.modelViewMatrix);
        vecMax.applyMatrix4(this._mesh.modelViewMatrix);
        
        vecMin.applyMatrix4(this._camera.projectionMatrix);
        vecMax.applyMatrix4(this._camera.projectionMatrix);

        const zMin = (vecMin.z / vecMin.w + 1) / 2;
        const zMax = (vecMax.z / vecMax.w + 1) / 2;
        
        this._material.uniforms.u_altitude_min_norm.value = zMin;
        this._material.uniforms.u_altitude_max_norm.value = zMax;
        
        this._renderer.state.reset();
        this._renderer.render(this._scene, this._camera);
    }
}

window.Custom3DLayer = VolumeLayer;
window.CustomColoring = { OpacityColoringFragment };