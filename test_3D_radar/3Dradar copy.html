<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Radar Cloud Visualization (WebGPU)</title>
    <style>
        body { margin: 0; overflow: hidden; background-color: #111; color: #fff; font-family: monospace; }
        canvas { display: block; }
        #loader {
            position: absolute; top: 50%; left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.5em;
        }
    </style>
</head>
<body>
    <div id="loader">Đang tải dữ liệu radar...</div>

    <script type="importmap">
        {
            "imports": {
                "three": "https://unpkg.com/three@0.165.0/build/three.module.js",
                "three/addons/": "https://unpkg.com/three@0.165.0/examples/jsm/",
                "lil-gui": "https://unpkg.com/lil-gui@0.19.2/dist/lil-gui.esm.js"
            }
        }
    </script>

    <script type="module">
        import * as THREE from 'three';
        import WebGPURenderer from 'three/addons/renderers/webgpu/WebGPURenderer.js';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
        import GUI from 'lil-gui';

        // --- CẤU HÌNH ---
        // !!! QUAN TRỌNG: Thay thế bằng đường dẫn đến file ảnh của bạn !!!
        const RADAR_IMAGE_URL = './radar_data/1.png'; 
        const IMAGE_DIMENSIONS = { width: 2310, height: 2310 };
        const MAX_PARTICLES = 2_000_000; // Tăng nếu cần chi tiết hơn, giảm nếu bị lag

        // --- KHỞI TẠO BIẾN TOÀN CỤC ---
        let scene, camera, renderer, controls;
        let floorMesh, gridHelper, particleSystem;
        let imageData = null;
        let isImageLoaded = false;
        let isUpdateNeeded = true;

        // --- BẢN ĐỒ THANG MÀU DBZ ---
        const dbzMap = [
            { r: 103, g: 213, b: 252, dbz: 10 },  // 67D5FC
            { r: 2,   g: 109, b: 249, dbz: 15 },  // 026DF9
            { r: 6,   g: 70,  b: 248, dbz: 20 },  // 0646F8
            { r: 167, g: 251, b: 132, dbz: 25 },  // A7FB84
            { r: 87,  g: 250, b: 36,  dbz: 30 },  // 57FA24
            { r: 6,   g: 224, b: 50,  dbz: 35 },  // 06E032
            { r: 255, g: 216, b: 0,   dbz: 40 },  // FFD800
            { r: 255, g: 166, b: 0,   dbz: 45 },  // FFA600
            { r: 254, g: 130, b: 19,  dbz: 55 },  // FE8213
            { r: 255, g: 28,  b: 0,   dbz: 60 },  // FF1C00
            { r: 204, g: 1,   b: 113, dbz: 65 },  // CC0171
            { r: 153, g: 0,   b: 204, dbz: 70 },  // 9900CC
            { r: 0,   g: 0,   b: 0,   dbz: 75 },  // > 70
        ];
        const colorCache = new Map(); // Cache để tăng tốc độ tìm màu

        const params = {
            roiX: Math.floor(IMAGE_DIMENSIONS.width / 2 - 200),
            roiY: Math.floor(IMAGE_DIMENSIONS.height / 2 - 200),
            roiWidth: 400,
            roiHeight: 400,
            startHeight: 5,
            maxCloudHeight: 250,
            particleDensity: 15.0, // Số hạt tối đa cho 1 pixel ở cường độ cao nhất
            particleSize: 5.0,
            floorColor: '#333333',
            gridColor: '#555555',
            // Tùy chọn điều chỉnh hệ tọa độ
            flipX: false,
            flipZ: true,
            offsetX: 0,
            offsetZ: 0,
        };

        init();
        
        async function init() {
            // Scene
            scene = new THREE.Scene();

            // Camera
            camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 1, 5000);
            camera.position.set(0, 800, 1500);

            // Renderer
            renderer = new WebGPURenderer({ antialias: true });
            renderer.setPixelRatio(window.devicePixelRatio);
            renderer.setSize(window.innerWidth, window.innerHeight);
            document.body.appendChild(renderer.domElement);
            await renderer.init();

            // Controls
            controls = new OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.target.set(0, params.maxCloudHeight / 4, 0);

            // Floor & Grid
            gridHelper = new THREE.GridHelper(IMAGE_DIMENSIONS.width, 50, params.gridColor, params.gridColor);
            gridHelper.position.y = 0.1; // Nâng lên 1 chút để không bị z-fighting
            scene.add(gridHelper);

            const floorGeometry = new THREE.PlaneGeometry(IMAGE_DIMENSIONS.width, IMAGE_DIMENSIONS.height);
            const floorMaterial = new THREE.MeshBasicMaterial({ color: params.floorColor, side: THREE.DoubleSide });
            floorMesh = new THREE.Mesh(floorGeometry, floorMaterial);
            floorMesh.rotation.x = -Math.PI / 2;
            scene.add(floorMesh);

            // Particle System
            setupParticleSystem();

            // GUI
            setupGUI();

            // Load Image Data
            await loadImageData();

            // Start Animation Loop
            animate();

            window.addEventListener('resize', onWindowResize);
        }

        function setupParticleSystem() {
            const geometry = new THREE.BufferGeometry();
            const positions = new Float32Array(MAX_PARTICLES * 3);
            const colors = new Float32Array(MAX_PARTICLES * 3);

            geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

            const material = new THREE.PointsMaterial({
                size: params.particleSize,
                vertexColors: true,
                blending: THREE.AdditiveBlending,
                depthWrite: false,
                transparent: true,
                sizeAttenuation: true,
            });

            particleSystem = new THREE.Points(geometry, material);
            particleSystem.frustumCulled = false; // Quan trọng để không bị mất hạt khi camera di chuyển
            scene.add(particleSystem);
        }

        function setupGUI() {
            const gui = new GUI();
            const roiFolder = gui.addFolder('Region of Interest (ROI)');
            roiFolder.add(params, 'roiX', 0, IMAGE_DIMENSIONS.width - params.roiWidth, 1).name('Vị trí X').onChange(() => isUpdateNeeded = true);
            roiFolder.add(params, 'roiY', 0, IMAGE_DIMENSIONS.height - params.roiHeight, 1).name('Vị trí Y').onChange(() => isUpdateNeeded = true);
            roiFolder.add(params, 'roiWidth', 100, 1000, 10).name('Chiều rộng').onChange(() => isUpdateNeeded = true);
            roiFolder.add(params, 'roiHeight', 100, 1000, 10).name('Chiều cao').onChange(() => isUpdateNeeded = true);
            
            const cloudFolder = gui.addFolder('Cloud Properties');
            cloudFolder.add(params, 'startHeight', 0, 200, 1).name('Chân mây (Y)').onChange(() => isUpdateNeeded = true);
            cloudFolder.add(params, 'maxCloudHeight', 50, 1000, 5).name('Chiều cao tối đa').onChange(() => isUpdateNeeded = true);
            cloudFolder.add(params, 'particleDensity', 1, 50, 1).name('Mật độ hạt').onChange(() => isUpdateNeeded = true);
            cloudFolder.add(params, 'particleSize', 1, 20, 0.5).name('Kích thước hạt').onChange(val => {
                particleSystem.material.size = val;
            });
            
            const sceneFolder = gui.addFolder('Scene Settings');
            sceneFolder.addColor(params, 'floorColor').name('Màu nền').onChange(val => floorMesh.material.color.set(val));
            sceneFolder.addColor(params, 'gridColor').name('Màu lưới').onChange(val => gridHelper.material.color.set(val));
            
            const coordFolder = gui.addFolder('Coordinate System');
            coordFolder.add(params, 'flipX').name('Đảo ngược X').onChange(() => isUpdateNeeded = true);
            coordFolder.add(params, 'flipZ').name('Đảo ngược Z').onChange(() => isUpdateNeeded = true);
            coordFolder.add(params, 'offsetX', -500, 500, 1).name('Dịch chuyển X').onChange(() => isUpdateNeeded = true);
            coordFolder.add(params, 'offsetZ', -500, 500, 1).name('Dịch chuyển Z').onChange(() => isUpdateNeeded = true);

            gui.onChange(() => {
                // Đảm bảo slider X/Y không đi ra ngoài ảnh khi thay đổi kích thước ROI
                const maxX = IMAGE_DIMENSIONS.width - params.roiWidth;
                const maxY = IMAGE_DIMENSIONS.height - params.roiHeight;
                if (params.roiX > maxX) params.roiX = maxX;
                if (params.roiY > maxY) params.roiY = maxY;
                gui.controllers.forEach(c => c.updateDisplay());
            });
        }
        
        function loadImageData() {
            return new Promise((resolve, reject) => {
                const loader = new THREE.TextureLoader();
                loader.load(
                    RADAR_IMAGE_URL,
                    (texture) => {
                        console.log("Radar image loaded successfully.");
                        // Áp ảnh radar lên sàn
                        floorMesh.material.map = texture;
                        floorMesh.material.needsUpdate = true;
                        
                        // Lấy dữ liệu pixel
                        const image = texture.image;
                        const canvas = document.createElement('canvas');
                        canvas.width = image.width;
                        canvas.height = image.height;
                        const context = canvas.getContext('2d', { willReadFrequently: true });
                        context.drawImage(image, 0, 0);
                        imageData = context.getImageData(0, 0, image.width, image.height);
                        
                        isImageLoaded = true;
                        document.getElementById('loader').style.display = 'none';
                        updateClouds();
                        resolve();
                    },
                    undefined,
                    (err) => {
                        console.error('An error occurred loading the radar image.', err);
                        document.getElementById('loader').innerText = "Lỗi: Không thể tải ảnh radar. Kiểm tra đường dẫn và Console (F12).";
                        reject(err);
                    }
                );
            });
        }

        // Hàm tìm kiếm giá trị DBZ từ màu pixel
        function getDBZFromColor(r, g, b) {
            const colorKey = (r << 16) | (g << 8) | b;
            if (colorCache.has(colorKey)) {
                return colorCache.get(colorKey);
            }

            if (r === 0 && g === 0 && b === 0) return 75; // Trường hợp đặc biệt >70 dBZ

            let minDistanceSq = Infinity;
            let foundDBZ = 0;

            for (const colorInfo of dbzMap) {
                const distSq = Math.pow(r - colorInfo.r, 2) + Math.pow(g - colorInfo.g, 2) + Math.pow(b - colorInfo.b, 2);
                if (distSq < minDistanceSq) {
                    minDistanceSq = distSq;
                    foundDBZ = colorInfo.dbz;
                }
            }

            colorCache.set(colorKey, foundDBZ);
            return foundDBZ;
        }

        // Hàm cốt lõi: Cập nhật các hạt mây dựa trên ROI
        function updateClouds() {
            if (!isImageLoaded) return;
            console.time('updateClouds');

            const positions = particleSystem.geometry.attributes.position.array;
            const colors = particleSystem.geometry.attributes.color.array;
            let particleCount = 0;

            const roiX_start = Math.floor(params.roiX);
            const roiY_start = Math.floor(params.roiY);
            const roiX_end = roiX_start + Math.floor(params.roiWidth);
            const roiY_end = roiY_start + Math.floor(params.roiHeight);

            for (let y = roiY_start; y < roiY_end; y++) {
                for (let x = roiX_start; x < roiX_end; x++) {
                    if (particleCount >= MAX_PARTICLES) break;

                    const i = (y * IMAGE_DIMENSIONS.width + x) * 4;
                    const r = imageData.data[i];
                    const g = imageData.data[i + 1];
                    const b = imageData.data[i + 2];
                    const a = imageData.data[i + 3];

                    if (a < 50) continue; // Bỏ qua pixel trong suốt

                    const dbz = getDBZFromColor(r, g, b);
                    if (dbz < 10) continue; // Bỏ qua vùng mưa quá nhẹ

                    const intensity = (dbz - 10) / (75 - 10); // Chuẩn hóa cường độ từ 0 -> 1
                    const cloudTopHeight = params.startHeight + intensity * params.maxCloudHeight;
                    const numParticles = Math.ceil(intensity * params.particleDensity);

                    for (let p = 0; p < numParticles; p++) {
                        if (particleCount >= MAX_PARTICLES) break;
                        
                        const idx = particleCount * 3;

                        // Tọa độ hạt, có thêm jitter để trông tự nhiên
                        let pX = x - IMAGE_DIMENSIONS.width / 2 + (Math.random() - 0.5) * 2;
                        const pY = params.startHeight + Math.random() * (cloudTopHeight - params.startHeight);
                        let pZ = (y - IMAGE_DIMENSIONS.height / 2) + (Math.random() - 0.5) * 2;
                        
                        // Áp dụng các tùy chọn điều chỉnh hệ tọa độ
                        if (params.flipX) pX = -pX;
                        if (params.flipZ) pZ = -pZ;
                        pX += params.offsetX;
                        pZ += params.offsetZ;

                        positions[idx] = pX;
                        positions[idx + 1] = pY;
                        positions[idx + 2] = pZ;

                        colors[idx] = r / 255;
                        colors[idx + 1] = g / 255;
                        colors[idx + 2] = b / 255;
                        
                        particleCount++;
                    }
                }
                if (particleCount >= MAX_PARTICLES) {
                    console.warn(`Đã đạt giới hạn ${MAX_PARTICLES} hạt. Tăng MAX_PARTICLES hoặc giảm mật độ.`);
                    break;
                }
            }

            particleSystem.geometry.setDrawRange(0, particleCount);
            particleSystem.geometry.attributes.position.needsUpdate = true;
            particleSystem.geometry.attributes.color.needsUpdate = true;
            
            isUpdateNeeded = false;
            console.timeEnd('updateClouds');
        }

        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }

        function animate() {
            requestAnimationFrame(animate);
            controls.update();

            // Chỉ cập nhật khi có thay đổi từ GUI
            if (isUpdateNeeded) {
                updateClouds();
            }

            renderer.render(scene, camera);
        }
    </script>
</body>
</html>