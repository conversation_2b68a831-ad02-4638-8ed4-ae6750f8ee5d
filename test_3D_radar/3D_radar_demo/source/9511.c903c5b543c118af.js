try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="63bc2fc5-988d-445c-929b-4e56a29963c1",e._sentryDebugIdIdentifier="sentry-dbid-63bc2fc5-988d-445c-929b-4e56a29963c1")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9511],{9511:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return x}});var a=r(85893),n=r(40645),l=r.n(n),o=r(11163),s=r(4298),d=r.n(s),i=r(67294);let u=new String("#vals{position:fixed;left:10px;bottom:10px;border-collapse:collapse;font-size:.7em}span.mapboxgl-ctrl-icon{display:none!important}#vals td{width:50px;height:24px;text-align:center;border:1px solid#000;color:#fff}#tools{position:absolute;left:10px;top:108px;color:#fff;z-index:100;background-color:#6b7c925e;border-color:var(--bs-dark);padding:8px;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px;font-weight:600}#tools b{color:#ccc}#radarLayer-time{top:130px!important;background-color:#6b7c925e;border-color:var(--bs-dark);padding:8px;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px}.about{float:left;margin:10px 0 0 10px;z-index:1000;position:absolute;left:0}");u.__hash="7942c2c17befb41";var c=r(41664),p=r.n(c);let b=e=>{let t=new URL(window.location.toString()),r=(parseFloat(t.searchParams.get("datascale"))||1)*e;r>0&&r<=1&&(t.searchParams.set("datascale",r.toString()),window.location.assign(t.toString()))};var x=()=>{let e=(0,i.useRef)(null),t=(0,o.useRouter)();return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("link",{href:"https://cdn.maptiler.com/mapbox-gl-js/v1.11.1/mapbox-gl.css",rel:"stylesheet"}),(0,a.jsx)(l(),{id:u.__hash,children:u}),(0,a.jsx)("div",{className:"about",children:(0,a.jsx)(p(),{href:"/weather#weatherPlus ",id:"primary-button",className:"btn btn-secondary me-1",role:"button",children:"Read about weather plus"})}),(0,a.jsx)("div",{className:"row",children:(0,a.jsxs)("div",{className:"pad-0 map-wrap fullscreen",children:[(0,a.jsx)("div",{id:"map",className:"map active"}),(0,a.jsx)("div",{id:"radarLayer-time",style:{position:"absolute",left:"10px",top:"65px",color:"#fff"}})]})}),(0,a.jsxs)("div",{id:"tools",children:[(0,a.jsx)("b",{children:"Animation"}),(0,a.jsx)("br",{}),"Opacity:"," ",(0,a.jsx)("input",{type:"range",title:"Opacity",defaultValue:"1",onChange:t=>{var r;return null===(r=e.current)||void 0===r?void 0:r.setOpacity(parseFloat(t.target.value))},min:"0",max:"1",step:"0.01"}),(0,a.jsx)("br",{}),"Speed:"," ",(0,a.jsx)("input",{type:"range",defaultValue:"0.5",onChange:t=>{var r;return null===(r=e.current)||void 0===r?void 0:r.animate(36e4*parseFloat(t.target.value))},min:"0",max:"10",step:"0.1"}),(0,a.jsx)("br",{}),(0,a.jsx)("br",{}),"Vertical exaggeration:"," ",(0,a.jsx)("input",{type:"range",defaultValue:"1",onChange:t=>{var r;return null===(r=e.current)||void 0===r?void 0:r.setVerticalExaggeration(parseFloat(t.target.value))},min:"0.1",max:"10",step:"0.05"}),(0,a.jsx)("br",{}),(0,a.jsx)("br",{}),(0,a.jsx)("b",{children:"Cut-off"}),(0,a.jsx)("br",{}),"dBZ min:"," ",(0,a.jsx)("input",{type:"range",defaultValue:"0",onChange:t=>{var r;return null===(r=e.current)||void 0===r?void 0:r.setCutoffValue(parseFloat(t.target.value),null)},min:"0",max:"64",step:"0.1"}),(0,a.jsx)("br",{}),"dBZ max:"," ",(0,a.jsx)("input",{type:"range",defaultValue:"64",onChange:t=>{var r;return null===(r=e.current)||void 0===r?void 0:r.setCutoffValue(null,parseFloat(t.target.value))},min:"0",max:"64",step:"0.1"}),(0,a.jsx)("br",{}),(0,a.jsx)("input",{type:"button",defaultValue:"auto",onClick:()=>{var t;return null===(t=e.current)||void 0===t?void 0:t.startCutoffPicking("auto")}}),(0,a.jsx)("input",{type:"button",defaultValue:"XY",onClick:()=>{var t;return null===(t=e.current)||void 0===t?void 0:t.startCutoffPicking("xy")}}),(0,a.jsx)("input",{type:"button",defaultValue:"X",onClick:()=>{var t;return null===(t=e.current)||void 0===t?void 0:t.startCutoffPicking("x")}}),(0,a.jsx)("input",{type:"button",defaultValue:"Y",onClick:()=>{var t;return null===(t=e.current)||void 0===t?void 0:t.startCutoffPicking("y")}}),(0,a.jsx)("input",{type:"button",defaultValue:"Z",onClick:()=>{var t;return null===(t=e.current)||void 0===t?void 0:t.startCutoffPicking("z")}}),(0,a.jsx)("input",{type:"button",defaultValue:"Reset",onClick:()=>{var t,r,a;null===(t=e.current)||void 0===t||t.setCutoffX(-180,180),null===(r=e.current)||void 0===r||r.setCutoffY(-90,90),null===(a=e.current)||void 0===a||a.setCutoffZ(0,2e4)}}),(0,a.jsx)("br",{}),(0,a.jsx)("br",{}),(0,a.jsx)("b",{children:"Performance"}),(0,a.jsx)("br",{}),"Data scale:",(0,a.jsx)("input",{type:"button",defaultValue:"-",onClick:()=>b(.5)}),(0,a.jsx)("input",{type:"button",defaultValue:"+",disabled:!t.query.datascale||Array.isArray(t.query.datascale)||parseFloat(t.query.datascale)>=1,onClick:()=>b(2)}),"(reloads page)",(0,a.jsx)("br",{}),"Render scale:",(0,a.jsx)("input",{type:"range",defaultValue:"1",onChange:t=>{var r;return null===(r=e.current)||void 0===r?void 0:r.setRenderScale(parseFloat(t.target.value))},min:"0.05",max:"1",step:"0.005"}),(0,a.jsx)("br",{}),"Render steps:",(0,a.jsx)("input",{type:"range",defaultValue:"32",onChange:t=>{var r;return null===(r=e.current)||void 0===r?void 0:r.setRenderSteps(parseFloat(t.target.value))},min:"8",max:"64",step:"1"}),(0,a.jsx)("br",{})]}),(0,a.jsx)("table",{id:"vals",style:{zIndex:100},className:"overflow-hidden rounded-pill shadow",children:(0,a.jsx)("tbody",{children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{style:{backgroundColor:"rgb(56, 0, 112)"},children:"5 dBZ"}),(0,a.jsx)("td",{style:{backgroundColor:"rgb(6, 0, 240)"},children:"10 dBZ"}),(0,a.jsx)("td",{style:{backgroundColor:"rgb(0, 108, 192)"},children:"15 dBZ"}),(0,a.jsx)("td",{style:{backgroundColor:"rgb(0, 160, 0)"},children:"20 dBZ"}),(0,a.jsx)("td",{style:{backgroundColor:"rgb(0, 190, 0)"},children:"25 dBZ"}),(0,a.jsx)("td",{style:{backgroundColor:"rgb(50, 216, 0)"},children:"30 dBZ"}),(0,a.jsx)("td",{style:{backgroundColor:"rgb(220, 220, 0)"},children:"35 dBZ"}),(0,a.jsx)("td",{style:{backgroundColor:"rgb(255, 176, 0)"},children:"40 dBZ"}),(0,a.jsx)("td",{style:{backgroundColor:"rgb(255, 132, 0)"},children:"45 dBZ"}),(0,a.jsx)("td",{style:{backgroundColor:"rgb(255, 50, 0)"},children:"50 dBZ"}),(0,a.jsx)("td",{style:{backgroundColor:"rgb(170, 0, 0)"},children:"55 dBZ"}),(0,a.jsx)("td",{style:{backgroundColor:"rgb(255, 255, 255)",color:"#000"},children:"60 dBZ"})]})})}),(0,a.jsx)(d(),{src:"./weather-demo-3d.js",onReady:()=>{e.current=window.weather3d()}})]})}},4298:function(e,t,r){e.exports=r(23381)}}]);