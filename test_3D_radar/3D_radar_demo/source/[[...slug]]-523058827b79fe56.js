try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="7d87e5ea-df53-409b-a86d-a540f5526b88",e._sentryDebugIdIdentifier="sentry-dbid-7d87e5ea-df53-409b-a86d-a540f5526b88")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5080],{70861:function(e,t,n){"use strict";n.d(t,{FT:function(){return s}});var r=n(67294),a=n(85893);let i=["as","disabled"];function s({tagName:e,disabled:t,href:n,target:r,rel:a,role:i,onClick:s,tabIndex:l=0,type:o}){e||(e=null!=n||null!=r||null!=a?"a":"button");let d={tagName:e};if("button"===e)return[{type:o||"button",disabled:t},d];let u=r=>{var a;if(!t&&("a"!==e||(a=n)&&"#"!==a.trim())||r.preventDefault(),t){r.stopPropagation();return}null==s||s(r)};return"a"===e&&(n||(n="#"),t&&(n=void 0)),[{role:null!=i?i:"button",disabled:void 0,tabIndex:t?void 0:l,href:n,target:"a"===e?r:void 0,"aria-disabled":t||void 0,rel:"a"===e?a:void 0,onClick:u,onKeyDown:e=>{" "===e.key&&(e.preventDefault(),u(e))}},d]}let l=r.forwardRef((e,t)=>{let{as:n,disabled:r}=e,l=function(e,t){if(null==e)return{};var n={};for(var r in e)if(({}).hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,i),[o,{tagName:d}]=s(Object.assign({tagName:n,disabled:r},l));return(0,a.jsx)(d,Object.assign({},l,o,{ref:t}))});l.displayName="Button",t.ZP=l},94167:function(e,t,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/tools/[[...slug]]",function(){return n(58520)}])},76720:function(e,t,n){"use strict";var r=n(93967),a=n.n(r),i=n(67294),s=n(70861),l=n(97400),o=n(85893);let d=i.forwardRef((e,t)=>{let{as:n,bsPrefix:r,variant:i="primary",size:d,active:u=!1,disabled:c=!1,className:h,...f}=e,p=(0,l.vE)(r,"btn"),[b,{tagName:m}]=(0,s.FT)({tagName:n,disabled:c,...f});return(0,o.jsx)(m,{...b,...f,ref:t,disabled:c,className:a()(h,p,u&&"active",i&&"".concat(p,"-").concat(i),d&&"".concat(p,"-").concat(d),f.href&&c&&"disabled")})});d.displayName="Button",t.Z=d},20554:function(e,t,n){"use strict";n.d(t,{H:function(){return i}});var r=n(85893),a=n(76720);n(67294);let i=e=>{let{resetErrorBoundary:t}=e;return(0,r.jsx)("div",{className:"error",children:(0,r.jsxs)("div",{className:"container shadow border pb-2",children:[(0,r.jsx)("h3",{children:"Something went wrong while loading this section"}),t&&(0,r.jsx)(a.Z,{onClick:t,children:"Try to reload"})]})})}},13342:function(e,t,n){"use strict";var r=n(85893);n(67294);var a=n(2962);t.Z=e=>{let{metadata:t}=e;if(!t||!t.metaImage.data)return null;let{attributes:n}=t.metaImage.data;return(0,r.jsx)(a.PB,{title:t.metaTitle,description:t.metaDescription,noindex:!!(t.metaRobots&&t.metaRobots.includes("noindex")),nofollow:!!(t.metaRobots&&t.metaRobots.includes("nofollow")),canonical:t.canonicalURL?t.canonicalURL:null,openGraph:{title:t.metaTitle,description:t.metaDescription,...t.metaImage.data&&{images:[{url:n.url,width:n.width,height:n.height,alt:n.alternativeText}]}}})}},85829:function(e,t,n){"use strict";var r=n(85893),a=n(67294);let i=(0,a.lazy)(()=>Promise.all([n.e(2887),n.e(2644),n.e(3751),n.e(1876),n.e(1426),n.e(3680)]).then(n.bind(n,17837))),s=(0,a.lazy)(()=>Promise.all([n.e(2887),n.e(4228),n.e(2644),n.e(3751),n.e(3644),n.e(1876),n.e(1426),n.e(8999),n.e(4924),n.e(6773),n.e(6760)]).then(n.bind(n,3714))),l=(0,a.lazy)(()=>n.e(1920).then(n.bind(n,41920))),o=(0,a.lazy)(()=>Promise.all([n.e(2887),n.e(2644),n.e(3751),n.e(1876),n.e(1426),n.e(3732)]).then(n.bind(n,83732))),d=(0,a.lazy)(()=>Promise.all([n.e(2887),n.e(4228),n.e(2644),n.e(3751),n.e(3644),n.e(1876),n.e(1426),n.e(3381)]).then(n.bind(n,13381))),u=(0,a.lazy)(()=>Promise.all([n.e(7679),n.e(1876),n.e(8423),n.e(2854),n.e(5492)]).then(n.bind(n,65492))),c=(0,a.lazy)(()=>Promise.all([n.e(4228),n.e(2887),n.e(2644),n.e(3751),n.e(3644),n.e(8532),n.e(3646),n.e(1821),n.e(1876),n.e(1426),n.e(1408),n.e(2811)]).then(n.bind(n,62811))),h=(0,a.lazy)(()=>Promise.all([n.e(7679),n.e(8423),n.e(4574)]).then(n.bind(n,24574))),f=(0,a.lazy)(()=>n.e(9511).then(n.bind(n,9511))),p=(0,a.lazy)(()=>n.e(4744).then(n.bind(n,14744))),b=(0,a.lazy)(()=>Promise.all([n.e(4228),n.e(2887),n.e(2644),n.e(3751),n.e(3644),n.e(1876),n.e(1426),n.e(8999),n.e(4924),n.e(5728),n.e(4995),n.e(9703)]).then(n.bind(n,29703))),m=(0,a.lazy)(()=>Promise.all([n.e(2644),n.e(3751),n.e(1876),n.e(1426),n.e(4287)]).then(n.bind(n,4287))),g=(0,a.lazy)(()=>Promise.all([n.e(2644),n.e(3751),n.e(1876),n.e(1426),n.e(6685)]).then(n.bind(n,36685))),y=(0,a.lazy)(()=>Promise.all([n.e(4228),n.e(2887),n.e(2644),n.e(3751),n.e(3644),n.e(1876),n.e(1426),n.e(6820)]).then(n.bind(n,56820))),v=["MapViewer","EngineDrone","EngineOutput","EnginePrinted","EngineVector"];t.Z=e=>{let{data:t,navigationSettings:n}=e,{tools:x,mapViewerSettings:j}=t,w={mapID:"streets-v2",latitude:0,longitude:0,zoom:0,fullscreen:!1};return(0,r.jsxs)(a.Suspense,{fallback:(0,r.jsx)("div",{children:(0,r.jsx)("p",{className:"blink position-absolute top-50 start-50 text-center p-0 text-nowrap",children:"Loading ..."})}),children:[v.includes(x)&&(0,r.jsx)(i,{data:j||w,navbar:null==n?void 0:n.NavbarVisible}),"CloudStaticMapsGenerator"===x&&(0,r.jsx)(s,{}),"Upload"===x&&(0,r.jsx)(l,{}),"Coordinates"===x&&(0,r.jsx)(d,{data:j||w}),"Hillshading"===x&&(0,r.jsx)(o,{data:j||w,navbar:null==n?void 0:n.NavbarVisible}),"Hypsometry"===x&&(0,r.jsx)(u,{data:j||w,navbar:null==n?void 0:n.NavbarVisible}),"Weather3D"===x&&(0,r.jsx)(f,{}),"Orthophoto"===x&&(0,r.jsx)(p,{}),"Weather"===x&&(0,r.jsx)(c,{data:j||w}),"NlsPlacenames"===x&&(0,r.jsx)(h,{}),"MapsReact"===x&&(0,r.jsx)(b,{}),"HeatmapFlyTo"===x&&(0,r.jsx)(m,{}),"PointFilter"===x&&(0,r.jsx)(g,{}),"GeocodingDemo"===x&&(0,r.jsx)(y,{})]})}},74781:function(e,t,n){"use strict";n.r(t);var r=n(85893),a=n(2962),i=n(31118),s=n(97375),l=n(19101),o=n(68070),d=n(41664),u=n.n(d);function c(e){let{statusCode:t,message:n="The page was not found on this server"}=e;return(0,r.jsxs)("main",{children:[(0,r.jsx)(a.PB,{title:t.toString()}),(0,r.jsx)(s.Z,{fluid:"lg",className:"mt-10 mb-20",children:(0,r.jsx)(l.Z,{children:(0,r.jsxs)(o.Z,{className:"text-center",children:[(0,r.jsx)("h2",{style:{fontSize:"120px"},children:t}),(0,r.jsx)("p",{children:n}),(0,r.jsx)("p",{className:"padt-4",children:(0,r.jsx)(u(),{className:"btn btn-lg btn-primary",href:"/",children:"Go to maptiler.com"})})]})})})]})}c.getInitialProps=async e=>{await i.u(e);let{res:t,err:n}=e;return{statusCode:t?t.statusCode:n?n.statusCode:404,message:t.statusMessage}},t.default=c},58520:function(e,t,n){"use strict";n.r(t),n.d(t,{__N_SSG:function(){return u}});var r=n(85893),a=n(20554),i=n(13484),s=n(13342),l=n(4511),o=n(85829),d=n(74781),u=!0;t.default=e=>{let{metadata:t,layoutSettings:n,toolsSelector:u,layoutData:c}=e;return u?(0,r.jsxs)(i.Z,{layoutData:c,settings:n,children:[(0,r.jsx)(s.Z,{metadata:t}),(0,r.jsx)(l.SV,{fallbackRender:a.H,children:(0,r.jsx)(o.Z,{data:u,navigationSettings:n})})]}):(0,r.jsx)(d.default,{statusCode:404,layoutData:c})}},4511:function(e,t,n){"use strict";n.d(t,{SV:function(){return s}});var r=n(67294);let a=(0,r.createContext)(null),i={didCatch:!1,error:null};class s extends r.Component{constructor(e){super(e),this.resetErrorBoundary=this.resetErrorBoundary.bind(this),this.state=i}static getDerivedStateFromError(e){return{didCatch:!0,error:e}}resetErrorBoundary(){let{error:e}=this.state;if(null!==e){for(var t,n,r=arguments.length,a=Array(r),s=0;s<r;s++)a[s]=arguments[s];null===(t=(n=this.props).onReset)||void 0===t||t.call(n,{args:a,reason:"imperative-api"}),this.setState(i)}}componentDidCatch(e,t){var n,r;null===(n=(r=this.props).onError)||void 0===n||n.call(r,e,t)}componentDidUpdate(e,t){let{didCatch:n}=this.state,{resetKeys:r}=this.props;if(n&&null!==t.error&&function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return e.length!==t.length||e.some((e,n)=>!Object.is(e,t[n]))}(e.resetKeys,r)){var a,s;null===(a=(s=this.props).onReset)||void 0===a||a.call(s,{next:r,prev:e.resetKeys,reason:"keys"}),this.setState(i)}}render(){let{children:e,fallbackRender:t,FallbackComponent:n,fallback:i}=this.props,{didCatch:s,error:l}=this.state,o=e;if(s){let e={error:l,resetErrorBoundary:this.resetErrorBoundary};if("function"==typeof t)o=t(e);else if(n)o=(0,r.createElement)(n,e);else if(void 0!==i)o=i;else throw l}return(0,r.createElement)(a.Provider,{value:{didCatch:s,error:l,resetErrorBoundary:this.resetErrorBoundary}},o)}}},31118:function(e,t,n){"use strict";n.d(t,{u:function(){return d}});var r=n(73076),a=n(39424),i=n(33280),s=n(17986),l=n(36096);async function o(){try{l.X&&s.kg.log("Flushing events..."),await (0,a.yl)(2e3),l.X&&s.kg.log("Done flushing events")}catch(e){l.X&&s.kg.log("Error while flushing events:\n",e)}}async function d(e){let{req:t,res:n,err:s}=e,l=n?.statusCode||e.statusCode;if(l&&l<500||!e.pathname)return Promise.resolve();(0,r.$e)(e=>{if(t){let n=function(e){let t=e.headers||{},n="string"==typeof t.host?t.host:void 0,r=e.protocol||(e.socket?.encrypted?"https":"http"),a=e.url||"",i=function({url:e,protocol:t,host:n}){return e?.startsWith("http")?e:e&&n?`${t}://${n}${e}`:void 0}({url:a,host:n,protocol:r}),s=e.body||void 0,l=e.cookies;return{url:i,method:e.method,query_string:function(e){if(e)try{let t=new URL(e,"http://s.io").search.slice(1);return t.length?t:void 0}catch{return}}(a),headers:function(e){let t=Object.create(null);try{Object.entries(e).forEach(([e,n])=>{"string"==typeof n&&(t[e]=n)})}catch{}return t}(t),cookies:l,data:s}}(t);e.setSDKProcessingMetadata({normalizedRequest:n})}(0,a.Tb)(s||`_error.js called with falsy error (${s})`,{mechanism:{type:"instrument",handled:!1,data:{function:"_error.getInitialProps"}}})}),function(e){let t=i.GLOBAL_OBJ[Symbol.for("@vercel/request-context")],n=t?.get&&t.get()?t.get():{};n?.waitUntil&&n.waitUntil(e)}(o())}}},function(e){e.O(0,[3128,4648,364,2888,9774,179],function(){return e(e.s=94167)}),_N_E=e.O()}]);