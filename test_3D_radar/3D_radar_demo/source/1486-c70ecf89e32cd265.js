try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="01f5201c-4232-4c8a-afaa-d72de2b27a52",e._sentryDebugIdIdentifier="sentry-dbid-01f5201c-4232-4c8a-afaa-d72de2b27a52")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1486],{1135:function(e,t,s){"use strict";var n=s(85893),l=s(80643),a=s(50702);s(67294),t.Z=e=>{let{data:t,className:s="",linkID:i=null,target:r=null}=e;return(0,n.jsxs)(l.Z,{id:i,className:"text-nowrap link-arrow ".concat(s),data:t,target:r,children:[(0,n.jsx)("span",{className:"text-wrap",children:t.text.trimEnd()}),(0,n.jsx)(a.H,{url:t.url}),"\xa0",(0,n.jsx)("svg",{className:"link-arrow-svg ms-no5",version:"1.1",xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",children:(0,n.jsx)("path",{fill:"currentColor",d:"M8.578 16.594l4.594-4.594-4.594-4.594 1.406-1.406 6 6-6 6z"})})]})}},99808:function(e,t,s){"use strict";var n=s(85893),l=s(1135),a=s(80643),i=s(50702);t.Z=e=>{let{data:t,className:s=null,bigLink:r=null}=e,c=t.newTab?"_blank":"_self",d=t.id+t.text.toLowerCase().replace(/\s+(.)/g,(e,t)=>t.toUpperCase());if("link"===t.type){let e=(0,n.jsx)(l.Z,{data:t,className:"".concat(r?"ms-0":"ms-0 my-1 me-1"," ").concat(s),target:c,linkID:d});return r?(0,n.jsx)("h4",{children:e},t.id):e}{let e="server"==t.type||"desktop"==t.type?"primary":t.type;return(0,n.jsxs)(a.Z,{className:"btn btn-".concat(e," ").concat(r?"mb-2 mt-1":""," ").concat(s," "),id:d,data:t,target:c,children:[t.text,(0,n.jsx)(i.H,{url:t.url})]})}}},24920:function(e,t,s){"use strict";var n=s(85893),l=s(29803),a=s(84526);let i={a:e=>{var t,s;let{node:a,...i}=e,r=null===(t=a.children[0])||void 0===t?void 0:t.value,c=null===(s=decodeURIComponent(i.href))||void 0===s?void 0:s.match(/^\/\^tooltip(?:::(.+))?$/);if(c){let e=c[1]?"text-".concat(c[1]):"";return(0,n.jsx)(l.Z,{text:r,className:e})}return(0,n.jsx)("a",{...i,children:r})}};t.Z=e=>{let{components:t={},...s}=e;return(0,n.jsx)(a.UG,{components:{...t,...i},...s})}},20554:function(e,t,s){"use strict";s.d(t,{H:function(){return a}});var n=s(85893),l=s(76720);s(67294);let a=e=>{let{resetErrorBoundary:t}=e;return(0,n.jsx)("div",{className:"error",children:(0,n.jsxs)("div",{className:"container shadow border pb-2",children:[(0,n.jsx)("h3",{children:"Something went wrong while loading this section"}),t&&(0,n.jsx)(l.Z,{onClick:t,children:"Try to reload"})]})})}},4378:function(e,t,s){"use strict";s.d(t,{Z:function(){return c}});var n=s(85893),l=s(99808),a=s(80278),i=s.n(a),r=e=>{let{children:t}=e;return(0,n.jsx)("div",{className:i().buttonContainer,children:t})},c=e=>{let{data:t,alignment:s="",className:a="",bigLink:i=!1}=e;return Array.isArray(t)?(0,n.jsx)("div",{className:"d-flex ".concat(s),children:(0,n.jsx)(r,{children:null==t?void 0:t.map(e=>(0,n.jsx)(l.Z,{data:e,bigLink:i,className:a},e.id+e.type))})}):(0,n.jsx)(l.Z,{data:t,bigLink:i,className:a},t.id+t.type)}},29803:function(e,t,s){"use strict";var n=s(85893),l=s(67294),a=s(34924),i=s(46350),r=s(67221);t.Z=e=>{let{text:t,className:s="",placement:c="top",big:d,children:o,map:m}=e,[x,h]=(0,l.useState)(!1),p=(0,l.useRef)(null);return(0,l.useEffect)(()=>{let e=()=>h(!1);if(x){var t;let s=null===(t=p.current)||void 0===t?void 0:t.parentElement;window.addEventListener("scroll",e,{passive:!0}),null==s||s.addEventListener("scroll",e,{passive:!0}),null==m||m.on("movestart",e)}return()=>{var t;window.removeEventListener("scroll",e),null===(t=parent)||void 0===t||t.addEventListener("scroll",e),null==m||m.off("movestart",e)}},[m,x]),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("a",{type:"button",ref:p,onTouchStart:()=>h(!0),onMouseEnter:()=>h(!0),onMouseLeave:()=>h(!1),children:null!=o?o:(0,n.jsx)(r.Z,{name:"info",width:20,height:20,className:"svg-text ".concat(d?"align-text-bottom ms-o5":"align-text-top ps-o5"," ").concat(s)})}),(0,n.jsx)(a.Z,{target:p.current,show:x,placement:c,children:(0,n.jsx)(i.Z,{children:t})})]})}},21486:function(e,t,s){"use strict";s.d(t,{Q:function(){return eN}});var n=s(85893),l=s(20554),a=s(67294),i=s(18357);let r=e=>{let{children:t,animate:s=!0,delayMs:l}=e,{ref:r,inView:c}=(0,i.YD)({threshold:.04,triggerOnce:!0}),[d,o]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{o(!0)},[]),s?(0,n.jsx)("div",{className:"fade-in",ref:r,style:{opacity:!d||c?1:0,transform:"translateY(".concat(!d||c?0:"4px",")"),transitionDelay:l?"".concat(l,"ms"):void 0},children:t}):t};var c=s(41686);let d=c.ZP.div.withConfig({componentId:"sc-8e6800d4-0"})(["z-index:-10;position:absolute;pointer-events:none;left:50%;transform:scale(",") translateX(-50%);transform-origin:0;"],e=>e.scale),o=c.ZP.svg.withConfig({componentId:"sc-8e6800d4-1"})(["transform:translateX(",") translateY(",") rotate(",");"],e=>e.$x,e=>e.$y,e=>e.$angle),m=e=>(0,n.jsx)(d,{scale:e.scale,children:(0,n.jsx)(o,{$x:e.x,$y:e.y,$angle:e.angle,width:"922",height:"922",viewBox:"0 0 922 922",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{opacity:"0.05",d:"M75.7356 325.213C37.1365 286.614 17.837 267.315 8.91882 245.785C-2.97294 217.077 -2.97294 184.821 8.91882 156.113C17.837 134.583 37.1365 115.284 75.7356 76.6845C114.335 38.0855 133.634 18.786 155.164 9.8678C183.872 -2.02397 216.128 -2.02397 244.836 9.8678C266.366 18.786 285.665 38.0855 324.264 76.6845C362.863 115.284 382.163 134.583 391.081 156.113C402.973 184.821 402.973 217.077 391.081 245.785C382.163 267.315 362.863 286.614 324.264 325.213C285.665 363.812 266.366 383.112 244.836 392.03C216.128 403.922 183.872 403.922 155.164 392.03C133.634 383.112 114.335 363.812 75.7356 325.213Z",fill:e.color||"#3170FE"})})});var x=e=>{let{data:t}=e;return(0,n.jsx)(d,{scale:{big:"2.56",medium:"1.75",small:"0.90"}[t.size],children:(0,n.jsx)(o,{$x:{left:"-300px",right:"400px",center:"0",offsetLeft:"-75px",offsetRight:"-75px"}[t.horizontalPosition],$y:{betweenBlocks:"0",blockBefore:"-125px",blockAfter:"100px"}[t.verticalPosition],$angle:{tiltedLeft:"-15deg",tiltedRight:"15deg",default:"0"}[t.angle],width:"400",height:"400",viewBox:"0 0 400 400",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{d:"M75.7356 325.213C37.1365 286.614 17.837 267.315 8.91882 245.785C-2.97294 217.077 -2.97294 184.821 8.91882 156.113C17.837 134.583 37.1365 115.284 75.7356 76.6845C114.335 38.0855 133.634 18.786 155.164 9.8678C183.872 -2.02397 216.128 -2.02397 244.836 9.8678C266.366 18.786 285.665 38.0855 324.264 76.6845C362.863 115.284 382.163 134.583 391.081 156.113C402.973 184.821 402.973 217.077 391.081 245.785C382.163 267.315 362.863 286.614 324.264 325.213C285.665 363.812 266.366 383.112 244.836 392.03C216.128 403.922 183.872 403.922 155.164 392.03C133.634 383.112 114.335 363.812 75.7356 325.213Z",fill:({gray:"#3170FE",data:"#64A856",engine:"#724FA7",server:"#E6353D"})[t.color].toString(),fillOpacity:"0.05"})})})},h=s(4378),p=s(47311),u=s(58749),j=s(19101),g=s(68070),b=s(34155),y=s(24920),f=s(41664),w=s.n(f),N=s(4511),v=s(1135);let Z={smallWidth:6,smallerMediumWidth:8,mediumWidth:9,fullWidth:12};var C=s(98827);let k=c.ZP.div.withConfig({componentId:"sc-6ed83950-0"})(["z-index:-100;position:absolute;pointer-events:none;left:0;right:0;transform:translateY(-450px);"]);var L=s(5152);let S=s.n(L)()(()=>s.e(7837).then(s.bind(s,17837)),{loadableGenerated:{webpack:()=>[17837]},ssr:!1});var I=s(29803),B=s(4298),E=s.n(B),_=s(67221),U=s(14760),D=s(11163),P=s(97375),M=s(85518),T=s(90273),F=s(2626),q=s(45087);let R=e=>{let{show:t,setShow:s,product:l,productVersion:i,detectedOS:r}=e,[c,d]=(0,a.useState)(""),[o,m]=(0,a.useState)(!1),x=(0,a.useRef)(null),h=(0,a.useRef)(null),p=(0,a.useRef)(null),[u,j]=(0,a.useState)(!1),[g,b]=(0,a.useState)(!1),y="engine"===l?{Windows:"https://maptiler.download/engine/maptiler-engine-".concat(i,"-app-win-x64-setup.exe"),MacOS:"https://maptiler.download/engine/maptiler-engine-".concat(i,"-app-mac.dmg"),Linux:{deb:"https://maptiler.download/engine/maptiler-engine-".concat(i,"-app-linux.deb"),rpm:"https://maptiler.download/engine/maptiler-engine-".concat(i,"-app-linux.rpm"),AppImage:"https://maptiler.download/engine/maptiler-engine-".concat(i,"-app-linux.AppImage")}}:{Windows:"https://maptiler.download/server/maptiler-server-".concat(i,"-win-x64-setup.exe"),MacOS:"https://maptiler.download/server/maptiler-server-".concat(i,"-mac.zip"),Linux:{deb:"https://maptiler.download/server/maptiler-server-".concat(i,"-linux.deb"),rpm:"https://maptiler.download/server/maptiler-server-".concat(i,"-linux.rpm"),AppImage:"https://maptiler.download/server/maptiler-server-".concat(i,"-linux.AppImage")}},[f,w]=(0,a.useState)(""),N=f||r,v=e=>()=>{w(e),s(!0)},Z=()=>{s(!1),w(r)},C=e=>()=>{var t,s;"engine"===l?null===(t=window.dataLayer)||void 0===t||t.push({event:"download_engine",engine_os_platform:"".concat("Mac OS"===N?"mac":N.toLocaleLowerCase()),engine_version:i,engine_installer:e}):null===(s=window.dataLayer)||void 0===s||s.push({event:"download_server",server_os_platform:"".concat("Mac OS"===N?"mac":N.toLocaleLowerCase()),server_version:i,server_installer:e}),Z()};return(0,n.jsxs)(n.Fragment,{children:[" ",(0,n.jsx)("p",{className:"text-darker mt-3",children:"All operating systems:"}),(0,n.jsx)("a",{className:"d-table big",role:"button",onClick:v("Windows"),children:"Windows"}),(0,n.jsx)("a",{className:"d-table big",role:"button",onClick:v("Mac OS"),children:"macOS"}),(0,n.jsx)("a",{className:"d-table big",role:"button",onClick:v("Linux"),children:"Linux"}),(0,n.jsx)("a",{className:"d-table big",role:"button",onClick:v("Docker"),children:"Docker"}),t&&(0,n.jsxs)(F.Z,{contentClassName:"p-2 border-0 rounded",centered:!0,show:!0,onHide:Z,children:[(0,n.jsx)("h3",{className:"m-0",children:"Download"}),u?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("p",{children:["Download the latest MapTiler"," ".concat("engine"===l?"Engine":"Server"," ").concat(i)]}),"Linux"===N?(0,n.jsxs)("div",{children:[(0,n.jsx)("a",{href:y.Linux.deb,onClick:C("Linux-DEB"),className:"btn my-o5 ".concat("engine"===l?"btn-desktop":"btn-server"),children:"Download .deb"}),(0,n.jsx)("br",{}),(0,n.jsx)("a",{href:y.Linux.rpm,onClick:C("Linux-RPM"),className:"btn my-o5 ".concat("engine"===l?"btn-desktop":"btn-server"),children:"Download .rpm"}),(0,n.jsx)("br",{}),(0,n.jsx)("a",{href:y.Linux.AppImage,onClick:C("Linux-AppImage"),className:"btn my-o5 ".concat("engine"===l?"btn-desktop":"btn-server"),children:"Download .AppImage"})]}):"Mac OS"===N?(0,n.jsx)("div",{children:(0,n.jsxs)("a",{href:y.MacOS,onClick:C("MacOS-DMG"),className:"btn ".concat("engine"===l?"btn-desktop":"btn-server"),children:["Download ","engine"===l?"DMG":"Installer"]})}):"Docker"===N?(0,n.jsx)("div",{children:(0,n.jsx)("a",{href:"https://hub.docker.com/r/maptiler/".concat(l),onClick:C("Docker"),target:"_blank",rel:"noopener",className:"btn ".concat("engine"===l?"btn-desktop":"btn-server"),children:"Get on Docker Hub"})}):(0,n.jsx)("div",{children:(0,n.jsx)("a",{href:y.Windows,onClick:C("Windows-EXE"),className:"btn ".concat("engine"===l?"btn-desktop":"btn-server"),children:"Download exe"})})]}):(0,n.jsxs)("div",{id:"downloadForm",children:[(0,n.jsx)("p",{children:"Before download, please provide your email."}),(0,n.jsx)("label",{htmlFor:"email",children:"First name"}),(0,n.jsx)("input",{id:"inputFirst",ref:x,className:"form-control w-100",type:"email",name:"firstname"}),(0,n.jsx)("label",{htmlFor:"email",className:"mt-1",children:"Last name"}),(0,n.jsx)("input",{id:"inputLast",ref:h,className:"form-control w-100",type:"email",name:"lastname"}),(0,n.jsx)("label",{htmlFor:"email",className:"mt-1",children:"Email"}),(0,n.jsx)("input",{id:"inputEmail",value:c,ref:p,onChange:e=>{let t=e.target.value;d(t),m((0,q.zL)(t))},className:"form-control w-100",type:"email",name:"email"}),(0,n.jsxs)("p",{className:"mb-3",children:[(0,n.jsx)("input",{className:"form-check-input me-1",type:"checkbox",checked:g,onChange:e=>b(e.target.checked)}),"I agree to the"," ",(0,n.jsx)("a",{href:"/privacy-policy/",target:"_blank",rel:"noopener",children:"Privacy policy"})," ","and"," ",(0,n.jsxs)("a",{href:"/terms/".concat("server"===l?"server-data":l,"/"),target:"_blank",rel:"noopener",children:[" ","Terms and Conditions"]}),"."]}),(0,n.jsx)("p",{className:"text-center",children:(0,n.jsx)("button",{id:"emailSubmit",className:"btn btn-primary",onClick:()=>{if(u||!g)return;let e={"mauticform[formId]":"engine"===l?10:1,"mauticform[formName]":"engine"===l?"desktopdownload":"serverdownload","mauticform[first_name]":x.current.value,"mauticform[last_name]":h.current.value,"mauticform[email]":p.current.value,"mauticform[website]":window.location.toString(),"mauticform[pagename]":N+"_".concat(i),"mauticform[messenger]":"engine"===l?10:1};j(!0),(0,q.iv)(e)},disabled:!o||u||!g,children:u?"Form Submitted":"Continue to download"})})]})]})]})},z={replace:e=>{if(e instanceof p.W_&&"h2"===e.name){var t;let s=null===(t=e.childNodes[0])||void 0===t?void 0:t.data,l=null==s?void 0:s.match(/Version\s(\d+(\.\d+)*(\.\d+)*)/i);if(l){let t="v".concat(l[1]),a=(0,p.e_)(e.attribs);return(0,n.jsx)("h2",{...a,id:t,children:s})}}return e}};var $=s(76773);s(21006);var V=s(37669),H=s(80643);let A=e=>{let{className:t,name:s}=e;return(0,n.jsx)(_.Z,{className:"".concat(t," me-o5"),name:s,width:18,height:18})};var O=e=>{let{card:t,i:s,engineData:l,cardHighlight:a,engine:i,geolayers:r,selectedOption:c,year:d,month:o,engineQueryParam:m=""}=e,{planName:x,usage:h,price:p,annual:u,priceDescription:b,priceDescriptionTooltip:f,buttonText:w,featureDescription:N,feature:v,footnote:Z}=t;return(0,n.jsxs)("div",{className:"bg-white rounded shadow overflow-hidden h-100 ".concat(a?"pricing-highlight":null),children:[(0,n.jsx)("h6",{className:"m-0 py-gutter text-white text-center ".concat(a?"bg-secondary":"bg-dark"),children:(0,V.bu)(h)}),(0,n.jsxs)("div",{className:"text-center px-2",children:[(0,n.jsx)("h3",{className:"fw-bold mb-3",children:(0,V.bu)(x)}),/[0-9$]/.test(p)?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("span",{className:"text-dark h1",children:l?l[c][s].price:p}),(0,n.jsx)("span",{className:"fw-bolder",children:"$0"===p?" ":u?"/"+d+" (USD)":"/"+o+" (USD)"}),l&&(0,n.jsx)("p",{className:"small",children:(0,n.jsx)("b",{children:l[c][s].description})})]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("span",{className:"text-white h1",children:"."}),(0,n.jsx)("span",{className:"fw-bolder bigger",children:l?l[c][s].price:t.price}),l&&(0,n.jsx)("p",{className:"small",children:(0,n.jsx)("b",{children:l[c][s].description})})]}),i,(0,n.jsxs)("p",{className:"small",children:[(0,n.jsx)(y.Z,{disallowedElements:["p"],unwrapDisallowed:!0,children:(0,V.bu)(b)}),t.priceDescriptionTooltip&&(0,n.jsx)(I.Z,{text:(0,V.bu)(f)})]}),l?(0,n.jsx)("a",{className:"btn btn-lg btn-primary w-100 my-1",href:"".concat(l[c][s].link).concat(m&&l[c][s].link.includes("subscribe")?"".concat(m):""),children:w}):(0,n.jsx)(n.Fragment,{children:r?(0,n.jsx)(j.Z,{className:"justify-content-around text-center",children:(0,n.jsx)(g.Z,{xs:6,className:"justify-content-around w-100 text-center mb-2",children:(0,n.jsx)(H.Z,{className:"btn btn-lg btn-primary  my-1",data:{url:t.buttonLink},children:w})})}):(0,n.jsx)(H.Z,{className:"btn btn-lg btn-primary w-100 my-1",data:{url:t.buttonLink},children:w})})]}),(0,n.jsxs)("div",{className:"ps-3 pe-1 small pb-3",children:[N&&(0,n.jsx)("p",{children:(0,V.bu)(N)}),v.map((e,t)=>{let{featureHeading:s,text:l,icon:a,tooltip:i}=e;return s?(0,n.jsx)("p",{className:"bg-lighter rounded-1 body-size p-o5 my-1 ms-n2 ps-2",children:(0,n.jsx)(y.Z,{disallowedElements:["p"],unwrapDisallowed:!0,children:(0,V.bu)(l)})},t):(0,n.jsxs)("p",{children:["check"===a||"add"==a?(0,n.jsx)(A,{className:"text-success",name:a}):"cross"===a?(0,n.jsx)(A,{className:"text-danger",name:"clear"}):(0,n.jsx)("span",{className:"me-2",children:"\xa0"}),(0,n.jsx)(y.Z,{disallowedElements:["p"],unwrapDisallowed:!0,children:(0,V.bu)(l)}),i&&(0,n.jsx)(I.Z,{text:(0,V.bu)(i)})]},t)}),(0,n.jsx)("div",{className:"pricing-card-markdown",children:(0,n.jsx)(y.Z,{children:(0,V.bu)(Z)})})]})]})},G=s(58917),W=s(78864),K=s(2066),X=s(17137),Y=s(99808);let Q=()=>{var e;let t=null===(e=Object.fromEntries(new URLSearchParams(location.hash.substring(1)).entries()).bounds)||void 0===e?void 0:e.split(",").map(Number);if(!t)return;let s=document.getElementById("region-active");if(null==s||s.scrollIntoView({behavior:"smooth"}),!t.every(e=>!isNaN(e)))return;let[n,l,a,i]=t;return{bounds:new U.LngLatBounds([n,Math.max(l,-90)],[a,Math.min(i,90)])}};var J=s(43147),ee=s(85829),et=s(48333),es=e=>{let{video:t,vimeoThumbnailUrl:s,className:l,background:i=!0}=e,r=(0,a.useRef)(null),c=(0,a.useRef)(null);return(0,a.useEffect)(()=>{if(!r.current)return r.current=new et.Z(c.current,{url:"https://vimeo.com/".concat(t),autopause:!1,background:i,byline:!1,title:!1,vimeo_logo:!1,portrait:!1}),()=>{r.current&&r.current.destroy()}},[i,t]),(0,n.jsx)("div",{className:"bg-white ".concat(l),ref:c,children:s&&(0,n.jsx)("img",{className:"vimeo-thumbnail",src:s,alt:"Vimeo Thumbnail"})})},en=e=>{let{text:t,icons:s}=e;return(0,n.jsxs)("p",{className:"mt-3 d-flex align-items-center gap-gutter",children:[(0,n.jsx)("span",{children:t}),s.data?(0,n.jsx)("span",{children:s.data.map((e,t)=>(0,n.jsx)("img",{src:e.attributes.url,alt:e.attributes.alternativeText,title:e.attributes.alternativeText,width:"32"},t))}):(0,n.jsxs)("span",{children:[(0,n.jsxs)("svg",{width:"32",height:"32",viewBox:"3 3 26 26",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,n.jsx)("g",{clipPath:"url(#clip0_2279_1254)",children:(0,n.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M7.00531 15.9335L7 10.407L13.8002 9.48372V15.9335H7.00534H7.00531ZM14.9331 9.31901L23.9975 7.99976V15.9335H14.9331L14.9331 9.31901ZM24 17.0667L23.9981 25.0004L14.9334 23.7251V17.0667H24L24 17.0667ZM13.7995 23.5801L7.00502 22.6483V17.0654H13.7995V23.5801Z",fill:"#6B7C92"})}),(0,n.jsx)("defs",{children:(0,n.jsx)("clipPath",{id:"clip0_2279_1254",children:(0,n.jsx)("rect",{width:"17",height:"17",fill:"white",transform:"translate(7 8)"})})})]}),(0,n.jsx)("svg",{width:"32",height:"32",viewBox:"3 3 26 26",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M21.3667 14.959C21.3421 12.4271 23.3958 11.2121 23.4876 11.1519C22.3332 9.43352 20.5348 9.19732 19.894 9.17076C18.3635 9.01352 16.9081 10.0883 16.1306 10.0883C15.3552 10.0883 14.1569 9.19435 12.888 9.218C11.2201 9.2431 9.68238 10.2053 8.82254 11.7255C7.0897 14.7851 8.37931 19.3199 10.0683 21.8009C10.8933 23.0159 11.8772 24.3807 13.1694 24.332C14.4133 24.2822 14.8841 23.5119 16.3888 23.5119C17.8931 23.5119 18.3156 24.332 19.6325 24.3058C20.9714 24.2807 21.8197 23.0672 22.6393 21.8481C23.5877 20.4382 23.9764 19.0749 24 19.0048C23.9702 18.9911 21.3892 17.985 21.3631 14.96L21.3667 14.959H21.3667ZM18.8934 7.52686C19.5788 6.68057 20.0419 5.5058 19.9154 4.33325C18.9278 4.37422 17.7303 5.00312 17.0213 5.84831C16.3848 6.59864 15.8288 7.79444 15.9775 8.94299C17.0804 9.03048 18.2065 8.37278 18.8923 7.52833L18.8934 7.52686Z",fill:"#6B7C92"})}),(0,n.jsx)("svg",{width:"32",height:"32",viewBox:"3 3 26 26",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{d:"M23.5035 21.4525C22.9542 21.2363 22.7192 20.9494 22.7419 20.5214C22.7653 20.0218 22.4703 19.6558 22.3302 19.5112C22.4148 19.2005 22.6622 18.1258 22.3304 17.1924C21.9739 16.1932 20.8855 14.6672 19.7624 13.1672C19.3026 12.5512 19.2809 11.8816 19.2558 11.1062C19.2317 10.3667 19.2045 9.5284 18.7753 8.59652C18.3086 7.58185 17.3721 7 16.2059 7C15.5121 7 14.8 7.20833 14.2519 7.57152C13.1297 8.31566 13.278 9.93809 13.3762 11.0117C13.3897 11.1587 13.4023 11.2975 13.4097 11.4161C13.475 12.4676 13.4156 13.0217 13.3378 13.1902C13.2875 13.3002 13.0398 13.6135 12.7778 13.9452C12.5067 14.2882 12.1994 14.677 11.9475 15.0393C11.6469 15.4752 11.4043 16.1416 11.1697 16.786C10.998 17.2575 10.8358 17.7029 10.678 17.9691C10.3788 18.4811 10.4532 18.9586 10.5154 19.1792C10.4021 19.2548 10.2385 19.4037 10.1003 19.6842C9.93339 20.0265 9.59464 20.2105 8.89023 20.3406C8.56652 20.4042 8.34331 20.535 8.2266 20.7293C8.05676 21.0121 8.14927 21.3673 8.23367 21.6101C8.35836 21.9671 8.28066 22.193 8.13938 22.6032C8.10683 22.6978 8.06991 22.805 8.0325 22.9232C7.9736 23.1098 7.99485 23.2794 8.09552 23.4273C8.36155 23.8181 9.13783 23.9559 9.93695 24.0465C10.4141 24.1009 10.9363 24.2843 11.4414 24.4617C11.9363 24.6355 12.448 24.8152 12.9132 24.8697C12.9839 24.8782 13.0539 24.8826 13.1214 24.8826C13.8237 24.8826 14.1411 24.4348 14.2417 24.2508C14.4939 24.2014 15.364 24.043 16.2607 24.0217C17.1561 23.9972 18.0223 24.167 18.2677 24.2199C18.3449 24.3617 18.5482 24.6858 18.8724 24.8528C19.0506 24.9464 19.2986 25 19.5526 25H19.5526C19.8239 25 20.3399 24.9384 20.7483 24.5256C21.1557 24.1108 22.1735 23.5813 22.9167 23.1947C23.0825 23.1084 23.2377 23.0277 23.3738 22.9548C23.7913 22.7324 24.0191 22.4147 23.9987 22.083C23.9818 21.8076 23.7921 21.566 23.5035 21.4525ZM14.2611 21.3727C14.2091 21.0209 13.7382 20.6721 13.1928 20.2682C12.7469 19.9378 12.2414 19.5635 12.1021 19.2465C11.8144 18.5924 12.0412 17.4424 12.4367 16.8503C12.6322 16.5538 12.7918 16.1041 12.9462 15.6693C13.1129 15.1997 13.2854 14.7143 13.4782 14.5017C13.7835 14.1699 14.0657 13.5244 14.1157 13.0156C14.4016 13.2779 14.8452 13.6106 15.2549 13.6106C15.318 13.6106 15.3792 13.6027 15.4378 13.5869C15.7181 13.5091 16.1305 13.2802 16.5292 13.0588C16.873 12.868 17.297 12.6326 17.4565 12.6112C17.73 12.9886 19.3193 16.3679 19.4817 17.4532C19.6102 18.3119 19.4745 19.0217 19.4063 19.2997C19.3513 19.2925 19.2859 19.2867 19.2171 19.2867C18.7744 19.2867 18.6572 19.5189 18.6267 19.6575C18.5482 20.0177 18.5399 21.1694 18.539 21.4282C18.3789 21.6236 17.5694 22.5438 16.4072 22.7092C15.9338 22.7753 15.4918 22.8088 15.0934 22.8088C14.7528 22.8088 14.5355 22.7836 14.4453 22.7704L13.8613 22.1284C14.0915 22.0191 14.3218 21.7886 14.2611 21.3727ZM15.0022 10.7859C14.9839 10.7934 14.966 10.8015 14.9484 10.8101C14.9466 10.7721 14.9425 10.7336 14.9361 10.6949C14.8724 10.3423 14.629 10.0863 14.3576 10.0863C14.3375 10.0863 14.3173 10.0878 14.2951 10.0911C14.1336 10.1169 14.007 10.2335 13.9376 10.3987C13.9984 10.0361 14.2122 9.76765 14.4659 9.76765C14.7638 9.76765 15.0155 10.1534 15.0155 10.6099C15.0155 10.6675 15.0111 10.7247 15.0022 10.7859ZM17.317 11.0578C17.3443 10.9743 17.359 10.884 17.359 10.7903C17.359 10.3764 17.0857 10.0521 16.7367 10.0521C16.3957 10.0521 16.1182 10.3832 16.1182 10.7903C16.1182 10.818 16.1196 10.8458 16.1223 10.8736C16.1043 10.8669 16.0867 10.8604 16.0695 10.8542C16.0302 10.74 16.0104 10.6208 16.0104 10.4992C16.0104 10.0042 16.3396 9.60146 16.7444 9.60146C17.1491 9.60146 17.4784 10.0042 17.4784 10.4992C17.4784 10.7051 17.4193 10.9016 17.317 11.0578ZM17.0185 12.0211C17.0127 12.0462 17.0003 12.0573 16.8631 12.1258C16.7938 12.1605 16.7075 12.2036 16.5996 12.2668L16.5275 12.3087C16.2377 12.4775 15.5592 12.8729 15.3749 12.8961C15.2498 12.9122 15.1724 12.8656 14.9984 12.752C14.9592 12.7263 14.9174 12.6991 14.8729 12.6716C14.5592 12.4739 14.3574 12.256 14.3346 12.1709C14.4369 12.0949 14.6904 11.9048 14.8202 11.7923C15.0836 11.5569 15.3487 11.3988 15.4799 11.3988C15.4869 11.3988 15.4931 11.3992 15.4996 11.4004C15.6538 11.4266 16.0341 11.5724 16.3118 11.6789C16.4402 11.7281 16.5511 11.7706 16.6291 11.7975C16.8749 11.8786 17.003 11.9823 17.0185 12.0211ZM19.2265 23.0201C19.3652 22.4193 19.5249 21.6019 19.499 21.12C19.4931 21.0105 19.483 20.8914 19.4732 20.7762C19.455 20.5608 19.4279 20.2406 19.4558 20.1456C19.4614 20.1432 19.4675 20.141 19.4743 20.1392C19.4755 20.4147 19.5378 20.9642 19.9951 21.1558C20.1314 21.213 20.2872 21.2419 20.4581 21.2419C20.9162 21.2419 21.4246 21.0259 21.6328 20.8259C21.7554 20.7081 21.8586 20.5639 21.9308 20.4498C21.9466 20.4942 21.9563 20.5523 21.9512 20.6272C21.924 21.0335 22.1295 21.5725 22.5206 21.7712L22.5776 21.7999C22.7169 21.8702 23.0869 22.0568 23.0929 22.1454C23.0929 22.1454 23.0897 22.1558 23.0691 22.1742C22.9764 22.2556 22.6502 22.4157 22.3347 22.5705C21.7751 22.845 21.1407 23.1562 20.8559 23.4441C20.4548 23.8496 20.0011 24.1221 19.7271 24.1221C19.6942 24.1221 19.6641 24.1181 19.6374 24.1099C19.3399 24.0208 19.095 23.6083 19.2265 23.0201ZM9.08476 21.4873C9.05441 21.3508 9.03046 21.2431 9.05619 21.1388C9.07487 21.0615 9.4721 20.9786 9.64175 20.9432C9.88026 20.8935 10.1269 20.8421 10.2882 20.748C10.5064 20.6211 10.6245 20.387 10.7286 20.1804C10.8041 20.031 10.882 19.8766 10.9746 19.8259C10.9799 19.8229 10.9879 19.8195 11.0031 19.8195C11.1767 19.8195 11.5411 20.1702 11.751 20.4842C11.8043 20.5633 11.903 20.7219 12.0171 20.9055C12.3584 21.4542 12.8258 22.2058 13.0699 22.4577C13.2898 22.6841 13.6459 23.1195 13.5583 23.4929C13.4942 23.7826 13.1528 24.0182 13.0723 24.0703C13.043 24.0767 13.0069 24.0799 12.9643 24.0799C12.4971 24.0799 11.5723 23.7065 11.0754 23.5058L11.0019 23.4761C10.7245 23.3644 10.2715 23.2939 9.83352 23.2258C9.485 23.1716 9.00773 23.0974 8.92862 23.0304C8.86449 22.9613 8.93887 22.7366 9.00448 22.5384C9.05171 22.3959 9.10048 22.2486 9.1272 22.0944C9.1651 21.8484 9.12051 21.6481 9.08476 21.4873Z",fill:"#6B7C92"})}),(0,n.jsxs)("svg",{width:"32",height:"32",viewBox:"3 3 26 26",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,n.jsx)("circle",{cx:"16",cy:"16",r:"9",fill:"#6B7C92"}),(0,n.jsxs)("g",{clipPath:"url(#clip0_2279_1257)",children:[(0,n.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M17.556 12.2128H16.3127V11H17.556V12.2128ZM14.6254 13.6809H13.4117V12.4681H14.6254V13.6809ZM16.1055 13.6809H14.8622V12.4681H16.1055V13.6809ZM17.556 13.6809H16.3127V12.4681H17.556V13.6809ZM13.1749 15.1489H11.9316V13.9362H13.1749V15.1489ZM14.6254 15.1489H13.4117V13.9362H14.6254V15.1489ZM16.1055 15.1489H14.8622V13.9362H16.1055V15.1489ZM17.556 15.1489H16.3127V13.9362H17.556V15.1489ZM19.0362 15.1489H17.7929V13.9362H19.0362V15.1489Z",fill:"white"}),(0,n.jsx)("path",{d:"M23.0028 14.6064C23.0028 14.6064 22.47 14.0638 21.3747 14.2553C21.2563 13.3298 20.3386 12.7872 20.3386 12.7872C20.3386 12.7872 19.4802 13.9043 20.1018 15.1489C19.9242 15.2447 19.6282 15.3723 19.1842 15.3723H11.0436C10.8955 15.9787 10.8955 20 14.9806 20C17.9113 20 20.1018 18.5319 21.1379 15.8511C22.6772 15.9787 23.0028 14.6064 23.0028 14.6064Z",fill:"white"})]}),(0,n.jsx)("defs",{children:(0,n.jsx)("clipPath",{id:"clip0_2279_1257",children:(0,n.jsx)("rect",{width:"12",height:"9",fill:"white",transform:"translate(11 11)"})})})]})]})]})},el=e=>{let{data:t}=e,s=t.shadow?"shadow":"",l="rounded "+s,a="iframed rounded "+s;return(0,n.jsxs)(j.Z,{className:"align-items-center justify-content-center justify-content-md-between text-center text-md-start my-md-3 mt-12 pt-12 pt-md-0",children:[(0,n.jsxs)(g.Z,{md:5,className:"order-md-2 order-sx-1 ",children:[(0,n.jsx)("h1",{children:t.title}),(0,n.jsx)(y.Z,{className:"my-3 bigger",children:t.description}),t.button&&(0,n.jsx)(h.Z,{data:t.button,alignment:"justify-content-center justify-content-md-start",className:"btn-lg"}),t.icons&&t.availableOn&&(0,n.jsx)(en,{text:t.availableOn,icons:t.icons})]}),(t.iframeURL||t.img.data)&&(0,n.jsx)(g.Z,{sm:8,md:6,className:"order-md-2 order-sx-0 my-4 ",children:t.iframeURL?(0,n.jsx)("div",{children:t.iframeURL.includes("http")?(0,n.jsx)("div",{className:a,children:(0,n.jsx)("iframe",{src:t.iframeURL,title:t.title,allowFullScreen:!0})}):(0,n.jsx)(es,{video:t.iframeURL,className:a,vimeoThumbnailUrl:t.vimeoThumbnailUrl})}):(0,n.jsx)(n.Fragment,{children:t.imgLink?(0,n.jsx)(H.Z,{data:{url:t.imgLink},children:t.img.data&&(0,n.jsx)(b.Z,{media:t.img,className:l})}):(0,n.jsx)(n.Fragment,{children:t.img.data&&(0,n.jsx)(b.Z,{media:t.img,className:l})})})})]})},ea=e=>{let{side:t}=e;return(0,n.jsx)("a",{id:"slide".concat(t),className:"mt-slide-arrow align-items-center justify-content-center ".concat("left"===t?"start-0":"end-0"),children:(0,n.jsx)(_.Z,{name:"keyboard_arrow_".concat(t),width:28,height:28})})},ei=s(72074),er=s(2261);s(64172),s(57638);var ec=s(25675),ed=s.n(ec),eo=s(54208);let em=e=>{let{card:t,visible:s}=e;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(b.Z,{media:t.img,loading:"lazy",className:"rounded shadow-sm my-1 img-hover ".concat(s?"":"invisible")}),t.title&&(0,n.jsx)("h3",{className:"mt-3",children:t.title})]})};s(63699),s(16303),s(12479);let ex=(0,a.lazy)(()=>s.e(5054).then(s.bind(s,25054)));var eh=s(50702);let ep=c.ZP.div.withConfig({componentId:"sc-231015fa-0"})(["box-shadow:0px 10px 35px 0px rgba(51,51,89,0.15) !important;border-radius:12px;background:white;table{position:relative;width:100%;td{text-align:center;line-height:1.2;font-size:14px;height:64px;width:20%;padding-left:10px;white-space:pre-wrap;padding:10px;@media (max-width:576px){padding:7px;}p{display:inline-block;margin:0;}&:first-child{text-align:left;@media (min-width:576px){min-width:220px;}}}}"]),eu={cloud:["Free","Flex","Unlimited","Custom"],engine:["Free","Plus","Pro","Enterprise"],onPrem:["On-prem Free","On-prem Standard","On-prem Custom"],weather:["Weather","Weather Plus"]},ej={"sections.hero-banner-image":el,"components.big-link":e=>{let{data:t}=e;return(0,n.jsx)(j.Z,{className:"justify-content-center my-4",children:(0,n.jsx)(g.Z,{className:"text-center",children:(0,n.jsx)("h4",{children:(0,n.jsx)(v.Z,{data:t})})})})},"components.map-button":h.Z,"components.restricted-button":h.Z,"components.button":h.Z,"sections.carousel":e=>{let{data:t}=e,[s,l]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{l(!0)},[]),(0,n.jsxs)(j.Z,{className:"justify-content-center text-center ".concat((0,C.m)(t.margines)),children:[t.title||t.description?(0,n.jsxs)(g.Z,{sm:12,className:"text-center",children:[t.title&&(0,n.jsx)("h2",{children:t.title}),t.description&&(0,n.jsx)(y.Z,{children:t.description})]}):null,(0,n.jsxs)(g.Z,{sm:12,className:"position-relative",children:[(0,n.jsx)(ea,{side:"left"}),(0,n.jsx)(ea,{side:"right"}),(0,n.jsx)(er.tq,{spaceBetween:t.handlers?60:45,loop:!0,navigation:{nextEl:"#slideright",prevEl:"#slideleft"},autoplay:{delay:2500,disableOnInteraction:!0},breakpoints:{0:{slidesPerView:1},576:{slidesPerView:2},768:{slidesPerView:t.handlers?3:4},992:{slidesPerView:t.handlers?3:5}},modules:[ei.W_,ei.pt],wrapperClass:"swiper-wrapper d-flex align-items-end",className:"mx-md-2 px-2 px-sm-4 rounded",children:t.carouselCard.map(e=>{var t;let l=(null===(t=e.youtubeVideoID)||void 0===t?void 0:t.includes("https://www.youtube.com/watch?v="))?e.youtubeVideoID.split("=")[1]:e.youtubeVideoID,a=(null==l?void 0:l.includes("&"))?l.split("&")[0]:l;return(0,n.jsxs)(er.o5,{className:"py-2 h-100  align-items-end",children:[e.url?(0,n.jsx)(H.Z,{data:e,children:(0,n.jsx)(em,{visible:s,card:e})}):(0,n.jsx)(em,{visible:s,card:e}),e.youtubeVideoID&&(0,n.jsxs)("a",{className:"video-player",type:"button",href:"https://www.youtube.com/watch?v="+a,"data-title":e.title,children:[(0,n.jsx)(ed(),{loading:"lazy",className:"w-100 h-auto rounded shadow",src:"https://img.youtube.com/vi/"+a+"/mqdefault.jpg",width:1600,height:900,alt:e.title}),(0,n.jsx)("div",{className:"modal fade rounded",tabIndex:-1,children:(0,n.jsx)(eo.Z,{id:a,title:e.title,iframeClass:"rounded",params:"autoplay=1&start=0&enablejsapi=1",wrapperClass:"yt-lite rounded"})})]})]},e.id)})})]})]})},"sections.carusel-content-block":e=>{let{data:t}=e,s="left"==t.imgPosition?"first":"last";return(0,n.jsxs)(j.Z,{className:"justify-content-center justify-content-md-between align-items-center my-md-12 my-4",children:[(0,n.jsxs)(g.Z,{md:5,className:"text-center text-md-start",children:[(0,n.jsx)("h2",{children:t.title}),(0,n.jsx)(y.Z,{children:t.description}),(0,n.jsx)(h.Z,{data:t.button})]}),(0,n.jsx)(g.Z,{xs:{order:"first"},md:{span:6,order:s},className:"text-center text-md-start rounded shadow p-0",children:(0,n.jsx)(er.tq,{modules:[ei.pt,ei.N1,ei.xW],slidesPerView:"auto",spaceBetween:0,loop:!0,speed:1500,autoplay:{delay:3e3,disableOnInteraction:!0},className:"rounded",effect:t.effect,children:t.img.data.map(e=>(0,n.jsx)(er.o5,{children:(0,n.jsx)(b.Z,{media:{data:e}})},e.id))})})]})},"sections.customers-logos-hover":e=>{var t;let{data:s}=e,[l,i]=(0,a.useState)({}),r=(e,t)=>{i(s=>({...s,[e]:t}))},c=(e,t)=>{i(s=>({...s,[e]:t}))};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(j.Z,{className:"justify-content-center mt-4",children:(s.title||s.description)&&(0,n.jsxs)(g.Z,{md:9,className:"text-center",children:[(0,n.jsx)("h2",{children:s.title}),(0,n.jsx)(y.Z,{children:s.description})]},s.title)},"customersLogosHover"),(0,n.jsx)(j.Z,{className:"justify-content-center",children:(0,n.jsx)(g.Z,{md:s.size?Z[s.size]:12,className:"px-0 d-flex flex-wrap align-items-center ".concat(s.spacing?"justify-content-"+s.spacing:"justify-content-center"),children:null===(t=s.logo)||void 0===t?void 0:t.map(e=>{var t,s,i,d,o,m,x,h,p,u,j;let g=l[null==e?void 0:null===(t=e.greyLogo)||void 0===t?void 0:t.id]===((null==e?void 0:null===(s=e.colorLogo)||void 0===s?void 0:s.data)?e.colorLogo.data.attributes.url:null);return(0,n.jsx)(a.Fragment,{children:(null===(i=e.colorLogo)||void 0===i?void 0:i.data)?(0,n.jsx)(H.Z,{data:{url:e.url},className:"my-gutter mx-2",onMouseEnter:()=>r(e.greyLogo.id,e.colorLogo.data.attributes.url),onMouseOut:()=>{var t;return c(e.greyLogo.id,null===(t=e.greyLogo)||void 0===t?void 0:t.data.attributes.url)},children:(0,n.jsx)(b.Z,{title:null==e?void 0:e.name,className:"m-my-gutter mx-2",height:null==e?void 0:null===(d=e.greyLogo)||void 0===d?void 0:d.data.attributes.height,width:null==e?void 0:null===(o=e.greyLogo)||void 0===o?void 0:o.data.attributes.width,media:null==e?void 0:e.greyLogo,src:g?null==e?void 0:null===(m=e.colorLogo)||void 0===m?void 0:m.data.attributes.url:null==e?void 0:null===(x=e.greyLogo)||void 0===x?void 0:x.data.attributes.url},null==e?void 0:null===(h=e.greyLogo)||void 0===h?void 0:h.id)},e.greyLogo.id):(0,n.jsx)(b.Z,{title:null==e?void 0:e.name,className:"my-gutter mx-2",height:null==e?void 0:null===(p=e.greyLogo)||void 0===p?void 0:p.data.attributes.height,width:null==e?void 0:null===(u=e.greyLogo)||void 0===u?void 0:u.data.attributes.width,media:e.greyLogo},null==e?void 0:null===(j=e.greyLogo)||void 0===j?void 0:j.id)},e.id+"logoline")})})})]})},"sections.card-gallery":e=>{let{data:t}=e,s=t.galleryCard.length<4?4:3;return(0,n.jsxs)(j.Z,{className:"justify-content-center my-md-12 my-4",children:[(0,n.jsxs)(g.Z,{md:12,className:"text-center mb-4",children:[(0,n.jsx)("h2",{children:t.title}),(0,n.jsx)(y.Z,{children:t.description})]}),t.galleryCard.map(e=>(0,n.jsx)(g.Z,{sm:12,md:s,children:(0,n.jsx)(H.Z,{data:e,children:(0,n.jsx)(b.Z,{className:"my-gutter rounded shadow",media:e.img},e.id)})},e.id))]})},"sections.card-row":e=>{var t;let s,{data:l}=e;s=l.cards.length%4==0||l.cards.length%5==0||l.cards.length%6==0?2:3;let a=l.cards.length%6==0?"px-2 ":"px-0 mx-gutter";return(0,n.jsxs)(j.Z,{className:"my-4 my-md-12 justify-content-center",children:[l.title||l.description?(0,n.jsxs)(g.Z,{sm:10,className:"text-center mb-4",children:[(0,n.jsx)("h2",{children:l.title}),(0,n.jsx)(y.Z,{children:l.description})]}):null,(0,n.jsx)(g.Z,{sm:12,children:(0,n.jsx)(j.Z,{className:"justify-content-center text-center mb-4",children:null===(t=l.cards)||void 0===t?void 0:t.map(e=>(0,n.jsxs)(g.Z,{xs:7,sm:4,lg:s,className:"py-md-1 my-2 ".concat(a," ").concat("left"==e.textAlignment?"text-start":"text-center"),children:[e.button?(0,n.jsx)(H.Z,{data:e.button,children:(0,n.jsx)(b.Z,{media:e.img,className:"rounded shadow",loading:"lazy"})}):(0,n.jsx)(b.Z,{media:e.img,className:"rounded shadow",loading:"lazy"}),(0,n.jsx)("h4",{children:e.title}),(0,n.jsx)(y.Z,{children:e.description}),e.button&&(0,n.jsx)("div",{children:(0,n.jsx)(h.Z,{className:"link"==e.button.type&&"ms-gutter",data:e.button},e.button.id)},e.button.id+e.button.text)]},e.id+"cardRow"))})})]})},"sections.ck-editor-centered":e=>{let{data:t}=e;return(0,n.jsx)(j.Z,{className:"justify-content-center my-md-12 my-4",children:(0,n.jsx)(g.Z,{md:10,className:"text-start bg-white shadow rounded px-md-6 py-4",children:(0,p.ZP)(t.CKEditor)})})},"sections.code-map":e=>{let{data:t}=e,s=(0,a.useRef)(null);return(0,a.useEffect)(()=>{s.current||(U.config.apiKey="O7VbOY3zrXxBupgrQtdE",s.current=new U.Map({container:"map",style:t.mapID,center:[t.longitude,t.latitude],zoom:t.zoom}))}),(0,n.jsxs)(j.Z,{className:"justify-content-center my-4 my-md-12",children:[(0,n.jsx)(g.Z,{md:12,className:"text-center",children:(0,n.jsx)("h2",{className:"mb-5",children:t.title})}),(0,n.jsxs)(g.Z,{className:"position-relative",children:[(0,n.jsx)("div",{id:"map",className:"map position-relative w-100 rounded shadow"}),(0,n.jsxs)("div",{className:"position-absolute bottom-0 bg-lighter rounded shadow m-2 p-gutter",children:[(0,n.jsx)("div",{className:"code-toolbar",children:(0,n.jsx)($.Z,{language:t.codeLanguage,useInlineStyles:!1,children:t.code})}),t.codeButton&&(0,n.jsx)(h.Z,{className:"my-1 me-gutter",data:t.codeButton},t.codeButton.id)]})]}),(0,n.jsx)(g.Z,{md:12,className:"text-center my-6",children:t.button&&(0,n.jsx)(h.Z,{data:t.button},t.button.id)})]})},"sections.contact":e=>{let{data:t}=e;return(0,n.jsxs)(j.Z,{className:"justify-content-around align-items-top my-md-6 my-4",children:[(0,n.jsx)(g.Z,{md:6,className:"text-center text-md-start mt-1",children:(0,n.jsx)("div",{className:"iframed rounded shadow  mt-2 h-75",children:(0,n.jsx)("iframe",{src:t.iframe,title:t.iframeTitle,allow:"clipboard-write; fullscreen; geolocation *"})})}),(0,n.jsxs)(g.Z,{md:6,className:"text-center text-md-start pe-0",children:[(0,n.jsx)("h1",{children:t.title}),(0,n.jsx)(y.Z,{children:t.description}),t.button&&(0,n.jsx)(h.Z,{data:t.button}),(0,n.jsx)(j.Z,{children:t.ContactColumn.map(e=>(0,n.jsxs)(g.Z,{sx:6,md:6,className:"my-1",children:[(0,n.jsx)("h4",{className:"mb-o5",children:e.title}),(0,n.jsx)(y.Z,{className:"mt-0 mb-2",children:e.text}),e.button&&(0,n.jsx)(h.Z,{data:e.button})]},e.id))})]})]})},"sections.content-block":e=>{let{data:t}=e,{title:s,componentId:l,description:a,imgLink:i,img:r,iframeURL:c,buttons:d,vimeoThumbnailUrl:o}=t,m=t.id+"content_Block",x="left"==t.imgPosition?"first":"last",p=t.shadow?"shadow":"";return(0,n.jsxs)(j.Z,{id:l||m,className:"justify-content-center justify-content-md-between align-items-center my-md-12 my-4",children:[(0,n.jsxs)(g.Z,{md:5,className:"text-start",children:[(0,n.jsx)("h2",{children:s}),(0,n.jsx)(y.Z,{children:a}),d&&(0,n.jsx)(h.Z,{data:d},d.id)]}),(0,n.jsx)(g.Z,{xs:{order:"first"},md:{span:6,order:x},className:"text-center text-md-start",children:c?(0,n.jsx)("div",{children:c.includes("http")?(0,n.jsx)("div",{className:p+" iframed rounded",children:(0,n.jsx)("iframe",{src:c,title:s,allowFullScreen:!0})}):(0,n.jsx)(es,{video:c,vimeoThumbnailUrl:o,className:p+" iframed rounded"})}):r.data&&(i?(0,n.jsx)(H.Z,{data:{url:i},children:(0,n.jsx)(b.Z,{media:r,className:"".concat(p," rounded")})}):(0,n.jsx)(b.Z,{media:r,className:"".concat(p," rounded")}))})]})},"sections.cta":e=>{let{data:t}=e;return(0,n.jsxs)(j.Z,{className:"justify-content-center text-center my-8 my-md-20 ",children:[(0,n.jsxs)(g.Z,{children:[t.title&&(0,n.jsx)("h2",{className:"mt-0",children:t.title}),t.description&&(0,n.jsx)(y.Z,{className:"pb-2 big",children:t.description}),(0,n.jsx)(h.Z,{data:t.button,alignment:"justify-content-center"},t.id)]}),t.shape&&(0,n.jsx)(d,{scale:"0.90",children:(0,n.jsx)(o,{$x:"-50%",$y:"-35%",$angle:"15deg",width:"400",height:"400",viewBox:"0 0 400 400",fill:"#3170FE",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{d:"M75.7356 325.213C37.1365 286.614 17.837 267.315 8.91882 245.785C-2.97294 217.077 -2.97294 184.821 8.91882 156.113C17.837 134.583 37.1365 115.284 75.7356 76.6845C114.335 38.0855 133.634 18.786 155.164 9.8678C183.872 -2.02397 216.128 -2.02397 244.836 9.8678C266.366 18.786 285.665 38.0855 324.264 76.6845C362.863 115.284 382.163 134.583 391.081 156.113C402.973 184.821 402.973 217.077 391.081 245.785C382.163 267.315 362.863 286.614 324.264 325.213C285.665 363.812 266.366 383.112 244.836 392.03C216.128 403.922 183.872 403.922 155.164 392.03C133.634 383.112 114.335 363.812 75.7356 325.213Z",fillOpacity:"0.05"})})})]})},"sections.iframe":e=>{let{data:t}=e;return(0,n.jsxs)(j.Z,{className:"justify-content-center my-4 my-md-12",children:[t.title&&t.description&&(0,n.jsxs)(g.Z,{md:Z[t.size],className:"text-center",children:[(0,n.jsx)("h2",{children:t.title}),(0,n.jsx)(y.Z,{children:t.description})]}),(0,n.jsx)(g.Z,{md:Z[t.size],className:"text-center",children:t.img.data?(0,n.jsx)(b.Z,{media:t.img,className:t.shadow?"rounded shadow":"rounded"}):(0,n.jsx)("div",{className:"rounded iframed ".concat(t.shadow?"shadow":""," ").concat(t.responsiveIframe?"iframed-responsive":""),children:t.iframeUrl&&t.iframeUrl.includes("http")?(0,n.jsx)("iframe",{src:t.iframeUrl,title:t.title,allow:"clipboard-write; fullscreen"}):t.iframeUrl&&(0,n.jsx)(es,{video:t.iframeUrl,vimeoThumbnailUrl:t.vimeoThumbnailUrl,background:!1})})}),(0,n.jsxs)(g.Z,{md:Z[t.size],className:"text-center",children:[(0,n.jsx)(y.Z,{children:t.text}),t.button&&(0,n.jsx)("div",{className:"mt-3",children:(0,n.jsx)(h.Z,{data:t.button,bigLink:!0,alignment:"justify-content-center"})})]})]})},"sections.centered-hero-banner":e=>{let{data:t}=e,{model:s,margins:l,title:i,description:r,button:c,img:d}=t;return(0,n.jsxs)(j.Z,{className:"justify-content-center text-center ".concat(s?"mt-8 mb-20":(0,C.m)(l)," "),children:[(0,n.jsxs)(g.Z,{md:8,className:"mb-6",children:[i&&(0,n.jsx)("h1",{children:i}),r&&(0,n.jsx)(y.Z,{className:"bigger",children:r}),c&&(0,n.jsx)(h.Z,{data:c,alignment:"justify-content-center mt-6"},c.id)]}),(0,n.jsxs)(g.Z,{md:9,className:"text-center position-relative ".concat(s&&"mb-12"),children:[s&&(0,n.jsx)("div",{className:"position-absolute top-100 start-50 translate-middle",children:(0,n.jsx)(a.Suspense,{children:(0,n.jsx)(ex,{src:"/tools/maptiler_scene2048compressed.glb",position:"14.22/43.72473/-110.76764"})})}),d.data&&d.data.length>1?(0,n.jsx)(er.tq,{modules:[ei.pt,ei.N1,ei.xW],slidesPerView:"auto",spaceBetween:0,loop:!0,speed:t.carouselSpeed||1500,autoplay:{delay:t.carouselDelay||3e3,disableOnInteraction:!0},className:"rounded-3",effect:"fade",fadeEffect:{crossFade:!0},children:d.data.map(e=>(0,n.jsx)(er.o5,{children:(0,n.jsx)(b.Z,{media:{data:e}})},e.id))}):d.data.map(e=>(0,n.jsx)(b.Z,{media:{data:e},className:"rounded"},e.id))]})]})},"sections.example-card-row":e=>{let{data:t}=e;return(0,n.jsxs)(j.Z,{className:"my-4 my-md-12 justify-content-center",children:[(0,n.jsx)(g.Z,{sm:10,className:"text-center mb-4",children:(0,n.jsx)("h2",{children:t.title})}),(0,n.jsx)(g.Z,{sm:12,children:(0,n.jsx)(j.Z,{className:"justify-content-center text-center mb-4",children:t.exampleCard.map(e=>(0,n.jsx)(g.Z,{sm:12,md:5,className:"py-md-1 text-start mb-sm-2",children:(0,n.jsxs)(H.Z,{data:e,children:[(0,n.jsx)(b.Z,{media:e.img,className:"rounded shadow"}),(0,n.jsx)("h4",{children:e.title})]})},e.id))})})]})},"sections.example-galery":e=>{let{data:t}=e,s=e=>{var t;let s,l;return(null===(t=e.button)||void 0===t?void 0:t.type)=="link"?(s="bg-lighter ",l="my-2"):(s="my-sm-1",l=""),(0,n.jsxs)(g.Z,{sx:6,className:"position-relative",children:[e.video?(0,n.jsx)("div",{children:e.video.includes("http")?(0,n.jsx)("div",{className:"rounded iframed shadow",children:(0,n.jsx)("iframe",{src:e.video,title:e.title,allowFullScreen:!0})}):(0,n.jsx)(es,{video:e.video,vimeoThumbnailUrl:e.vimeoThumbnailUrl,className:"rounded iframed shadow "})}):e.img.data&&(0,n.jsx)(b.Z,{media:e.img,className:"rounded shadow my-0"}),e.button&&(0,n.jsx)("div",{className:"position-absolute bottom-0 left-0 ".concat(l," "),children:(0,n.jsx)(h.Z,{data:e.button,className:"m-2 ms-sm-2 p-1 rounded shadow ".concat(s)},e.button.id)})]},e.id)};return(0,n.jsxs)(j.Z,{className:"my-md-12 my-4 text-center",children:[(0,n.jsxs)(j.Z,{className:"text-center",children:[(0,n.jsx)("h2",{children:t.title}),(0,n.jsx)(y.Z,{children:t.description})]}),(0,n.jsxs)(j.Z,{children:[(0,n.jsx)(g.Z,{md:6,className:"position-relative my-2",children:s(t.exampleGalleryCard[0])}),(0,n.jsx)(g.Z,{className:"position-relative my-2",children:s(t.exampleGalleryCard[1])})]}),(0,n.jsxs)(j.Z,{children:[(0,n.jsx)(g.Z,{md:7,className:"position-relative my-2",children:s(t.exampleGalleryCard[2])}),(0,n.jsx)(g.Z,{md:5,className:"position-relative my-2",children:s(t.exampleGalleryCard[3])})]}),(0,n.jsx)(j.Z,{children:(0,n.jsx)(g.Z,{className:"justify-content-center my-4",children:t.button&&(0,n.jsx)(h.Z,{data:t.button},t.button.id)})})]})},"sections.faq":e=>{let{data:t}=e;return(0,n.jsx)(j.Z,{id:"faq",className:"justify-content-center my-12",children:(0,n.jsxs)(g.Z,{id:"accordion",children:[(0,n.jsx)(j.Z,{md:12,className:"text-center",children:(0,n.jsx)("h2",{children:(0,n.jsx)("a",{className:"text-secondary",href:"#faq",children:t.title})})}),(0,n.jsx)(j.Z,{className:"mt-4",children:(0,n.jsx)(g.Z,{id:"accordion",children:t.question&&t.question.map(e=>{let{title:t,description:s,id:l}=e;return(0,n.jsxs)("div",{className:"bg-lighter rounded-1 my-1",children:[(0,n.jsx)("a",{role:"button","data-bs-toggle":"collapse","data-bs-target":"#collapseAnswar".concat(l),"aria-expanded":"false","aria-controls":"collapseAnswar".concat(l),children:(0,n.jsxs)("span",{id:"answar",className:"p-2 h5 text-secondary d-block m-0",children:[t,(0,n.jsx)("span",{className:"float-end collapse-arrow",children:(0,n.jsx)(_.Z,{name:"keyboard_arrow_down",width:24,height:24})})]})}),(0,n.jsx)("div",{className:"collapse",id:"collapseAnswar".concat(l),"aria-labelledby":"answar","data-parent":"#accordion",children:(0,n.jsx)("div",{className:"card-body px-2 py-1 faq-markdown",children:(0,n.jsx)(y.Z,{children:s})})})]},l)})})}),(0,n.jsx)(j.Z,{children:(0,n.jsx)(y.Z,{className:"text-center my-1",children:t.text})})]})})},"elements.geoip-app":e=>{let{data:t}=e,s=(0,a.useRef)(null),[l,i]=(0,a.useState)("");return(0,a.useEffect)(()=>{s.current||(U.config.apiKey="O7VbOY3zrXxBupgrQtdE",s.current=new U.Map({container:"map",style:U.MapStyle.BASIC,zoom:14,navigationControl:!1,geolocateControl:!1,geolocate:U.GeolocationType.POINT}),U.geolocation.info().then(e=>{i(JSON.stringify(e,null,2)),new U.Marker().setLngLat([e.longitude,e.latitude]).addTo(s.current)}))},[]),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(j.Z,{className:"justify-content-center mt-4 mt-md-12",children:(0,n.jsxs)(g.Z,{md:9,className:"text-center my-2",children:[(0,n.jsx)("h2",{children:t.title}),(0,n.jsx)(y.Z,{children:t.description})]})}),(0,n.jsx)(j.Z,{className:"justify-content-center",children:(0,n.jsx)(g.Z,{md:10,children:(0,n.jsxs)(j.Z,{className:"justify-content-center justify-content-md-center align-items-center my-2 shadow rounded",children:[(0,n.jsx)(g.Z,{md:6,className:"p-0 px-2 h-md-500px h-300px rounded-start bg-lighter",children:(0,n.jsx)($.Z,{language:"javascript",useInlineStyles:!1,children:l})}),(0,n.jsx)(g.Z,{md:6,className:"p-0 order-first order-md-2",children:(0,n.jsx)("div",{id:"map",className:"position-relative h-md-500px h-300px w-100 rounded-end",children:(0,n.jsx)("a",{href:"https://www.maptiler.com",style:{position:"absolute",left:10,bottom:10,zIndex:"999"},children:(0,n.jsx)("img",{src:"https://api.maptiler.com/resources/logo.svg",alt:"MapTiler logo"})})})})]})})})]})},"elements.geolayers-pricing":e=>{let{data:t}=e;return(0,n.jsxs)(j.Z,{className:"justify-content-center justify-content-md-between align-items-center my-md-12 my-4",children:[(0,n.jsx)(g.Z,{md:4,className:"text-center text-md-start",children:(0,n.jsx)(O,{card:t.card,i:1,engineData:!1,cardHighlight:!0,engine:!1,geolayers:!0,selectedOption:null,year:"year",month:"month"})}),(0,n.jsx)(g.Z,{md:7,className:"text-start",children:(0,n.jsx)(y.Z,{children:t.description})})]})},"sections.heading":e=>{let{data:t}=e;return(0,n.jsx)(j.Z,{className:"justify-content-center ".concat((0,C.m)(t.margines,"none")),children:(0,n.jsxs)(g.Z,{md:Z[t.size],className:"text-".concat(t.textAlighment),children:[(0,n.jsx)("h2",{children:t.title}),(0,n.jsx)(y.Z,{className:"bigger",children:t.description})]})})},"sections.highlighted-content-block":e=>{let{data:t}=e,s="left"==t.imgPosition?"first":"last";return(0,n.jsxs)(j.Z,{className:"justify-content-center align-items-center my-md-6 my-2 rounded shadow p-3",children:[(0,n.jsxs)(g.Z,{md:5,className:"text-start",children:[null!=t.logo.data?(0,n.jsx)(g.Z,{xs:4,className:"my-2 ",children:(0,n.jsx)(b.Z,{media:t.logo})}):(0,n.jsx)("h2",{children:t.title}),(0,n.jsx)(y.Z,{children:t.description}),t.button&&(0,n.jsx)(h.Z,{data:t.button},t.button.id)]}),(0,n.jsx)(g.Z,{xs:{order:"first"},md:{span:6,order:s},className:"text-center text-md-start",children:t.iframeURL?(0,n.jsx)("div",{children:t.iframeURL.includes("http")?(0,n.jsx)("div",{className:"iframed rounded shadow",children:(0,n.jsx)("iframe",{src:t.iframeURL,title:t.title,allowFullScreen:!0})}):(0,n.jsx)(es,{video:t.iframeURL,vimeoThumbnailUrl:t.vimeoThumbnailUrl,className:"iframed rounded shadow"})}):(0,n.jsx)(n.Fragment,{children:t.img.data&&(0,n.jsx)(b.Z,{media:t.img,className:t.shadow?"rounded  shadow":"rounded"})})})]})},"sections.highlight-cta":e=>{let{data:t}=e;return(0,n.jsx)(j.Z,{className:"justify-content-center text-center my-4 my-md-12 bg-lighter rounded",children:(0,n.jsxs)(g.Z,{xs:Z[t.size],className:"py-7",children:[(0,n.jsx)("h2",{className:"mt-0",children:t.title}),(0,n.jsx)(y.Z,{className:"big",children:t.description}),t.button&&(0,n.jsx)(h.Z,{data:t.button,alignment:"justify-content-center"},t.id)]})})},"sections.highlighted-paragraph":e=>{let{data:t}=e;return(0,n.jsxs)(j.Z,{className:"justify-content-center justify-content-md-between align-items-center my-md-12 my-4 bg-lighter p-md-7 rounded",children:[(0,n.jsxs)(g.Z,{md:7,className:"text-center text-md-start",children:[(0,n.jsx)("h2",{children:t.title}),(0,n.jsx)(y.Z,{children:t.description}),t.button&&(0,n.jsx)(h.Z,{data:t.button,alignment:"justify-content-center justify-content-md-start"},t.id)]}),(0,n.jsx)(g.Z,{md:5,className:"text-center text-md-start py-2",children:t.img.data&&(0,n.jsx)(b.Z,{media:t.img,className:"rounded"})})]})},"sections.highlight-images-block":e=>{let{data:t}=e;return(0,n.jsxs)(j.Z,{className:"py-4 justify-content-around align-items-center bg-lighter my-md-12 my-4",children:[(0,n.jsxs)(g.Z,{md:4,className:" text-start",children:[(0,n.jsx)("h2",{children:t.title}),(0,n.jsx)(y.Z,{className:"mb-3 mb-md-0",children:t.description}),t.button&&(0,n.jsx)("div",{className:"text-center text-md-start",children:(0,n.jsx)(h.Z,{data:t.button})})]}),(0,n.jsx)(g.Z,{md:7,className:"align-items-center",children:(0,n.jsxs)(j.Z,{children:[t.image.map(e=>(0,n.jsxs)(g.Z,{md:6,className:"text-center",children:[(0,n.jsx)("h5",{className:"my-4",children:e.title}),e.img&&(0,n.jsx)(b.Z,{media:e.img}),(0,n.jsx)("p",{className:"my-4",children:e.description})]},e.id)),(0,n.jsx)(g.Z,{xs:12,className:"text-center",children:t.button&&(0,n.jsx)(h.Z,{data:t.imagesButton})})]})})]})},"sections.home-page-cta":e=>{let{data:t}=e;return(0,n.jsxs)(j.Z,{className:"justify-content-center text-center bg-gradient",children:[(0,n.jsxs)(g.Z,{md:6,className:"mt-4 mt-md-10 mb-8 mb-md-16",children:[t.title&&(0,n.jsx)("h2",{className:"mt-0",children:t.title}),t.description&&(0,n.jsx)(y.Z,{className:"my-4 bigger",children:t.description}),(0,n.jsx)(h.Z,{data:t.button,alignment:"justify-content-center"},t.button.id)]}),(0,n.jsx)(d,{scale:"0.70",children:(0,n.jsx)(o,{$x:"-60%",$y:"40%",$angle:"0",width:"400",height:"400",viewBox:"0 0 400 400",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("circle",{cx:"15",cy:"15",r:"15",fill:"#05D0DF"})})}),(0,n.jsx)(d,{scale:"1",children:(0,n.jsx)(o,{$x:"-95%",$y:"-25%",$angle:"-165deg",width:"400",height:"400",viewBox:"0 0 400 400",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{d:"M13 0L25.9904 22.5L0.00961876 22.5L13 0Z",fill:"#FBC935"})})}),(0,n.jsx)(d,{scale:"0.7",children:(0,n.jsx)(o,{$x:"100%",$y:"110%",$angle:"30deg",width:"400",height:"400",viewBox:"0 0 400 400",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("rect",{width:"30",height:"30",fill:"#593895"})})}),(0,n.jsx)(d,{scale:"1",children:(0,n.jsx)(o,{$x:"10%",$y:"-30%",$angle:"160deg",width:"400",height:"400",viewBox:"0 0 400 400",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{d:"M13 0L25.9904 22.5L0.00961876 22.5L13 0Z",fill:"#FFAA01"})})})]})},"sections.home-page-banner":e=>{let{data:t}=e,{L1CardLinkText:s,L1CardURL:l,L1CardImg:a,M1CardLinkText:i,M1CardURL:r,M1CardImg:c,M2CardLinkText:m,M2CardURL:x,M2CardImg:p,R1CardLinkText:u,R1CardURL:f,R1CardImg:w,R2CardLinkText:N,R2CardURL:v,R2CardImg:Z}=t;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(j.Z,{className:"justify-content-center text-center mt-4 mt-md-10",children:[(0,n.jsxs)(g.Z,{md:7,className:"mb-1",children:[t.title&&(0,n.jsx)("h1",{className:"mt-0",children:t.title}),t.description&&(0,n.jsx)(y.Z,{className:"bigger my-4",children:t.description}),t.button&&(0,n.jsx)(h.Z,{data:t.button},t.button.id)]}),(0,n.jsx)(d,{scale:"1",children:(0,n.jsx)(o,{$x:"-500%",$y:"900%",$angle:"-30deg",width:"30",height:"30",viewBox:"0 0 30 30",fill:"#593895",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("rect",{width:"30",height:"30"})})}),(0,n.jsx)(d,{scale:"1.15",children:(0,n.jsx)(o,{$x:"-900%",$y:"500%",$angle:"0deg",width:"30",height:"30",viewBox:"0 0 30 30",fill:"#FB3A1B",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("circle",{cx:"15",cy:"15",r:"15"})})}),(0,n.jsx)(d,{scale:"1.5",children:(0,n.jsx)(o,{$x:"600%",$y:"500%",$angle:"45deg",width:"30",height:"30",viewBox:"0 0 30 30",fill:"#FFAA01",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{d:"M13 0L25.9904 22.5L0.00961876 22.5L13 0Z"})})}),(0,n.jsx)(d,{scale:"0.85",children:(0,n.jsx)(o,{$x:"1500%",$y:"1300%",$angle:"0deg",width:"30",height:"30",viewBox:"0 0 30 30",fill:"#03A1C4",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("circle",{cx:"15",cy:"15",r:"15"})})}),(0,n.jsx)(d,{scale:"1",children:(0,n.jsx)(o,{$x:"-1000%",$y:"1300%",$angle:"75deg",width:"30",height:"30",viewBox:"0 0 30 30",fill:"#FBC935",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{d:"M13 0L25.9904 22.5L0.00961876 22.5L13 0Z"})})})]}),(0,n.jsxs)(j.Z,{className:"align-items-center justify-content-center mt-0 mb-md-8",children:[(0,n.jsx)(g.Z,{sm:8,md:5,xl:4,className:"order-last order-xl-first",children:(0,n.jsx)(g.Z,{className:"position-relative hero-card rounded-3 shadow",children:(0,n.jsxs)("a",{href:l,children:[(0,n.jsx)("div",{className:"position-absolute left-0 top-0 w-80 w-sm-100 h-100 py-sm-gutter px-3 z-10",children:(0,n.jsxs)("h3",{className:"text-white",children:[s,(0,n.jsx)(eh.H,{url:l}),(0,n.jsx)(_.Z,{name:"keyboard_arrow_right",className:"link-arrow-svg"})]})}),(0,n.jsx)(b.Z,{className:"rounded-3 w-100 h-auto shadow hero-card-picture",media:a})]})})}),(0,n.jsx)(g.Z,{md:12,xl:4,children:(0,n.jsxs)(j.Z,{className:"align-items-center justify-content-around mx-0",children:[(0,n.jsx)(g.Z,{sm:8,md:6,xl:12,className:"position-relative my-gutter p-0 hero-card rounded-3 shadow",children:(0,n.jsxs)("a",{href:r,className:"text-nowrap link-arrow",children:[(0,n.jsx)(g.Z,{md:6,className:"position-absolute top-0 left-0 h-100  w-50  py-sm-gutter ps-3 z-10",children:(0,n.jsxs)("h3",{className:"text-to-split",children:[i,(0,n.jsx)(eh.H,{url:r}),(0,n.jsx)(_.Z,{name:"keyboard_arrow_right",className:"link-arrow-svg"})]})}),(0,n.jsx)(b.Z,{className:"w-100 h-auto hero-card-picture",media:c,priority:!0})]})}),(0,n.jsx)(g.Z,{sm:8,md:5,xl:12,className:"position-relative p-0 hero-card hero-height rounded-3 shadow",children:(0,n.jsx)("div",{className:"position-absolute w-100 h-100 z-10 bg-white",children:(0,n.jsx)("div",{className:"mt-2",children:(0,n.jsxs)("a",{href:x,className:"h-100",children:[(0,n.jsxs)("h3",{className:"my-0 py-gutter px-3",children:[m,(0,n.jsx)(eh.H,{url:x}),(0,n.jsx)(_.Z,{name:"keyboard_arrow_right",className:"link-arrow-svg"})]}),(0,n.jsx)(b.Z,{className:"hero-card-picture",media:p})]})})})})]})}),(0,n.jsx)(g.Z,{md:12,xl:4,children:(0,n.jsxs)(j.Z,{className:"align-items-center justify-content-around mx-0 pt-xl-15",children:[(0,n.jsx)(g.Z,{sm:8,md:5,xl:12,className:"position-relative my-gutter p-0 hero-card rounded-3 shadow",children:(0,n.jsxs)("a",{className:"link-arrow",href:f,children:[(0,n.jsx)("div",{className:"position-absolute bottom-0 z-10 ps-2 ps-md-4",children:(0,n.jsxs)("h3",{className:"text-white mb-2 py-gutter",children:[u,(0,n.jsx)(eh.H,{url:f}),(0,n.jsx)(_.Z,{name:"keyboard_arrow_right",className:"link-arrow-svg"})]})}),(0,n.jsx)(b.Z,{className:"w-100 h-auto hero-card-picture rounded-3",media:w})]})}),(0,n.jsx)(g.Z,{sm:8,md:6,xl:12,className:"position-relative my-gutter p-0 hero-card rounded-3 shadow",children:(0,n.jsxs)("a",{href:v,children:[(0,n.jsx)("div",{className:"position-absolute bottom-0 z-10 ps-2 ps-md-4",children:(0,n.jsxs)("h3",{className:"text-white mb-2 py-gutter",children:[N,(0,n.jsx)(eh.H,{url:v}),(0,n.jsx)(_.Z,{name:"keyboard_arrow_right",className:"link-arrow-svg"})]})}),(0,n.jsx)(b.Z,{className:"w-100 h-auto hero-card-picture rounded-3",media:Z})]})})]})}),(0,n.jsx)(d,{scale:"1",children:(0,n.jsx)(o,{$x:"1700%",$y:"1000%",$angle:"35deg",width:"30",height:"30",viewBox:"0 0 30 30",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("rect",{width:"30",height:"30",fill:"#05D0DF"})})})]})]})},"sections.homepage-content-block":e=>{let{data:t}=e,{title:s,description:l,button:a,img1:i,img2:r,img3:c}=t;return(0,n.jsxs)(j.Z,{className:"justify-content-center my-md-12 my-4",children:[(0,n.jsx)(g.Z,{xs:12,className:"text-center",children:(0,n.jsx)("h2",{children:s})}),(0,n.jsxs)(g.Z,{md:6,lg:3,className:"px-0",children:[(0,n.jsx)(y.Z,{children:l}),t.button&&(0,n.jsx)(h.Z,{className:"my-1",data:a},a.id)]}),(0,n.jsxs)(g.Z,{xs:6,md:8,lg:4,className:"text-center text-md-start align-self-center",children:[(0,n.jsx)(d,{scale:"0.85",children:(0,n.jsx)(o,{$x:"1450%",$y:"600%",$angle:"0deg",width:"30",height:"30",viewBox:"0 0 30 30",fill:"#03A1C4",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("circle",{cx:"15",cy:"15",r:"15"})})}),(0,n.jsx)(d,{scale:"1.5",children:(0,n.jsx)(o,{$x:"700%",$y:"100%",$angle:"150deg",width:"30",height:"30",viewBox:"0 0 30 30",fill:"#FFAA01",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{d:"M13 0L25.9904 22.5L0.00961876 22.5L13 0Z"})})}),(0,n.jsx)(d,{scale:"0.9",children:(0,n.jsx)(o,{$x:"-300%",$y:"500%",$angle:"-30deg",width:"30",height:"30",viewBox:"0 0 30 30",fill:"#593895",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("rect",{width:"30",height:"30"})})}),(0,n.jsx)(d,{scale:"1.2",children:(0,n.jsx)(o,{$x:"-600%",$y:"1200%",$angle:"120deg",width:"30",height:"30",viewBox:"0 0 30 30",fill:"#05D0DF",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("rect",{width:"30",height:"30"})})}),(0,n.jsx)(d,{scale:"0.95",children:(0,n.jsx)(o,{$x:"1000%",$y:"1600%",$angle:"120deg",width:"30",height:"30",viewBox:"0 0 30 30",fill:"#F1175D",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("circle",{cx:"15",cy:"15",r:"15"})})}),(0,n.jsx)("div",{className:"text-end pt-6 pb-3",children:i.data&&(0,n.jsx)(b.Z,{media:i,className:"w-100 w-sm-70 h-auto rounded shadow",loading:"lazy"})}),(0,n.jsx)("div",{className:"text-end",children:c.data&&(0,n.jsx)(b.Z,{media:c,className:"w-100 w-sm-90 h-auto rounded shadow",loading:"lazy"})})]}),(0,n.jsx)(g.Z,{xs:6,md:4,lg:2,className:"align-self-center",children:r.data&&(0,n.jsx)(b.Z,{media:r,className:"rounded shadow",loading:"lazy"})})]})},"sections.icon-block":e=>{let t,{data:s}=e;t=s.iconCard.length%4==0?[6,3]:[5,4];let l=!0===s.backgrounds?"  rounded bg-lighter":"";return(0,n.jsx)(j.Z,{className:"text-center justify-content-between my-12",children:(0,n.jsxs)(g.Z,{children:[(0,n.jsxs)(j.Z,{children:[(0,n.jsx)("h2",{children:s.title}),(0,n.jsx)(y.Z,{children:s.description})]}),(0,n.jsx)(j.Z,{className:"justify-content-center",children:s.iconCard.map(e=>(0,n.jsx)(g.Z,{className:"text-center my-2",lg:t[1],md:t[0],children:(0,n.jsxs)("div",{className:"p-2 h-100"+l,children:[(0,n.jsx)("div",{className:"mt-2 ",children:(0,n.jsx)(b.Z,{width:e.icon.data.attributes.width,height:e.icon.data.attributes.height,media:e.icon})}),(0,n.jsx)("h3",{className:"text-center mb-0",children:e.title}),(0,n.jsx)(y.Z,{className:"text-center mt-0",children:e.description})]})},e.id))})]})})},"sections.image-row":e=>{let{data:t}=e;return(0,n.jsxs)(j.Z,{className:"justify-content-around my-4 my-md-12",children:[(t.title||t.description)&&(0,n.jsxs)(g.Z,{xs:"12",className:"text-center my-2",children:[(0,n.jsx)("h2",{children:t.title}),(0,n.jsx)(y.Z,{children:t.description})]}),t.ImageCard.map(e=>e.img.data&&(0,n.jsx)(g.Z,{className:"text-center my-2 px-1",lg:Math.floor(12/t.ImageCard.length),md:5,children:(0,n.jsxs)(w(),{target:e.newTab?"_blank":"_self",href:e.url,children:[(0,n.jsx)(b.Z,{media:e.img,className:"mb-2 rounded shadow"}),(0,n.jsx)(y.Z,{children:e.description})]})},e.id)),t.button&&(0,n.jsx)(g.Z,{xs:"12",className:"text-center my-2",children:(0,n.jsx)(h.Z,{className:"btn-lg",data:t.button},t.button.id)})]})},"sections.image-card-row":e=>{var t;let{data:s}=e,{alignment:l,background:a,componentId:i}=s,r=null===(t=s.title)||void 0===t?void 0:t.replace(/%20|\s|\./g,"");return(0,n.jsxs)(j.Z,{className:"my-4 my-md-12",id:i||r,children:[(0,n.jsxs)(g.Z,{sm:12,className:"justify-content-lg-between text-center text-lg-start justify-content-center ",children:[s.title&&(0,n.jsx)("h2",{children:s.title}),(0,n.jsx)(y.Z,{children:s.description})]}),(0,n.jsx)(g.Z,{sm:12,children:(0,n.jsx)(j.Z,{className:"justify-content-".concat(l," align-item-center"),children:s.imageCard.map(e=>(0,n.jsx)(g.Z,{xs:12,sm:6,lg:4,children:(0,n.jsx)("a",{href:e.url,children:(0,n.jsx)(j.Z,{className:a?"p-1":"",children:(0,n.jsxs)(g.Z,{className:"text-center py-2 rounded ".concat(a?"bg-lighter ps-3":""),children:[e.img&&(0,n.jsx)(b.Z,{media:e.img,className:"rounded px-0 ".concat(s.background?"":"shadow")}),e.title&&(0,n.jsx)("h4",{children:e.title}),(0,n.jsx)(y.Z,{children:e.description})]})})})},e.id))})})]})},"sections.image-list-row":e=>{let{data:t}=e,s=t.shadow?"shadow":"",l="three"===t.columns?4:6;return(0,n.jsxs)(j.Z,{className:"".concat((0,C.m)(t.margines,"none")," justify-content-center"),children:[(t.title||t.description)&&(0,n.jsxs)(g.Z,{sm:8,className:"text-center mb-6",children:[t.title&&(0,n.jsx)("h2",{children:t.title}),t.description&&(0,n.jsx)(y.Z,{children:t.description})]}),(0,n.jsx)(g.Z,{sm:12,children:(0,n.jsx)(j.Z,{className:"justify-content-around align-item-center mt-lg-2",children:t.card.map(e=>(0,n.jsx)(g.Z,{md:l,className:"my-2",children:(0,n.jsxs)(j.Z,{children:[e.img.data&&(0,n.jsx)(g.Z,{xs:2,className:"p-0 ps-2 pe-sm-2 px-md-0 rounded",children:(0,n.jsx)(b.Z,{media:e.img,className:t.imgRound?"w-90 px-0 rounded-circle  "+s:"rounded px-0 "+s})}),(0,n.jsxs)(g.Z,{xs:10,children:[e.title&&(e.link?(0,n.jsx)(H.Z,{data:{url:e.link},children:(0,n.jsx)("h4",{className:"my-0 d-inline-block",children:e.title})}):(0,n.jsx)("h4",{className:"my-0 d-inline-block",children:e.title})),(0,n.jsx)("span",{className:"badge bg-lighter ms-1 mb-o5 text-body align-bottom",children:e.tag}),(0,n.jsx)(y.Z,{children:e.description})]})]})},e.id))})}),(0,n.jsx)(g.Z,{className:"text-center",children:t.button&&(0,n.jsx)(h.Z,{data:t.button})})]})},"sections.img-cta":e=>{let{data:t}=e;return(0,n.jsxs)(j.Z,{className:"justify-content-center align-items-center my-md-20 my-4",children:[(0,n.jsxs)(g.Z,{md:4,className:"text-center text-md-start",children:[t.img.data&&(0,n.jsx)(b.Z,{media:t.img}),(0,n.jsx)(d,{scale:"1.1",children:(0,n.jsx)(o,{$x:"-50%",$y:"-75%",$angle:"-15deg",width:"400",height:"400",viewBox:"0 0 400 400",fill:"#3170FE",xmlns:"http://www.w3.org/2000/svg",children:(0,n.jsx)("path",{d:"M75.7356 325.213C37.1365 286.614 17.837 267.315 8.91882 245.785C-2.97294 217.077 -2.97294 184.821 8.91882 156.113C17.837 134.583 37.1365 115.284 75.7356 76.6845C114.335 38.0855 133.634 18.786 155.164 9.8678C183.872 -2.02397 216.128 -2.02397 244.836 9.8678C266.366 18.786 285.665 38.0855 324.264 76.6845C362.863 115.284 382.163 134.583 391.081 156.113C402.973 184.821 402.973 217.077 391.081 245.785C382.163 267.315 362.863 286.614 324.264 325.213C285.665 363.812 266.366 383.112 244.836 392.03C216.128 403.922 183.872 403.922 155.164 392.03C133.634 383.112 114.335 363.812 75.7356 325.213Z",fillOpacity:"0.05"})})})]}),(0,n.jsxs)(g.Z,{md:7,className:"text-center",children:[(0,n.jsx)("h2",{children:t.title}),t.button&&(0,n.jsx)(h.Z,{data:t.button},t.button.id)]})]})},"sections.logo-content-block":e=>{let{data:t}=e,s="left"==t.imgPosition?"last":"first",[l,i]=(0,a.useState)({}),r=(e,t)=>{i(s=>({...s,[e]:t}))},c=(e,t)=>{i(s=>({...s,[e]:t}))};return(0,n.jsxs)(P.Z,{className:"justify-content-center justify-content-md-between align-items-center my-md-12 my-4 rounded bg-lighter p-3",children:[(0,n.jsxs)(j.Z,{xs:12,className:"text-center my-gutter",children:[(0,n.jsx)("h2",{children:t.title}),(0,n.jsx)(g.Z,{children:t.logo.map(e=>{var t,s,i,d,o,m,x,h,p;let u=l[null===(t=e.greyLogo)||void 0===t?void 0:t.id]===((null===(s=e.colorLogo)||void 0===s?void 0:s.data)?e.colorLogo.data.attributes.url:null);return(0,n.jsx)(a.Fragment,{children:(null===(i=e.colorLogo)||void 0===i?void 0:i.data)?(0,n.jsx)(H.Z,{data:e,className:"m-gutter p-gutter d-inline-block",children:(0,n.jsx)(b.Z,{height:null===(d=e.greyLogo)||void 0===d?void 0:d.data.attributes.height,width:null===(o=e.greyLogo)||void 0===o?void 0:o.data.attributes.width,media:e.greyLogo,onMouseEnter:()=>r(e.greyLogo.id,e.colorLogo.data.attributes.url),onMouseOut:()=>{var t,s;return c(null===(t=e.greyLogo)||void 0===t?void 0:t.id,null===(s=e.greyLogo)||void 0===s?void 0:s.data.attributes.url)},src:u?null===(m=e.colorLogo)||void 0===m?void 0:m.data.attributes.url:null===(x=e.greyLogo)||void 0===x?void 0:x.data.attributes.url},e.greyLogo.data.id+"_greyLogo")}):(0,n.jsx)(b.Z,{className:"m-gutter",height:null===(h=e.greyLogo)||void 0===h?void 0:h.data.attributes.height,width:null===(p=e.greyLogo)||void 0===p?void 0:p.data.attributes.width,media:e.greyLogo})},e.greyLogo.data.id+"_greyLogo")})})]}),(0,n.jsxs)(j.Z,{className:"justify-content-center justify-content-md-between align-items-center",children:[(0,n.jsxs)(g.Z,{md:{span:5},className:"text-center text-md-start",children:[(0,n.jsx)(y.Z,{children:t.description}),(0,n.jsx)(h.Z,{data:t.button},t.id+"_buttonLogoContentBlock")]}),(0,n.jsx)(g.Z,{xs:{order:"first"},md:{span:6,order:s},className:"text-center text-md-start",children:t.iframeURL?(0,n.jsx)("div",{children:t.iframeURL.includes("http")?(0,n.jsx)("div",{className:"iframed rounded shadow",children:(0,n.jsx)("iframe",{src:t.iframeURL,title:t.title,allowFullScreen:!0})}):(0,n.jsx)(es,{video:t.iframeURL,vimeoThumbnailUrl:t.vimeoThumbnailUrl,className:"iframed rounded shadow"})}):t.img.data&&(0,n.jsx)(b.Z,{media:t.img,className:t.shadow?"rounded  shadow":"rounded"})})]})]})},"sections.industry-header":e=>{let{data:t}=e;return(0,n.jsxs)(j.Z,{className:"align-items-center justify-content-center justify-content-md-left text-center text-md-start my-4 my-md-12",children:[(0,n.jsx)("h1",{children:t.title}),(0,n.jsx)(y.Z,{children:t.description})]})},"sections.story-banner":e=>{let{data:t}=e;return(0,n.jsx)(j.Z,{className:"align-items-center justify-content-center justify-content-md-between text-center text-md-start pt-12 pt-md-0 my-4 my-md-16",children:(0,n.jsxs)(g.Z,{md:5,className:"order-md-2 order-sx-1 ",children:[(0,n.jsx)("h3",{className:"text-body",children:t.subtitle}),(0,n.jsx)("h1",{children:t.title}),t.button&&(0,n.jsx)(h.Z,{data:t.button,className:"btn-lg"},t.button.id+t.button.type)]})})},"sections.people-block":e=>{let{data:t}=e;return(0,n.jsxs)(j.Z,{className:"justify-content-center align-items-center my-md-12 my-4",children:[(0,n.jsxs)(g.Z,{md:4,className:"text-center text-md-start",children:[(0,n.jsx)("h2",{children:t.title}),(0,n.jsx)(y.Z,{children:t.description}),(0,n.jsx)("div",{className:"text-md-end",children:t.imgB.data&&(0,n.jsx)(b.Z,{className:"w-75",media:t.imgB})})]}),(0,n.jsx)(g.Z,{md:7,children:t.imgR.data&&(0,n.jsx)(b.Z,{media:t.imgR})})]})},"sections.personal-cta":e=>{let{data:t}=e;return(0,n.jsx)(j.Z,{className:"justify-content-center justify-content-md-between align-items-center my-12 rounded bg-sales bg-luis",children:(0,n.jsx)(j.Z,{className:"align-items-center justify-content-center justify-content-lg-end text-md-start ",children:(0,n.jsxs)(g.Z,{xs:8,sm:6,md:5,className:"pb-lg-12",children:[(0,n.jsx)("h3",{className:"pt-4 mt-md-0",children:t.title}),(0,n.jsx)(y.Z,{children:t.description}),(0,n.jsx)(h.Z,{data:t.button},t.button.id)]})})})},"sections.platforms-banner":el,"sections.recent-news":e=>{let{data:t}=e,s=t.readMore?t.readMore:"Read more news";return(0,n.jsxs)(j.Z,{className:"my-4 my-md-12",children:[(t.title||t.description)&&(0,n.jsx)(r,{children:(0,n.jsxs)(g.Z,{xs:"12",className:"text-center my-2 mb-sm-6",children:[(0,n.jsx)("h2",{children:t.title}),(0,n.jsx)(y.Z,{children:t.description})]})}),t.articles.filter(e=>!t.tag||e.attributes.tags.includes(t.tag)).slice(0,t.postCount).map((e,s)=>{var l;return(0,n.jsx)(g.Z,{lg:Math.floor(12/t.postCount),sm:6,className:"my-2",children:(0,n.jsxs)(r,{delayMs:100*s,children:[(0,n.jsx)(w(),{href:"/news/".concat(e.attributes.slug,"/"),locale:!1,children:e.attributes.thumbnail.data&&(0,n.jsx)(b.Z,{loading:"lazy",className:"h-auto rounded w-100",media:e.attributes.thumbnail})}),(0,n.jsxs)("p",{children:[null===(l=e.attributes.author.data)||void 0===l?void 0:l.attributes.name," /"," ",new Date(e.attributes.date).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"})]}),(0,n.jsx)(w(),{href:"/news/".concat(e.attributes.slug,"/"),locale:!1,children:(0,n.jsx)("h3",{className:"hover-primary",children:e.attributes.shortName})})]})},e.id)}),(0,n.jsx)(r,{children:(0,n.jsx)(g.Z,{xs:12,className:"mt-2 text-start text-sm-center",children:(0,n.jsx)(h.Z,{data:{url:"".concat(t.tag?"https://www.maptiler.com/news/labels/#"+t.tag:"https://www.maptiler.com/news/"),type:"link",text:s}})})})]})},"sections.pricing-table":e=>{let{data:t}=e;return(0,n.jsxs)(j.Z,{className:"justify-content-center",children:[t.title&&(0,n.jsxs)(g.Z,{xs:12,className:"mt-4 text-center",children:[(0,n.jsx)("h2",{id:t.title.toLowerCase().replace(/\s+(.)/g,(e,t)=>t.toUpperCase()),children:t.title}),(0,n.jsx)(y.Z,{children:t.description})]}),(0,n.jsxs)(g.Z,{xs:12,className:"px-0 px-sm-gutter ".concat(t.description?"":"my-4"),children:[(0,n.jsxs)(ep,{className:"d-none d-md-block px-lg-4 px-2 pt-0 pb-4",children:[(0,n.jsxs)("table",{children:[(0,n.jsx)("thead",{children:(0,n.jsxs)("tr",{children:[(0,n.jsx)("td",{className:"border-bottom border-light"}),(eu[t.product]||[]).map((e,t)=>(0,n.jsx)("td",{className:"border-bottom border-light",children:(0,n.jsx)("h4",{className:"m-0",children:e})},t))]})}),(0,n.jsx)("tbody",{children:t.TableRow.map((e,s)=>(0,n.jsxs)("tr",{className:s%2==0?"":"bg-lighter",children:[(0,n.jsxs)("td",{children:[(0,n.jsx)(y.Z,{className:"text-darker d-inline-block",children:(0,V.bu)(e.description)}),e.tooltip&&(0,n.jsx)(I.Z,{text:e.tooltip})]}),(0,n.jsx)("td",{children:(0,n.jsx)(y.Z,{children:(0,V.bu)(e.FirstPlan)})}),(0,n.jsx)("td",{children:(0,n.jsx)(y.Z,{children:(0,V.bu)(e.SecondPlan)})}),eu[t.product].length>2&&(0,n.jsx)("td",{children:(0,n.jsx)(y.Z,{children:(0,V.bu)(e.ThirdPlan)})}),eu[t.product].length>3&&(0,n.jsx)("td",{children:(0,n.jsx)(y.Z,{children:(0,V.bu)(e.FourthPlan)})})]},e.id))})]}),t.footnote&&(0,n.jsx)("div",{className:"ps-1 text-prewrap",children:(0,n.jsx)(y.Z,{children:(0,V.bu)(t.footnote)})})]}),(eu[t.product]||[]).map((e,s)=>(0,n.jsxs)(ep,{className:"d-block d-md-none px-lg-4 px-2 pt-0 pb-4 my-2",children:[(0,n.jsxs)("table",{children:[(0,n.jsx)("thead",{children:(0,n.jsxs)("tr",{children:[(0,n.jsx)("td",{className:"border-bottom border-light"}),(0,n.jsx)("td",{className:"border-bottom border-light",children:(0,n.jsx)("h4",{className:"m-0",children:e})},s)]})}),(0,n.jsx)("tbody",{children:t.TableRow.map((e,t)=>(0,n.jsxs)("tr",{className:t%2==0?"":"bg-lighter",children:[(0,n.jsxs)("td",{children:[(0,n.jsx)(y.Z,{children:(0,V.bu)(e.description)}),e.tooltip&&(0,n.jsx)(I.Z,{text:e.tooltip})]}),(0,n.jsx)("td",{children:(0,n.jsx)(y.Z,{children:(0,V.bu)(0===s?e.FirstPlan:1===s?e.SecondPlan:2===s?e.ThirdPlan:e.FourthPlan)})})]},e.id))})]}),t.footnote&&(0,n.jsx)("div",{className:"ps-1 text-prewrap",children:(0,n.jsx)(y.Z,{children:(0,V.bu)(t.footnote)})})]},e+s))]}),t.text&&(0,n.jsxs)(g.Z,{xs:12,className:"my-4 text-center",children:[(0,n.jsx)(y.Z,{children:t.text}),t.button&&(0,n.jsx)(h.Z,{className:"ms-gutter my-2 my-xs-0",data:t.button},t.button.id)]})]})},"sections.progress-card-row":e=>{let t,s,{data:l}=e;l.background?(t="bg-lighter rounded p-4",s=" bg-light rounded-circle"):(t="",s="text-primary bg-lighter rounded-circle ");let i=l.progressCard.length;return(0,n.jsxs)(j.Z,{className:"justify-content-around text-center my-4 my-md-12 ".concat(t),children:[(0,n.jsx)(g.Z,{xs:12,children:(0,n.jsx)("h2",{children:l.title})}),l.progressCard.map((e,t)=>(0,n.jsxs)(a.Fragment,{children:[(0,n.jsx)(g.Z,{xs:12,sm:5,lg:!0,className:"px-0",children:(0,n.jsxs)("div",{className:"h-100 d-flex flex-column justify-content-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"d-inline-block bigger px-gutter py-o5 ".concat(s),children:t+1}),(0,n.jsx)("h3",{children:e.title}),(0,n.jsx)(y.Z,{children:e.description})]}),(0,n.jsx)("div",{children:e.button&&(0,n.jsx)(h.Z,{className:"mb-3 mb-sm-0",data:e.button},e.id+"_progressCardButton")})]})}),t+1<i&&(0,n.jsx)(g.Z,{sm:1,className:"d-none d-lg-block ",children:(0,n.jsx)(_.Z,{name:"arrow_forward",width:50,height:50,className:"mt-sm-12"})})]},e.id+"progressCard"))]})},"sections.related":e=>{let{data:t}=e;return(0,n.jsxs)(j.Z,{className:"justify-content-center justify-content-md-between align-items-center my-12",children:[t.ComponentTitle?(0,n.jsx)(g.Z,{xs:12,className:"mb-4 mb-md-6",children:(0,n.jsx)("h2",{className:"text-center",children:t.ComponentTitle})}):null,(0,n.jsx)(g.Z,{xs:10,md:6,className:"text-center text-md-start",children:t.img.data?(0,n.jsx)(b.Z,{media:t.img,className:t.shadow?"rounded  shadow":"rounded"}):(0,n.jsx)(es,{video:t.videoURL,vimeoThumbnailUrl:t.vimeoThumbnailUrl,className:"iframed rounded shadow"})}),(0,n.jsxs)(g.Z,{xs:8,sm:6,md:5,className:"text-md-start",children:[(0,n.jsx)("h3",{className:"text-uppercase mt-4 mt-md-0",children:t.title}),(0,n.jsx)(y.Z,{children:t.description}),(0,n.jsx)(h.Z,{data:t.button},t.button.id)]})]})},"components.rich-text":e=>{let{data:t}=e,s="text-"+t.textAlignment;return(0,n.jsx)(j.Z,{className:"justify-content-center",children:(0,n.jsx)(g.Z,{md:Z[t.size],className:s+(0,C.m)(t.margins,"none"),children:(0,n.jsx)(y.Z,{children:t.text})})})},"components.rectangle-shape":x,"elements.cloud-pricing":e=>{let{data:t}=e,[l,i]=(0,a.useState)(!1),[r,c]=(0,a.useState)(!1);(0,a.useEffect)(()=>{l&&r&&function(e){let{plan:t,includedSessions:n,includedRequests:l,includedFiles:a,prepaidContract:i,custom:r,free:c,total:d}=e,o=e.pricingCalculator[0],m=e.pricingCalculator[1],x=e.pricingCalculator[2],h=e.pricingCalculator[3],p=c||"Free",u=document.getElementById("planDiv"),j=document.getElementById("planInfo"),g=document.getElementById("subButton"),b=document.getElementsByClassName("priceTotal"),y={planPrice:0,purchaseLink:{text:o.subscribeButtonText,url:o.subscribeButtonUrl},planDiv:'\n  <div class="text-center px-2">\n  <h3 class="fw-bold m-0 mb-1 my-lg-2">'.concat(o.planName,'</h3>\n  <span class="h1 text-dark text-capitalize priceTotal">').concat(b[0].textContent,'</span>\n  <p class="small d-none d-lg-block">').concat(o.requestInfo,'\n    <a class="tooltip-handle text-dark" type="button" data-bs-toggle="tooltip" data-bs-placement="top"\n      title=\'').concat(o.requestToolTip,'\'>\n     <svg class="svg-text" version="1.1" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24">\n    <use href="/styles/style/icon/icon.svg#info"></use>\n  </svg>\n    </a>\n  </p>\n  <a class="btn btn-lg btn-primary my-1 d-none d-lg-block" href=').concat(o.planButtonUrl,">").concat(o.planButtonText,'</a>\n  <p class="d-none d-lg-block">').concat(o.planDescription,"</p>\n  </div>\n  "),planInfo:'\n  <p class="mb-o5 d-flex justify-content-between">\n  <span> <span class="fw-bolder text-secondary m-0 text-uppercase">'.concat(o.planName,'</span> <span class="text-secondary fw-bolder m-0">').concat(t,'</span></span>\n  <span class="text-success text-end">').concat(p,'</span>\n  </p>\n  <div class="ps-1">\n    <p class="d-flex m-0 justify-content-between small fw-bolder"><span>').concat(n,'</span><span>5k</span></p>\n    <p class="d-flex m-0 justify-content-between small fw-bolder"><span>').concat(l,'</span><span>100k</span></p>\n    <p class="d-flex m-0 justify-content-between small fw-bolder"><span>').concat(a,"</span><span>100MB/1 ").concat(o.filesUnit,"</span></p>\n  </div>\n  ")},f={planPrice:25,purchaseLink:{text:m.subscribeButtonText,url:m.subscribeButtonUrl},planDiv:'\n<div class="text-center px-2">\n<h3 class="fw-bold m-0 mb-1 my-lg-2">'.concat(m.planName,"<span>  (").concat(d,')</span></h3>\n<span class="h1 text-dark text-capitalize priceTotal">').concat(b[0].textContent,'</span>\n<span class="fw-bolder d-inline">/').concat(m.priceUnit,' (USD)</span>\n<p class="small d-none d-lg-block">').concat(m.requestInfo,'\n  <a class="tooltip-handle text-dark" type="button" data-bs-toggle="tooltip" data-bs-placement="top"\n    title="').concat(m.requestToolTip,'">\n   <svg class="svg-text" version="1.1" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24">\n  <use href="/styles/style/icon/icon.svg#info"></use>\n</svg>\n  </a>\n</p>\n<a class="btn btn-lg btn-primary my-1 d-none d-lg-block" href="').concat(m.planButtonUrl,'">').concat(m.planButtonText,'</a>\n<p class="d-none text-dark d-lg-block">').concat(m.planDescription,"</p>\n</div>\n"),planInfo:'\n<p class="mb-o5 d-flex justify-content-between">\n<span> <span class="fw-bolder text-secondary m-0 text-uppercase">'.concat(m.planName,'</span> <span class="fw-bolder text-secondary">').concat(t,'</span> </span>\n<span class=" text-end">$25</span>\n</p>\n<div class="ps-1">\n  <p class="d-flex m-0 justify-content-between small fw-bolder"><span>').concat(n,'</span><span>25k</span></p>\n  <p class="d-flex m-0 justify-content-between small fw-bolder"><span>').concat(l,'</span><span>500k</span></p>\n  <p class="d-flex m-0 justify-content-between small fw-bolder"><span>').concat(a,"</span><span>10GB/10 ").concat(m.filesUnit,"</span></p>\n</div>\n")},w={planPrice:295,purchaseLink:{text:x.subscribeButtonText,url:x.subscribeButtonUrl},planDiv:'\n    <div class="text-center px-2">\n      <h3 class="fw-bold m-0 mb-1 my-lg-2">'.concat(x.planName,"<span>  (").concat(d,')</span></h3>\n      <span class="h1 text-dark text-capitalize priceTotal">').concat(b[0].textContent,'</span>\n      <span class="fw-bolder d-inline">/').concat(x.priceUnit,' (USD)</span>\n      <p class="small d-none d-lg-block">').concat(x.requestInfo,'\n        <a class="tooltip-handle text-dark" type="button" data-bs-toggle="tooltip" data-bs-placement="top" title="').concat(x.requestToolTip,'">\n          <svg class="svg-text" version="1.1" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24">\n            <use href="/styles/style/icon/icon.svg#info"></use>\n          </svg>\n        </a>\n      </p>\n      <a class="btn btn-lg btn-primary my-1 d-none d-lg-block" href="').concat(x.planButtonUrl,'">\n      ').concat(x.planButtonText,' </a>\n      <p class="d-none d-lg-block">').concat(x.planDescription,"</p>\n    </div>\n"),planInfo:'\n<p class="mb-o5 d-flex justify-content-between">\n<span>\n  <span class="fw-bolder text-secondary m-0 text-uppercase">'.concat(x.planName,'</span>\n  <span class="fw-bolder text-secondary">').concat(t,'</span>\n  </span>\n<span class="text-success text-end">$295</span>\n</p>\n<div class="ps-1">\n <p class="d-flex m-0 justify-content-between small fw-bolder"><span>').concat(n,'</span><span>25k</span></p>\n  <p class="d-flex m-0 justify-content-between small fw-bolder"><span>').concat(l,'</span><span>500k</span></p>\n  <p class="d-flex m-0 justify-content-between small fw-bolder"><span>').concat(a,"</span><span>100GB/1000 ").concat(x.filesUnit,"</span></p>\n</div>\n")},N={purchaseLink:{text:h.subscribeButtonText,url:h.subscribeButtonUrl},planDiv:'<div class="text-center px-2">\n      <h3 class="fw-bold m-0 mb-1 my-lg-2">'.concat(h.planName,'</h3>\n       <span class="h1 text-dark text-capitalize priceTotal d-lg-none">').concat(i,'</span>\n        <span class="fw-bolder d-none d-lg-block ">').concat(i,'</span>\n          <p class="small d-none d-lg-block">').concat(h.requestInfo,'\n            <a class="tooltip-handle text-dark" type="button" data-bs-toggle="tooltip" data-bs-placement="top"\n              title="').concat(h.requestToolTip,'">\n             <svg class="svg-text" version="1.1" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24">\n  <use href="/styles/style/icon/icon.svg#info"></use>\n</svg>\n            </a>\n          </p>\n          <a class="btn btn-lg btn-primary my-1 d-none d-lg-block" href="').concat(h.planButtonUrl,'" type="button">').concat(h.planButtonText,'</a>\n          <p class="d-none d-lg-block">').concat(h.planDescription,"</p>\n        </div>\n"),planInfo:'\n<p class="mb-o5 d-flex justify-content-between">\n<span><span class="fw-bolder text-secondary m-0 text-uppercase">'.concat(h.planName,'</span>\n<span class="fw-bolder text-secondary"> ').concat(t,'</span></span>\n<span class="text-success text-end">').concat(i,'</span>\n</p>\n<div class="ps-1">\n  <p class="d-flex m-0 justify-content-between small fw-bolder"><span>').concat(n,"</span><span>").concat(r,'</span></p>\n  <p class="d-flex m-0 justify-content-between small fw-bolder"><span>').concat(l,"</span><span>").concat(r,'</span></p>\n  <p class="d-flex m-0 justify-content-between small fw-bolder"><span>').concat(a,"</span><span>").concat(r,"</span></p>\n</div>\n")},v=document.getElementById("pricingDetail"),Z=()=>{v.style.display="none"===v.style.display?"block":"none"};window.addEventListener("resize",()=>{let e=window.innerWidth||document.documentElement.clientWidth;e>992&&"none"===v.style.display?v.style.display=Z():e<992&&"block"===v.style.display&&(v.style.display=Z())});let C=document.getElementById("mapsSessions"),k=document.getElementById("mapsSessionsInput"),L=document.getElementById("weatherSessions"),S=document.getElementById("weatherSessionsInput"),I=document.getElementById("sessionsTotal"),B=document.getElementById("extraSessionsNum"),E=document.getElementById("extraSessionsPrice"),_=[null],U=[null],D=[null],P=[null],M=[],T=[],F=document.getElementById("totalRequests"),q=document.getElementById("extraRequestsNum"),R=document.getElementById("extraRequestsPrice"),z={},$=document.getElementById("fileSlider"),V=document.getElementById("fileInput"),H=document.getElementById("totalFiles"),A=[null],O=document.getElementById("storageSlider"),G=document.getElementById("storageInput"),W=document.getElementById("totalStorage"),K=[null],X=document.getElementById("tilesSlider"),Y=document.getElementById("tilesInput"),Q=document.getElementById("staticSlider"),J=document.getElementById("staticInput"),ee=e=>"$"+(e.planPrice+parseFloat(R.textContent)+parseFloat(E.textContent));async function et(){let e=parseInt(C.noUiSlider.get())+parseInt(L.noUiSlider.get());I.textContent=e,await en()}C.noUiSlider||noUiSlider.create(C,{start:0,connect:"lower",range:{min:[0,1e3],"25%":[5e3,1e3],"50%":[25e3,1e3],"75%":[3e5,1e3],max:[1e6,1e3]},pips:{mode:"positions",values:[0,25,50,75,100],density:20,format:{to:e=>e<1e6?e/1e3+"k":e/1e6+"m"}},format:wNumb({decimals:0})}),L.noUiSlider||noUiSlider.create(L,{start:0,connect:"lower",range:{min:[0,1e3],"25%":[5e3,1e3],"50%":[25e3,1e3],"75%":[3e5,1e3],max:[1e6,1e3]},pips:{mode:"positions",values:[0,25,50,75,100],density:20,format:{to:e=>e<1e6?e/1e3+"k":e/1e6+"m"}},format:wNumb({decimals:0})}),k.addEventListener("change",function(){C.noUiSlider.set(this.value)}),S.addEventListener("change",function(){L.noUiSlider.set(this.value)}),C.noUiSlider.on("update",function(e,t){k.value=e[t],et(),_[t]&&_[t].classList.remove("active-pip"),_[t]=C.querySelector('.noUi-value[data-value="'+k.value+'"]'),_[t]&&_[t].classList.add("active-pip")}),L.noUiSlider.on("update",function(e,t){S.value=e[t],et(),U[t]&&U[t].classList.remove("active-pip"),U[t]=L.querySelector('.noUi-value[data-value="'+S.value+'"]'),U[t]&&U[t].classList.add("active-pip")}),Q.noUiSlider||noUiSlider.create(Q,{start:0,connect:"lower",range:{min:[0,1e4],"25%":[1e5,1e4],"50%":[5e5,1e4],"75%":[5e6,1e4],max:[1e7,1e4]},format:wNumb({decimals:0}),pips:{mode:"positions",values:[0,25,50,75,100],density:20,format:{to:e=>e<1e6?e/1e3+"k":e/1e6+"m"}}}),X.noUiSlider||noUiSlider.create(X,{start:0,connect:"lower",range:{min:[0,1e4],"25%":[1e5,1e4],"50%":[5e5,1e4],"75%":[5e6,1e4],max:[1e7,1e4]},format:wNumb({decimals:0}),pips:{mode:"positions",values:[0,25,50,75,100],density:20,format:{to:e=>e<1e6?e/1e3+"k":e/1e6+"m"}}}),Y.addEventListener("change",function(){X.noUiSlider.set(this.value)}),X.noUiSlider.on("update",async function(e,t){Y.value=e[t],es(),await en(),D[t]&&D[t].classList.remove("active-pip"),D[t]=X.querySelector('.noUi-value[data-value="'+Y.value+'"]'),D[t]&&D[t].classList.add("active-pip")}),J.addEventListener("change",function(){Q.noUiSlider.set(this.value)}),Q.noUiSlider.on("update",async function(e,t){J.value=e[t],es(),await en(),P[t]&&P[t].classList.remove("active-pip"),P[t]=Q.querySelector('.noUi-value[data-value="'+J.value+'"]'),P[t]&&P[t].classList.add("active-pip")});for(let e=1;e<=7;e++){let t=document.getElementById("requestSlider"+e),s=document.getElementById("requestInput"+e);t.noUiSlider||noUiSlider.create(t,{start:0,connect:"lower",range:{min:[0,1e4],"25%":[1e5,1e4],"50%":[5e5,1e4],"75%":[5e6,1e4],max:[1e7,1e4]},format:wNumb({decimals:0}),pips:{mode:"positions",values:[0,25,50,75,100],density:20,format:{to:e=>e<1e6?e/1e3+"k":e/1e6+"m"}}}),M.push(t),T.push(s);let n=[null];z[e-1]=n}function es(){let e;e=0+4*parseInt(X.noUiSlider.get())+15*parseInt(Q.noUiSlider.get()),M.forEach(function(t){e+=parseInt(t.noUiSlider.get())}),F.textContent=e}async function en(){I.textContent<=5e3&&F.textContent<=1e5&&H.textContent<=1&&W.textContent<=100?(u.innerHTML=y.planDiv,j.innerHTML=y.planInfo,g.href=y.purchaseLink.url,g.textContent=y.purchaseLink.text,Array.prototype.forEach.call(b,e=>{e.textContent=p}),B.textContent=0,E.textContent=0,q.textContent=0,R.textContent=0):I.textContent<16e4&&F.textContent<32e5&&H.textContent<=10&&W.textContent<=1e4?(u.innerHTML=f.planDiv,j.innerHTML=f.planInfo,g.href=f.purchaseLink.url,g.textContent=f.purchaseLink.text,I.textContent>25e3?(B.textContent=(I.textContent-25e3)/1e3,E.textContent=(I.textContent-25e3)/1e3*2):(B.textContent=0,E.textContent=0),F.textContent>5e5?(q.textContent=(F.textContent-5e5)/1e3,R.textContent=(F.textContent-5e5)/1e3*.1):(q.textContent=0,R.textContent=0),Array.prototype.forEach.call(b,e=>{e.textContent=ee(f)})):I.textContent<1e6&&F.textContent<1e7&&H.textContent<=1e3&&W.textContent<=1e5?(u.innerHTML=w.planDiv,j.innerHTML=w.planInfo,g.href=w.purchaseLink.url,g.textContent=w.purchaseLink.text,I.textContent>3e5?(B.textContent=(I.textContent-3e5)/1e3,E.textContent=((I.textContent-3e5)/1e3*1.5).toFixed(2)):(B.textContent=0,E.textContent=0),F.textContent>5e6?(q.textContent=(F.textContent-5e6)/1e3,R.textContent=((F.textContent-5e6)/1e3*.08).toFixed(2)):(q.textContent=0,R.textContent=0),Array.prototype.forEach.call(b,e=>{e.textContent=ee(w)})):(u.innerHTML=N.planDiv,j.innerHTML=N.planInfo,g.href=N.purchaseLink.url,g.textContent=N.purchaseLink.text,q.textContent=0,R.textContent=0,B.textContent=0,E.textContent=0,Array.prototype.forEach.call(b,e=>{e.textContent=i}));let e=await s.e(8665).then(s.bind(s,8665)),t=document.querySelector(".tooltip-handle");new e.Tooltip(t)}T.forEach(function(e,t){e.addEventListener("change",function(){M[t].noUiSlider.set(this.value)})}),M.forEach(function(e,t){e.noUiSlider.on("update",async function(s,n){T[t].value=s[n];let l=z[t];es(null,null),await en(),l[n]&&l[n].classList.remove("active-pip"),l[n]=e.querySelector('.noUi-value[data-value="'+T[t].value+'"]'),l[n]&&l[n].classList.add("active-pip")})}),$.noUiSlider||noUiSlider.create($,{start:[1],connect:"lower",range:{min:[1,1],"33%":[10,1],"66%":[1e3,1],max:[1e4,1]},format:wNumb({decimals:0}),pips:{mode:"positions",values:[0,33,66,100],density:20,format:{to:e=>e>=1e4?e+" +":e}}}),$.noUiSlider.on("update",async function(e,t){V.value=e[t],H.textContent=e[t],await en(),A[t]&&A[t].classList.remove("active-pip"),A[t]=$.querySelector('.noUi-value[data-value="'+V.value+'"]'),A[t]&&A[t].classList.add("active-pip")}),V.addEventListener("change",function(){$.noUiSlider.set(this.value)}),O.noUiSlider||noUiSlider.create(O,{start:[100],connect:"lower",range:{min:[100,100],"33%":[1e4,100],"66%":[1e5,100],max:[1e6,100]},format:wNumb({decimals:0}),pips:{mode:"positions",values:[0,33,66,100],density:20,format:{to:e=>e<1e3?e+"MB":e<1e6?e/1e3+"GB":e/1e6+"TB +"}}}),O.noUiSlider.on("update",async function(e,t){G.value=e[t],W.textContent=e[t],await en(),K[t]&&K[t].classList.remove("active-pip"),K[t]=O.querySelector('.noUi-value[data-value="'+G.value+'"]'),K[t]&&K[t].classList.add("active-pip")}),G.addEventListener("change",function(){O.noUiSlider.set(this.value)});let el=window.innerWidth||document.documentElement.clientWidth;document.getElementById("detailButton").addEventListener("click",Z),el<992&&"block"===v.style.display&&Z(),document.getElementById("resetButton").addEventListener("click",function(){C.noUiSlider.reset(),L.noUiSlider.reset(),X.noUiSlider.reset(),Q.noUiSlider.reset(),O.noUiSlider.reset(),$.noUiSlider.reset(),M.forEach(function(e){e.noUiSlider.reset()})}),en()}(t)},[l,r,t]);let{total:d,vatInfo:o,comparePlans:m,reset:x,extraSessions:h,extraRequests:p,apis:u,detail:b,subscribe:f}=t,w=(e,s)=>{let n=null==t?void 0:t.cards.find(t=>t.block===e);return n?n[s]:""},N=e=>e?(0,n.jsx)(I.Z,{text:e,big:!0}):null;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(j.Z,{className:"justify-content-center align-items-top",children:[(0,n.jsx)(g.Z,{lg:8,children:(0,n.jsxs)("div",{className:"d-flex flex-column justify-content-center align-items-center",children:[(0,n.jsxs)("div",{className:"bg-white shadow p-gutter my-gutter w-100 rounded",children:[(0,n.jsxs)("div",{className:"d-flex align-items-center border-bottom border-light py-1",children:[(0,n.jsx)("img",{src:"/img/maps-icon.svg",alt:""}),(0,n.jsx)("h3",{className:"m-0 ms-gutter d-inline-block",id:"maps",children:w("maps","title")})]}),(0,n.jsxs)("div",{className:"px-2",children:[(0,n.jsx)("p",{className:"text-prewrap",children:(0,n.jsx)(y.Z,{disallowedElements:["p"],unwrapDisallowed:!0,children:w("maps","header")})}),(0,n.jsxs)("div",{className:"d-flex align-items-baseline px-1 py-2 flex-column flex-lg-row",children:[(0,n.jsx)(g.Z,{xs:12,lg:6,id:"mapsSessions"}),(0,n.jsxs)(g.Z,{xs:12,lg:5,className:"mt-3 mt-lg-0 offset-lg-1 d-flex",children:[(0,n.jsx)("input",{className:"form-control w-40",step:1e3,type:"number",id:"mapsSessionsInput"}),(0,n.jsxs)("p",{className:"m-0 smaller d-inline-block ms-1",children:[(0,n.jsxs)("span",{className:"text-secondary big",children:[w("maps","unit")," /"]}),(0,n.jsx)("span",{className:"text-secondary",children:w("maps","time")}),(0,n.jsx)("br",{}),w("maps","unitDescription")]})]})]}),(0,n.jsx)("p",{className:"mt-1 text-prewrap",children:(0,n.jsx)(y.Z,{disallowedElements:["p"],unwrapDisallowed:!0,children:w("maps","footer")})})]})]}),(0,n.jsxs)("div",{className:"bg-white shadow p-gutter my-gutter w-100 rounded",children:[(0,n.jsxs)("div",{className:"d-flex align-items-center border-bottom border-light py-1",children:[(0,n.jsx)("img",{src:"/img/maps-icon.svg",alt:""}),(0,n.jsx)("h3",{className:"m-0 ms-gutter d-inline-block",children:w("search","title")})]}),(0,n.jsxs)("div",{className:"px-3",children:[(0,n.jsx)("p",{className:"text-prewrap",children:(0,n.jsx)(y.Z,{disallowedElements:["p"],unwrapDisallowed:!0,children:w("search","header")})}),(0,n.jsxs)("div",{className:"d-flex align-items-baseline px-1 py-2 flex-column flex-lg-row",children:[(0,n.jsx)(g.Z,{xs:12,lg:6,id:"requestSlider1"}),(0,n.jsxs)(g.Z,{xs:12,lg:5,className:"mt-3 mt-lg-0 offset-lg-1 d-flex",children:[(0,n.jsx)("input",{className:"form-control w-40",step:1e4,type:"number",id:"requestInput1"}),(0,n.jsxs)("p",{className:"m-0 smaller d-inline-block ms-1",children:[(0,n.jsxs)("span",{className:"text-secondary big",children:[w("search","unit")," /"]}),(0,n.jsx)("span",{className:"text-secondary",children:w("search","time")}),(0,n.jsx)("br",{}),w("search","unitDescription")]})]})]}),(0,n.jsx)("p",{className:"mt-1 text-prewrap",children:(0,n.jsx)(y.Z,{disallowedElements:["p"],unwrapDisallowed:!0,children:w("search","footer")})})]})]}),(0,n.jsxs)("div",{id:"weather",className:"bg-white shadow p-gutter my-gutter w-100 rounded",children:[(0,n.jsxs)("div",{className:"d-flex align-items-center border-bottom border-light py-1",children:[(0,n.jsx)("img",{src:"/img/weather-icon.svg",alt:""}),(0,n.jsx)("h3",{className:"m-0 ms-gutter d-inline-block",children:w("weather","title")})]}),(0,n.jsxs)("div",{className:"px-2",children:[(0,n.jsx)("p",{className:"text-prewrap",children:(0,n.jsx)(y.Z,{disallowedElements:["p"],unwrapDisallowed:!0,children:w("weather","header")})}),(0,n.jsxs)("div",{className:"d-flex align-items-baseline px-1 py-2 flex-column flex-lg-row",children:[(0,n.jsx)(g.Z,{xs:12,lg:6,id:"weatherSessions"}),(0,n.jsxs)(g.Z,{xs:12,lg:6,className:"mt-3 mt-lg-0 offset-lg-1 d-flex",children:[(0,n.jsx)("input",{className:"form-control w-40",step:1e3,type:"number",id:"weatherSessionsInput"}),(0,n.jsxs)("p",{className:"m-0 smaller d-inline-block ms-1",children:[(0,n.jsxs)("span",{className:"text-secondary big",children:[w("weather","unit")," /"]}),(0,n.jsx)("span",{className:"text-secondary",children:w("weather","time")}),(0,n.jsx)("br",{}),w("weather","unitDescription")]})]})]}),(0,n.jsx)("p",{className:"mt-1 text-prewrap",children:(0,n.jsx)(y.Z,{disallowedElements:["p"],unwrapDisallowed:!0,children:w("weather","footer")})})]})]}),(0,n.jsxs)("div",{className:"bg-white shadow p-gutter my-gutter w-100 rounded",id:"storage",children:[(0,n.jsxs)("div",{className:"d-flex align-items-center border-bottom border-light py-1",children:[(0,n.jsx)("img",{src:"/styles/style/icon/SVG/storage_pricing.svg",alt:""}),(0,n.jsx)("h3",{className:"m-0 ms-gutter d-inline-block",children:w("storage","title")})]}),(0,n.jsxs)("div",{className:"px-2",children:[(0,n.jsx)("p",{className:"text-prewrap",children:(0,n.jsx)(y.Z,{disallowedElements:["p"],unwrapDisallowed:!0,children:w("storage","header")})}),(0,n.jsxs)("div",{className:"d-flex align-items-baseline px-1 py-2 flex-column flex-lg-row",children:[(0,n.jsx)("div",{className:"col-12 col-lg-6",id:"fileSlider"}),(0,n.jsxs)("div",{className:"col-12 col-lg-5 mt-3 mt-lg-0 offset-lg-1 d-flex align-items-center",children:[(0,n.jsx)("input",{className:"form-control w-40",step:1,type:"number",id:"fileInput"}),(0,n.jsx)("p",{className:"m-0 smaller d-inline-block ms-1 text-secondary big",children:w("storage","unit")})]})]}),(0,n.jsxs)("div",{className:"d-flex align-items-baseline px-1 py-2 flex-column flex-lg-row",children:[(0,n.jsx)(g.Z,{xs:12,lg:6,id:"storageSlider"}),(0,n.jsxs)(g.Z,{xs:12,lg:5,className:"mt-3 mt-lg-0 offset-lg-1 d-flex align-items-center",children:[(0,n.jsx)("input",{className:"form-control w-40",step:1e3,type:"number",id:"storageInput"}),(0,n.jsx)("p",{className:"m-0 smaller d-inline-block ms-1 text-secondary big",children:w("storage","unit2")})]})]}),(0,n.jsx)("p",{className:"mt-1 text-prewrap",children:(0,n.jsx)(y.Z,{disallowedElements:["p"],unwrapDisallowed:!0,children:w("storage","footer")})})]})]}),(0,n.jsx)("div",{className:"bg-lighter shadow px-gutter my-gutter w-100 text-center rounded",children:(0,n.jsx)("h6",{className:"my-1",children:(0,n.jsxs)("a",{className:"text-nowrap link-arrow",href:"#allPlans",children:[m,(0,n.jsx)(_.Z,{name:"keyboard_arrow_right",width:24,height:24,className:"link-arrow-svg"})]})})}),(0,n.jsxs)("div",{className:"bg-white shadow p-gutter my-gutter w-100 rounded",children:[(0,n.jsxs)("div",{className:"d-flex align-items-center border-bottom border-light py-1",children:[(0,n.jsx)("img",{src:"/img/maps-icon.svg",alt:""}),(0,n.jsx)("h3",{className:"m-0 ms-gutter d-inline-block",children:u})]}),(0,n.jsxs)("div",{className:"px-3",children:[(0,n.jsxs)("h4",{children:[w("renderedTiles","title"),N(w("renderedTiles","tooltip"))]}),(0,n.jsxs)("div",{className:"d-flex align-items-baseline px-1 py-2 flex-column flex-lg-row",children:[(0,n.jsx)(g.Z,{xs:12,lg:6,id:"tilesSlider"}),(0,n.jsxs)(g.Z,{xs:12,lg:5,className:"mt-3 mt-lg-0 offset-lg-1 d-flex",children:[(0,n.jsx)("input",{className:"form-control w-40",step:1e4,type:"number",id:"tilesInput"}),(0,n.jsxs)("p",{className:"m-0 smaller d-inline-block ms-1",children:[(0,n.jsxs)("span",{className:"text-secondary big",children:[w("renderedTiles","unit")," /"]}),(0,n.jsx)("span",{className:"text-secondary",children:w("renderedTiles","time")}),(0,n.jsx)("br",{}),w("renderedTiles","unitDescription")]})]})]}),(0,n.jsx)("p",{className:"mt-1 text-prewrap",children:(0,n.jsx)(y.Z,{disallowedElements:["p"],unwrapDisallowed:!0,children:w("renderedTiles","footer")})}),(0,n.jsxs)("div",{className:"d-none",children:[(0,n.jsx)(g.Z,{xs:12,lg:6,id:"requestSlider2"}),(0,n.jsx)("input",{className:"form-control w-40",step:1e4,type:"number",id:"requestInput2"})]})]}),(0,n.jsxs)("div",{className:"px-3 border-bottom border-light",children:[(0,n.jsxs)("h4",{children:[w("tiles","title"),N(w("tiles","tooltip"))]}),(0,n.jsxs)("div",{className:"d-flex align-items-baseline px-1 py-2 flex-column flex-lg-row",children:[(0,n.jsx)(g.Z,{xs:12,lg:6,id:"requestSlider3"}),(0,n.jsxs)(g.Z,{xs:12,lg:5,className:"mt-3 mt-lg-0 offset-lg-1 d-flex",children:[(0,n.jsx)("input",{className:"form-control w-40",step:1e4,type:"number",id:"requestInput3"}),(0,n.jsxs)("p",{className:"m-0 smaller d-inline-block ms-1",children:[(0,n.jsxs)("span",{className:"text-secondary big",children:[w("tiles","unit")," /"]}),(0,n.jsx)("span",{className:"text-secondary",children:w("tiles","time")}),(0,n.jsx)("br",{}),w("tiles","unitDescription")]})]})]}),(0,n.jsx)("p",{className:"mt-1 text-prewrap",children:(0,n.jsx)(y.Z,{disallowedElements:["p"],unwrapDisallowed:!0,children:w("tiles","footer")})})]}),(0,n.jsxs)("div",{className:"px-3 border-bottom border-light",children:[(0,n.jsxs)("h4",{children:[w("staticMaps","title"),N(w("staticMaps","tooltip"))]}),(0,n.jsxs)("div",{className:"d-flex align-items-baseline px-1 py-2 flex-column flex-lg-row",children:[(0,n.jsx)(g.Z,{xs:12,lg:6,id:"staticSlider"}),(0,n.jsxs)(g.Z,{xs:12,lg:5,className:"mt-3 mt-lg-0 offset-lg-1 d-flex",children:[(0,n.jsx)("input",{className:"form-control w-40 ",step:1e4,type:"number",id:"staticInput"}),(0,n.jsxs)("p",{className:"m-0 smaller d-inline-block ms-1",children:[(0,n.jsxs)("span",{className:"text-secondary big",children:[w("staticMaps","unit")," /"]}),(0,n.jsx)("span",{className:"text-secondary",children:w("staticMaps","time")}),(0,n.jsx)("br",{}),w("staticMaps","unitDescription")]})]})]}),(0,n.jsx)("p",{className:"mt-1 text-prewrap",children:(0,n.jsx)(y.Z,{disallowedElements:["p"],unwrapDisallowed:!0,children:w("staticMaps","footer")})}),(0,n.jsxs)("div",{className:"d-none",children:[(0,n.jsx)("div",{className:"col-12 col-lg-6",id:"requestSlider4"}),(0,n.jsx)("input",{className:"form-control w-40",step:1e4,type:"number",id:"requestInput4"})]})]}),(0,n.jsxs)("div",{className:"px-3 border-bottom border-light",children:[(0,n.jsxs)("h4",{children:[w("data","title"),N(w("data","tooltip"))]}),(0,n.jsxs)("div",{className:"d-flex align-items-baseline px-1 py-2 flex-column flex-lg-row",children:[(0,n.jsx)(g.Z,{xs:12,lg:6,id:"requestSlider5"}),(0,n.jsxs)(g.Z,{xs:12,lg:5,className:"mt-3 mt-lg-0 offset-lg-1 d-flex",children:[(0,n.jsx)("input",{className:"form-control w-40",step:1e4,type:"number",id:"requestInput5"}),(0,n.jsxs)("p",{className:"m-0 smaller d-inline-block ms-1",children:[(0,n.jsxs)("span",{className:"text-secondary big",children:[w("data","unit")," /"]}),(0,n.jsx)("span",{className:"text-secondary",children:w("data","time")}),(0,n.jsx)("br",{}),w("data","unitDescription")]})]})]}),(0,n.jsx)("p",{className:"mt-1 text-prewrap",children:(0,n.jsx)(y.Z,{disallowedElements:["p"],unwrapDisallowed:!0,children:w("data","footer")})})]}),(0,n.jsxs)("div",{className:"px-3",children:[(0,n.jsxs)("h4",{children:[w("ipGeolocation","title"),N(w("ipGeolocation","tooltip"))]}),(0,n.jsxs)("div",{className:"d-flex align-items-baseline px-1 py-2 flex-column flex-lg-row",children:[(0,n.jsx)(g.Z,{xs:12,lg:6,id:"requestSlider6"}),(0,n.jsxs)(g.Z,{xs:12,lg:5,className:"mt-3 mt-lg-0 offset-lg-1 d-flex",children:[(0,n.jsx)("input",{className:"form-control w-40",step:1e4,type:"number",id:"requestInput6"}),(0,n.jsxs)("p",{className:"m-0 smaller d-inline-block ms-1",children:[(0,n.jsxs)("span",{className:"text-secondary big",children:[w("ipGeolocation","unit")," /"]}),(0,n.jsx)("span",{className:"text-secondary",children:w("ipGeolocation","time")}),(0,n.jsx)("br",{}),w("ipGeolocation","unitDescription")]})]})]}),(0,n.jsx)("p",{className:"mt-1 text-prewrap",children:(0,n.jsx)(y.Z,{disallowedElements:["p"],unwrapDisallowed:!0,children:w("ipGeolocation","footer")})})]}),(0,n.jsxs)("div",{className:"px-3",children:[(0,n.jsxs)("h4",{id:"coordinates",children:[w("coordinates","title"),N(w("coordinates","tooltip"))]}),(0,n.jsxs)("div",{className:"d-flex align-items-baseline px-1 py-2 flex-column flex-lg-row",children:[(0,n.jsx)(g.Z,{xs:12,lg:6,id:"requestSlider7"}),(0,n.jsxs)(g.Z,{xs:12,lg:5,className:"mt-3 mt-lg-0 offset-lg-1 d-flex",children:[(0,n.jsx)("input",{className:"form-control w-40",step:1e4,type:"number",id:"requestInput7"}),(0,n.jsxs)("p",{className:"m-0 smaller d-inline-block ms-1",children:[(0,n.jsxs)("span",{className:"text-secondary big",children:[w("coordinates","unit")," /"]}),(0,n.jsx)("span",{className:"text-secondary",children:w("coordinates","time")}),(0,n.jsx)("br",{}),w("coordinates","unitDescription")]})]})]}),(0,n.jsx)("p",{className:"mt-1 text-prewrap",children:(0,n.jsx)(y.Z,{disallowedElements:["p"],unwrapDisallowed:!0,children:w("coordinates","footer")})})]})]})]})}),(0,n.jsx)(g.Z,{lg:4,id:"pricingContainer",children:(0,n.jsxs)("div",{id:"pricingDiv",className:"bg-white shadow my-gutter rounded",children:[(0,n.jsxs)("div",{id:"mobilePricingDiv",className:"px-3 py-gutter",children:[(0,n.jsx)("div",{id:"planDiv"}),(0,n.jsxs)("div",{id:"pricingDetail",style:{display:"block"},className:"border-top border-light",children:[(0,n.jsxs)("div",{className:"d-none",children:[(0,n.jsx)("span",{id:"sessionsTotal"}),(0,n.jsx)("span",{id:"totalRequests"}),(0,n.jsx)("span",{id:"totalFiles"}),(0,n.jsx)("span",{id:"totalStorage"})]}),(0,n.jsx)("div",{id:"planInfo"}),(0,n.jsxs)("div",{id:"planCalc",children:[(0,n.jsxs)("p",{id:"extraSessions",className:"mb-o5 d-flex justify-content-between",children:[(0,n.jsx)("span",{className:"fw-bolder text-secondary m-0",children:h}),(0,n.jsxs)("span",{className:"fw-bolder text-secondary m-0",children:[(0,n.jsx)("span",{id:"extraSessionsNum",children:"0"}),"k"]}),(0,n.jsxs)("span",{className:"text-success",children:["$",(0,n.jsx)("span",{id:"extraSessionsPrice",children:"0"})]})]}),(0,n.jsxs)("p",{id:"extraRequests",className:"mt-o5 d-flex justify-content-between",children:[(0,n.jsx)("span",{className:"fw-bolder text-secondary m-0",children:p}),(0,n.jsxs)("span",{className:"fw-bolder text-secondary m-0",children:[(0,n.jsx)("span",{id:"extraRequestsNum",children:"0"}),"k"]}),(0,n.jsxs)("span",{className:"text-success",children:["$",(0,n.jsx)("span",{id:"extraRequestsPrice",children:"0"})]})]})]})]}),(0,n.jsx)("div",{children:(0,n.jsxs)("p",{className:"my-0 py-1 d-none d-lg-flex justify-content-between border-top border-light",children:[(0,n.jsx)("span",{className:"fw-bolder text-success m-0",children:d}),(0,n.jsx)("span",{id:"priceTotal",className:"fw-bolder text-success m-0 priceTotal",children:"0"})]})}),(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("p",{className:"d-none d-lg-block",children:o}),(0,n.jsx)("a",{className:"btn btn-lighter m-lg-gutter d-none d-lg-inline-block",type:"button",id:"resetButton",role:"button",children:x}),(0,n.jsx)("a",{className:"btn btn-lighter m-gutter d-inline-block d-lg-none",type:"button",id:"detailButton",role:"button",children:b}),(0,n.jsx)("a",{className:"btn btn-primary",id:"subButton",role:"button",children:f})]})]}),(0,n.jsx)("div",{className:"bg-lighter px-gutter w-100 text-center py-1 rounded-bottom",children:(0,n.jsx)("h6",{className:"my-0",children:(0,n.jsxs)("a",{className:"text-nowrap link-arrow",href:"#allPlans",children:[m,(0,n.jsx)(_.Z,{name:"keyboard_arrow_right",width:24,height:24,className:"link-arrow-svg"})]})})})]})})]}),(0,n.jsx)(E(),{type:"module",src:"/js/nouislider.js",onReady:()=>i(!0)}),(0,n.jsx)(E(),{type:"module",src:"/js/wNumb.js",onReady:()=>c(!0)})]})},"elements.request-table":e=>{let{data:t}=e,{title:s,description:l,row:a,firstColumnHeading:i,secondColumnHeading:r}=t;return(0,n.jsxs)(j.Z,{className:"justify-content-center justify-content-md-between align-items-center my-md-12 my-4 p-4 bg-lighter rounded",children:[(0,n.jsxs)(g.Z,{md:5,className:"text-start",children:[(0,n.jsx)("h2",{children:s}),(0,n.jsx)(y.Z,{children:l})]}),(0,n.jsx)(g.Z,{md:6,className:"text-center text-md-start",children:(0,n.jsxs)("table",{className:"table border border-light table-sm",children:[(0,n.jsx)("thead",{className:"table-light",children:(0,n.jsxs)("tr",{children:[(0,n.jsx)("th",{children:i}),(0,n.jsx)("th",{children:r})]})}),(0,n.jsx)("tbody",{children:a.map((e,t)=>(0,n.jsxs)("tr",{children:[(0,n.jsx)("td",{children:e.firstColumn}),(0,n.jsx)("td",{children:e.secondColumn})]},t))})]})})]})},"sections.story-intro":e=>{let{data:t}=e;return(0,n.jsxs)(j.Z,{className:"justify-content-center justify-content-md-start align-items-center my-md-12 my-4",children:[t.img.data&&(0,n.jsx)(g.Z,{children:t.img.data.map(e=>(0,n.jsx)(b.Z,{className:"me-4",media:{data:e},height:e.attributes.height,width:e.attributes.width},e.id))}),(0,n.jsxs)(g.Z,{xs:12,className:"text-center text-md-start",children:[!t.img.data&&(0,n.jsx)("h2",{children:t.title}),(0,n.jsx)("i",{className:"big",children:(0,n.jsx)(y.Z,{children:t.description})})]})]})},"sections.row-small-block":e=>{let{data:t}=e;return(0,n.jsxs)(j.Z,{className:"my-md-12 my-4",children:[t.title||t.description?(0,n.jsxs)(g.Z,{xs:12,className:"text-center",children:[t.title?(0,n.jsx)("h2",{children:t.title}):null,t.description?(0,n.jsx)(y.Z,{children:t.description}):null]}):null,(0,n.jsx)(g.Z,{xs:12,children:(0,n.jsx)(j.Z,{className:"justify-content-left align-items-center",children:t.smallblock.map(e=>{let t="left"===e.imgPosition?"first":"last",s="left"===e.imgPosition?"ps-0 ps-md-4 pe-sm-0 ":"pe-md-4 ps-0 mx-md-2",l="left"===e.imgPosition?" mx-sm-2":" mx-sm-2 mx-md-0";return(0,n.jsx)(g.Z,{xs:{span:12},lg:6,children:(0,n.jsxs)(j.Z,{className:"my-2 align-items-center",children:[(0,n.jsxs)(g.Z,{xs:6,className:s,children:[e.title?(0,n.jsx)("h3",{children:e.title}):null,e.description?(0,n.jsx)(y.Z,{children:e.description}):null,e.button?(0,n.jsx)(h.Z,{data:e.button},e.button):null]}),(0,n.jsx)(g.Z,{xs:{span:6,order:t},sm:4,className:"px-sm-0 justify-content-center mt-2"+l,children:e.img.data&&(0,n.jsx)(b.Z,{media:e.img,className:"rounded shadow"})})]})},e.id)})})}),t.button?(0,n.jsx)(g.Z,{xs:12,className:"text-center mt-2",children:(0,n.jsx)(h.Z,{data:t.button},t.button.id)}):null]})},"sections.team-member":e=>{let{data:t}=e;return(0,n.jsxs)(j.Z,{className:"align-items-center",children:[(0,n.jsxs)(g.Z,{className:"col-12 col-md-5 my-4",children:[(0,n.jsx)("h1",{children:t.name}),(0,n.jsx)("p",{className:"mt-0 big",children:t.job}),(0,n.jsx)(y.Z,{children:t.description})]}),(0,n.jsx)(g.Z,{className:"col-12 col-md-7 my-4 align-self-center",children:(0,n.jsx)(b.Z,{media:t.background})})]})},"sections.text-cards":e=>{let{data:t}=e,{cardsInRow:s,title:l,description:a,textCard:i}=t,r="two"===s,c=r?6:12/i.length,d=r?6:i.length%2==0?6:4;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(j.Z,{className:"justify-content-around mt-4 mt-md-12",children:(l||a)&&(0,n.jsxs)(g.Z,{xs:"12",className:"text-center my-2",children:[r?(0,n.jsx)("h1",{children:l}):(0,n.jsx)("h2",{children:l}),(0,n.jsx)(y.Z,{children:a})]})}),(0,n.jsx)(j.Z,{className:"justify-content-stretch mb-4 mb-md-12",children:i.map(e=>(0,n.jsx)(g.Z,{className:"text-center ".concat(r?"py-1 order-1 order-md-0":"my-2"),md:d,xl:c,children:(0,n.jsxs)("div",{className:"bg-lighter rounded h-100 d-flex flex-column justify-content-between",children:[(0,n.jsxs)("div",{className:"px-4 ".concat(r?"pt-2":"pt-3"),children:[r?(0,n.jsx)("h2",{children:e.title}):(0,n.jsx)("h4",{children:e.title}),(0,n.jsx)(y.Z,{className:r?"px-1 px-md-4 my-sm-4":"",children:e.description})]}),(0,n.jsxs)("div",{className:r?"px-8":"",children:[e.button&&(0,n.jsx)("div",{className:"me-sm-0 ".concat(e.img.data?"mb-2":"mb-5"),children:(0,n.jsx)(h.Z,{data:e.button},e.button.id)}),e.img.data&&(0,n.jsx)(b.Z,{className:"rounded-bottom",media:e.img},e.img.id)]})]})},e.id))})]})},"sections.tech-spec":e=>{let{data:t}=e;return(0,n.jsx)(j.Z,{id:"technicalSpecification",className:"justify-content-center my-12",children:(0,n.jsxs)(g.Z,{id:"accordion",children:[(0,n.jsx)(j.Z,{className:"text-center",children:(0,n.jsx)("h2",{children:(0,n.jsx)("a",{className:"text-secondary",href:"#technicalSpecification",children:t.title})})}),(0,n.jsxs)(j.Z,{className:"mt-4 justify-content-center",children:[(0,n.jsx)(g.Z,{md:5,id:"accordion",children:t.specificationLeft&&t.specificationLeft.map((e,t)=>{let{title:s,description:l,id:a}=e;return(0,n.jsxs)("div",{className:"bg-lighter rounded my-1",children:[(0,n.jsx)("a",{role:"button","data-bs-toggle":"collapse","data-bs-target":"#collapseAnswar".concat(a),"aria-expanded":0===t?"true":"false","aria-controls":"collapseAnswar".concat(a),children:(0,n.jsxs)("span",{id:s,className:"p-2 h4 fw-bold text-secondary d-block m-0",children:[s,(0,n.jsx)("span",{className:"float-end collapse-arrow",children:(0,n.jsx)(_.Z,{name:"keyboard_arrow_down",width:24,height:24})})]})}),(0,n.jsx)("div",{className:"collapse ".concat(0===t?"show":""),id:"collapseAnswar".concat(a),"aria-labelledby":"answar","data-parent":"#accordion",children:(0,n.jsx)("div",{className:"card-body px-2 py-1 faq-markdown",children:(0,n.jsx)(y.Z,{children:l})})})]},a)})}),(0,n.jsx)(g.Z,{md:5,id:"accordion",children:t.specificationRight&&t.specificationRight.map(e=>{let{title:t,description:s,id:l}=e;return(0,n.jsxs)("div",{className:"bg-lighter rounded my-1",children:[(0,n.jsx)("a",{role:"button","data-bs-toggle":"collapse","data-bs-target":"#collapseAnswar".concat(l),"aria-expanded":"false","aria-controls":"collapseAnswar".concat(l),children:(0,n.jsxs)("span",{id:t,className:"p-2 h4 fw-bold text-secondary d-block m-0",children:[t,(0,n.jsx)("span",{className:"float-end collapse-arrow",children:(0,n.jsx)(_.Z,{name:"keyboard_arrow_down",width:24,height:24})})]})}),(0,n.jsx)("div",{className:"collapse",id:"collapseAnswar".concat(l),"aria-labelledby":"answar","data-parent":"#accordion",children:(0,n.jsx)("div",{className:"card-body px-2 py-1 faq-markdown",children:(0,n.jsx)(y.Z,{children:s})})})]},l)})})]}),(0,n.jsx)(j.Z,{children:(0,n.jsx)(y.Z,{className:"text-center my-1",children:t.text})})]})})},"components.two-coloum-text":e=>{let{data:t}=e;return(0,n.jsxs)(j.Z,{className:"mb-md-12 my-4 justify-content-between",children:[t.title&&(0,n.jsx)(g.Z,{md:12,children:(0,n.jsx)("h2",{children:t.title})}),(0,n.jsx)(g.Z,{md:6,children:(0,n.jsx)(y.Z,{className:"big",children:t.textLeft})}),(0,n.jsx)(g.Z,{md:6,children:(0,n.jsx)(y.Z,{className:"big",children:t.textRight})})]})},"sections.text-content-block":e=>{let{data:t}=e;return(0,n.jsxs)(j.Z,{className:"justify-content-center justify-content-md-between align-items-center my-md-12 my-4 bg-lighter p-4",children:[(0,n.jsxs)(g.Z,{md:5,className:"text-center text-md-start",children:[(0,n.jsx)("h2",{children:t.title}),(0,n.jsx)(y.Z,{children:t.description})]}),(0,n.jsx)(g.Z,{md:5,className:"text-start bg-white shadow rounded p-3",children:(0,n.jsx)(y.Z,{children:t.rightText})})]})},"components.table":e=>{let{data:t}=e;return(0,p.ZP)(t.table,{replace:()=>null})},"components.wave-shape":e=>{let{data:t}=e;return(0,n.jsx)(k,{children:(0,n.jsxs)("svg",{className:"w-100 h-auto",id:"wave",viewBox:"0 0 1920 1080",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,n.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"ascending"==t.wave?"M1920 49.6216L1812.8 30.4216C1707.2 11.2216 1492.8 -27.1784 1280 30.4216C1067.2 88.0216 852.8 241.622 640 270.422C427.2 299.222 212.8 203.222 107.2 155.222L0 107.222V1102.82H107.2C212.8 1102.82 427.2 1102.82 640 1102.82C852.8 1102.82 1067.2 1102.82 1280 1102.82C1492.8 1102.82 1707.2 1102.82 1812.8 1102.82H1920V49.6216Z":"M0 49.6216L107.2 30.4216C212.8 11.2216 427.2 -27.1784 640 30.4216C852.8 88.0216 1067.2 241.622 1280 270.422C1492.8 299.222 1707.2 ************.8 155.222L1920 107.222V932.822H1812.8C1707.2 ************.8 ************ 932.822C1067.2 932.822 852.8 932.822 640 932.822C427.2 932.822 212.8 932.822 107.2 932.822H0V49.6216Z",fill:"url(#paint_linear)"}),(0,n.jsx)("defs",{children:(0,n.jsxs)("linearGradient",{id:"paint_linear",x1:"960",y1:"0",x2:"960",y2:"1102.82",gradientUnits:"userSpaceOnUse",children:[(0,n.jsx)("stop",{stopColor:"#F5F8FF"}),(0,n.jsx)("stop",{offset:"1",stopColor:"#F5F8FF",stopOpacity:"0"})]})})]})})},"elements.satellite-map":e=>{let{data:t}=e,{SatelliteMap:s,title:l,searchPlaceholder:i,sidebarCTA:r,sidebarCTALink:c,footnote:d}=t,o=(0,a.useRef)(null),m=(0,a.useRef)(null),[x,h]=(0,a.useState)(0),[p,u]=(0,a.useState)(""),b=(0,a.useRef)(null);(0,a.useEffect)(()=>{let e=setTimeout(()=>{if(p.length>=3){var e;null===(e=window.dataLayer)||void 0===e||e.push({event:"satellite_search",searchTerm:p})}},Number("500"));return()=>{clearTimeout(e)}},[p]);let f=s.filter(e=>"global"===e.category),w=[...f,...s.filter(e=>"country"===e.category).sort((e,t)=>"USA"===e.name?-1:"USA"===t.name?1:e.name.localeCompare(t.name))],N=()=>{if(p.length<3)return w;{let e=[...w.filter(e=>e.name.toLowerCase().includes(p.toLowerCase())),...w.filter(e=>{let t;return e.subregions&&(t=e.subregions[0].name.toLowerCase().includes(p.toLowerCase())),t})].sort((e,t)=>e.name.localeCompare(t.name));return 0==e.length?f:e}};(0,a.useEffect)(()=>{var e;m.current||(U.config.apiKey="O7VbOY3zrXxBupgrQtdE",m.current=new U.Map({container:"map",style:U.MapStyle.HYBRID,bounds:(null===(e=Q())||void 0===e?void 0:e.bounds)||null,center:[0,0],zoom:x}))},[x]),(0,a.useEffect)(()=>{m.current.on("zoom",function(){h(Math.ceil(100*m.current.getZoom())/100)})},[x]);let v=e=>{var t;let{bbox:s,name:n}=e,l=s.reduce((e,t,s,n)=>(s%2==0&&e.push([t,n[s+1]]),e),[]);m.current.fitBounds(l),null===(t=window.dataLayer)||void 0===t||t.push({event:"satellite_click",region_name:n})};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(j.Z,{className:"justify-content-center",children:(0,n.jsx)(g.Z,{className:"text-center my-2",children:(0,n.jsx)("h2",{id:"region-active",children:l})})}),(0,n.jsxs)(j.Z,{className:"shadow p-0",children:[(0,n.jsxs)(g.Z,{md:4,className:"bg-white m-0 p-0 satellite-showcase position-relative",children:[(0,n.jsx)("div",{className:"sticky-top z-1 pe-o5",children:(0,n.jsx)("input",{type:"text",className:"w-100 form-control m-o5",placeholder:i,value:p,onChange:e=>{let{value:t}=e.target;u(t)},onKeyDown:e=>{"Enter"===e.key&&(v(N()[0]),b.current.focus())}})}),N().map((e,t)=>{let{name:s,date:l,category:a,maxresolution:i}=e;return(0,n.jsx)("button",{className:"satellite-item w-100 p-0",ref:0===t?b:null,onClick:()=>{v(e)},onKeyDown:t=>{"Enter"===t.key&&v(e)},children:(0,n.jsx)(g.Z,{id:"satellite-region",className:"satellite-region",children:(0,n.jsxs)(g.Z,{id:"satellite-region-info",className:"satellite-region-info",children:[(0,n.jsxs)("div",{className:"d-flex justify-content-between",children:[(0,n.jsx)("h5",{className:"m-0 d-inline-block text-start",children:s}),(0,n.jsx)("span",{className:"text-nowrap text-dark",children:l})]}),(0,n.jsxs)("p",{className:"text-nowrap m-0 d-flex justify-content-left text-dark",children:["Max resolution: ",i]})]})})},a+s)}),(0,n.jsx)("div",{className:"rounded shadow w-100 z-1 p-1 sticky-bottom bg-white",children:(0,n.jsx)(Y.Z,{data:{type:"link",id:"satellite-cta",text:r,url:c,newTab:"_self"}})})]}),(0,n.jsxs)(g.Z,{className:"p-0 m-0 map-wrap",children:[(0,n.jsx)("div",{ref:o,className:"map active d-inline-block position-absolute",id:"map"}),(0,n.jsx)("div",{className:"bg-lighter m-2 ms-sm-2 p-1 rounded shadow my-sm-1 position-absolute bottom-0 left-0",children:(0,n.jsx)(X.Z,{children:(0,n.jsx)(X.Z.Check,{type:"switch",id:"custom-switch",label:"Show map style",defaultChecked:!0,className:"mb-0",onChange:e=>{var t,s;t=e.target.checked,null===(s=m.current)||void 0===s||s.setStyle(t?U.MapStyle.HYBRID:U.MapStyle.SATELLITE)}})})})]})]}),(0,n.jsx)(j.Z,{className:"align-items-center justify-content-center my-2",children:(0,n.jsx)(g.Z,{className:"d-inline p-0 m-0",children:(0,n.jsx)(y.Z,{className:"text-center",children:d})})})]})},"sections.step-card-row":e=>{let{data:t}=e;return(0,n.jsxs)(j.Z,{className:"justify-content-center text-center my-4 my-md-12",children:[t.title&&(0,n.jsx)(g.Z,{sm:12,className:"text-center mb-4",children:(0,n.jsx)("h2",{children:t.title})}),(0,n.jsx)(g.Z,{sm:12,children:(0,n.jsx)(j.Z,{className:"justify-content-around text-center mb-4",children:t.stepCard.map((e,t)=>{let{id:s,img:l,shadow:a,description:i}=e;return(0,n.jsx)(g.Z,{sm:8,lg:3,md:4,children:(0,n.jsxs)(j.Z,{children:[l&&l.data&&l.data.attributes&&(0,n.jsx)(g.Z,{xs:12,children:(0,n.jsx)(b.Z,{media:l,width:l.data.attributes.width,className:a?"rounded  shadow":"rounded"})}),(0,n.jsx)(g.Z,{xs:12,children:(0,n.jsx)("span",{className:"d-inline-block text-white px-2 py-1 mt-2 rounded-circle bg-primary h1",children:t+1})}),(0,n.jsx)(g.Z,{xs:12,children:(0,n.jsx)(y.Z,{children:i})})]})},s)})})})]})},"sections.customer-succes-stories":e=>{let{data:t}=e;return(0,n.jsxs)(j.Z,{className:"my-md-12 my-4 justify-content-center align-items-between",children:[t.title&&(0,n.jsx)(g.Z,{xs:12,className:"text-center",children:(0,n.jsx)("h2",{children:t.title})}),t.customerCard.map(e=>{let t="left"===e.imgPosition?"last":"first",s="left"===e.imgPosition?" pe-xs-0 ":" ps-xs-0 mx-md-2",l="left"===e.imgPosition?" mx-sm-2":" mx-sm-2 mx-md-0";return(0,n.jsx)(g.Z,{xs:{span:12},md:6,className:"align-items-center",children:(0,n.jsxs)(j.Z,{className:"my-2 align-items-center",children:[(0,n.jsxs)(g.Z,{xs:{span:12,order:t},sm:6,md:5,className:s,children:[e.logo.data?(0,n.jsx)(b.Z,{media:e.logo,height:e.logo.data.attributes.height,width:e.logo.data.attributes.width},e.logo.id):(0,n.jsx)("h2",{children:e.title}),(0,n.jsx)(y.Z,{children:e.description}),e.button&&e.button.map(e=>(0,n.jsx)(h.Z,{data:e},e.id))]}),(0,n.jsx)(g.Z,{xs:12,sm:5,className:"px-0 py-2 justify-content-center mt-2"+l,children:e.img.data&&(0,n.jsx)(b.Z,{className:"rounded shadow",media:e.img})})]})},e.id)})]})},"sections.logo-paragraph":e=>{let{data:t}=e;return(0,n.jsxs)(j.Z,{className:"justify-content-center align-items-center my-md-12 my-4 text-center text-sm-start",children:[(0,n.jsx)(g.Z,{sm:5,children:t.logo.data&&(0,n.jsx)(b.Z,{media:t.logo})}),(0,n.jsxs)(g.Z,{sm:7,className:"px-sm-0 ms-sm-n5 ms-lg-n8 ms-xl-n10",children:[(0,n.jsx)("h2",{children:t.title}),(0,n.jsx)(y.Z,{className:"big",children:t.description}),(0,n.jsx)(h.Z,{data:t.button,alignment:"justify-content-center justify-content-sm-start"},t.button.id)]})]})},"sections.cartographic-assets":e=>{let{data:t}=e;return(0,n.jsxs)(j.Z,{className:"mb-md-12 justify-content-between",children:[(0,n.jsx)(g.Z,{md:12,children:(0,n.jsx)("h2",{children:t.title})}),(0,n.jsxs)(g.Z,{sm:5,children:[(0,n.jsx)(y.Z,{className:"big",children:t.textLeft1}),t.imgL1?(0,n.jsx)(b.Z,{media:t.imgL1}):null,t.textLeft2?(0,n.jsx)(y.Z,{className:"big",children:t.textLeft2}):null,t.imgL2?(0,n.jsx)(b.Z,{media:t.imgL2}):null]}),(0,n.jsxs)(g.Z,{sm:5,children:[(0,n.jsx)(y.Z,{className:"big",children:t.textRight1}),t.imgR2?(0,n.jsx)(b.Z,{media:t.imgR1}):null,t.textRight2?(0,n.jsx)(y.Z,{className:"big",children:t.textRight2}):null,t.imgR2?(0,n.jsx)(b.Z,{media:t.imgR2}):null]})]})},"sections.image-galery":e=>{let{data:t}=e;return(0,n.jsxs)(j.Z,{className:"my-md-12 my-4",children:[(0,n.jsxs)(g.Z,{md:12,children:[(0,n.jsx)("h2",{children:t.title}),(0,n.jsx)(y.Z,{children:t.description})]}),(0,n.jsx)(g.Z,{md:5,children:(0,n.jsxs)(j.Z,{className:"justify-content-around",children:[(0,n.jsx)(g.Z,{sx:6,className:"pt-2 pt-md-0",children:t.imgLTL.data&&(0,n.jsx)(b.Z,{media:t.imgLTL,className:"rounded shadow"})}),(0,n.jsx)(g.Z,{sx:6,className:"pt-2 pt-md-0",children:t.imgLTR.data&&(0,n.jsx)(b.Z,{media:t.imgLTR,className:"rounded shadow"})}),(0,n.jsx)(g.Z,{md:12,className:"mt-3",children:t.imgLB.data&&(0,n.jsx)(b.Z,{media:t.imgLB,className:"rounded shadow"})})]})}),(0,n.jsx)(g.Z,{md:6,className:"pt-2 pt-md-0",children:t.imgR.data&&(0,n.jsx)(b.Z,{media:t.imgR,className:"rounded shadow"})})]})},"layout.story-navigation":e=>{let{data:t}=e;return(0,n.jsxs)(j.Z,{className:"mb-md-12 my-4 justify-content-center align-items-center",children:[(0,n.jsx)(g.Z,{md:6,children:(0,n.jsx)(w(),{href:t.linkL,children:(0,n.jsxs)(j.Z,{className:"justify-content-center align-items-center border-right border-light",children:[(0,n.jsx)(g.Z,{xs:4,children:t.imgL.data&&(0,n.jsx)(b.Z,{media:t.imgL})}),(0,n.jsx)(g.Z,{xs:6,children:(0,n.jsx)("h2",{children:(0,n.jsx)(y.Z,{children:t.textL})})})]})})}),(0,n.jsx)(g.Z,{md:6,children:t.linkR&&(0,n.jsx)(w(),{href:t.linkR,children:(0,n.jsxs)(j.Z,{className:"justify-content-center align-items-center",children:[(0,n.jsx)(g.Z,{xs:6,children:(0,n.jsx)("h2",{children:(0,n.jsx)(y.Z,{children:t.textR})})}),(0,n.jsx)(g.Z,{xs:4,children:t.imgR.data&&(0,n.jsx)(b.Z,{media:t.imgR})})]})})})]})},"sections.button-carousel":e=>{let{data:t}=e;return(0,n.jsxs)(j.Z,{className:"justify-content-center my-4 my-md-12",children:[t.title&&(0,n.jsx)(g.Z,{sm:12,className:"text-center",children:(0,n.jsx)("h2",{children:t.title})}),(0,n.jsxs)(g.Z,{sm:12,className:"position-relative",children:[(0,n.jsx)(ea,{side:"left"}),(0,n.jsx)(ea,{side:"right"}),(0,n.jsx)(er.tq,{loop:!0,navigation:{nextEl:"#slideright",prevEl:"#slideleft"},autoplay:{delay:4e3,disableOnInteraction:!0},slidesPerView:1,modules:[ei.W_],wrapperClass:"swiper-wrapper d-flex align-items-center",className:"py-2 py-sm-6",children:t.block.map(e=>(0,n.jsx)(er.o5,{children:(0,n.jsxs)(j.Z,{className:"align-items-center justify-content-around px-4",children:[(0,n.jsx)(g.Z,{md:7,children:(0,n.jsx)(b.Z,{media:e.img,className:"rounded shadow-sm my-1"})}),(0,n.jsxs)(g.Z,{md:4,children:[(0,n.jsx)(y.Z,{children:e.text}),e.button&&(0,n.jsx)(h.Z,{data:e.button})]})]})},e.id))})]})]})},"elements.engine-changelog":e=>{let{data:t}=e;return(0,n.jsx)(j.Z,{children:(0,n.jsxs)(g.Z,{lg:8,children:[(0,n.jsx)("h1",{children:t.title}),(0,n.jsx)("p",{children:t.description}),(0,n.jsx)("div",{className:"mt-4",children:(0,p.ZP)(t.changelog,z)})]})})},"elements.download-container":e=>{let{data:t}=e,{product:s,img:l,imgWindows:i,title:r,description:c,directDownloadLinks:d}=t,o=(0,a.useContext)(T.k),m="engine"===s?o.attributes.engineVersion:o.attributes.serverVersion,[h,p]=(0,a.useState)("");(0,a.useEffect)(()=>{p(M.BF)},[]);let[u,f]=(0,a.useState)(!1);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(x,{data:{size:"medium",horizontalPosition:"left",verticalPosition:"blockAfter",angle:"default",color:"gray"}}),(0,n.jsxs)(j.Z,{className:"bg-white  rounded shadow align-items-center justify-content-around my-4 mb-sm-15 py-4 px-gutter"+("server"===s?" bg-server-download":""),children:[(0,n.jsx)(g.Z,{xs:{span:12,order:"last"},md:{span:7,order:"first"},className:"d-flex align-items-center justify-content-center",children:(!i.data||h)&&(0,n.jsx)(b.Z,{media:"Windows"===h&&i.data?i:l})}),(0,n.jsxs)(g.Z,{md:"engine"===s?4:5,children:[(0,n.jsx)("h2",{children:r}),(0,n.jsx)("p",{children:c}),(0,n.jsxs)("p",{className:"small text-secondary fw-bold",children:["Version:",(0,n.jsx)("span",{className:"big text-secondary ps-o5",children:m})]}),(0,n.jsx)("p",{children:(0,n.jsxs)("a",{className:"btn btn-primary",role:"button",onClick:d?void 0:()=>f(!0),href:d?d[h]||d.default:void 0,children:["Download",(0,n.jsx)("span",{children:h&&["Windows","Mac OS","Linux"].includes(h)?" for ".concat(h):""})]})}),!d&&(0,n.jsx)(R,{show:u,setShow:f,detectedOS:h,product:s,productVersion:m}),(0,n.jsx)(y.Z,{children:t.footerLinks})]})]})]})},"elements.cloud-infrastructure":e=>{let{data:t}=e;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"row mt-sm-8",children:(0,n.jsx)("div",{className:"col-12 py-2",children:(0,n.jsx)("h1",{children:t.title})})}),(0,n.jsxs)("div",{className:"bg-white shadow rounded p-5 mb-sm-8",children:[t.boxTitle&&(0,n.jsx)("div",{className:"row justify-content-center text-center",children:(0,n.jsx)("div",{className:"col-sm-10 pb-0",children:(0,n.jsx)("h3",{children:t.boxTitle})})}),(0,n.jsx)("div",{className:"row",children:(0,n.jsxs)("div",{className:"col-12 pt-2",children:[t.map&&(0,n.jsx)(S,{data:t.map}),(0,n.jsx)("p",{className:"m-0 py-4 text-center",children:(0,n.jsx)("a",{className:"btn btn-primary",target:"_blank",rel:"noopener",href:t.statusURL,children:"See status page"})})]})}),(0,n.jsxs)("div",{className:"row py-2",children:[(0,n.jsx)("div",{className:"col-lg-4 big",children:(0,n.jsx)(y.Z,{children:t.firstCol})}),(0,n.jsx)("div",{className:"col-lg-4 big",children:(0,n.jsx)(y.Z,{children:t.secondCol})}),(0,n.jsx)("div",{className:"col-lg-4 big",children:(0,n.jsx)(y.Z,{children:t.thirdCol})})]})]})]})},"sections.pricing-cards":e=>{var t,s,l,i;let{data:r}=e,c=null!==(t=r.enginePricing)&&void 0!==t&&!!t.engine&&r.enginePricing,[d,o]=(0,a.useState)("annual"),x=e=>{o(e.target.value)},h=(0,D.useRouter)(),p=(h.query.key?"&key="+h.query.key:"")+(h.query.coupon?"&coupon="+h.query.coupon:"");return(0,n.jsxs)(n.Fragment,{children:[c&&(0,n.jsx)(j.Z,{children:(0,n.jsxs)(g.Z,{className:"text-center position-relative",children:[(0,n.jsxs)("div",{className:"switcher-pricing-desktop d-inline-block p-1",children:[(0,n.jsx)("input",{type:"radio",className:"btn-check switcher-desktop-pricing-check",onChange:x,name:"options",value:"monthly",id:"pricingMonth"}),(0,n.jsx)("label",{className:"switcher-desktop-btn py-o5 px-1",htmlFor:"pricingMonth",children:(null===(s=r.enginePricing.text)||void 0===s?void 0:s.monthly)?r.enginePricing.text.monthly:"monthly"}),(0,n.jsx)("input",{type:"radio",className:"btn-check switcher-desktop-pricing-check",onChange:x,name:"options",value:"annual",id:"pricingYear",defaultChecked:!0}),(0,n.jsx)("label",{className:"switcher-desktop-btn py-o5 px-1",htmlFor:"pricingYear",children:(null===(l=r.enginePricing.text)||void 0===l?void 0:l.annual)?r.enginePricing.text.annual:"annual"})]}),(0,n.jsx)("div",{className:"align-items-center bottom-0 d-inline-flex position-absolute top-0",children:(0,n.jsx)("span",{className:"bg-lighter text-primary p-1 rounded small fw-bolder",children:(null===(i=r.enginePricing.text)||void 0===i?void 0:i.discount)?r.enginePricing.text.discount:"Get over 70% discount"})})]})}),(0,n.jsx)(m,{y:"310px",x:"20px",scale:"2.56",angle:"0"}),(0,n.jsx)(j.Z,{id:r.anchorID?r.anchorID:"allPlans",className:"align-items-stretch my-2",md:2,xl:r.card.length,children:r.card.map((e,t)=>{var s;return(0,n.jsx)(g.Z,{className:"my-gutter",style:{minWidth:"292px"},children:(0,n.jsx)(O,{card:e,i:t,engineData:c,cardHighlight:null!==(s=e.highlighted)&&void 0!==s?s:2===t,engine:r.engine,geolayers:!1,selectedOption:d,engineQueryParam:p,month:r.month,year:r.year})},e.id)})})]})},"elements.cloud-requests":()=>{let[e,t]=(0,a.useState)(800),[s,l]=(0,a.useState)(600),[i,r]=(0,a.useState)(!1),[c,d]=(0,a.useState)(!1),[o,m]=(0,a.useState)(0),x=(0,a.useRef)(null),h=(0,D.useRouter)();return(0,a.useEffect)(()=>{let{width:e,height:s,raster:n,raster_size_512:a}=h.query;e&&t(parseInt(e)),s&&l(parseInt(s)),"1"===n&&r(!0),"1"===a&&d(!0)},[h.query]),(0,a.useEffect)(()=>{x.current||(U.config.apiKey="O7VbOY3zrXxBupgrQtdE",x.current=new U.Map({container:"map-requests",style:U.MapStyle.BASIC,center:[-.1275,51.507222],zoom:8,geolocateControl:!1,hash:!0}),x.current.showTileBoundaries=!0);let e=e=>{e.tile&&m(e=>e+1)};return x.current&&(i?x.current.setStyle({version:8,sources:{"raster-tiles":{type:"raster",url:"https://api.maptiler.com/maps/basic-v2".concat(c?"":"/256","/tiles.json"),tileSize:c?512:256}},layers:[{id:"simple-tiles",type:"raster",source:"raster-tiles",minzoom:0,maxzoom:22}]}):x.current.setStyle(U.MapStyle.BASIC),x.current.on("data",e)),()=>{x.current&&(m(0),x.current.off("data",e))}},[i,c]),(0,a.useEffect)(()=>{x.current&&(()=>{let t=x.current.getContainer();t&&(t.style.width="".concat(e,"px"),t.style.height="".concat(s,"px"),t.style.marginLeft="calc(50% - ".concat(e/2,"px)"),x.current.resize())})()},[e,s]),(0,a.useEffect)(()=>{(()=>{let t=new URLSearchParams;t.set("width",e.toString()),t.set("height",s.toString()),i&&(t.set("raster","1"),c&&t.set("raster_size_512","1")),window.history.replaceState({},"","?".concat(t.toString()).concat(window.location.hash))})()},[e,s,i,c]),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("div",{className:"row my-4",children:(0,n.jsxs)("div",{className:"col-lg-5",children:[(0,n.jsx)("h1",{children:"Request counter"}),(0,n.jsx)("p",{className:"big",children:"Zoom and move the map to get a better idea about the number of requests created by the map interaction."}),(0,n.jsxs)("h2",{className:"bigger",children:["Total tile requests:",(0,n.jsx)("span",{className:"b px-1",style:{fontSize:44},children:o})]})]})}),(0,n.jsxs)("div",{className:"row mt-4 mb-12 justify-content-center",children:[(0,n.jsx)("div",{className:"col-sm-12",children:(0,n.jsx)("div",{className:"rounded shadow",id:"map-requests"})}),(0,n.jsxs)("div",{className:"col-sm-12 text-center",children:[i&&(0,n.jsx)("p",{className:"my-0 mt-1 smaller",children:(0,n.jsx)("i",{children:"Note: Tile file size is only available for vector tiles"})}),(0,n.jsxs)("div",{className:"py-2 ms-n6",children:["Map size:\xa0",(0,n.jsx)("input",{className:"form-control w-auto d-inline-block",type:"number",value:e,min:50,max:2e3,onChange:e=>t(Number(e.target.value))}),"\xa0",(0,n.jsx)(_.Z,{name:"clear",width:24,height:24}),"\xa0",(0,n.jsx)("input",{className:"form-control w-auto d-inline-block",type:"number",value:s,min:50,max:2e3,onChange:e=>l(Number(e.target.value))})]}),(0,n.jsxs)("div",{className:"ms-11",children:[(0,n.jsxs)("div",{style:{display:"inline-block"},className:"form-check form-switch",children:[(0,n.jsx)("label",{className:"ms-n14",htmlFor:"raster",children:"Vector\xa0tiles"}),(0,n.jsx)("input",{type:"checkbox",className:"form-check-input",id:"raster",checked:i,onChange:e=>r(e.target.checked)}),(0,n.jsx)("label",{className:"form-check-label ms-6",htmlFor:"raster",children:"Raster\xa0tiles"})]}),(0,n.jsx)("br",{}),(0,n.jsxs)("div",{style:{display:i?"inline-block":"none"},id:"raster_size",className:"form-check form-switch",children:[(0,n.jsx)("label",{className:"ms-n14",htmlFor:"raster_size_512",children:"256x256px"}),(0,n.jsx)("input",{type:"checkbox",className:"form-check-input",id:"raster_size_512",checked:c,onChange:e=>d(e.target.checked)}),(0,n.jsx)("label",{className:"form-check-label ms-6",htmlFor:"raster_size_512",children:"512x512px"})]})]})]})]})]})},"elements.copyright-container":e=>{let{data:t}=e;return(0,n.jsx)("div",{className:"bg-gradient full-width py-8 my-md-12 my-4",children:(0,n.jsx)(P.Z,{fluid:"lg",children:(0,n.jsx)(j.Z,{className:"justify-content-center",children:(0,n.jsx)(g.Z,{md:9,children:(0,p.ZP)(t.copyright)})})})})},"sections.text-block":e=>{let{data:t}=e;return(0,n.jsxs)(j.Z,{className:"justify-content-center",children:[(0,n.jsx)(g.Z,{xs:12,children:(0,n.jsx)("h2",{children:t.title})}),t.textblock&&t.textblock.map(e=>(0,n.jsx)(g.Z,{children:(0,n.jsx)(y.Z,{children:e.text})},e.id))]})},"elements.press-index-container":e=>{let{data:t}=e,{downloadLink:s,pressData:l}=t;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(j.Z,{className:"mt-1",children:[(0,n.jsxs)(g.Z,{md:6,children:[(0,n.jsx)("h1",{children:"MapTiler Press room"}),(0,n.jsx)("p",{children:(0,n.jsx)("a",{href:"mailto:<EMAIL>",children:"<EMAIL>"})})]}),(0,n.jsx)(W.Z,{}),t.pressArticles.slice(0,2).map(e=>(0,n.jsx)(g.Z,{className:"my-4",md:6,children:(0,n.jsxs)(w(),{className:"text-body",href:"/news/"+e.attributes.slug,children:[(0,n.jsx)(b.Z,{className:"rounded shadow",media:e.attributes.thumbnail}),(0,n.jsx)("h3",{className:"mt-4",children:e.attributes.shortName}),(0,n.jsxs)("p",{children:["Published ",(0,K.WU)(new Date(e.attributes.date),"PPP")]}),(0,n.jsx)("p",{children:(0,V.vd)(e.attributes.content,30)})]})},e.id)),(0,n.jsx)(g.Z,{xs:12,className:"text-center my-2",children:(0,n.jsx)(h.Z,{data:t={url:"/press/archive",type:"link",text:"See the archive"}})})]}),(0,n.jsx)(j.Z,{className:"justify-content-center mt-4",children:(0,n.jsx)(g.Z,{sm:10,children:(0,n.jsxs)("div",{className:"bg-lighter p-3 text-center text-sm-start rounded d-flex align-items-center",children:[(0,n.jsx)(g.Z,{sm:3,children:(0,n.jsx)("img",{loading:"lazy",className:"h-auto w-80",src:"/img/icon/press.svg",alt:"Media kit"})}),(0,n.jsxs)(g.Z,{sm:6,children:[(0,n.jsx)("h2",{children:"Media kit"}),(0,n.jsx)("p",{className:"big",children:"All logos, images at one place."})]}),(0,n.jsxs)(g.Z,{sm:6,children:[(0,n.jsx)("a",{className:"btn btn-primary",target:"_blank",rel:"noopener",href:"https://drive.google.com/open?id=1lWBWtTI74IeNN4XyGXyR7jmMjCK5CHSa",children:"Open media kit"}),(0,n.jsx)("a",{children:(0,n.jsx)("p",{})}),(0,n.jsx)("a",{className:"btn btn-primary",target:"_blank",rel:"noopener",href:"https://vimeo.com/showcase/10528900/",children:"Open video kit"}),(0,n.jsx)("a",{})]})]})})}),(0,n.jsxs)(j.Z,{className:"justify-content-between",children:[(0,n.jsx)(g.Z,{xs:"12",children:(0,n.jsx)("h2",{id:"logo",className:"mt-6",children:"Logo"})}),l.logos.map((e,t)=>(0,n.jsxs)("div",{className:"col-sm-3",children:[(0,n.jsx)("a",{target:"_blank",rel:"noopener",href:"".concat(s+e.path,".png"),children:(0,n.jsx)("img",{loading:"lazy",className:"h-auto w-100 rounded border border-light",src:e.thumbnail,alt:e.label})}),(0,n.jsx)("p",{className:"my-2 mb-0 big text-secondary",children:e.label}),(0,n.jsxs)("p",{className:"my-2",children:["Download:\xa0",(0,n.jsx)("a",{target:"_blank",rel:"noopener",href:"".concat(s+e.path,".png"),children:"PNG"}),"\xa0/\xa0",(0,n.jsx)("a",{target:"_blank",rel:"noopener",href:"".concat(s+e.path,".pdf"),children:"PDF"})]})]},"".concat(e.thumbnail+t))),(0,n.jsx)(g.Z,{sm:12,children:(0,n.jsxs)("p",{className:"big d-flex justify-content-center align-items-center mt-4",children:[(0,n.jsx)("span",{children:"See more logos"}),(0,n.jsx)("a",{target:"_blank",rel:"noopener",href:"https://drive.google.com/open?id=1DwH4Jq5DpM_xkDOVFNtfdRYCzNxVZLQZ",className:"btn btn-primary ms-2",children:"Open in press kit"})]})})]}),(0,n.jsxs)(j.Z,{className:"justify-content-between",children:[(0,n.jsx)(g.Z,{xs:"12",children:(0,n.jsx)("h2",{id:"images",className:"mt-4",children:"Promo images"})}),l.images.map((e,t)=>(0,n.jsxs)("div",{className:"col-sm-3",children:[(0,n.jsx)("a",{target:"_blank",rel:"noopener",href:"".concat(s+e.path).concat(e.format?".png":".jpg"),children:(0,n.jsx)("img",{loading:"lazy",className:"h-auto w-100 rounded shadow",src:e.thumbnail,alt:e.label})}),(0,n.jsx)("p",{className:"my-2 mb-0 big text-secondary",children:e.label}),(0,n.jsxs)("p",{className:"my-2",children:["Download:\xa0",(0,n.jsxs)("a",{target:"_blank",rel:"noopener",href:"".concat(s+e.path).concat(e.format?".png":".jpg"),children:[e.format?"PNG":"JPG"," screen"]}),!1!=e.print&&(0,n.jsxs)(n.Fragment,{children:["\xa0/\xa0",(0,n.jsxs)("a",{target:"_blank",rel:"noopener",href:"".concat(s+e.path,"-print").concat(e.format?".png":".jpg"),children:[e.format?"PNG":"JPG"," print"]})]})]})]},"".concat(e.thumbnail+t))),(0,n.jsx)(g.Z,{sm:12,children:(0,n.jsxs)("p",{className:"big d-flex justify-content-center align-items-center mt-4",children:[(0,n.jsx)("span",{children:"See more images"}),(0,n.jsx)("a",{target:"_blank",rel:"noopener",href:"https://drive.google.com/open?id=1lmesa-4IXXzBTMsLRIw_rtlcntufaGw6",className:"btn btn-primary ms-2",children:"Open in press kit"})]})})]}),(0,n.jsxs)(j.Z,{className:"justify-content-between",children:[(0,n.jsx)(g.Z,{xs:"12",children:(0,n.jsx)("h2",{id:"videos",className:"mt-4",children:"Videos"})}),l.videos.map((e,t)=>(0,n.jsxs)("div",{className:"col-sm-3",children:[(0,n.jsx)("a",{target:"_blank",rel:"noopener",href:e.vimeo,children:(0,n.jsx)("img",{loading:"lazy",className:"w-100 h-auto rounded shadow",src:e.thumbnail,alt:e.label})}),(0,n.jsx)("p",{className:"my-2 mb-0 big text-secondary",children:e.label}),(0,n.jsxs)("p",{className:"my-2",children:["Download:\xa0",(0,n.jsx)("a",{target:"_blank",rel:"noopener",href:e.vimeo,children:"MP4"})]})]},"".concat(e.thumbnail+t))),(0,n.jsx)(g.Z,{sm:12,children:(0,n.jsxs)("p",{className:"big d-flex justify-content-center align-items-center mt-4",children:[(0,n.jsx)("span",{children:"See more videos"}),(0,n.jsx)("a",{target:"_blank",rel:"noopener",href:"https://vimeo.com/showcase/10528900/",className:"btn btn-primary ms-2",children:"Open in press kit"})]})})]}),(0,n.jsx)(j.Z,{className:"justify-content-center my-6",children:(0,n.jsx)(g.Z,{sm:10,children:(0,n.jsxs)("div",{className:"bg-lighter p-3 rounded  row align-items-center",children:[(0,n.jsx)(g.Z,{sm:3,children:(0,n.jsx)("img",{loading:"lazy",className:"h-auto w-100",src:"/img/icon/maptiler-in-box.svg",alt:"Design manual"})}),(0,n.jsxs)(g.Z,{sm:6,children:[(0,n.jsx)("h2",{children:"Design manual"}),(0,n.jsx)("p",{className:"big",children:"For more reference download our design manual."})]}),(0,n.jsx)(g.Z,{sm:3,children:(0,n.jsx)("a",{className:"btn btn-primary ",target:"_blank",rel:"noopener",href:"https://drive.google.com/open?id=1mGBSC8qrhegOFm2M88UaKtS82Q-jHui9",children:"Download"})})]})})})]})},"elements.press-archive-container":e=>{let{data:t}=e;return(0,n.jsxs)(j.Z,{className:"mt-1",children:[(0,n.jsxs)(g.Z,{md:6,children:[(0,n.jsx)("h1",{children:"MapTiler Press Archive"}),(0,n.jsx)("p",{children:(0,n.jsx)("a",{href:"mailto:<EMAIL>",children:"<EMAIL>"})})]}),(0,n.jsx)(W.Z,{}),(0,n.jsx)(g.Z,{xs:12,className:"mt-2",children:(0,n.jsx)(j.Z,{children:t.pressArticles.map(e=>(0,n.jsx)(G.Z,{small:!1,article:e,description:(0,V.vd)(e.attributes.content,30)},e.id))})})]})},"elements.search-container":J.Gh,"elements.tools-selector":ee.Z},eg=["components.wave-shape","sections.recent-news"];var eb=e=>{let{sections:t,articles:s,nav:a}=e;return(0,n.jsxs)(n.Fragment,{children:[a&&(0,n.jsx)(j.Z,{className:"justify-content-end",children:(0,n.jsx)(g.Z,{md:6,children:(0,n.jsx)(u.Z,{data:a})})}),t.map(e=>{let t=ej[e.__component];return t?(0,n.jsx)(N.SV,{fallbackRender:l.H,children:(0,n.jsx)(r,{animate:!(eg.includes(e.__component)||"elements.tools-selector"===e.__component&&"MapsReact"===e.tools),children:(0,n.jsx)(t,{data:{...e,title:(0,V.bu)(null==e?void 0:e.title)},articles:s})})},"".concat(e.__component).concat(e.id)):null})]})},ey=s(13484),ef=s(13342),ew=s(74781);let eN=e=>{let{sections:t,metadata:s,shape:l,articles:a,layoutSettings:i,layoutData:r,nav:c,altLangs:d}=e;return(null==t?void 0:t.length)?(0,n.jsxs)(ey.Z,{shape:l,settings:i,layoutData:r,altLangs:d,children:[(0,n.jsx)(ef.Z,{metadata:s}),(0,n.jsx)(eb,{sections:t,articles:a,nav:c})]}):(0,n.jsx)(ew.default,{statusCode:404,layoutData:r})}},10228:function(e,t,s){"use strict";var n=s(85893),l=s(2066),a=s(53280);t.Z=e=>{let{tags:t,date:s}=e;return(0,n.jsxs)("p",{className:"d-flex align-items-center",children:[(null==t?void 0:t.includes("update"))&&(0,n.jsx)(a.Z,{bg:"lighter",text:"secondary",className:"me-o8",children:"Update"}),"Published ",(0,l.WU)(new Date(s),"PPP")]})}},78864:function(e,t,s){"use strict";var n=s(85893);s(67294);var l=s(68070),a=s(21664);t.Z=()=>(0,n.jsxs)(l.Z,{sx:3,lg:6,className:"d-flex flex-wrap justify-content-lg-end align-items-center",children:[(0,n.jsx)("p",{className:"me-1 d-inline-flex",children:"Follow Us"}),(0,n.jsxs)("div",{className:"d-flex flex-wrap align-items-center",children:[(0,n.jsx)("a",{target:"_blank",rel:"noopener",className:"d-flex align-items-center my-o5",href:"https://x.com/MapTiler",children:(0,n.jsx)(a.Z,{social:"twitter colored"})}),(0,n.jsx)("a",{target:"_blank",rel:"noopener",className:"d-flex align-items-center my-o5",href:"https://bsky.app/profile/maptiler.bsky.social",children:(0,n.jsx)(a.Z,{social:"bsky colored"})}),(0,n.jsx)("a",{target:"_blank",rel:"noopener",className:"d-flex align-items-center my-o5",href:"https://www.linkedin.com/company/maptiler",children:(0,n.jsx)(a.Z,{social:"linkedin colored"})}),(0,n.jsx)("a",{target:"_blank",rel:"noopener",className:"d-flex align-items-center my-o5",href:"https://www.facebook.com/maptiler/",children:(0,n.jsx)(a.Z,{social:"facebook colored"})}),(0,n.jsx)("a",{target:"_blank",rel:"noopener",className:"d-flex align-items-center my-o5",href:"https://www.instagram.com/maptiler/",children:(0,n.jsx)(a.Z,{social:"instagram colored"})}),(0,n.jsx)("a",{target:"_blank",rel:"noopener",className:"d-flex align-items-center my-o5",href:"https://www.youtube.com/channel/UCubcQeWuBKvqpMu172CLEXw",children:(0,n.jsx)(a.Z,{social:"youtube colored"})}),(0,n.jsx)("a",{className:"d-flex align-items-center my-o5",href:"https://www.maptiler.com/news/feed/posts.xml",children:(0,n.jsx)(a.Z,{social:"rss colored"})})]})]})},58917:function(e,t,s){"use strict";var n=s(85893),l=s(68070),a=s(19101),i=s(34155),r=s(41664),c=s.n(r),d=s(10228);t.Z=e=>{let{article:t,small:s,description:r}=e;return(0,n.jsx)(l.Z,{sm:12,className:"border-top border-gray py-1",children:(0,n.jsx)(c(),{href:"/news/"+t.attributes.slug,children:(0,n.jsxs)(a.Z,{className:"align-items-center justify-content-between",children:[(0,n.jsx)(l.Z,{md:5,lg:s?2:3,xs:s?{order:"last"}:null,children:(0,n.jsx)(i.Z,{className:"rounded shadow",media:t.attributes.thumbnail})}),(0,n.jsxs)(l.Z,{md:7,lg:9,className:"text-body",children:[(0,n.jsx)("h3",{children:t.attributes.shortName}),(0,n.jsx)(d.Z,{tags:t.tags,date:t.attributes.date}),s?null:(0,n.jsx)("p",{children:r})]})]})})},t.id)}},43147:function(e,t,s){"use strict";s.d(t,{IG:function(){return q},Gh:function(){return R},Ef:function(){return F},Jw:function(){return T}});var n=s(85893),l=s(32608),a=s(39424),i=s(11163),r=s(67294),c=s(26346),d=s(19101),o=s(68070),m=s(24585),x=s(54942),h=s(2684),p=s(31089),u=s(58685),j=s(2417),g=s(34155),b=s(41664),y=s.n(b),f=s(81806),w=s(2066),N=s(50012),v=s(51204),Z=s(97449),C=s.n(Z);let k=e=>{let{children:t,...s}=e;return(0,n.jsx)(f.Z.Item,{className:C().breadCrumbItem,...s,children:t})},L=e=>{let{hit:t,renderContent:s}=e,l=t.fullUrl||t.url||"/news/".concat(t.slug),a=t.thumbnail||t.metaImage;return(0,n.jsx)(o.Z,{sm:12,className:"border-top border-gray py-1",children:(0,n.jsx)(y(),{href:l,children:(0,n.jsxs)(d.Z,{className:"align-items-center justify-content-between",children:[a&&(0,n.jsx)(o.Z,{md:5,lg:3,children:(0,n.jsx)(g.Z,{media:{data:{attributes:a}},sizes:"(max-width: 768px) 100vw, (max-width: 991px) 33vw, 270px",className:"rounded shadow w-100"})}),(0,n.jsx)(o.Z,{md:a&&7,lg:a&&9,className:"text-body",children:s(t)})]})})})},S=e=>{let{hit:t}=e;return(0,n.jsx)(L,{hit:t,renderContent:e=>(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("h3",{children:(0,n.jsx)(N.y,{attribute:"metaTitle",hit:e})}),(0,n.jsxs)("p",{children:["https://www.maptiler.com",(0,n.jsx)("wbr",{}),(0,n.jsx)(N.y,{attribute:"fullUrl",hit:e})]}),(0,n.jsx)("p",{children:(0,n.jsx)(v.p,{attribute:"contentSections",hit:e})})]})})};var I=s(37386),B=s(41686);B.ZP.button.withConfig({componentId:"sc-7a89a5f1-0"})(["background:none;border:none;outline:none;position:absolute;left:0;z-index:10;"]);let E=B.ZP.input.withConfig({componentId:"sc-7a89a5f1-1"})(["border:1px solid transparent;outline:none;box-shadow:0px 2px 8px 0px rgba(51,51,89,0.15);padding:8px 8px 8px 60px;border-radius:4px;width:310px;@media (max-width:767.98px){width:100%;}&:focus{border:1px solid $primary;}"]);B.ZP.form.withConfig({componentId:"sc-7a89a5f1-2"})(["position:relative;display:block;"]);var _=s(67221);let U=e=>{var t;let{placeholder:s}=e,{refine:l}=(0,I.l)(),a=(0,i.useRouter)(),c=a.asPath.split("?")[0],m=null===(t=a.query.q)||void 0===t?void 0:t.toString(),[x,h]=(0,r.useState)(m||""),p=(0,r.useRef)(null);return(0,r.useEffect)(()=>{void 0!==m&&(h(m),m.length>=1?l(m):l(""))},[m,l]),(0,n.jsx)(d.Z,{className:"justify-content-center",children:(0,n.jsx)(o.Z,{children:(0,n.jsxs)("div",{className:"position-relative my-2",children:[(0,n.jsx)(E,{autoFocus:!0,type:"text",className:"w-100 pe-4",value:x,onChange:e=>{let t=e.target.value;h(t),p.current&&clearTimeout(p.current),p.current=setTimeout(()=>{a.replace({pathname:c,query:{index:a.query.index||"page",q:t}},void 0,{shallow:!0})},Number("500"))},placeholder:s,minLength:1}),(0,n.jsx)(_.Z,{className:"ms-1 mt-o5 position-absolute start-0 bottom-0 top-0 text-secondary",name:"search",width:30,height:30}),(0,n.jsx)("a",{type:"button",onClick:()=>{h(""),a.replace({pathname:c,query:{index:a.query.index||"page",q:void 0}},void 0,{shallow:!0})},className:x?"":"d-none",children:(0,n.jsx)(_.Z,{className:"me-1 mt-o5 position-absolute end-0 bottom-0 top-0 text-body",name:"clear",width:20,height:31})})]})})})};var D=s(58117);let P={news:e=>{let{hit:t}=e;return(0,n.jsx)(L,{hit:t,renderContent:e=>(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("h3",{children:(0,n.jsx)(N.y,{attribute:"shortName",hit:e})}),e.date&&(0,n.jsxs)("p",{children:["Published ",(0,w.WU)(e.date,"PPP")]}),(0,n.jsx)("p",{children:(0,n.jsx)(v.p,{attribute:"content",hit:e})})]})})},"docs-v2":e=>{let{hit:t}=e;return(0,n.jsx)(L,{hit:t,renderContent:e=>(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("h3",{className:"mb-0",children:(0,n.jsx)(N.y,{attribute:"hierarchy_lvl5",hit:e})}),(0,n.jsx)(f.Z,{className:"mb-0 small mt-1 text-body",children:[void 0,void 0,void 0,void 0,void 0].map((t,s)=>{let l=e["hierarchy_lvl".concat(s)];return l&&l!==e.hierarchy_lvl5?(0,n.jsx)(k,{linkAs:"span",children:l},s):null})}),(0,n.jsx)("p",{className:"mt-1 text-body",children:(0,n.jsx)(v.p,{attribute:"content",hit:e})})]})})},page:S},M=e=>Object.keys(P).find(t=>!Array.isArray(e)&&t.startsWith(e))||"page",{searchClient:T}=(0,l.instantMeiliSearch)("https://cms-search.maptiler.com/","9fb24e5744dd482866abc158812fb797a6bd9c883e727fab79f3b5bb8d85606d",{placeholderSearch:!1,meiliSearchParams:{attributesToCrop:["contentSections","content:45"],cropLength:24}}),F=e=>{let{data:t,query:s}=e,{status:l,error:i}=(0,x.b)(),{items:r}=(0,h.O)(),{noResultMessage:d,loadingMessage:o}=t;return"loading"===l||"stalled"===l?(0,n.jsxs)("div",{className:"text-center my-4",children:[(0,n.jsx)(c.Z,{className:"text-primary",animation:"border"}),(0,n.jsx)("h4",{children:o})]}):"error"===l?(a.Tb(i),(0,n.jsx)("h5",{className:"text-center my-4",children:(null==i?void 0:i.message)||"An error occurred while fetching results."})):0===r.length?(0,n.jsx)("h5",{className:"text-center my-4",children:(null==s?void 0:s.length)>=1?d:""}):null},q=e=>{let{currentIndex:t}=e,{items:s}=(0,h.O)(),l=P[t]||S;return(0,n.jsx)(n.Fragment,{children:s.map(e=>(0,n.jsx)(l,{hit:e},e.id))})},R=e=>{var t;let{data:s}=e,l=(0,i.useRouter)(),a=M(l.query.index),[c,x]=(0,r.useState)(a),{title:h,placeholder:g}=s;(0,r.useEffect)(()=>{l.query.index&&x(M(l.query.index))},[l.query.index,l.query.q]);let b=e=>{l.replace({pathname:l.asPath.split("?")[0],query:{index:e,q:l.query.q||void 0}},void 0,{shallow:!0})};return(0,n.jsx)(d.Z,{children:(0,n.jsxs)(o.Z,{children:[(0,n.jsx)("h1",{className:"mt-4",children:h}),(0,n.jsxs)(p.p,{indexName:a,searchClient:T,children:[(0,n.jsx)(u.T,{hitsPerPage:c.startsWith("docs")?30:10}),(0,n.jsx)(U,{placeholder:g}),(0,n.jsx)(d.Z,{children:(0,n.jsxs)(m.Z,{className:"col-md-6 my-2",activeKey:c.split("-")[0],children:[(0,n.jsx)(m.Z.Item,{onClick:()=>b("page"),children:(0,n.jsx)(D.Z,{eventKey:"page",href:null,theme:"dark",children:"General"})}),(0,n.jsx)(m.Z.Item,{onClick:()=>b("news"),children:(0,n.jsx)(D.Z,{eventKey:"news",href:null,theme:"dark",children:"News"})}),(0,n.jsx)(m.Z.Item,{onClick:()=>b("docs"),children:(0,n.jsx)(D.Z,{eventKey:"docs",href:null,theme:"dark",target:"_blank",children:"Documentation"})}),(0,n.jsx)(m.Z.Item,{children:(0,n.jsx)(D.Z,{eventKey:"videos",href:l.query.q?"https://www.youtube.com/@maptilerofficial/search?query="+l.query.q:"https://www.youtube.com/@maptilerofficial/featured",theme:"dark",target:"_blank",children:(0,n.jsxs)(n.Fragment,{children:["Videos",(0,n.jsx)(_.Z,{name:"launch",width:20,height:20,className:"svg-text align-text-top ps-o5"})]})})})]})}),(0,n.jsx)(F,{data:s,query:null===(t=l.query.q)||void 0===t?void 0:t.toString()}),(0,n.jsx)(j.g,{indexName:c,children:(0,n.jsx)(q,{currentIndex:c})})]})]})})}},85829:function(e,t,s){"use strict";var n=s(85893),l=s(67294);let a=(0,l.lazy)(()=>Promise.all([s.e(2887),s.e(2644),s.e(3751),s.e(1876),s.e(1426),s.e(3680)]).then(s.bind(s,17837))),i=(0,l.lazy)(()=>Promise.all([s.e(2887),s.e(4228),s.e(2644),s.e(3751),s.e(3644),s.e(1876),s.e(1426),s.e(8999),s.e(4924),s.e(6773),s.e(6760)]).then(s.bind(s,3714))),r=(0,l.lazy)(()=>s.e(1920).then(s.bind(s,41920))),c=(0,l.lazy)(()=>Promise.all([s.e(2887),s.e(2644),s.e(3751),s.e(1876),s.e(1426),s.e(3732)]).then(s.bind(s,83732))),d=(0,l.lazy)(()=>Promise.all([s.e(2887),s.e(4228),s.e(2644),s.e(3751),s.e(3644),s.e(1876),s.e(1426),s.e(3381)]).then(s.bind(s,13381))),o=(0,l.lazy)(()=>Promise.all([s.e(7679),s.e(1876),s.e(8423),s.e(2854),s.e(5492)]).then(s.bind(s,65492))),m=(0,l.lazy)(()=>Promise.all([s.e(4228),s.e(2887),s.e(2644),s.e(3751),s.e(3644),s.e(8532),s.e(3646),s.e(1821),s.e(1876),s.e(1426),s.e(1408),s.e(2811)]).then(s.bind(s,62811))),x=(0,l.lazy)(()=>Promise.all([s.e(7679),s.e(8423),s.e(4574)]).then(s.bind(s,24574))),h=(0,l.lazy)(()=>s.e(9511).then(s.bind(s,9511))),p=(0,l.lazy)(()=>s.e(4744).then(s.bind(s,14744))),u=(0,l.lazy)(()=>Promise.all([s.e(4228),s.e(2887),s.e(2644),s.e(3751),s.e(3644),s.e(1876),s.e(1426),s.e(8999),s.e(4924),s.e(5728),s.e(4995),s.e(9703)]).then(s.bind(s,29703))),j=(0,l.lazy)(()=>Promise.all([s.e(2644),s.e(3751),s.e(1876),s.e(1426),s.e(4287)]).then(s.bind(s,4287))),g=(0,l.lazy)(()=>Promise.all([s.e(2644),s.e(3751),s.e(1876),s.e(1426),s.e(6685)]).then(s.bind(s,36685))),b=(0,l.lazy)(()=>Promise.all([s.e(4228),s.e(2887),s.e(2644),s.e(3751),s.e(3644),s.e(1876),s.e(1426),s.e(6820)]).then(s.bind(s,56820))),y=["MapViewer","EngineDrone","EngineOutput","EnginePrinted","EngineVector"];t.Z=e=>{let{data:t,navigationSettings:s}=e,{tools:f,mapViewerSettings:w}=t,N={mapID:"streets-v2",latitude:0,longitude:0,zoom:0,fullscreen:!1};return(0,n.jsxs)(l.Suspense,{fallback:(0,n.jsx)("div",{children:(0,n.jsx)("p",{className:"blink position-absolute top-50 start-50 text-center p-0 text-nowrap",children:"Loading ..."})}),children:[y.includes(f)&&(0,n.jsx)(a,{data:w||N,navbar:null==s?void 0:s.NavbarVisible}),"CloudStaticMapsGenerator"===f&&(0,n.jsx)(i,{}),"Upload"===f&&(0,n.jsx)(r,{}),"Coordinates"===f&&(0,n.jsx)(d,{data:w||N}),"Hillshading"===f&&(0,n.jsx)(c,{data:w||N,navbar:null==s?void 0:s.NavbarVisible}),"Hypsometry"===f&&(0,n.jsx)(o,{data:w||N,navbar:null==s?void 0:s.NavbarVisible}),"Weather3D"===f&&(0,n.jsx)(h,{}),"Orthophoto"===f&&(0,n.jsx)(p,{}),"Weather"===f&&(0,n.jsx)(m,{data:w||N}),"NlsPlacenames"===f&&(0,n.jsx)(x,{}),"MapsReact"===f&&(0,n.jsx)(u,{}),"HeatmapFlyTo"===f&&(0,n.jsx)(j,{}),"PointFilter"===f&&(0,n.jsx)(g,{}),"GeocodingDemo"===f&&(0,n.jsx)(b,{})]})}},98827:function(e,t,s){"use strict";s.d(t,{m:function(){return n}});let n=(e,t)=>{switch(e){case"top-bottom":return"my-4 my-md-12";case"bottom":return"mb-4 mb-md-12";case"top":return"mt-4 mt-md-12";case"none":return"";default:return t?n(t):"my-4 my-md-12"}}},37669:function(e,t,s){"use strict";function n(e){return e.replace(/<[^>]+>|&nbsp;/g," ")}function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:30;return n(e).split(" ").slice(0,t).join(" ")+"..."}s.d(t,{Iv:function(){return n},bu:function(){return a},vd:function(){return l}});let a=e=>null==e?void 0:e.replaceAll(/&break;/g,"\n").replaceAll(/&nbsp;/g,"\xa0")},97449:function(e){e.exports={breadCrumbItem:"styles_breadCrumbItem__0XkQ4"}},80278:function(e){e.exports={buttonContainer:"styles_buttonContainer__GmoiZ"}}}]);