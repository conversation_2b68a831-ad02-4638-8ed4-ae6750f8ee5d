try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="d892147e-cd35-416e-ace9-b78130d20820",e._sentryDebugIdIdentifier="sentry-dbid-d892147e-cd35-416e-ace9-b78130d20820")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9713],{17331:function(e){function t(){this._events=this._events||{},this._maxListeners=this._maxListeners||void 0}function r(e){return"function"==typeof e}function n(e){return"object"==typeof e&&null!==e}e.exports=t,t.prototype._events=void 0,t.prototype._maxListeners=void 0,t.defaultMaxListeners=10,t.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||isNaN(e))throw TypeError("n must be a positive number");return this._maxListeners=e,this},t.prototype.emit=function(e){var t,i,a,o,s,c;if(this._events||(this._events={}),"error"===e&&(!this._events.error||n(this._events.error)&&!this._events.error.length)){if(t=arguments[1],t instanceof Error)throw t;var u=Error('Uncaught, unspecified "error" event. ('+t+")");throw u.context=t,u}if(void 0===(i=this._events[e]))return!1;if(r(i))switch(arguments.length){case 1:i.call(this);break;case 2:i.call(this,arguments[1]);break;case 3:i.call(this,arguments[1],arguments[2]);break;default:o=Array.prototype.slice.call(arguments,1),i.apply(this,o)}else if(n(i))for(s=0,o=Array.prototype.slice.call(arguments,1),a=(c=i.slice()).length;s<a;s++)c[s].apply(this,o);return!0},t.prototype.addListener=function(e,i){var a;if(!r(i))throw TypeError("listener must be a function");return this._events||(this._events={}),this._events.newListener&&this.emit("newListener",e,r(i.listener)?i.listener:i),this._events[e]?n(this._events[e])?this._events[e].push(i):this._events[e]=[this._events[e],i]:this._events[e]=i,n(this._events[e])&&!this._events[e].warned&&(a=void 0===this._maxListeners?t.defaultMaxListeners:this._maxListeners)&&a>0&&this._events[e].length>a&&(this._events[e].warned=!0,console.error("(node) warning: possible EventEmitter memory leak detected. %d listeners added. Use emitter.setMaxListeners() to increase limit.",this._events[e].length),"function"==typeof console.trace&&console.trace()),this},t.prototype.on=t.prototype.addListener,t.prototype.once=function(e,t){if(!r(t))throw TypeError("listener must be a function");var n=!1;function i(){this.removeListener(e,i),n||(n=!0,t.apply(this,arguments))}return i.listener=t,this.on(e,i),this},t.prototype.removeListener=function(e,t){var i,a,o,s;if(!r(t))throw TypeError("listener must be a function");if(!this._events||!this._events[e])return this;if(o=(i=this._events[e]).length,a=-1,i===t||r(i.listener)&&i.listener===t)delete this._events[e],this._events.removeListener&&this.emit("removeListener",e,t);else if(n(i)){for(s=o;s-- >0;)if(i[s]===t||i[s].listener&&i[s].listener===t){a=s;break}if(a<0)return this;1===i.length?(i.length=0,delete this._events[e]):i.splice(a,1),this._events.removeListener&&this.emit("removeListener",e,t)}return this},t.prototype.removeAllListeners=function(e){var t,n;if(!this._events)return this;if(!this._events.removeListener)return 0==arguments.length?this._events={}:this._events[e]&&delete this._events[e],this;if(0==arguments.length){for(t in this._events)"removeListener"!==t&&this.removeAllListeners(t);return this.removeAllListeners("removeListener"),this._events={},this}if(r(n=this._events[e]))this.removeListener(e,n);else if(n)for(;n.length;)this.removeListener(e,n[n.length-1]);return delete this._events[e],this},t.prototype.listeners=function(e){return this._events&&this._events[e]?r(this._events[e])?[this._events[e]]:this._events[e].slice():[]},t.prototype.listenerCount=function(e){if(this._events){var t=this._events[e];if(r(t))return 1;if(t)return t.length}return 0},t.listenerCount=function(e,t){return e.listenerCount(t)}},8131:function(e,t,r){"use strict";var n=r(49374),i=r(67450),a=r(43650),o=r(17775),s=r(23076);function c(e,t,r,i){return new n(e,t,r,i)}c.version=r(24336),c.AlgoliaSearchHelper=n,c.SearchParameters=o,c.RecommendParameters=i,c.SearchResults=s,c.RecommendResults=a,e.exports=c},68078:function(e,t,r){"use strict";var n=r(17331);function i(e,t,r){this.main=e,this.fn=t,this.recommendFn=r,this.lastResults=null,this.lastRecommendResults=null}r(14853)(i,n),i.prototype.detach=function(){this.removeAllListeners(),this.main.detachDerivedHelper(this)},i.prototype.getModifiedState=function(e){return this.fn(e)},i.prototype.getModifiedRecommendState=function(e){return this.recommendFn(e)},e.exports=i},67450:function(e){"use strict";function t(e){e=e||{},this.params=e.params||[]}t.prototype={constructor:t,addParams:function(e){var r=this.params.slice();return r.push(e),new t({params:r})},removeParams:function(e){return new t({params:this.params.filter(function(t){return t.$$id!==e})})},addFrequentlyBoughtTogether:function(e){return this.addParams(Object.assign({},e,{model:"bought-together"}))},addRelatedProducts:function(e){return this.addParams(Object.assign({},e,{model:"related-products"}))},addTrendingItems:function(e){return this.addParams(Object.assign({},e,{model:"trending-items"}))},addTrendingFacets:function(e){return this.addParams(Object.assign({},e,{model:"trending-facets"}))},addLookingSimilar:function(e){return this.addParams(Object.assign({},e,{model:"looking-similar"}))},_buildQueries:function(e,t){return this.params.filter(function(e){return void 0===t[e.$$id]}).map(function(t){var r=Object.assign({},t,{indexName:e,threshold:t.threshold||0});return delete r.$$id,r})}},e.exports=t},43650:function(e){"use strict";function t(e,t){this._state=e,this._rawResults={};var r=this;e.params.forEach(function(e){var n=e.$$id;r[n]=t[n],r._rawResults[n]=t[n]})}t.prototype={constructor:t},e.exports=t},82437:function(e,t,r){"use strict";var n=r(52344),i=r(90116),a=r(49803),o={addRefinement:function(e,t,r){if(o.isRefined(e,t,r))return e;var i=""+r,a=e[t]?e[t].concat(i):[i],s={};return s[t]=a,n(s,e)},removeRefinement:function(e,t,r){if(void 0===r)return o.clearRefinement(e,function(e,r){return t===r});var n=""+r;return o.clearRefinement(e,function(e,r){return t===r&&n===e})},toggleRefinement:function(e,t,r){if(void 0===r)throw Error("toggleRefinement should be used with a value");return o.isRefined(e,t,r)?o.removeRefinement(e,t,r):o.addRefinement(e,t,r)},clearRefinement:function(e,t,r){if(void 0===t)return i(e)?{}:e;if("string"==typeof t)return a(e,[t]);if("function"==typeof t){var n=!1,o=Object.keys(e).reduce(function(i,a){var o=e[a]||[],s=o.filter(function(e){return!t(e,a,r)});return s.length!==o.length&&(n=!0),i[a]=s,i},{});return n?o:e}},isRefined:function(e,t,r){var n=!!e[t]&&e[t].length>0;return void 0!==r&&n?-1!==e[t].indexOf(""+r):n}};e.exports=o},17775:function(e,t,r){"use strict";var n=r(52344),i=r(7888),a=r(41190),o=r(60185),s=r(90116),c=r(49803),u=r(28023),l=r(46801),f=r(82437);function h(e,t){return Array.isArray(e)&&Array.isArray(t)?e.length===t.length&&e.every(function(e,r){return h(t[r],e)}):e===t}function d(e){var t=e?d._parseNumbers(e):{};void 0===t.userToken||l(t.userToken)||console.warn("[algoliasearch-helper] The `userToken` parameter is invalid. This can lead to wrong analytics.\n  - Format: [a-zA-Z0-9_-]{1,64}"),this.facets=t.facets||[],this.disjunctiveFacets=t.disjunctiveFacets||[],this.hierarchicalFacets=t.hierarchicalFacets||[],this.facetsRefinements=t.facetsRefinements||{},this.facetsExcludes=t.facetsExcludes||{},this.disjunctiveFacetsRefinements=t.disjunctiveFacetsRefinements||{},this.numericRefinements=t.numericRefinements||{},this.tagRefinements=t.tagRefinements||[],this.hierarchicalFacetsRefinements=t.hierarchicalFacetsRefinements||{};var r=this;Object.keys(t).forEach(function(e){var n=-1!==d.PARAMETERS.indexOf(e),i=void 0!==t[e];!n&&i&&(r[e]=t[e])})}d.PARAMETERS=Object.keys(new d),d._parseNumbers=function(e){if(e instanceof d)return e;var t={};if(["aroundPrecision","aroundRadius","getRankingInfo","minWordSizefor2Typos","minWordSizefor1Typo","page","maxValuesPerFacet","distinct","minimumAroundRadius","hitsPerPage","minProximity"].forEach(function(r){var n=e[r];if("string"==typeof n){var i=parseFloat(n);t[r]=isNaN(i)?n:i}}),Array.isArray(e.insideBoundingBox)&&(t.insideBoundingBox=e.insideBoundingBox.map(function(e){return Array.isArray(e)?e.map(function(e){return parseFloat(e)}):e})),e.numericRefinements){var r={};Object.keys(e.numericRefinements).forEach(function(t){var n=e.numericRefinements[t]||{};r[t]={},Object.keys(n).forEach(function(e){var i=n[e].map(function(e){return Array.isArray(e)?e.map(function(e){return"string"==typeof e?parseFloat(e):e}):"string"==typeof e?parseFloat(e):e});r[t][e]=i})}),t.numericRefinements=r}return o(e,t)},d.make=function(e){var t=new d(e);return(e.hierarchicalFacets||[]).forEach(function(e){if(e.rootPath){var r=t.getHierarchicalRefinement(e.name);r.length>0&&0!==r[0].indexOf(e.rootPath)&&(t=t.clearRefinements(e.name)),0===(r=t.getHierarchicalRefinement(e.name)).length&&(t=t.toggleHierarchicalFacetRefinement(e.name,e.rootPath))}}),t},d.validate=function(e,t){var r=t||{};return e.tagFilters&&r.tagRefinements&&r.tagRefinements.length>0?Error("[Tags] Cannot switch from the managed tag API to the advanced API. It is probably an error, if it is really what you want, you should first clear the tags with clearTags method."):e.tagRefinements.length>0&&r.tagFilters?Error("[Tags] Cannot switch from the advanced tag API to the managed API. It is probably an error, if it is not, you should first clear the tags with clearTags method."):e.numericFilters&&r.numericRefinements&&s(r.numericRefinements)?Error("[Numeric filters] Can't switch from the advanced to the managed API. It is probably an error, if this is really what you want, you have to first clear the numeric filters."):s(e.numericRefinements)&&r.numericFilters?Error("[Numeric filters] Can't switch from the managed API to the advanced. It is probably an error, if this is really what you want, you have to first clear the numeric filters."):null},d.prototype={constructor:d,clearRefinements:function(e){var t={numericRefinements:this._clearNumericRefinements(e),facetsRefinements:f.clearRefinement(this.facetsRefinements,e,"conjunctiveFacet"),facetsExcludes:f.clearRefinement(this.facetsExcludes,e,"exclude"),disjunctiveFacetsRefinements:f.clearRefinement(this.disjunctiveFacetsRefinements,e,"disjunctiveFacet"),hierarchicalFacetsRefinements:f.clearRefinement(this.hierarchicalFacetsRefinements,e,"hierarchicalFacet")};return t.numericRefinements===this.numericRefinements&&t.facetsRefinements===this.facetsRefinements&&t.facetsExcludes===this.facetsExcludes&&t.disjunctiveFacetsRefinements===this.disjunctiveFacetsRefinements&&t.hierarchicalFacetsRefinements===this.hierarchicalFacetsRefinements?this:this.setQueryParameters(t)},clearTags:function(){return void 0===this.tagFilters&&0===this.tagRefinements.length?this:this.setQueryParameters({tagFilters:void 0,tagRefinements:[]})},setIndex:function(e){return e===this.index?this:this.setQueryParameters({index:e})},setQuery:function(e){return e===this.query?this:this.setQueryParameters({query:e})},setPage:function(e){return e===this.page?this:this.setQueryParameters({page:e})},setFacets:function(e){return this.setQueryParameters({facets:e})},setDisjunctiveFacets:function(e){return this.setQueryParameters({disjunctiveFacets:e})},setHitsPerPage:function(e){return this.hitsPerPage===e?this:this.setQueryParameters({hitsPerPage:e})},setTypoTolerance:function(e){return this.typoTolerance===e?this:this.setQueryParameters({typoTolerance:e})},addNumericRefinement:function(e,t,r){var n=u(r);if(this.isNumericRefined(e,t,n))return this;var i=o({},this.numericRefinements);return i[e]=o({},i[e]),i[e][t]?(i[e][t]=i[e][t].slice(),i[e][t].push(n)):i[e][t]=[n],this.setQueryParameters({numericRefinements:i})},getConjunctiveRefinements:function(e){return this.isConjunctiveFacet(e)&&this.facetsRefinements[e]||[]},getDisjunctiveRefinements:function(e){return this.isDisjunctiveFacet(e)&&this.disjunctiveFacetsRefinements[e]||[]},getHierarchicalRefinement:function(e){return this.hierarchicalFacetsRefinements[e]||[]},getExcludeRefinements:function(e){return this.isConjunctiveFacet(e)&&this.facetsExcludes[e]||[]},removeNumericRefinement:function(e,t,r){return void 0!==r?this.isNumericRefined(e,t,r)?this.setQueryParameters({numericRefinements:this._clearNumericRefinements(function(n,i){return i===e&&n.op===t&&h(n.val,u(r))})}):this:void 0!==t?this.isNumericRefined(e,t)?this.setQueryParameters({numericRefinements:this._clearNumericRefinements(function(r,n){return n===e&&r.op===t})}):this:this.isNumericRefined(e)?this.setQueryParameters({numericRefinements:this._clearNumericRefinements(function(t,r){return r===e})}):this},getNumericRefinements:function(e){return this.numericRefinements[e]||{}},getNumericRefinement:function(e,t){return this.numericRefinements[e]&&this.numericRefinements[e][t]},_clearNumericRefinements:function(e){if(void 0===e)return s(this.numericRefinements)?{}:this.numericRefinements;if("string"==typeof e)return c(this.numericRefinements,[e]);if("function"==typeof e){var t=!1,r=this.numericRefinements,n=Object.keys(r).reduce(function(n,i){var a=r[i],o={};return Object.keys(a=a||{}).forEach(function(r){var n=a[r]||[],s=[];n.forEach(function(t){e({val:t,op:r},i,"numeric")||s.push(t)}),s.length!==n.length&&(t=!0),o[r]=s}),n[i]=o,n},{});return t?n:this.numericRefinements}},addFacet:function(e){return this.isConjunctiveFacet(e)?this:this.setQueryParameters({facets:this.facets.concat([e])})},addDisjunctiveFacet:function(e){return this.isDisjunctiveFacet(e)?this:this.setQueryParameters({disjunctiveFacets:this.disjunctiveFacets.concat([e])})},addHierarchicalFacet:function(e){if(this.isHierarchicalFacet(e.name))throw Error("Cannot declare two hierarchical facets with the same name: `"+e.name+"`");return this.setQueryParameters({hierarchicalFacets:this.hierarchicalFacets.concat([e])})},addFacetRefinement:function(e,t){if(!this.isConjunctiveFacet(e))throw Error(e+" is not defined in the facets attribute of the helper configuration");return f.isRefined(this.facetsRefinements,e,t)?this:this.setQueryParameters({facetsRefinements:f.addRefinement(this.facetsRefinements,e,t)})},addExcludeRefinement:function(e,t){if(!this.isConjunctiveFacet(e))throw Error(e+" is not defined in the facets attribute of the helper configuration");return f.isRefined(this.facetsExcludes,e,t)?this:this.setQueryParameters({facetsExcludes:f.addRefinement(this.facetsExcludes,e,t)})},addDisjunctiveFacetRefinement:function(e,t){if(!this.isDisjunctiveFacet(e))throw Error(e+" is not defined in the disjunctiveFacets attribute of the helper configuration");return f.isRefined(this.disjunctiveFacetsRefinements,e,t)?this:this.setQueryParameters({disjunctiveFacetsRefinements:f.addRefinement(this.disjunctiveFacetsRefinements,e,t)})},addTagRefinement:function(e){if(this.isTagRefined(e))return this;var t={tagRefinements:this.tagRefinements.concat(e)};return this.setQueryParameters(t)},removeFacet:function(e){return this.isConjunctiveFacet(e)?this.clearRefinements(e).setQueryParameters({facets:this.facets.filter(function(t){return t!==e})}):this},removeDisjunctiveFacet:function(e){return this.isDisjunctiveFacet(e)?this.clearRefinements(e).setQueryParameters({disjunctiveFacets:this.disjunctiveFacets.filter(function(t){return t!==e})}):this},removeHierarchicalFacet:function(e){return this.isHierarchicalFacet(e)?this.clearRefinements(e).setQueryParameters({hierarchicalFacets:this.hierarchicalFacets.filter(function(t){return t.name!==e})}):this},removeFacetRefinement:function(e,t){if(!this.isConjunctiveFacet(e))throw Error(e+" is not defined in the facets attribute of the helper configuration");return f.isRefined(this.facetsRefinements,e,t)?this.setQueryParameters({facetsRefinements:f.removeRefinement(this.facetsRefinements,e,t)}):this},removeExcludeRefinement:function(e,t){if(!this.isConjunctiveFacet(e))throw Error(e+" is not defined in the facets attribute of the helper configuration");return f.isRefined(this.facetsExcludes,e,t)?this.setQueryParameters({facetsExcludes:f.removeRefinement(this.facetsExcludes,e,t)}):this},removeDisjunctiveFacetRefinement:function(e,t){if(!this.isDisjunctiveFacet(e))throw Error(e+" is not defined in the disjunctiveFacets attribute of the helper configuration");return f.isRefined(this.disjunctiveFacetsRefinements,e,t)?this.setQueryParameters({disjunctiveFacetsRefinements:f.removeRefinement(this.disjunctiveFacetsRefinements,e,t)}):this},removeTagRefinement:function(e){if(!this.isTagRefined(e))return this;var t={tagRefinements:this.tagRefinements.filter(function(t){return t!==e})};return this.setQueryParameters(t)},toggleRefinement:function(e,t){return this.toggleFacetRefinement(e,t)},toggleFacetRefinement:function(e,t){if(this.isHierarchicalFacet(e))return this.toggleHierarchicalFacetRefinement(e,t);if(this.isConjunctiveFacet(e))return this.toggleConjunctiveFacetRefinement(e,t);if(this.isDisjunctiveFacet(e))return this.toggleDisjunctiveFacetRefinement(e,t);throw Error("Cannot refine the undeclared facet "+e+"; it should be added to the helper options facets, disjunctiveFacets or hierarchicalFacets")},toggleConjunctiveFacetRefinement:function(e,t){if(!this.isConjunctiveFacet(e))throw Error(e+" is not defined in the facets attribute of the helper configuration");return this.setQueryParameters({facetsRefinements:f.toggleRefinement(this.facetsRefinements,e,t)})},toggleExcludeFacetRefinement:function(e,t){if(!this.isConjunctiveFacet(e))throw Error(e+" is not defined in the facets attribute of the helper configuration");return this.setQueryParameters({facetsExcludes:f.toggleRefinement(this.facetsExcludes,e,t)})},toggleDisjunctiveFacetRefinement:function(e,t){if(!this.isDisjunctiveFacet(e))throw Error(e+" is not defined in the disjunctiveFacets attribute of the helper configuration");return this.setQueryParameters({disjunctiveFacetsRefinements:f.toggleRefinement(this.disjunctiveFacetsRefinements,e,t)})},toggleHierarchicalFacetRefinement:function(e,t){if(!this.isHierarchicalFacet(e))throw Error(e+" is not defined in the hierarchicalFacets attribute of the helper configuration");var r=this._getHierarchicalFacetSeparator(this.getHierarchicalFacetByName(e)),i={};return void 0!==this.hierarchicalFacetsRefinements[e]&&this.hierarchicalFacetsRefinements[e].length>0&&(this.hierarchicalFacetsRefinements[e][0]===t||0===this.hierarchicalFacetsRefinements[e][0].indexOf(t+r))?-1===t.indexOf(r)?i[e]=[]:i[e]=[t.slice(0,t.lastIndexOf(r))]:i[e]=[t],this.setQueryParameters({hierarchicalFacetsRefinements:n(i,this.hierarchicalFacetsRefinements)})},addHierarchicalFacetRefinement:function(e,t){if(this.isHierarchicalFacetRefined(e))throw Error(e+" is already refined.");if(!this.isHierarchicalFacet(e))throw Error(e+" is not defined in the hierarchicalFacets attribute of the helper configuration.");var r={};return r[e]=[t],this.setQueryParameters({hierarchicalFacetsRefinements:n(r,this.hierarchicalFacetsRefinements)})},removeHierarchicalFacetRefinement:function(e){if(!this.isHierarchicalFacetRefined(e))return this;var t={};return t[e]=[],this.setQueryParameters({hierarchicalFacetsRefinements:n(t,this.hierarchicalFacetsRefinements)})},toggleTagRefinement:function(e){return this.isTagRefined(e)?this.removeTagRefinement(e):this.addTagRefinement(e)},isDisjunctiveFacet:function(e){return this.disjunctiveFacets.indexOf(e)>-1},isHierarchicalFacet:function(e){return void 0!==this.getHierarchicalFacetByName(e)},isConjunctiveFacet:function(e){return this.facets.indexOf(e)>-1},isFacetRefined:function(e,t){return!!this.isConjunctiveFacet(e)&&f.isRefined(this.facetsRefinements,e,t)},isExcludeRefined:function(e,t){return!!this.isConjunctiveFacet(e)&&f.isRefined(this.facetsExcludes,e,t)},isDisjunctiveFacetRefined:function(e,t){return!!this.isDisjunctiveFacet(e)&&f.isRefined(this.disjunctiveFacetsRefinements,e,t)},isHierarchicalFacetRefined:function(e,t){if(!this.isHierarchicalFacet(e))return!1;var r=this.getHierarchicalRefinement(e);return t?-1!==r.indexOf(t):r.length>0},isNumericRefined:function(e,t,r){if(void 0===r&&void 0===t)return!!this.numericRefinements[e];var n=this.numericRefinements[e]&&void 0!==this.numericRefinements[e][t];if(void 0===r||!n)return n;var a=u(r),o=void 0!==i(this.numericRefinements[e][t],function(e){return h(e,a)});return n&&o},isTagRefined:function(e){return -1!==this.tagRefinements.indexOf(e)},getRefinedDisjunctiveFacets:function(){var e=this,t=a(Object.keys(this.numericRefinements).filter(function(t){return Object.keys(e.numericRefinements[t]).length>0}),this.disjunctiveFacets);return Object.keys(this.disjunctiveFacetsRefinements).filter(function(t){return e.disjunctiveFacetsRefinements[t].length>0}).concat(t).concat(this.getRefinedHierarchicalFacets()).sort()},getRefinedHierarchicalFacets:function(){var e=this;return a(this.hierarchicalFacets.map(function(e){return e.name}),Object.keys(this.hierarchicalFacetsRefinements).filter(function(t){return e.hierarchicalFacetsRefinements[t].length>0})).sort()},getUnrefinedDisjunctiveFacets:function(){var e=this.getRefinedDisjunctiveFacets();return this.disjunctiveFacets.filter(function(t){return -1===e.indexOf(t)})},managedParameters:["index","facets","disjunctiveFacets","facetsRefinements","hierarchicalFacets","facetsExcludes","disjunctiveFacetsRefinements","numericRefinements","tagRefinements","hierarchicalFacetsRefinements"],getQueryParams:function(){var e=this.managedParameters,t={},r=this;return Object.keys(this).forEach(function(n){var i=r[n];-1===e.indexOf(n)&&void 0!==i&&(t[n]=i)}),t},setQueryParameter:function(e,t){if(this[e]===t)return this;var r={};return r[e]=t,this.setQueryParameters(r)},setQueryParameters:function(e){if(!e)return this;var t=d.validate(this,e);if(t)throw t;var r=this,n=d._parseNumbers(e),i=Object.keys(this).reduce(function(e,t){return e[t]=r[t],e},{}),a=Object.keys(n).reduce(function(e,t){var r=void 0!==e[t],i=void 0!==n[t];return r&&!i?c(e,[t]):(i&&(e[t]=n[t]),e)},i);return new this.constructor(a)},resetPage:function(){return void 0===this.page?this:this.setPage(0)},_getHierarchicalFacetSortBy:function(e){return e.sortBy||["isRefined:desc","name:asc"]},_getHierarchicalFacetSeparator:function(e){return e.separator||" > "},_getHierarchicalRootPath:function(e){return e.rootPath||null},_getHierarchicalShowParentLevel:function(e){return"boolean"!=typeof e.showParentLevel||e.showParentLevel},getHierarchicalFacetByName:function(e){return i(this.hierarchicalFacets,function(t){return t.name===e})},getHierarchicalFacetBreadcrumb:function(e){if(!this.isHierarchicalFacet(e))return[];var t=this.getHierarchicalRefinement(e)[0];if(!t)return[];var r=this._getHierarchicalFacetSeparator(this.getHierarchicalFacetByName(e));return t.split(r).map(function(e){return e.trim()})},toString:function(){return JSON.stringify(this,null,2)}},e.exports=d},10210:function(e,t,r){"use strict";e.exports=function(e){return function(t,r){var n=e.hierarchicalFacets[r],u=e.hierarchicalFacetsRefinements[n.name]&&e.hierarchicalFacetsRefinements[n.name][0]||"",l=e._getHierarchicalFacetSeparator(n),f=e._getHierarchicalRootPath(n),h=e._getHierarchicalShowParentLevel(n),d=a(e._getHierarchicalFacetSortBy(n)),p=t.every(function(e){return e.exhaustive}),m=t;return f&&(m=t.slice(f.split(l).length)),m.reduce(function(e,t,r){var n=e;if(r>0){var a=0;for(n=e;a<r;)n=i(n&&Array.isArray(n.data)?n.data:[],function(e){return e.isRefined}),a++}if(n){var p=Object.keys(t.data).map(function(e){return[e,t.data[e]]}).filter(function(e){var t,r;return t=e[0],r=n.path||f,(!f||0===t.indexOf(f)&&f!==t)&&(!f&&-1===t.indexOf(l)||f&&t.split(l).length-f.split(l).length==1||-1===t.indexOf(l)&&-1===u.indexOf(l)||0===u.indexOf(t)||0===t.indexOf(r+l)&&(h||0===t.indexOf(u)))});n.data=o(p.map(function(e){var r,n,i,a,o=e[0];return r=e[1],n=c(u),i=t.exhaustive,{name:(a=o.split(l))[a.length-1].trim(),path:o,escapedValue:s(o),count:r,isRefined:n===o||0===n.indexOf(o+l),exhaustive:i,data:null}}),d[0],d[1])}return e},{name:e.hierarchicalFacets[r].name,count:null,isRefined:!0,path:null,escapedValue:null,exhaustive:p,data:null})}};var n=r(94039),i=r(7888),a=r(82293),o=r(42148),s=n.escapeFacetValue,c=n.unescapeFacetValue},23076:function(e,t,r){"use strict";var n=r(74587),i=r(52344),a=r(94039),o=r(7888),s=r(69725),c=r(82293),u=r(23034),l=r(42148),f=a.escapeFacetValue,h=a.unescapeFacetValue,d=r(10210);function p(e){var t={};return e.forEach(function(e,r){t[e]=r}),t}function m(e,t,r){t&&t[r]&&(e.stats=t[r])}function g(e,t,r){var a=t[0]||{};this._rawResults=t;var c=this;Object.keys(a).forEach(function(e){c[e]=a[e]});var l=i(r,{persistHierarchicalRootCount:!1});Object.keys(l).forEach(function(e){c[e]=l[e]}),this.processingTimeMS=t.reduce(function(e,t){return void 0===t.processingTimeMS?e:e+t.processingTimeMS},0),this.disjunctiveFacets=[],this.hierarchicalFacets=e.hierarchicalFacets.map(function(){return[]}),this.facets=[];var f=e.getRefinedDisjunctiveFacets(),g=p(e.facets),y=p(e.disjunctiveFacets),v=1,b=a.facets||{};Object.keys(b).forEach(function(t){var r=b[t],n=o(e.hierarchicalFacets,function(e){return(e.attributes||[]).indexOf(t)>-1});if(n){var i=n.attributes.indexOf(t),u=s(e.hierarchicalFacets,function(e){return e.name===n.name});c.hierarchicalFacets[u][i]={attribute:t,data:r,exhaustive:a.exhaustiveFacetsCount}}else{var l,f=-1!==e.disjunctiveFacets.indexOf(t),h=-1!==e.facets.indexOf(t);f&&(l=y[t],c.disjunctiveFacets[l]={name:t,data:r,exhaustive:a.exhaustiveFacetsCount},m(c.disjunctiveFacets[l],a.facets_stats,t)),h&&(l=g[t],c.facets[l]={name:t,data:r,exhaustive:a.exhaustiveFacetsCount},m(c.facets[l],a.facets_stats,t))}}),this.hierarchicalFacets=n(this.hierarchicalFacets),f.forEach(function(r){var n=t[v],o=n&&n.facets?n.facets:{},l=e.getHierarchicalFacetByName(r);Object.keys(o).forEach(function(t){var r,f=o[t];if(l){r=s(e.hierarchicalFacets,function(e){return e.name===l.name});var d=s(c.hierarchicalFacets[r],function(e){return e.attribute===t});if(-1===d)return;c.hierarchicalFacets[r][d].data=c.persistHierarchicalRootCount?u(c.hierarchicalFacets[r][d].data,f):i(f,c.hierarchicalFacets[r][d].data)}else{r=y[t];var p=a.facets&&a.facets[t]||{};c.disjunctiveFacets[r]={name:t,data:u(p,f),exhaustive:n.exhaustiveFacetsCount},m(c.disjunctiveFacets[r],n.facets_stats,t),e.disjunctiveFacetsRefinements[t]&&e.disjunctiveFacetsRefinements[t].forEach(function(n){!c.disjunctiveFacets[r].data[n]&&e.disjunctiveFacetsRefinements[t].indexOf(h(n))>-1&&(c.disjunctiveFacets[r].data[n]=0)})}}),v++}),e.getRefinedHierarchicalFacets().forEach(function(r){var n=e.getHierarchicalFacetByName(r),a=e._getHierarchicalFacetSeparator(n),o=e.getHierarchicalRefinement(r);0===o.length||o[0].split(a).length<2||t.slice(v).forEach(function(t){var r=t&&t.facets?t.facets:{};Object.keys(r).forEach(function(t){var u=r[t],l=s(e.hierarchicalFacets,function(e){return e.name===n.name}),f=s(c.hierarchicalFacets[l],function(e){return e.attribute===t});if(-1!==f){var h={};if(o.length>0&&!c.persistHierarchicalRootCount){var d=o[0].split(a)[0];h[d]=c.hierarchicalFacets[l][f].data[d]}c.hierarchicalFacets[l][f].data=i(h,u,c.hierarchicalFacets[l][f].data)}}),v++})}),Object.keys(e.facetsExcludes).forEach(function(t){var r=e.facetsExcludes[t],n=g[t];c.facets[n]={name:t,data:b[t],exhaustive:a.exhaustiveFacetsCount},r.forEach(function(e){c.facets[n]=c.facets[n]||{name:t},c.facets[n].data=c.facets[n].data||{},c.facets[n].data[e]=0})}),this.hierarchicalFacets=this.hierarchicalFacets.map(d(e)),this.facets=n(this.facets),this.disjunctiveFacets=n(this.disjunctiveFacets),this._state=e}function y(e,t){var r=o(e,function(e){return e.name===t});return r&&r.stats}function v(e,t,r,n,i){var a=o(i,function(e){return e.name===r}),s=a&&a.data&&a.data[n]?a.data[n]:0;return{type:t,attributeName:r,name:n,count:s,exhaustive:a&&a.exhaustive||!1}}g.prototype.getFacetByName=function(e){function t(t){return t.name===e}return o(this.facets,t)||o(this.disjunctiveFacets,t)||o(this.hierarchicalFacets,t)},g.DEFAULT_SORT=["isRefined:desc","count:desc","name:asc"],g.prototype.getFacetValues=function(e,t){var r,n=function(e,t){function r(e){return e.name===t}if(e._state.isConjunctiveFacet(t)){var n=o(e.facets,r);return n?Object.keys(n.data).map(function(r){var i=f(r);return{name:r,escapedValue:i,count:n.data[r],isRefined:e._state.isFacetRefined(t,i),isExcluded:e._state.isExcludeRefined(t,r)}}):[]}if(e._state.isDisjunctiveFacet(t)){var i=o(e.disjunctiveFacets,r);return i?Object.keys(i.data).map(function(r){var n=f(r);return{name:r,escapedValue:n,count:i.data[r],isRefined:e._state.isDisjunctiveFacetRefined(t,n)}}):[]}if(e._state.isHierarchicalFacet(t)){var a=o(e.hierarchicalFacets,r);if(!a)return a;var s=e._state.getHierarchicalFacetByName(t),c=e._state._getHierarchicalFacetSeparator(s),u=h(e._state.getHierarchicalRefinement(t)[0]||"");0===u.indexOf(s.rootPath)&&(u=u.replace(s.rootPath+c,""));var l=u.split(c);return l.unshift(t),function e(t,r,n){t.isRefined=t.name===(r[n]&&r[n].trim()),t.data&&t.data.forEach(function(t){e(t,r,n+1)})}(a,l,0),a}}(this,e);if(n){var a=i(t,{sortBy:g.DEFAULT_SORT,facetOrdering:!(t&&t.sortBy)}),s=this;return r=Array.isArray(n)?[e]:s._state.getHierarchicalFacetByName(n.name).attributes,function e(t,r,n,a){if(a=a||0,Array.isArray(r))return t(r,n[a]);if(!r.data||0===r.data.length)return r;var o=r.data.map(function(r){return e(t,r,n,a+1)});return i({data:t(o,n[a])},r)}(function(e,t){if(a.facetOrdering){var r,n,i,o,u,f,h,d=s.renderingContent&&s.renderingContent.facetOrdering&&s.renderingContent.facetOrdering.values&&s.renderingContent.facetOrdering.values[t];if(d)return i=[],o=[],u=d.hide||[],f=(d.order||[]).reduce(function(e,t,r){return e[t]=r,e},{}),e.forEach(function(e){var t=e.path||e.name,r=u.indexOf(t)>-1;r||void 0===f[t]?r||o.push(e):i[f[t]]=e}),i=i.filter(function(e){return e}),"hidden"===(h=d.sortRemainingBy)?i:(n="alpha"===h?[["path","name"],["asc","asc"]]:[["count"],["desc"]],i.concat(l(o,n[0],n[1])))}if(Array.isArray(a.sortBy)){var p=c(a.sortBy,g.DEFAULT_SORT);return l(e,p[0],p[1])}if("function"==typeof a.sortBy)return r=a.sortBy,e.sort(r);throw Error("options.sortBy is optional but if defined it must be either an array of string (predicates) or a sorting function")},n,r)}},g.prototype.getFacetStats=function(e){return this._state.isConjunctiveFacet(e)?y(this.facets,e):this._state.isDisjunctiveFacet(e)?y(this.disjunctiveFacets,e):void 0},g.prototype.getRefinements=function(){var e=this._state,t=this,r=[];return Object.keys(e.facetsRefinements).forEach(function(n){e.facetsRefinements[n].forEach(function(i){r.push(v(e,"facet",n,i,t.facets))})}),Object.keys(e.facetsExcludes).forEach(function(n){e.facetsExcludes[n].forEach(function(i){r.push(v(e,"exclude",n,i,t.facets))})}),Object.keys(e.disjunctiveFacetsRefinements).forEach(function(n){e.disjunctiveFacetsRefinements[n].forEach(function(i){r.push(v(e,"disjunctive",n,i,t.disjunctiveFacets))})}),Object.keys(e.hierarchicalFacetsRefinements).forEach(function(n){e.hierarchicalFacetsRefinements[n].forEach(function(i){var a,s,c,u,l,f,h,d;r.push((a=t.hierarchicalFacets,s=e.getHierarchicalFacetByName(n),c=e._getHierarchicalFacetSeparator(s),u=i.split(c),l=o(a,function(e){return e.name===n}),h=(f=u.reduce(function(e,t){var r=e&&o(e.data,function(e){return e.name===t});return void 0!==r?r:e},l))&&f.count||0,d=f&&f.exhaustive||!1,{type:"hierarchical",attributeName:n,name:f&&f.path||"",count:h,exhaustive:d}))})}),Object.keys(e.numericRefinements).forEach(function(t){var n=e.numericRefinements[t];Object.keys(n).forEach(function(e){n[e].forEach(function(n){r.push({type:"numeric",attributeName:t,name:n,numericValue:n,operator:e})})})}),e.tagRefinements.forEach(function(e){r.push({type:"tag",attributeName:"_tags",name:e})}),r},e.exports=g},49374:function(e,t,r){"use strict";var n=r(17331),i=r(68078),a=r(94039).escapeFacetValue,o=r(14853),s=r(60185),c=r(90116),u=r(49803),l=r(67450),f=r(43650),h=r(96394),d=r(17775),p=r(23076),m=r(97878),g=r(24336);function y(e,t,r,n){"function"==typeof e.addAlgoliaAgent&&e.addAlgoliaAgent("JS Helper ("+g+")"),this.setClient(e);var i=r||{};i.index=t,this.state=d.make(i),this.recommendState=new l({params:i.recommendState}),this.lastResults=null,this.lastRecommendResults=null,this._queryId=0,this._recommendQueryId=0,this._lastQueryIdReceived=-1,this._lastRecommendQueryIdReceived=-1,this.derivedHelpers=[],this._currentNbQueries=0,this._currentNbRecommendQueries=0,this._searchResultsOptions=n,this._recommendCache={}}function v(e){if(e<0)throw Error("Page requested below 0.");return this._change({state:this.state.setPage(e),isPageReset:!1}),this}function b(){return this.state.page}o(y,n),y.prototype.search=function(){return this._search({onlyWithDerivedHelpers:!1}),this},y.prototype.searchOnlyWithDerivedHelpers=function(){return this._search({onlyWithDerivedHelpers:!0}),this},y.prototype.searchWithComposition=function(){return this._runComposition({onlyWithDerivedHelpers:!0}),this},y.prototype.recommend=function(){return this._recommend(),this},y.prototype.getQuery=function(){var e=this.state;return h._getHitsSearchParams(e)},y.prototype.searchOnce=function(e,t){var r=e?this.state.setQueryParameters(e):this.state,n=h._getQueries(r.index,r),i=this;if(this._currentNbQueries++,this.emit("searchOnce",{state:r}),t){this.client.search(n).then(function(e){i._currentNbQueries--,0===i._currentNbQueries&&i.emit("searchQueueEmpty"),t(null,new p(r,e.results),r)}).catch(function(e){i._currentNbQueries--,0===i._currentNbQueries&&i.emit("searchQueueEmpty"),t(e,null,r)});return}return this.client.search(n).then(function(e){return i._currentNbQueries--,0===i._currentNbQueries&&i.emit("searchQueueEmpty"),{content:new p(r,e.results),state:r,_originalResponse:e}},function(e){throw i._currentNbQueries--,0===i._currentNbQueries&&i.emit("searchQueueEmpty"),e})},y.prototype.findAnswers=function(e){console.warn("[algoliasearch-helper] answers is no longer supported");var t=this.state,r=this.derivedHelpers[0];if(!r)return Promise.resolve([]);var n=r.getModifiedState(t),i=s({attributesForPrediction:e.attributesForPrediction,nbHits:e.nbHits},{params:u(h._getHitsSearchParams(n),["attributesToSnippet","hitsPerPage","restrictSearchableAttributes","snippetEllipsisText"])}),a="search for answers was called, but this client does not have a function client.initIndex(index).findAnswers";if("function"!=typeof this.client.initIndex)throw Error(a);var o=this.client.initIndex(n.index);if("function"!=typeof o.findAnswers)throw Error(a);return o.findAnswers(n.query,e.queryLanguages,i)},y.prototype.searchForFacetValues=function(e,t,r,n){var i,o="function"==typeof this.client.searchForFacetValues&&"function"!=typeof this.client.searchForFacets,s="function"==typeof this.client.initIndex;if(!o&&!s&&"function"!=typeof this.client.search)throw Error("search for facet values (searchable) was called, but this client does not have a function client.searchForFacetValues or client.initIndex(index).searchForFacetValues");var c=this.state.setQueryParameters(n||{}),u=c.isDisjunctiveFacet(e),l=h.getSearchForFacetQuery(e,t,r,c);this._currentNbQueries++;var f=this;return o?i=this.client.searchForFacetValues([{indexName:c.index,params:l}]):s?i=this.client.initIndex(c.index).searchForFacetValues(l):(delete l.facetName,i=this.client.search([{type:"facet",facet:e,indexName:c.index,params:l}]).then(function(e){return e.results[0]})),this.emit("searchForFacetValues",{state:c,facet:e,query:t}),i.then(function(t){return f._currentNbQueries--,0===f._currentNbQueries&&f.emit("searchQueueEmpty"),(t=Array.isArray(t)?t[0]:t).facetHits.forEach(function(t){t.escapedValue=a(t.value),t.isRefined=u?c.isDisjunctiveFacetRefined(e,t.escapedValue):c.isFacetRefined(e,t.escapedValue)}),t},function(e){throw f._currentNbQueries--,0===f._currentNbQueries&&f.emit("searchQueueEmpty"),e})},y.prototype.searchForCompositionFacetValues=function(e,t,r,n){if("function"!=typeof this.client.searchForFacetValues)throw Error("search for facet values (searchable) was called, but this client does not have a function client.searchForFacetValues");var i,o=this.state.setQueryParameters(n||{}),s=o.isDisjunctiveFacet(e);this._currentNbQueries++;var c=this;return i=this.client.searchForFacetValues({compositionID:o.index,facetName:e,searchForFacetValuesRequest:{params:{query:t,maxFacetHits:r,searchQuery:h._getCompositionHitsSearchParams(o)}}}),this.emit("searchForFacetValues",{state:o,facet:e,query:t}),i.then(function(t){return c._currentNbQueries--,0===c._currentNbQueries&&c.emit("searchQueueEmpty"),(t=t.results[0]).facetHits.forEach(function(t){t.escapedValue=a(t.value),t.isRefined=s?o.isDisjunctiveFacetRefined(e,t.escapedValue):o.isFacetRefined(e,t.escapedValue)}),t},function(e){throw c._currentNbQueries--,0===c._currentNbQueries&&c.emit("searchQueueEmpty"),e})},y.prototype.setQuery=function(e){return this._change({state:this.state.resetPage().setQuery(e),isPageReset:!0}),this},y.prototype.clearRefinements=function(e){return this._change({state:this.state.resetPage().clearRefinements(e),isPageReset:!0}),this},y.prototype.clearTags=function(){return this._change({state:this.state.resetPage().clearTags(),isPageReset:!0}),this},y.prototype.addDisjunctiveFacetRefinement=function(e,t){return this._change({state:this.state.resetPage().addDisjunctiveFacetRefinement(e,t),isPageReset:!0}),this},y.prototype.addDisjunctiveRefine=function(){return this.addDisjunctiveFacetRefinement.apply(this,arguments)},y.prototype.addHierarchicalFacetRefinement=function(e,t){return this._change({state:this.state.resetPage().addHierarchicalFacetRefinement(e,t),isPageReset:!0}),this},y.prototype.addNumericRefinement=function(e,t,r){return this._change({state:this.state.resetPage().addNumericRefinement(e,t,r),isPageReset:!0}),this},y.prototype.addFacetRefinement=function(e,t){return this._change({state:this.state.resetPage().addFacetRefinement(e,t),isPageReset:!0}),this},y.prototype.addRefine=function(){return this.addFacetRefinement.apply(this,arguments)},y.prototype.addFacetExclusion=function(e,t){return this._change({state:this.state.resetPage().addExcludeRefinement(e,t),isPageReset:!0}),this},y.prototype.addExclude=function(){return this.addFacetExclusion.apply(this,arguments)},y.prototype.addTag=function(e){return this._change({state:this.state.resetPage().addTagRefinement(e),isPageReset:!0}),this},y.prototype.addFrequentlyBoughtTogether=function(e){return this._recommendChange({state:this.recommendState.addFrequentlyBoughtTogether(e)}),this},y.prototype.addRelatedProducts=function(e){return this._recommendChange({state:this.recommendState.addRelatedProducts(e)}),this},y.prototype.addTrendingItems=function(e){return this._recommendChange({state:this.recommendState.addTrendingItems(e)}),this},y.prototype.addTrendingFacets=function(e){return this._recommendChange({state:this.recommendState.addTrendingFacets(e)}),this},y.prototype.addLookingSimilar=function(e){return this._recommendChange({state:this.recommendState.addLookingSimilar(e)}),this},y.prototype.removeNumericRefinement=function(e,t,r){return this._change({state:this.state.resetPage().removeNumericRefinement(e,t,r),isPageReset:!0}),this},y.prototype.removeDisjunctiveFacetRefinement=function(e,t){return this._change({state:this.state.resetPage().removeDisjunctiveFacetRefinement(e,t),isPageReset:!0}),this},y.prototype.removeDisjunctiveRefine=function(){return this.removeDisjunctiveFacetRefinement.apply(this,arguments)},y.prototype.removeHierarchicalFacetRefinement=function(e){return this._change({state:this.state.resetPage().removeHierarchicalFacetRefinement(e),isPageReset:!0}),this},y.prototype.removeFacetRefinement=function(e,t){return this._change({state:this.state.resetPage().removeFacetRefinement(e,t),isPageReset:!0}),this},y.prototype.removeRefine=function(){return this.removeFacetRefinement.apply(this,arguments)},y.prototype.removeFacetExclusion=function(e,t){return this._change({state:this.state.resetPage().removeExcludeRefinement(e,t),isPageReset:!0}),this},y.prototype.removeExclude=function(){return this.removeFacetExclusion.apply(this,arguments)},y.prototype.removeTag=function(e){return this._change({state:this.state.resetPage().removeTagRefinement(e),isPageReset:!0}),this},y.prototype.removeFrequentlyBoughtTogether=function(e){return this._recommendChange({state:this.recommendState.removeParams(e)}),this},y.prototype.removeRelatedProducts=function(e){return this._recommendChange({state:this.recommendState.removeParams(e)}),this},y.prototype.removeTrendingItems=function(e){return this._recommendChange({state:this.recommendState.removeParams(e)}),this},y.prototype.removeTrendingFacets=function(e){return this._recommendChange({state:this.recommendState.removeParams(e)}),this},y.prototype.removeLookingSimilar=function(e){return this._recommendChange({state:this.recommendState.removeParams(e)}),this},y.prototype.toggleFacetExclusion=function(e,t){return this._change({state:this.state.resetPage().toggleExcludeFacetRefinement(e,t),isPageReset:!0}),this},y.prototype.toggleExclude=function(){return this.toggleFacetExclusion.apply(this,arguments)},y.prototype.toggleRefinement=function(e,t){return this.toggleFacetRefinement(e,t)},y.prototype.toggleFacetRefinement=function(e,t){return this._change({state:this.state.resetPage().toggleFacetRefinement(e,t),isPageReset:!0}),this},y.prototype.toggleRefine=function(){return this.toggleFacetRefinement.apply(this,arguments)},y.prototype.toggleTag=function(e){return this._change({state:this.state.resetPage().toggleTagRefinement(e),isPageReset:!0}),this},y.prototype.nextPage=function(){var e=this.state.page||0;return this.setPage(e+1)},y.prototype.previousPage=function(){var e=this.state.page||0;return this.setPage(e-1)},y.prototype.setCurrentPage=v,y.prototype.setPage=v,y.prototype.setIndex=function(e){return this._change({state:this.state.resetPage().setIndex(e),isPageReset:!0}),this},y.prototype.setQueryParameter=function(e,t){return this._change({state:this.state.resetPage().setQueryParameter(e,t),isPageReset:!0}),this},y.prototype.setState=function(e){return this._change({state:d.make(e),isPageReset:!1}),this},y.prototype.overrideStateWithoutTriggeringChangeEvent=function(e){return this.state=new d(e),this},y.prototype.hasRefinements=function(e){return!!c(this.state.getNumericRefinements(e))||(this.state.isConjunctiveFacet(e)?this.state.isFacetRefined(e):this.state.isDisjunctiveFacet(e)?this.state.isDisjunctiveFacetRefined(e):!!this.state.isHierarchicalFacet(e)&&this.state.isHierarchicalFacetRefined(e))},y.prototype.isExcluded=function(e,t){return this.state.isExcludeRefined(e,t)},y.prototype.isDisjunctiveRefined=function(e,t){return this.state.isDisjunctiveFacetRefined(e,t)},y.prototype.hasTag=function(e){return this.state.isTagRefined(e)},y.prototype.isTagRefined=function(){return this.hasTagRefinements.apply(this,arguments)},y.prototype.getIndex=function(){return this.state.index},y.prototype.getCurrentPage=b,y.prototype.getPage=b,y.prototype.getTags=function(){return this.state.tagRefinements},y.prototype.getRefinements=function(e){var t=[];this.state.isConjunctiveFacet(e)?(this.state.getConjunctiveRefinements(e).forEach(function(e){t.push({value:e,type:"conjunctive"})}),this.state.getExcludeRefinements(e).forEach(function(e){t.push({value:e,type:"exclude"})})):this.state.isDisjunctiveFacet(e)&&this.state.getDisjunctiveRefinements(e).forEach(function(e){t.push({value:e,type:"disjunctive"})});var r=this.state.getNumericRefinements(e);return Object.keys(r).forEach(function(e){var n=r[e];t.push({value:n,operator:e,type:"numeric"})}),t},y.prototype.getNumericRefinement=function(e,t){return this.state.getNumericRefinement(e,t)},y.prototype.getHierarchicalFacetBreadcrumb=function(e){return this.state.getHierarchicalFacetBreadcrumb(e)},y.prototype._search=function(e){var t=this.state,r=[],n=[];e.onlyWithDerivedHelpers||(n=h._getQueries(t.index,t),r.push({state:t,queriesCount:n.length,helper:this}),this.emit("search",{state:t,results:this.lastResults}));var i=this.derivedHelpers.map(function(e){var n=e.getModifiedState(t),i=n.index?h._getQueries(n.index,n):[];return r.push({state:n,queriesCount:i.length,helper:e}),e.emit("search",{state:n,results:e.lastResults}),i}),a=Array.prototype.concat.apply(n,i),o=this._queryId++;if(this._currentNbQueries++,!a.length)return Promise.resolve({results:[]}).then(this._dispatchAlgoliaResponse.bind(this,r,o));try{this.client.search(a).then(this._dispatchAlgoliaResponse.bind(this,r,o)).catch(this._dispatchAlgoliaError.bind(this,o))}catch(e){this.emit("error",{error:e})}},y.prototype._runComposition=function(){var e=this.state,t=[],r=this.derivedHelpers.map(function(r){var n=r.getModifiedState(e),i=h._getCompositionQueries(n);return t.push({state:n,queriesCount:i.length,helper:r}),r.emit("search",{state:n,results:r.lastResults}),i}),n=Array.prototype.concat.apply([],r),i=this._queryId++;if(this._currentNbQueries++,!n.length)return Promise.resolve({results:[]}).then(this._dispatchAlgoliaResponse.bind(this,t,i));if(n.length>1)throw Error("Only one query is allowed when using a composition.");var a=n[0];try{this.client.search(a).then(this._dispatchAlgoliaResponse.bind(this,t,i)).catch(this._dispatchAlgoliaError.bind(this,i))}catch(e){this.emit("error",{error:e})}},y.prototype._recommend=function(){var e=this.state,t=this.recommendState,r=this.getIndex(),n=[{state:t,index:r,helper:this}],i=t.params.map(function(e){return e.$$id});this.emit("fetch",{recommend:{state:t,results:this.lastRecommendResults}});var a=this._recommendCache,o=this.derivedHelpers.map(function(t){var r=t.getModifiedState(e).index;if(!r)return[];var o=t.getModifiedRecommendState(new l);return n.push({state:o,index:r,helper:t}),i=Array.prototype.concat.apply(i,o.params.map(function(e){return e.$$id})),t.emit("fetch",{recommend:{state:o,results:t.lastRecommendResults}}),o._buildQueries(r,a)}),s=Array.prototype.concat.apply(this.recommendState._buildQueries(r,a),o);if(0!==s.length){if(s.length>0&&void 0===this.client.getRecommendations){console.warn("Please update algoliasearch/lite to the latest version in order to use recommend widgets.");return}var c=this._recommendQueryId++;this._currentNbRecommendQueries++;try{this.client.getRecommendations(s).then(this._dispatchRecommendResponse.bind(this,c,n,i)).catch(this._dispatchRecommendError.bind(this,c))}catch(e){this.emit("error",{error:e})}}},y.prototype._dispatchAlgoliaResponse=function(e,t,r){var n=this;if(!(t<this._lastQueryIdReceived)){this._currentNbQueries-=t-this._lastQueryIdReceived,this._lastQueryIdReceived=t,0===this._currentNbQueries&&this.emit("searchQueueEmpty");var i=r.results.slice(),a=Object.keys(r).reduce(function(e,t){return"results"!==t&&(e[t]=r[t]),e},{});Object.keys(a).length<=0&&(a=void 0),e.forEach(function(e){var t=e.state,r=e.queriesCount,o=e.helper,s=i.splice(0,r);if(!t.index){o.emit("result",{results:null,state:t});return}o.lastResults=new p(t,s,n._searchResultsOptions),void 0!==a&&(o.lastResults._rawContent=a),o.emit("result",{results:o.lastResults,state:t})})}},y.prototype._dispatchRecommendResponse=function(e,t,r,n){if(!(e<this._lastRecommendQueryIdReceived)){this._currentNbRecommendQueries-=e-this._lastRecommendQueryIdReceived,this._lastRecommendQueryIdReceived=e,0===this._currentNbRecommendQueries&&this.emit("recommendQueueEmpty");var i=this._recommendCache,a={};r.filter(function(e){return void 0===i[e]}).forEach(function(e,t){a[e]||(a[e]=[]),a[e].push(t)}),Object.keys(a).forEach(function(e){var t=a[e],r=n.results[t[0]];if(1===t.length){i[e]=r;return}i[e]=Object.assign({},r,{hits:m(t.map(function(e){return n.results[e].hits}))})});var o={};r.forEach(function(e){o[e]=i[e]}),t.forEach(function(e){var t=e.state,r=e.helper;if(!e.index){r.emit("recommend:result",{results:null,state:t});return}r.lastRecommendResults=new f(t,o),r.emit("recommend:result",{recommend:{results:r.lastRecommendResults,state:t}})})}},y.prototype._dispatchAlgoliaError=function(e,t){e<this._lastQueryIdReceived||(this._currentNbQueries-=e-this._lastQueryIdReceived,this._lastQueryIdReceived=e,this.emit("error",{error:t}),0===this._currentNbQueries&&this.emit("searchQueueEmpty"))},y.prototype._dispatchRecommendError=function(e,t){e<this._lastRecommendQueryIdReceived||(this._currentNbRecommendQueries-=e-this._lastRecommendQueryIdReceived,this._lastRecommendQueryIdReceived=e,this.emit("error",{error:t}),0===this._currentNbRecommendQueries&&this.emit("recommendQueueEmpty"))},y.prototype.containsRefinement=function(e,t,r,n){return e||0!==t.length||0!==r.length||0!==n.length},y.prototype._hasDisjunctiveRefinements=function(e){return this.state.disjunctiveRefinements[e]&&this.state.disjunctiveRefinements[e].length>0},y.prototype._change=function(e){var t=e.state,r=e.isPageReset;t!==this.state&&(this.state=t,this.emit("change",{state:this.state,results:this.lastResults,isPageReset:r}))},y.prototype._recommendChange=function(e){var t=e.state;t!==this.recommendState&&(this.recommendState=t,this.emit("recommend:change",{search:{results:this.lastResults,state:this.state},recommend:{results:this.lastRecommendResults,state:this.recommendState}}))},y.prototype.clearCache=function(){return this.client.clearCache&&this.client.clearCache(),this},y.prototype.setClient=function(e){return this.client===e||("function"==typeof e.addAlgoliaAgent&&e.addAlgoliaAgent("JS Helper ("+g+")"),this.client=e),this},y.prototype.getClient=function(){return this.client},y.prototype.derive=function(e,t){var r=new i(this,e,t);return this.derivedHelpers.push(r),r},y.prototype.detachDerivedHelper=function(e){var t=this.derivedHelpers.indexOf(e);if(-1===t)throw Error("Derived helper already detached");this.derivedHelpers.splice(t,1)},y.prototype.hasPendingRequests=function(){return this._currentNbQueries>0},e.exports=y},74587:function(e){"use strict";e.exports=function(e){return Array.isArray(e)?e.filter(Boolean):[]}},52344:function(e){"use strict";e.exports=function(){var e=Array.prototype.slice.call(arguments);return e.reduceRight(function(e,t){return Object.keys(Object(t)).forEach(function(r){void 0!==t[r]&&(void 0!==e[r]&&delete e[r],e[r]=t[r])}),e},{})}},94039:function(e){"use strict";e.exports={escapeFacetValue:function(e){return"string"!=typeof e?e:String(e).replace(/^-/,"\\-")},unescapeFacetValue:function(e){return"string"!=typeof e?e:e.replace(/^\\-/,"-")}}},7888:function(e){"use strict";e.exports=function(e,t){if(Array.isArray(e)){for(var r=0;r<e.length;r++)if(t(e[r]))return e[r]}}},69725:function(e){"use strict";e.exports=function(e,t){if(!Array.isArray(e))return -1;for(var r=0;r<e.length;r++)if(t(e[r]))return r;return -1}},7948:function(e){e.exports=function(e){return e.reduce(function(e,t){return e.concat(t)},[])}},82293:function(e,t,r){"use strict";var n=r(7888);e.exports=function(e,t){var r=(t||[]).map(function(e){return e.split(":")});return e.reduce(function(e,t){var i=t.split(":"),a=n(r,function(e){return e[0]===i[0]});return i.length>1||!a?(e[0].push(i[0]),e[1].push(i[1])):(e[0].push(a[0]),e[1].push(a[1])),e},[[],[]])}},14853:function(e){"use strict";e.exports=function(e,t){e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}},41190:function(e){"use strict";e.exports=function(e,t){return e.filter(function(r,n){return t.indexOf(r)>-1&&e.indexOf(r)===n})}},60185:function(e){"use strict";function t(e){return"function"==typeof e||Array.isArray(e)||"[object Object]"===Object.prototype.toString.call(e)}e.exports=function(e){t(e)||(e={});for(var r=1,n=arguments.length;r<n;r++){var i=arguments[r];t(i)&&function e(r,n){if(r===n)return r;for(var i in n)if(Object.prototype.hasOwnProperty.call(n,i)&&"__proto__"!==i&&"constructor"!==i){var a=n[i],o=r[i];(void 0===o||void 0!==a)&&(t(o)&&t(a)?r[i]=e(o,a):r[i]="object"==typeof a&&null!==a?e(Array.isArray(a)?[]:{},a):a)}return r}(e,i)}return e}},23034:function(e){"use strict";e.exports=function(){var e=Array.prototype.slice.call(arguments);return e.reduceRight(function(e,t){return Object.keys(Object(t)).forEach(function(r){var n="number"==typeof e[r]?e[r]:0,i=t[r];void 0!==i&&i>=n&&(void 0!==e[r]&&delete e[r],e[r]=i)}),e},{})}},90116:function(e){"use strict";e.exports=function(e){return e&&Object.keys(e).length>0}},49803:function(e){"use strict";e.exports=function(e,t){if(null===e)return{};var r,n,i={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}},42148:function(e){"use strict";e.exports=function(e,t,r){if(!Array.isArray(e))return[];Array.isArray(r)||(r=[]);var n=e.map(function(e,r){return{criteria:t.map(function(t){return e[t]}),index:r,value:e}});return n.sort(function(e,t){for(var n=-1;++n<e.criteria.length;){var i=function(e,t){if(e!==t){var r=void 0!==e,n=null===e,i=void 0!==t,a=null===t;if(!a&&e>t||n&&i||!r)return 1;if(!n&&e<t||a&&r||!i)return -1}return 0}(e.criteria[n],t.criteria[n]);if(i){if(n>=r.length)return i;if("desc"===r[n])return-i;return i}}return e.index-t.index}),n.map(function(e){return e.value})}},28023:function(e){"use strict";e.exports=function e(t){if("number"==typeof t)return t;if("string"==typeof t)return parseFloat(t);if(Array.isArray(t))return t.map(e);throw Error("The value should be a number, a parsable string or an array of those.")}},96394:function(e,t,r){"use strict";var n=r(60185);function i(e){return Object.keys(e).sort().reduce(function(t,r){return t[r]=e[r],t},{})}var a={_getQueries:function(e,t){var r=[];return r.push({indexName:e,params:a._getHitsSearchParams(t)}),t.getRefinedDisjunctiveFacets().forEach(function(n){r.push({indexName:e,params:a._getDisjunctiveFacetSearchParams(t,n)})}),t.getRefinedHierarchicalFacets().forEach(function(n){var i=t.getHierarchicalFacetByName(n),o=t.getHierarchicalRefinement(n),s=t._getHierarchicalFacetSeparator(i);if(o.length>0&&o[0].split(s).length>1){var c=o[0].split(s).slice(0,-1).reduce(function(e,t,r){return e.concat({attribute:i.attributes[r],value:0===r?t:[e[e.length-1].value,t].join(s)})},[]);c.forEach(function(n,o){var s=a._getDisjunctiveFacetSearchParams(t,n.attribute,0===o);function u(e){return i.attributes.some(function(t){return t===e.split(":")[0]})}var l=(s.facetFilters||[]).reduce(function(e,t){if(Array.isArray(t)){var r=t.filter(function(e){return!u(e)});r.length>0&&e.push(r)}return"string"!=typeof t||u(t)||e.push(t),e},[]),f=c[o-1];o>0?s.facetFilters=l.concat(f.attribute+":"+f.value):l.length>0?s.facetFilters=l:delete s.facetFilters,r.push({indexName:e,params:s})})}}),r},_getCompositionQueries:function(e){return[{compositionID:e.index,requestBody:{params:a._getCompositionHitsSearchParams(e)}}]},_getHitsSearchParams:function(e){var t=e.facets.concat(e.disjunctiveFacets).concat(a._getHitsHierarchicalFacetsAttributes(e)).sort(),r=a._getFacetFilters(e),o=a._getNumericFilters(e),s=a._getTagFilters(e),c={};return t.length>0&&(c.facets=t.indexOf("*")>-1?["*"]:t),s.length>0&&(c.tagFilters=s),r.length>0&&(c.facetFilters=r),o.length>0&&(c.numericFilters=o),i(n({},e.getQueryParams(),c))},_getCompositionHitsSearchParams:function(e){var t=e.facets.concat(e.disjunctiveFacets.map(function(e){return"disjunctive("+e+")"})).concat(a._getHitsHierarchicalFacetsAttributes(e)).sort(),r=a._getFacetFilters(e),o=a._getNumericFilters(e),s=a._getTagFilters(e),c={};t.length>0&&(c.facets=t.indexOf("*")>-1?["*"]:t),s.length>0&&(c.tagFilters=s),r.length>0&&(c.facetFilters=r),o.length>0&&(c.numericFilters=o);var u=e.getQueryParams();return delete u.highlightPreTag,delete u.highlightPostTag,delete u.index,i(n({},u,c))},_getDisjunctiveFacetSearchParams:function(e,t,r){var o=a._getFacetFilters(e,t,r),s=a._getNumericFilters(e,t),c=a._getTagFilters(e),u={hitsPerPage:0,page:0,analytics:!1,clickAnalytics:!1};c.length>0&&(u.tagFilters=c);var l=e.getHierarchicalFacetByName(t);return l?u.facets=a._getDisjunctiveHierarchicalFacetAttribute(e,l,r):u.facets=t,s.length>0&&(u.numericFilters=s),o.length>0&&(u.facetFilters=o),i(n({},e.getQueryParams(),u))},_getNumericFilters:function(e,t){if(e.numericFilters)return e.numericFilters;var r=[];return Object.keys(e.numericRefinements).forEach(function(n){var i=e.numericRefinements[n]||{};Object.keys(i).forEach(function(e){var a=i[e]||[];t!==n&&a.forEach(function(t){if(Array.isArray(t)){var i=t.map(function(t){return n+e+t});r.push(i)}else r.push(n+e+t)})})}),r},_getTagFilters:function(e){return e.tagFilters?e.tagFilters:e.tagRefinements.join(",")},_getFacetFilters:function(e,t,r){var n=[],i=e.facetsRefinements||{};Object.keys(i).sort().forEach(function(e){(i[e]||[]).slice().sort().forEach(function(t){n.push(e+":"+t)})});var a=e.facetsExcludes||{};Object.keys(a).sort().forEach(function(e){(a[e]||[]).sort().forEach(function(t){n.push(e+":-"+t)})});var o=e.disjunctiveFacetsRefinements||{};Object.keys(o).sort().forEach(function(e){var r=o[e]||[];if(e!==t&&r&&0!==r.length){var i=[];r.slice().sort().forEach(function(t){i.push(e+":"+t)}),n.push(i)}});var s=e.hierarchicalFacetsRefinements||{};return Object.keys(s).sort().forEach(function(i){var a,o,c=(s[i]||[])[0];if(void 0!==c){var u=e.getHierarchicalFacetByName(i),l=e._getHierarchicalFacetSeparator(u),f=e._getHierarchicalRootPath(u);if(t===i){if(-1===c.indexOf(l)||!f&&!0===r||f&&f.split(l).length===c.split(l).length)return;f?(o=f.split(l).length-1,c=f):(o=c.split(l).length-2,c=c.slice(0,c.lastIndexOf(l))),a=u.attributes[o]}else o=c.split(l).length-1,a=u.attributes[o];a&&n.push([a+":"+c])}}),n},_getHitsHierarchicalFacetsAttributes:function(e){return e.hierarchicalFacets.reduce(function(t,r){var n=e.getHierarchicalRefinement(r.name)[0];if(!n)return t.push(r.attributes[0]),t;var i=e._getHierarchicalFacetSeparator(r),a=n.split(i).length,o=r.attributes.slice(0,a+1);return t.concat(o)},[])},_getDisjunctiveHierarchicalFacetAttribute:function(e,t,r){var n=e._getHierarchicalFacetSeparator(t);if(!0===r){var i=e._getHierarchicalRootPath(t),a=0;return i&&(a=i.split(n).length),[t.attributes[a]]}var o=(e.getHierarchicalRefinement(t.name)[0]||"").split(n).length-1;return t.attributes.slice(0,o+1)},getSearchForFacetQuery:function(e,t,r,o){var s=o.isDisjunctiveFacet(e)?o.clearRefinements(e):o,c={facetQuery:t,facetName:e};return"number"==typeof r&&(c.maxFacetHits=r),i(n({},a._getHitsSearchParams(s),c))}};e.exports=a},46801:function(e){"use strict";e.exports=function(e){return null!==e&&/^[a-zA-Z0-9_-]{1,64}$/.test(e)}},97878:function(e,t,r){"use strict";var n=r(7888),i=r(7948);e.exports=function(e){var t,r,a={};return e.forEach(function(e){e.forEach(function(e,t){a[e.objectID]?a[e.objectID]={indexSum:a[e.objectID].indexSum+t,count:a[e.objectID].count+1}:a[e.objectID]={indexSum:t,count:1}})}),(t=e.length,r=[],Object.keys(a).forEach(function(e){a[e].count<2&&(a[e].indexSum+=100),r.push({objectID:e,avgOfIndices:a[e].indexSum/t})}),r.sort(function(e,t){return e.avgOfIndices>t.avgOfIndices?1:-1})).reduce(function(t,r){var a=n(i(e),function(e){return e.objectID===r.objectID});return a?t.concat(a):t},[])}},24336:function(e){"use strict";e.exports="3.24.2"},55904:function(e){"use strict";var t=String.prototype.replace,r=/%20/g,n="RFC3986";e.exports={default:n,formatters:{RFC1738:function(e){return t.call(e,r,"+")},RFC3986:function(e){return String(e)}},RFC1738:"RFC1738",RFC3986:n}},57368:function(e,t,r){"use strict";var n=r(83307),i=r(46316),a=r(55904);e.exports={formats:a,parse:i,stringify:n}},46316:function(e,t,r){"use strict";var n=r(59084),i=Object.prototype.hasOwnProperty,a=Array.isArray,o={allowDots:!1,allowPrototypes:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decoder:n.decode,delimiter:"&",depth:5,ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictNullHandling:!1},s=function(e,t){return e&&"string"==typeof e&&t.comma&&e.indexOf(",")>-1?e.split(","):e},c=function(e,t){var r={},c=t.ignoreQueryPrefix?e.replace(/^\?/,""):e,u=t.parameterLimit===1/0?void 0:t.parameterLimit,l=c.split(t.delimiter,u),f=-1,h=t.charset;if(t.charsetSentinel)for(d=0;d<l.length;++d)0===l[d].indexOf("utf8=")&&("utf8=%E2%9C%93"===l[d]?h="utf-8":"utf8=%26%2310003%3B"===l[d]&&(h="iso-8859-1"),f=d,d=l.length);for(d=0;d<l.length;++d)if(d!==f){var d,p,m,g=l[d],y=g.indexOf("]="),v=-1===y?g.indexOf("="):y+1;-1===v?(p=t.decoder(g,o.decoder,h,"key"),m=t.strictNullHandling?null:""):(p=t.decoder(g.slice(0,v),o.decoder,h,"key"),m=n.maybeMap(s(g.slice(v+1),t),function(e){return t.decoder(e,o.decoder,h,"value")})),m&&t.interpretNumericEntities&&"iso-8859-1"===h&&(m=m.replace(/&#(\d+);/g,function(e,t){return String.fromCharCode(parseInt(t,10))})),g.indexOf("[]=")>-1&&(m=a(m)?[m]:m),i.call(r,p)?r[p]=n.combine(r[p],m):r[p]=m}return r},u=function(e,t,r,n){for(var i=n?t:s(t,r),a=e.length-1;a>=0;--a){var o,c=e[a];if("[]"===c&&r.parseArrays)o=[].concat(i);else{o=r.plainObjects?Object.create(null):{};var u="["===c.charAt(0)&&"]"===c.charAt(c.length-1)?c.slice(1,-1):c,l=parseInt(u,10);r.parseArrays||""!==u?!isNaN(l)&&c!==u&&String(l)===u&&l>=0&&r.parseArrays&&l<=r.arrayLimit?(o=[])[l]=i:"__proto__"!==u&&(o[u]=i):o={0:i}}i=o}return i},l=function(e,t,r,n){if(e){var a=r.allowDots?e.replace(/\.([^.[]+)/g,"[$1]"):e,o=/(\[[^[\]]*])/g,s=r.depth>0&&/(\[[^[\]]*])/.exec(a),c=s?a.slice(0,s.index):a,l=[];if(c){if(!r.plainObjects&&i.call(Object.prototype,c)&&!r.allowPrototypes)return;l.push(c)}for(var f=0;r.depth>0&&null!==(s=o.exec(a))&&f<r.depth;){if(f+=1,!r.plainObjects&&i.call(Object.prototype,s[1].slice(1,-1))&&!r.allowPrototypes)return;l.push(s[1])}return s&&l.push("["+a.slice(s.index)+"]"),u(l,t,r,n)}},f=function(e){if(!e)return o;if(null!==e.decoder&&void 0!==e.decoder&&"function"!=typeof e.decoder)throw TypeError("Decoder has to be a function.");if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var t=void 0===e.charset?o.charset:e.charset;return{allowDots:void 0===e.allowDots?o.allowDots:!!e.allowDots,allowPrototypes:"boolean"==typeof e.allowPrototypes?e.allowPrototypes:o.allowPrototypes,arrayLimit:"number"==typeof e.arrayLimit?e.arrayLimit:o.arrayLimit,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:o.charsetSentinel,comma:"boolean"==typeof e.comma?e.comma:o.comma,decoder:"function"==typeof e.decoder?e.decoder:o.decoder,delimiter:"string"==typeof e.delimiter||n.isRegExp(e.delimiter)?e.delimiter:o.delimiter,depth:"number"==typeof e.depth||!1===e.depth?+e.depth:o.depth,ignoreQueryPrefix:!0===e.ignoreQueryPrefix,interpretNumericEntities:"boolean"==typeof e.interpretNumericEntities?e.interpretNumericEntities:o.interpretNumericEntities,parameterLimit:"number"==typeof e.parameterLimit?e.parameterLimit:o.parameterLimit,parseArrays:!1!==e.parseArrays,plainObjects:"boolean"==typeof e.plainObjects?e.plainObjects:o.plainObjects,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:o.strictNullHandling}};e.exports=function(e,t){var r=f(t);if(""===e||null==e)return r.plainObjects?Object.create(null):{};for(var i="string"==typeof e?c(e,r):e,a=r.plainObjects?Object.create(null):{},o=Object.keys(i),s=0;s<o.length;++s){var u=o[s],h=l(u,i[u],r,"string"==typeof e);a=n.merge(a,h,r)}return n.compact(a)}},83307:function(e,t,r){"use strict";var n=r(59084),i=r(55904),a=Object.prototype.hasOwnProperty,o={brackets:function(e){return e+"[]"},comma:"comma",indices:function(e,t){return e+"["+t+"]"},repeat:function(e){return e}},s=Array.isArray,c=String.prototype.split,u=Array.prototype.push,l=function(e,t){u.apply(e,s(t)?t:[t])},f=Date.prototype.toISOString,h=i.default,d={addQueryPrefix:!1,allowDots:!1,charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encoder:n.encode,encodeValuesOnly:!1,format:h,formatter:i.formatters[h],indices:!1,serializeDate:function(e){return f.call(e)},skipNulls:!1,strictNullHandling:!1},p=function e(t,r,i,a,o,u,f,h,p,m,g,y,v,b){var S,O,j=t;if("function"==typeof f?j=f(r,j):j instanceof Date?j=m(j):"comma"===i&&s(j)&&(j=n.maybeMap(j,function(e){return e instanceof Date?m(e):e})),null===j){if(a)return u&&!v?u(r,d.encoder,b,"key",g):r;j=""}if("string"==typeof(S=j)||"number"==typeof S||"boolean"==typeof S||"symbol"==typeof S||"bigint"==typeof S||n.isBuffer(j)){if(u){var R=v?r:u(r,d.encoder,b,"key",g);if("comma"===i&&v){for(var P=c.call(String(j),","),w="",x=0;x<P.length;++x)w+=(0===x?"":",")+y(u(P[x],d.encoder,b,"value",g));return[y(R)+"="+w]}return[y(R)+"="+y(u(j,d.encoder,b,"value",g))]}return[y(r)+"="+y(String(j))]}var F=[];if(void 0===j)return F;if("comma"===i&&s(j))O=[{value:j.length>0?j.join(",")||null:void 0}];else if(s(f))O=f;else{var _=Object.keys(j);O=h?_.sort(h):_}for(var E=0;E<O.length;++E){var T=O[E],I="object"==typeof T&&void 0!==T.value?T.value:j[T];o&&null===I||l(F,e(I,s(j)?"function"==typeof i?i(r,T):r:r+(p?"."+T:"["+T+"]"),i,a,o,u,f,h,p,m,g,y,v,b))}return F},m=function(e){if(!e)return d;if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw TypeError("Encoder has to be a function.");var t=e.charset||d.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var r=i.default;if(void 0!==e.format){if(!a.call(i.formatters,e.format))throw TypeError("Unknown format option provided.");r=e.format}var n=i.formatters[r],o=d.filter;return("function"==typeof e.filter||s(e.filter))&&(o=e.filter),{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:d.addQueryPrefix,allowDots:void 0===e.allowDots?d.allowDots:!!e.allowDots,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:d.charsetSentinel,delimiter:void 0===e.delimiter?d.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:d.encode,encoder:"function"==typeof e.encoder?e.encoder:d.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:d.encodeValuesOnly,filter:o,format:r,formatter:n,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:d.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:d.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:d.strictNullHandling}};e.exports=function(e,t){var r,n,i=e,a=m(t);"function"==typeof a.filter?i=(0,a.filter)("",i):s(a.filter)&&(r=a.filter);var c=[];if("object"!=typeof i||null===i)return"";n=t&&t.arrayFormat in o?t.arrayFormat:t&&"indices"in t?t.indices?"indices":"repeat":"indices";var u=o[n];r||(r=Object.keys(i)),a.sort&&r.sort(a.sort);for(var f=0;f<r.length;++f){var h=r[f];a.skipNulls&&null===i[h]||l(c,p(i[h],h,u,a.strictNullHandling,a.skipNulls,a.encode?a.encoder:null,a.filter,a.sort,a.allowDots,a.serializeDate,a.format,a.formatter,a.encodeValuesOnly,a.charset))}var d=c.join(a.delimiter),g=!0===a.addQueryPrefix?"?":"";return a.charsetSentinel&&("iso-8859-1"===a.charset?g+="utf8=%26%2310003%3B&":g+="utf8=%E2%9C%93&"),d.length>0?g+d:""}},59084:function(e,t,r){"use strict";var n=r(55904),i=Object.prototype.hasOwnProperty,a=Array.isArray,o=function(){for(var e=[],t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}(),s=function(e){for(;e.length>1;){var t=e.pop(),r=t.obj[t.prop];if(a(r)){for(var n=[],i=0;i<r.length;++i)void 0!==r[i]&&n.push(r[i]);t.obj[t.prop]=n}}},c=function(e,t){for(var r=t&&t.plainObjects?Object.create(null):{},n=0;n<e.length;++n)void 0!==e[n]&&(r[n]=e[n]);return r};e.exports={arrayToObject:c,assign:function(e,t){return Object.keys(t).reduce(function(e,r){return e[r]=t[r],e},e)},combine:function(e,t){return[].concat(e,t)},compact:function(e){for(var t=[{obj:{o:e},prop:"o"}],r=[],n=0;n<t.length;++n)for(var i=t[n],a=i.obj[i.prop],o=Object.keys(a),c=0;c<o.length;++c){var u=o[c],l=a[u];"object"==typeof l&&null!==l&&-1===r.indexOf(l)&&(t.push({obj:a,prop:u}),r.push(l))}return s(t),e},decode:function(e,t,r){var n=e.replace(/\+/g," ");if("iso-8859-1"===r)return n.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(n)}catch(e){return n}},encode:function(e,t,r,i,a){if(0===e.length)return e;var s=e;if("symbol"==typeof e?s=Symbol.prototype.toString.call(e):"string"!=typeof e&&(s=String(e)),"iso-8859-1"===r)return escape(s).replace(/%u[0-9a-f]{4}/gi,function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"});for(var c="",u=0;u<s.length;++u){var l=s.charCodeAt(u);if(45===l||46===l||95===l||126===l||l>=48&&l<=57||l>=65&&l<=90||l>=97&&l<=122||a===n.RFC1738&&(40===l||41===l)){c+=s.charAt(u);continue}if(l<128){c+=o[l];continue}if(l<2048){c+=o[192|l>>6]+o[128|63&l];continue}if(l<55296||l>=57344){c+=o[224|l>>12]+o[128|l>>6&63]+o[128|63&l];continue}u+=1,c+=o[240|(l=65536+((1023&l)<<10|1023&s.charCodeAt(u)))>>18]+o[128|l>>12&63]+o[128|l>>6&63]+o[128|63&l]}return c},isBuffer:function(e){return!!e&&"object"==typeof e&&!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))},isRegExp:function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},maybeMap:function(e,t){if(a(e)){for(var r=[],n=0;n<e.length;n+=1)r.push(t(e[n]));return r}return t(e)},merge:function e(t,r,n){if(!r)return t;if("object"!=typeof r){if(a(t))t.push(r);else{if(!t||"object"!=typeof t)return[t,r];(n&&(n.plainObjects||n.allowPrototypes)||!i.call(Object.prototype,r))&&(t[r]=!0)}return t}if(!t||"object"!=typeof t)return[t].concat(r);var o=t;return(a(t)&&!a(r)&&(o=c(t,n)),a(t)&&a(r))?(r.forEach(function(r,a){if(i.call(t,a)){var o=t[a];o&&"object"==typeof o&&r&&"object"==typeof r?t[a]=e(o,r,n):t.push(r)}else t[a]=r}),t):Object.keys(r).reduce(function(t,a){var o=r[a];return i.call(t,a)?t[a]=e(t[a],o,n):t[a]=o,t},o)}}},81806:function(e,t,r){"use strict";r.d(t,{Z:function(){return f}});var n=r(93967),i=r.n(n),a=r(67294),o=r(97400),s=r(76681),c=r(85893);let u=a.forwardRef((e,t)=>{let{bsPrefix:r,active:n=!1,children:a,className:u,as:l="li",linkAs:f=s.Z,linkProps:h={},href:d,title:p,target:m,...g}=e,y=(0,o.vE)(r,"breadcrumb-item");return(0,c.jsx)(l,{ref:t,...g,className:i()(y,u,{active:n}),"aria-current":n?"page":void 0,children:n?a:(0,c.jsx)(f,{...h,href:d,title:p,target:m,children:a})})});u.displayName="BreadcrumbItem";let l=a.forwardRef((e,t)=>{let{bsPrefix:r,className:n,listProps:a={},children:s,label:u="breadcrumb",as:l="nav",...f}=e,h=(0,o.vE)(r,"breadcrumb");return(0,c.jsx)(l,{"aria-label":u,className:n,ref:t,...f,children:(0,c.jsx)("ol",{...a,className:i()(h,null==a?void 0:a.className),children:s})})});l.displayName="Breadcrumb";var f=Object.assign(l,{Item:u})},26346:function(e,t,r){"use strict";var n=r(93967),i=r.n(n),a=r(67294),o=r(97400),s=r(85893);let c=a.forwardRef((e,t)=>{let{bsPrefix:r,variant:n,animation:a="border",size:c,as:u="div",className:l,...f}=e;r=(0,o.vE)(r,"spinner");let h="".concat(r,"-").concat(a);return(0,s.jsx)(u,{ref:t,...f,className:i()(l,h,c&&"".concat(h,"-").concat(c),n&&"text-".concat(n))})});c.displayName="Spinner",t.Z=c},53250:function(e,t,r){"use strict";var n=r(67294),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,o=n.useEffect,s=n.useLayoutEffect,c=n.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var l="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),i=n[0].inst,l=n[1];return s(function(){i.value=r,i.getSnapshot=t,u(i)&&l({inst:i})},[e,r,t]),o(function(){return u(i)&&l({inst:i}),e(function(){u(i)&&l({inst:i})})},[e]),c(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:l},61688:function(e,t,r){"use strict";e.exports=r(53250)},53758:function(e,t,r){"use strict";function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.reduce(function(e,t){return Array.isArray(t)?e.concat(t):e.concat([t])},[]).filter(Boolean).join(" ")}r.d(t,{cx:function(){return n}})},28537:function(e,t,r){"use strict";function n(e,t){if(void 0===e||"function"!=typeof e)throw Error("The render function is not valid (received type ".concat(Object.prototype.toString.call(e).slice(8,-1),").\n\n").concat(t))}r.d(t,{_:function(){return n}})},87456:function(e,t,r){"use strict";function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t.map(function(e){var t;return["https://www.algolia.com/doc/api-reference/widgets/",e.name,"/js/",void 0!==(t=e.connector)&&t?"#connector":""].join("")}).join(", ");return function(e){return[e,"See documentation: ".concat(n)].filter(Boolean).join("\n\n")}}r.d(t,{K:function(){return n}})},90761:function(e,t,r){"use strict";r.d(t,{Rn:function(){return f},dg:function(){return l},mY:function(){return d}});var n=r(22686),i=r(7105);function a(e){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(){return(o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){u(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function u(e,t,r){var n;return(n=function(e,t){if("object"!=a(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=a(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==a(n)?n:String(n))in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var l={highlightPreTag:"__ais-highlight__",highlightPostTag:"__/ais-highlight__"},f={highlightPreTag:"<mark>",highlightPostTag:"</mark>"};function h(e){var t;return(0,i.P)(e)&&"string"!=typeof e.value?Object.keys(e).reduce(function(t,r){return c(c({},t),{},u({},r,h(e[r])))},{}):Array.isArray(e)?e.map(h):c(c({},e),{},{value:(t=e.value,(0,n.Y)(t).replace(RegExp(l.highlightPreTag,"g"),f.highlightPreTag).replace(RegExp(l.highlightPostTag,"g"),f.highlightPostTag))})}function d(e){return void 0===e.__escaped&&((e=e.map(function(e){var t=o({},(function(e){if(null==e)throw TypeError("Cannot destructure "+e)}(e),e));return t._highlightResult&&(t._highlightResult=h(t._highlightResult)),t._snippetResult&&(t._snippetResult=h(t._snippetResult)),t})).__escaped=!0),e}},22686:function(e,t,r){"use strict";r.d(t,{A:function(){return l},Y:function(){return o}});var n={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},i=/[&<>"']/g,a=RegExp(i.source);function o(e){return e&&a.test(e)?e.replace(i,function(e){return n[e]}):e}var s={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"},c=/&(amp|quot|lt|gt|#39);/g,u=RegExp(c.source);function l(e){return e&&u.test(e)?e.replace(c,function(e){return s[e]}):e}},48361:function(e,t,r){"use strict";r.d(t,{H:function(){return i}});var n=r(90761);function i(e){var t=n.Rn.highlightPostTag,r=n.Rn.highlightPreTag,i=e.split(r),a=i.shift(),o=a?[{value:a,isHighlighted:!1}]:[];return i.forEach(function(e){var r=e.split(t);o.push({value:r[0],isHighlighted:!0}),""!==r[1]&&o.push({value:r[1],isHighlighted:!1})}),o}},54442:function(e,t,r){"use strict";function n(e,t){return(Array.isArray(t)?t:t.split(".")).reduce(function(e,t){return e&&e[t]},e)}r.d(t,{E:function(){return n}})},79649:function(e,t,r){"use strict";function n(e){return"ais.index"===e.$$type}r.d(t,{J:function(){return n}})},7105:function(e,t,r){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e){if(!("object"===n(e)&&null!==e)||"[object Object]"!==(null===e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)))return!1;if(null===Object.getPrototypeOf(e))return!0;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}r.d(t,{P:function(){return i}})},82729:function(e,t,r){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}r.d(t,{Z:function(){return u}});var i=["facets","disjunctiveFacets","facetsRefinements","facetsExcludes","disjunctiveFacetsRefinements","numericRefinements","tagRefinements","hierarchicalFacets","hierarchicalFacetsRefinements","ruleContexts"];function a(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(Object(r),!0).forEach(function(t){var i,a;i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!=n(i))return i;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==n(t)?t:String(t)}(i))in e?Object.defineProperty(e,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var s=function(e,t){t.facets,t.disjunctiveFacets,t.facetsRefinements,t.facetsExcludes,t.disjunctiveFacetsRefinements,t.numericRefinements,t.tagRefinements,t.hierarchicalFacets,t.hierarchicalFacetsRefinements,t.ruleContexts;var r=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(t,i);return e.setQueryParameters(r)},c=function(e,t){var r=[].concat(e.ruleContexts).concat(t.ruleContexts).filter(Boolean).filter(function(e,t,r){return r.indexOf(e)===t});return r.length>0?e.setQueryParameters({ruleContexts:r}):e},u=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.reduce(function(e,t){var r,n,i,a,u,l,f,h;return s((h=c((n=(r=e.setQueryParameters({hierarchicalFacetsRefinements:o(o({},e.hierarchicalFacetsRefinements),t.hierarchicalFacetsRefinements)})).setQueryParameters({hierarchicalFacets:t.hierarchicalFacets.reduce(function(e,t){var r=function(e,t){if(!Array.isArray(e))return -1;for(var r=0;r<e.length;r++)if(t(e[r]))return r;return -1}(e,function(e){return e.name===t.name});if(-1===r)return e.concat(t);var n=e.slice();return n.splice(r,1,t),n},r.hierarchicalFacets)}),f=(l=(u=(a=(i=t.tagRefinements.reduce(function(e,t){return e.addTagRefinement(t)},n)).setQueryParameters({numericRefinements:o(o({},i.numericRefinements),t.numericRefinements)})).setQueryParameters({disjunctiveFacetsRefinements:o(o({},a.disjunctiveFacetsRefinements),t.disjunctiveFacetsRefinements)})).setQueryParameters({facetsExcludes:o(o({},u.facetsExcludes),t.facetsExcludes)})).setQueryParameters({facetsRefinements:o(o({},l.facetsRefinements),t.facetsRefinements)}),t.disjunctiveFacets.reduce(function(e,t){return e.addDisjunctiveFacet(t)},f)),t),t.facets.reduce(function(e,t){return e.addFacet(t)},h)),t)})}},11490:function(e,t,r){"use strict";function n(){}r.d(t,{Z:function(){return n}})},5852:function(e,t,r){"use strict";function n(e,t,r){var n=t.getHelper();return{uiState:r,helper:n,parent:t,instantSearchInstance:e,state:n.state,renderState:e.renderState,templatesConfig:e.templatesConfig,createURL:t.createURL,scopedResults:[],searchMetadata:{isSearchStalled:"stalled"===e.status},status:e.status,error:e.error}}function i(e,t,r){var n=t.getResultsForWidget(r),i=t.getHelper();return{helper:i,parent:t,instantSearchInstance:e,results:n,scopedResults:t.getScopedResults(),state:n&&"_state"in n?n._state:i.state,renderState:e.renderState,templatesConfig:e.templatesConfig,createURL:t.createURL,searchMetadata:{isSearchStalled:"stalled"===e.status},status:e.status,error:e.error}}r.d(t,{d:function(){return i},q:function(){return n}})},34004:function(e,t,r){"use strict";function n(e){return btoa(encodeURIComponent(JSON.stringify(e)))}r.d(t,{a:function(){return n}})},51477:function(e,t,r){"use strict";r.d(t,{Z:function(){return R}});var n=r(8131),i=r(87456),a=r(79649),o=r(5852),s=r(82729),c=0;function u(e){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var l=["initialSearchParameters"],f=["initialRecommendParameters"];function h(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?h(Object(r),!0).forEach(function(t){p(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function p(e,t,r){var n;return(n=function(e,t){if("object"!=u(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=u(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==u(n)?n:String(n))in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function m(e){return function(e){if(Array.isArray(e))return g(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return g(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return g(e,void 0)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function g(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function y(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var v=(0,i.K)({name:"index-widget"});function b(e,t){var r=t.state,n=t.recommendState,i=t.isPageReset,a=t._uiState;r!==e.state&&(e.state=r,e.emit("change",{state:e.state,results:e.lastResults,isPageReset:i,_uiState:a})),n!==e.recommendState&&(e.recommendState=n)}function S(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return e.reduce(function(e,r){return!(0,a.J)(r)&&(r.getWidgetUiState||r.getWidgetState)?r.getWidgetUiState?r.getWidgetUiState(e,t):r.getWidgetState(e,t):e},r)}function O(e,t){var r=t.initialSearchParameters,n=y(t,l);return e.reduce(function(e,t){return!t.getWidgetSearchParameters||(0,a.J)(t)?e:"search"===t.dependsOn&&t.getWidgetParameters?t.getWidgetParameters(e,n):t.getWidgetSearchParameters(e,n)},r)}function j(e,t){var r=t.initialRecommendParameters,n=y(t,f);return e.reduce(function(e,t){return!(0,a.J)(t)&&"recommend"===t.dependsOn&&t.getWidgetParameters?t.getWidgetParameters(e,n):e},r)}var R=function(e){if(void 0===e||void 0===e.indexName)throw Error(v("The `indexName` option is required."));var t=e.indexName,r=e.indexId,i=void 0===r?t:r,u=[],l={},f=null,h=null,g=null,y=null,R=null,w=!1,x=!1;return{$$type:"ais.index",$$widgetType:"ais.index",getIndexName:function(){return t},getIndexId:function(){return i},getHelper:function(){return g},getResults:function(){var e;return null!==(e=y)&&void 0!==e&&e.lastResults?(y.lastResults._state=g.state,y.lastResults):null},getResultsForWidget:function(e){var t;return"recommend"!==e.dependsOn||(0,a.J)(e)||void 0===e.$$id?this.getResults():null!==(t=g)&&void 0!==t&&t.lastRecommendResults?g.lastRecommendResults[e.$$id]:null},getPreviousState:function(){return R},getScopedResults:function(){var e=this.getParent();return function e(t){return t.filter(a.J).reduce(function(t,r){return t.concat.apply(t,[{indexId:r.getIndexId(),results:r.getResults(),helper:r.getHelper()}].concat(m(e(r.getWidgets()))))},[])}(e?e.getWidgets():0===t.length?this.getWidgets():[this])},getParent:function(){return h},createURL:function(e){return"function"==typeof e?f._createURL(p({},i,e(l))):f._createURL(p({},i,S(u,{searchParameters:e,helper:g})))},getWidgets:function(){return u},addWidgets:function(e){var t=this;if(!Array.isArray(e))throw Error(v("The `addWidgets` method expects an array of widgets."));if(e.some(function(e){return"function"!=typeof e.init&&"function"!=typeof e.render}))throw Error(v("The widget definition expects a `render` and/or an `init` method."));return e.forEach(function(e){if(!(0,a.J)(e))f&&"recommend"===e.dependsOn?f._hasRecommendWidget=!0:f?f._hasSearchWidget=!0:"recommend"===e.dependsOn?w=!0:x=!0,"recommend"===e.dependsOn&&(e.$$id=c++)}),u=u.concat(e),f&&e.length&&(b(g,{state:O(u,{uiState:l,initialSearchParameters:g.state}),recommendState:j(u,{uiState:l,initialRecommendParameters:g.recommendState}),_uiState:l}),e.forEach(function(e){e.getRenderState&&P({renderState:e.getRenderState(f.renderState[t.getIndexId()]||{},(0,o.q)(f,t,f._initialUiState)),instantSearchInstance:f,parent:t})}),e.forEach(function(e){e.init&&e.init((0,o.q)(f,t,f._initialUiState))}),f.scheduleSearch()),this},removeWidgets:function(e){var t=this;if(!Array.isArray(e))throw Error(v("The `removeWidgets` method expects an array of widgets."));if(e.some(function(e){return"function"!=typeof e.dispose}))throw Error(v("The widget definition expects a `dispose` method."));if((u=u.filter(function(t){return -1===e.indexOf(t)})).forEach(function(e){(0,a.J)(e)||(f&&"recommend"===e.dependsOn?f._hasRecommendWidget=!0:f?f._hasSearchWidget=!0:"recommend"===e.dependsOn?w=!0:x=!0)}),f&&e.length){var r=e.reduce(function(e,r){var i=r.dispose({helper:g,state:e.cleanedSearchState,recommendState:e.cleanedRecommendState,parent:t});return i instanceof n.RecommendParameters?e.cleanedRecommendState=i:i&&(e.cleanedSearchState=i),e},{cleanedSearchState:g.state,cleanedRecommendState:g.recommendState}),i=r.cleanedSearchState,o=r.cleanedRecommendState,s=f.future.preserveSharedStateOnUnmount?O(u,{uiState:l,initialSearchParameters:new n.SearchParameters({index:this.getIndexName()})}):O(u,{uiState:S(u,{searchParameters:i,helper:g}),initialSearchParameters:i});l=S(u,{searchParameters:s,helper:g}),g.setState(s),g.recommendState=o,u.length&&f.scheduleSearch()}return this},init:function(e){var r,c=this,d=e.instantSearchInstance,p=e.parent,v=e.uiState;if(null===g){f=d,h=p,l=v[i]||{};var F=d.mainHelper,_=O(u,{uiState:l,initialSearchParameters:new n.SearchParameters({index:t})}),E=j(u,{uiState:l,initialRecommendParameters:new n.RecommendParameters});(g=n({},_.index,_)).recommendState=E,g.search=function(){return d.onStateChange?(d.onStateChange({uiState:d.mainIndex.getWidgetUiState({}),setUiState:function(e){return d.setUiState(e,!1)}}),F):F.search()},g.searchWithoutTriggeringOnStateChange=function(){return F.search()},g.searchForFacetValues=function(e,t,r,n){var i=g.state.setQueryParameters(n);return F.searchForFacetValues(e,t,r,i)},y=F.derive(function(){return s.Z.apply(void 0,[F.state].concat(m(function(e){for(var t=e.getParent(),r=[e.getHelper().state];null!==t;)r=[t.getHelper().state].concat(r),t=t.getParent();return r}(c))))},function(){return c.getHelper().recommendState});var T=null===(r=d._initialResults)||void 0===r?void 0:r[this.getIndexId()];if(null!=T&&T.results){var I=new n.SearchResults(new n.SearchParameters(T.state),T.results);y.lastResults=I,g.lastResults=I}if(null!=T&&T.recommendResults){var A=new n.RecommendResults(new n.RecommendParameters({params:T.recommendResults.params}),T.recommendResults.results);y.lastRecommendResults=A,g.lastRecommendResults=A}g.on("change",function(e){e.isPageReset&&function e(t){var r=t.filter(a.J);0!==r.length&&r.forEach(function(t){var r=t.getHelper();b(r,{state:r.state.resetPage(),recommendState:r.recommendState,isPageReset:!0}),e(t.getWidgets())})}(u)}),y.on("search",function(){d.scheduleStalledRender()}),y.on("result",function(e){var t=e.results;d.scheduleRender(),g.lastResults=t,R=null==t?void 0:t._state}),y.on("recommend:result",function(e){var t=e.recommend;d.scheduleRender(),g.lastRecommendResults=t.results}),u.forEach(function(e){e.getRenderState&&P({renderState:e.getRenderState(d.renderState[c.getIndexId()]||{},(0,o.q)(d,c,v)),instantSearchInstance:d,parent:c})}),u.forEach(function(e){e.init&&e.init((0,o.q)(d,c,v))}),g.on("change",function(e){var t=e.state,r=e._uiState;l=S(u,{searchParameters:t,helper:g},r||{}),d.onStateChange||d.onInternalStateChange()}),T&&d.scheduleRender(),w&&(d._hasRecommendWidget=!0),x&&(d._hasSearchWidget=!0)}},render:function(e){var t,r=this,n=e.instantSearchInstance;"error"===n.status&&!n.mainHelper.hasPendingRequests()&&R&&g.setState(R);var i=this.getResults()||null!==(t=y)&&void 0!==t&&t.lastRecommendResults?u:u.filter(a.J);(i=i.filter(function(e){return!e.shouldRender||e.shouldRender({instantSearchInstance:n})})).forEach(function(e){e.getRenderState&&P({renderState:e.getRenderState(n.renderState[r.getIndexId()]||{},(0,o.d)(n,r,e)),instantSearchInstance:n,parent:r})}),i.forEach(function(e){e.render&&e.render((0,o.d)(n,r,e))})},dispose:function(){var e,t,r=this;u.forEach(function(e){e.dispose&&g&&e.dispose({helper:g,state:g.state,recommendState:g.recommendState,parent:r})}),f=null,h=null,null===(e=g)||void 0===e||e.removeAllListeners(),g=null,null===(t=y)||void 0===t||t.detach(),y=null},getWidgetUiState:function(e){return u.filter(a.J).reduce(function(e,t){return t.getWidgetUiState(e)},d(d({},e),{},p({},i,d(d({},e[i]),l))))},getWidgetState:function(e){return this.getWidgetUiState(e)},getWidgetSearchParameters:function(e,t){var r=t.uiState;return O(u,{uiState:r,initialSearchParameters:e})},refreshUiState:function(){l=S(u,{searchParameters:this.getHelper().state,helper:this.getHelper()},l)},setIndexUiState:function(e){var t="function"==typeof e?e(l):e;f.setUiState(function(e){return d(d({},e),{},p({},i,t))})}}};function P(e){var t=e.renderState,r=e.instantSearchInstance,n=e.parent,i=n?n.getIndexId():r.mainIndex.getIndexId();r.renderState=d(d({},r.renderState),{},p({},i,d(d({},r.renderState[i]),t)))}},58685:function(e,t,r){"use strict";r.d(t,{T:function(){return v}});var n=r(8131),i=r(87456),a=r(11490),o=r(7105),s=r(82729);function c(e){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach(function(t){f(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function f(e,t,r){var n;return(n=function(e,t){if("object"!=c(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=c(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==c(n)?n:String(n))in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var h=(0,i.K)({name:"configure",connector:!0});function d(e,t){return e.setQueryParameters(Object.keys(t.searchParameters).reduce(function(e,t){return l(l({},e),{},f({},t,void 0))},{}))}var p=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:a.Z,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:a.Z;return function(r){if(!r||!(0,o.P)(r.searchParameters))throw Error(h("The `searchParameters` option expects an object."));var i={};return{$$type:"ais.configure",init:function(t){var r=t.instantSearchInstance;e(l(l({},this.getWidgetRenderState(t)),{},{instantSearchInstance:r}),!0)},render:function(t){var r=t.instantSearchInstance;e(l(l({},this.getWidgetRenderState(t)),{},{instantSearchInstance:r}),!1)},dispose:function(e){var n=e.state;return t(),d(n,r)},getRenderState:function(e,t){var r,i=this.getWidgetRenderState(t);return l(l({},e),{},{configure:l(l({},i),{},{widgetParams:l(l({},i.widgetParams),{},{searchParameters:(0,s.Z)(new n.SearchParameters(null===(r=e.configure)||void 0===r?void 0:r.widgetParams.searchParameters),new n.SearchParameters(i.widgetParams.searchParameters)).getQueryParams()})})})},getWidgetRenderState:function(e){var t=e.helper;return i.refine||(i.refine=function(e){var i=d(t.state,r),a=(0,s.Z)(i,new n.SearchParameters(e));r.searchParameters=e,t.setState(a).search()}),{refine:i.refine,widgetParams:r}},getWidgetSearchParameters:function(e,t){var i=t.uiState;return(0,s.Z)(e,new n.SearchParameters(l(l({},i.configure),r.searchParameters)))},getWidgetUiState:function(e){return l(l({},e),{},{configure:l(l({},e.configure),r.searchParameters)})}}}},m=r(53874);function g(e){return(g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function y(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function v(e){var t,r;return t=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?y(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=g(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=g(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==g(t)?t:String(t)}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):y(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e),r={$$widgetType:"ais.configure"},(0,m.B)(p,{searchParameters:t},r),null}},2417:function(e,t,r){"use strict";r.d(t,{g:function(){return p}});var n=r(67294),i=r(95930),a=r(51477),o=r(89309),s=r(97406),c=r(58336),u=r(37556),l=r(94397),f=r(3353),h=r(33903),d=["children"];function p(e){var t,r,p,m,g,y,v,b,S,O=e.children,j=(t=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,d),r=(0,c.a)(),m=null==(p=(0,u.s)())?void 0:p.initialResults,g=(0,s.a)(),y=(0,f.q)(t),b=(v=(0,n.useMemo)(function(){return(0,a.Z)(y)},[y])).getHelper(),S=(0,o.N)(),(0,l.L)(function(){S()},[b,S]),(0,h.F)({widget:v,parentIndex:g,props:y,shouldSsr:!!(r||m)}),v);return null===j.getHelper()?null:n.createElement(i.Z.Provider,{value:j},O)}},31089:function(e,t,r){"use strict";r.d(t,{p:function(){return eK}});var n=r(67294),i=r(95930),a=r(23130),o=r(17331),s=r(8131);function c(e){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function u(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{fallback:function(){}},r=t.fallback;return"undefined"==typeof window?r():e({window:window})}var l=r(11490);function f(e,t){for(var r,n=0;n<e.length;n++)if(t(r=e[n],n,e))return r}function h(e){return(h="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=h(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=h(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==h(t)?t:String(t)}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function m(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,o,s=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(e){u=!0,i=e}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(u)throw i}}return s}}(e,t)||g(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function g(e,t){if(e){if("string"==typeof e)return y(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return y(e,t)}}function y(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var v="2.17.2",b="https://cdn.jsdelivr.net/npm/search-insights@".concat(v,"/dist/search-insights.min.js");function S(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.insightsClient,r=e.insightsInitParams,n=e.onEvent,i=e.$$internal,a=void 0!==i&&i,o=e.$$automatic,s=void 0!==o&&o,h=t;t||null===t||u(function(e){var t=e.window,r=t.AlgoliaAnalyticsObject||"aa";"string"==typeof r&&(h=t[r]),h||(t.AlgoliaAnalyticsObject=r,t[r]||(t[r]=function(){t[r].queue||(t[r].queue=[]);for(var e=arguments.length,n=Array(e),i=0;i<e;i++)n[i]=arguments[i];t[r].queue.push(n)},t[r].version=v,t[r].shouldAddScript=!0),h=t[r])});var d=h||l.Z;return function(e){var t,i,o=e.instantSearchInstance,u=o.middleware.filter(function(e){return"ais.insights"===e.instance.$$type&&e.instance.$$internal}).map(function(e){return e.creator});o.unuse.apply(o,function(e){if(Array.isArray(e))return y(e)}(u)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(u)||g(u)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}());var h=m(function(e){if(e.appId&&e.apiKey)return[e.appId,e.apiKey];if(!e.transporter)return[e.applicationID,e.apiKey];var t=e.transporter,r=t.headers||t.baseHeaders,n=t.queryParameters||t.baseQueryParameters,i="x-algolia-application-id",a="x-algolia-api-key";return[r[i]||n[i],r[a]||n[a]]}(o.client),2),v=h[0],S=h[1],R=void 0,P=void 0,w=void 0,x=d.queue;if(Array.isArray(x)){var F=["setUserToken","init"].map(function(e){var t=f(x.slice().reverse(),function(t){return m(t,1)[0]===e})||[];return m(t,2)[1]}),_=m(F,2);P=_[0],R=_[1]}return d("getUserToken",null,function(e,t){w=j(t)}),(r||!O(d))&&d("init",p({appId:v,apiKey:S,partial:!0},r)),{$$type:"ais.insights",$$internal:a,$$automatic:s,onStateChange:function(){},subscribe:function(){if(d.shouldAddScript){var e="[insights middleware]: could not load search-insights.js. Please load it manually following https://alg.li/insights-init";try{var t=document.createElement("script");t.async=!0,t.src=b,t.onerror=function(){o.emit("error",Error(e))},document.body.appendChild(t),d.shouldAddScript=!1}catch(t){d.shouldAddScript=!1,o.emit("error",Error(e))}}},started:function(){d("addAlgoliaAgent","insights-middleware"),i=o.mainHelper;var e,u,l,h,g,y,b,x,F,_,E,T,I=d.queue;if(Array.isArray(I)){var A=["setUserToken","init"].map(function(e){var t=f(I.slice().reverse(),function(t){return m(t,1)[0]===e})||[];return m(t,2)[1]}),N=m(A,2);P=N[0],R=N[1]}l=(null===(e=o._initialResults)||void 0===e?void 0:null===(u=e[o.indexName])||void 0===u?void 0:u.state)||{},h=o.mainHelper.state,t={userToken:l.userToken||h.userToken,clickAnalytics:l.clickAnalytics||h.clickAnalytics},s||i.overrideStateWithoutTriggeringChangeEvent(p(p({},i.state),{},{clickAnalytics:!0})),a||o.scheduleSearch();var D=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=j(e);if(r){var n=i.state.userToken;t?a():setTimeout(a,0)}function a(){i.overrideStateWithoutTriggeringChangeEvent(p(p({},i.state),{},{userToken:r})),n&&n!==e&&o.scheduleSearch()}};function H(e){D(e,!0),d("setUserToken",e)}var k=void 0;k=function(e){if(("undefined"==typeof document?"undefined":c(document))==="object"&&"string"==typeof document.cookie)for(var t="".concat(e,"="),r=document.cookie.split(";"),n=0;n<r.length;n++){for(var i=r[n];" "===i.charAt(0);)i=i.substring(1);if(0===i.indexOf(t))return i.substring(t.length,i.length)}}("_ALGOLIA")||"anonymous-".concat("xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}));var C=t.userToken;null!=r&&r.userToken&&(F=r.userToken),F?H(F):C?H(C):w?H(w):P?H(P):k&&(H(k),(null!=r&&r.useCookie||null!==(_=R)&&void 0!==_&&_.useCookie)&&(g=k,y=(null==r?void 0:r.cookieDuration)||(null===(E=R)||void 0===E?void 0:E.cookieDuration),(b=new Date).setTime(b.getTime()+(y||15552e6)),x="expires=".concat(b.toUTCString()),document.cookie="_ALGOLIA=".concat(g,";").concat(x,";path=/"))),d("onUserTokenChange",function(e){return D(e,!0)},{immediate:!0});var Q=d;O(d)&&(Q=function(e,t){return d(e,t,{headers:{"X-Algolia-Application-Id":v,"X-Algolia-API-Key":S}})});var L=new Set;o.mainHelper.derivedHelpers[0].on("result",function(e){var t=e.results;t&&(!t.queryID||t.queryID!==T)&&(T=t.queryID,L.clear())}),o.sendEventToInsights=function(e){if(n)n(e,Q);else if(e.insightsMethod){if("viewedObjectIDs"===e.insightsMethod){var t=e.payload,r=t.objectIDs.filter(function(e){return!L.has(e)});if(0===r.length)return;r.forEach(function(e){return L.add(e)}),t.objectIDs=r}e.payload.algoliaSource=["instantsearch"],s&&e.payload.algoliaSource.push("instantsearch-automatic"),"internal"===e.eventModifier&&e.payload.algoliaSource.push("instantsearch-internal"),Q(e.insightsMethod,e.payload)}}},unsubscribe:function(){d("onUserTokenChange",void 0),o.sendEventToInsights=l.Z,i&&t&&(i.overrideStateWithoutTriggeringChangeEvent(p(p({},i.state),t)),o.scheduleSearch())}}}}function O(e){var t=m((e.version||"").split(".").map(Number),2),r=t[0],n=t[1];return r>=3||2===r&&n>=6||1===r&&n>=10}function j(e){if(e)return"number"==typeof e?e.toString():e}var R=r(5852),P=r(57368);function w(e){return(w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function x(e,t,r){return(t=F(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function F(e){var t=function(e,t){if("object"!=w(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=w(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==w(t)?t:String(t)}var _=function(e){e&&(window.document.title=e)},E=function(){var e;function t(e){var r=this,n=e.windowTitle,i=e.writeDelay,a=e.createURL,o=e.parseURL,s=e.getLocation,c=e.start,l=e.dispose,f=e.push,h=e.cleanUrlOnDispose;!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,t),x(this,"$$type","ais.browser"),x(this,"windowTitle",void 0),x(this,"writeDelay",void 0),x(this,"_createURL",void 0),x(this,"parseURL",void 0),x(this,"getLocation",void 0),x(this,"writeTimer",void 0),x(this,"_onPopState",void 0),x(this,"inPopState",!1),x(this,"isDisposed",!1),x(this,"latestAcknowledgedHistory",0),x(this,"_start",void 0),x(this,"_dispose",void 0),x(this,"_push",void 0),x(this,"_cleanUrlOnDispose",void 0),this.windowTitle=n,this.writeTimer=void 0,this.writeDelay=void 0===i?400:i,this._createURL=a,this.parseURL=o,this.getLocation=s,this._start=c,this._dispose=l,this._push=f,this._cleanUrlOnDispose=void 0===h||h,u(function(e){var t=e.window;_(r.windowTitle&&r.windowTitle(r.read())),r.latestAcknowledgedHistory=t.history.length})}return e=[{key:"read",value:function(){return this.parseURL({qsModule:P,location:this.getLocation()})}},{key:"write",value:function(e){var t=this;u(function(r){var n=r.window,i=t.createURL(e),a=t.windowTitle&&t.windowTitle(e);t.writeTimer&&clearTimeout(t.writeTimer),t.writeTimer=setTimeout(function(){_(a),t.shouldWrite(i)&&(t._push?t._push(i):n.history.pushState(e,a||"",i),t.latestAcknowledgedHistory=n.history.length),t.inPopState=!1,t.writeTimer=void 0},t.writeDelay)})}},{key:"onUpdate",value:function(e){var t=this;this._start&&this._start(function(){e(t.read())}),this._onPopState=function(){t.writeTimer&&(clearTimeout(t.writeTimer),t.writeTimer=void 0),t.inPopState=!0,e(t.read())},u(function(e){e.window.addEventListener("popstate",t._onPopState)})}},{key:"createURL",value:function(e){return this._createURL({qsModule:P,routeState:e,location:this.getLocation()})}},{key:"dispose",value:function(){var e=this;this._dispose&&this._dispose(),this.isDisposed=!0,u(function(t){var r=t.window;e._onPopState&&r.removeEventListener("popstate",e._onPopState)}),this.writeTimer&&clearTimeout(this.writeTimer),this._cleanUrlOnDispose&&this.write({})}},{key:"start",value:function(){this.isDisposed=!1}},{key:"shouldWrite",value:function(e){var t=this;return u(function(r){var n=r.window;if(t.isDisposed&&!t._cleanUrlOnDispose)return!1;var i=!(t.isDisposed&&t.latestAcknowledgedHistory!==n.history.length);return!t.inPopState&&i&&e!==n.location.href})}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,F(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}();function T(e){return(T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var I=["configure"];function A(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function N(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?A(Object(r),!0).forEach(function(t){D(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):A(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function D(e,t,r){var n;return(n=function(e,t){if("object"!=T(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=T(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==T(n)?n:String(n))in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function H(e){return e.configure,function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,I)}function k(e){return e!==Object(e)}function C(e){return(C="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Q(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function L(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Q(Object(r),!0).forEach(function(t){U(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Q(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function U(e,t,r){var n;return(n=function(e,t){if("object"!=C(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=C(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==C(n)?n:String(n))in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var W=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.router,r=void 0===t?function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.createURL,r=void 0===t?function(e){var t=e.qsModule,r=e.routeState,n=e.location,i=n.protocol,a=n.hostname,o=n.port,s=void 0===o?"":o,c=n.pathname,u=n.hash,l=t.stringify(r),f=""===s?"":":".concat(s);return l?"".concat(i,"//").concat(a).concat(f).concat(c,"?").concat(l).concat(u):"".concat(i,"//").concat(a).concat(f).concat(c).concat(u)}:t,n=e.parseURL,i=e.writeDelay,a=e.windowTitle,o=e.getLocation;return new E({createURL:r,parseURL:void 0===n?function(e){var t=e.qsModule,r=e.location;return t.parse(r.search.slice(1),{arrayLimit:99})}:n,writeDelay:void 0===i?400:i,windowTitle:a,getLocation:void 0===o?function(){return u(function(e){return e.window.location},{fallback:function(){throw Error("You need to provide `getLocation` to the `history` router in environments where `window` does not exist.")}})}:o,start:e.start,dispose:e.dispose,push:e.push,cleanUrlOnDispose:e.cleanUrlOnDispose})}():t,n=e.stateMapping,i=void 0===n?{$$type:"ais.simple",stateToRoute:function(e){return Object.keys(e).reduce(function(t,r){return N(N({},t),{},D({},r,H(e[r])))},{})},routeToState:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object.keys(e).reduce(function(t,r){return N(N({},t),{},D({},r,H(e[r])))},{})}}:n,a=e.$$internal,o=void 0!==a&&a;return function(e){var t=e.instantSearchInstance;t._createURL=function(e){var n=0===t.mainIndex.getWidgets().length?t._initialUiState:t.mainIndex.getWidgetUiState({}),a=Object.keys(e).reduce(function(t,r){return L(L({},t),{},U({},r,e[r]))},n),o=i.stateToRoute(a);return r.createURL(o)};var n=void 0,a=t._initialUiState;return{$$type:"ais.router({router:".concat(r.$$type||"__unknown__",", stateMapping:").concat(i.$$type||"__unknown__","})"),$$internal:o,onStateChange:function(e){var t=e.uiState,a=i.stateToRoute(t);(void 0===n||!function e(t,r){if(t===r)return!0;if(k(t)||k(r)||"function"==typeof t||"function"==typeof r)return t===r;if(Object.keys(t).length!==Object.keys(r).length)return!1;for(var n=0,i=Object.keys(t);n<i.length;n++){var a=i[n];if(!(a in r)||!e(t[a],r[a]))return!1}return!0}(n,a))&&(r.write(a),n=a)},subscribe:function(){t._initialUiState=L(L({},a),i.routeToState(r.read())),r.onUpdate(function(e){t.mainIndex.getWidgets().length>0&&t.setUiState(i.routeToState(e))})},started:function(){var e;null===(e=r.start)||void 0===e||e.call(r)},unsubscribe:function(){r.dispose()}}}},$=r(51477),q=function(e){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=t.descendantName,n=t.modifierName;return"".concat("ais","-").concat(e).concat(r?"-".concat(r):"").concat(n?"--".concat(n):"")}},B=r(54442),M=r(90761),V=q("Highlight");function J(e){var t=M.Rn.highlightPreTag,r=M.Rn.highlightPostTag;return e.map(function(e){return e.isHighlighted?t+e.value+r:e.value}).join("")}var Z=r(22686),z=new RegExp(/\w/i);function K(e){return(K="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Y(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function X(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Y(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=K(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=K(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==K(t)?t:String(t)}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Y(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function G(e){return e.some(function(e){return e.isHighlighted})?e.map(function(t,r){var n,i,a,o,s;return X(X({},t),{},{isHighlighted:(a=e[r],o=(null===(n=e[r+1])||void 0===n?void 0:n.isHighlighted)||!0,s=(null===(i=e[r-1])||void 0===i?void 0:i.isHighlighted)||!0,z.test((0,Z.A)(a.value))||s!==o?!a.isHighlighted:!s)})}):e.map(function(e){return X(X({},e),{},{isHighlighted:!1})})}var ee=r(48361),et=q("ReverseHighlight"),er=q("Snippet"),en=q("ReverseSnippet"),ei=r(34004);function ea(e){return(ea="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function eo(e){return(eo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function es(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ec(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?es(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=eo(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=eo(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==eo(t)?t:String(t)}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):es(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var eu=r(87456),el=Promise.resolve();function ef(e){var t=null,r=!1,n=function(){for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];null===t&&(t=el.then(function(){if(t=null,r){r=!1;return}e.apply(void 0,i)}))};return n.wait=function(){if(null===t)throw Error("The deferred function should be called before calling `wait()`");return t},n.cancel=function(){null!==t&&(r=!0)},n}var eh=r(79649);function ed(e){return(ed="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ep(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function em(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function eg(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?em(Object(r),!0).forEach(function(t){ey(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):em(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ey(e,t,r){var n;return(n=function(e,t){if("object"!=ed(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=ed(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==ed(n)?n:String(n))in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ev(e){var t=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];var i=0;return e.replace(/%s/g,function(){return encodeURIComponent(r[i++])})};return Object.keys(e).map(function(r){var n;return t("%s=%s",r,(n=e[r],"[object Object]"===Object.prototype.toString.call(n)||"[object Array]"===Object.prototype.toString.call(n))?JSON.stringify(e[r]):e[r])}).join("&")}function eb(e){return(eb="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function eS(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function eO(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eS(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=eb(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=eb(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==eb(t)?t:String(t)}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eS(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ej(e,t){var r=e[t.getIndexId()]||{};t.getHelper().setState(t.getWidgetSearchParameters(t.getHelper().state,{uiState:r})),t.getWidgets().filter(eh.J).forEach(function(t){return ej(e,t)})}function eR(e){return(eR="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function eP(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ew(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eP(Object(r),!0).forEach(function(t){eE(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eP(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ex(e,t){return(ex=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function eF(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function e_(e){return(e_=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function eE(e,t,r){return(t=eT(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function eT(e){var t=function(e,t){if("object"!=eR(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=eR(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==eR(t)?t:String(t)}var eI=(0,eu.K)({name:"instantsearch"});function eA(){return"#"}var eN={preserveSharedStateOnUnmount:!1,persistHierarchicalRootCount:!1},eD=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ex(e,t)}(i,e);var t,r,n=(t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}(),function(){var e,r=e_(i);return e=t?Reflect.construct(r,arguments,e_(this).constructor):r.apply(this,arguments),function(e,t){if(t&&("object"===eR(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return eF(e)}(this,e)});function i(e){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,i),eE(eF(t=n.call(this)),"client",void 0),eE(eF(t),"indexName",void 0),eE(eF(t),"compositionID",void 0),eE(eF(t),"insightsClient",void 0),eE(eF(t),"onStateChange",null),eE(eF(t),"future",void 0),eE(eF(t),"helper",void 0),eE(eF(t),"mainHelper",void 0),eE(eF(t),"mainIndex",void 0),eE(eF(t),"started",void 0),eE(eF(t),"templatesConfig",void 0),eE(eF(t),"renderState",{}),eE(eF(t),"_stalledSearchDelay",void 0),eE(eF(t),"_searchStalledTimer",void 0),eE(eF(t),"_initialUiState",void 0),eE(eF(t),"_initialResults",void 0),eE(eF(t),"_createURL",void 0),eE(eF(t),"_searchFunction",void 0),eE(eF(t),"_mainHelperSearch",void 0),eE(eF(t),"_hasSearchWidget",!1),eE(eF(t),"_hasRecommendWidget",!1),eE(eF(t),"_insights",void 0),eE(eF(t),"middleware",[]),eE(eF(t),"sendEventToInsights",void 0),eE(eF(t),"status","idle"),eE(eF(t),"error",void 0),eE(eF(t),"scheduleSearch",ef(function(){t.started&&t.mainHelper.search()})),eE(eF(t),"scheduleRender",ef(function(){var e,r=!(arguments.length>0)||void 0===arguments[0]||arguments[0];!(null!==(e=t.mainHelper)&&void 0!==e&&e.hasPendingRequests())&&(clearTimeout(t._searchStalledTimer),t._searchStalledTimer=null,r&&(t.status="idle",t.error=void 0)),t.mainIndex.render({instantSearchInstance:eF(t)}),t.emit("render")})),eE(eF(t),"onInternalStateChange",ef(function(){var e=t.mainIndex.getWidgetUiState({});t.middleware.forEach(function(t){t.instance.onStateChange({uiState:e})})})),t.setMaxListeners(100);var t,r=e.indexName,a=e.compositionID,o=e.numberLocale,s=e.initialUiState,c=e.routing,f=void 0===c?null:c,h=e.insights,d=void 0===h?void 0:h,p=e.searchFunction,m=e.stalledSearchDelay,g=e.searchClient,y=void 0===g?null:g,v=e.insightsClient,b=void 0===v?null:v,O=e.onStateChange,j=e.future,P=void 0===j?ew(ew({},eN),e.future||{}):j;if(null===y)throw Error(eI("The `searchClient` option is required."));if("function"!=typeof y.search)throw Error("The `searchClient` must implement a `search` method.\n\nSee: https://www.algolia.com/doc/guides/building-search-ui/going-further/backend-search/in-depth/backend-instantsearch/js/");if("function"==typeof y.addAlgoliaAgent&&y.addAlgoliaAgent("instantsearch.js (".concat("4.78.0",")")),b&&"function"!=typeof b)throw Error(eI("The `insightsClient` option should be a function."));if(t.client=y,t.future=P,t.insightsClient=b,t.indexName=void 0===r?"":r,t.compositionID=a,t.helper=null,t.mainHelper=null,t.mainIndex=(0,$.Z)({indexName:t.compositionID||t.indexName}),t.onStateChange=void 0===O?null:O,t.started=!1,t.templatesConfig={helpers:{formatNumber:function(e,t){return Number(t(e)).toLocaleString(o)},highlight:function(e,t){try{var r,n,i,a,o,s,c,u,l,f=JSON.parse(e);return t((n=(r=ec(ec({},f),{},{hit:this})).attribute,i=r.highlightedTagName,a=void 0===i?"mark":i,o=r.hit,s=r.cssClasses,c=void 0===s?{}:s,u=((0,B.E)(o._highlightResult,n)||{}).value,l=V({descendantName:"highlighted"})+(c.highlighted?" ".concat(c.highlighted):""),(void 0===u?"":u).replace(RegExp(M.Rn.highlightPreTag,"g"),"<".concat(a,' class="').concat(l,'">')).replace(RegExp(M.Rn.highlightPostTag,"g"),"</".concat(a,">"))))}catch(e){throw Error('\nThe highlight helper expects a JSON object of the format:\n{ "attribute": "name", "highlightedTagName": "mark" }')}},reverseHighlight:function(e,t){try{var r,n,i,a,o,s,c,u,l,f=JSON.parse(e);return t((n=(r=ec(ec({},f),{},{hit:this})).attribute,i=r.highlightedTagName,a=void 0===i?"mark":i,o=r.hit,s=r.cssClasses,c=void 0===s?{}:s,u=((0,B.E)(o._highlightResult,n)||{}).value,l=et({descendantName:"highlighted"})+(c.highlighted?" ".concat(c.highlighted):""),J(G((0,ee.H)(void 0===u?"":u))).replace(RegExp(M.Rn.highlightPreTag,"g"),"<".concat(a,' class="').concat(l,'">')).replace(RegExp(M.Rn.highlightPostTag,"g"),"</".concat(a,">"))))}catch(e){throw Error('\n  The reverseHighlight helper expects a JSON object of the format:\n  { "attribute": "name", "highlightedTagName": "mark" }')}},snippet:function(e,t){try{var r,n,i,a,o,s,c,u,l,f=JSON.parse(e);return t((n=(r=ec(ec({},f),{},{hit:this})).attribute,i=r.highlightedTagName,a=void 0===i?"mark":i,o=r.hit,s=r.cssClasses,c=void 0===s?{}:s,u=((0,B.E)(o._snippetResult,n)||{}).value,l=er({descendantName:"highlighted"})+(c.highlighted?" ".concat(c.highlighted):""),(void 0===u?"":u).replace(RegExp(M.Rn.highlightPreTag,"g"),"<".concat(a,' class="').concat(l,'">')).replace(RegExp(M.Rn.highlightPostTag,"g"),"</".concat(a,">"))))}catch(e){throw Error('\nThe snippet helper expects a JSON object of the format:\n{ "attribute": "name", "highlightedTagName": "mark" }')}},reverseSnippet:function(e,t){try{var r,n,i,a,o,s,c,u,l,f=JSON.parse(e);return t((n=(r=ec(ec({},f),{},{hit:this})).attribute,i=r.highlightedTagName,a=void 0===i?"mark":i,o=r.hit,s=r.cssClasses,c=void 0===s?{}:s,u=((0,B.E)(o._snippetResult,n)||{}).value,l=en({descendantName:"highlighted"})+(c.highlighted?" ".concat(c.highlighted):""),J(G((0,ee.H)(void 0===u?"":u))).replace(RegExp(M.Rn.highlightPreTag,"g"),"<".concat(a,' class="').concat(l,'">')).replace(RegExp(M.Rn.highlightPostTag,"g"),"</".concat(a,">"))))}catch(e){throw Error('\n  The reverseSnippet helper expects a JSON object of the format:\n  { "attribute": "name", "highlightedTagName": "mark" }')}},insights:function(e,t){try{var r,n=JSON.parse(e),i=n.method,a=n.payload;return t((r=ec({objectIDs:[this.objectID]},a),function(e){var t,r=e.method,n=e.payload;if("object"!==ea(n))throw Error("The insights helper expects the payload to be an object.");try{t=(0,ei.a)(n)}catch(e){throw Error("Could not JSON serialize the payload object.")}return'data-insights-method="'.concat(r,'" data-insights-payload="').concat(t,'"')}({method:i,payload:r})))}catch(e){throw Error('\nThe insights helper expects a JSON object of the format:\n{ "method": "method-name", "payload": { "eventName": "name of the event" } }')}}},compileOptions:{}},t._stalledSearchDelay=void 0===m?200:m,t._searchStalledTimer=null,t._createURL=eA,t._initialUiState=void 0===s?{}:s,t._initialResults=null,t._insights=d,p&&(t._searchFunction=p),t.sendEventToInsights=l.Z,f){var w="boolean"==typeof f?{}:f;w.$$internal=!0,t.use(W(w))}if(d){var x="boolean"==typeof d?{}:d;x.$$internal=!0,t.use(S(x))}return u(function(e){var t,r;return(null===(t=e.window.navigator)||void 0===t?void 0:null===(r=t.userAgent)||void 0===r?void 0:r.indexOf("Algolia Crawler"))>-1},{fallback:function(){return!1}})&&t.use(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.$$internal,r=void 0!==t&&t;return function(e){var t=e.instantSearchInstance,n={widgets:[]},i=document.createElement("meta"),a=document.querySelector("head");return i.name="instantsearch:widgets",{$$type:"ais.metadata",$$internal:r,onStateChange:function(){},subscribe:function(){setTimeout(function(){var e=t.client;n.ua=e.transporter&&e.transporter.userAgent?e.transporter.userAgent.value:e._ua,function e(t,r,n){var i=(0,R.q)(r,r.mainIndex,r._initialUiState);t.forEach(function(t){var a={};if(t.getWidgetRenderState){var o=t.getWidgetRenderState(i);o&&o.widgetParams&&(a=o.widgetParams)}var s=Object.keys(a).filter(function(e){return void 0!==a[e]});n.widgets.push({type:t.$$type,widgetType:t.$$widgetType,params:s}),"ais.index"===t.$$type&&e(t.getWidgets(),r,n)})}(t.mainIndex.getWidgets(),t,n),t.middleware.forEach(function(e){return n.widgets.push({middleware:!0,type:e.instance.$$type,internal:e.instance.$$internal})}),i.content=JSON.stringify(n),a.appendChild(i)},0)},started:function(){},unsubscribe:function(){i.remove()}}}}({$$internal:!0})),t}return r=[{key:"_isSearchStalled",get:function(){return"stalled"===this.status}},{key:"use",value:function(){for(var e=this,t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];var i=r.map(function(t){var r=ew({$$type:"__unknown__",$$internal:!1,subscribe:l.Z,started:l.Z,unsubscribe:l.Z,onStateChange:l.Z},t({instantSearchInstance:e}));return e.middleware.push({creator:t,instance:r}),r});return this.started&&i.forEach(function(e){e.subscribe(),e.started()}),this}},{key:"unuse",value:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return this.middleware.filter(function(e){return t.includes(e.creator)}).forEach(function(e){return e.instance.unsubscribe()}),this.middleware=this.middleware.filter(function(e){return!t.includes(e.creator)}),this}},{key:"EXPERIMENTAL_use",value:function(){return this.use.apply(this,arguments)}},{key:"addWidget",value:function(e){return this.addWidgets([e])}},{key:"addWidgets",value:function(e){if(!Array.isArray(e))throw Error(eI("The `addWidgets` method expects an array of widgets. Please use `addWidget`."));if(e.some(function(e){return"function"!=typeof e.init&&"function"!=typeof e.render}))throw Error(eI("The widget definition expects a `render` and/or an `init` method."));if(this.compositionID&&e.some(eh.J))throw Error(eI("The `index` widget cannot be used with a composition-based InstantSearch implementation."));return this.mainIndex.addWidgets(e),this}},{key:"removeWidget",value:function(e){return this.removeWidgets([e])}},{key:"removeWidgets",value:function(e){if(!Array.isArray(e))throw Error(eI("The `removeWidgets` method expects an array of widgets. Please use `removeWidget`."));if(e.some(function(e){return"function"!=typeof e.dispose}))throw Error(eI("The widget definition expects a `dispose` method."));return this.mainIndex.removeWidgets(e),this}},{key:"start",value:function(){var e=this;if(this.started)throw Error(eI("The `start` method has already been called once."));var t=this.mainHelper||s(this.client,this.indexName,void 0,{persistHierarchicalRootCount:this.future.persistHierarchicalRootCount});if(this.compositionID&&(t.searchForFacetValues=t.searchForCompositionFacetValues.bind(t)),t.search=function(){return e.status="loading",e.scheduleRender(!1),e._hasSearchWidget&&(e.compositionID?t.searchWithComposition():t.searchOnlyWithDerivedHelpers()),e._hasRecommendWidget&&t.recommend(),t},this._searchFunction){var r={search:function(){return new Promise(l.Z)}};this._mainHelperSearch=t.search.bind(t),t.search=function(){var n=e.mainIndex.getHelper(),i=s(r,n.state.index,n.state);return i.once("search",function(t){var r=t.state;n.overrideStateWithoutTriggeringChangeEvent(r),e._mainHelperSearch()}),i.on("change",function(e){var t=e.state;n.setState(t)}),e._searchFunction(i),t}}if(t.on("error",function(t){var r=t.error;if(!(r instanceof Error)){var n=r;r=Object.keys(n).reduce(function(e,t){return e[t]=n[t],e},Error(n.message))}r.error=r,e.error=r,e.status="error",e.scheduleRender(!1),e.emit("error",r)}),this.mainHelper=t,this.middleware.forEach(function(e){e.instance.subscribe()}),this.mainIndex.init({instantSearchInstance:this,parent:null,uiState:this._initialUiState}),this._initialResults){(function(e,t){if(t&&("transporter"in e&&!e._cacheHydrated||e._useCache&&"function"==typeof e.addAlgoliaAgent)){var r=[Object.keys(t).reduce(function(e,r){var n=t[r],i=n.state,a=n.requestParams,o=n.results,s=o&&i?o.map(function(e,t){return eg({indexName:i.index||e.index},null!=a&&a[t]||e.params?{params:ev((null==a?void 0:a[t])||e.params.split("&").reduce(function(e,t){var r,n=function(e){if(Array.isArray(e))return e}(r=t.split("="))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,o,s=[],c=!0,u=!1;try{for(a=(r=r.call(e)).next;!(c=(n=a.call(r)).done)&&(s.push(n.value),2!==s.length);c=!0);}catch(e){u=!0,i=e}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(u)throw i}}return s}}(r,2)||function(e,t){if(e){if("string"==typeof e)return ep(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ep(e,2)}}(r,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=n[0],a=n[1];return e[i]=a?decodeURIComponent(a):"",e},{}))}:{})}):[];return e.concat(s)},[])],n=Object.keys(t).reduce(function(e,r){var n=t[r].results;return n?e.concat(n):e},[]);if("transporter"in e&&!e._cacheHydrated){e._cacheHydrated=!0;var i=e.search.bind(e);e.search=function(t){for(var r=arguments.length,n=Array(r>1?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];var o=Array.isArray(t)?t.map(function(e){return eg(eg({},e),{},{params:ev(e.params)})}):ev(t.requestBody.params);return e.transporter.responsesCache.get({method:"search",args:[o].concat(n)},function(){return i.apply(void 0,[t].concat(n))})},e.transporter.responsesCache.set({method:"search",args:r},{results:n})}if(!("transporter"in e)){var a="/1/indexes/*/queries_body_".concat(JSON.stringify({requests:r}));e.cache=eg(eg({},e.cache),{},ey({},a,JSON.stringify({results:Object.keys(t).map(function(e){return t[e].results})})))}}})(this.client,this._initialResults),n=this.mainHelper,a=Object.keys(i=this._initialResults).reduce(function(e,t){var r=i[t];return r.recommendResults?eO(eO({},e),r.recommendResults.results):e},{}),n._recommendCache=a;var n,i,a,o=this.scheduleSearch;this.scheduleSearch=ef(l.Z),ef(function(){e.scheduleSearch=o})()}else this.mainIndex.getWidgets().length>0&&this.scheduleSearch();this.helper=this.mainIndex.getHelper(),this.started=!0,this.middleware.forEach(function(e){e.instance.started()}),void 0===this._insights&&t.derivedHelpers[0].once("result",function(){e.mainIndex.getScopedResults().some(function(e){var t=e.results;return null==t?void 0:t._automaticInsights})&&e.use(S({$$internal:!0,$$automatic:!0}))})}},{key:"dispose",value:function(){var e;this.scheduleSearch.cancel(),this.scheduleRender.cancel(),clearTimeout(this._searchStalledTimer),this.removeWidgets(this.mainIndex.getWidgets()),this.mainIndex.dispose(),this.started=!1,this.removeAllListeners(),null===(e=this.mainHelper)||void 0===e||e.removeAllListeners(),this.mainHelper=null,this.helper=null,this.middleware.forEach(function(e){e.instance.unsubscribe()})}},{key:"scheduleStalledRender",value:function(){var e=this;this._searchStalledTimer||(this._searchStalledTimer=setTimeout(function(){e.status="stalled",e.scheduleRender()},this._stalledSearchDelay))}},{key:"setUiState",value:function(e){var t=this,r=!(arguments.length>1)||void 0===arguments[1]||arguments[1];if(!this.mainHelper)throw Error(eI("The `start` method needs to be called before `setUiState`."));this.mainIndex.refreshUiState();var n="function"==typeof e?e(this.mainIndex.getWidgetUiState({})):e;this.onStateChange&&r?this.onStateChange({uiState:n,setUiState:function(e){ej("function"==typeof e?e(n):e,t.mainIndex),t.scheduleSearch(),t.onInternalStateChange()}}):(ej(n,this.mainIndex),this.scheduleSearch(),this.onInternalStateChange())}},{key:"getUiState",value:function(){return this.started&&this.mainIndex.refreshUiState(),this.mainIndex.getWidgetUiState({})}},{key:"createURL",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!this.started)throw Error(eI("The `start` method needs to be called before `createURL`."));return this._createURL(e)}},{key:"refresh",value:function(){if(!this.mainHelper)throw Error(eI("The `start` method needs to be called before `refresh`."));this.mainHelper.clearCache().search()}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,eT(n.key),n)}}(i.prototype,r),Object.defineProperty(i,"prototype",{writable:!1}),i}(o),eH=r(61688),ek="7.15.4",eC=r(19160),eQ=r(89309),eL=r(58336),eU=r(37556),eW=r(628),e$=r(83454);function eq(e){return(eq="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function eB(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function eM(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eB(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=eq(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=eq(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==eq(t)?t:String(t)}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eB(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var eV=["react (".concat(n.version,")"),"react-instantsearch (".concat(ek,")"),"react-instantsearch-core (".concat(ek,")")],eJ="react-instantsearch-server (".concat(ek,")");function eZ(e,t){"function"==typeof e.addAlgoliaAgent&&t.filter(Boolean).forEach(function(t){e.addAlgoliaAgent(t)})}var ez=["children"];function eK(e){var t=e.children,r=function(e){var t=(0,eQ.N)(),r=(0,eL.a)(),i=(0,eU.s)(),a=(0,eW.P)(),o=null==i?void 0:i.initialResults,s=(0,n.useRef)(e),c=r||o||a,u=(0,n.useRef)(null);if(i&&(u=i.ssrSearchRef),null===u.current){var l,f,h,d=new eD(e);d._schedule=function(e){d._schedule.queue.push(e),clearTimeout(d._schedule.timer),d._schedule.timer=setTimeout(function(){d._schedule.queue.forEach(function(e){e()}),d._schedule.queue=[]},0)},d._schedule.queue=[],c&&(d._initialResults=o||{}),eZ(e.searchClient,[].concat(eV,[r&&eJ,(h="undefined"!=typeof window&&(null===(l=window.next)||void 0===l?void 0:l.version)||(void 0!==e$?null===(f=e$.env)||void 0===f?void 0:"":void 0))?"next.js (".concat(h,")"):null])),c&&d.start(),r&&r.notifyServer({search:d}),e.routing,u.current=d}var p,m=u.current,g=s.current;g.indexName!==e.indexName&&(m.helper.setIndex(e.indexName||"").search(),s.current=e),g.searchClient!==e.searchClient&&(eZ(e.searchClient,[].concat(eV,[r&&eJ])),m.mainHelper.setClient(e.searchClient).search(),s.current=e),g.onStateChange!==e.onStateChange&&(m.onStateChange=e.onStateChange,s.current=e),g.searchFunction!==e.searchFunction&&(m._searchFunction=e.searchFunction,s.current=e),g.stalledSearchDelay!==e.stalledSearchDelay&&(m._stalledSearchDelay=null!==(p=e.stalledSearchDelay)&&void 0!==p?p:200,s.current=e),(0,eC.J)(g.future,e.future)||(m.future=eM(eM({},eN),e.future),s.current=e);var y=(0,n.useRef)(null);return(0,eH.useSyncExternalStore)((0,n.useCallback)(function(){var e=u.current;return null===y.current?e.started||(e.start(),t()):(clearTimeout(y.current),e._preventWidgetCleanup=!1),function(){clearTimeout(e._schedule.timer),y.current=setTimeout(function(){e.dispose()}),e._preventWidgetCleanup=!0}},[t]),function(){return u.current},function(){return u.current})}(function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,ez));return r.started?n.createElement(a.Z.Provider,{value:r},n.createElement(i.Z.Provider,{value:r.mainIndex},t)):null}},2684:function(e,t,r){"use strict";r.d(t,{O:function(){return x}});var n=r(87456),i=r(11490),a=r(28537),o=r(34004);function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=l(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=l(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==l(t)?t:String(t)}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function l(e){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function f(e){var t,r=e.helper,n=e.widgetType,i=(e.methodName,e.args),a=e.instantSearchInstance;if(1===i.length&&"object"===l(i[0]))return[i[0]];var o=function(e){if(Array.isArray(e))return e}(t=i[0].split(":"))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,o,s=[],c=!0,u=!1;try{for(a=(r=r.call(e)).next;!(c=(n=a.call(r)).done)&&(s.push(n.value),2!==s.length);c=!0);}catch(e){u=!0,i=e}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(u)throw i}}return s}}(t,2)||function(e,t){if(e){if("string"==typeof e)return u(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return u(e,2)}}(t,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),s=o[0],f=o[1],h=i[1],d=i[2],p=i[3]||{};if(!h||("click"===s||"conversion"===s)&&!d)return[];var m=Array.isArray(h)?h:[h];if(0===m.length)return[];var g=m[0].__queryID,y=function(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20,r=[],n=0;n<Math.ceil(e.length/t);n++)r.push(e.slice(n*t,(n+1)*t));return r}(m),v=y.map(function(e){return e.map(function(e){return e.objectID})}),b=y.map(function(e){return e.map(function(e){return e.__position})});return"view"===s?"idle"!==a.status?[]:y.map(function(e,t){var i;return{insightsMethod:"viewedObjectIDs",widgetType:n,eventType:s,payload:c({eventName:d||"Hits Viewed",index:(null===(i=r.lastResults)||void 0===i?void 0:i.index)||r.state.index,objectIDs:v[t]},p),hits:e,eventModifier:f}}):"click"===s?y.map(function(e,t){var i;return{insightsMethod:"clickedObjectIDsAfterSearch",widgetType:n,eventType:s,payload:c({eventName:d||"Hit Clicked",index:(null===(i=r.lastResults)||void 0===i?void 0:i.index)||r.state.index,queryID:g,objectIDs:v[t],positions:b[t]},p),hits:e,eventModifier:f}}):"conversion"===s?y.map(function(e,t){var i;return{insightsMethod:"convertedObjectIDsAfterSearch",widgetType:n,eventType:s,payload:c({eventName:d||"Hit Converted",index:(null===(i=r.lastResults)||void 0===i?void 0:i.index)||r.state.index,queryID:g,objectIDs:v[t]},p),hits:e,eventModifier:f}}):[]}var h=r(90761);function d(e){return(d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function m(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=d(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=d(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==d(t)?t:String(t)}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function g(e){return(g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function y(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function v(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?y(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=g(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=g(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==g(t)?t:String(t)}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):y(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function b(e){return(b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function S(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function O(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?S(Object(r),!0).forEach(function(t){j(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):S(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function j(e,t,r){var n;return(n=function(e,t){if("object"!=b(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=b(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"),(t="symbol"==b(n)?n:String(n))in e)?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var R=(0,n.K)({name:"hits",connector:!0}),P=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i.Z;return(0,a._)(e,R()),function(r){var n,i,a=r||{},s=a.escapeHTML,c=void 0===s||s,u=a.transformItems,l=void 0===u?function(e){return e}:u;return{$$type:"ais.hits",init:function(t){e(O(O({},this.getWidgetRenderState(t)),{},{instantSearchInstance:t.instantSearchInstance}),!0)},render:function(t){var r=this.getWidgetRenderState(t);e(O(O({},r),{},{instantSearchInstance:t.instantSearchInstance}),!1),r.sendEvent("view:internal",r.items)},getRenderState:function(e,t){return O(O({},e),{},{hits:this.getWidgetRenderState(t)})},getWidgetRenderState:function(e){var t,a,s,u,d,p,g,y,b,S,O,j,R,P,w,x,F,_,E=e.results,T=e.helper,I=e.instantSearchInstance;if(n||(a=(t={instantSearchInstance:I,helper:T,widgetType:this.$$type}).instantSearchInstance,s=t.helper,u=t.widgetType,d={},p=void 0,n=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];f({widgetType:u,helper:s,methodName:"sendEvent",args:t,instantSearchInstance:a}).forEach(function(e){"click"===e.eventType&&"internal"===e.eventModifier&&d[e.eventType]||(d[e.eventType]=!0,a.sendEventToInsights(e))}),clearTimeout(p),p=setTimeout(function(){d={}},0)}),i||(y=(g={helper:T,widgetType:this.$$type,instantSearchInstance:I}).helper,b=g.widgetType,S=g.instantSearchInstance,i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=f({widgetType:b,helper:y,methodName:"bindEvent",args:t,instantSearchInstance:S});return n.length?"data-insights-event=".concat((0,o.a)(n)):""}),!E)return{hits:[],items:[],results:void 0,banner:void 0,sendEvent:n,bindEvent:i,widgetParams:r};c&&E.hits.length>0&&(E.hits=(0,h.mY)(E.hits));var A=l((O=E.hits,j=E.page,R=E.hitsPerPage,P=O.map(function(e,t){return m(m({},e),{},{__position:R*j+t+1})}),(w=E.queryID)?P.map(function(e){return v(v({},e),{},{__queryID:w})}):P),{results:E}),N=null===(x=E.renderingContent)||void 0===x?void 0:null===(F=x.widgets)||void 0===F?void 0:null===(_=F.banners)||void 0===_?void 0:_[0];return{hits:A,items:A,results:E,banner:N,sendEvent:n,bindEvent:i,widgetParams:r}},dispose:function(e){var r=e.state;return(t(),c)?r.setQueryParameters(Object.keys(h.dg).reduce(function(e,t){return O(O({},e),{},j({},t,void 0))},{})):r},getWidgetSearchParameters:function(e,t){return c?e.setQueryParameters(h.dg):e}}}},w=r(53874);function x(e,t){return(0,w.B)(P,e,t)}},37386:function(e,t,r){"use strict";r.d(t,{l:function(){return d}});var n=r(87456),i=r(11490),a=r(28537);function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=o(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=o(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==o(t)?t:String(t)}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var u=(0,n.K)({name:"search-box",connector:!0}),l=function(e,t){return t(e)},f=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i.Z;return(0,a._)(e,u()),function(r){var n,i,a=(r||{}).queryHook,o=void 0===a?l:a;return{$$type:"ais.searchBox",init:function(t){var r=t.instantSearchInstance;e(c(c({},this.getWidgetRenderState(t)),{},{instantSearchInstance:r}),!0)},render:function(t){var r=t.instantSearchInstance;e(c(c({},this.getWidgetRenderState(t)),{},{instantSearchInstance:r}),!1)},dispose:function(e){var r=e.state;return t(),r.setQueryParameter("query",void 0)},getRenderState:function(e,t){return c(c({},e),{},{searchBox:this.getWidgetRenderState(t)})},getWidgetRenderState:function(e){var t=e.helper,a=e.instantSearchInstance,s=e.state;return n||(n=function(e){o(e,function(e){return t.setQuery(e).search()})},i=function(){t.setQuery("").search()}),{query:s.query||"",refine:n,clear:i,widgetParams:r,isSearchStalled:"stalled"===a.status}},getWidgetUiState:function(e,t){var r=t.searchParameters.query||"";return""===r||e&&e.query===r?e:c(c({},e),{},{query:r})},getWidgetSearchParameters:function(e,t){var r=t.uiState;return e.setQueryParameter("query",r.query||"")}}}},h=r(53874);function d(e,t){return(0,h.B)(f,e,t)}},53874:function(e,t,r){"use strict";r.d(t,{B:function(){return b}});var n=r(67294),i=r(19160),a=r(87925),o=r(97406),s=r(80258),c=r(58336),u=r(37556),l=r(3353),f=r(33903);function h(e){return(h="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var d=["instantSearchInstance","widgetParams"],p=["widgetParams"];function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function g(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function y(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?g(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=h(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=h(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==h(t)?t:String(t)}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function v(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function b(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},h=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},g=(0,c.a)(),b=(0,u.s)(),S=(0,s.z)(),O=(0,o.a)(),j=(0,l.q)(r),R=(0,l.q)(h),P=(0,n.useRef)(!0),w=(0,n.useRef)(null),x=(0,n.useRef)(S.status),F=(0,n.useMemo)(function(){return y(y({},e(function(e,t){if(t){P.current=!0;return}if(P.current){var r=e.instantSearchInstance,n=(e.widgetParams,v(e,d));(0,i.J)(n,w.current,function(e,t){return(null==e?void 0:e.constructor)===Function&&(null==t?void 0:t.constructor)===Function})&&r.status===x.current||(T(n),w.current=n,x.current=r.status)}},function(){P.current=!1})(j)),R)},[e,j,R]),_=function(e){if(Array.isArray(e))return e}(t=(0,n.useState)(function(){if(F.getWidgetRenderState){var e,t=O.getHelper(),r=O.getWidgetUiState({})[O.getIndexId()];t.state=(null===(e=F.getWidgetSearchParameters)||void 0===e?void 0:e.call(F,t.state,{uiState:r}))||t.state;var n=(0,a.E)(O),i=n.results,o=n.scopedResults,s=n.recommendResults,c=F.getWidgetRenderState({helper:t,parent:O,instantSearchInstance:S,results:"recommend"===F.dependsOn&&s&&b?s[b.recommendIdx.current++]:i,scopedResults:o,state:t.state,renderState:S.renderState,templatesConfig:S.templatesConfig,createURL:O.createURL,searchMetadata:{isSearchStalled:"stalled"===S.status},status:S.status,error:S.error});return c.widgetParams,v(c,p)}return{}}))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,o,s=[],c=!0,u=!1;try{for(a=(r=r.call(e)).next;!(c=(n=a.call(r)).done)&&(s.push(n.value),2!==s.length);c=!0);}catch(e){u=!0,i=e}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(u)throw i}}return s}}(t,2)||function(e,t){if(e){if("string"==typeof e)return m(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return m(e,2)}}(t,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),E=_[0],T=_[1];return(0,f.F)({widget:F,parentIndex:O,props:j,shouldSsr:!!g}),E}},54942:function(e,t,r){"use strict";r.d(t,{b:function(){return h}});var n=r(67294),i=r(80258),a=r(94397),o=r(79649),s=r(87925),c=r(97406);function u(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function l(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,o,s=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(e){u=!0,i=e}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(u)throw i}}return s}}(e,t)||function(e,t){if(e){if("string"==typeof e)return f(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return f(e,t)}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function h(){var e,t,r,f,h,d,p,m,g,y,v,b,S,O,j,R,P,w,x,F=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},_=F.catchError,E=(0,i.z)(),T=(e=(0,i.z)(),r=(t=(0,c.a)()).getIndexId(),h=(f=l((0,n.useState)(function(){return e.getUiState()}),2))[0],d=f[1],p=h[r],g=(m=l((0,n.useState)(function(){return e.renderState}),2))[0],y=m[1],v=g[r]||{},b=(0,n.useCallback)(function(t){e.setUiState(t)},[e]),S=(0,n.useCallback)(function(e){t.setIndexUiState(e)},[t]),(0,n.useEffect)(function(){function t(){d(e.getUiState()),y(e.renderState)}return e.addListener("render",t),function(){e.removeListener("render",t)}},[e]),{uiState:h,setUiState:b,indexUiState:p,setIndexUiState:S,renderState:g,indexRenderState:v}),I=T.uiState,A=T.setUiState,N=T.indexUiState,D=T.setIndexUiState,H=T.renderState,k=T.indexRenderState,C=(O=(0,i.z)(),j=(0,c.a)(),w=(P=function(e){if(Array.isArray(e))return e}(R=(0,n.useState)(function(){var e=(0,s.E)(j);return{results:e.results,scopedResults:e.scopedResults}}))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,o,s=[],c=!0,u=!1;try{for(a=(r=r.call(e)).next;!(c=(n=a.call(r)).done)&&(s.push(n.value),2!==s.length);c=!0);}catch(e){u=!0,i=e}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(u)throw i}}return s}}(R,2)||function(e,t){if(e){if("string"==typeof e)return u(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return u(e,2)}}(R,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())[0],x=P[1],(0,n.useEffect)(function(){function e(){var e=j.getResults();if(null!==e)x({results:e,scopedResults:j.getScopedResults()});else if(0===O.mainIndex.getIndexName().length){var t=O.mainIndex.getWidgets().find(o.J);t&&x({results:(0,s.E)(j).results,scopedResults:t.getScopedResults()})}}return O.addListener("render",e),function(){O.removeListener("render",e)}},[O,j]),w),Q=C.results,L=C.scopedResults,U=(0,n.useCallback)(function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return E.use.apply(E,t),function(){E.unuse.apply(E,t)}},[E]),W=(0,n.useCallback)(function(){E.refresh()},[E]);return(0,a.L)(function(){if(_){var e=function(){};return E.addListener("error",e),function(){return E.removeListener("error",e)}}return function(){}},[E,_]),{results:Q,scopedResults:L,uiState:I,setUiState:A,indexUiState:N,setIndexUiState:D,renderState:H,indexRenderState:k,addMiddlewares:U,refresh:W,status:E.status,error:E.error}}},95930:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});var n=(0,r(67294).createContext)(null)},23130:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});var n=(0,r(67294).createContext)(null)},19160:function(e,t,r){"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}r.d(t,{J:function(){return function e(t,r,a){var o,s;if(null!=a&&a(t,r)||t===r)return!0;if(t&&r&&(o=t.constructor)===r.constructor){if(o===Date)return t.getTime()===r.getTime();if(o===RegExp)return t.toString()===r.toString();if(o===Array){if((s=t.length)===r.length)for(;s--&&e(t[s],r[s],a););return -1===s}if(!o||"object"===n(t)){for(o in s=0,t)if(i.call(t,o)&&++s&&!i.call(r,o)||!(o in r)||!e(t[o],r[o],a))return!1;return Object.keys(r).length===s}}return t!=t&&r!=r}}});var i=Object.prototype.hasOwnProperty},87925:function(e,t,r){"use strict";r.d(t,{E:function(){return c}});var n=r(8131);function i(e){var t,r,i;return new n.SearchResults(e,[{query:null!==(t=e.query)&&void 0!==t?t:"",page:null!==(r=e.page)&&void 0!==r?r:0,hitsPerPage:null!==(i=e.hitsPerPage)&&void 0!==i?i:20,hits:[],nbHits:0,nbPages:0,params:"",exhaustiveNbHits:!0,exhaustiveFacetsCount:!0,processingTimeMS:0,index:e.index}],{__isArtificial:!0})}function a(e){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=a(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=a(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==a(t)?t:String(t)}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function c(e){var t=e.getHelper(),r=e.getResults()||i(t.state),n=e.getScopedResults().map(function(t){var n=t.indexId===e.getIndexId()?r:i(t.helper.state);return s(s({},t),{},{results:t.results||n})});return{results:r,scopedResults:n,recommendResults:t.lastRecommendResults}}},91049:function(e,t,r){"use strict";function n(e,t){if(!e)throw Error("Invariant failed")}r.d(t,{k:function(){return n}})},89309:function(e,t,r){"use strict";r.d(t,{N:function(){return a}});var n=r(67294);function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function a(){var e;return(function(e){if(Array.isArray(e))return e}(e=(0,n.useReducer)(function(e){return e+1},0))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,o,s=[],c=!0,u=!1;try{for(a=(r=r.call(e)).next;!(c=(n=a.call(r)).done)&&(s.push(n.value),2!==s.length);c=!0);}catch(e){u=!0,i=e}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(u)throw i}}return s}}(e,2)||function(e,t){if(e){if("string"==typeof e)return i(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return i(e,2)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())[1]}},97406:function(e,t,r){"use strict";r.d(t,{a:function(){return o}});var n=r(67294),i=r(95930),a=r(91049);function o(){var e=(0,n.useContext)(i.Z);return(0,a.k)(null!==e,"The <Index> component must be used within <InstantSearch>."),e}},80258:function(e,t,r){"use strict";r.d(t,{z:function(){return o}});var n=r(67294),i=r(23130),a=r(91049);function o(){var e=(0,n.useContext)(i.Z);return(0,a.k)(null!==e,"Hooks must be used inside the <InstantSearch> component.\n\nThey are not compatible with the `react-instantsearch-core@6.x` and `react-instantsearch-dom` packages, so make sure to use the <InstantSearch> component from `react-instantsearch-core@7.x`."),e}},37556:function(e,t,r){"use strict";r.d(t,{s:function(){return a}});var n=r(67294),i=(0,n.createContext)(null);function a(){return(0,n.useContext)(i)}},58336:function(e,t,r){"use strict";r.d(t,{a:function(){return a}});var n=r(67294),i=(0,n.createContext)(null);function a(){return(0,n.useContext)(i)}},94397:function(e,t,r){"use strict";r.d(t,{L:function(){return i}});var n=r(67294),i="undefined"!=typeof window?n.useLayoutEffect:n.useEffect},628:function(e,t,r){"use strict";r.d(t,{P:function(){return a}});var n=r(67294),i=(0,n.createContext)(null);function a(){return(0,n.useContext)(i)}},3353:function(e,t,r){"use strict";r.d(t,{q:function(){return o}});var n=r(67294),i=r(19160);function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function o(e){var t,r=function(e){if(Array.isArray(e))return e}(t=(0,n.useState)(function(){return e}))||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,o,s=[],c=!0,u=!1;try{for(a=(r=r.call(e)).next;!(c=(n=a.call(r)).done)&&(s.push(n.value),2!==s.length);c=!0);}catch(e){u=!0,i=e}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(u)throw i}}return s}}(t,2)||function(e,t){if(e){if("string"==typeof e)return a(e,2);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return a(e,2)}}(t,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),o=r[0],s=r[1];return(0,i.J)(o,e)||s(e),o}},33903:function(e,t,r){"use strict";r.d(t,{F:function(){return l}});var n=r(67294),i=r.t(n,2),a=r(19160),o=i.use,s=r(80258),c=r(94397),u=r(628);function l(e){var t,r,i=e.widget,l=e.parentIndex,f=e.props,h=e.shouldSsr,d=(0,u.P)(),p=(0,n.useRef)(f);(0,n.useEffect)(function(){p.current=f},[f]);var m=(0,n.useRef)(i);(0,n.useEffect)(function(){m.current=i},[i]);var g=(0,n.useRef)(null),y=h&&!l.getWidgets().includes(i),v=(0,s.z)();(0,c.L)(function(){var e=m.current;return g.current?(clearTimeout(g.current),(0,a.J)(f,p.current)||(l.removeWidgets([e]),l.addWidgets([i]))):h||l.addWidgets([i]),function(){g.current=setTimeout(function(){v._schedule(function(){v._preventWidgetCleanup||l.removeWidgets([e])})})}},[l,i,h,v,f]),(y||(null==d?void 0:null===(t=d.current)||void 0===t?void 0:t.status)==="pending")&&l.addWidgets([i]),"undefined"==typeof window&&null!=d&&d.current&&"ais.index"!==i.$$type&&(o(d.current),"ais.dynamicWidgets"!==i.$$type&&null!==(r=v.helper)&&void 0!==r&&r.lastResults&&o(d.current))}},15022:function(e,t,r){"use strict";r.d(t,{A:function(){return h}});var n,i,a,o,s=r(87462),c=r(45987),u=r(53758),l=["parts","highlightedTagName","nonHighlightedTagName","separator","className","classNames"],f=r(67294),h=(i=(n={createElement:f.createElement,Fragment:f.Fragment}).createElement,a=n.Fragment,o=function(e){var t=e.classNames,r=e.children,n=e.highlightedTagName,a=e.isHighlighted,o=e.nonHighlightedTagName;return i(a?n:o,{className:a?t.highlighted:t.nonHighlighted},r)},function(e){var t=e.parts,r=e.highlightedTagName,n=void 0===r?"mark":r,f=e.nonHighlightedTagName,h=void 0===f?"span":f,d=e.separator,p=void 0===d?", ":d,m=e.className,g=e.classNames,y=void 0===g?{}:g,v=(0,c.Z)(e,l);return i("span",(0,s.Z)({},v,{className:(0,u.cx)(y.root,m)}),t.map(function(e,r){var s=r===t.length-1;return i(a,{key:r},e.map(function(e,t){return i(o,{key:t,classNames:y,highlightedTagName:n,nonHighlightedTagName:h,isHighlighted:e.isHighlighted},e.value)}),!s&&i("span",{className:y.separator},p))}))})},50012:function(e,t,r){"use strict";r.d(t,{y:function(){return p}});var n=r(54442),i=r(48361),a=r(22686),o=r(67294),s=r(53758),c=r(15022),u=["classNames"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function f(e){var t=e.classNames,r=void 0===t?{}:t,n=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,u);return o.createElement(c.A,l({classNames:{root:(0,s.cx)("ais-Highlight",r.root),highlighted:(0,s.cx)("ais-Highlight-highlighted",r.highlighted),nonHighlighted:(0,s.cx)("ais-Highlight-nonHighlighted",r.nonHighlighted),separator:(0,s.cx)("ais-Highlight-separator",r.separator)}},n))}var h=["hit","attribute","highlightedTagName","nonHighlightedTagName","separator"];function d(){return(d=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function p(e){var t=e.hit,r=e.attribute,s=e.highlightedTagName,c=e.nonHighlightedTagName,u=e.separator,l=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,h),p=(0,n.E)(t._highlightResult,r)||[],m=(Array.isArray(p)?p:[p]).map(function(e){return(0,i.H)((0,a.A)(e.value||""))});return o.createElement(f,d({},l,{parts:m,highlightedTagName:s,nonHighlightedTagName:c,separator:u}))}},51204:function(e,t,r){"use strict";r.d(t,{p:function(){return p}});var n=r(54442),i=r(48361),a=r(22686),o=r(67294),s=r(53758),c=r(15022),u=["classNames"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function f(e){var t=e.classNames,r=void 0===t?{}:t,n=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,u);return o.createElement(c.A,l({classNames:{root:(0,s.cx)("ais-Snippet",r.root),highlighted:(0,s.cx)("ais-Snippet-highlighted",r.highlighted),nonHighlighted:(0,s.cx)("ais-Snippet-nonHighlighted",r.nonHighlighted),separator:(0,s.cx)("ais-Snippet-separator",r.separator)}},n))}var h=["hit","attribute","highlightedTagName","nonHighlightedTagName","separator"];function d(){return(d=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function p(e){var t=e.hit,r=e.attribute,s=e.highlightedTagName,c=e.nonHighlightedTagName,u=e.separator,l=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r,n,i={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(i[r]=e[r]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,h),p=(0,n.E)(t._snippetResult,r)||[],m=(Array.isArray(p)?p:[p]).map(function(e){return(0,i.H)((0,a.A)(e.value||""))});return o.createElement(f,d({},l,{parts:m,highlightedTagName:s,nonHighlightedTagName:c,separator:u}))}}}]);