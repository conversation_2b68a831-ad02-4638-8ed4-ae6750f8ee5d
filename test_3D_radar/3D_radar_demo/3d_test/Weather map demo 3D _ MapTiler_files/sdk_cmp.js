(function() {
var Vi=Object.defineProperty;var Os=Object.getOwnPropertySymbols;var Ni=Object.prototype.hasOwnProperty,Ti=Object.prototype.propertyIsEnumerable;var xi=(N,I,D)=>I in N?Vi(N,I,{enumerable:!0,configurable:!0,writable:!0,value:D}):N[I]=D;var Ds=(N,I)=>{var D={};for(var M in N)Ni.call(N,M)&&I.indexOf(M)<0&&(D[M]=N[M]);if(N!=null&&Os)for(var M of Os(N))I.indexOf(M)<0&&Ti.call(N,M)&&(D[M]=N[M]);return D};var r=(N,I,D)=>xi(N,typeof I!="symbol"?I+"":I,D);var Pe=(N,I,D)=>new Promise((M,H)=>{var Oe=j=>{try{pe(D.next(j))}catch(ne){H(ne)}},De=j=>{try{pe(D.throw(j))}catch(ne){H(ne)}},pe=j=>j.done?M(j.value):Promise.resolve(j.value).then(Oe,De);pe((D=D.apply(N,I)).next())});(function(){"use strict";var cs,ls,us,ds,ps,hs,fs,_s,gs,vs,ms,bs,Cs,Ss,Is,ys,ws,Es,As,ks,Ls,Vs,Ns,Ts;var N,I,D,M,H,Oe,De,pe,j,ne,Ye,Ke,Se={},bt=[],Rs=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,Re=Array.isArray;function J(i,e){for(var t in e)i[t]=e[t];return i}function Xe(i){i&&i.parentNode&&i.parentNode.removeChild(i)}function l(i,e,t){var s,n,o,a={};for(o in e)o=="key"?s=e[o]:o=="ref"?n=e[o]:a[o]=e[o];if(arguments.length>2&&(a.children=arguments.length>3?N.call(arguments,2):t),typeof i=="function"&&i.defaultProps!=null)for(o in i.defaultProps)a[o]===void 0&&(a[o]=i.defaultProps[o]);return Me(i,a,s,n,null)}function Me(i,e,t,s,n){var o={type:i,props:e,key:t,ref:s,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:n==null?++D:n,__i:-1,__u:0};return n==null&&I.vnode!=null&&I.vnode(o),o}function ee(i){return i.children}function Ie(i,e){this.props=i,this.context=e}function he(i,e){if(e==null)return i.__?he(i.__,i.__i+1):null;for(var t;e<i.__k.length;e++)if((t=i.__k[e])!=null&&t.__e!=null)return t.__e;return typeof i.type=="function"?he(i):null}function Ct(i){var e,t;if((i=i.__)!=null&&i.__c!=null){for(i.__e=i.__c.base=null,e=0;e<i.__k.length;e++)if((t=i.__k[e])!=null&&t.__e!=null){i.__e=i.__c.base=t.__e;break}return Ct(i)}}function St(i){(!i.__d&&(i.__d=!0)&&H.push(i)&&!Fe.__r++||Oe!==I.debounceRendering)&&((Oe=I.debounceRendering)||De)(Fe)}function Fe(){for(var i,e,t,s,n,o,a,u=1;H.length;)H.length>u&&H.sort(pe),i=H.shift(),u=H.length,i.__d&&(t=void 0,n=(s=(e=i).__v).__e,o=[],a=[],e.__P&&((t=J({},s)).__v=s.__v+1,I.vnode&&I.vnode(t),Ze(e.__P,t,s,e.__n,e.__P.namespaceURI,32&s.__u?[n]:null,o,n==null?he(s):n,!!(32&s.__u),a),t.__v=s.__v,t.__.__k[t.__i]=t,At(o,t,a),t.__e!=n&&Ct(t)));Fe.__r=0}function It(i,e,t,s,n,o,a,u,c,h,g){var d,v,b,E,R,V,w=s&&s.__k||bt,A=e.length;for(c=Ms(t,e,w,c,A),d=0;d<A;d++)(b=t.__k[d])!=null&&(v=b.__i===-1?Se:w[b.__i]||Se,b.__i=d,V=Ze(i,b,v,n,o,a,u,c,h,g),E=b.__e,b.ref&&v.ref!=b.ref&&(v.ref&&et(v.ref,null,b),g.push(b.ref,b.__c||E,b)),R==null&&E!=null&&(R=E),4&b.__u||v.__k===b.__k?c=yt(b,c,i):typeof b.type=="function"&&V!==void 0?c=V:E&&(c=E.nextSibling),b.__u&=-7);return t.__e=R,c}function Ms(i,e,t,s,n){var o,a,u,c,h,g=t.length,d=g,v=0;for(i.__k=new Array(n),o=0;o<n;o++)(a=e[o])!=null&&typeof a!="boolean"&&typeof a!="function"?(c=o+v,(a=i.__k[o]=typeof a=="string"||typeof a=="number"||typeof a=="bigint"||a.constructor==String?Me(null,a,null,null,null):Re(a)?Me(ee,{children:a},null,null,null):a.constructor===void 0&&a.__b>0?Me(a.type,a.props,a.key,a.ref?a.ref:null,a.__v):a).__=i,a.__b=i.__b+1,u=null,(h=a.__i=Fs(a,t,c,d))!==-1&&(d--,(u=t[h])&&(u.__u|=2)),u==null||u.__v===null?(h==-1&&(n>g?v--:n<g&&v++),typeof a.type!="function"&&(a.__u|=4)):h!=c&&(h==c-1?v--:h==c+1?v++:(h>c?v--:v++,a.__u|=4))):i.__k[o]=null;if(d)for(o=0;o<g;o++)(u=t[o])!=null&&!(2&u.__u)&&(u.__e==s&&(s=he(u)),Lt(u,u));return s}function yt(i,e,t){var s,n;if(typeof i.type=="function"){for(s=i.__k,n=0;s&&n<s.length;n++)s[n]&&(s[n].__=i,e=yt(s[n],e,t));return e}i.__e!=e&&(e&&i.type&&!t.contains(e)&&(e=he(i)),t.insertBefore(i.__e,e||null),e=i.__e);do e=e&&e.nextSibling;while(e!=null&&e.nodeType==8);return e}function Fs(i,e,t,s){var n,o,a=i.key,u=i.type,c=e[t];if(c===null&&i.key==null||c&&a==c.key&&u===c.type&&!(2&c.__u))return t;if(s>(c!=null&&!(2&c.__u)?1:0))for(n=t-1,o=t+1;n>=0||o<e.length;){if(n>=0){if((c=e[n])&&!(2&c.__u)&&a==c.key&&u===c.type)return n;n--}if(o<e.length){if((c=e[o])&&!(2&c.__u)&&a==c.key&&u===c.type)return o;o++}}return-1}function wt(i,e,t){e[0]=="-"?i.setProperty(e,t==null?"":t):i[e]=t==null?"":typeof t!="number"||Rs.test(e)?t:t+"px"}function Ue(i,e,t,s,n){var o;e:if(e=="style")if(typeof t=="string")i.style.cssText=t;else{if(typeof s=="string"&&(i.style.cssText=s=""),s)for(e in s)t&&e in t||wt(i.style,e,"");if(t)for(e in t)s&&t[e]===s[e]||wt(i.style,e,t[e])}else if(e[0]=="o"&&e[1]=="n")o=e!=(e=e.replace(j,"$1")),e=e.toLowerCase()in i||e=="onFocusOut"||e=="onFocusIn"?e.toLowerCase().slice(2):e.slice(2),i.l||(i.l={}),i.l[e+o]=t,t?s?t.t=s.t:(t.t=ne,i.addEventListener(e,o?Ke:Ye,o)):i.removeEventListener(e,o?Ke:Ye,o);else{if(n=="http://www.w3.org/2000/svg")e=e.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if(e!="width"&&e!="height"&&e!="href"&&e!="list"&&e!="form"&&e!="tabIndex"&&e!="download"&&e!="rowSpan"&&e!="colSpan"&&e!="role"&&e!="popover"&&e in i)try{i[e]=t==null?"":t;break e}catch(a){}typeof t=="function"||(t==null||t===!1&&e[4]!="-"?i.removeAttribute(e):i.setAttribute(e,e=="popover"&&t==1?"":t))}}function Et(i){return function(e){if(this.l){var t=this.l[e.type+i];if(e.u==null)e.u=ne++;else if(e.u<t.t)return;return t(I.event?I.event(e):e)}}}function Ze(i,e,t,s,n,o,a,u,c,h){var g,d,v,b,E,R,V,w,A,X,z,be,le,ze,Ce,ue,Te,F=e.type;if(e.constructor!==void 0)return null;128&t.__u&&(c=!!(32&t.__u),o=[u=e.__e=t.__e]),(g=I.__b)&&g(e);e:if(typeof F=="function")try{if(w=e.props,A="prototype"in F&&F.prototype.render,X=(g=F.contextType)&&s[g.__c],z=g?X?X.props.value:g.__:s,t.__c?V=(d=e.__c=t.__c).__=d.__E:(A?e.__c=d=new F(w,z):(e.__c=d=new Ie(w,z),d.constructor=F,d.render=$s),X&&X.sub(d),d.props=w,d.state||(d.state={}),d.context=z,d.__n=s,v=d.__d=!0,d.__h=[],d._sb=[]),A&&d.__s==null&&(d.__s=d.state),A&&F.getDerivedStateFromProps!=null&&(d.__s==d.state&&(d.__s=J({},d.__s)),J(d.__s,F.getDerivedStateFromProps(w,d.__s))),b=d.props,E=d.state,d.__v=e,v)A&&F.getDerivedStateFromProps==null&&d.componentWillMount!=null&&d.componentWillMount(),A&&d.componentDidMount!=null&&d.__h.push(d.componentDidMount);else{if(A&&F.getDerivedStateFromProps==null&&w!==b&&d.componentWillReceiveProps!=null&&d.componentWillReceiveProps(w,z),!d.__e&&(d.shouldComponentUpdate!=null&&d.shouldComponentUpdate(w,d.__s,z)===!1||e.__v==t.__v)){for(e.__v!=t.__v&&(d.props=w,d.state=d.__s,d.__d=!1),e.__e=t.__e,e.__k=t.__k,e.__k.some(function(de){de&&(de.__=e)}),be=0;be<d._sb.length;be++)d.__h.push(d._sb[be]);d._sb=[],d.__h.length&&a.push(d);break e}d.componentWillUpdate!=null&&d.componentWillUpdate(w,d.__s,z),A&&d.componentDidUpdate!=null&&d.__h.push(function(){d.componentDidUpdate(b,E,R)})}if(d.context=z,d.props=w,d.__P=i,d.__e=!1,le=I.__r,ze=0,A){for(d.state=d.__s,d.__d=!1,le&&le(e),g=d.render(d.props,d.state,d.context),Ce=0;Ce<d._sb.length;Ce++)d.__h.push(d._sb[Ce]);d._sb=[]}else do d.__d=!1,le&&le(e),g=d.render(d.props,d.state,d.context),d.state=d.__s;while(d.__d&&++ze<25);d.state=d.__s,d.getChildContext!=null&&(s=J(J({},s),d.getChildContext())),A&&!v&&d.getSnapshotBeforeUpdate!=null&&(R=d.getSnapshotBeforeUpdate(b,E)),ue=g,g!=null&&g.type===ee&&g.key==null&&(ue=kt(g.props.children)),u=It(i,Re(ue)?ue:[ue],e,t,s,n,o,a,u,c,h),d.base=e.__e,e.__u&=-161,d.__h.length&&a.push(d),V&&(d.__E=d.__=null)}catch(de){if(e.__v=null,c||o!=null)if(de.then){for(e.__u|=c?160:128;u&&u.nodeType==8&&u.nextSibling;)u=u.nextSibling;o[o.indexOf(u)]=null,e.__e=u}else for(Te=o.length;Te--;)Xe(o[Te]);else e.__e=t.__e,e.__k=t.__k;I.__e(de,e,t)}else o==null&&e.__v==t.__v?(e.__k=t.__k,e.__e=t.__e):u=e.__e=Us(t.__e,e,t,s,n,o,a,c,h);return(g=I.diffed)&&g(e),128&e.__u?void 0:u}function At(i,e,t){for(var s=0;s<t.length;s++)et(t[s],t[++s],t[++s]);I.__c&&I.__c(e,i),i.some(function(n){try{i=n.__h,n.__h=[],i.some(function(o){o.call(n)})}catch(o){I.__e(o,n.__v)}})}function kt(i){return typeof i!="object"||i==null?i:Re(i)?i.map(kt):J({},i)}function Us(i,e,t,s,n,o,a,u,c){var h,g,d,v,b,E,R,V=t.props,w=e.props,A=e.type;if(A=="svg"?n="http://www.w3.org/2000/svg":A=="math"?n="http://www.w3.org/1998/Math/MathML":n||(n="http://www.w3.org/1999/xhtml"),o!=null){for(h=0;h<o.length;h++)if((b=o[h])&&"setAttribute"in b==!!A&&(A?b.localName==A:b.nodeType==3)){i=b,o[h]=null;break}}if(i==null){if(A==null)return document.createTextNode(w);i=document.createElementNS(n,A,w.is&&w),u&&(I.__m&&I.__m(e,o),u=!1),o=null}if(A===null)V===w||u&&i.data===w||(i.data=w);else{if(o=o&&N.call(i.childNodes),V=t.props||Se,!u&&o!=null)for(V={},h=0;h<i.attributes.length;h++)V[(b=i.attributes[h]).name]=b.value;for(h in V)if(b=V[h],h!="children"){if(h=="dangerouslySetInnerHTML")d=b;else if(!(h in w)){if(h=="value"&&"defaultValue"in w||h=="checked"&&"defaultChecked"in w)continue;Ue(i,h,null,b,n)}}for(h in w)b=w[h],h=="children"?v=b:h=="dangerouslySetInnerHTML"?g=b:h=="value"?E=b:h=="checked"?R=b:u&&typeof b!="function"||V[h]===b||Ue(i,h,b,V[h],n);if(g)u||d&&(g.__html===d.__html||g.__html===i.innerHTML)||(i.innerHTML=g.__html),e.__k=[];else if(d&&(i.innerHTML=""),It(e.type==="template"?i.content:i,Re(v)?v:[v],e,t,s,A=="foreignObject"?"http://www.w3.org/1999/xhtml":n,o,a,o?o[0]:t.__k&&he(t,0),u,c),o!=null)for(h=o.length;h--;)Xe(o[h]);u||(h="value",A=="progress"&&E==null?i.removeAttribute("value"):E!==void 0&&(E!==i[h]||A=="progress"&&!E||A=="option"&&E!==V[h])&&Ue(i,h,E,V[h],n),h="checked",R!==void 0&&R!==i[h]&&Ue(i,h,R,V[h],n))}return i}function et(i,e,t){try{if(typeof i=="function"){var s=typeof i.__u=="function";s&&i.__u(),s&&e==null||(i.__u=i(e))}else i.current=e}catch(n){I.__e(n,t)}}function Lt(i,e,t){var s,n;if(I.unmount&&I.unmount(i),(s=i.ref)&&(s.current&&s.current!==i.__e||et(s,null,e)),(s=i.__c)!=null){if(s.componentWillUnmount)try{s.componentWillUnmount()}catch(o){I.__e(o,e)}s.base=s.__P=null}if(s=i.__k)for(n=0;n<s.length;n++)s[n]&&Lt(s[n],e,t||typeof i.type!="function");t||Xe(i.__e),i.__c=i.__=i.__e=void 0}function $s(i,e,t){return this.constructor(i,t)}function Gs(i,e,t){var s,n,o,a;e==document&&(e=document.documentElement),I.__&&I.__(i,e),n=(s=!1)?null:e.__k,o=[],a=[],Ze(e,i=e.__k=l(ee,null,[i]),n||Se,Se,e.namespaceURI,n?null:e.firstChild?N.call(e.childNodes):null,o,n?n.__e:e.firstChild,s,a),At(o,i,a)}N=bt.slice,I={__e:function(i,e,t,s){for(var n,o,a;e=e.__;)if((n=e.__c)&&!n.__)try{if((o=n.constructor)&&o.getDerivedStateFromError!=null&&(n.setState(o.getDerivedStateFromError(i)),a=n.__d),n.componentDidCatch!=null&&(n.componentDidCatch(i,s||{}),a=n.__d),a)return n.__E=n}catch(u){i=u}throw i}},D=0,M=function(i){return i!=null&&i.constructor==null},Ie.prototype.setState=function(i,e){var t;t=this.__s!=null&&this.__s!==this.state?this.__s:this.__s=J({},this.state),typeof i=="function"&&(i=i(J({},t),this.props)),i&&J(t,i),i!=null&&this.__v&&(e&&this._sb.push(e),St(this))},Ie.prototype.forceUpdate=function(i){this.__v&&(this.__e=!0,i&&this.__h.push(i),St(this))},Ie.prototype.render=ee,H=[],De=typeof Promise=="function"?Promise.prototype.then.bind(Promise.resolve()):setTimeout,pe=function(i,e){return i.__v.__b-e.__v.__b},Fe.__r=0,j=/(PointerCapture)$|Capture$/i,ne=0,Ye=Et(!1),Ke=Et(!0);var P;(function(i){i.PING="ping",i.GET_TC_DATA="getTCData",i.GET_IN_APP_TC_DATA="getInAppTCData",i.GET_VENDOR_LIST="getVendorList",i.ADD_EVENT_LISTENER="addEventListener",i.REMOVE_EVENT_LISTENER="removeEventListener"})(P||(P={}));var oe;(function(i){i.STUB="stub",i.LOADING="loading",i.LOADED="loaded",i.ERROR="error"})(oe||(oe={}));var re;(function(i){i.VISIBLE="visible",i.HIDDEN="hidden",i.DISABLED="disabled"})(re||(re={}));var ye;(function(i){i.TC_LOADED="tcloaded",i.CMP_UI_SHOWN="cmpuishown",i.USER_ACTION_COMPLETE="useractioncomplete"})(ye||(ye={}));class $e{constructor(e,t,s,n){r(this,"listenerId");r(this,"callback");r(this,"next");r(this,"param");r(this,"success",!0);Object.assign(this,{callback:e,listenerId:s,param:t,next:n});try{this.respond()}catch(o){this.invokeCallback(null)}}invokeCallback(e){const t=e!==null;typeof this.next=="function"?this.callback(this.next,e,t):this.callback(e,t)}}class Ge extends $e{respond(){this.throwIfParamInvalid(),this.invokeCallback(new Vt(this.param,this.listenerId))}throwIfParamInvalid(){if(this.param!==void 0&&(!Array.isArray(this.param)||!this.param.every(Number.isInteger)))throw new Error("Invalid Parameter")}}class Hs{constructor(){r(this,"eventQueue",new Map);r(this,"queueNumber",0)}add(e){return this.eventQueue.set(this.queueNumber,e),this.queueNumber++}remove(e){return this.eventQueue.delete(e)}exec(){this.eventQueue.forEach((e,t)=>{new Ge(e.callback,e.param,t,e.next)})}clear(){this.queueNumber=0,this.eventQueue.clear()}get size(){return this.eventQueue.size}}class m{static reset(){delete this.cmpId,delete this.cmpVersion,delete this.eventStatus,delete this.gdprApplies,delete this.tcModel,delete this.tcString,delete this.tcfPolicyVersion,this.cmpStatus=oe.LOADING,this.disabled=!1,this.displayStatus=re.HIDDEN,this.eventQueue.clear()}}r(m,"apiVersion","2"),r(m,"tcfPolicyVersion"),r(m,"eventQueue",new Hs),r(m,"cmpStatus",oe.LOADING),r(m,"disabled",!1),r(m,"displayStatus",re.HIDDEN),r(m,"cmpId"),r(m,"cmpVersion"),r(m,"eventStatus"),r(m,"gdprApplies"),r(m,"tcModel"),r(m,"tcString");class tt{constructor(){r(this,"cmpId",m.cmpId);r(this,"cmpVersion",m.cmpVersion);r(this,"gdprApplies",m.gdprApplies);r(this,"tcfPolicyVersion",m.tcfPolicyVersion)}}class js extends tt{constructor(){super(...arguments);r(this,"cmpStatus",oe.ERROR)}}class Vt extends tt{constructor(t,s){super();r(this,"tcString");r(this,"listenerId");r(this,"eventStatus");r(this,"cmpStatus");r(this,"isServiceSpecific");r(this,"useNonStandardTexts");r(this,"publisherCC");r(this,"purposeOneTreatment");r(this,"outOfBand");r(this,"purpose");r(this,"vendor");r(this,"specialFeatureOptins");r(this,"publisher");if(this.eventStatus=m.eventStatus,this.cmpStatus=m.cmpStatus,this.listenerId=s,m.gdprApplies){const n=m.tcModel;this.tcString=m.tcString,this.isServiceSpecific=n.isServiceSpecific,this.useNonStandardTexts=n.useNonStandardTexts,this.purposeOneTreatment=n.purposeOneTreatment,this.publisherCC=n.publisherCountryCode,this.outOfBand={allowedVendors:this.createVectorField(n.vendorsAllowed,t),disclosedVendors:this.createVectorField(n.vendorsDisclosed,t)},this.purpose={consents:this.createVectorField(n.purposeConsents),legitimateInterests:this.createVectorField(n.purposeLegitimateInterests)},this.vendor={consents:this.createVectorField(n.vendorConsents,t),legitimateInterests:this.createVectorField(n.vendorLegitimateInterests,t)},this.specialFeatureOptins=this.createVectorField(n.specialFeatureOptins),this.publisher={consents:this.createVectorField(n.publisherConsents),legitimateInterests:this.createVectorField(n.publisherLegitimateInterests),customPurpose:{consents:this.createVectorField(n.publisherCustomConsents),legitimateInterests:this.createVectorField(n.publisherCustomLegitimateInterests)},restrictions:this.createRestrictions(n.publisherRestrictions)}}}createRestrictions(t){const s={};if(t.numRestrictions>0){const n=t.getMaxVendorId();for(let o=1;o<=n;o++){const a=o.toString();t.getRestrictions(o).forEach(u=>{const c=u.purposeId.toString();s[c]||(s[c]={}),s[c][a]=u.restrictionType})}}return s}createVectorField(t,s){return s?s.reduce((n,o)=>(n[String(o)]=t.has(Number(o)),n),{}):[...t].reduce((n,o)=>(n[o[0].toString(10)]=o[1],n),{})}}class Bs extends Vt{constructor(e){super(e),delete this.outOfBand}createVectorField(e){return[...e].reduce((t,s)=>(t+=s[1]?"1":"0",t),"")}createRestrictions(e){const t={};if(e.numRestrictions>0){const s=e.getMaxVendorId();e.getRestrictions().forEach(n=>{t[n.purposeId.toString()]="_".repeat(s)});for(let n=0;n<s;n++){const o=n+1;e.getRestrictions(o).forEach(a=>{const u=a.restrictionType.toString(),c=a.purposeId.toString(),h=t[c].substr(0,n),g=t[c].substr(n+1);t[c]=h+u+g})}}return t}}class Qs extends tt{constructor(){super();r(this,"cmpLoaded",!0);r(this,"cmpStatus",m.cmpStatus);r(this,"displayStatus",m.displayStatus);r(this,"apiVersion",String(m.apiVersion));r(this,"gvlVersion");m.tcModel&&m.tcModel.vendorListVersion&&(this.gvlVersion=+m.tcModel.vendorListVersion)}}class Ws extends $e{respond(){this.invokeCallback(new Qs)}}class qs extends Ge{respond(){this.throwIfParamInvalid(),this.invokeCallback(new Bs(this.param))}}class te extends Error{constructor(e){super(e),this.name="DecodingError"}}class Y extends Error{constructor(e){super(e),this.name="EncodingError"}}class we extends Error{constructor(e){super(e),this.name="GVLError"}}class K extends Error{constructor(e,t){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"";super(`invalid value ${t} passed for ${e} ${s}`),this.name="TCModelError"}}class ae{static encode(e){if(!/^[0-1]+$/.test(e))throw new Y("Invalid bitField");const t=e.length%this.LCM;e+=t?"0".repeat(this.LCM-t):"";let s="";for(let n=0;n<e.length;n+=this.BASIS)s+=this.DICT[parseInt(e.substr(n,this.BASIS),2)];return s}static decode(e){if(!/^[A-Za-z0-9\-_]+$/.test(e))throw new te("Invalidly encoded Base64URL string");let t="";for(let s=0;s<e.length;s++){const n=this.REVERSE_DICT.get(e[s]).toString(2);t+="0".repeat(this.BASIS-n.length)+n}return t}}r(ae,"DICT","ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"),r(ae,"REVERSE_DICT",new Map([["A",0],["B",1],["C",2],["D",3],["E",4],["F",5],["G",6],["H",7],["I",8],["J",9],["K",10],["L",11],["M",12],["N",13],["O",14],["P",15],["Q",16],["R",17],["S",18],["T",19],["U",20],["V",21],["W",22],["X",23],["Y",24],["Z",25],["a",26],["b",27],["c",28],["d",29],["e",30],["f",31],["g",32],["h",33],["i",34],["j",35],["k",36],["l",37],["m",38],["n",39],["o",40],["p",41],["q",42],["r",43],["s",44],["t",45],["u",46],["v",47],["w",48],["x",49],["y",50],["z",51],["0",52],["1",53],["2",54],["3",55],["4",56],["5",57],["6",58],["7",59],["8",60],["9",61],["-",62],["_",63]])),r(ae,"BASIS",6),r(ae,"LCM",24);const q=class q{has(e){return q.langSet.has(e)}parseLanguage(e){e=e.toUpperCase();const t=e.split("-")[0];if(e.length>=2&&t.length==2){if(q.langSet.has(e))return e;if(q.langSet.has(t))return t;const s=t+"-"+t;if(q.langSet.has(s))return s;for(const n of q.langSet)if(n.indexOf(e)!==-1||n.indexOf(t)!==-1)return n}throw new Error(`unsupported language ${e}`)}forEach(e){q.langSet.forEach(e)}get size(){return q.langSet.size}};r(q,"langSet",new Set(["AR","BG","BS","CA","CS","DA","DE","EL","EN","ES","ET","EU","FI","FR","GL","HR","HU","IT","JA","LT","LV","MT","NL","NO","PL","PT-BR","PT-PT","RO","RU","SK","SL","SR-LATN","SR-CYRL","SV","TR","ZH"]));let st=q;class p{}r(p,"cmpId","cmpId"),r(p,"cmpVersion","cmpVersion"),r(p,"consentLanguage","consentLanguage"),r(p,"consentScreen","consentScreen"),r(p,"created","created"),r(p,"supportOOB","supportOOB"),r(p,"isServiceSpecific","isServiceSpecific"),r(p,"lastUpdated","lastUpdated"),r(p,"numCustomPurposes","numCustomPurposes"),r(p,"policyVersion","policyVersion"),r(p,"publisherCountryCode","publisherCountryCode"),r(p,"publisherCustomConsents","publisherCustomConsents"),r(p,"publisherCustomLegitimateInterests","publisherCustomLegitimateInterests"),r(p,"publisherLegitimateInterests","publisherLegitimateInterests"),r(p,"publisherConsents","publisherConsents"),r(p,"publisherRestrictions","publisherRestrictions"),r(p,"purposeConsents","purposeConsents"),r(p,"purposeLegitimateInterests","purposeLegitimateInterests"),r(p,"purposeOneTreatment","purposeOneTreatment"),r(p,"specialFeatureOptins","specialFeatureOptins"),r(p,"useNonStandardTexts","useNonStandardTexts"),r(p,"vendorConsents","vendorConsents"),r(p,"vendorLegitimateInterests","vendorLegitimateInterests"),r(p,"vendorListVersion","vendorListVersion"),r(p,"vendorsAllowed","vendorsAllowed"),r(p,"vendorsDisclosed","vendorsDisclosed"),r(p,"version","version");class Ee{clone(){const e=new this.constructor;return Object.keys(this).forEach(s=>{const n=this.deepClone(this[s]);n!==void 0&&(e[s]=n)}),e}deepClone(e){const t=typeof e;if(t==="number"||t==="string"||t==="boolean")return e;if(e!==null&&t==="object"){if(typeof e.clone=="function")return e.clone();if(e instanceof Date)return new Date(e.getTime());if(e[Symbol.iterator]!==void 0){const s=[];for(const n of e)s.push(this.deepClone(n));return e instanceof Array?s:new e.constructor(s)}else{const s={};for(const n in e)e.hasOwnProperty(n)&&(s[n]=this.deepClone(e[n]));return s}}}}var B;(function(i){i[i.NOT_ALLOWED=0]="NOT_ALLOWED",i[i.REQUIRE_CONSENT=1]="REQUIRE_CONSENT",i[i.REQUIRE_LI=2]="REQUIRE_LI"})(B||(B={}));const Ne=class Ne extends Ee{constructor(t,s){super();r(this,"purposeId_");r(this,"restrictionType");t!==void 0&&(this.purposeId=t),s!==void 0&&(this.restrictionType=s)}static unHash(t){const s=t.split(this.hashSeparator),n=new Ne;if(s.length!==2)throw new K("hash",t);return n.purposeId=parseInt(s[0],10),n.restrictionType=parseInt(s[1],10),n}get hash(){if(!this.isValid())throw new Error("cannot hash invalid PurposeRestriction");return`${this.purposeId}${Ne.hashSeparator}${this.restrictionType}`}get purposeId(){return this.purposeId_}set purposeId(t){this.purposeId_=t}isValid(){return Number.isInteger(this.purposeId)&&this.purposeId>0&&(this.restrictionType===B.NOT_ALLOWED||this.restrictionType===B.REQUIRE_CONSENT||this.restrictionType===B.REQUIRE_LI)}isSameAs(t){return this.purposeId===t.purposeId&&this.restrictionType===t.restrictionType}};r(Ne,"hashSeparator","-");let ce=Ne;class Nt extends Ee{constructor(){super(...arguments);r(this,"bitLength",0);r(this,"map",new Map);r(this,"gvl_")}has(t){return this.map.has(t)}isOkToHave(t,s,n){var a;let o=!0;if((a=this.gvl)!=null&&a.vendors){const u=this.gvl.vendors[n];if(u)if(t===B.NOT_ALLOWED)o=u.legIntPurposes.includes(s)||u.purposes.includes(s);else if(u.flexiblePurposes.length)switch(t){case B.REQUIRE_CONSENT:o=u.flexiblePurposes.includes(s)&&u.legIntPurposes.includes(s);break;case B.REQUIRE_LI:o=u.flexiblePurposes.includes(s)&&u.purposes.includes(s);break}else o=!1;else o=!1}return o}add(t,s){if(this.isOkToHave(s.restrictionType,s.purposeId,t)){const n=s.hash;this.has(n)||(this.map.set(n,new Set),this.bitLength=0),this.map.get(n).add(t)}}restrictPurposeToLegalBasis(t){const s=Array.from(this.gvl.vendorIds),n=t.hash,o=s[s.length-1],a=[...Array(o).keys()].map(u=>u+1);if(!this.has(n))this.map.set(n,new Set(a)),this.bitLength=0;else for(let u=1;u<=o;u++)this.map.get(n).add(u)}getVendors(t){let s=[];if(t){const n=t.hash;this.has(n)&&(s=Array.from(this.map.get(n)))}else{const n=new Set;this.map.forEach(o=>{o.forEach(a=>{n.add(a)})}),s=Array.from(n)}return s.sort((n,o)=>n-o)}getRestrictionType(t,s){let n;return this.getRestrictions(t).forEach(o=>{o.purposeId===s&&(n===void 0||n>o.restrictionType)&&(n=o.restrictionType)}),n}vendorHasRestriction(t,s){let n=!1;const o=this.getRestrictions(t);for(let a=0;a<o.length&&!n;a++)n=s.isSameAs(o[a]);return n}getMaxVendorId(){let t=0;return this.map.forEach(s=>{t=Math.max(Array.from(s)[s.size-1],t)}),t}getRestrictions(t){const s=[];return this.map.forEach((n,o)=>{t?n.has(t)&&s.push(ce.unHash(o)):s.push(ce.unHash(o))}),s}getPurposes(){const t=new Set;return this.map.forEach((s,n)=>{t.add(ce.unHash(n).purposeId)}),Array.from(t)}remove(t,s){const n=s.hash,o=this.map.get(n);o&&(o.delete(t),o.size==0&&(this.map.delete(n),this.bitLength=0))}set gvl(t){this.gvl_||(this.gvl_=t,this.map.forEach((s,n)=>{const o=ce.unHash(n);Array.from(s).forEach(u=>{this.isOkToHave(o.restrictionType,o.purposeId,u)||s.delete(u)})}))}get gvl(){return this.gvl_}isEmpty(){return this.map.size===0}get numRestrictions(){return this.map.size}}var Tt;(function(i){i.COOKIE="cookie",i.WEB="web",i.APP="app"})(Tt||(Tt={}));var L;(function(i){i.CORE="core",i.VENDORS_DISCLOSED="vendorsDisclosed",i.VENDORS_ALLOWED="vendorsAllowed",i.PUBLISHER_TC="publisherTC"})(L||(L={}));class He{}r(He,"ID_TO_KEY",[L.CORE,L.VENDORS_DISCLOSED,L.VENDORS_ALLOWED,L.PUBLISHER_TC]),r(He,"KEY_TO_ID",{[L.CORE]:0,[L.VENDORS_DISCLOSED]:1,[L.VENDORS_ALLOWED]:2,[L.PUBLISHER_TC]:3});class U extends Ee{constructor(){super(...arguments);r(this,"bitLength",0);r(this,"maxId_",0);r(this,"set_",new Set)}*[Symbol.iterator](){for(let t=1;t<=this.maxId;t++)yield[t,this.has(t)]}values(){return this.set_.values()}get maxId(){return this.maxId_}has(t){return this.set_.has(t)}unset(t){Array.isArray(t)?t.forEach(s=>this.unset(s)):typeof t=="object"?this.unset(Object.keys(t).map(s=>Number(s))):(this.set_.delete(Number(t)),this.bitLength=0,t===this.maxId&&(this.maxId_=0,this.set_.forEach(s=>{this.maxId_=Math.max(this.maxId,s)})))}isIntMap(t){let s=typeof t=="object";return s=s&&Object.keys(t).every(n=>{let o=Number.isInteger(parseInt(n,10));return o=o&&this.isValidNumber(t[n].id),o=o&&t[n].name!==void 0,o}),s}isValidNumber(t){return parseInt(t,10)>0}isSet(t){let s=!1;return t instanceof Set&&(s=Array.from(t).every(this.isValidNumber)),s}set(t){if(Array.isArray(t))t.forEach(s=>this.set(s));else if(this.isSet(t))this.set(Array.from(t));else if(this.isIntMap(t))this.set(Object.keys(t).map(s=>Number(s)));else if(this.isValidNumber(t))this.set_.add(t),this.maxId_=Math.max(this.maxId,t),this.bitLength=0;else throw new K("set()",t,"must be positive integer array, positive integer, Set<number>, or IntMap")}empty(){this.set_=new Set}forEach(t){for(let s=1;s<=this.maxId;s++)t(this.has(s),s)}get size(){return this.set_.size}setAll(t){this.set(t)}}Es=p.cmpId,ws=p.cmpVersion,ys=p.consentLanguage,Is=p.consentScreen,Ss=p.created,Cs=p.isServiceSpecific,bs=p.lastUpdated,ms=p.policyVersion,vs=p.publisherCountryCode,gs=p.publisherLegitimateInterests,_s=p.publisherConsents,fs=p.purposeConsents,hs=p.purposeLegitimateInterests,ps=p.purposeOneTreatment,ds=p.specialFeatureOptins,us=p.useNonStandardTexts,ls=p.vendorListVersion,cs=p.version;class _{}r(_,Es,12),r(_,ws,12),r(_,ys,12),r(_,Is,6),r(_,Ss,36),r(_,Cs,1),r(_,bs,36),r(_,ms,6),r(_,vs,12),r(_,gs,24),r(_,_s,24),r(_,fs,24),r(_,hs,24),r(_,ps,1),r(_,ds,12),r(_,us,1),r(_,ls,12),r(_,cs,6),r(_,"anyBoolean",1),r(_,"encodingType",1),r(_,"maxId",16),r(_,"numCustomPurposes",6),r(_,"numEntries",12),r(_,"numRestrictions",12),r(_,"purposeId",6),r(_,"restrictionType",2),r(_,"segmentType",3),r(_,"singleOrRange",1),r(_,"vendorId",16);class Q{static encode(e){return String(Number(e))}static decode(e){return e==="1"}}class y{static encode(e,t){let s;if(typeof e=="string"&&(e=parseInt(e,10)),s=e.toString(2),s.length>t||e<0)throw new Y(`${e} too large to encode into ${t}`);return s.length<t&&(s="0".repeat(t-s.length)+s),s}static decode(e,t){if(t!==e.length)throw new te("invalid bit length");return parseInt(e,2)}}class xt{static encode(e,t){return y.encode(Math.round(e.getTime()/100),t)}static decode(e,t){if(t!==e.length)throw new te("invalid bit length");const s=new Date;return s.setTime(y.decode(e,t)*100),s}}class se{static encode(e,t){let s="";for(let n=1;n<=t;n++)s+=Q.encode(e.has(n));return s}static decode(e,t){if(e.length!==t)throw new te("bitfield encoding length mismatch");const s=new U;for(let n=1;n<=t;n++)Q.decode(e[n-1])&&s.set(n);return s.bitLength=e.length,s}}class Pt{static encode(e,t){e=e.toUpperCase();const s=65,n=e.charCodeAt(0)-s,o=e.charCodeAt(1)-s;if(n<0||n>25||o<0||o>25)throw new Y(`invalid language code: ${e}`);if(t%2===1)throw new Y(`numBits must be even, ${t} is not valid`);t=t/2;const a=y.encode(n,t),u=y.encode(o,t);return a+u}static decode(e,t){let s;if(t===e.length&&!(e.length%2)){const o=e.length/2,a=y.decode(e.slice(0,o),o)+65,u=y.decode(e.slice(o),o)+65;s=String.fromCharCode(a)+String.fromCharCode(u)}else throw new te("invalid bit length for language");return s}}class zs{static encode(e){let t=y.encode(e.numRestrictions,_.numRestrictions);if(!e.isEmpty()){const s=(n,o)=>{for(let a=n+1;a<=o;a++)if(e.gvl.vendorIds.has(a))return a;return n};e.getRestrictions().forEach(n=>{t+=y.encode(n.purposeId,_.purposeId),t+=y.encode(n.restrictionType,_.restrictionType);const o=e.getVendors(n),a=o.length;let u=0,c=0,h="";for(let g=0;g<a;g++){const d=o[g];if(c===0&&(u++,c=d),g===a-1||o[g+1]>s(d,o[a-1])){const v=d!==c;h+=Q.encode(v),h+=y.encode(c,_.vendorId),v&&(h+=y.encode(d,_.vendorId)),c=0}}t+=y.encode(u,_.numEntries),t+=h})}return t}static decode(e){let t=0;const s=new Nt,n=y.decode(e.substr(t,_.numRestrictions),_.numRestrictions);t+=_.numRestrictions;for(let o=0;o<n;o++){const a=y.decode(e.substr(t,_.purposeId),_.purposeId);t+=_.purposeId;const u=y.decode(e.substr(t,_.restrictionType),_.restrictionType);t+=_.restrictionType;const c=new ce(a,u),h=y.decode(e.substr(t,_.numEntries),_.numEntries);t+=_.numEntries;for(let g=0;g<h;g++){const d=Q.decode(e.substr(t,_.anyBoolean));t+=_.anyBoolean;const v=y.decode(e.substr(t,_.vendorId),_.vendorId);if(t+=_.vendorId,d){const b=y.decode(e.substr(t,_.vendorId),_.vendorId);if(t+=_.vendorId,b<v)throw new te(`Invalid RangeEntry: endVendorId ${b} is less than ${v}`);for(let E=v;E<=b;E++)s.add(E,c)}else s.add(v,c)}}return s.bitLength=t,s}}var Ae;(function(i){i[i.FIELD=0]="FIELD",i[i.RANGE=1]="RANGE"})(Ae||(Ae={}));class ke{static encode(e){const t=[];let s=[],n=y.encode(e.maxId,_.maxId),o="",a;const u=_.maxId+_.encodingType,c=u+e.maxId,h=_.vendorId*2+_.singleOrRange+_.numEntries;let g=u+_.numEntries;return e.forEach((d,v)=>{o+=Q.encode(d),a=e.maxId>h&&g<c,a&&d&&(e.has(v+1)?s.length===0&&(s.push(v),g+=_.singleOrRange,g+=_.vendorId):(s.push(v),g+=_.vendorId,t.push(s),s=[]))}),a?(n+=String(Ae.RANGE),n+=this.buildRangeEncoding(t)):(n+=String(Ae.FIELD),n+=o),n}static decode(e,t){let s,n=0;const o=y.decode(e.substr(n,_.maxId),_.maxId);n+=_.maxId;const a=y.decode(e.charAt(n),_.encodingType);if(n+=_.encodingType,a===Ae.RANGE){if(s=new U,t===1){if(e.substr(n,1)==="1")throw new te("Unable to decode default consent=1");n++}const u=y.decode(e.substr(n,_.numEntries),_.numEntries);n+=_.numEntries;for(let c=0;c<u;c++){const h=Q.decode(e.charAt(n));n+=_.singleOrRange;const g=y.decode(e.substr(n,_.vendorId),_.vendorId);if(n+=_.vendorId,h){const d=y.decode(e.substr(n,_.vendorId),_.vendorId);n+=_.vendorId;for(let v=g;v<=d;v++)s.set(v)}else s.set(g)}}else{const u=e.substr(n,o);n+=o,s=se.decode(u,o)}return s.bitLength=n,s}static buildRangeEncoding(e){const t=e.length;let s=y.encode(t,_.numEntries);return e.forEach(n=>{const o=n.length===1;s+=Q.encode(!o),s+=y.encode(n[0],_.vendorId),o||(s+=y.encode(n[1],_.vendorId))}),s}}function Ot(){return{[p.version]:y,[p.created]:xt,[p.lastUpdated]:xt,[p.cmpId]:y,[p.cmpVersion]:y,[p.consentScreen]:y,[p.consentLanguage]:Pt,[p.vendorListVersion]:y,[p.policyVersion]:y,[p.isServiceSpecific]:Q,[p.useNonStandardTexts]:Q,[p.specialFeatureOptins]:se,[p.purposeConsents]:se,[p.purposeLegitimateInterests]:se,[p.purposeOneTreatment]:Q,[p.publisherCountryCode]:Pt,[p.vendorConsents]:ke,[p.vendorLegitimateInterests]:ke,[p.publisherRestrictions]:zs,segmentType:y,[p.vendorsDisclosed]:ke,[p.vendorsAllowed]:ke,[p.publisherConsents]:se,[p.publisherLegitimateInterests]:se,[p.numCustomPurposes]:y,[p.publisherCustomConsents]:se,[p.publisherCustomLegitimateInterests]:se}}class Js{constructor(){r(this,1,{[L.CORE]:[p.version,p.created,p.lastUpdated,p.cmpId,p.cmpVersion,p.consentScreen,p.consentLanguage,p.vendorListVersion,p.purposeConsents,p.vendorConsents]});r(this,2,{[L.CORE]:[p.version,p.created,p.lastUpdated,p.cmpId,p.cmpVersion,p.consentScreen,p.consentLanguage,p.vendorListVersion,p.policyVersion,p.isServiceSpecific,p.useNonStandardTexts,p.specialFeatureOptins,p.purposeConsents,p.purposeLegitimateInterests,p.purposeOneTreatment,p.publisherCountryCode,p.vendorConsents,p.vendorLegitimateInterests,p.publisherRestrictions],[L.PUBLISHER_TC]:[p.publisherConsents,p.publisherLegitimateInterests,p.numCustomPurposes,p.publisherCustomConsents,p.publisherCustomLegitimateInterests],[L.VENDORS_ALLOWED]:[p.vendorsAllowed],[L.VENDORS_DISCLOSED]:[p.vendorsDisclosed]})}}class Ys{constructor(e,t){r(this,1,[L.CORE]);r(this,2,[L.CORE]);if(e.version===2)if(e.isServiceSpecific)this[2].push(L.PUBLISHER_TC);else{const s=!!(t&&t.isForVendors);(!s||e[p.supportOOB]===!0)&&this[2].push(L.VENDORS_DISCLOSED),s&&(e[p.supportOOB]&&e[p.vendorsAllowed].size>0&&this[2].push(L.VENDORS_ALLOWED),this[2].push(L.PUBLISHER_TC))}}}class it{static encode(e,t){let s;try{s=this.fieldSequence[String(e.version)][t]}catch(a){throw new Y(`Unable to encode version: ${e.version}, segment: ${t}`)}let n="";t!==L.CORE&&(n=y.encode(He.KEY_TO_ID[t],_.segmentType));const o=Ot();return s.forEach(a=>{const u=e[a],c=o[a];let h=_[a];h===void 0&&this.isPublisherCustom(a)&&(h=Number(e[p.numCustomPurposes]));try{n+=c.encode(u,h)}catch(g){throw new Y(`Error encoding ${t}->${a}: ${g.message}`)}}),ae.encode(n)}static decode(e,t,s){const n=ae.decode(e);let o=0;s===L.CORE&&(t.version=y.decode(n.substr(o,_[p.version]),_[p.version])),s!==L.CORE&&(o+=_.segmentType);const a=this.fieldSequence[String(t.version)][s],u=Ot();return a.forEach(c=>{const h=u[c];let g=_[c];if(g===void 0&&this.isPublisherCustom(c)&&(g=Number(t[p.numCustomPurposes])),g!==0){const d=n.substr(o,g);if(h===ke?t[c]=h.decode(d,t.version):t[c]=h.decode(d,g),Number.isInteger(g))o+=g;else if(Number.isInteger(t[c].bitLength))o+=t[c].bitLength;else throw new te(c)}}),t}static isPublisherCustom(e){return e.indexOf("publisherCustom")===0}}r(it,"fieldSequence",new Js);class Dt{static process(e,t){const s=e.gvl;if(!s)throw new Y("Unable to encode TCModel without a GVL");if(!s.isReady)throw new Y("Unable to encode TCModel tcModel.gvl.readyPromise is not resolved");e=e.clone(),e.consentLanguage=s.language.slice(0,2).toUpperCase(),(t==null?void 0:t.version)>0&&(t==null?void 0:t.version)<=this.processor.length?e.version=t.version:e.version=this.processor.length;const n=e.version-1;if(!this.processor[n])throw new Y(`Invalid version: ${e.version}`);return this.processor[n](e,s)}}r(Dt,"processor",[e=>e,(e,t)=>{e.publisherRestrictions.gvl=t,e.purposeLegitimateInterests.unset([1,3,4,5,6]);const s=new Map;return s.set("legIntPurposes",e.vendorLegitimateInterests),s.set("purposes",e.vendorConsents),s.forEach((n,o)=>{n.forEach((a,u)=>{if(a){const c=t.vendors[u];if(!c||c.deletedDate)n.unset(u);else if(c[o].length===0&&!(o==="legIntPurposes"&&c.purposes.length===0&&c.legIntPurposes.length===0&&c.specialPurposes.length>0))if(e.isServiceSpecific)if(c.flexiblePurposes.length===0)n.unset(u);else{const h=e.publisherRestrictions.getRestrictions(u);let g=!1;for(let d=0,v=h.length;d<v&&!g;d++)g=h[d].restrictionType===B.REQUIRE_CONSENT&&o==="purposes"||h[d].restrictionType===B.REQUIRE_LI&&o==="legIntPurposes";g||n.unset(u)}else n.unset(u)}})}),e.vendorsDisclosed.set(t.vendors),e}]);class Ks{static absCall(e,t,s,n){return new Promise((o,a)=>{const u=new XMLHttpRequest,c=()=>{if(u.readyState==XMLHttpRequest.DONE)if(u.status>=200&&u.status<300){let v=u.response;if(typeof v=="string")try{v=JSON.parse(v)}catch(b){}o(v)}else a(new Error(`HTTP Status: ${u.status} response type: ${u.responseType}`))},h=()=>{a(new Error("error"))},g=()=>{a(new Error("aborted"))},d=()=>{a(new Error("Timeout "+n+"ms "+e))};u.withCredentials=s,u.addEventListener("load",c),u.addEventListener("error",h),u.addEventListener("abort",g),t===null?u.open("GET",e,!0):u.open("POST",e,!0),u.responseType="json",u.timeout=n,u.ontimeout=d,u.send(t)})}static post(e,t){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:0;return this.absCall(e,JSON.stringify(t),s,n)}static fetch(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0;return this.absCall(e,null,t,s)}}const S=class S extends Ee{constructor(t){super();r(this,"readyPromise");r(this,"gvlSpecificationVersion");r(this,"vendorListVersion");r(this,"tcfPolicyVersion");r(this,"lastUpdated");r(this,"purposes");r(this,"specialPurposes");r(this,"features");r(this,"specialFeatures");r(this,"isReady_",!1);r(this,"vendors_");r(this,"vendorIds");r(this,"fullVendorList");r(this,"byPurposeVendorMap");r(this,"bySpecialPurposeVendorMap");r(this,"byFeatureVendorMap");r(this,"bySpecialFeatureVendorMap");r(this,"stacks");r(this,"dataCategories");r(this,"lang_");r(this,"cacheLang_");r(this,"isLatest",!1);let s=S.baseUrl;if(this.lang_=S.DEFAULT_LANGUAGE,this.cacheLang_=S.DEFAULT_LANGUAGE,this.isVendorList(t))this.populate(t),this.readyPromise=Promise.resolve();else{if(!s)throw new we("must specify GVL.baseUrl before loading GVL json");if(t>0){const n=t;S.CACHE.has(n)?(this.populate(S.CACHE.get(n)),this.readyPromise=Promise.resolve()):(s+=S.versionedFilename.replace("[VERSION]",String(n)),this.readyPromise=this.fetchJson(s))}else S.CACHE.has(S.LATEST_CACHE_KEY)?(this.populate(S.CACHE.get(S.LATEST_CACHE_KEY)),this.readyPromise=Promise.resolve()):(this.isLatest=!0,this.readyPromise=this.fetchJson(s+S.latestFilename))}}static set baseUrl(t){if(/^https?:\/\/vendorlist\.consensu\.org\//.test(t))throw new we("Invalid baseUrl!  You may not pull directly from vendorlist.consensu.org and must provide your own cache");t.length>0&&t[t.length-1]!=="/"&&(t+="/"),this.baseUrl_=t}static get baseUrl(){return this.baseUrl_}static emptyLanguageCache(t){let s=!1;return t==null&&S.LANGUAGE_CACHE.size>0?(S.LANGUAGE_CACHE=new Map,s=!0):typeof t=="string"&&this.consentLanguages.has(t.toUpperCase())&&(S.LANGUAGE_CACHE.delete(t.toUpperCase()),s=!0),s}static emptyCache(t){let s=!1;return Number.isInteger(t)&&t>=0?(S.CACHE.delete(t),s=!0):t===void 0&&(S.CACHE=new Map,s=!0),s}cacheLanguage(){S.LANGUAGE_CACHE.has(this.cacheLang_)||S.LANGUAGE_CACHE.set(this.cacheLang_,{purposes:this.purposes,specialPurposes:this.specialPurposes,features:this.features,specialFeatures:this.specialFeatures,stacks:this.stacks,dataCategories:this.dataCategories})}fetchJson(t){return Pe(this,null,function*(){try{this.populate(yield Ks.fetch(t))}catch(s){throw new we(s.message)}})}getJson(){return JSON.parse(JSON.stringify({gvlSpecificationVersion:this.gvlSpecificationVersion,vendorListVersion:this.vendorListVersion,tcfPolicyVersion:this.tcfPolicyVersion,lastUpdated:this.lastUpdated,purposes:this.purposes,specialPurposes:this.specialPurposes,features:this.features,specialFeatures:this.specialFeatures,stacks:this.stacks,dataCategories:this.dataCategories,vendors:this.fullVendorList}))}changeLanguage(t){return Pe(this,null,function*(){let s=t;try{s=S.consentLanguages.parseLanguage(t)}catch(o){throw new we("Error during parsing the language: "+o.message)}const n=t.toUpperCase();if(!(s.toLowerCase()===S.DEFAULT_LANGUAGE.toLowerCase()&&!S.LANGUAGE_CACHE.has(n))&&s!==this.lang_)if(this.lang_=s,S.LANGUAGE_CACHE.has(n)){const o=S.LANGUAGE_CACHE.get(n);for(const a in o)o.hasOwnProperty(a)&&(this[a]=o[a])}else{const o=S.baseUrl+S.languageFilename.replace("[LANG]",this.lang_.toLowerCase());try{yield this.fetchJson(o),this.cacheLang_=n,this.cacheLanguage()}catch(a){throw new we("unable to load language: "+a.message)}}})}get language(){return this.lang_}isVendorList(t){return t!==void 0&&t.vendors!==void 0}populate(t){this.purposes=t.purposes,this.specialPurposes=t.specialPurposes,this.features=t.features,this.specialFeatures=t.specialFeatures,this.stacks=t.stacks,this.dataCategories=t.dataCategories,this.isVendorList(t)&&(this.gvlSpecificationVersion=t.gvlSpecificationVersion,this.tcfPolicyVersion=t.tcfPolicyVersion,this.vendorListVersion=t.vendorListVersion,this.lastUpdated=t.lastUpdated,typeof this.lastUpdated=="string"&&(this.lastUpdated=new Date(this.lastUpdated)),this.vendors_=t.vendors,this.fullVendorList=t.vendors,this.mapVendors(),this.isReady_=!0,this.isLatest&&S.CACHE.set(S.LATEST_CACHE_KEY,this.getJson()),S.CACHE.has(this.vendorListVersion)||S.CACHE.set(this.vendorListVersion,this.getJson())),this.cacheLanguage()}mapVendors(t){this.byPurposeVendorMap={},this.bySpecialPurposeVendorMap={},this.byFeatureVendorMap={},this.bySpecialFeatureVendorMap={},Object.keys(this.purposes).forEach(s=>{this.byPurposeVendorMap[s]={legInt:new Set,consent:new Set,flexible:new Set}}),Object.keys(this.specialPurposes).forEach(s=>{this.bySpecialPurposeVendorMap[s]=new Set}),Object.keys(this.features).forEach(s=>{this.byFeatureVendorMap[s]=new Set}),Object.keys(this.specialFeatures).forEach(s=>{this.bySpecialFeatureVendorMap[s]=new Set}),Array.isArray(t)||(t=Object.keys(this.fullVendorList).map(s=>+s)),this.vendorIds=new Set(t),this.vendors_=t.reduce((s,n)=>{const o=this.vendors_[String(n)];return o&&o.deletedDate===void 0&&(o.purposes.forEach(a=>{this.byPurposeVendorMap[String(a)].consent.add(n)}),o.specialPurposes.forEach(a=>{this.bySpecialPurposeVendorMap[String(a)].add(n)}),o.legIntPurposes.forEach(a=>{this.byPurposeVendorMap[String(a)].legInt.add(n)}),o.flexiblePurposes&&o.flexiblePurposes.forEach(a=>{this.byPurposeVendorMap[String(a)].flexible.add(n)}),o.features.forEach(a=>{this.byFeatureVendorMap[String(a)].add(n)}),o.specialFeatures.forEach(a=>{this.bySpecialFeatureVendorMap[String(a)].add(n)}),s[n]=o),s},{})}getFilteredVendors(t,s,n,o){const a=t.charAt(0).toUpperCase()+t.slice(1);let u;const c={};return t==="purpose"&&n?u=this["by"+a+"VendorMap"][String(s)][n]:u=this["by"+(o?"Special":"")+a+"VendorMap"][String(s)],u.forEach(h=>{c[String(h)]=this.vendors[String(h)]}),c}getVendorsWithConsentPurpose(t){return this.getFilteredVendors("purpose",t,"consent")}getVendorsWithLegIntPurpose(t){return this.getFilteredVendors("purpose",t,"legInt")}getVendorsWithFlexiblePurpose(t){return this.getFilteredVendors("purpose",t,"flexible")}getVendorsWithSpecialPurpose(t){return this.getFilteredVendors("purpose",t,void 0,!0)}getVendorsWithFeature(t){return this.getFilteredVendors("feature",t)}getVendorsWithSpecialFeature(t){return this.getFilteredVendors("feature",t,void 0,!0)}get vendors(){return this.vendors_}narrowVendorsTo(t){this.mapVendors(t)}get isReady(){return this.isReady_}clone(){const t=new S(this.getJson());return this.lang_!==S.DEFAULT_LANGUAGE&&t.changeLanguage(this.lang_),t}static isInstanceOf(t){return typeof t=="object"&&typeof t.narrowVendorsTo=="function"}};r(S,"LANGUAGE_CACHE",new Map),r(S,"CACHE",new Map),r(S,"LATEST_CACHE_KEY",0),r(S,"DEFAULT_LANGUAGE","EN"),r(S,"consentLanguages",new st),r(S,"baseUrl_"),r(S,"latestFilename","vendor-list.json"),r(S,"versionedFilename","archives/vendor-list-v[VERSION].json"),r(S,"languageFilename","purposes-[LANG].json");let ie=S;class je extends Ee{constructor(t){super();r(this,"isServiceSpecific_",!1);r(this,"supportOOB_",!0);r(this,"useNonStandardTexts_",!1);r(this,"purposeOneTreatment_",!1);r(this,"publisherCountryCode_","AA");r(this,"version_",2);r(this,"consentScreen_",0);r(this,"policyVersion_",4);r(this,"consentLanguage_","EN");r(this,"cmpId_",0);r(this,"cmpVersion_",0);r(this,"vendorListVersion_",0);r(this,"numCustomPurposes_",0);r(this,"gvl_");r(this,"created");r(this,"lastUpdated");r(this,"specialFeatureOptins",new U);r(this,"purposeConsents",new U);r(this,"purposeLegitimateInterests",new U);r(this,"publisherConsents",new U);r(this,"publisherLegitimateInterests",new U);r(this,"publisherCustomConsents",new U);r(this,"publisherCustomLegitimateInterests",new U);r(this,"customPurposes");r(this,"vendorConsents",new U);r(this,"vendorLegitimateInterests",new U);r(this,"vendorsDisclosed",new U);r(this,"vendorsAllowed",new U);r(this,"publisherRestrictions",new Nt);t&&(this.gvl=t),this.updated()}set gvl(t){ie.isInstanceOf(t)||(t=new ie(t)),this.gvl_=t,this.publisherRestrictions.gvl=t}get gvl(){return this.gvl_}set cmpId(t){if(t=Number(t),Number.isInteger(t)&&t>1)this.cmpId_=t;else throw new K("cmpId",t)}get cmpId(){return this.cmpId_}set cmpVersion(t){if(t=Number(t),Number.isInteger(t)&&t>-1)this.cmpVersion_=t;else throw new K("cmpVersion",t)}get cmpVersion(){return this.cmpVersion_}set consentScreen(t){if(t=Number(t),Number.isInteger(t)&&t>-1)this.consentScreen_=t;else throw new K("consentScreen",t)}get consentScreen(){return this.consentScreen_}set consentLanguage(t){this.consentLanguage_=t}get consentLanguage(){return this.consentLanguage_}set publisherCountryCode(t){if(/^([A-z]){2}$/.test(t))this.publisherCountryCode_=t.toUpperCase();else throw new K("publisherCountryCode",t)}get publisherCountryCode(){return this.publisherCountryCode_}set vendorListVersion(t){if(t=Number(t)>>0,t<0)throw new K("vendorListVersion",t);this.vendorListVersion_=t}get vendorListVersion(){return this.gvl?this.gvl.vendorListVersion:this.vendorListVersion_}set policyVersion(t){if(this.policyVersion_=parseInt(t,10),this.policyVersion_<0)throw new K("policyVersion",t)}get policyVersion(){return this.gvl?this.gvl.tcfPolicyVersion:this.policyVersion_}set version(t){this.version_=parseInt(t,10)}get version(){return this.version_}set isServiceSpecific(t){this.isServiceSpecific_=t}get isServiceSpecific(){return this.isServiceSpecific_}set useNonStandardTexts(t){this.useNonStandardTexts_=t}get useNonStandardTexts(){return this.useNonStandardTexts_}set supportOOB(t){this.supportOOB_=t}get supportOOB(){return this.supportOOB_}set purposeOneTreatment(t){this.purposeOneTreatment_=t}get purposeOneTreatment(){return this.purposeOneTreatment_}setAllVendorConsents(){this.vendorConsents.set(this.gvl.vendors)}unsetAllVendorConsents(){this.vendorConsents.empty()}setAllVendorsDisclosed(){this.vendorsDisclosed.set(this.gvl.vendors)}unsetAllVendorsDisclosed(){this.vendorsDisclosed.empty()}setAllVendorsAllowed(){this.vendorsAllowed.set(this.gvl.vendors)}unsetAllVendorsAllowed(){this.vendorsAllowed.empty()}setAllVendorLegitimateInterests(){this.vendorLegitimateInterests.set(this.gvl.vendors)}unsetAllVendorLegitimateInterests(){this.vendorLegitimateInterests.empty()}setAllPurposeConsents(){this.purposeConsents.set(this.gvl.purposes)}unsetAllPurposeConsents(){this.purposeConsents.empty()}setAllPurposeLegitimateInterests(){this.purposeLegitimateInterests.set(this.gvl.purposes)}unsetAllPurposeLegitimateInterests(){this.purposeLegitimateInterests.empty()}setAllSpecialFeatureOptins(){this.specialFeatureOptins.set(this.gvl.specialFeatures)}unsetAllSpecialFeatureOptins(){this.specialFeatureOptins.empty()}setAll(){this.setAllVendorConsents(),this.setAllPurposeLegitimateInterests(),this.setAllSpecialFeatureOptins(),this.setAllPurposeConsents(),this.setAllVendorLegitimateInterests()}unsetAll(){this.unsetAllVendorConsents(),this.unsetAllPurposeLegitimateInterests(),this.unsetAllSpecialFeatureOptins(),this.unsetAllPurposeConsents(),this.unsetAllVendorLegitimateInterests()}get numCustomPurposes(){let t=this.numCustomPurposes_;if(typeof this.customPurposes=="object"){const s=Object.keys(this.customPurposes).sort((n,o)=>Number(n)-Number(o));t=parseInt(s.pop(),10)}return t}set numCustomPurposes(t){if(this.numCustomPurposes_=parseInt(t,10),this.numCustomPurposes_<0)throw new K("numCustomPurposes",t)}updated(){const t=new Date,s=new Date(Date.UTC(t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate()));this.created=s,this.lastUpdated=s}}r(je,"consentLanguages",ie.consentLanguages);class fe{static encode(e,t){let s="",n;return e=Dt.process(e,t),Array.isArray(t==null?void 0:t.segments)?n=t.segments:n=new Ys(e,t)[""+e.version],n.forEach((o,a)=>{let u="";a<n.length-1&&(u="."),s+=it.encode(e,o)+u}),s}static decode(e,t){const s=e.split("."),n=s.length;t||(t=new je);for(let o=0;o<n;o++){const a=s[o],c=ae.decode(a.charAt(0)).substr(0,_.segmentType),h=He.ID_TO_KEY[y.decode(c,_.segmentType).toString()];it.decode(a,t,h)}return t}}class Xs extends $e{respond(){const e=m.tcModel,t=e.vendorListVersion;let s;this.param===void 0&&(this.param=t),this.param===t&&e.gvl?s=e.gvl:s=new ie(this.param),s.readyPromise.then(()=>{this.invokeCallback(s.getJson())})}}class Zs extends Ge{respond(){this.listenerId=m.eventQueue.add({callback:this.callback,param:this.param,next:this.next}),super.respond()}}class ei extends $e{respond(){this.invokeCallback(m.eventQueue.remove(this.param))}}Ts=P.PING,Ns=P.GET_TC_DATA,Vs=P.GET_IN_APP_TC_DATA,Ls=P.GET_VENDOR_LIST,ks=P.ADD_EVENT_LISTENER,As=P.REMOVE_EVENT_LISTENER;class G{}r(G,Ts,Ws),r(G,Ns,Ge),r(G,Vs,qs),r(G,Ls,Xs),r(G,ks,Zs),r(G,As,ei);class Rt{static has(e){return typeof e=="string"&&(e=Number(e)),this.set_.has(e)}}r(Rt,"set_",new Set([0,2,void 0,null]));const nt="__tcfapi";class ti{constructor(e){r(this,"callQueue");r(this,"customCommands");if(e){let t=P.ADD_EVENT_LISTENER;if(e!=null&&e[t])throw new Error(`Built-In Custom Commmand for ${t} not allowed: Use ${P.GET_TC_DATA} instead`);if(t=P.REMOVE_EVENT_LISTENER,e!=null&&e[t])throw new Error(`Built-In Custom Commmand for ${t} not allowed`);e!=null&&e[P.GET_TC_DATA]&&(e[P.ADD_EVENT_LISTENER]=e[P.GET_TC_DATA],e[P.REMOVE_EVENT_LISTENER]=e[P.GET_TC_DATA]),this.customCommands=e}try{this.callQueue=window[nt]()||[]}catch(t){this.callQueue=[]}finally{window[nt]=this.apiCall.bind(this),this.purgeQueuedCalls()}}apiCall(e,t,s){for(var n=arguments.length,o=new Array(n>3?n-3:0),a=3;a<n;a++)o[a-3]=arguments[a];if(typeof e!="string")s(null,!1);else if(!Rt.has(t))s(null,!1);else{if(typeof s!="function")throw new Error("invalid callback function");m.disabled?s(new js,!1):!this.isCustomCommand(e)&&!this.isBuiltInCommand(e)?s(null,!1):this.isCustomCommand(e)&&!this.isBuiltInCommand(e)?this.customCommands[e](s,...o):e===P.PING?this.isCustomCommand(e)?new G[e](this.customCommands[e],o[0],null,s):new G[e](s,o[0]):m.tcModel===void 0?this.callQueue.push([e,t,s,...o]):this.isCustomCommand(e)&&this.isBuiltInCommand(e)?new G[e](this.customCommands[e],o[0],null,s):new G[e](s,o[0])}}purgeQueuedCalls(){const e=this.callQueue;this.callQueue=[],e.forEach(t=>{window[nt](...t)})}isCustomCommand(e){return this.customCommands&&typeof this.customCommands[e]=="function"}isBuiltInCommand(e){return G[e]!==void 0}}class Mt{constructor(e,t){r(this,"callResponder");r(this,"isServiceSpecific");r(this,"numUpdates",0);let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,n=arguments.length>3?arguments[3]:void 0;this.throwIfInvalidInt(e,"cmpId",2),this.throwIfInvalidInt(t,"cmpVersion",0),m.cmpId=e,m.cmpVersion=t,m.tcfPolicyVersion=4,this.isServiceSpecific=!!s,this.callResponder=new ti(n)}throwIfInvalidInt(e,t,s){if(!(typeof e=="number"&&Number.isInteger(e)&&e>=s))throw new Error(`Invalid ${t}: ${e}`)}update(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(m.disabled)throw new Error("CmpApi Disabled");m.cmpStatus=oe.LOADED,t?(m.displayStatus=re.VISIBLE,m.eventStatus=ye.CMP_UI_SHOWN):m.tcModel===void 0?(m.displayStatus=re.DISABLED,m.eventStatus=ye.TC_LOADED):(m.displayStatus=re.HIDDEN,m.eventStatus=ye.USER_ACTION_COMPLETE),m.gdprApplies=e!==null,m.gdprApplies?(e===""?(m.tcModel=new je,m.tcModel.cmpId=m.cmpId,m.tcModel.cmpVersion=m.cmpVersion):m.tcModel=fe.decode(e),m.tcModel.isServiceSpecific=this.isServiceSpecific,m.tcfPolicyVersion=Number(m.tcModel.policyVersion),m.tcString=e):m.tcModel=null,this.numUpdates===0?this.callResponder.purgeQueuedCalls():m.eventQueue.exec(),this.numUpdates++}disable(){m.disabled=!0,m.cmpStatus=oe.ERROR}}var Be,T,ot,Ft,rt=0,Ut=[],x=I,$t=x.__b,Gt=x.__r,Ht=x.diffed,jt=x.__c,Bt=x.unmount,Qt=x.__;function Wt(i,e){x.__h&&x.__h(T,i,rt||e),rt=0;var t=T.__H||(T.__H={__:[],__h:[]});return i>=t.__.length&&t.__.push({}),t.__[i]}function W(i){return rt=1,si(Jt,i)}function si(i,e,t){var s=Wt(Be++,2);if(s.t=i,!s.__c&&(s.__=[Jt(void 0,e),function(u){var c=s.__N?s.__N[0]:s.__[0],h=s.t(c,u);c!==h&&(s.__N=[h,s.__[1]],s.__c.setState({}))}],s.__c=T,!T.__f)){var n=function(u,c,h){if(!s.__c.__H)return!0;var g=s.__c.__H.__.filter(function(v){return!!v.__c});if(g.every(function(v){return!v.__N}))return!o||o.call(this,u,c,h);var d=s.__c.props!==u;return g.forEach(function(v){if(v.__N){var b=v.__[0];v.__=v.__N,v.__N=void 0,b!==v.__[0]&&(d=!0)}}),o&&o.call(this,u,c,h)||d};T.__f=!0;var o=T.shouldComponentUpdate,a=T.componentWillUpdate;T.componentWillUpdate=function(u,c,h){if(this.__e){var g=o;o=void 0,n(u,c,h),o=g}a&&a.call(this,u,c,h)},T.shouldComponentUpdate=n}return s.__N||s.__}function qt(i,e){var t=Wt(Be++,7);return oi(t.__H,e)&&(t.__=i(),t.__H=e,t.__h=i),t.__}function ii(){for(var i;i=Ut.shift();)if(i.__P&&i.__H)try{i.__H.__h.forEach(Qe),i.__H.__h.forEach(at),i.__H.__h=[]}catch(e){i.__H.__h=[],x.__e(e,i.__v)}}x.__b=function(i){T=null,$t&&$t(i)},x.__=function(i,e){i&&e.__k&&e.__k.__m&&(i.__m=e.__k.__m),Qt&&Qt(i,e)},x.__r=function(i){Gt&&Gt(i),Be=0;var e=(T=i.__c).__H;e&&(ot===T?(e.__h=[],T.__h=[],e.__.forEach(function(t){t.__N&&(t.__=t.__N),t.u=t.__N=void 0})):(e.__h.forEach(Qe),e.__h.forEach(at),e.__h=[],Be=0)),ot=T},x.diffed=function(i){Ht&&Ht(i);var e=i.__c;e&&e.__H&&(e.__H.__h.length&&(Ut.push(e)!==1&&Ft===x.requestAnimationFrame||((Ft=x.requestAnimationFrame)||ni)(ii)),e.__H.__.forEach(function(t){t.u&&(t.__H=t.u),t.u=void 0})),ot=T=null},x.__c=function(i,e){e.some(function(t){try{t.__h.forEach(Qe),t.__h=t.__h.filter(function(s){return!s.__||at(s)})}catch(s){e.some(function(n){n.__h&&(n.__h=[])}),e=[],x.__e(s,t.__v)}}),jt&&jt(i,e)},x.unmount=function(i){Bt&&Bt(i);var e,t=i.__c;t&&t.__H&&(t.__H.__.forEach(function(s){try{Qe(s)}catch(n){e=n}}),t.__H=void 0,e&&x.__e(e,t.__v))};var zt=typeof requestAnimationFrame=="function";function ni(i){var e,t=function(){clearTimeout(s),zt&&cancelAnimationFrame(e),setTimeout(i)},s=setTimeout(t,100);zt&&(e=requestAnimationFrame(t))}function Qe(i){var e=T,t=i.__c;typeof t=="function"&&(i.__c=void 0,t()),T=e}function at(i){var e=T;i.__c=i.__(),T=e}function oi(i,e){return!i||i.length!==e.length||e.some(function(t,s){return t!==i[s]})}function Jt(i,e){return typeof e=="function"?e(i):e}var ri=Symbol.for("preact-signals");function ct(){if(_e>1)_e--;else{for(var i,e=!1;Le!==void 0;){var t=Le;for(Le=void 0,lt++;t!==void 0;){var s=t.o;if(t.o=void 0,t.f&=-3,!(8&t.f)&&Kt(t))try{t.c()}catch(n){e||(i=n,e=!0)}t=s}}if(lt=0,_e--,e)throw i}}var k=void 0,Le=void 0,_e=0,lt=0,We=0;function Yt(i){if(k!==void 0){var e=i.n;if(e===void 0||e.t!==k)return e={i:0,S:i,p:k.s,n:void 0,t:k,e:void 0,x:void 0,r:e},k.s!==void 0&&(k.s.n=e),k.s=e,i.n=e,32&k.f&&i.S(e),e;if(e.i===-1)return e.i=0,e.n!==void 0&&(e.n.p=e.p,e.p!==void 0&&(e.p.n=e.n),e.p=k.s,e.n=void 0,k.s.n=e,k.s=e),e}}function O(i){this.v=i,this.i=0,this.n=void 0,this.t=void 0}O.prototype.brand=ri,O.prototype.h=function(){return!0},O.prototype.S=function(i){this.t!==i&&i.e===void 0&&(i.x=this.t,this.t!==void 0&&(this.t.e=i),this.t=i)},O.prototype.U=function(i){if(this.t!==void 0){var e=i.e,t=i.x;e!==void 0&&(e.x=t,i.e=void 0),t!==void 0&&(t.e=e,i.x=void 0),i===this.t&&(this.t=t)}},O.prototype.subscribe=function(i){var e=this;return dt(function(){var t=e.value,s=k;k=void 0;try{i(t)}finally{k=s}})},O.prototype.valueOf=function(){return this.value},O.prototype.toString=function(){return this.value+""},O.prototype.toJSON=function(){return this.value},O.prototype.peek=function(){var i=k;k=void 0;try{return this.value}finally{k=i}},Object.defineProperty(O.prototype,"value",{get:function(){var i=Yt(this);return i!==void 0&&(i.i=this.i),this.v},set:function(i){if(i!==this.v){if(lt>100)throw new Error("Cycle detected");this.v=i,this.i++,We++,_e++;try{for(var e=this.t;e!==void 0;e=e.x)e.t.N()}finally{ct()}}}});function $(i){return new O(i)}function Kt(i){for(var e=i.s;e!==void 0;e=e.n)if(e.S.i!==e.i||!e.S.h()||e.S.i!==e.i)return!0;return!1}function Xt(i){for(var e=i.s;e!==void 0;e=e.n){var t=e.S.n;if(t!==void 0&&(e.r=t),e.S.n=e,e.i=-1,e.n===void 0){i.s=e;break}}}function Zt(i){for(var e=i.s,t=void 0;e!==void 0;){var s=e.p;e.i===-1?(e.S.U(e),s!==void 0&&(s.n=e.n),e.n!==void 0&&(e.n.p=s)):t=e,e.S.n=e.r,e.r!==void 0&&(e.r=void 0),e=s}i.s=t}function ge(i){O.call(this,void 0),this.x=i,this.s=void 0,this.g=We-1,this.f=4}(ge.prototype=new O).h=function(){if(this.f&=-3,1&this.f)return!1;if((36&this.f)==32||(this.f&=-5,this.g===We))return!0;if(this.g=We,this.f|=1,this.i>0&&!Kt(this))return this.f&=-2,!0;var i=k;try{Xt(this),k=this;var e=this.x();(16&this.f||this.v!==e||this.i===0)&&(this.v=e,this.f&=-17,this.i++)}catch(t){this.v=t,this.f|=16,this.i++}return k=i,Zt(this),this.f&=-2,!0},ge.prototype.S=function(i){if(this.t===void 0){this.f|=36;for(var e=this.s;e!==void 0;e=e.n)e.S.S(e)}O.prototype.S.call(this,i)},ge.prototype.U=function(i){if(this.t!==void 0&&(O.prototype.U.call(this,i),this.t===void 0)){this.f&=-33;for(var e=this.s;e!==void 0;e=e.n)e.S.U(e)}},ge.prototype.N=function(){if(!(2&this.f)){this.f|=6;for(var i=this.t;i!==void 0;i=i.x)i.t.N()}},Object.defineProperty(ge.prototype,"value",{get:function(){if(1&this.f)throw new Error("Cycle detected");var i=Yt(this);if(this.h(),i!==void 0&&(i.i=this.i),16&this.f)throw this.v;return this.v}});function ai(i){return new ge(i)}function es(i){var e=i.u;if(i.u=void 0,typeof e=="function"){_e++;var t=k;k=void 0;try{e()}catch(s){throw i.f&=-2,i.f|=8,ut(i),s}finally{k=t,ct()}}}function ut(i){for(var e=i.s;e!==void 0;e=e.n)e.S.U(e);i.x=void 0,i.s=void 0,es(i)}function ci(i){if(k!==this)throw new Error("Out-of-order effect");Zt(this),k=i,this.f&=-2,8&this.f&&ut(this),ct()}function Ve(i){this.x=i,this.u=void 0,this.s=void 0,this.o=void 0,this.f=32}Ve.prototype.c=function(){var i=this.S();try{if(8&this.f||this.x===void 0)return;var e=this.x();typeof e=="function"&&(this.u=e)}finally{i()}},Ve.prototype.S=function(){if(1&this.f)throw new Error("Cycle detected");this.f|=1,this.f&=-9,es(this),Xt(this),_e++;var i=k;return k=this,ci.bind(this,i)},Ve.prototype.N=function(){2&this.f||(this.f|=2,this.o=Le,Le=this)},Ve.prototype.d=function(){this.f|=8,1&this.f||ut(this)};function dt(i){var e=new Ve(i);try{e.c()}catch(t){throw e.d(),t}return e.d.bind(e)}var pt;function ve(i,e){I[i]=e.bind(null,I[i]||function(){})}function qe(i){pt&&pt(),pt=i&&i.S()}function ts(i){var e=this,t=i.data,s=ui(t);s.value=t;var n=qt(function(){for(var o=e.__v;o=o.__;)if(o.__c){o.__c.__$f|=4;break}return e.__$u.c=function(){var a,u=e.__$u.S(),c=n.value;u(),M(c)||((a=e.base)==null?void 0:a.nodeType)!==3?(e.__$f|=1,e.setState({})):e.base.data=c},ai(function(){var a=s.value.value;return a===0?0:a===!0?"":a||""})},[]);return n.value}ts.displayName="_st",Object.defineProperties(O.prototype,{constructor:{configurable:!0,value:void 0},type:{configurable:!0,value:ts},props:{configurable:!0,get:function(){return{data:this}}},__b:{configurable:!0,value:1}}),ve("__b",function(i,e){if(typeof e.type=="string"){var t,s=e.props;for(var n in s)if(n!=="children"){var o=s[n];o instanceof O&&(t||(e.__np=t={}),t[n]=o,s[n]=o.peek())}}i(e)}),ve("__r",function(i,e){qe();var t,s=e.__c;s&&(s.__$f&=-2,(t=s.__$u)===void 0&&(s.__$u=t=function(n){var o;return dt(function(){o=this}),o.c=function(){s.__$f|=1,s.setState({})},o}())),qe(t),i(e)}),ve("__e",function(i,e,t,s){qe(),i(e,t,s)}),ve("diffed",function(i,e){qe();var t;if(typeof e.type=="string"&&(t=e.__e)){var s=e.__np,n=e.props;if(s){var o=t.U;if(o)for(var a in o){var u=o[a];u!==void 0&&!(a in s)&&(u.d(),o[a]=void 0)}else t.U=o={};for(var c in s){var h=o[c],g=s[c];h===void 0?(h=li(t,c,g,n),o[c]=h):h.o(g,n)}}}i(e)});function li(i,e,t,s){var n=e in i&&i.ownerSVGElement===void 0,o=$(t);return{o:function(a,u){o.value=a,s=u},d:dt(function(){var a=o.value.value;s[e]!==a&&(s[e]=a,n?i[e]=a:a?i.setAttribute(e,a):i.removeAttribute(e))})}}ve("unmount",function(i,e){if(typeof e.type=="string"){var t=e.__e;if(t){var s=t.U;if(s){t.U=void 0;for(var n in s){var o=s[n];o&&o.d()}}}}else{var a=e.__c;if(a){var u=a.__$u;u&&(a.__$u=void 0,u.d())}}i(e)}),ve("__h",function(i,e,t,s){(s<3||s===9)&&(e.__$f|=2),i(e,t,s)}),Ie.prototype.shouldComponentUpdate=function(i,e){var t=this.__$u,s=t&&t.s!==void 0;for(var n in e)return!0;if(this.__f||typeof this.u=="boolean"&&this.u===!0){if(!(s||2&this.__$f||4&this.__$f)||1&this.__$f)return!0}else if(!(s||4&this.__$f)||3&this.__$f)return!0;for(var o in i)if(o!=="__source"&&i[o]!==this.props[o])return!0;for(var a in this.props)if(!(a in i))return!0;return!1};function ui(i){return qt(function(){return $(i)},[])}const f={activeType:$("purposes"),tcModel:$(null),gvl:$({vendorList:[],purposes:[],specialPurposes:[],features:[],specialFeatures:[],dataCategories:[],vendorsForPurposes:{},vendorsForSpecialPurposes:{},vendorsForFeatures:{},vendorsSpecialFeatures:{}}),googleVendors:$([]),googleSelectedVendorsIds:$(new Set),iabLegIntPurposes:$([]),version:$(null),isUserChanged:$(!1)},C={currentLang:$("en"),text:$({}),textTranslations:$({})},di=i=>{C.currentLang.value=i;const e={};Object.keys(C.textTranslations.value).forEach(t=>{const s=C.textTranslations.value[t],n=JSON.parse(s);e[t]=i in n&&n[i].length===0?C.text.value[t]:n[i]}),e&&(C.text.value=e)},ss=i=>{let{illustrations:e}=i;const[t,s]=W(!1);return l("div",null,l("div",{tabIndex:"0",role:"button",className:"cookiescript-iab-showButton",onClick:()=>{s(!t)}},t?C.text.value.iab_hide_examples:C.text.value.iab_show_examples),t&&l("div",{className:"cookiescript-iab-itemContent"},l("ul",{className:"cookiescript-iab-itemContentList"},e.map(o=>l("li",null,o)))))},is=i=>{let{info:e}=i;const[t,s]=W(!1);return l("div",null,l("div",{tabIndex:"0",role:"button",className:"cookiescript-iab-showButton",onClick:()=>{s(!t)}},l("span",null,t?C.text.value.iab_hide_vendors:C.text.value.iab_show_vendors)," (",e.count,")"),t&&l("div",{className:"cookiescript-iab-itemContent"},e.text))},ns=i=>{let{type:e,value:{id:t,name:s,description:n,illustrations:o},vendorsInfo:a,isShowLegal:u=!1,isChecked:c,isLegalChecked:h,onChange:g=()=>{},onChangeLegal:d=()=>{}}=i;const[v,b]=W(!0),E=()=>{b(!v)};return l("div",{className:"cookiescript-iab-itemContainer"},l("div",{className:"cookiescript-iab-itemHeader"},l("div",{className:"cookiescript-iab-itemHeaderContent"},l("div",{className:"cookiescript-iab-itemCheckboxContainer"},l("input",{className:"cookiescript-iab-itemCheckbox",id:`${e}_${t}`,type:"checkbox",checked:c,onChange:g}),l("label",{className:"cookiescript_checkbox_label",htmlFor:`${e}_${t}`},l("b",null,s))),u?l("div",{className:"cookiescript-iab-itemCheckboxContainer cookiescript-iab-itemCheckboxContainerLI"},l("input",{className:"cookiescript-iab-itemCheckbox",id:`legal_${e}_${t}`,type:"checkbox",checked:h,onChange:d}),l("label",{role:"button",className:"cookiescript_checkbox_label",htmlFor:`legal_${e}_${t}`},l("b",{className:"cookiescript-iab-itemLI"},C.text.value.iab_interest))):""),l("div",{className:"cookiescript-iab-itemHeaderAction",onClick:E},v?"-":"+")),v&&l("div",{"data-cs-toggle-content":"cookiescript"},l("div",{className:"cookiescript-iab-itemContent"},n)),o.length>0&&l(ss,{illustrations:o}),a[t]&&l(is,{info:a[t]}))},os=i=>{let{value:{id:e,name:t,description:s,illustrations:n},vendorsInfo:o}=i;const[a,u]=W(!0),c=()=>{u(!a)};return l("div",{className:"cookiescript-iab-itemContainer"},l("div",{className:"cookiescript-iab-itemHeader"},l("div",{className:"cookiescript-iab-itemHeaderText"},l("b",null,t)),l("div",{className:"cookiescript-iab-itemHeaderAction",onClick:c},a?"-":"+")),a&&l("div",{"data-cs-toggle-content":"cookiescript"},l("div",{className:"cookiescript-iab-itemContent"},s)),n.length>0&&l(ss,{illustrations:n}),o[e]&&l(is,{info:o[e]}))},ht=()=>{const i=new CustomEvent("CookieScriptCMPClickCheckbox",{bubbles:!0,cancelable:!0,detail:{}});window.document.dispatchEvent(i)},rs=function(i){const e={years:"years",year:"year",days:"days",day:"day",hours:"hours",hour:"hour",minutes:"minutes",seconds:"seconds",second:"second",session:"Session"};if(i<=0)return e.session;for(var t=[[Math.floor(i/31536e3),[e.year,e.years]],[Math.floor(i%31536e3/86400),[e.day,e.days]],[Math.floor(i%31536e3%86400/3600),[e.hour,e.hours]],[Math.floor(i%31536e3%86400%3600/60),[e.minutes,e.minutes]],[i%31536e3%86400%3600%60,[e.second,e.seconds]]],s="",n=0,o=t.length;n<o;n++)t[n][0]!==0&&(s+=" "+t[n][0]+" "+(t[n][0]===1?t[n][1][0]:t[n][1][1]));return s.trim()},pi=()=>{const i=o=>()=>{o(),f.isUserChanged.value=!0,ht()},e=o=>()=>{f.tcModel.value.purposeConsents.has(o)?f.tcModel.value.purposeConsents.unset(o):f.tcModel.value.purposeConsents.set(o)},t=o=>()=>{f.tcModel.value.purposeLegitimateInterests.has(o)?f.tcModel.value.purposeLegitimateInterests.unset(o):f.tcModel.value.purposeLegitimateInterests.set(o)},s=()=>{const o=f.tcModel.value.clone();o.setAllPurposeConsents(),o.setAllPurposeLegitimateInterests(),f.tcModel.value=o},n=()=>{const o=f.tcModel.value.clone();o.unsetAllPurposeConsents(),o.unsetAllPurposeLegitimateInterests(),f.tcModel.value=o};return l("div",{className:"cookiescript-iab-tabContent","data-cs-iab-tab-content":"purposes"},C.text.value.iab_purposes_text&&l("div",{class:"cookiescript-iab-infoText"},C.text.value.iab_purposes_text),l("div",{className:"cookiescript-iab-headerContainer"},l("div",{className:"cookiescript-iab-header"},C.text.value.iab_purposes),l("div",{className:"cookiescript-iab-headerActions"},l("div",{className:"cookiescript-iab-headerAction",onClick:i(s)},C.text.value.iab_accept),l("div",{className:"cookiescript-iab-headerActionSeparator"},"|"),l("div",{className:"cookiescript-iab-headerAction",onClick:i(n)},C.text.value.iab_reject))),l("div",null,f.gvl.value.purposes.map(o=>l(ns,{type:"purposes",value:o,vendorsInfo:f.gvl.value.vendorsForPurposes,isChecked:f.tcModel.value.purposeConsents.has(Number(o.id)),onChange:i(e(o.id)),isShowLegal:f.iabLegIntPurposes.value.includes(o.id),isLegalChecked:f.tcModel.value.purposeLegitimateInterests.has(Number(o.id)),onChangeLegal:i(t(Number(o.id)))}))),l("div",{className:"cookiescript-iab-headerContainer"},l("div",{className:"cookiescript-iab-header"},C.text.value.iab_special_purposes)),l("div",null,f.gvl.value.specialPurposes.map(o=>l(os,{value:o,vendorsInfo:f.gvl.value.vendorsForSpecialPurposes}))))},hi=()=>{const i=n=>()=>{n(),f.isUserChanged.value=!0,ht()},e=n=>()=>{f.tcModel.value.specialFeatureOptins.has(n)?f.tcModel.value.specialFeatureOptins.unset(n):f.tcModel.value.specialFeatureOptins.set(n)},t=()=>{const n=f.tcModel.value.clone();n.setAllSpecialFeatureOptins(),f.tcModel.value=n},s=()=>{const n=f.tcModel.value.clone();n.unsetAllSpecialFeatureOptins(),f.tcModel.value=n};return l("div",{className:"cookiescript-iab-tabContent","data-cs-iab-tab-content":"purposes"},C.text.value.iab_features_text&&l("div",{class:"cookiescript-iab-infoText"},C.text.value.iab_features_text),l("div",{className:"cookiescript-iab-headerContainer"},l("div",{className:"cookiescript-iab-header"},C.text.value.iab_features)),l("div",null,f.gvl.value.features.map(n=>l(os,{value:n,vendorsInfo:f.gvl.value.vendorsForFeatures}))),l("div",{className:"cookiescript-iab-headerContainer"},l("div",{className:"cookiescript-iab-header"},C.text.value.iab_special_features),l("div",{className:"cookiescript-iab-headerActions"},l("div",{className:"cookiescript-iab-headerAction",onClick:i(t)},C.text.value.iab_accept),l("div",{className:"cookiescript-iab-headerActionSeparator"},"|"),l("div",{className:"cookiescript-iab-headerAction",onClick:i(s)},C.text.value.iab_reject))),l("div",null,f.gvl.value.specialFeatures.map(n=>l(ns,{type:"specialFeatures",value:n,vendorsInfo:f.gvl.value.vendorsSpecialFeatures,isChecked:f.tcModel.value.specialFeatureOptins.has(Number(n.id)),onChange:i(e(Number(n.id))),isShowLegal:!1,isLegalChecked:!1}))))};function ft(){return ft=Object.assign?Object.assign.bind():function(i){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var s in t)({}).hasOwnProperty.call(t,s)&&(i[s]=t[s])}return i},ft.apply(null,arguments)}const as=i=>{let{url:e}=i;return l("div",{className:"cookiescript-iab-policyUrl"},l("b",null,C.text.value.iab_policy_url),":",l("br",null),l("a",{className:"cookiescript-iab-policyLink",href:e},e))},fi=i=>{let{url:e}=i;return l("div",{className:"cookiescript-iab-policyUrl"},l("b",null,C.text.value.iab_interest),":",l("br",null),l("a",{className:"cookiescript-iab-policyLink",href:e},e))},me=(i=>e=>{let n=e,{show:t}=n,s=Ds(n,["show"]);return t?l(i,s):""})(i=>{let{allInfo:e,infoIds:t,dataRetention:s,title:n,additionalTitle:o}=i;const a=c=>{let h={};return e.forEach(g=>{g.id===c&&(h=g)}),h.name},u=()=>o?l("span",null,"  ( ",C.text.value[o]," )"):"";return l("div",{className:"cookiescript-iab-itemContentVendorInfo"},l("b",null,l("span",null,C.text.value[n]),u()),l("ul",{className:"cookiescript-iab-itemContentList"},t.map(c=>l("li",null,a(c),s&&l(ee,null," (",C.text.value.iab_data_retention,": ",s.specific[c]?s.specific[c]:s.common," days )")))))}),_i=i=>{let{disclosures:e,allPurposes:t}=i;const s=n=>{let o={};return t.forEach(a=>{a.id===n&&(o=a)}),o.name};return l("table",{className:"cookiescript-iab-vendor-cookie-table"},l("thead",null,l("tr",null,l("th",{scope:"col"},"Name"),l("th",{scope:"col"},"Domain"),l("th",{scope:"col"},"Purposes"),l("th",{scope:"col"},"Expiry"),l("th",{scope:"col"},"Type"))),l("tbody",null,e.map(n=>l("tr",null,l("td",null,n.identifier),l("td",null,n.domain?l("span",null,n.domain):n.domains.map(o=>l(ee,null,l("span",null,o),l("br",null)))),l("td",null,l("ul",{className:"cookiescript-iab-itemContentList"},n.purposes&&n.purposes.map(o=>l("li",null,s(o))))),l("td",null,n.maxAgeSeconds&&rs(n.maxAgeSeconds),l("br",null),n.cookieRefresh&&C.text.value.iab_cookie_refresh),l("td",null,n.type)))))},gi=i=>{let{type:e,currentLang:t,value:{id:s,name:n,legIntPurposes:o,purposes:a,specialFeatures:u,specialPurposes:c,features:h,cookieMaxAgeSeconds:g,usesCookies:d,usesNonCookieAccess:v,dataDeclaration:b,urls:E,dataRetention:R,cookieRefresh:V,deviceStorageDisclosureUrl:w},dataCategories:A,allPurposes:X,allSpecialPurposes:z,allFeatures:be,allSpecialFeatures:le,isChecked:ze,isLegalChecked:Ce,onChange:ue=()=>{},onChangeLegal:Te=()=>{}}=i;const[F,de]=W(!1),yi=()=>{gt(!1),de(!F)},[_t,gt]=W(!1),[wi,xs]=W(!0),[Ei,vt]=W(""),[Ps,mt]=W(null),Ai=(xe,Je)=>xe&&Je?l("li",null,C.text.value.iab_consent_tracking_other):xe?l("li",null,C.text.value.iab_consent_tracking_cookies):"";let Z=null;E.length>0&&(Z=E.find(xe=>xe.langId===t),Z||(Z=E[0]));const ki=xe=>{mt(null),_t?gt(!1):(xs(!0),w&&fetch(w,{mode:"cors"}).then(Je=>{Je.ok?Je.json().then(Li=>{mt(Li),vt("")}):(vt("An error occurred. Please try again"),mt(null))}).catch(()=>{vt("An error occurred. Please try again")}),xs(!1),gt(!0))};return l("div",{className:"cookiescript-iab-itemContainer"},l("div",{className:"cookiescript-iab-itemHeader"},l("div",{className:"cookiescript-iab-itemHeaderContent"},l("div",{className:"cookiescript-iab-itemCheckboxContainer"},l("input",{className:"cookiescript-iab-itemCheckbox",id:`${e}_${s}`,type:"checkbox",checked:ze,onChange:ue}),l("label",{className:"cookiescript_checkbox_label",htmlFor:`${e}_${s}`},l("b",null,n))),o.length>0?l("div",{className:"cookiescript-iab-itemCheckboxContainer cookiescript-iab-itemCheckboxContainerLI"},l("input",{className:"cookiescript-iab-itemCheckbox",id:`legal_${e}_${s}`,type:"checkbox",checked:Ce,onChange:Te}),l("label",{className:"cookiescript_checkbox_label",htmlFor:`legal_${e}_${s}`},l("b",{className:"cookiescript-iab-itemLI"},C.text.value.iab_interest))):""),l("div",{className:"cookiescript-iab-itemHeaderAction",onClick:yi},F?"-":"+")),F&&l("div",{"data-cs-toggle-content":"cookiescript",className:""},l("div",{className:"cookiescript-iab-itemContent"},Z&&Z.privacy?l(as,{url:Z.privacy}):"",Z&&Z.legIntClaim?l(fi,{url:Z.legIntClaim}):"",l(me,{show:a.length>0,infoIds:a,allInfo:X,dataRetention:{common:R.stdRetention,specific:R.purposes},title:"iab_purposes",additionalTitle:"iab_consent"}),l(me,{show:o.length>0,infoIds:o,allInfo:X,title:"iab_purposes",additionalTitle:"iab_interest"}),l(me,{show:c.length>0,infoIds:c,allInfo:z,dataRetention:{common:R.stdRetention,specific:R.specialPurposes},title:"iab_special_purposes"}),l(me,{show:h.length>0,infoIds:h,allInfo:be,title:"iab_features"}),l(me,{show:u.length>0,allInfo:le,infoIds:u,title:"iab_special_features"}),l(me,{show:b&&b.length>0,allInfo:A,infoIds:b,title:"iab_categories_data"}),(g||d||v||V)&&l("div",{className:"cookiescript-iab-itemContentVendorInfo"},l("b",null,C.text.value.iab_your_consent),l("ul",{className:"cookiescript-iab-itemContentList"},d&&l("li",null,C.text.value.iab_consent_expiration,": ",rs(g)),Ai(d,v),V&&l("li",null,C.text.value.iab_cookie_refresh))),l("div",null,l("div",{tabIndex:"0",role:"button",className:"cookiescript-iab-showButton",onClick:ki},_t?C.text.value.iab_hide_cookies:C.text.value.iab_show_cookies),_t&&l("div",null,wi?"Loading...":"",Ei,Ps&&l(_i,ft({},Ps,{allPurposes:X})))))))},vi=i=>{let{type:e,value:{provider_id:t,provider_name:s,policy_url:n},isChecked:o,onChange:a=()=>{}}=i;const[u,c]=W(!1),h=()=>{c(!u)};return l("div",{className:"cookiescript-iab-itemContainer"},l("div",{className:"cookiescript-iab-itemHeader"},l("div",{className:"cookiescript-iab-itemHeaderContent"},l("div",{className:"cookiescript-iab-itemCheckboxContainer"},l("input",{className:"cookiescript-iab-itemCheckbox",id:`${e}_${t}`,type:"checkbox",checked:o,onChange:a}),l("label",{className:"cookiescript_checkbox_label",htmlFor:`${e}_${t}`},l("b",null,s)))),l("div",{className:"cookiescript-iab-itemHeaderAction",onClick:h},u?"-":"+")),u&&l("div",{"data-cs-toggle-content":"cookiescript"},l("div",{className:"cookiescript-iab-itemContent"},n?l(as,{url:n}):"")))},mi=()=>{const i=c=>()=>{c(),f.isUserChanged.value=!0,ht()},e=c=>()=>{f.tcModel.value.vendorConsents.has(c)?f.tcModel.value.vendorConsents.unset(c):f.tcModel.value.vendorConsents.set(c)},t=c=>()=>{f.tcModel.value.vendorLegitimateInterests.has(c)?f.tcModel.value.vendorLegitimateInterests.unset(c):f.tcModel.value.vendorLegitimateInterests.set(c)},s=c=>()=>{f.googleSelectedVendorsIds.value.has(c)?f.googleSelectedVendorsIds.value.delete(c):f.googleSelectedVendorsIds.value.add(c)},n=()=>{const c=f.tcModel.value.clone();c.setAllVendorConsents(),c.setAllVendorLegitimateInterests(),f.tcModel.value=c},o=()=>{const c=f.tcModel.value.clone();c.unsetAllVendorConsents(),c.unsetAllVendorLegitimateInterests(),f.tcModel.value=c},a=()=>{f.googleSelectedVendorsIds.value=new Set(f.googleVendors.value.map(c=>c.provider_id))},u=()=>{f.googleSelectedVendorsIds.value=new Set};return l("div",{className:"cookiescript-iab-tabContent","data-cs-iab-tab-content":"purposes"},C.text.value.iab_vendors_text&&l("div",{class:"cookiescript-iab-infoText"},C.text.value.iab_vendors_text),l("div",{className:"cookiescript-iab-headerContainer"},l("div",{className:"cookiescript-iab-header"},C.text.value.iab_vendors),l("div",{className:"cookiescript-iab-headerActions"},l("div",{className:"cookiescript-iab-headerAction",onClick:i(n)},C.text.value.iab_accept),l("div",{className:"cookiescript-iab-headerActionSeparator"},"|"),l("div",{className:"cookiescript-iab-headerAction",onClick:i(o)},C.text.value.iab_reject))),l("div",null,f.gvl.value.vendorList.map(c=>l(gi,{value:c,type:"vendorList",allPurposes:f.gvl.value.purposes,allSpecialPurposes:f.gvl.value.specialPurposes,allFeatures:f.gvl.value.features,allSpecialFeatures:f.gvl.value.specialFeatures,dataCategories:f.gvl.value.dataCategories,isChecked:f.tcModel.value.vendorConsents.has(Number(c.id)),onChange:i(e(Number(c.id))),isLegalChecked:f.tcModel.value.vendorLegitimateInterests.has(Number(c.id)),onChangeLegal:i(t(Number(c.id))),currentLang:C.currentLang.value}))),f.googleVendors.value.length>0&&l(ee,null,l("div",{className:"cookiescript-iab-headerContainer"},l("div",{className:"cookiescript-iab-header"},C.text.value.iab_google_vendors),l("div",{className:"cookiescript-iab-headerActions"},l("div",{className:"cookiescript-iab-headerAction",onClick:i(a)},C.text.value.iab_accept),l("div",{className:"cookiescript-iab-headerActionSeparator"},"|"),l("div",{className:"cookiescript-iab-headerAction",onClick:i(u)},C.text.value.iab_reject))),l("div",null,f.googleVendors.value.map(c=>l(vi,{value:c,type:"googleVendors",isChecked:f.googleSelectedVendorsIds.value.has(c.provider_id),onChange:i(s(c.provider_id))})))))};function bi(){const i=e=>{f.activeType.value=e};return l(ee,null,l("div",{id:"cookiescript_iab_type","data-cs-iab-tabs":"cookiescript"},l("div",{className:f.activeType.value==="purposes"?"cookiescript_active":"","data-cs-iab-tab":"cookiescript","data-cs-iab-tab-type":"purposes",onClick:()=>i("purposes")},C.text.value.iab_purposes),l("div",{className:f.activeType.value==="features"?"cookiescript_active":"","data-cs-iab-tab":"cookiescript","data-cs-iab-tab-type":"features",onClick:()=>i("features")},C.text.value.iab_features),l("div",{className:f.activeType.value==="vendorList"?"cookiescript_active":"","data-cs-iab-tab":"cookiescript","data-cs-iab-tab-type":"vendorList",onClick:()=>i("vendorList")},C.text.value.iab_vendors)),l("div",{id:"cookiescript_iabwrap"},f.activeType.value==="purposes"&&l(pi,null),f.activeType.value==="features"&&l(hi,null),f.activeType.value==="vendorList"&&l(mi,null)))}const Ci=["ar","bg","bs","ca","cs","da","de","el","es","et","fi","fr","gl","hr","hu","it","ja","lt","lv","mt","nl","no","pl","pt-br","pt-pt","ro","ru","sk","sl","sr-cyrl","sr-latn","sv","tr","uk","zh"],Si=["purposes","features","vendorList"];class Ii{constructor(){r(this,"cookieScriptVersion",null);r(this,"cmpId",null);r(this,"cmpVersion",2);r(this,"googleACStringVersion",2);r(this,"iabVendorsIds",[]);r(this,"iabLegIntPurposes",[]);r(this,"scriptVersion",null);r(this,"activeType","purposes");r(this,"isLoadFromCookie",!1);r(this,"googleVendors",[]);r(this,"googleVendorsIds",[]);r(this,"googleACString",void 0);r(this,"isGoogleACStringLoadFromCookie",!1);r(this,"enableAdvertiserConsentMode",!1);r(this,"enabledIgnoreDecline",!1);r(this,"iabSdkUrl","");r(this,"gvlLoaded",null);r(this,"gvlLoadedLang",null);r(this,"isDisabledGdprApplies",!1);r(this,"loadSettings",()=>{if(window.CookieScript.instance.version){const e=Number(window.CookieScript.instance.version);isNaN(e)||(this.cookieScriptVersion=e)}window.CookieScript.instance.IABEnableAdvertiserConsentMode&&(this.enableAdvertiserConsentMode=window.CookieScript.instance.IABEnableAdvertiserConsentMode()),window.CookieScript.instance.IABEnabledIgnoreDecline&&(this.enabledIgnoreDecline=window.CookieScript.instance.IABEnabledIgnoreDecline),window.CookieScript.instance.iabDisabledGdprAppliesStatus&&(this.isDisabledGdprApplies=window.CookieScript.instance.iabDisabledGdprAppliesStatus()),this.scriptVersion=window.CookieScript.instance.version,this.cmpId=window.CookieScript.instance.getCMPId(),this.iabVendorsIds=window.CookieScript.instance.getIABVendorsIds(),this.iabLegIntPurposes=window.CookieScript.instance.getIABLegIntPurposes(),this.googleVendorsIds=window.CookieScript.instance.getGoogleVendorsIds(),this.iabSdkUrl=window.CookieScript.instance.getIABSdkUrl(),C.currentLang.value=window.CookieScript.instance.currentLang,C.text.value=window.CookieScript.instance.getIABText(),C.textTranslations.value=window.CookieScript.instance.getIABTextTranslations()});r(this,"getGVL",(()=>{var e=this;return function(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"en";return t==="pt"&&(t="pt-pt"),Ci.includes(t)||(t="en"),e.gvlLoaded&&e.gvlLoadedLang===t?new Promise((s,n)=>{Array.isArray(e.iabVendorsIds)&&e.iabVendorsIds.length>0&&e.GVL.narrowVendorsTo(e.iabVendorsIds),s(e.GVL)}):new Promise((s,n)=>{e.GVL.changeLanguage(t).then(()=>{e.GVL.readyPromise.then(()=>{e.gvlLoaded=!0,e.gvlLoadedLang=t,Array.isArray(e.iabVendorsIds)&&e.iabVendorsIds.length>0&&e.GVL.narrowVendorsTo(e.iabVendorsIds),s(e.GVL)})})})}})());r(this,"getGoogleVendors",()=>Pe(this,null,function*(){const t=yield fetch(this.iabSdkUrl+"/google-vendors.json");t.status===200&&(this.googleVendors=yield t.json(),Array.isArray(this.googleVendorsIds)&&(this.googleVendors=this.googleVendors.filter(s=>this.googleVendorsIds.includes(s.provider_id))))}));r(this,"encodeGoogleACString",(()=>{var e=this;return function(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!t)return null;const s=e.googleACStringVersion,n=t.consented||[],o=t.disclosed||[];return`${s}~${n.join(".")}~dv.${o.join(".")}`}})());r(this,"decodeGoogleACString",e=>{if(!e)return null;const t=e.split("~");if(t.length!==3)return null;const s=Number(t[0]),n=t[1].split("."),o=t[2].split(".").filter(a=>a);return{version:s,consented:n,disclosed:o}});r(this,"unSelectTCModel",e=>{const t=new je(e||this.GVL);return t.cmpVersion=this.cmpVersion,t.cmpId=this.cmpId,t.consentScreen=1,t.isServiceSpecific=!0,t.unsetAll(),t});r(this,"setDefaultTCModel",e=>{this.getGVL(C.currentLang.value).then(t=>{Array.isArray(this.iabVendorsIds)&&this.iabVendorsIds.length>0&&t.narrowVendorsTo(this.iabVendorsIds);const s=this.unSelectTCModel(t);s.setAllPurposeLegitimateInterests(),s.setAllVendorLegitimateInterests(),f.tcModel.value=s;let n=fe.encode(s,{isForVendors:!0});e&&(this.isDisabledGdprApplies?n=null:n=""),this.cmpApi.update(n,e)})});r(this,"initData",e=>Pe(this,null,function*(){if(yield this.getGoogleVendors(),this.googleACString=this.getGoogleACStingCookie(),this.googleACString){const n=this.decodeGoogleACString(this.googleACString);n&&(f.googleSelectedVendorsIds.value=new Set(n.consented),this.isGoogleACStringLoadFromCookie=!0)}const t=!e,s=this.getCMPCookie();if(s){let n=null;try{n=fe.decode(s)}catch(o){}if(n)return this.getGVL(C.currentLang.value).then(o=>{f.tcModel.value=n,f.tcModel.value.gvl=o}),this.cmpApi.update(s,t),this.isLoadFromCookie=!0,!0}this.setDefaultTCModel(t)}));r(this,"viewData",()=>{if(this.isCheckedIABCategoryMap()&&(this.isLoadFromCookie||f.tcModel.value.setAll(),!this.isGoogleACStringLoadFromCookie)){const e=this.googleVendors.map(t=>t.provider_id);f.googleSelectedVendorsIds.value=new Set(e)}});r(this,"selectAll",()=>new Promise((e,t)=>{const s=this.googleVendors.map(a=>a.provider_id);f.googleSelectedVendorsIds.value=new Set(s);const n={consented:s,disclosed:[]};this.googleACString=this.encodeGoogleACString(n),this.setGoogleACStingCookie(this.googleACString),f.tcModel.value.setAll();const o=fe.encode(f.tcModel.value,{isForVendors:!0});this.cmpApi.update(o,!1),this.setCMPCookie(o),e(!0)}));r(this,"rejectAll",()=>new Promise((e,t)=>{f.googleSelectedVendorsIds.value=new Set;const s={consented:[],disclosed:this.googleVendors.map(o=>o.provider_id)};this.googleACString=this.encodeGoogleACString(s),this.setGoogleACStingCookie(this.googleACString),this.enabledIgnoreDecline?(f.tcModel.value.unsetAllPurposeConsents(),f.tcModel.value.unsetAllSpecialFeatureOptins(),f.tcModel.value.unsetAllVendorConsents()):f.tcModel.value.unsetAll();const n=fe.encode(f.tcModel.value,{isForVendors:!0});this.cmpApi.update(n,!1),this.setCMPCookie(n),e(!0)}));r(this,"setOnlyChecked",()=>{!f.isUserChanged.value&&this.isCheckedIABCategoryMap()&&(f.tcModel.value.setAll(),f.googleSelectedVendorsIds.value=new Set(this.googleVendors.map(s=>s.provider_id)));const e=fe.encode(f.tcModel.value,{isForVendors:!0}),t={consented:f.googleSelectedVendorsIds.value.values().toArray(),disclosed:this.googleVendors.map(s=>s.provider_id).filter(s=>!f.googleSelectedVendorsIds.value.has(s))};this.googleACString=this.encodeGoogleACString(t),this.setGoogleACStingCookie(this.googleACString),this.cmpApi.update(e,!1),this.setCMPCookie(e)});r(this,"vendorsInfo",e=>{const t={},s={},n={},o={};return e.forEach(a=>{a.purposes.forEach(u=>{const c=t[u]||{count:0,text:""};c.count+=1,c.text=c.text+a.name+", ",t[u]=c}),a.specialPurposes.forEach(u=>{const c=s[u]||{count:0,text:""};c.count+=1,c.text=c.text+a.name+", ",s[u]=c}),a.features.forEach(u=>{const c=n[u]||{count:0,text:""};c.count+=1,c.text=c.text+a.name+", ",n[u]=c}),a.specialFeatures.forEach(u=>{const c=o[u]||{count:0,text:""};c.count+=1,c.text=c.text+a.name+", ",o[u]=c})}),{vendorsForPurposes:t,vendorsForSpecialPurposes:s,vendorsForFeatures:n,vendorsSpecialFeatures:o}});r(this,"applyGvlData",e=>{const t=Object.values(e.vendors),s=Object.values(e.purposes),n=Object.values(e.specialPurposes),o=Object.values(e.features),a=Object.values(e.specialFeatures),u=Object.values(e.dataCategories),{vendorsForPurposes:c,vendorsForSpecialPurposes:h,vendorsForFeatures:g,vendorsSpecialFeatures:d}=this.vendorsInfo(t);f.gvl.value={vendorList:t,purposes:s,specialPurposes:n,features:o,specialFeatures:a,dataCategories:u,vendorsForPurposes:c,vendorsForSpecialPurposes:h,vendorsForFeatures:g,vendorsSpecialFeatures:d}});r(this,"applyTranslations",e=>{this.getGVL(e).then(t=>{this.applyGvlData(t),di(e)})});r(this,"showSpecificTab",e=>{let t=this.activeType;Si.includes(e)&&(t=e),this.activeType=t,f.activeType.value=t});r(this,"buildView",()=>new Promise((e,t)=>{const s=document.querySelector('div[data-cs-maintab-content="setting_advertising"]');s?(this.loadView(s),e(!0)):e(!1)}));r(this,"loadView",e=>{this.getGVL(C.currentLang.value).then(t=>{this.viewData(),e.querySelectorAll("div[data-cs-iab-tab-type]").forEach(s=>{s.getAttribute("data-cs-iab-tab-show")&&(this.activeType=s.getAttribute("data-cs-iab-tab-type"))}),this.applyGvlData(t),f.activeType.value=this.activeType,f.googleVendors.value=this.googleVendors,f.iabLegIntPurposes.value=this.iabLegIntPurposes,f.version.value=this.cookieScriptVersion,e.innerHTML="",setTimeout(function(){Gs(l(bi,null),e)},100)})});let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;if(arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1){new Mt(window.CookieScriptGeo.cmpId,this.cmpVersion,!0).update(null,!1);return}this.loadSettings();const s={getTCData:(n,o,a)=>{typeof o!="boolean"&&(o.addtlConsent=this.googleACString,o.enableAdvertiserConsentMode=this.enableAdvertiserConsentMode),n(o,a)},getInAppTCData:(n,o,a)=>{typeof o!="boolean"&&(o.addtlConsent=this.googleACString,o.enableAdvertiserConsentMode=this.enableAdvertiserConsentMode),n(o,a)}};this.cmpApi=new Mt(this.cmpId,this.cmpVersion,!0,s),ie.baseUrl=this.iabSdkUrl,this.GVL=new ie,this.initData(e).then(()=>{})}setCMPCookie(e){window.CookieScript&&window.CookieScript.instance&&window.CookieScript.instance.setCMPCookie&&window.CookieScript.instance.setCMPCookie(e)}getCMPCookie(){return window.CookieScript&&window.CookieScript.instance&&window.CookieScript.instance.getCMPCookie?window.CookieScript.instance.getCMPCookie():null}setGoogleACStingCookie(e){window.CookieScript&&window.CookieScript.instance&&window.CookieScript.instance.setGoogleACStringCookie&&window.CookieScript.instance.setGoogleACStringCookie(e)}getGoogleACStingCookie(){return window.CookieScript&&window.CookieScript.instance&&window.CookieScript.instance.getGoogleACStringCookie?window.CookieScript.instance.getGoogleACStringCookie():null}isCheckedIABCategoryMap(){return window.CookieScript&&window.CookieScript.instance&&window.CookieScript.instance.isCheckedIABCategoryMap?window.CookieScript.instance.isCheckedIABCategoryMap():!1}}window.CookieScriptCMP={iabCMP:Ii}})();

})();