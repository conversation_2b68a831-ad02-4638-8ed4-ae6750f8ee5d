try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="b2779b37-f285-4180-93d2-d67df7fc1906",e._sentryDebugIdIdentifier="sentry-dbid-b2779b37-f285-4180-93d2-d67df7fc1906")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5711],{48333:function(e,t,r){"use strict";function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,i)}return r}function n(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){c(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function s(){s=function(){return e};var e={},t=Object.prototype,r=t.hasOwnProperty,i=Object.defineProperty||function(e,t,r){e[t]=r.value},n="function"==typeof Symbol?Symbol:{},a=n.iterator||"@@iterator",o=n.asyncIterator||"@@asyncIterator",l=n.toStringTag||"@@toStringTag";function d(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{d({},"")}catch(e){d=function(e,t,r){return e[t]=r}}function u(e,t,r,n){var s,a,o=Object.create((t&&t.prototype instanceof h?t:h).prototype);return i(o,"_invoke",{value:(s=new x(n||[]),a="suspendedStart",function(t,i){if("executing"===a)throw Error("Generator is already running");if("completed"===a){if("throw"===t)throw i;return P()}for(s.method=t,s.arg=i;;){var n=s.delegate;if(n){var o=function e(t,r){var i=r.method,n=t.iterator[i];if(void 0===n)return r.delegate=null,"throw"===i&&t.iterator.return&&(r.method="return",r.arg=void 0,e(t,r),"throw"===r.method)||"return"!==i&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+i+"' method")),p;var s=c(n,t.iterator,r.arg);if("throw"===s.type)return r.method="throw",r.arg=s.arg,r.delegate=null,p;var a=s.arg;return a?a.done?(r[t.resultName]=a.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=void 0),r.delegate=null,p):a:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,p)}(n,s);if(o){if(o===p)continue;return o}}if("next"===s.method)s.sent=s._sent=s.arg;else if("throw"===s.method){if("suspendedStart"===a)throw a="completed",s.arg;s.dispatchException(s.arg)}else"return"===s.method&&s.abrupt("return",s.arg);a="executing";var l=c(e,r,s);if("normal"===l.type){if(a=s.done?"completed":"suspendedYield",l.arg===p)continue;return{value:l.arg,done:s.done}}"throw"===l.type&&(a="completed",s.method="throw",s.arg=l.arg)}})}),o}function c(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var p={};function h(){}function f(){}function m(){}var v={};d(v,a,function(){return this});var g=Object.getPrototypeOf,y=g&&g(g(k([])));y&&y!==t&&r.call(y,a)&&(v=y);var w=m.prototype=h.prototype=Object.create(v);function b(e){["next","throw","return"].forEach(function(t){d(e,t,function(e){return this._invoke(t,e)})})}function E(e,t){var n;i(this,"_invoke",{value:function(i,s){function a(){return new t(function(n,a){!function i(n,s,a,o){var l=c(e[n],e,s);if("throw"!==l.type){var d=l.arg,u=d.value;return u&&"object"==typeof u&&r.call(u,"__await")?t.resolve(u.__await).then(function(e){i("next",e,a,o)},function(e){i("throw",e,a,o)}):t.resolve(u).then(function(e){d.value=e,a(d)},function(e){return i("throw",e,a,o)})}o(l.arg)}(i,s,n,a)})}return n=n?n.then(a,a):a()}})}function T(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function S(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function x(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(T,this),this.reset(!0)}function k(e){if(e){var t=e[a];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,n=function t(){for(;++i<e.length;)if(r.call(e,i))return t.value=e[i],t.done=!1,t;return t.value=void 0,t.done=!0,t};return n.next=n}}return{next:P}}function P(){return{value:void 0,done:!0}}return f.prototype=m,i(w,"constructor",{value:m,configurable:!0}),i(m,"constructor",{value:f,configurable:!0}),f.displayName=d(m,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===f||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,m):(e.__proto__=m,d(e,l,"GeneratorFunction")),e.prototype=Object.create(w),e},e.awrap=function(e){return{__await:e}},b(E.prototype),d(E.prototype,o,function(){return this}),e.AsyncIterator=E,e.async=function(t,r,i,n,s){void 0===s&&(s=Promise);var a=new E(u(t,r,i,n),s);return e.isGeneratorFunction(r)?a:a.next().then(function(e){return e.done?e.value:a.next()})},b(w),d(w,l,"Generator"),d(w,a,function(){return this}),d(w,"toString",function(){return"[object Generator]"}),e.keys=function(e){var t=Object(e),r=[];for(var i in t)r.push(i);return r.reverse(),function e(){for(;r.length;){var i=r.pop();if(i in t)return e.value=i,e.done=!1,e}return e.done=!0,e}},e.values=k,x.prototype={constructor:x,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(S),!e)for(var t in this)"t"===t.charAt(0)&&r.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function i(r,i){return a.type="throw",a.arg=e,t.next=r,i&&(t.method="next",t.arg=void 0),!!i}for(var n=this.tryEntries.length-1;n>=0;--n){var s=this.tryEntries[n],a=s.completion;if("root"===s.tryLoc)return i("end");if(s.tryLoc<=this.prev){var o=r.call(s,"catchLoc"),l=r.call(s,"finallyLoc");if(o&&l){if(this.prev<s.catchLoc)return i(s.catchLoc,!0);if(this.prev<s.finallyLoc)return i(s.finallyLoc)}else if(o){if(this.prev<s.catchLoc)return i(s.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<s.finallyLoc)return i(s.finallyLoc)}}}},abrupt:function(e,t){for(var i=this.tryEntries.length-1;i>=0;--i){var n=this.tryEntries[i];if(n.tryLoc<=this.prev&&r.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var s=n;break}}s&&("break"===e||"continue"===e)&&s.tryLoc<=t&&t<=s.finallyLoc&&(s=null);var a=s?s.completion:{};return a.type=e,a.arg=t,s?(this.method="next",this.next=s.finallyLoc,p):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),p},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),S(r),p}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var i=r.completion;if("throw"===i.type){var n=i.arg;S(r)}return n}}throw Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:k(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=void 0),p}},e}function a(e,t,r,i,n,s,a){try{var o=e[s](a),l=o.value}catch(e){r(e);return}o.done?t(l):Promise.resolve(l).then(i,n)}function o(e){return function(){var t=this,r=arguments;return new Promise(function(i,n){var s=e.apply(t,r);function o(e){a(s,i,n,o,l,"next",e)}function l(e){a(s,i,n,o,l,"throw",e)}o(void 0)})}}function l(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function d(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,y(i.key),i)}}function u(e,t,r){return t&&d(e.prototype,t),r&&d(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function c(e,t,r){return(t=y(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function p(e){return(p=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function h(e,t){return(h=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function f(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}function m(e,t,r){return(m=f()?Reflect.construct.bind():function(e,t,r){var i=[null];i.push.apply(i,t);var n=new(Function.bind.apply(e,i));return r&&h(n,r.prototype),n}).apply(null,arguments)}function v(e){var t="function"==typeof Map?new Map:void 0;return(v=function(e){if(null===e||-1===Function.toString.call(e).indexOf("[native code]"))return e;if("function"!=typeof e)throw TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return m(e,arguments,p(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),h(r,e)})(e)}function g(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function y(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!=typeof i)return i;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}var w,b,E,T,S,x,k,P=void 0!==r.g&&"[object global]"===({}).toString.call(r.g);function C(e,t){return 0===e.indexOf(t.toLowerCase())?e:"".concat(t.toLowerCase()).concat(e.substr(0,1).toUpperCase()).concat(e.substr(1))}function M(e){return/^(https?:)?\/\/((((player|www)\.)?vimeo\.com)|((player\.)?[a-zA-Z0-9-]+\.(videoji\.(hk|cn)|vimeo\.work)))(?=$|\/)/.test(e)}function _(e){return/^https:\/\/player\.((vimeo\.com)|([a-zA-Z0-9-]+\.(videoji\.(hk|cn)|vimeo\.work)))\/video\/\d+/.test(e)}function O(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.id,r=e.url,i=t||r;if(!i)throw Error("An id or url must be passed, either in an options object or as a data-vimeo-id or data-vimeo-url attribute.");if(!isNaN(parseFloat(i))&&isFinite(i)&&Math.floor(i)==i)return"https://vimeo.com/".concat(i);if(M(i))return i.replace("http:","https:");if(t)throw TypeError("“".concat(t,"” is not a valid video id."));throw TypeError("“".concat(i,"” is not a vimeo.com url."))}var L=function(e,t,r){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"addEventListener",n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"removeEventListener",s="string"==typeof t?[t]:t;return s.forEach(function(t){e[i](t,r)}),{cancel:function(){return s.forEach(function(t){return e[n](t,r)})}}},A=void 0!==Array.prototype.indexOf,I="undefined"!=typeof window&&void 0!==window.postMessage;if(!P&&(!A||!I))throw Error("Sorry, the Vimeo Player API is not available in this browser.");var j="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:void 0!==r.g?r.g:"undefined"!=typeof self?self:{};!function(e){if(!e.WeakMap){var t=Object.prototype.hasOwnProperty,r=Object.defineProperty&&function(){try{return 1===Object.defineProperty({},"x",{value:1}).x}catch(e){}}(),i=function(e,t,i){r?Object.defineProperty(e,t,{configurable:!0,writable:!0,value:i}):e[t]=i};e.WeakMap=function(){function e(){if(void 0===this)throw TypeError("Constructor WeakMap requires 'new'");if(i(this,"_id","_WeakMap_"+s()+"."+s()),arguments.length>0)throw TypeError("WeakMap iterable is not supported")}function r(e,r){if(!n(e)||!t.call(e,"_id"))throw TypeError(r+" method called on incompatible receiver "+typeof e)}function s(){return Math.random().toString().substring(2)}return i(e.prototype,"delete",function(e){if(r(this,"delete"),!n(e))return!1;var t=e[this._id];return!!t&&t[0]===e&&(delete e[this._id],!0)}),i(e.prototype,"get",function(e){if(r(this,"get"),n(e)){var t=e[this._id];if(t&&t[0]===e)return t[1]}}),i(e.prototype,"has",function(e){if(r(this,"has"),!n(e))return!1;var t=e[this._id];return!!t&&t[0]===e}),i(e.prototype,"set",function(e,t){if(r(this,"set"),!n(e))throw TypeError("Invalid value used as weak map key");var s=e[this._id];return s&&s[0]===e?s[1]=t:i(e,this._id,[e,t]),this}),i(e,"_polyfill",!0),e}()}function n(e){return Object(e)===e}}("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:j);var N=(w=T={exports:{}},T.exports,b="Promise",E=function(){var e,t,r,i=Object.prototype.toString,n="undefined"!=typeof setImmediate?function(e){return setImmediate(e)}:setTimeout;try{Object.defineProperty({},"x",{}),e=function(e,t,r,i){return Object.defineProperty(e,t,{value:r,writable:!0,configurable:!1!==i})}}catch(t){e=function(e,t,r){return e[t]=r,e}}function s(e,i){r.add(e,i),t||(t=n(r.drain))}function a(e){var t,r=typeof e;return null!=e&&("object"==r||"function"==r)&&(t=e.then),"function"==typeof t&&t}function o(){for(var e=0;e<this.chain.length;e++)(function(e,t,r){var i,n;try{!1===t?r.reject(e.msg):(i=!0===t?e.msg:t.call(void 0,e.msg))===r.promise?r.reject(TypeError("Promise-chain cycle")):(n=a(i))?n.call(i,r.resolve,r.reject):r.resolve(i)}catch(e){r.reject(e)}})(this,1===this.state?this.chain[e].success:this.chain[e].failure,this.chain[e]);this.chain.length=0}function l(e){var t,r=this;if(!r.triggered){r.triggered=!0,r.def&&(r=r.def);try{(t=a(e))?s(function(){var i=new c(r);try{t.call(e,function(){l.apply(i,arguments)},function(){d.apply(i,arguments)})}catch(e){d.call(i,e)}}):(r.msg=e,r.state=1,r.chain.length>0&&s(o,r))}catch(e){d.call(new c(r),e)}}}function d(e){var t=this;!t.triggered&&(t.triggered=!0,t.def&&(t=t.def),t.msg=e,t.state=2,t.chain.length>0&&s(o,t))}function u(e,t,r,i){for(var n=0;n<t.length;n++)!function(n){e.resolve(t[n]).then(function(e){r(n,e)},i)}(n)}function c(e){this.def=e,this.triggered=!1}function p(e){this.promise=e,this.state=0,this.triggered=!1,this.chain=[],this.msg=void 0}function h(e){if("function"!=typeof e)throw TypeError("Not a function");if(0!==this.__NPO__)throw TypeError("Not a promise");this.__NPO__=1;var t=new p(this);this.then=function(e,r){var i={success:"function"!=typeof e||e,failure:"function"==typeof r&&r};return i.promise=new this.constructor(function(e,t){if("function"!=typeof e||"function"!=typeof t)throw TypeError("Not a function");i.resolve=e,i.reject=t}),t.chain.push(i),0!==t.state&&s(o,t),i.promise},this.catch=function(e){return this.then(void 0,e)};try{e.call(void 0,function(e){l.call(t,e)},function(e){d.call(t,e)})}catch(e){d.call(t,e)}}r=function(){var e,r,i;function n(e,t){this.fn=e,this.self=t,this.next=void 0}return{add:function(t,s){i=new n(t,s),r?r.next=i:e=i,r=i,i=void 0},drain:function(){var i=e;for(e=r=t=void 0;i;)i.fn.call(i.self),i=i.next}}}();var f=e({},"constructor",h,!1);return h.prototype=f,e(f,"__NPO__",0,!1),e(h,"resolve",function(e){return e&&"object"==typeof e&&1===e.__NPO__?e:new this(function(t,r){if("function"!=typeof t||"function"!=typeof r)throw TypeError("Not a function");t(e)})}),e(h,"reject",function(e){return new this(function(t,r){if("function"!=typeof t||"function"!=typeof r)throw TypeError("Not a function");r(e)})}),e(h,"all",function(e){var t=this;return"[object Array]"!=i.call(e)?t.reject(TypeError("Not an array")):0===e.length?t.resolve([]):new t(function(r,i){if("function"!=typeof r||"function"!=typeof i)throw TypeError("Not a function");var n=e.length,s=Array(n),a=0;u(t,e,function(e,t){s[e]=t,++a===n&&r(s)},i)})}),e(h,"race",function(e){var t=this;return"[object Array]"!=i.call(e)?t.reject(TypeError("Not an array")):new t(function(r,i){if("function"!=typeof r||"function"!=typeof i)throw TypeError("Not a function");u(t,e,function(e,t){r(t)},i)})}),h},j[b]=j[b]||E(),w.exports&&(w.exports=j[b]),T.exports),z=new WeakMap;function D(e,t,r){var i=z.get(e.element)||{};t in i||(i[t]=[]),i[t].push(r),z.set(e.element,i)}function F(e,t){return(z.get(e.element)||{})[t]||[]}function R(e,t,r){var i=z.get(e.element)||{};if(!i[t])return!0;if(!r)return i[t]=[],z.set(e.element,i),!0;var n=i[t].indexOf(r);return -1!==n&&i[t].splice(n,1),z.set(e.element,i),i[t]&&0===i[t].length}function V(e){if("string"==typeof e)try{e=JSON.parse(e)}catch(e){return console.warn(e),{}}return e}function G(e,t,r){if(e.element.contentWindow&&e.element.contentWindow.postMessage){var i={method:t};void 0!==r&&(i.value=r);var n=parseFloat(navigator.userAgent.toLowerCase().replace(/^.*msie (\d+).*$/,"$1"));n>=8&&n<10&&(i=JSON.stringify(i)),e.element.contentWindow.postMessage(i,e.origin)}}var B=["airplay","audio_tracks","audiotrack","autopause","autoplay","background","byline","cc","chapter_id","chapters","chromecast","color","colors","controls","dnt","end_time","fullscreen","height","id","interactive_params","keyboard","loop","maxheight","maxwidth","muted","play_button_position","playsinline","portrait","progress_bar","quality_selector","responsive","skipping_forward","speed","start_time","texttrack","title","transcript","transparent","unmute_button","url","vimeo_logo","volume","watch_full_video","width"];function $(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return B.reduce(function(t,r){var i=e.getAttribute("data-vimeo-".concat(r));return(i||""===i)&&(t[r]=""===i?1:i),t},t)}function q(e,t){var r=e.html;if(!t)throw TypeError("An element must be provided");if(null!==t.getAttribute("data-vimeo-initialized"))return t.querySelector("iframe");var i=document.createElement("div");return i.innerHTML=r,t.appendChild(i.firstChild),t.setAttribute("data-vimeo-initialized","true"),t.querySelector("iframe")}function H(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2?arguments[2]:void 0;return new Promise(function(i,n){if(!M(e))throw TypeError("“".concat(e,"” is not a vimeo.com url."));var s=function(e){for(var t=(e||"").match(/^(?:https?:)?(?:\/\/)?([^/?]+)/),r=(t&&t[1]||"").replace("player.",""),i=0,n=[".videoji.hk",".vimeo.work",".videoji.cn"];i<n.length;i++){var s=n[i];if(r.endsWith(s))return r}return"vimeo.com"}(e),a="https://".concat(s,"/api/oembed.json?url=").concat(encodeURIComponent(e));for(var o in t)t.hasOwnProperty(o)&&(a+="&".concat(o,"=").concat(encodeURIComponent(t[o])));var l="XDomainRequest"in window?new XDomainRequest:new XMLHttpRequest;l.open("GET",a,!0),l.onload=function(){if(404===l.status){n(Error("“".concat(e,"” was not found.")));return}if(403===l.status){n(Error("“".concat(e,"” is not embeddable.")));return}try{var t=JSON.parse(l.responseText);if(403===t.domain_status_code){q(t,r),n(Error("“".concat(e,"” is not embeddable.")));return}i(t)}catch(e){n(e)}},l.onerror=function(){var e=l.status?" (".concat(l.status,")"):"";n(Error("There was an error fetching the embed code from Vimeo".concat(e,".")))},l.send()})}var W={role:"viewer",autoPlayMuted:!0,allowedDrift:.3,maxAllowedDrift:1,minCheckInterval:.1,maxRateAdjustment:.2,maxTimeToCatchUp:1},Y=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&h(e,t)}(m,e);var t,r,i,a,d=(t=f(),function(){var e,r=p(m);return e=t?Reflect.construct(r,arguments,p(this).constructor):r.apply(this,arguments),function(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return g(e)}(this,e)});function m(e,t){var r,i,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},u=arguments.length>3?arguments[3]:void 0;return l(this,m),c(g(i=d.call(this)),"logger",void 0),c(g(i),"speedAdjustment",0),c(g(i),"adjustSpeed",(r=o(s().mark(function e(t,r){var n;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(i.speedAdjustment!==r){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,t.getPlaybackRate();case 4:return e.t0=e.sent,e.t1=i.speedAdjustment,e.t2=e.t0-e.t1,e.t3=r,n=e.t2+e.t3,i.log("New playbackRate:  ".concat(n)),e.next=12,t.setPlaybackRate(n);case 12:i.speedAdjustment=r;case 13:case"end":return e.stop()}},e)})),function(e,t){return r.apply(this,arguments)})),i.logger=u,i.init(t,e,n(n({},W),a)),i}return u(m,[{key:"disconnect",value:function(){this.dispatchEvent(new Event("disconnect"))}},{key:"init",value:(r=o(s().mark(function e(t,r,i){var n,a,o,l=this;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.waitForTOReadyState(t,"open");case 2:if("viewer"!==i.role){e.next=10;break}return e.next=5,this.updatePlayer(t,r,i);case 5:n=L(t,"change",function(){return l.updatePlayer(t,r,i)}),a=this.maintainPlaybackPosition(t,r,i),this.addEventListener("disconnect",function(){a.cancel(),n.cancel()}),e.next=14;break;case 10:return e.next=12,this.updateTimingObject(t,r);case 12:o=L(r,["seeked","play","pause","ratechange"],function(){return l.updateTimingObject(t,r)},"on","off"),this.addEventListener("disconnect",function(){return o.cancel()});case 14:case"end":return e.stop()}},e,this)})),function(e,t,i){return r.apply(this,arguments)})},{key:"updateTimingObject",value:(i=o(s().mark(function e(t,r){return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.t0=t,e.next=3,r.getCurrentTime();case 3:return e.t1=e.sent,e.next=6,r.getPaused();case 6:if(!e.sent){e.next=10;break}e.t2=0,e.next=13;break;case 10:return e.next=12,r.getPlaybackRate();case 12:e.t2=e.sent;case 13:e.t3=e.t2,e.t4={position:e.t1,velocity:e.t3},e.t0.update.call(e.t0,e.t4);case 16:case"end":return e.stop()}},e)})),function(e,t){return i.apply(this,arguments)})},{key:"updatePlayer",value:(a=o(s().mark(function e(t,r,i){var n,a,l;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(a=(n=t.query()).position,l=n.velocity,"number"==typeof a&&r.setCurrentTime(a),"number"!=typeof l){e.next=25;break}if(0!==l){e.next=11;break}return e.next=6,r.getPaused();case 6:if(e.t0=e.sent,!1!==e.t0){e.next=9;break}r.pause();case 9:e.next=25;break;case 11:if(!(l>0)){e.next=25;break}return e.next=14,r.getPaused();case 14:if(e.t1=e.sent,!0!==e.t1){e.next=19;break}return e.next=18,r.play().catch(function(){var e=o(s().mark(function e(t){return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!("NotAllowedError"===t.name&&i.autoPlayMuted)){e.next=5;break}return e.next=3,r.setMuted(!0);case 3:return e.next=5,r.play().catch(function(e){return console.error("Couldn't play the video from TimingSrcConnector. Error:",e)});case 5:case"end":return e.stop()}},e)}));return function(t){return e.apply(this,arguments)}}());case 18:this.updatePlayer(t,r,i);case 19:return e.next=21,r.getPlaybackRate();case 21:if(e.t2=e.sent,e.t3=l,!(e.t2!==e.t3)){e.next=25;break}r.setPlaybackRate(l);case 25:case"end":return e.stop()}},e,this)})),function(e,t,r){return a.apply(this,arguments)})},{key:"maintainPlaybackPosition",value:function(e,t,r){var i,n=this,a=r.allowedDrift,l=r.maxAllowedDrift,d=r.minCheckInterval,u=r.maxRateAdjustment,c=r.maxTimeToCatchUp,p=(i=o(s().mark(function r(){var i,o,d,p,h;return s().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:if(r.t0=0===e.query().velocity,r.t0){r.next=6;break}return r.next=4,t.getPaused();case 4:r.t1=r.sent,r.t0=!0===r.t1;case 6:if(!r.t0){r.next=8;break}return r.abrupt("return");case 8:return r.t2=e.query().position,r.next=11,t.getCurrentTime();case 11:if(r.t3=r.sent,o=Math.abs(i=r.t2-r.t3),n.log("Drift: ".concat(i)),!(o>l)){r.next=22;break}return r.next=18,n.adjustSpeed(t,0);case 18:t.setCurrentTime(e.query().position),n.log("Resync by currentTime"),r.next=29;break;case 22:if(!(o>a)){r.next=29;break}return h=(d=o/c)<(p=u)?(p-d)/2:p,r.next=28,n.adjustSpeed(t,h*Math.sign(i));case 28:n.log("Resync by playbackRate");case 29:case"end":return r.stop()}},r)})),function(){return i.apply(this,arguments)}),h=setInterval(function(){return p()},1e3*Math.min(c,Math.max(d,l)));return{cancel:function(){return clearInterval(h)}}}},{key:"log",value:function(e){var t;null===(t=this.logger)||void 0===t||t.call(this,"TimingSrcConnector: ".concat(e))}},{key:"waitForTOReadyState",value:function(e,t){return new Promise(function(r){!function i(){e.readyState===t?r():e.addEventListener("readystatechange",i,{once:!0})}()})}}]),m}(v(EventTarget)),X=new WeakMap,U=new WeakMap,K={},Q=function(){var e;function t(e){var r,i=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(l(this,t),window.jQuery&&e instanceof jQuery&&(e.length>1&&window.console&&console.warn&&console.warn("A jQuery object with multiple elements was passed, using the first element."),e=e[0]),"undefined"!=typeof document&&"string"==typeof e&&(e=document.getElementById(e)),!((r=e)&&1===r.nodeType&&"nodeName"in r&&r.ownerDocument&&r.ownerDocument.defaultView))throw TypeError("You must pass either a valid element or a valid id.");if("IFRAME"!==e.nodeName){var s=e.querySelector("iframe");s&&(e=s)}if("IFRAME"===e.nodeName&&!M(e.getAttribute("src")||""))throw Error("The player element passed isn’t a Vimeo embed.");if(X.has(e))return X.get(e);this._window=e.ownerDocument.defaultView,this.element=e,this.origin="*";var a=new N(function(t,r){if(i._onMessage=function(e){if(M(e.origin)&&i.element.contentWindow===e.source){"*"===i.origin&&(i.origin=e.origin);var n=V(e.data);if(n&&"error"===n.event&&n.data&&"ready"===n.data.method){var s=Error(n.data.message);s.name=n.data.name,r(s);return}var a=n&&"ready"===n.event,o=n&&"ping"===n.method;if(a||o){i.element.setAttribute("data-ready","true"),t();return}!function(e,t){t=V(t);var r,i=[];if(t.event)"error"===t.event&&F(e,t.data.method).forEach(function(r){var i=Error(t.data.message);i.name=t.data.name,r.reject(i),R(e,t.data.method,r)}),i=F(e,"event:".concat(t.event)),r=t.data;else if(t.method){var n=function(e,t){var r=F(e,t);if(r.length<1)return!1;var i=r.shift();return R(e,t,i),i}(e,t.method);n&&(i.push(n),r=t.value)}i.forEach(function(t){try{if("function"==typeof t){t.call(e,r);return}t.resolve(r)}catch(e){}})}(i,n)}},i._window.addEventListener("message",i._onMessage),"IFRAME"!==i.element.nodeName){var s=$(e,n);H(O(s),s,e).then(function(t){var r,n,s=q(t,e);return i.element=s,i._originalElement=e,r=e,n=z.get(r),z.set(s,n),z.delete(r),X.set(i.element,i),t}).catch(r)}});if(U.set(this,a),X.set(this.element,this),"IFRAME"===this.element.nodeName&&G(this,"ping"),K.isEnabled){var o=function(){return K.exit()};this.fullscreenchangeHandler=function(){K.isFullscreen?D(i,"event:exitFullscreen",o):R(i,"event:exitFullscreen",o),i.ready().then(function(){G(i,"fullscreenchange",K.isFullscreen)})},K.on("fullscreenchange",this.fullscreenchangeHandler)}return this}return u(t,[{key:"callMethod",value:function(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new N(function(i,n){return t.ready().then(function(){D(t,e,{resolve:i,reject:n}),G(t,e,r)}).catch(n)})}},{key:"get",value:function(e){var t=this;return new N(function(r,i){return e=C(e,"get"),t.ready().then(function(){D(t,e,{resolve:r,reject:i}),G(t,e)}).catch(i)})}},{key:"set",value:function(e,t){var r=this;return new N(function(i,n){if(e=C(e,"set"),null==t)throw TypeError("There must be a value to set.");return r.ready().then(function(){D(r,e,{resolve:i,reject:n}),G(r,e,t)}).catch(n)})}},{key:"on",value:function(e,t){if(!e)throw TypeError("You must pass an event name.");if(!t)throw TypeError("You must pass a callback function.");if("function"!=typeof t)throw TypeError("The callback must be a function.");0===F(this,"event:".concat(e)).length&&this.callMethod("addEventListener",e).catch(function(){}),D(this,"event:".concat(e),t)}},{key:"off",value:function(e,t){if(!e)throw TypeError("You must pass an event name.");if(t&&"function"!=typeof t)throw TypeError("The callback must be a function.");R(this,"event:".concat(e),t)&&this.callMethod("removeEventListener",e).catch(function(e){})}},{key:"loadVideo",value:function(e){return this.callMethod("loadVideo",e)}},{key:"ready",value:function(){var e=U.get(this)||new N(function(e,t){t(Error("Unknown player. Probably unloaded."))});return N.resolve(e)}},{key:"addCuePoint",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.callMethod("addCuePoint",{time:e,data:t})}},{key:"removeCuePoint",value:function(e){return this.callMethod("removeCuePoint",e)}},{key:"enableTextTrack",value:function(e,t){if(!e)throw TypeError("You must pass a language.");return this.callMethod("enableTextTrack",{language:e,kind:t})}},{key:"disableTextTrack",value:function(){return this.callMethod("disableTextTrack")}},{key:"pause",value:function(){return this.callMethod("pause")}},{key:"play",value:function(){return this.callMethod("play")}},{key:"requestFullscreen",value:function(){return K.isEnabled?K.request(this.element):this.callMethod("requestFullscreen")}},{key:"exitFullscreen",value:function(){return K.isEnabled?K.exit():this.callMethod("exitFullscreen")}},{key:"getFullscreen",value:function(){return K.isEnabled?N.resolve(K.isFullscreen):this.get("fullscreen")}},{key:"requestPictureInPicture",value:function(){return this.callMethod("requestPictureInPicture")}},{key:"exitPictureInPicture",value:function(){return this.callMethod("exitPictureInPicture")}},{key:"getPictureInPicture",value:function(){return this.get("pictureInPicture")}},{key:"remotePlaybackPrompt",value:function(){return this.callMethod("remotePlaybackPrompt")}},{key:"unload",value:function(){return this.callMethod("unload")}},{key:"destroy",value:function(){var e=this;return new N(function(t){if(U.delete(e),X.delete(e.element),e._originalElement&&(X.delete(e._originalElement),e._originalElement.removeAttribute("data-vimeo-initialized")),e.element&&"IFRAME"===e.element.nodeName&&e.element.parentNode&&(e.element.parentNode.parentNode&&e._originalElement&&e._originalElement!==e.element.parentNode?e.element.parentNode.parentNode.removeChild(e.element.parentNode):e.element.parentNode.removeChild(e.element)),e.element&&"DIV"===e.element.nodeName&&e.element.parentNode){e.element.removeAttribute("data-vimeo-initialized");var r=e.element.querySelector("iframe");r&&r.parentNode&&(r.parentNode.parentNode&&e._originalElement&&e._originalElement!==r.parentNode?r.parentNode.parentNode.removeChild(r.parentNode):r.parentNode.removeChild(r))}e._window.removeEventListener("message",e._onMessage),K.isEnabled&&K.off("fullscreenchange",e.fullscreenchangeHandler),t()})}},{key:"getAutopause",value:function(){return this.get("autopause")}},{key:"setAutopause",value:function(e){return this.set("autopause",e)}},{key:"getBuffered",value:function(){return this.get("buffered")}},{key:"getCameraProps",value:function(){return this.get("cameraProps")}},{key:"setCameraProps",value:function(e){return this.set("cameraProps",e)}},{key:"getChapters",value:function(){return this.get("chapters")}},{key:"getCurrentChapter",value:function(){return this.get("currentChapter")}},{key:"getColor",value:function(){return this.get("color")}},{key:"getColors",value:function(){return N.all([this.get("colorOne"),this.get("colorTwo"),this.get("colorThree"),this.get("colorFour")])}},{key:"setColor",value:function(e){return this.set("color",e)}},{key:"setColors",value:function(e){if(!Array.isArray(e))return new N(function(e,t){return t(TypeError("Argument must be an array."))});var t=new N(function(e){return e(null)}),r=[e[0]?this.set("colorOne",e[0]):t,e[1]?this.set("colorTwo",e[1]):t,e[2]?this.set("colorThree",e[2]):t,e[3]?this.set("colorFour",e[3]):t];return N.all(r)}},{key:"getCuePoints",value:function(){return this.get("cuePoints")}},{key:"getCurrentTime",value:function(){return this.get("currentTime")}},{key:"setCurrentTime",value:function(e){return this.set("currentTime",e)}},{key:"getDuration",value:function(){return this.get("duration")}},{key:"getEnded",value:function(){return this.get("ended")}},{key:"getLoop",value:function(){return this.get("loop")}},{key:"setLoop",value:function(e){return this.set("loop",e)}},{key:"setMuted",value:function(e){return this.set("muted",e)}},{key:"getMuted",value:function(){return this.get("muted")}},{key:"getPaused",value:function(){return this.get("paused")}},{key:"getPlaybackRate",value:function(){return this.get("playbackRate")}},{key:"setPlaybackRate",value:function(e){return this.set("playbackRate",e)}},{key:"getPlayed",value:function(){return this.get("played")}},{key:"getQualities",value:function(){return this.get("qualities")}},{key:"getQuality",value:function(){return this.get("quality")}},{key:"setQuality",value:function(e){return this.set("quality",e)}},{key:"getRemotePlaybackAvailability",value:function(){return this.get("remotePlaybackAvailability")}},{key:"getRemotePlaybackState",value:function(){return this.get("remotePlaybackState")}},{key:"getSeekable",value:function(){return this.get("seekable")}},{key:"getSeeking",value:function(){return this.get("seeking")}},{key:"getTextTracks",value:function(){return this.get("textTracks")}},{key:"getVideoEmbedCode",value:function(){return this.get("videoEmbedCode")}},{key:"getVideoId",value:function(){return this.get("videoId")}},{key:"getVideoTitle",value:function(){return this.get("videoTitle")}},{key:"getVideoWidth",value:function(){return this.get("videoWidth")}},{key:"getVideoHeight",value:function(){return this.get("videoHeight")}},{key:"getVideoUrl",value:function(){return this.get("videoUrl")}},{key:"getVolume",value:function(){return this.get("volume")}},{key:"setVolume",value:function(e){return this.set("volume",e)}},{key:"setTimingSrc",value:(e=o(s().mark(function e(t,r){var i,n=this;return s().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=2;break}throw TypeError("A Timing Object must be provided.");case 2:return e.next=4,this.ready();case 4:return i=new Y(this,t,r),G(this,"notifyTimingObjectConnect"),i.addEventListener("disconnect",function(){return G(n,"notifyTimingObjectDisconnect")}),e.abrupt("return",i);case 8:case"end":return e.stop()}},e,this)})),function(t,r){return e.apply(this,arguments)})}]),t}();P||(x={fullscreenchange:(S=function(){for(var e,t=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],r=0,i=t.length,n={};r<i;r++)if((e=t[r])&&e[1]in document){for(r=0;r<e.length;r++)n[t[0][r]]=e[r];return n}return!1}()).fullscreenchange,fullscreenerror:S.fullscreenerror},Object.defineProperties(k={request:function(e){return new Promise(function(t,r){var i=function e(){k.off("fullscreenchange",e),t()};k.on("fullscreenchange",i);var n=(e=e||document.documentElement)[S.requestFullscreen]();n instanceof Promise&&n.then(i).catch(r)})},exit:function(){return new Promise(function(e,t){if(!k.isFullscreen){e();return}var r=function t(){k.off("fullscreenchange",t),e()};k.on("fullscreenchange",r);var i=document[S.exitFullscreen]();i instanceof Promise&&i.then(r).catch(t)})},on:function(e,t){var r=x[e];r&&document.addEventListener(r,t)},off:function(e,t){var r=x[e];r&&document.removeEventListener(r,t)}},{isFullscreen:{get:function(){return!!document[S.fullscreenElement]}},element:{enumerable:!0,get:function(){return document[S.fullscreenElement]}},isEnabled:{enumerable:!0,get:function(){return!!document[S.fullscreenEnabled]}}}),K=k,function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document,t=[].slice.call(e.querySelectorAll("[data-vimeo-id], [data-vimeo-url]")),r=function(e){"console"in window&&console.error&&console.error("There was an error creating an embed: ".concat(e))};t.forEach(function(e){try{if(null!==e.getAttribute("data-vimeo-defer"))return;var t=$(e),i=O(t);H(i,t,e).then(function(t){return q(t,e)}).catch(r)}catch(e){r(e)}})}(),function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document;window.VimeoPlayerResizeEmbeds_||(window.VimeoPlayerResizeEmbeds_=!0,window.addEventListener("message",function(t){if(M(t.origin)&&t.data&&"spacechange"===t.data.event){for(var r=e.querySelectorAll("iframe"),i=0;i<r.length;i++)if(r[i].contentWindow===t.source){r[i].parentElement.style.paddingBottom="".concat(t.data.data[0].bottom,"px");break}}}))}(),function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document;window.VimeoSeoMetadataAppended||(window.VimeoSeoMetadataAppended=!0,window.addEventListener("message",function(t){if(M(t.origin)){var r=V(t.data);if(r&&"ready"===r.event)for(var i=e.querySelectorAll("iframe"),n=0;n<i.length;n++){var s=i[n],a=s.contentWindow===t.source;_(s.src)&&a&&new Q(s).callMethod("appendVideoMetadata",window.location.href)}}}))}(),function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document;if(!window.VimeoCheckedUrlTimeParam){window.VimeoCheckedUrlTimeParam=!0;var t=function(e){"console"in window&&console.error&&console.error("There was an error getting video Id: ".concat(e))};window.addEventListener("message",function(r){if(M(r.origin)){var i=V(r.data);if(i&&"ready"===i.event)for(var n=e.querySelectorAll("iframe"),s=0;s<n.length;s++)!function(){var e=n[s],i=e.contentWindow===r.source;if(_(e.src)&&i){var a=new Q(e);a.getVideoId().then(function(e){var t=new RegExp("[?&]vimeo_t_".concat(e,"=([^&#]*)")).exec(window.location.href);if(t&&t[1]){var r=decodeURI(t[1]);a.setCurrentTime(r)}}).catch(t)}}()}})}}()),t.Z=Q},32602:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return o},noSSR:function(){return a}});let i=r(38754);r(85893),r(67294);let n=i._(r(35491));function s(e){return{default:(null==e?void 0:e.default)||e}}function a(e,t){return delete t.webpack,delete t.modules,e(t)}function o(e,t){let r=n.default,i={loading:e=>{let{error:t,isLoading:r,pastDelay:i}=e;return null}};e instanceof Promise?i.loader=()=>e:"function"==typeof e?i.loader=e:"object"==typeof e&&(i={...i,...e});let o=(i={...i,...t}).loader;return(i.loadableGenerated&&(i={...i,...i.loadableGenerated},delete i.loadableGenerated),"boolean"!=typeof i.ssr||i.ssr)?r({...i,loader:()=>null!=o?o().then(s):Promise.resolve(s(()=>null))}):(delete i.webpack,delete i.modules,a(r,i))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1159:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"LoadableContext",{enumerable:!0,get:function(){return i}});let i=r(38754)._(r(67294)).default.createContext(null)},35491:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return p}});let i=r(38754)._(r(67294)),n=r(1159),s=[],a=[],o=!1;function l(e){let t=e(),r={loading:!0,loaded:null,error:null};return r.promise=t.then(e=>(r.loading=!1,r.loaded=e,e)).catch(e=>{throw r.loading=!1,r.error=e,e}),r}class d{promise(){return this._res.promise}retry(){this._clearTimeouts(),this._res=this._loadFn(this._opts.loader),this._state={pastDelay:!1,timedOut:!1};let{_res:e,_opts:t}=this;e.loading&&("number"==typeof t.delay&&(0===t.delay?this._state.pastDelay=!0:this._delay=setTimeout(()=>{this._update({pastDelay:!0})},t.delay)),"number"==typeof t.timeout&&(this._timeout=setTimeout(()=>{this._update({timedOut:!0})},t.timeout))),this._res.promise.then(()=>{this._update({}),this._clearTimeouts()}).catch(e=>{this._update({}),this._clearTimeouts()}),this._update({})}_update(e){this._state={...this._state,error:this._res.error,loaded:this._res.loaded,loading:this._res.loading,...e},this._callbacks.forEach(e=>e())}_clearTimeouts(){clearTimeout(this._delay),clearTimeout(this._timeout)}getCurrentValue(){return this._state}subscribe(e){return this._callbacks.add(e),()=>{this._callbacks.delete(e)}}constructor(e,t){this._loadFn=e,this._opts=t,this._callbacks=new Set,this._delay=null,this._timeout=null,this.retry()}}function u(e){return function(e,t){let r=Object.assign({loader:null,loading:null,delay:200,timeout:null,webpack:null,modules:null},t),s=null;function l(){if(!s){let t=new d(e,r);s={getCurrentValue:t.getCurrentValue.bind(t),subscribe:t.subscribe.bind(t),retry:t.retry.bind(t),promise:t.promise.bind(t)}}return s.promise()}if(!o){let e=r.webpack?r.webpack():r.modules;e&&a.push(t=>{for(let r of e)if(t.includes(r))return l()})}function u(e,t){!function(){l();let e=i.default.useContext(n.LoadableContext);e&&Array.isArray(r.modules)&&r.modules.forEach(t=>{e(t)})}();let a=i.default.useSyncExternalStore(s.subscribe,s.getCurrentValue,s.getCurrentValue);return i.default.useImperativeHandle(t,()=>({retry:s.retry}),[]),i.default.useMemo(()=>{var t;return a.loading||a.error?i.default.createElement(r.loading,{isLoading:a.loading,pastDelay:a.pastDelay,timedOut:a.timedOut,error:a.error,retry:s.retry}):a.loaded?i.default.createElement((t=a.loaded)&&t.default?t.default:t,e):null},[e,a])}return u.preload=()=>l(),u.displayName="LoadableComponent",i.default.forwardRef(u)}(l,e)}function c(e,t){let r=[];for(;e.length;){let i=e.pop();r.push(i(t))}return Promise.all(r).then(()=>{if(e.length)return c(e,t)})}u.preloadAll=()=>new Promise((e,t)=>{c(s).then(e,t)}),u.preloadReady=e=>(void 0===e&&(e=[]),new Promise(t=>{let r=()=>(o=!0,t());c(a,e).then(r,r)})),window.__NEXT_PRELOADREADY=u.preloadReady;let p=u},76720:function(e,t,r){"use strict";var i=r(93967),n=r.n(i),s=r(67294),a=r(70861),o=r(97400),l=r(85893);let d=s.forwardRef((e,t)=>{let{as:r,bsPrefix:i,variant:s="primary",size:d,active:u=!1,disabled:c=!1,className:p,...h}=e,f=(0,o.vE)(i,"btn"),[m,{tagName:v}]=(0,a.FT)({tagName:r,disabled:c,...h});return(0,l.jsx)(v,{...m,...h,ref:t,disabled:c,className:n()(p,f,u&&"active",s&&"".concat(f,"-").concat(s),d&&"".concat(f,"-").concat(d),h.href&&c&&"disabled")})});d.displayName="Button",t.Z=d},63699:function(){},16303:function(){},12479:function(){},57638:function(){},64172:function(){},5152:function(e,t,r){e.exports=r(32602)},4298:function(e,t,r){e.exports=r(23381)},4511:function(e,t,r){"use strict";r.d(t,{SV:function(){return a}});var i=r(67294);let n=(0,i.createContext)(null),s={didCatch:!1,error:null};class a extends i.Component{constructor(e){super(e),this.resetErrorBoundary=this.resetErrorBoundary.bind(this),this.state=s}static getDerivedStateFromError(e){return{didCatch:!0,error:e}}resetErrorBoundary(){let{error:e}=this.state;if(null!==e){for(var t,r,i=arguments.length,n=Array(i),a=0;a<i;a++)n[a]=arguments[a];null===(t=(r=this.props).onReset)||void 0===t||t.call(r,{args:n,reason:"imperative-api"}),this.setState(s)}}componentDidCatch(e,t){var r,i;null===(r=(i=this.props).onError)||void 0===r||r.call(i,e,t)}componentDidUpdate(e,t){let{didCatch:r}=this.state,{resetKeys:i}=this.props;if(r&&null!==t.error&&function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return e.length!==t.length||e.some((e,r)=>!Object.is(e,t[r]))}(e.resetKeys,i)){var n,a;null===(n=(a=this.props).onReset)||void 0===n||n.call(a,{next:i,prev:e.resetKeys,reason:"keys"}),this.setState(s)}}render(){let{children:e,fallbackRender:t,FallbackComponent:r,fallback:s}=this.props,{didCatch:a,error:o}=this.state,l=e;if(a){let e={error:o,resetErrorBoundary:this.resetErrorBoundary};if("function"==typeof t)l=t(e);else if(r)l=(0,i.createElement)(r,e);else if(void 0!==s)l=s;else throw o}return(0,i.createElement)(n.Provider,{value:{didCatch:a,error:o,resetErrorBoundary:this.resetErrorBoundary}},l)}}},54208:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});var i=r(67294),n=function(){return(n=Object.assign||function(e){for(var t,r=1,i=arguments.length;r<i;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}).apply(this,arguments)},s=i.forwardRef(function(e,t){var r=i.useState(!1),s=r[0],a=r[1],o=i.useState(!1),l=o[0],d=o[1],u=encodeURIComponent(e.id),c="string"==typeof e.playlistCoverId?encodeURIComponent(e.playlistCoverId):null,p=e.title,h=e.poster||"hqdefault",f="&".concat(e.params)||0,m=e.muted?"&mute=1":"",v=e.announce||"Watch",g=e.webp?"webp":"jpg",y=e.webp?"vi_webp":"vi",w=e.thumbnail||(e.playlist?"https://i.ytimg.com/".concat(y,"/").concat(c,"/").concat(h,".").concat(g):"https://i.ytimg.com/".concat(y,"/").concat(u,"/").concat(h,".").concat(g)),b=e.noCookie?"https://www.youtube-nocookie.com":"https://www.youtube.com";b=e.cookie?"https://www.youtube.com":"https://www.youtube-nocookie.com";var E=e.playlist?"".concat(b,"/embed/videoseries?autoplay=1").concat(m,"&list=").concat(u).concat(f):"".concat(b,"/embed/").concat(u,"?autoplay=1&state=1").concat(m).concat(f),T=e.activatedClass||"lyt-activated",S=e.adNetwork||!1,x=e.aspectHeight||9,k=e.aspectWidth||16,P=e.iframeClass||"",C=e.playerClass||"lty-playbtn",M=e.wrapperClass||"yt-lite",_=e.onIframeAdded||function(){},O=e.rel?"prefetch":"preload",L=e.containerElement||"article";return i.useEffect(function(){l&&_()},[l]),i.createElement(i.Fragment,null,i.createElement("link",{rel:O,href:w,as:"image"}),i.createElement(i.Fragment,null,s&&i.createElement(i.Fragment,null,i.createElement("link",{rel:"preconnect",href:b}),i.createElement("link",{rel:"preconnect",href:"https://www.google.com"}),S&&i.createElement(i.Fragment,null,i.createElement("link",{rel:"preconnect",href:"https://static.doubleclick.net"}),i.createElement("link",{rel:"preconnect",href:"https://googleads.g.doubleclick.net"})))),i.createElement(L,{onPointerOver:function(){s||a(!0)},onClick:function(){l||d(!0)},className:"".concat(M," ").concat(l?T:""),"data-title":p,style:n({backgroundImage:"url(".concat(w,")")},{"--aspect-ratio":"".concat(x/k*100,"%")})},i.createElement("button",{type:"button",className:C,"aria-label":"".concat(v," ").concat(p)}),l&&i.createElement("iframe",{ref:t,className:P,title:p,width:"560",height:"315",frameBorder:"0",allow:"accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0,src:E})))})},18357:function(e,t,r){"use strict";r.d(t,{YD:function(){return d}});var i=r(67294),n=Object.defineProperty,s=new Map,a=new WeakMap,o=0,l=void 0;function d({threshold:e,delay:t,trackVisibility:r,rootMargin:n,root:d,triggerOnce:u,skip:c,initialInView:p,fallbackInView:h,onChange:f}={}){var m;let[v,g]=i.useState(null),y=i.useRef(f),[w,b]=i.useState({inView:!!p,entry:void 0});y.current=f,i.useEffect(()=>{let i;if(!c&&v)return i=function(e,t,r={},i=l){if(void 0===window.IntersectionObserver&&void 0!==i){let n=e.getBoundingClientRect();return t(i,{isIntersecting:i,target:e,intersectionRatio:"number"==typeof r.threshold?r.threshold:0,time:0,boundingClientRect:n,intersectionRect:n,rootBounds:n}),()=>{}}let{id:n,observer:d,elements:u}=function(e){let t=Object.keys(e).sort().filter(t=>void 0!==e[t]).map(t=>{var r;return`${t}_${"root"===t?(r=e.root)?(a.has(r)||(o+=1,a.set(r,o.toString())),a.get(r)):"0":e[t]}`}).toString(),r=s.get(t);if(!r){let i;let n=new Map,a=new IntersectionObserver(t=>{t.forEach(t=>{var r;let s=t.isIntersecting&&i.some(e=>t.intersectionRatio>=e);e.trackVisibility&&void 0===t.isVisible&&(t.isVisible=s),null==(r=n.get(t.target))||r.forEach(e=>{e(s,t)})})},e);i=a.thresholds||(Array.isArray(e.threshold)?e.threshold:[e.threshold||0]),r={id:t,observer:a,elements:n},s.set(t,r)}return r}(r),c=u.get(e)||[];return u.has(e)||u.set(e,c),c.push(t),d.observe(e),function(){c.splice(c.indexOf(t),1),0===c.length&&(u.delete(e),d.unobserve(e)),0===u.size&&(d.disconnect(),s.delete(n))}}(v,(e,t)=>{b({inView:e,entry:t}),y.current&&y.current(e,t),t.isIntersecting&&u&&i&&(i(),i=void 0)},{root:d,rootMargin:n,threshold:e,trackVisibility:r,delay:t},h),()=>{i&&i()}},[Array.isArray(e)?e.toString():e,v,d,n,u,c,r,h,t]);let E=null==(m=w.entry)?void 0:m.target,T=i.useRef(void 0);v||!E||u||c||T.current===E||(T.current=E,b({inView:!!p,entry:void 0}));let S=[g,w.inView,w.entry];return S.ref=S[0],S.inView=S[1],S.entry=S[2],S}i.Component},72074:function(e,t,r){"use strict";r.d(t,{pt:function(){return o},xW:function(){return l},N1:function(){return s},W_:function(){return a}});var i=r(13433),n=r(7185);function s(e){let{swiper:t,extendParams:r,on:s,emit:a}=e,o=(0,i.g)(),l=(0,i.a)();function d(e){if(!t.enabled)return;let{rtlTranslate:r}=t,i=e;i.originalEvent&&(i=i.originalEvent);let s=i.keyCode||i.charCode,d=t.params.keyboard.pageUpDown,u=d&&33===s,c=d&&34===s,p=37===s,h=39===s,f=38===s,m=40===s;if(!t.allowSlideNext&&(t.isHorizontal()&&h||t.isVertical()&&m||c)||!t.allowSlidePrev&&(t.isHorizontal()&&p||t.isVertical()&&f||u))return!1;if(!i.shiftKey&&!i.altKey&&!i.ctrlKey&&!i.metaKey&&(!o.activeElement||!o.activeElement.nodeName||"input"!==o.activeElement.nodeName.toLowerCase()&&"textarea"!==o.activeElement.nodeName.toLowerCase())){if(t.params.keyboard.onlyInViewport&&(u||c||p||h||f||m)){let e=!1;if((0,n.a)(t.el,`.${t.params.slideClass}, swiper-slide`).length>0&&0===(0,n.a)(t.el,`.${t.params.slideActiveClass}`).length)return;let i=t.el,s=i.clientWidth,a=i.clientHeight,o=l.innerWidth,d=l.innerHeight,u=(0,n.b)(i);r&&(u.left-=i.scrollLeft);let c=[[u.left,u.top],[u.left+s,u.top],[u.left,u.top+a],[u.left+s,u.top+a]];for(let t=0;t<c.length;t+=1){let r=c[t];if(r[0]>=0&&r[0]<=o&&r[1]>=0&&r[1]<=d){if(0===r[0]&&0===r[1])continue;e=!0}}if(!e)return}t.isHorizontal()?((u||c||p||h)&&(i.preventDefault?i.preventDefault():i.returnValue=!1),((c||h)&&!r||(u||p)&&r)&&t.slideNext(),((u||p)&&!r||(c||h)&&r)&&t.slidePrev()):((u||c||f||m)&&(i.preventDefault?i.preventDefault():i.returnValue=!1),(c||m)&&t.slideNext(),(u||f)&&t.slidePrev()),a("keyPress",s)}}function u(){t.keyboard.enabled||(o.addEventListener("keydown",d),t.keyboard.enabled=!0)}function c(){t.keyboard.enabled&&(o.removeEventListener("keydown",d),t.keyboard.enabled=!1)}t.keyboard={enabled:!1},r({keyboard:{enabled:!1,onlyInViewport:!0,pageUpDown:!0}}),s("init",()=>{t.params.keyboard.enabled&&u()}),s("destroy",()=>{t.keyboard.enabled&&c()}),Object.assign(t.keyboard,{enable:u,disable:c})}function a(e){let{swiper:t,extendParams:r,on:i,emit:s}=e;function a(e){let r;return e&&"string"==typeof e&&t.isElement&&(r=t.el.querySelector(e)||t.hostEl.querySelector(e))?r:(e&&("string"==typeof e&&(r=[...document.querySelectorAll(e)]),t.params.uniqueNavElements&&"string"==typeof e&&r&&r.length>1&&1===t.el.querySelectorAll(e).length?r=t.el.querySelector(e):r&&1===r.length&&(r=r[0])),e&&!r)?e:r}function o(e,r){let i=t.params.navigation;(e=(0,n.m)(e)).forEach(e=>{e&&(e.classList[r?"add":"remove"](...i.disabledClass.split(" ")),"BUTTON"===e.tagName&&(e.disabled=r),t.params.watchOverflow&&t.enabled&&e.classList[t.isLocked?"add":"remove"](i.lockClass))})}function l(){let{nextEl:e,prevEl:r}=t.navigation;if(t.params.loop){o(r,!1),o(e,!1);return}o(r,t.isBeginning&&!t.params.rewind),o(e,t.isEnd&&!t.params.rewind)}function d(e){e.preventDefault(),(!t.isBeginning||t.params.loop||t.params.rewind)&&(t.slidePrev(),s("navigationPrev"))}function u(e){e.preventDefault(),(!t.isEnd||t.params.loop||t.params.rewind)&&(t.slideNext(),s("navigationNext"))}function c(){var e,r,i;let s=t.params.navigation;if(t.params.navigation=(e=t.originalParams.navigation,r=t.params.navigation,i={nextEl:"swiper-button-next",prevEl:"swiper-button-prev"},t.params.createElements&&Object.keys(i).forEach(s=>{if(!r[s]&&!0===r.auto){let a=(0,n.e)(t.el,`.${i[s]}`)[0];a||((a=(0,n.c)("div",i[s])).className=i[s],t.el.append(a)),r[s]=a,e[s]=a}}),r),!(s.nextEl||s.prevEl))return;let o=a(s.nextEl),l=a(s.prevEl);Object.assign(t.navigation,{nextEl:o,prevEl:l}),o=(0,n.m)(o),l=(0,n.m)(l);let c=(e,r)=>{e&&e.addEventListener("click","next"===r?u:d),!t.enabled&&e&&e.classList.add(...s.lockClass.split(" "))};o.forEach(e=>c(e,"next")),l.forEach(e=>c(e,"prev"))}function p(){let{nextEl:e,prevEl:r}=t.navigation;e=(0,n.m)(e),r=(0,n.m)(r);let i=(e,r)=>{e.removeEventListener("click","next"===r?u:d),e.classList.remove(...t.params.navigation.disabledClass.split(" "))};e.forEach(e=>i(e,"next")),r.forEach(e=>i(e,"prev"))}r({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),t.navigation={nextEl:null,prevEl:null},i("init",()=>{!1===t.params.navigation.enabled?h():(c(),l())}),i("toEdge fromEdge lock unlock",()=>{l()}),i("destroy",()=>{p()}),i("enable disable",()=>{let{nextEl:e,prevEl:r}=t.navigation;if(e=(0,n.m)(e),r=(0,n.m)(r),t.enabled){l();return}[...e,...r].filter(e=>!!e).forEach(e=>e.classList.add(t.params.navigation.lockClass))}),i("click",(e,r)=>{let{nextEl:i,prevEl:a}=t.navigation;i=(0,n.m)(i),a=(0,n.m)(a);let o=r.target,l=a.includes(o)||i.includes(o);if(t.isElement&&!l){let e=r.path||r.composedPath&&r.composedPath();e&&(l=e.find(e=>i.includes(e)||a.includes(e)))}if(t.params.navigation.hideOnClick&&!l){let e;if(t.pagination&&t.params.pagination&&t.params.pagination.clickable&&(t.pagination.el===o||t.pagination.el.contains(o)))return;i.length?e=i[0].classList.contains(t.params.navigation.hiddenClass):a.length&&(e=a[0].classList.contains(t.params.navigation.hiddenClass)),!0===e?s("navigationShow"):s("navigationHide"),[...i,...a].filter(e=>!!e).forEach(e=>e.classList.toggle(t.params.navigation.hiddenClass))}});let h=()=>{t.el.classList.add(...t.params.navigation.navigationDisabledClass.split(" ")),p()};Object.assign(t.navigation,{enable:()=>{t.el.classList.remove(...t.params.navigation.navigationDisabledClass.split(" ")),c(),l()},disable:h,update:l,init:c,destroy:p})}function o(e){let t,r,n,s,a,o,l,d,u,c,{swiper:p,extendParams:h,on:f,emit:m,params:v}=e;p.autoplay={running:!1,paused:!1,timeLeft:0},h({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!1,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}});let g=v&&v.autoplay?v.autoplay.delay:3e3,y=v&&v.autoplay?v.autoplay.delay:3e3,w=new Date().getTime();function b(e){p&&!p.destroyed&&p.wrapperEl&&e.target===p.wrapperEl&&(p.wrapperEl.removeEventListener("transitionend",b),!c&&(!e.detail||!e.detail.bySwiperTouchMove)&&C())}let E=()=>{if(p.destroyed||!p.autoplay.running)return;p.autoplay.paused?s=!0:s&&(y=n,s=!1);let e=p.autoplay.paused?n:w+y-new Date().getTime();p.autoplay.timeLeft=e,m("autoplayTimeLeft",e,e/g),r=requestAnimationFrame(()=>{E()})},T=()=>{let e;if(e=p.virtual&&p.params.virtual.enabled?p.slides.find(e=>e.classList.contains("swiper-slide-active")):p.slides[p.activeIndex])return parseInt(e.getAttribute("data-swiper-autoplay"),10)},S=e=>{if(p.destroyed||!p.autoplay.running)return;cancelAnimationFrame(r),E();let i=void 0===e?p.params.autoplay.delay:e;g=p.params.autoplay.delay,y=p.params.autoplay.delay;let s=T();!Number.isNaN(s)&&s>0&&void 0===e&&(i=s,g=s,y=s),n=i;let a=p.params.speed,o=()=>{p&&!p.destroyed&&(p.params.autoplay.reverseDirection?!p.isBeginning||p.params.loop||p.params.rewind?(p.slidePrev(a,!0,!0),m("autoplay")):p.params.autoplay.stopOnLastSlide||(p.slideTo(p.slides.length-1,a,!0,!0),m("autoplay")):!p.isEnd||p.params.loop||p.params.rewind?(p.slideNext(a,!0,!0),m("autoplay")):p.params.autoplay.stopOnLastSlide||(p.slideTo(0,a,!0,!0),m("autoplay")),p.params.cssMode&&(w=new Date().getTime(),requestAnimationFrame(()=>{S()})))};return i>0?(clearTimeout(t),t=setTimeout(()=>{o()},i)):requestAnimationFrame(()=>{o()}),i},x=()=>{w=new Date().getTime(),p.autoplay.running=!0,S(),m("autoplayStart")},k=()=>{p.autoplay.running=!1,clearTimeout(t),cancelAnimationFrame(r),m("autoplayStop")},P=(e,r)=>{if(p.destroyed||!p.autoplay.running)return;clearTimeout(t),e||(u=!0);let i=()=>{m("autoplayPause"),p.params.autoplay.waitForTransition?p.wrapperEl.addEventListener("transitionend",b):C()};if(p.autoplay.paused=!0,r){d&&(n=p.params.autoplay.delay),d=!1,i();return}n=(n||p.params.autoplay.delay)-(new Date().getTime()-w),p.isEnd&&n<0&&!p.params.loop||(n<0&&(n=0),i())},C=()=>{p.isEnd&&n<0&&!p.params.loop||p.destroyed||!p.autoplay.running||(w=new Date().getTime(),u?(u=!1,S(n)):S(),p.autoplay.paused=!1,m("autoplayResume"))},M=()=>{if(p.destroyed||!p.autoplay.running)return;let e=(0,i.g)();"hidden"===e.visibilityState&&(u=!0,P(!0)),"visible"===e.visibilityState&&C()},_=e=>{"mouse"===e.pointerType&&(u=!0,c=!0,p.animating||p.autoplay.paused||P(!0))},O=e=>{"mouse"===e.pointerType&&(c=!1,p.autoplay.paused&&C())},L=()=>{p.params.autoplay.pauseOnMouseEnter&&(p.el.addEventListener("pointerenter",_),p.el.addEventListener("pointerleave",O))},A=()=>{p.el&&"string"!=typeof p.el&&(p.el.removeEventListener("pointerenter",_),p.el.removeEventListener("pointerleave",O))},I=()=>{(0,i.g)().addEventListener("visibilitychange",M)},j=()=>{(0,i.g)().removeEventListener("visibilitychange",M)};f("init",()=>{p.params.autoplay.enabled&&(L(),I(),x())}),f("destroy",()=>{A(),j(),p.autoplay.running&&k()}),f("_freeModeStaticRelease",()=>{(o||u)&&C()}),f("_freeModeNoMomentumRelease",()=>{p.params.autoplay.disableOnInteraction?k():P(!0,!0)}),f("beforeTransitionStart",(e,t,r)=>{!p.destroyed&&p.autoplay.running&&(r||!p.params.autoplay.disableOnInteraction?P(!0,!0):k())}),f("sliderFirstMove",()=>{if(!p.destroyed&&p.autoplay.running){if(p.params.autoplay.disableOnInteraction){k();return}a=!0,o=!1,u=!1,l=setTimeout(()=>{u=!0,o=!0,P(!0)},200)}}),f("touchEnd",()=>{if(!p.destroyed&&p.autoplay.running&&a){if(clearTimeout(l),clearTimeout(t),p.params.autoplay.disableOnInteraction){o=!1,a=!1;return}o&&p.params.cssMode&&C(),o=!1,a=!1}}),f("slideChange",()=>{!p.destroyed&&p.autoplay.running&&(d=!0)}),Object.assign(p.autoplay,{start:x,stop:k,pause:P,resume:C})}function l(e){let{swiper:t,extendParams:r,on:i}=e;r({fadeEffect:{crossFade:!1}}),function(e){let t;let{effect:r,swiper:i,on:n,setTranslate:s,setTransition:a,overwriteParams:o,perspective:l,recreateShadows:d,getEffectParams:u}=e;n("beforeInit",()=>{if(i.params.effect!==r)return;i.classNames.push(`${i.params.containerModifierClass}${r}`),l&&l()&&i.classNames.push(`${i.params.containerModifierClass}3d`);let e=o?o():{};Object.assign(i.params,e),Object.assign(i.originalParams,e)}),n("setTranslate",()=>{i.params.effect===r&&s()}),n("setTransition",(e,t)=>{i.params.effect===r&&a(t)}),n("transitionEnd",()=>{i.params.effect===r&&d&&u&&u().slideShadows&&(i.slides.forEach(e=>{e.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(e=>e.remove())}),d())}),n("virtualUpdate",()=>{i.params.effect===r&&(i.slides.length||(t=!0),requestAnimationFrame(()=>{t&&i.slides&&i.slides.length&&(s(),t=!1)}))})}({effect:"fade",swiper:t,on:i,setTranslate:()=>{let{slides:e}=t;t.params.fadeEffect;for(let r=0;r<e.length;r+=1){let e=t.slides[r],i=-e.swiperSlideOffset;t.params.virtualTranslate||(i-=t.translate);let s=0;t.isHorizontal()||(s=i,i=0);let a=t.params.fadeEffect.crossFade?Math.max(1-Math.abs(e.progress),0):1+Math.min(Math.max(e.progress,-1),0),o=function(e,t){let r=(0,n.g)(t);return r!==t&&(r.style.backfaceVisibility="hidden",r.style["-webkit-backface-visibility"]="hidden"),r}(0,e);o.style.opacity=a,o.style.transform=`translate3d(${i}px, ${s}px, 0px)`}},setTransition:e=>{let r=t.slides.map(e=>(0,n.g)(e));r.forEach(t=>{t.style.transitionDuration=`${e}ms`}),function(e){let{swiper:t,duration:r,transformElements:i,allSlides:s}=e,{activeIndex:a}=t,o=e=>e.parentElement?e.parentElement:t.slides.find(t=>t.shadowRoot&&t.shadowRoot===e.parentNode);if(t.params.virtualTranslate&&0!==r){let e=!1;(s?i:i.filter(e=>{let r=e.classList.contains("swiper-slide-transform")?o(e):e;return t.getSlideIndex(r)===a})).forEach(r=>{(0,n.k)(r,()=>{if(e||!t||t.destroyed)return;e=!0,t.animating=!1;let r=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0});t.wrapperEl.dispatchEvent(r)})})}}({swiper:t,duration:e,transformElements:r,allSlides:!0})},overwriteParams:()=>({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!t.params.cssMode})})}},13433:function(e,t,r){"use strict";function i(e){return null!==e&&"object"==typeof e&&"constructor"in e&&e.constructor===Object}function n(e,t){void 0===e&&(e={}),void 0===t&&(t={});let r=["__proto__","constructor","prototype"];Object.keys(t).filter(e=>0>r.indexOf(e)).forEach(r=>{void 0===e[r]?e[r]=t[r]:i(t[r])&&i(e[r])&&Object.keys(t[r]).length>0&&n(e[r],t[r])})}r.d(t,{a:function(){return l},g:function(){return a}});let s={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector:()=>null,querySelectorAll:()=>[],getElementById:()=>null,createEvent:()=>({initEvent(){}}),createElement:()=>({children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName:()=>[]}),createElementNS:()=>({}),importNode:()=>null,location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function a(){let e="undefined"!=typeof document?document:{};return n(e,s),e}let o={document:s,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle:()=>({getPropertyValue:()=>""}),Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia:()=>({}),requestAnimationFrame:e=>"undefined"==typeof setTimeout?(e(),null):setTimeout(e,0),cancelAnimationFrame(e){"undefined"!=typeof setTimeout&&clearTimeout(e)}};function l(){let e="undefined"!=typeof window?window:{};return n(e,o),e}},7185:function(e,t,r){"use strict";r.d(t,{a:function(){return E},b:function(){return v},c:function(){return m},d:function(){return a},e:function(){return p},f:function(){return S},g:function(){return c},h:function(){return b},j:function(){return o},k:function(){return T},m:function(){return x},n:function(){return s},p:function(){return w},q:function(){return y},r:function(){return g},s:function(){return d},t:function(){return u},u:function(){return f},v:function(){return h},w:function(){return function e(){let t=Object(arguments.length<=0?void 0:arguments[0]),r=["__proto__","constructor","prototype"];for(let i=1;i<arguments.length;i+=1){let n=i<0||arguments.length<=i?void 0:arguments[i];if(null!=n&&("undefined"!=typeof window&&void 0!==window.HTMLElement?!(n instanceof HTMLElement):!n||1!==n.nodeType&&11!==n.nodeType)){let i=Object.keys(Object(n)).filter(e=>0>r.indexOf(e));for(let r=0,s=i.length;r<s;r+=1){let s=i[r],a=Object.getOwnPropertyDescriptor(n,s);void 0!==a&&a.enumerable&&(l(t[s])&&l(n[s])?n[s].__swiper__?t[s]=n[s]:e(t[s],n[s]):!l(t[s])&&l(n[s])?(t[s]={},n[s].__swiper__?t[s]=n[s]:e(t[s],n[s])):t[s]=n[s])}}}return t}},x:function(){return n}});var i=r(13433);function n(e){Object.keys(e).forEach(t=>{try{e[t]=null}catch(e){}try{delete e[t]}catch(e){}})}function s(e,t){return void 0===t&&(t=0),setTimeout(e,t)}function a(){return Date.now()}function o(e,t){let r,n,s;void 0===t&&(t="x");let a=(0,i.a)(),o=function(e){let t;let r=(0,i.a)();return r.getComputedStyle&&(t=r.getComputedStyle(e,null)),!t&&e.currentStyle&&(t=e.currentStyle),t||(t=e.style),t}(e);return a.WebKitCSSMatrix?((n=o.transform||o.webkitTransform).split(",").length>6&&(n=n.split(", ").map(e=>e.replace(",",".")).join(", ")),s=new a.WebKitCSSMatrix("none"===n?"":n)):r=(s=o.MozTransform||o.OTransform||o.MsTransform||o.msTransform||o.transform||o.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,")).toString().split(","),"x"===t&&(n=a.WebKitCSSMatrix?s.m41:16===r.length?parseFloat(r[12]):parseFloat(r[4])),"y"===t&&(n=a.WebKitCSSMatrix?s.m42:16===r.length?parseFloat(r[13]):parseFloat(r[5])),n||0}function l(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)}function d(e,t,r){e.style.setProperty(t,r)}function u(e){let t,{swiper:r,targetPosition:n,side:s}=e,a=(0,i.a)(),o=-r.translate,l=null,d=r.params.speed;r.wrapperEl.style.scrollSnapType="none",a.cancelAnimationFrame(r.cssModeFrameID);let u=n>o?"next":"prev",c=(e,t)=>"next"===u&&e>=t||"prev"===u&&e<=t,p=()=>{t=new Date().getTime(),null===l&&(l=t);let e=o+(.5-Math.cos(Math.max(Math.min((t-l)/d,1),0)*Math.PI)/2)*(n-o);if(c(e,n)&&(e=n),r.wrapperEl.scrollTo({[s]:e}),c(e,n)){r.wrapperEl.style.overflow="hidden",r.wrapperEl.style.scrollSnapType="",setTimeout(()=>{r.wrapperEl.style.overflow="",r.wrapperEl.scrollTo({[s]:e})}),a.cancelAnimationFrame(r.cssModeFrameID);return}r.cssModeFrameID=a.requestAnimationFrame(p)};p()}function c(e){return e.querySelector(".swiper-slide-transform")||e.shadowRoot&&e.shadowRoot.querySelector(".swiper-slide-transform")||e}function p(e,t){void 0===t&&(t="");let r=(0,i.a)(),n=[...e.children];return(r.HTMLSlotElement&&e instanceof HTMLSlotElement&&n.push(...e.assignedElements()),t)?n.filter(e=>e.matches(t)):n}function h(e,t){let r=(0,i.a)(),n=t.contains(e);return!n&&r.HTMLSlotElement&&t instanceof HTMLSlotElement&&!(n=[...t.assignedElements()].includes(e))&&(n=function(e,t){let r=[t];for(;r.length>0;){let t=r.shift();if(e===t)return!0;r.push(...t.children,...t.shadowRoot?t.shadowRoot.children:[],...t.assignedElements?t.assignedElements():[])}}(e,t)),n}function f(e){try{console.warn(e);return}catch(e){}}function m(e,t){var r;void 0===t&&(t=[]);let i=document.createElement(e);return i.classList.add(...Array.isArray(t)?t:(void 0===(r=t)&&(r=""),r.trim().split(" ").filter(e=>!!e.trim()))),i}function v(e){let t=(0,i.a)(),r=(0,i.g)(),n=e.getBoundingClientRect(),s=r.body,a=e.clientTop||s.clientTop||0,o=e.clientLeft||s.clientLeft||0,l=e===t?t.scrollY:e.scrollTop,d=e===t?t.scrollX:e.scrollLeft;return{top:n.top+l-a,left:n.left+d-o}}function g(e,t){let r=[];for(;e.previousElementSibling;){let i=e.previousElementSibling;t?i.matches(t)&&r.push(i):r.push(i),e=i}return r}function y(e,t){let r=[];for(;e.nextElementSibling;){let i=e.nextElementSibling;t?i.matches(t)&&r.push(i):r.push(i),e=i}return r}function w(e,t){return(0,i.a)().getComputedStyle(e,null).getPropertyValue(t)}function b(e){let t,r=e;if(r){for(t=0;null!==(r=r.previousSibling);)1===r.nodeType&&(t+=1);return t}}function E(e,t){let r=[],i=e.parentElement;for(;i;)t?i.matches(t)&&r.push(i):r.push(i),i=i.parentElement;return r}function T(e,t){t&&e.addEventListener("transitionend",function r(i){i.target===e&&(t.call(e,i),e.removeEventListener("transitionend",r))})}function S(e,t,r){let n=(0,i.a)();return r?e["width"===t?"offsetWidth":"offsetHeight"]+parseFloat(n.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-right":"margin-top"))+parseFloat(n.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-left":"margin-bottom")):e.offsetWidth}function x(e){return(Array.isArray(e)?e:[e]).filter(e=>!!e)}},2261:function(e,t,r){"use strict";let i,n,s;r.d(t,{tq:function(){return W},o5:function(){return Y}});var a=r(67294),o=r(13433),l=r(7185);function d(){return i||(i=function(){let e=(0,o.a)(),t=(0,o.g)();return{smoothScroll:t.documentElement&&t.documentElement.style&&"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch)}}()),i}function u(e){return void 0===e&&(e={}),n||(n=function(e){let{userAgent:t}=void 0===e?{}:e,r=d(),i=(0,o.a)(),n=i.navigator.platform,s=t||i.navigator.userAgent,a={ios:!1,android:!1},l=i.screen.width,u=i.screen.height,c=s.match(/(Android);?[\s\/]+([\d.]+)?/),p=s.match(/(iPad).*OS\s([\d_]+)/),h=s.match(/(iPod)(.*OS\s([\d_]+))?/),f=!p&&s.match(/(iPhone\sOS|iOS)\s([\d_]+)/),m="MacIntel"===n;return!p&&m&&r.touch&&["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf(`${l}x${u}`)>=0&&((p=s.match(/(Version)\/([\d.]+)/))||(p=[0,1,"13_0_0"]),m=!1),c&&"Win32"!==n&&(a.os="android",a.android=!0),(p||f||h)&&(a.os="ios",a.ios=!0),a}(e)),n}function c(){return s||(s=function(){let e=(0,o.a)(),t=u(),r=!1;function i(){let t=e.navigator.userAgent.toLowerCase();return t.indexOf("safari")>=0&&0>t.indexOf("chrome")&&0>t.indexOf("android")}if(i()){let t=String(e.navigator.userAgent);if(t.includes("Version/")){let[e,i]=t.split("Version/")[1].split(" ")[0].split(".").map(e=>Number(e));r=e<16||16===e&&i<2}}let n=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent),s=i(),a=s||n&&t.ios;return{isSafari:r||s,needPerspectiveFix:r,need3dFix:a,isWebView:n}}()),s}let p=(e,t,r)=>{t&&!e.classList.contains(r)?e.classList.add(r):!t&&e.classList.contains(r)&&e.classList.remove(r)},h=(e,t,r)=>{t&&!e.classList.contains(r)?e.classList.add(r):!t&&e.classList.contains(r)&&e.classList.remove(r)},f=(e,t)=>{if(!e||e.destroyed||!e.params)return;let r=t.closest(e.isElement?"swiper-slide":`.${e.params.slideClass}`);if(r){let t=r.querySelector(`.${e.params.lazyPreloaderClass}`);!t&&e.isElement&&(r.shadowRoot?t=r.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{r.shadowRoot&&(t=r.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`))&&t.remove()})),t&&t.remove()}},m=(e,t)=>{if(!e.slides[t])return;let r=e.slides[t].querySelector('[loading="lazy"]');r&&r.removeAttribute("loading")},v=e=>{if(!e||e.destroyed||!e.params)return;let t=e.params.lazyPreloadPrevNext,r=e.slides.length;if(!r||!t||t<0)return;t=Math.min(t,r);let i="auto"===e.params.slidesPerView?e.slidesPerViewDynamic():Math.ceil(e.params.slidesPerView),n=e.activeIndex;if(e.params.grid&&e.params.grid.rows>1){let r=[n-t];r.push(...Array.from({length:t}).map((e,t)=>n+i+t)),e.slides.forEach((t,i)=>{r.includes(t.column)&&m(e,i)});return}let s=n+i-1;if(e.params.rewind||e.params.loop)for(let i=n-t;i<=s+t;i+=1){let t=(i%r+r)%r;(t<n||t>s)&&m(e,t)}else for(let i=Math.max(n-t,0);i<=Math.min(s+t,r-1);i+=1)i!==n&&(i>s||i<n)&&m(e,i)};function g(e){let{swiper:t,runCallbacks:r,direction:i,step:n}=e,{activeIndex:s,previousIndex:a}=t,o=i;if(o||(o=s>a?"next":s<a?"prev":"reset"),t.emit(`transition${n}`),r&&s!==a){if("reset"===o){t.emit(`slideResetTransition${n}`);return}t.emit(`slideChangeTransition${n}`),"next"===o?t.emit(`slideNextTransition${n}`):t.emit(`slidePrevTransition${n}`)}}function y(e,t,r){let i=(0,o.a)(),{params:n}=e,s=n.edgeSwipeDetection,a=n.edgeSwipeThreshold;return!s||!(r<=a)&&!(r>=i.innerWidth-a)||"prevent"===s&&(t.preventDefault(),!0)}function w(e){let t=(0,o.g)(),r=e;r.originalEvent&&(r=r.originalEvent);let i=this.touchEventsData;if("pointerdown"===r.type){if(null!==i.pointerId&&i.pointerId!==r.pointerId)return;i.pointerId=r.pointerId}else"touchstart"===r.type&&1===r.targetTouches.length&&(i.touchId=r.targetTouches[0].identifier);if("touchstart"===r.type){y(this,r,r.targetTouches[0].pageX);return}let{params:n,touches:s,enabled:a}=this;if(!a||!n.simulateTouch&&"mouse"===r.pointerType||this.animating&&n.preventInteractionOnTransition)return;!this.animating&&n.cssMode&&n.loop&&this.loopFix();let d=r.target;if("wrapper"===n.touchEventsTarget&&!(0,l.v)(d,this.wrapperEl)||"which"in r&&3===r.which||"button"in r&&r.button>0||i.isTouched&&i.isMoved)return;let u=!!n.noSwipingClass&&""!==n.noSwipingClass,c=r.composedPath?r.composedPath():r.path;u&&r.target&&r.target.shadowRoot&&c&&(d=c[0]);let p=n.noSwipingSelector?n.noSwipingSelector:`.${n.noSwipingClass}`,h=!!(r.target&&r.target.shadowRoot);if(n.noSwiping&&(h?function(e,t){return void 0===t&&(t=this),function t(r){if(!r||r===(0,o.g)()||r===(0,o.a)())return null;r.assignedSlot&&(r=r.assignedSlot);let i=r.closest(e);return i||r.getRootNode?i||t(r.getRootNode().host):null}(t)}(p,d):d.closest(p))){this.allowClick=!0;return}if(n.swipeHandler&&!d.closest(n.swipeHandler))return;s.currentX=r.pageX,s.currentY=r.pageY;let f=s.currentX,m=s.currentY;if(!y(this,r,f))return;Object.assign(i,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),s.startX=f,s.startY=m,i.touchStartTime=(0,l.d)(),this.allowClick=!0,this.updateSize(),this.swipeDirection=void 0,n.threshold>0&&(i.allowThresholdMove=!1);let v=!0;d.matches(i.focusableElements)&&(v=!1,"SELECT"===d.nodeName&&(i.isTouched=!1)),t.activeElement&&t.activeElement.matches(i.focusableElements)&&t.activeElement!==d&&("mouse"===r.pointerType||"mouse"!==r.pointerType&&!d.matches(i.focusableElements))&&t.activeElement.blur();let g=v&&this.allowTouchMove&&n.touchStartPreventDefault;(n.touchStartForcePreventDefault||g)&&!d.isContentEditable&&r.preventDefault(),n.freeMode&&n.freeMode.enabled&&this.freeMode&&this.animating&&!n.cssMode&&this.freeMode.onTouchStart(),this.emit("touchStart",r)}function b(e){let t,r;let i=(0,o.g)(),n=this.touchEventsData,{params:s,touches:a,rtlTranslate:d,enabled:u}=this;if(!u||!s.simulateTouch&&"mouse"===e.pointerType)return;let c=e;if(c.originalEvent&&(c=c.originalEvent),"pointermove"===c.type&&(null!==n.touchId||c.pointerId!==n.pointerId))return;if("touchmove"===c.type){if(!(t=[...c.changedTouches].find(e=>e.identifier===n.touchId))||t.identifier!==n.touchId)return}else t=c;if(!n.isTouched){n.startMoving&&n.isScrolling&&this.emit("touchMoveOpposite",c);return}let p=t.pageX,h=t.pageY;if(c.preventedByNestedSwiper){a.startX=p,a.startY=h;return}if(!this.allowTouchMove){c.target.matches(n.focusableElements)||(this.allowClick=!1),n.isTouched&&(Object.assign(a,{startX:p,startY:h,currentX:p,currentY:h}),n.touchStartTime=(0,l.d)());return}if(s.touchReleaseOnEdges&&!s.loop){if(this.isVertical()){if(h<a.startY&&this.translate<=this.maxTranslate()||h>a.startY&&this.translate>=this.minTranslate()){n.isTouched=!1,n.isMoved=!1;return}}else if(p<a.startX&&this.translate<=this.maxTranslate()||p>a.startX&&this.translate>=this.minTranslate())return}if(i.activeElement&&i.activeElement.matches(n.focusableElements)&&i.activeElement!==c.target&&"mouse"!==c.pointerType&&i.activeElement.blur(),i.activeElement&&c.target===i.activeElement&&c.target.matches(n.focusableElements)){n.isMoved=!0,this.allowClick=!1;return}n.allowTouchCallbacks&&this.emit("touchMove",c),a.previousX=a.currentX,a.previousY=a.currentY,a.currentX=p,a.currentY=h;let f=a.currentX-a.startX,m=a.currentY-a.startY;if(this.params.threshold&&Math.sqrt(f**2+m**2)<this.params.threshold)return;if(void 0===n.isScrolling){let e;this.isHorizontal()&&a.currentY===a.startY||this.isVertical()&&a.currentX===a.startX?n.isScrolling=!1:f*f+m*m>=25&&(e=180*Math.atan2(Math.abs(m),Math.abs(f))/Math.PI,n.isScrolling=this.isHorizontal()?e>s.touchAngle:90-e>s.touchAngle)}if(n.isScrolling&&this.emit("touchMoveOpposite",c),void 0===n.startMoving&&(a.currentX!==a.startX||a.currentY!==a.startY)&&(n.startMoving=!0),n.isScrolling||"touchmove"===c.type&&n.preventTouchMoveFromPointerMove){n.isTouched=!1;return}if(!n.startMoving)return;this.allowClick=!1,!s.cssMode&&c.cancelable&&c.preventDefault(),s.touchMoveStopPropagation&&!s.nested&&c.stopPropagation();let v=this.isHorizontal()?f:m,g=this.isHorizontal()?a.currentX-a.previousX:a.currentY-a.previousY;s.oneWayMovement&&(v=Math.abs(v)*(d?1:-1),g=Math.abs(g)*(d?1:-1)),a.diff=v,v*=s.touchRatio,d&&(v=-v,g=-g);let y=this.touchesDirection;this.swipeDirection=v>0?"prev":"next",this.touchesDirection=g>0?"prev":"next";let w=this.params.loop&&!s.cssMode,b="next"===this.touchesDirection&&this.allowSlideNext||"prev"===this.touchesDirection&&this.allowSlidePrev;if(!n.isMoved){if(w&&b&&this.loopFix({direction:this.swipeDirection}),n.startTranslate=this.getTranslate(),this.setTransition(0),this.animating){let e=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});this.wrapperEl.dispatchEvent(e)}n.allowMomentumBounce=!1,s.grabCursor&&(!0===this.allowSlideNext||!0===this.allowSlidePrev)&&this.setGrabCursor(!0),this.emit("sliderFirstMove",c)}if(new Date().getTime(),!1!==s._loopSwapReset&&n.isMoved&&n.allowThresholdMove&&y!==this.touchesDirection&&w&&b&&Math.abs(v)>=1){Object.assign(a,{startX:p,startY:h,currentX:p,currentY:h,startTranslate:n.currentTranslate}),n.loopSwapReset=!0,n.startTranslate=n.currentTranslate;return}this.emit("sliderMove",c),n.isMoved=!0,n.currentTranslate=v+n.startTranslate;let E=!0,T=s.resistanceRatio;if(s.touchReleaseOnEdges&&(T=0),v>0?(w&&b&&!r&&n.allowThresholdMove&&n.currentTranslate>(s.centeredSlides?this.minTranslate()-this.slidesSizesGrid[this.activeIndex+1]-("auto"!==s.slidesPerView&&this.slides.length-s.slidesPerView>=2?this.slidesSizesGrid[this.activeIndex+1]+this.params.spaceBetween:0)-this.params.spaceBetween:this.minTranslate())&&this.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),n.currentTranslate>this.minTranslate()&&(E=!1,s.resistance&&(n.currentTranslate=this.minTranslate()-1+(-this.minTranslate()+n.startTranslate+v)**T))):v<0&&(w&&b&&!r&&n.allowThresholdMove&&n.currentTranslate<(s.centeredSlides?this.maxTranslate()+this.slidesSizesGrid[this.slidesSizesGrid.length-1]+this.params.spaceBetween+("auto"!==s.slidesPerView&&this.slides.length-s.slidesPerView>=2?this.slidesSizesGrid[this.slidesSizesGrid.length-1]+this.params.spaceBetween:0):this.maxTranslate())&&this.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:this.slides.length-("auto"===s.slidesPerView?this.slidesPerViewDynamic():Math.ceil(parseFloat(s.slidesPerView,10)))}),n.currentTranslate<this.maxTranslate()&&(E=!1,s.resistance&&(n.currentTranslate=this.maxTranslate()+1-(this.maxTranslate()-n.startTranslate-v)**T))),E&&(c.preventedByNestedSwiper=!0),!this.allowSlideNext&&"next"===this.swipeDirection&&n.currentTranslate<n.startTranslate&&(n.currentTranslate=n.startTranslate),!this.allowSlidePrev&&"prev"===this.swipeDirection&&n.currentTranslate>n.startTranslate&&(n.currentTranslate=n.startTranslate),this.allowSlidePrev||this.allowSlideNext||(n.currentTranslate=n.startTranslate),s.threshold>0){if(Math.abs(v)>s.threshold||n.allowThresholdMove){if(!n.allowThresholdMove){n.allowThresholdMove=!0,a.startX=a.currentX,a.startY=a.currentY,n.currentTranslate=n.startTranslate,a.diff=this.isHorizontal()?a.currentX-a.startX:a.currentY-a.startY;return}}else{n.currentTranslate=n.startTranslate;return}}s.followFinger&&!s.cssMode&&((s.freeMode&&s.freeMode.enabled&&this.freeMode||s.watchSlidesProgress)&&(this.updateActiveIndex(),this.updateSlidesClasses()),s.freeMode&&s.freeMode.enabled&&this.freeMode&&this.freeMode.onTouchMove(),this.updateProgress(n.currentTranslate),this.setTranslate(n.currentTranslate))}function E(e){let t,r;let i=this,n=i.touchEventsData,s=e;if(s.originalEvent&&(s=s.originalEvent),"touchend"===s.type||"touchcancel"===s.type){if(!(t=[...s.changedTouches].find(e=>e.identifier===n.touchId))||t.identifier!==n.touchId)return}else{if(null!==n.touchId||s.pointerId!==n.pointerId)return;t=s}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(s.type)&&!(["pointercancel","contextmenu"].includes(s.type)&&(i.browser.isSafari||i.browser.isWebView)))return;n.pointerId=null,n.touchId=null;let{params:a,touches:o,rtlTranslate:d,slidesGrid:u,enabled:c}=i;if(!c||!a.simulateTouch&&"mouse"===s.pointerType)return;if(n.allowTouchCallbacks&&i.emit("touchEnd",s),n.allowTouchCallbacks=!1,!n.isTouched){n.isMoved&&a.grabCursor&&i.setGrabCursor(!1),n.isMoved=!1,n.startMoving=!1;return}a.grabCursor&&n.isMoved&&n.isTouched&&(!0===i.allowSlideNext||!0===i.allowSlidePrev)&&i.setGrabCursor(!1);let p=(0,l.d)(),h=p-n.touchStartTime;if(i.allowClick){let e=s.path||s.composedPath&&s.composedPath();i.updateClickedSlide(e&&e[0]||s.target,e),i.emit("tap click",s),h<300&&p-n.lastClickTime<300&&i.emit("doubleTap doubleClick",s)}if(n.lastClickTime=(0,l.d)(),(0,l.n)(()=>{i.destroyed||(i.allowClick=!0)}),!n.isTouched||!n.isMoved||!i.swipeDirection||0===o.diff&&!n.loopSwapReset||n.currentTranslate===n.startTranslate&&!n.loopSwapReset){n.isTouched=!1,n.isMoved=!1,n.startMoving=!1;return}if(n.isTouched=!1,n.isMoved=!1,n.startMoving=!1,r=a.followFinger?d?i.translate:-i.translate:-n.currentTranslate,a.cssMode)return;if(a.freeMode&&a.freeMode.enabled){i.freeMode.onTouchEnd({currentPos:r});return}let f=r>=-i.maxTranslate()&&!i.params.loop,m=0,v=i.slidesSizesGrid[0];for(let e=0;e<u.length;e+=e<a.slidesPerGroupSkip?1:a.slidesPerGroup){let t=e<a.slidesPerGroupSkip-1?1:a.slidesPerGroup;void 0!==u[e+t]?(f||r>=u[e]&&r<u[e+t])&&(m=e,v=u[e+t]-u[e]):(f||r>=u[e])&&(m=e,v=u[u.length-1]-u[u.length-2])}let g=null,y=null;a.rewind&&(i.isBeginning?y=a.virtual&&a.virtual.enabled&&i.virtual?i.virtual.slides.length-1:i.slides.length-1:i.isEnd&&(g=0));let w=(r-u[m])/v,b=m<a.slidesPerGroupSkip-1?1:a.slidesPerGroup;if(h>a.longSwipesMs){if(!a.longSwipes){i.slideTo(i.activeIndex);return}"next"===i.swipeDirection&&(w>=a.longSwipesRatio?i.slideTo(a.rewind&&i.isEnd?g:m+b):i.slideTo(m)),"prev"===i.swipeDirection&&(w>1-a.longSwipesRatio?i.slideTo(m+b):null!==y&&w<0&&Math.abs(w)>a.longSwipesRatio?i.slideTo(y):i.slideTo(m))}else{if(!a.shortSwipes){i.slideTo(i.activeIndex);return}i.navigation&&(s.target===i.navigation.nextEl||s.target===i.navigation.prevEl)?s.target===i.navigation.nextEl?i.slideTo(m+b):i.slideTo(m):("next"===i.swipeDirection&&i.slideTo(null!==g?g:m+b),"prev"===i.swipeDirection&&i.slideTo(null!==y?y:m))}}function T(){let e=this,{params:t,el:r}=e;if(r&&0===r.offsetWidth)return;t.breakpoints&&e.setBreakpoint();let{allowSlideNext:i,allowSlidePrev:n,snapGrid:s}=e,a=e.virtual&&e.params.virtual.enabled;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses();let o=a&&t.loop;"auto"!==t.slidesPerView&&!(t.slidesPerView>1)||!e.isEnd||e.isBeginning||e.params.centeredSlides||o?e.params.loop&&!a?e.slideToLoop(e.realIndex,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0):e.slideTo(e.slides.length-1,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&(clearTimeout(e.autoplay.resizeTimeout),e.autoplay.resizeTimeout=setTimeout(()=>{e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.resume()},500)),e.allowSlidePrev=n,e.allowSlideNext=i,e.params.watchOverflow&&s!==e.snapGrid&&e.checkOverflow()}function S(e){this.enabled&&!this.allowClick&&(this.params.preventClicks&&e.preventDefault(),this.params.preventClicksPropagation&&this.animating&&(e.stopPropagation(),e.stopImmediatePropagation()))}function x(){let{wrapperEl:e,rtlTranslate:t,enabled:r}=this;if(!r)return;this.previousTranslate=this.translate,this.isHorizontal()?this.translate=-e.scrollLeft:this.translate=-e.scrollTop,0===this.translate&&(this.translate=0),this.updateActiveIndex(),this.updateSlidesClasses();let i=this.maxTranslate()-this.minTranslate();(0===i?0:(this.translate-this.minTranslate())/i)!==this.progress&&this.updateProgress(t?-this.translate:this.translate),this.emit("setTranslate",this.translate,!1)}function k(e){f(this,e.target),!this.params.cssMode&&("auto"===this.params.slidesPerView||this.params.autoHeight)&&this.update()}function P(){!this.documentTouchHandlerProceeded&&(this.documentTouchHandlerProceeded=!0,this.params.touchReleaseOnEdges&&(this.el.style.touchAction="auto"))}let C=(e,t)=>{let r=(0,o.g)(),{params:i,el:n,wrapperEl:s,device:a}=e,l=!!i.nested,d="on"===t?"addEventListener":"removeEventListener";n&&"string"!=typeof n&&(r[d]("touchstart",e.onDocumentTouchStart,{passive:!1,capture:l}),n[d]("touchstart",e.onTouchStart,{passive:!1}),n[d]("pointerdown",e.onTouchStart,{passive:!1}),r[d]("touchmove",e.onTouchMove,{passive:!1,capture:l}),r[d]("pointermove",e.onTouchMove,{passive:!1,capture:l}),r[d]("touchend",e.onTouchEnd,{passive:!0}),r[d]("pointerup",e.onTouchEnd,{passive:!0}),r[d]("pointercancel",e.onTouchEnd,{passive:!0}),r[d]("touchcancel",e.onTouchEnd,{passive:!0}),r[d]("pointerout",e.onTouchEnd,{passive:!0}),r[d]("pointerleave",e.onTouchEnd,{passive:!0}),r[d]("contextmenu",e.onTouchEnd,{passive:!0}),(i.preventClicks||i.preventClicksPropagation)&&n[d]("click",e.onClick,!0),i.cssMode&&s[d]("scroll",e.onScroll),i.updateOnWindowResize?e[t](a.ios||a.android?"resize orientationchange observerUpdate":"resize observerUpdate",T,!0):e[t]("observerUpdate",T,!0),n[d]("load",e.onLoad,{capture:!0}))},M=(e,t)=>e.grid&&t.grid&&t.grid.rows>1;var _={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};let O={eventsEmitter:{on(e,t,r){let i=this;if(!i.eventsListeners||i.destroyed||"function"!=typeof t)return i;let n=r?"unshift":"push";return e.split(" ").forEach(e=>{i.eventsListeners[e]||(i.eventsListeners[e]=[]),i.eventsListeners[e][n](t)}),i},once(e,t,r){let i=this;if(!i.eventsListeners||i.destroyed||"function"!=typeof t)return i;function n(){i.off(e,n),n.__emitterProxy&&delete n.__emitterProxy;for(var r=arguments.length,s=Array(r),a=0;a<r;a++)s[a]=arguments[a];t.apply(i,s)}return n.__emitterProxy=t,i.on(e,n,r)},onAny(e,t){return!this.eventsListeners||this.destroyed||"function"!=typeof e||0>this.eventsAnyListeners.indexOf(e)&&this.eventsAnyListeners[t?"unshift":"push"](e),this},offAny(e){if(!this.eventsListeners||this.destroyed||!this.eventsAnyListeners)return this;let t=this.eventsAnyListeners.indexOf(e);return t>=0&&this.eventsAnyListeners.splice(t,1),this},off(e,t){let r=this;return r.eventsListeners&&!r.destroyed&&r.eventsListeners&&e.split(" ").forEach(e=>{void 0===t?r.eventsListeners[e]=[]:r.eventsListeners[e]&&r.eventsListeners[e].forEach((i,n)=>{(i===t||i.__emitterProxy&&i.__emitterProxy===t)&&r.eventsListeners[e].splice(n,1)})}),r},emit(){let e,t,r;let i=this;if(!i.eventsListeners||i.destroyed||!i.eventsListeners)return i;for(var n=arguments.length,s=Array(n),a=0;a<n;a++)s[a]=arguments[a];return"string"==typeof s[0]||Array.isArray(s[0])?(e=s[0],t=s.slice(1,s.length),r=i):(e=s[0].events,t=s[0].data,r=s[0].context||i),t.unshift(r),(Array.isArray(e)?e:e.split(" ")).forEach(e=>{i.eventsAnyListeners&&i.eventsAnyListeners.length&&i.eventsAnyListeners.forEach(i=>{i.apply(r,[e,...t])}),i.eventsListeners&&i.eventsListeners[e]&&i.eventsListeners[e].forEach(e=>{e.apply(r,t)})}),i}},update:{updateSize:function(){let e,t;let r=this.el;e=void 0!==this.params.width&&null!==this.params.width?this.params.width:r.clientWidth,t=void 0!==this.params.height&&null!==this.params.height?this.params.height:r.clientHeight,0===e&&this.isHorizontal()||0===t&&this.isVertical()||(e=e-parseInt((0,l.p)(r,"padding-left")||0,10)-parseInt((0,l.p)(r,"padding-right")||0,10),t=t-parseInt((0,l.p)(r,"padding-top")||0,10)-parseInt((0,l.p)(r,"padding-bottom")||0,10),Number.isNaN(e)&&(e=0),Number.isNaN(t)&&(t=0),Object.assign(this,{width:e,height:t,size:this.isHorizontal()?e:t}))},updateSlides:function(){let e;let t=this;function r(e,r){return parseFloat(e.getPropertyValue(t.getDirectionLabel(r))||0)}let i=t.params,{wrapperEl:n,slidesEl:s,size:a,rtlTranslate:o,wrongRTL:d}=t,u=t.virtual&&i.virtual.enabled,c=u?t.virtual.slides.length:t.slides.length,p=(0,l.e)(s,`.${t.params.slideClass}, swiper-slide`),h=u?t.virtual.slides.length:p.length,f=[],m=[],v=[],g=i.slidesOffsetBefore;"function"==typeof g&&(g=i.slidesOffsetBefore.call(t));let y=i.slidesOffsetAfter;"function"==typeof y&&(y=i.slidesOffsetAfter.call(t));let w=t.snapGrid.length,b=t.slidesGrid.length,E=i.spaceBetween,T=-g,S=0,x=0;if(void 0===a)return;"string"==typeof E&&E.indexOf("%")>=0?E=parseFloat(E.replace("%",""))/100*a:"string"==typeof E&&(E=parseFloat(E)),t.virtualSize=-E,p.forEach(e=>{o?e.style.marginLeft="":e.style.marginRight="",e.style.marginBottom="",e.style.marginTop=""}),i.centeredSlides&&i.cssMode&&((0,l.s)(n,"--swiper-centered-offset-before",""),(0,l.s)(n,"--swiper-centered-offset-after",""));let k=i.grid&&i.grid.rows>1&&t.grid;k?t.grid.initSlides(p):t.grid&&t.grid.unsetSlides();let P="auto"===i.slidesPerView&&i.breakpoints&&Object.keys(i.breakpoints).filter(e=>void 0!==i.breakpoints[e].slidesPerView).length>0;for(let n=0;n<h;n+=1){let s;if(e=0,p[n]&&(s=p[n]),k&&t.grid.updateSlide(n,s,p),!p[n]||"none"!==(0,l.p)(s,"display")){if("auto"===i.slidesPerView){P&&(p[n].style[t.getDirectionLabel("width")]="");let a=getComputedStyle(s),o=s.style.transform,d=s.style.webkitTransform;if(o&&(s.style.transform="none"),d&&(s.style.webkitTransform="none"),i.roundLengths)e=t.isHorizontal()?(0,l.f)(s,"width",!0):(0,l.f)(s,"height",!0);else{let t=r(a,"width"),i=r(a,"padding-left"),n=r(a,"padding-right"),o=r(a,"margin-left"),l=r(a,"margin-right"),d=a.getPropertyValue("box-sizing");if(d&&"border-box"===d)e=t+o+l;else{let{clientWidth:r,offsetWidth:a}=s;e=t+i+n+o+l+(a-r)}}o&&(s.style.transform=o),d&&(s.style.webkitTransform=d),i.roundLengths&&(e=Math.floor(e))}else e=(a-(i.slidesPerView-1)*E)/i.slidesPerView,i.roundLengths&&(e=Math.floor(e)),p[n]&&(p[n].style[t.getDirectionLabel("width")]=`${e}px`);p[n]&&(p[n].swiperSlideSize=e),v.push(e),i.centeredSlides?(T=T+e/2+S/2+E,0===S&&0!==n&&(T=T-a/2-E),0===n&&(T=T-a/2-E),.001>Math.abs(T)&&(T=0),i.roundLengths&&(T=Math.floor(T)),x%i.slidesPerGroup==0&&f.push(T),m.push(T)):(i.roundLengths&&(T=Math.floor(T)),(x-Math.min(t.params.slidesPerGroupSkip,x))%t.params.slidesPerGroup==0&&f.push(T),m.push(T),T=T+e+E),t.virtualSize+=e+E,S=e,x+=1}}if(t.virtualSize=Math.max(t.virtualSize,a)+y,o&&d&&("slide"===i.effect||"coverflow"===i.effect)&&(n.style.width=`${t.virtualSize+E}px`),i.setWrapperSize&&(n.style[t.getDirectionLabel("width")]=`${t.virtualSize+E}px`),k&&t.grid.updateWrapperSize(e,f),!i.centeredSlides){let e=[];for(let r=0;r<f.length;r+=1){let n=f[r];i.roundLengths&&(n=Math.floor(n)),f[r]<=t.virtualSize-a&&e.push(n)}f=e,Math.floor(t.virtualSize-a)-Math.floor(f[f.length-1])>1&&f.push(t.virtualSize-a)}if(u&&i.loop){let e=v[0]+E;if(i.slidesPerGroup>1){let r=Math.ceil((t.virtual.slidesBefore+t.virtual.slidesAfter)/i.slidesPerGroup),n=e*i.slidesPerGroup;for(let e=0;e<r;e+=1)f.push(f[f.length-1]+n)}for(let r=0;r<t.virtual.slidesBefore+t.virtual.slidesAfter;r+=1)1===i.slidesPerGroup&&f.push(f[f.length-1]+e),m.push(m[m.length-1]+e),t.virtualSize+=e}if(0===f.length&&(f=[0]),0!==E){let e=t.isHorizontal()&&o?"marginLeft":t.getDirectionLabel("marginRight");p.filter((e,t)=>!i.cssMode||!!i.loop||t!==p.length-1).forEach(t=>{t.style[e]=`${E}px`})}if(i.centeredSlides&&i.centeredSlidesBounds){let e=0;v.forEach(t=>{e+=t+(E||0)});let t=(e-=E)>a?e-a:0;f=f.map(e=>e<=0?-g:e>t?t+y:e)}if(i.centerInsufficientSlides){let e=0;v.forEach(t=>{e+=t+(E||0)}),e-=E;let t=(i.slidesOffsetBefore||0)+(i.slidesOffsetAfter||0);if(e+t<a){let r=(a-e-t)/2;f.forEach((e,t)=>{f[t]=e-r}),m.forEach((e,t)=>{m[t]=e+r})}}if(Object.assign(t,{slides:p,snapGrid:f,slidesGrid:m,slidesSizesGrid:v}),i.centeredSlides&&i.cssMode&&!i.centeredSlidesBounds){(0,l.s)(n,"--swiper-centered-offset-before",`${-f[0]}px`),(0,l.s)(n,"--swiper-centered-offset-after",`${t.size/2-v[v.length-1]/2}px`);let e=-t.snapGrid[0],r=-t.slidesGrid[0];t.snapGrid=t.snapGrid.map(t=>t+e),t.slidesGrid=t.slidesGrid.map(e=>e+r)}if(h!==c&&t.emit("slidesLengthChange"),f.length!==w&&(t.params.watchOverflow&&t.checkOverflow(),t.emit("snapGridLengthChange")),m.length!==b&&t.emit("slidesGridLengthChange"),i.watchSlidesProgress&&t.updateSlidesOffset(),t.emit("slidesUpdated"),!u&&!i.cssMode&&("slide"===i.effect||"fade"===i.effect)){let e=`${i.containerModifierClass}backface-hidden`,r=t.el.classList.contains(e);h<=i.maxBackfaceHiddenSlides?r||t.el.classList.add(e):r&&t.el.classList.remove(e)}},updateAutoHeight:function(e){let t;let r=this,i=[],n=r.virtual&&r.params.virtual.enabled,s=0;"number"==typeof e?r.setTransition(e):!0===e&&r.setTransition(r.params.speed);let a=e=>n?r.slides[r.getSlideIndexByData(e)]:r.slides[e];if("auto"!==r.params.slidesPerView&&r.params.slidesPerView>1){if(r.params.centeredSlides)(r.visibleSlides||[]).forEach(e=>{i.push(e)});else for(t=0;t<Math.ceil(r.params.slidesPerView);t+=1){let e=r.activeIndex+t;if(e>r.slides.length&&!n)break;i.push(a(e))}}else i.push(a(r.activeIndex));for(t=0;t<i.length;t+=1)if(void 0!==i[t]){let e=i[t].offsetHeight;s=e>s?e:s}(s||0===s)&&(r.wrapperEl.style.height=`${s}px`)},updateSlidesOffset:function(){let e=this.slides,t=this.isElement?this.isHorizontal()?this.wrapperEl.offsetLeft:this.wrapperEl.offsetTop:0;for(let r=0;r<e.length;r+=1)e[r].swiperSlideOffset=(this.isHorizontal()?e[r].offsetLeft:e[r].offsetTop)-t-this.cssOverflowAdjustment()},updateSlidesProgress:function(e){void 0===e&&(e=this&&this.translate||0);let t=this.params,{slides:r,rtlTranslate:i,snapGrid:n}=this;if(0===r.length)return;void 0===r[0].swiperSlideOffset&&this.updateSlidesOffset();let s=-e;i&&(s=e),this.visibleSlidesIndexes=[],this.visibleSlides=[];let a=t.spaceBetween;"string"==typeof a&&a.indexOf("%")>=0?a=parseFloat(a.replace("%",""))/100*this.size:"string"==typeof a&&(a=parseFloat(a));for(let e=0;e<r.length;e+=1){let o=r[e],l=o.swiperSlideOffset;t.cssMode&&t.centeredSlides&&(l-=r[0].swiperSlideOffset);let d=(s+(t.centeredSlides?this.minTranslate():0)-l)/(o.swiperSlideSize+a),u=(s-n[0]+(t.centeredSlides?this.minTranslate():0)-l)/(o.swiperSlideSize+a),c=-(s-l),h=c+this.slidesSizesGrid[e],f=c>=0&&c<=this.size-this.slidesSizesGrid[e],m=c>=0&&c<this.size-1||h>1&&h<=this.size||c<=0&&h>=this.size;m&&(this.visibleSlides.push(o),this.visibleSlidesIndexes.push(e)),p(o,m,t.slideVisibleClass),p(o,f,t.slideFullyVisibleClass),o.progress=i?-d:d,o.originalProgress=i?-u:u}},updateProgress:function(e){if(void 0===e){let t=this.rtlTranslate?-1:1;e=this&&this.translate&&this.translate*t||0}let t=this.params,r=this.maxTranslate()-this.minTranslate(),{progress:i,isBeginning:n,isEnd:s,progressLoop:a}=this,o=n,l=s;if(0===r)i=0,n=!0,s=!0;else{i=(e-this.minTranslate())/r;let t=1>Math.abs(e-this.minTranslate()),a=1>Math.abs(e-this.maxTranslate());n=t||i<=0,s=a||i>=1,t&&(i=0),a&&(i=1)}if(t.loop){let t=this.getSlideIndexByData(0),r=this.getSlideIndexByData(this.slides.length-1),i=this.slidesGrid[t],n=this.slidesGrid[r],s=this.slidesGrid[this.slidesGrid.length-1],o=Math.abs(e);(a=o>=i?(o-i)/s:(o+s-n)/s)>1&&(a-=1)}Object.assign(this,{progress:i,progressLoop:a,isBeginning:n,isEnd:s}),(t.watchSlidesProgress||t.centeredSlides&&t.autoHeight)&&this.updateSlidesProgress(e),n&&!o&&this.emit("reachBeginning toEdge"),s&&!l&&this.emit("reachEnd toEdge"),(o&&!n||l&&!s)&&this.emit("fromEdge"),this.emit("progress",i)},updateSlidesClasses:function(){let e,t,r;let{slides:i,params:n,slidesEl:s,activeIndex:a}=this,o=this.virtual&&n.virtual.enabled,d=this.grid&&n.grid&&n.grid.rows>1,u=e=>(0,l.e)(s,`.${n.slideClass}${e}, swiper-slide${e}`)[0];if(o){if(n.loop){let t=a-this.virtual.slidesBefore;t<0&&(t=this.virtual.slides.length+t),t>=this.virtual.slides.length&&(t-=this.virtual.slides.length),e=u(`[data-swiper-slide-index="${t}"]`)}else e=u(`[data-swiper-slide-index="${a}"]`)}else d?(e=i.find(e=>e.column===a),r=i.find(e=>e.column===a+1),t=i.find(e=>e.column===a-1)):e=i[a];e&&!d&&(r=(0,l.q)(e,`.${n.slideClass}, swiper-slide`)[0],n.loop&&!r&&(r=i[0]),t=(0,l.r)(e,`.${n.slideClass}, swiper-slide`)[0],n.loop),i.forEach(i=>{h(i,i===e,n.slideActiveClass),h(i,i===r,n.slideNextClass),h(i,i===t,n.slidePrevClass)}),this.emitSlidesClasses()},updateActiveIndex:function(e){let t,r;let i=this,n=i.rtlTranslate?i.translate:-i.translate,{snapGrid:s,params:a,activeIndex:o,realIndex:l,snapIndex:d}=i,u=e,c=e=>{let t=e-i.virtual.slidesBefore;return t<0&&(t=i.virtual.slides.length+t),t>=i.virtual.slides.length&&(t-=i.virtual.slides.length),t};if(void 0===u&&(u=function(e){let t;let{slidesGrid:r,params:i}=e,n=e.rtlTranslate?e.translate:-e.translate;for(let e=0;e<r.length;e+=1)void 0!==r[e+1]?n>=r[e]&&n<r[e+1]-(r[e+1]-r[e])/2?t=e:n>=r[e]&&n<r[e+1]&&(t=e+1):n>=r[e]&&(t=e);return i.normalizeSlideIndex&&(t<0||void 0===t)&&(t=0),t}(i)),s.indexOf(n)>=0)t=s.indexOf(n);else{let e=Math.min(a.slidesPerGroupSkip,u);t=e+Math.floor((u-e)/a.slidesPerGroup)}if(t>=s.length&&(t=s.length-1),u===o&&!i.params.loop){t!==d&&(i.snapIndex=t,i.emit("snapIndexChange"));return}if(u===o&&i.params.loop&&i.virtual&&i.params.virtual.enabled){i.realIndex=c(u);return}let p=i.grid&&a.grid&&a.grid.rows>1;if(i.virtual&&a.virtual.enabled&&a.loop)r=c(u);else if(p){let e=i.slides.find(e=>e.column===u),t=parseInt(e.getAttribute("data-swiper-slide-index"),10);Number.isNaN(t)&&(t=Math.max(i.slides.indexOf(e),0)),r=Math.floor(t/a.grid.rows)}else if(i.slides[u]){let e=i.slides[u].getAttribute("data-swiper-slide-index");r=e?parseInt(e,10):u}else r=u;Object.assign(i,{previousSnapIndex:d,snapIndex:t,previousRealIndex:l,realIndex:r,previousIndex:o,activeIndex:u}),i.initialized&&v(i),i.emit("activeIndexChange"),i.emit("snapIndexChange"),(i.initialized||i.params.runCallbacksOnInit)&&(l!==r&&i.emit("realIndexChange"),i.emit("slideChange"))},updateClickedSlide:function(e,t){let r;let i=this.params,n=e.closest(`.${i.slideClass}, swiper-slide`);!n&&this.isElement&&t&&t.length>1&&t.includes(e)&&[...t.slice(t.indexOf(e)+1,t.length)].forEach(e=>{!n&&e.matches&&e.matches(`.${i.slideClass}, swiper-slide`)&&(n=e)});let s=!1;if(n){for(let e=0;e<this.slides.length;e+=1)if(this.slides[e]===n){s=!0,r=e;break}}if(n&&s)this.clickedSlide=n,this.virtual&&this.params.virtual.enabled?this.clickedIndex=parseInt(n.getAttribute("data-swiper-slide-index"),10):this.clickedIndex=r;else{this.clickedSlide=void 0,this.clickedIndex=void 0;return}i.slideToClickedSlide&&void 0!==this.clickedIndex&&this.clickedIndex!==this.activeIndex&&this.slideToClickedSlide()}},translate:{getTranslate:function(e){void 0===e&&(e=this.isHorizontal()?"x":"y");let{params:t,rtlTranslate:r,translate:i,wrapperEl:n}=this;if(t.virtualTranslate)return r?-i:i;if(t.cssMode)return i;let s=(0,l.j)(n,e);return s+=this.cssOverflowAdjustment(),r&&(s=-s),s||0},setTranslate:function(e,t){let{rtlTranslate:r,params:i,wrapperEl:n,progress:s}=this,a=0,o=0;this.isHorizontal()?a=r?-e:e:o=e,i.roundLengths&&(a=Math.floor(a),o=Math.floor(o)),this.previousTranslate=this.translate,this.translate=this.isHorizontal()?a:o,i.cssMode?n[this.isHorizontal()?"scrollLeft":"scrollTop"]=this.isHorizontal()?-a:-o:i.virtualTranslate||(this.isHorizontal()?a-=this.cssOverflowAdjustment():o-=this.cssOverflowAdjustment(),n.style.transform=`translate3d(${a}px, ${o}px, 0px)`);let l=this.maxTranslate()-this.minTranslate();(0===l?0:(e-this.minTranslate())/l)!==s&&this.updateProgress(e),this.emit("setTranslate",this.translate,t)},minTranslate:function(){return-this.snapGrid[0]},maxTranslate:function(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function(e,t,r,i,n){let s;void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===r&&(r=!0),void 0===i&&(i=!0);let a=this,{params:o,wrapperEl:d}=a;if(a.animating&&o.preventInteractionOnTransition)return!1;let u=a.minTranslate(),c=a.maxTranslate();if(s=i&&e>u?u:i&&e<c?c:e,a.updateProgress(s),o.cssMode){let e=a.isHorizontal();if(0===t)d[e?"scrollLeft":"scrollTop"]=-s;else{if(!a.support.smoothScroll)return(0,l.t)({swiper:a,targetPosition:-s,side:e?"left":"top"}),!0;d.scrollTo({[e?"left":"top"]:-s,behavior:"smooth"})}return!0}return 0===t?(a.setTransition(0),a.setTranslate(s),r&&(a.emit("beforeTransitionStart",t,n),a.emit("transitionEnd"))):(a.setTransition(t),a.setTranslate(s),r&&(a.emit("beforeTransitionStart",t,n),a.emit("transitionStart")),a.animating||(a.animating=!0,a.onTranslateToWrapperTransitionEnd||(a.onTranslateToWrapperTransitionEnd=function(e){a&&!a.destroyed&&e.target===this&&(a.wrapperEl.removeEventListener("transitionend",a.onTranslateToWrapperTransitionEnd),a.onTranslateToWrapperTransitionEnd=null,delete a.onTranslateToWrapperTransitionEnd,a.animating=!1,r&&a.emit("transitionEnd"))}),a.wrapperEl.addEventListener("transitionend",a.onTranslateToWrapperTransitionEnd))),!0}},transition:{setTransition:function(e,t){this.params.cssMode||(this.wrapperEl.style.transitionDuration=`${e}ms`,this.wrapperEl.style.transitionDelay=0===e?"0ms":""),this.emit("setTransition",e,t)},transitionStart:function(e,t){void 0===e&&(e=!0);let{params:r}=this;r.cssMode||(r.autoHeight&&this.updateAutoHeight(),g({swiper:this,runCallbacks:e,direction:t,step:"Start"}))},transitionEnd:function(e,t){void 0===e&&(e=!0);let{params:r}=this;this.animating=!1,r.cssMode||(this.setTransition(0),g({swiper:this,runCallbacks:e,direction:t,step:"End"}))}},slide:{slideTo:function(e,t,r,i,n){let s;void 0===e&&(e=0),void 0===r&&(r=!0),"string"==typeof e&&(e=parseInt(e,10));let a=this,o=e;o<0&&(o=0);let{params:d,snapGrid:u,slidesGrid:p,previousIndex:h,activeIndex:f,rtlTranslate:m,wrapperEl:v,enabled:g}=a;if(!g&&!i&&!n||a.destroyed||a.animating&&d.preventInteractionOnTransition)return!1;void 0===t&&(t=a.params.speed);let y=Math.min(a.params.slidesPerGroupSkip,o),w=y+Math.floor((o-y)/a.params.slidesPerGroup);w>=u.length&&(w=u.length-1);let b=-u[w];if(d.normalizeSlideIndex)for(let e=0;e<p.length;e+=1){let t=-Math.floor(100*b),r=Math.floor(100*p[e]),i=Math.floor(100*p[e+1]);void 0!==p[e+1]?t>=r&&t<i-(i-r)/2?o=e:t>=r&&t<i&&(o=e+1):t>=r&&(o=e)}if(a.initialized&&o!==f&&(!a.allowSlideNext&&(m?b>a.translate&&b>a.minTranslate():b<a.translate&&b<a.minTranslate())||!a.allowSlidePrev&&b>a.translate&&b>a.maxTranslate()&&(f||0)!==o))return!1;o!==(h||0)&&r&&a.emit("beforeSlideChangeStart"),a.updateProgress(b),s=o>f?"next":o<f?"prev":"reset";let E=a.virtual&&a.params.virtual.enabled;if(!(E&&n)&&(m&&-b===a.translate||!m&&b===a.translate))return a.updateActiveIndex(o),d.autoHeight&&a.updateAutoHeight(),a.updateSlidesClasses(),"slide"!==d.effect&&a.setTranslate(b),"reset"!==s&&(a.transitionStart(r,s),a.transitionEnd(r,s)),!1;if(d.cssMode){let e=a.isHorizontal(),r=m?b:-b;if(0===t)E&&(a.wrapperEl.style.scrollSnapType="none",a._immediateVirtual=!0),E&&!a._cssModeVirtualInitialSet&&a.params.initialSlide>0?(a._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{v[e?"scrollLeft":"scrollTop"]=r})):v[e?"scrollLeft":"scrollTop"]=r,E&&requestAnimationFrame(()=>{a.wrapperEl.style.scrollSnapType="",a._immediateVirtual=!1});else{if(!a.support.smoothScroll)return(0,l.t)({swiper:a,targetPosition:r,side:e?"left":"top"}),!0;v.scrollTo({[e?"left":"top"]:r,behavior:"smooth"})}return!0}let T=c().isSafari;return E&&!n&&T&&a.isElement&&a.virtual.update(!1,!1,o),a.setTransition(t),a.setTranslate(b),a.updateActiveIndex(o),a.updateSlidesClasses(),a.emit("beforeTransitionStart",t,i),a.transitionStart(r,s),0===t?a.transitionEnd(r,s):a.animating||(a.animating=!0,a.onSlideToWrapperTransitionEnd||(a.onSlideToWrapperTransitionEnd=function(e){a&&!a.destroyed&&e.target===this&&(a.wrapperEl.removeEventListener("transitionend",a.onSlideToWrapperTransitionEnd),a.onSlideToWrapperTransitionEnd=null,delete a.onSlideToWrapperTransitionEnd,a.transitionEnd(r,s))}),a.wrapperEl.addEventListener("transitionend",a.onSlideToWrapperTransitionEnd)),!0},slideToLoop:function(e,t,r,i){void 0===e&&(e=0),void 0===r&&(r=!0),"string"==typeof e&&(e=parseInt(e,10));let n=this;if(n.destroyed)return;void 0===t&&(t=n.params.speed);let s=n.grid&&n.params.grid&&n.params.grid.rows>1,a=e;if(n.params.loop){if(n.virtual&&n.params.virtual.enabled)a+=n.virtual.slidesBefore;else{let e;if(s){let t=a*n.params.grid.rows;e=n.slides.find(e=>1*e.getAttribute("data-swiper-slide-index")===t).column}else e=n.getSlideIndexByData(a);let t=s?Math.ceil(n.slides.length/n.params.grid.rows):n.slides.length,{centeredSlides:r}=n.params,o=n.params.slidesPerView;"auto"===o?o=n.slidesPerViewDynamic():(o=Math.ceil(parseFloat(n.params.slidesPerView,10)),r&&o%2==0&&(o+=1));let l=t-e<o;if(r&&(l=l||e<Math.ceil(o/2)),i&&r&&"auto"!==n.params.slidesPerView&&!s&&(l=!1),l){let i=r?e<n.activeIndex?"prev":"next":e-n.activeIndex-1<n.params.slidesPerView?"next":"prev";n.loopFix({direction:i,slideTo:!0,activeSlideIndex:"next"===i?e+1:e-t+1,slideRealIndex:"next"===i?n.realIndex:void 0})}if(s){let e=a*n.params.grid.rows;a=n.slides.find(t=>1*t.getAttribute("data-swiper-slide-index")===e).column}else a=n.getSlideIndexByData(a)}}return requestAnimationFrame(()=>{n.slideTo(a,t,r,i)}),n},slideNext:function(e,t,r){void 0===t&&(t=!0);let i=this,{enabled:n,params:s,animating:a}=i;if(!n||i.destroyed)return i;void 0===e&&(e=i.params.speed);let o=s.slidesPerGroup;"auto"===s.slidesPerView&&1===s.slidesPerGroup&&s.slidesPerGroupAuto&&(o=Math.max(i.slidesPerViewDynamic("current",!0),1));let l=i.activeIndex<s.slidesPerGroupSkip?1:o,d=i.virtual&&s.virtual.enabled;if(s.loop){if(a&&!d&&s.loopPreventsSliding)return!1;if(i.loopFix({direction:"next"}),i._clientLeft=i.wrapperEl.clientLeft,i.activeIndex===i.slides.length-1&&s.cssMode)return requestAnimationFrame(()=>{i.slideTo(i.activeIndex+l,e,t,r)}),!0}return s.rewind&&i.isEnd?i.slideTo(0,e,t,r):i.slideTo(i.activeIndex+l,e,t,r)},slidePrev:function(e,t,r){void 0===t&&(t=!0);let i=this,{params:n,snapGrid:s,slidesGrid:a,rtlTranslate:o,enabled:l,animating:d}=i;if(!l||i.destroyed)return i;void 0===e&&(e=i.params.speed);let u=i.virtual&&n.virtual.enabled;if(n.loop){if(d&&!u&&n.loopPreventsSliding)return!1;i.loopFix({direction:"prev"}),i._clientLeft=i.wrapperEl.clientLeft}function c(e){return e<0?-Math.floor(Math.abs(e)):Math.floor(e)}let p=c(o?i.translate:-i.translate),h=s.map(e=>c(e)),f=n.freeMode&&n.freeMode.enabled,m=s[h.indexOf(p)-1];if(void 0===m&&(n.cssMode||f)){let e;s.forEach((t,r)=>{p>=t&&(e=r)}),void 0!==e&&(m=f?s[e]:s[e>0?e-1:e])}let v=0;if(void 0!==m&&((v=a.indexOf(m))<0&&(v=i.activeIndex-1),"auto"===n.slidesPerView&&1===n.slidesPerGroup&&n.slidesPerGroupAuto&&(v=Math.max(v=v-i.slidesPerViewDynamic("previous",!0)+1,0))),n.rewind&&i.isBeginning){let n=i.params.virtual&&i.params.virtual.enabled&&i.virtual?i.virtual.slides.length-1:i.slides.length-1;return i.slideTo(n,e,t,r)}return n.loop&&0===i.activeIndex&&n.cssMode?(requestAnimationFrame(()=>{i.slideTo(v,e,t,r)}),!0):i.slideTo(v,e,t,r)},slideReset:function(e,t,r){if(void 0===t&&(t=!0),!this.destroyed)return void 0===e&&(e=this.params.speed),this.slideTo(this.activeIndex,e,t,r)},slideToClosest:function(e,t,r,i){if(void 0===t&&(t=!0),void 0===i&&(i=.5),this.destroyed)return;void 0===e&&(e=this.params.speed);let n=this.activeIndex,s=Math.min(this.params.slidesPerGroupSkip,n),a=s+Math.floor((n-s)/this.params.slidesPerGroup),o=this.rtlTranslate?this.translate:-this.translate;if(o>=this.snapGrid[a]){let e=this.snapGrid[a];o-e>(this.snapGrid[a+1]-e)*i&&(n+=this.params.slidesPerGroup)}else{let e=this.snapGrid[a-1];o-e<=(this.snapGrid[a]-e)*i&&(n-=this.params.slidesPerGroup)}return n=Math.min(n=Math.max(n,0),this.slidesGrid.length-1),this.slideTo(n,e,t,r)},slideToClickedSlide:function(){let e;let t=this;if(t.destroyed)return;let{params:r,slidesEl:i}=t,n="auto"===r.slidesPerView?t.slidesPerViewDynamic():r.slidesPerView,s=t.clickedIndex,a=t.isElement?"swiper-slide":`.${r.slideClass}`;if(r.loop){if(t.animating)return;e=parseInt(t.clickedSlide.getAttribute("data-swiper-slide-index"),10),r.centeredSlides?s<t.loopedSlides-n/2||s>t.slides.length-t.loopedSlides+n/2?(t.loopFix(),s=t.getSlideIndex((0,l.e)(i,`${a}[data-swiper-slide-index="${e}"]`)[0]),(0,l.n)(()=>{t.slideTo(s)})):t.slideTo(s):s>t.slides.length-n?(t.loopFix(),s=t.getSlideIndex((0,l.e)(i,`${a}[data-swiper-slide-index="${e}"]`)[0]),(0,l.n)(()=>{t.slideTo(s)})):t.slideTo(s)}else t.slideTo(s)}},loop:{loopCreate:function(e){let t=this,{params:r,slidesEl:i}=t;if(!r.loop||t.virtual&&t.params.virtual.enabled)return;let n=t.grid&&r.grid&&r.grid.rows>1,s=r.slidesPerGroup*(n?r.grid.rows:1),a=t.slides.length%s!=0,o=n&&t.slides.length%r.grid.rows!=0,d=e=>{for(let i=0;i<e;i+=1){let e=t.isElement?(0,l.c)("swiper-slide",[r.slideBlankClass]):(0,l.c)("div",[r.slideClass,r.slideBlankClass]);t.slidesEl.append(e)}};a?r.loopAddBlankSlides?(d(s-t.slides.length%s),t.recalcSlides(),t.updateSlides()):(0,l.u)("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)"):o&&(r.loopAddBlankSlides?(d(r.grid.rows-t.slides.length%r.grid.rows),t.recalcSlides(),t.updateSlides()):(0,l.u)("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)")),(0,l.e)(i,`.${r.slideClass}, swiper-slide`).forEach((e,t)=>{e.setAttribute("data-swiper-slide-index",t)}),t.loopFix({slideRealIndex:e,direction:r.centeredSlides?void 0:"next"})},loopFix:function(e){let{slideRealIndex:t,slideTo:r=!0,direction:i,setTranslate:n,activeSlideIndex:s,byController:a,byMousewheel:o}=void 0===e?{}:e,d=this;if(!d.params.loop)return;d.emit("beforeLoopFix");let{slides:u,allowSlidePrev:c,allowSlideNext:p,slidesEl:h,params:f}=d,{centeredSlides:m}=f;if(d.allowSlidePrev=!0,d.allowSlideNext=!0,d.virtual&&f.virtual.enabled){r&&(f.centeredSlides||0!==d.snapIndex?f.centeredSlides&&d.snapIndex<f.slidesPerView?d.slideTo(d.virtual.slides.length+d.snapIndex,0,!1,!0):d.snapIndex===d.snapGrid.length-1&&d.slideTo(d.virtual.slidesBefore,0,!1,!0):d.slideTo(d.virtual.slides.length,0,!1,!0)),d.allowSlidePrev=c,d.allowSlideNext=p,d.emit("loopFix");return}let v=f.slidesPerView;"auto"===v?v=d.slidesPerViewDynamic():(v=Math.ceil(parseFloat(f.slidesPerView,10)),m&&v%2==0&&(v+=1));let g=f.slidesPerGroupAuto?v:f.slidesPerGroup,y=g;y%g!=0&&(y+=g-y%g),y+=f.loopAdditionalSlides,d.loopedSlides=y;let w=d.grid&&f.grid&&f.grid.rows>1;u.length<v+y?(0,l.u)("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled and not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):w&&"row"===f.grid.fill&&(0,l.u)("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");let b=[],E=[],T=d.activeIndex;void 0===s?s=d.getSlideIndex(u.find(e=>e.classList.contains(f.slideActiveClass))):T=s;let S="next"===i||!i,x="prev"===i||!i,k=0,P=0,C=w?Math.ceil(u.length/f.grid.rows):u.length,M=(w?u[s].column:s)+(m&&void 0===n?-v/2+.5:0);if(M<y){k=Math.max(y-M,g);for(let e=0;e<y-M;e+=1){let t=e-Math.floor(e/C)*C;if(w){let e=C-t-1;for(let t=u.length-1;t>=0;t-=1)u[t].column===e&&b.push(t)}else b.push(C-t-1)}}else if(M+v>C-y){P=Math.max(M-(C-2*y),g);for(let e=0;e<P;e+=1){let t=e-Math.floor(e/C)*C;w?u.forEach((e,r)=>{e.column===t&&E.push(r)}):E.push(t)}}if(d.__preventObserver__=!0,requestAnimationFrame(()=>{d.__preventObserver__=!1}),x&&b.forEach(e=>{u[e].swiperLoopMoveDOM=!0,h.prepend(u[e]),u[e].swiperLoopMoveDOM=!1}),S&&E.forEach(e=>{u[e].swiperLoopMoveDOM=!0,h.append(u[e]),u[e].swiperLoopMoveDOM=!1}),d.recalcSlides(),"auto"===f.slidesPerView?d.updateSlides():w&&(b.length>0&&x||E.length>0&&S)&&d.slides.forEach((e,t)=>{d.grid.updateSlide(t,e,d.slides)}),f.watchSlidesProgress&&d.updateSlidesOffset(),r){if(b.length>0&&x){if(void 0===t){let e=d.slidesGrid[T],t=d.slidesGrid[T+k]-e;o?d.setTranslate(d.translate-t):(d.slideTo(T+Math.ceil(k),0,!1,!0),n&&(d.touchEventsData.startTranslate=d.touchEventsData.startTranslate-t,d.touchEventsData.currentTranslate=d.touchEventsData.currentTranslate-t))}else if(n){let e=w?b.length/f.grid.rows:b.length;d.slideTo(d.activeIndex+e,0,!1,!0),d.touchEventsData.currentTranslate=d.translate}}else if(E.length>0&&S){if(void 0===t){let e=d.slidesGrid[T],t=d.slidesGrid[T-P]-e;o?d.setTranslate(d.translate-t):(d.slideTo(T-P,0,!1,!0),n&&(d.touchEventsData.startTranslate=d.touchEventsData.startTranslate-t,d.touchEventsData.currentTranslate=d.touchEventsData.currentTranslate-t))}else{let e=w?E.length/f.grid.rows:E.length;d.slideTo(d.activeIndex-e,0,!1,!0)}}}if(d.allowSlidePrev=c,d.allowSlideNext=p,d.controller&&d.controller.control&&!a){let e={slideRealIndex:t,direction:i,setTranslate:n,activeSlideIndex:s,byController:!0};Array.isArray(d.controller.control)?d.controller.control.forEach(t=>{!t.destroyed&&t.params.loop&&t.loopFix({...e,slideTo:t.params.slidesPerView===f.slidesPerView&&r})}):d.controller.control instanceof d.constructor&&d.controller.control.params.loop&&d.controller.control.loopFix({...e,slideTo:d.controller.control.params.slidesPerView===f.slidesPerView&&r})}d.emit("loopFix")},loopDestroy:function(){let{params:e,slidesEl:t}=this;if(!e.loop||!t||this.virtual&&this.params.virtual.enabled)return;this.recalcSlides();let r=[];this.slides.forEach(e=>{r[void 0===e.swiperSlideIndex?1*e.getAttribute("data-swiper-slide-index"):e.swiperSlideIndex]=e}),this.slides.forEach(e=>{e.removeAttribute("data-swiper-slide-index")}),r.forEach(e=>{t.append(e)}),this.recalcSlides(),this.slideTo(this.realIndex,0)}},grabCursor:{setGrabCursor:function(e){let t=this;if(!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode)return;let r="container"===t.params.touchEventsTarget?t.el:t.wrapperEl;t.isElement&&(t.__preventObserver__=!0),r.style.cursor="move",r.style.cursor=e?"grabbing":"grab",t.isElement&&requestAnimationFrame(()=>{t.__preventObserver__=!1})},unsetGrabCursor:function(){let e=this;e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e.isElement&&(e.__preventObserver__=!0),e["container"===e.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1}))}},events:{attachEvents:function(){let{params:e}=this;this.onTouchStart=w.bind(this),this.onTouchMove=b.bind(this),this.onTouchEnd=E.bind(this),this.onDocumentTouchStart=P.bind(this),e.cssMode&&(this.onScroll=x.bind(this)),this.onClick=S.bind(this),this.onLoad=k.bind(this),C(this,"on")},detachEvents:function(){C(this,"off")}},breakpoints:{setBreakpoint:function(){let e=this,{realIndex:t,initialized:r,params:i,el:n}=e,s=i.breakpoints;if(!s||s&&0===Object.keys(s).length)return;let a=(0,o.g)(),d="window"!==i.breakpointsBase&&i.breakpointsBase?"container":i.breakpointsBase,u=["window","container"].includes(i.breakpointsBase)||!i.breakpointsBase?e.el:a.querySelector(i.breakpointsBase),c=e.getBreakpoint(s,d,u);if(!c||e.currentBreakpoint===c)return;let p=(c in s?s[c]:void 0)||e.originalParams,h=M(e,i),f=M(e,p),m=e.params.grabCursor,v=p.grabCursor,g=i.enabled;h&&!f?(n.classList.remove(`${i.containerModifierClass}grid`,`${i.containerModifierClass}grid-column`),e.emitContainerClasses()):!h&&f&&(n.classList.add(`${i.containerModifierClass}grid`),(p.grid.fill&&"column"===p.grid.fill||!p.grid.fill&&"column"===i.grid.fill)&&n.classList.add(`${i.containerModifierClass}grid-column`),e.emitContainerClasses()),m&&!v?e.unsetGrabCursor():!m&&v&&e.setGrabCursor(),["navigation","pagination","scrollbar"].forEach(t=>{if(void 0===p[t])return;let r=i[t]&&i[t].enabled,n=p[t]&&p[t].enabled;r&&!n&&e[t].disable(),!r&&n&&e[t].enable()});let y=p.direction&&p.direction!==i.direction,w=i.loop&&(p.slidesPerView!==i.slidesPerView||y),b=i.loop;y&&r&&e.changeDirection(),(0,l.w)(e.params,p);let E=e.params.enabled,T=e.params.loop;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),g&&!E?e.disable():!g&&E&&e.enable(),e.currentBreakpoint=c,e.emit("_beforeBreakpoint",p),r&&(w?(e.loopDestroy(),e.loopCreate(t),e.updateSlides()):!b&&T?(e.loopCreate(t),e.updateSlides()):b&&!T&&e.loopDestroy()),e.emit("breakpoint",p)},getBreakpoint:function(e,t,r){if(void 0===t&&(t="window"),!e||"container"===t&&!r)return;let i=!1,n=(0,o.a)(),s="window"===t?n.innerHeight:r.clientHeight,a=Object.keys(e).map(e=>"string"==typeof e&&0===e.indexOf("@")?{value:s*parseFloat(e.substr(1)),point:e}:{value:e,point:e});a.sort((e,t)=>parseInt(e.value,10)-parseInt(t.value,10));for(let e=0;e<a.length;e+=1){let{point:s,value:o}=a[e];"window"===t?n.matchMedia(`(min-width: ${o}px)`).matches&&(i=s):o<=r.clientWidth&&(i=s)}return i||"max"}},checkOverflow:{checkOverflow:function(){let{isLocked:e,params:t}=this,{slidesOffsetBefore:r}=t;if(r){let e=this.slides.length-1,t=this.slidesGrid[e]+this.slidesSizesGrid[e]+2*r;this.isLocked=this.size>t}else this.isLocked=1===this.snapGrid.length;!0===t.allowSlideNext&&(this.allowSlideNext=!this.isLocked),!0===t.allowSlidePrev&&(this.allowSlidePrev=!this.isLocked),e&&e!==this.isLocked&&(this.isEnd=!1),e!==this.isLocked&&this.emit(this.isLocked?"lock":"unlock")}},classes:{addClasses:function(){let{classNames:e,params:t,rtl:r,el:i,device:n}=this,s=function(e,t){let r=[];return e.forEach(e=>{"object"==typeof e?Object.keys(e).forEach(i=>{e[i]&&r.push(t+i)}):"string"==typeof e&&r.push(t+e)}),r}(["initialized",t.direction,{"free-mode":this.params.freeMode&&t.freeMode.enabled},{autoheight:t.autoHeight},{rtl:r},{grid:t.grid&&t.grid.rows>1},{"grid-column":t.grid&&t.grid.rows>1&&"column"===t.grid.fill},{android:n.android},{ios:n.ios},{"css-mode":t.cssMode},{centered:t.cssMode&&t.centeredSlides},{"watch-progress":t.watchSlidesProgress}],t.containerModifierClass);e.push(...s),i.classList.add(...e),this.emitContainerClasses()},removeClasses:function(){let{el:e,classNames:t}=this;e&&"string"!=typeof e&&(e.classList.remove(...t),this.emitContainerClasses())}}},L={};class A{constructor(){let e,t;for(var r=arguments.length,i=Array(r),n=0;n<r;n++)i[n]=arguments[n];1===i.length&&i[0].constructor&&"Object"===Object.prototype.toString.call(i[0]).slice(8,-1)?t=i[0]:[e,t]=i,t||(t={}),t=(0,l.w)({},t),e&&!t.el&&(t.el=e);let s=(0,o.g)();if(t.el&&"string"==typeof t.el&&s.querySelectorAll(t.el).length>1){let e=[];return s.querySelectorAll(t.el).forEach(r=>{let i=(0,l.w)({},t,{el:r});e.push(new A(i))}),e}let a=this;a.__swiper__=!0,a.support=d(),a.device=u({userAgent:t.userAgent}),a.browser=c(),a.eventsListeners={},a.eventsAnyListeners=[],a.modules=[...a.__modules__],t.modules&&Array.isArray(t.modules)&&a.modules.push(...t.modules);let p={};a.modules.forEach(e=>{var r;e({params:t,swiper:a,extendParams:(r=t,function(e){void 0===e&&(e={});let t=Object.keys(e)[0],i=e[t];if("object"!=typeof i||null===i||(!0===r[t]&&(r[t]={enabled:!0}),"navigation"===t&&r[t]&&r[t].enabled&&!r[t].prevEl&&!r[t].nextEl&&(r[t].auto=!0),["pagination","scrollbar"].indexOf(t)>=0&&r[t]&&r[t].enabled&&!r[t].el&&(r[t].auto=!0),!(t in r&&"enabled"in i))){(0,l.w)(p,e);return}"object"!=typeof r[t]||"enabled"in r[t]||(r[t].enabled=!0),r[t]||(r[t]={enabled:!1}),(0,l.w)(p,e)}),on:a.on.bind(a),once:a.once.bind(a),off:a.off.bind(a),emit:a.emit.bind(a)})});let h=(0,l.w)({},_,p);return a.params=(0,l.w)({},h,L,t),a.originalParams=(0,l.w)({},a.params),a.passedParams=(0,l.w)({},t),a.params&&a.params.on&&Object.keys(a.params.on).forEach(e=>{a.on(e,a.params.on[e])}),a.params&&a.params.onAny&&a.onAny(a.params.onAny),Object.assign(a,{enabled:a.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:()=>"horizontal"===a.params.direction,isVertical:()=>"vertical"===a.params.direction,activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return 8388608*Math.trunc(this.translate/8388608)},allowSlideNext:a.params.allowSlideNext,allowSlidePrev:a.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:a.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:a.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),a.emit("_swiper"),a.params.init&&a.init(),a}getDirectionLabel(e){return this.isHorizontal()?e:({width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"})[e]}getSlideIndex(e){let{slidesEl:t,params:r}=this,i=(0,l.e)(t,`.${r.slideClass}, swiper-slide`),n=(0,l.h)(i[0]);return(0,l.h)(e)-n}getSlideIndexByData(e){return this.getSlideIndex(this.slides.find(t=>1*t.getAttribute("data-swiper-slide-index")===e))}recalcSlides(){let{slidesEl:e,params:t}=this;this.slides=(0,l.e)(e,`.${t.slideClass}, swiper-slide`)}enable(){this.enabled||(this.enabled=!0,this.params.grabCursor&&this.setGrabCursor(),this.emit("enable"))}disable(){this.enabled&&(this.enabled=!1,this.params.grabCursor&&this.unsetGrabCursor(),this.emit("disable"))}setProgress(e,t){e=Math.min(Math.max(e,0),1);let r=this.minTranslate(),i=(this.maxTranslate()-r)*e+r;this.translateTo(i,void 0===t?0:t),this.updateActiveIndex(),this.updateSlidesClasses()}emitContainerClasses(){let e=this;if(!e.params._emitClasses||!e.el)return;let t=e.el.className.split(" ").filter(t=>0===t.indexOf("swiper")||0===t.indexOf(e.params.containerModifierClass));e.emit("_containerClasses",t.join(" "))}getSlideClasses(e){let t=this;return t.destroyed?"":e.className.split(" ").filter(e=>0===e.indexOf("swiper-slide")||0===e.indexOf(t.params.slideClass)).join(" ")}emitSlidesClasses(){let e=this;if(!e.params._emitClasses||!e.el)return;let t=[];e.slides.forEach(r=>{let i=e.getSlideClasses(r);t.push({slideEl:r,classNames:i}),e.emit("_slideClass",r,i)}),e.emit("_slideClasses",t)}slidesPerViewDynamic(e,t){void 0===e&&(e="current"),void 0===t&&(t=!1);let{params:r,slides:i,slidesGrid:n,slidesSizesGrid:s,size:a,activeIndex:o}=this,l=1;if("number"==typeof r.slidesPerView)return r.slidesPerView;if(r.centeredSlides){let e,t=i[o]?Math.ceil(i[o].swiperSlideSize):0;for(let r=o+1;r<i.length;r+=1)i[r]&&!e&&(t+=Math.ceil(i[r].swiperSlideSize),l+=1,t>a&&(e=!0));for(let r=o-1;r>=0;r-=1)i[r]&&!e&&(t+=i[r].swiperSlideSize,l+=1,t>a&&(e=!0))}else if("current"===e)for(let e=o+1;e<i.length;e+=1)(t?n[e]+s[e]-n[o]<a:n[e]-n[o]<a)&&(l+=1);else for(let e=o-1;e>=0;e-=1)n[o]-n[e]<a&&(l+=1);return l}update(){let e;let t=this;if(!t||t.destroyed)return;let{snapGrid:r,params:i}=t;function n(){let e=Math.min(Math.max(t.rtlTranslate?-1*t.translate:t.translate,t.maxTranslate()),t.minTranslate());t.setTranslate(e),t.updateActiveIndex(),t.updateSlidesClasses()}if(i.breakpoints&&t.setBreakpoint(),[...t.el.querySelectorAll('[loading="lazy"]')].forEach(e=>{e.complete&&f(t,e)}),t.updateSize(),t.updateSlides(),t.updateProgress(),t.updateSlidesClasses(),i.freeMode&&i.freeMode.enabled&&!i.cssMode)n(),i.autoHeight&&t.updateAutoHeight();else{if(("auto"===i.slidesPerView||i.slidesPerView>1)&&t.isEnd&&!i.centeredSlides){let r=t.virtual&&i.virtual.enabled?t.virtual.slides:t.slides;e=t.slideTo(r.length-1,0,!1,!0)}else e=t.slideTo(t.activeIndex,0,!1,!0);e||n()}i.watchOverflow&&r!==t.snapGrid&&t.checkOverflow(),t.emit("update")}changeDirection(e,t){void 0===t&&(t=!0);let r=this.params.direction;return e||(e="horizontal"===r?"vertical":"horizontal"),e===r||"horizontal"!==e&&"vertical"!==e||(this.el.classList.remove(`${this.params.containerModifierClass}${r}`),this.el.classList.add(`${this.params.containerModifierClass}${e}`),this.emitContainerClasses(),this.params.direction=e,this.slides.forEach(t=>{"vertical"===e?t.style.width="":t.style.height=""}),this.emit("changeDirection"),t&&this.update()),this}changeLanguageDirection(e){(!this.rtl||"rtl"!==e)&&(this.rtl||"ltr"!==e)&&(this.rtl="rtl"===e,this.rtlTranslate="horizontal"===this.params.direction&&this.rtl,this.rtl?(this.el.classList.add(`${this.params.containerModifierClass}rtl`),this.el.dir="rtl"):(this.el.classList.remove(`${this.params.containerModifierClass}rtl`),this.el.dir="ltr"),this.update())}mount(e){let t=this;if(t.mounted)return!0;let r=e||t.params.el;if("string"==typeof r&&(r=document.querySelector(r)),!r)return!1;r.swiper=t,r.parentNode&&r.parentNode.host&&r.parentNode.host.nodeName===t.params.swiperElementNodeName.toUpperCase()&&(t.isElement=!0);let i=()=>`.${(t.params.wrapperClass||"").trim().split(" ").join(".")}`,n=r&&r.shadowRoot&&r.shadowRoot.querySelector?r.shadowRoot.querySelector(i()):(0,l.e)(r,i())[0];return!n&&t.params.createElements&&(n=(0,l.c)("div",t.params.wrapperClass),r.append(n),(0,l.e)(r,`.${t.params.slideClass}`).forEach(e=>{n.append(e)})),Object.assign(t,{el:r,wrapperEl:n,slidesEl:t.isElement&&!r.parentNode.host.slideSlots?r.parentNode.host:n,hostEl:t.isElement?r.parentNode.host:r,mounted:!0,rtl:"rtl"===r.dir.toLowerCase()||"rtl"===(0,l.p)(r,"direction"),rtlTranslate:"horizontal"===t.params.direction&&("rtl"===r.dir.toLowerCase()||"rtl"===(0,l.p)(r,"direction")),wrongRTL:"-webkit-box"===(0,l.p)(n,"display")}),!0}init(e){let t=this;if(t.initialized||!1===t.mount(e))return t;t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.loop&&t.virtual&&t.params.virtual.enabled?t.slideTo(t.params.initialSlide+t.virtual.slidesBefore,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.params.loop&&t.loopCreate(),t.attachEvents();let r=[...t.el.querySelectorAll('[loading="lazy"]')];return t.isElement&&r.push(...t.hostEl.querySelectorAll('[loading="lazy"]')),r.forEach(e=>{e.complete?f(t,e):e.addEventListener("load",e=>{f(t,e.target)})}),v(t),t.initialized=!0,v(t),t.emit("init"),t.emit("afterInit"),t}destroy(e,t){void 0===e&&(e=!0),void 0===t&&(t=!0);let r=this,{params:i,el:n,wrapperEl:s,slides:a}=r;return void 0===r.params||r.destroyed||(r.emit("beforeDestroy"),r.initialized=!1,r.detachEvents(),i.loop&&r.loopDestroy(),t&&(r.removeClasses(),n&&"string"!=typeof n&&n.removeAttribute("style"),s&&s.removeAttribute("style"),a&&a.length&&a.forEach(e=>{e.classList.remove(i.slideVisibleClass,i.slideFullyVisibleClass,i.slideActiveClass,i.slideNextClass,i.slidePrevClass),e.removeAttribute("style"),e.removeAttribute("data-swiper-slide-index")})),r.emit("destroy"),Object.keys(r.eventsListeners).forEach(e=>{r.off(e)}),!1!==e&&(r.el&&"string"!=typeof r.el&&(r.el.swiper=null),(0,l.x)(r)),r.destroyed=!0),null}static extendDefaults(e){(0,l.w)(L,e)}static get extendedDefaults(){return L}static get defaults(){return _}static installModule(e){A.prototype.__modules__||(A.prototype.__modules__=[]);let t=A.prototype.__modules__;"function"==typeof e&&0>t.indexOf(e)&&t.push(e)}static use(e){return Array.isArray(e)?e.forEach(e=>A.installModule(e)):A.installModule(e),A}}Object.keys(O).forEach(e=>{Object.keys(O[e]).forEach(t=>{A.prototype[t]=O[e][t]})}),A.use([function(e){let{swiper:t,on:r,emit:i}=e,n=(0,o.a)(),s=null,a=null,l=()=>{t&&!t.destroyed&&t.initialized&&(i("beforeResize"),i("resize"))},d=()=>{t&&!t.destroyed&&t.initialized&&(s=new ResizeObserver(e=>{a=n.requestAnimationFrame(()=>{let{width:r,height:i}=t,n=r,s=i;e.forEach(e=>{let{contentBoxSize:r,contentRect:i,target:a}=e;a&&a!==t.el||(n=i?i.width:(r[0]||r).inlineSize,s=i?i.height:(r[0]||r).blockSize)}),(n!==r||s!==i)&&l()})})).observe(t.el)},u=()=>{a&&n.cancelAnimationFrame(a),s&&s.unobserve&&t.el&&(s.unobserve(t.el),s=null)},c=()=>{t&&!t.destroyed&&t.initialized&&i("orientationchange")};r("init",()=>{if(t.params.resizeObserver&&void 0!==n.ResizeObserver){d();return}n.addEventListener("resize",l),n.addEventListener("orientationchange",c)}),r("destroy",()=>{u(),n.removeEventListener("resize",l),n.removeEventListener("orientationchange",c)})},function(e){let{swiper:t,extendParams:r,on:i,emit:n}=e,s=[],a=(0,o.a)(),d=function(e,r){void 0===r&&(r={});let i=new(a.MutationObserver||a.WebkitMutationObserver)(e=>{if(t.__preventObserver__)return;if(1===e.length){n("observerUpdate",e[0]);return}let r=function(){n("observerUpdate",e[0])};a.requestAnimationFrame?a.requestAnimationFrame(r):a.setTimeout(r,0)});i.observe(e,{attributes:void 0===r.attributes||r.attributes,childList:t.isElement||(void 0===r.childList||r).childList,characterData:void 0===r.characterData||r.characterData}),s.push(i)};r({observer:!1,observeParents:!1,observeSlideChildren:!1}),i("init",()=>{if(t.params.observer){if(t.params.observeParents){let e=(0,l.a)(t.hostEl);for(let t=0;t<e.length;t+=1)d(e[t])}d(t.hostEl,{childList:t.params.observeSlideChildren}),d(t.wrapperEl,{attributes:!1})}}),i("destroy",()=>{s.forEach(e=>{e.disconnect()}),s.splice(0,s.length)})}]);let I=["eventsPrefix","injectStyles","injectStylesUrls","modules","init","_direction","oneWayMovement","swiperElementNodeName","touchEventsTarget","initialSlide","_speed","cssMode","updateOnWindowResize","resizeObserver","nested","focusableElements","_enabled","_width","_height","preventInteractionOnTransition","userAgent","url","_edgeSwipeDetection","_edgeSwipeThreshold","_freeMode","_autoHeight","setWrapperSize","virtualTranslate","_effect","breakpoints","breakpointsBase","_spaceBetween","_slidesPerView","maxBackfaceHiddenSlides","_grid","_slidesPerGroup","_slidesPerGroupSkip","_slidesPerGroupAuto","_centeredSlides","_centeredSlidesBounds","_slidesOffsetBefore","_slidesOffsetAfter","normalizeSlideIndex","_centerInsufficientSlides","_watchOverflow","roundLengths","touchRatio","touchAngle","simulateTouch","_shortSwipes","_longSwipes","longSwipesRatio","longSwipesMs","_followFinger","allowTouchMove","_threshold","touchMoveStopPropagation","touchStartPreventDefault","touchStartForcePreventDefault","touchReleaseOnEdges","uniqueNavElements","_resistance","_resistanceRatio","_watchSlidesProgress","_grabCursor","preventClicks","preventClicksPropagation","_slideToClickedSlide","_loop","loopAdditionalSlides","loopAddBlankSlides","loopPreventsSliding","_rewind","_allowSlidePrev","_allowSlideNext","_swipeHandler","_noSwiping","noSwipingClass","noSwipingSelector","passiveListeners","containerModifierClass","slideClass","slideActiveClass","slideVisibleClass","slideFullyVisibleClass","slideNextClass","slidePrevClass","slideBlankClass","wrapperClass","lazyPreloaderClass","lazyPreloadPrevNext","runCallbacksOnInit","observer","observeParents","observeSlideChildren","a11y","_autoplay","_controller","coverflowEffect","cubeEffect","fadeEffect","flipEffect","creativeEffect","cardsEffect","hashNavigation","history","keyboard","mousewheel","_navigation","_pagination","parallax","_scrollbar","_thumbs","virtual","zoom","control"];function j(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)&&!e.__swiper__}function N(e,t){let r=["__proto__","constructor","prototype"];Object.keys(t).filter(e=>0>r.indexOf(e)).forEach(r=>{void 0===e[r]?e[r]=t[r]:j(t[r])&&j(e[r])&&Object.keys(t[r]).length>0?t[r].__swiper__?e[r]=t[r]:N(e[r],t[r]):e[r]=t[r]})}function z(e){return void 0===e&&(e={}),e.navigation&&void 0===e.navigation.nextEl&&void 0===e.navigation.prevEl}function D(e){return void 0===e&&(e={}),e.pagination&&void 0===e.pagination.el}function F(e){return void 0===e&&(e={}),e.scrollbar&&void 0===e.scrollbar.el}function R(e){void 0===e&&(e="");let t=e.split(" ").map(e=>e.trim()).filter(e=>!!e),r=[];return t.forEach(e=>{0>r.indexOf(e)&&r.push(e)}),r.join(" ")}let V=e=>{e&&!e.destroyed&&e.params.virtual&&(!e.params.virtual||e.params.virtual.enabled)&&(e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),e.parallax&&e.params.parallax&&e.params.parallax.enabled&&e.parallax.setTranslate())};function G(){return(G=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(e[i]=r[i])}return e}).apply(this,arguments)}function B(e){return e.type&&e.type.displayName&&e.type.displayName.includes("SwiperSlide")}function $(e,t){return"undefined"==typeof window?(0,a.useEffect)(e,t):(0,a.useLayoutEffect)(e,t)}let q=(0,a.createContext)(null),H=(0,a.createContext)(null),W=(0,a.forwardRef)(function(e,t){var r;let{className:i,tag:n="div",wrapperTag:s="div",children:o,onSwiper:l,...d}=void 0===e?{}:e,u=!1,[c,p]=(0,a.useState)("swiper"),[h,f]=(0,a.useState)(null),[m,v]=(0,a.useState)(!1),g=(0,a.useRef)(!1),y=(0,a.useRef)(null),w=(0,a.useRef)(null),b=(0,a.useRef)(null),E=(0,a.useRef)(null),T=(0,a.useRef)(null),S=(0,a.useRef)(null),x=(0,a.useRef)(null),k=(0,a.useRef)(null),{params:P,passedParams:C,rest:M,events:O}=function(e,t){void 0===e&&(e={}),void 0===t&&(t=!0);let r={on:{}},i={},n={};N(r,_),r._emitClasses=!0,r.init=!1;let s={},a=I.map(e=>e.replace(/_/,""));return Object.keys(Object.assign({},e)).forEach(o=>{void 0!==e[o]&&(a.indexOf(o)>=0?j(e[o])?(r[o]={},n[o]={},N(r[o],e[o]),N(n[o],e[o])):(r[o]=e[o],n[o]=e[o]):0===o.search(/on[A-Z]/)&&"function"==typeof e[o]?t?i[`${o[2].toLowerCase()}${o.substr(3)}`]=e[o]:r.on[`${o[2].toLowerCase()}${o.substr(3)}`]=e[o]:s[o]=e[o])}),["navigation","pagination","scrollbar"].forEach(e=>{!0===r[e]&&(r[e]={}),!1===r[e]&&delete r[e]}),{params:r,passedParams:n,rest:s,events:i}}(d),{slides:L,slots:q}=function(e){let t=[],r={"container-start":[],"container-end":[],"wrapper-start":[],"wrapper-end":[]};return a.Children.toArray(e).forEach(e=>{if(B(e))t.push(e);else if(e.props&&e.props.slot&&r[e.props.slot])r[e.props.slot].push(e);else if(e.props&&e.props.children){let i=function e(t){let r=[];return a.Children.toArray(t).forEach(t=>{B(t)?r.push(t):t.props&&t.props.children&&e(t.props.children).forEach(e=>r.push(e))}),r}(e.props.children);i.length>0?i.forEach(e=>t.push(e)):r["container-end"].push(e)}else r["container-end"].push(e)}),{slides:t,slots:r}}(o),W=()=>{v(!m)};Object.assign(P.on,{_containerClasses(e,t){p(t)}});let Y=()=>{Object.assign(P.on,O),u=!0;let e={...P};if(delete e.wrapperClass,w.current=new A(e),w.current.virtual&&w.current.params.virtual.enabled){w.current.virtual.slides=L;let e={cache:!1,slides:L,renderExternal:f,renderExternalUpdate:!1};N(w.current.params.virtual,e),N(w.current.originalParams.virtual,e)}};y.current||Y(),w.current&&w.current.on("_beforeBreakpoint",W);let X=()=>{!u&&O&&w.current&&Object.keys(O).forEach(e=>{w.current.on(e,O[e])})},U=()=>{O&&w.current&&Object.keys(O).forEach(e=>{w.current.off(e,O[e])})};return(0,a.useEffect)(()=>()=>{w.current&&w.current.off("_beforeBreakpoint",W)}),(0,a.useEffect)(()=>{!g.current&&w.current&&(w.current.emitSlidesClasses(),g.current=!0)}),$(()=>{if(t&&(t.current=y.current),y.current)return w.current.destroyed&&Y(),function(e,t){let{el:r,nextEl:i,prevEl:n,paginationEl:s,scrollbarEl:a,swiper:o}=e;z(t)&&i&&n&&(o.params.navigation.nextEl=i,o.originalParams.navigation.nextEl=i,o.params.navigation.prevEl=n,o.originalParams.navigation.prevEl=n),D(t)&&s&&(o.params.pagination.el=s,o.originalParams.pagination.el=s),F(t)&&a&&(o.params.scrollbar.el=a,o.originalParams.scrollbar.el=a),o.init(r)}({el:y.current,nextEl:T.current,prevEl:S.current,paginationEl:x.current,scrollbarEl:k.current,swiper:w.current},P),l&&!w.current.destroyed&&l(w.current),()=>{w.current&&!w.current.destroyed&&w.current.destroy(!0,!1)}},[]),$(()=>{X();let e=function(e,t,r,i,n){let s=[];if(!t)return s;let a=e=>{0>s.indexOf(e)&&s.push(e)};if(r&&i){let e=i.map(n),t=r.map(n);e.join("")!==t.join("")&&a("children"),i.length!==r.length&&a("children")}return I.filter(e=>"_"===e[0]).map(e=>e.replace(/_/,"")).forEach(r=>{if(r in e&&r in t){if(j(e[r])&&j(t[r])){let i=Object.keys(e[r]),n=Object.keys(t[r]);i.length!==n.length?a(r):(i.forEach(i=>{e[r][i]!==t[r][i]&&a(r)}),n.forEach(i=>{e[r][i]!==t[r][i]&&a(r)}))}else e[r]!==t[r]&&a(r)}}),s}(C,b.current,L,E.current,e=>e.key);return b.current=C,E.current=L,e.length&&w.current&&!w.current.destroyed&&function(e){let t,r,i,n,s,a,o,l,{swiper:d,slides:u,passedParams:c,changedParams:p,nextEl:h,prevEl:f,scrollbarEl:m,paginationEl:v}=e,g=p.filter(e=>"children"!==e&&"direction"!==e&&"wrapperClass"!==e),{params:y,pagination:w,navigation:b,scrollbar:E,virtual:T,thumbs:S}=d;p.includes("thumbs")&&c.thumbs&&c.thumbs.swiper&&!c.thumbs.swiper.destroyed&&y.thumbs&&(!y.thumbs.swiper||y.thumbs.swiper.destroyed)&&(t=!0),p.includes("controller")&&c.controller&&c.controller.control&&y.controller&&!y.controller.control&&(r=!0),p.includes("pagination")&&c.pagination&&(c.pagination.el||v)&&(y.pagination||!1===y.pagination)&&w&&!w.el&&(i=!0),p.includes("scrollbar")&&c.scrollbar&&(c.scrollbar.el||m)&&(y.scrollbar||!1===y.scrollbar)&&E&&!E.el&&(n=!0),p.includes("navigation")&&c.navigation&&(c.navigation.prevEl||f)&&(c.navigation.nextEl||h)&&(y.navigation||!1===y.navigation)&&b&&!b.prevEl&&!b.nextEl&&(s=!0);let x=e=>{d[e]&&(d[e].destroy(),"navigation"===e?(d.isElement&&(d[e].prevEl.remove(),d[e].nextEl.remove()),y[e].prevEl=void 0,y[e].nextEl=void 0,d[e].prevEl=void 0,d[e].nextEl=void 0):(d.isElement&&d[e].el.remove(),y[e].el=void 0,d[e].el=void 0))};p.includes("loop")&&d.isElement&&(y.loop&&!c.loop?a=!0:!y.loop&&c.loop?o=!0:l=!0),g.forEach(e=>{if(j(y[e])&&j(c[e]))Object.assign(y[e],c[e]),("navigation"===e||"pagination"===e||"scrollbar"===e)&&"enabled"in c[e]&&!c[e].enabled&&x(e);else{let t=c[e];(!0===t||!1===t)&&("navigation"===e||"pagination"===e||"scrollbar"===e)?!1===t&&x(e):y[e]=c[e]}}),g.includes("controller")&&!r&&d.controller&&d.controller.control&&y.controller&&y.controller.control&&(d.controller.control=y.controller.control),p.includes("children")&&u&&T&&y.virtual.enabled?(T.slides=u,T.update(!0)):p.includes("virtual")&&T&&y.virtual.enabled&&(u&&(T.slides=u),T.update(!0)),p.includes("children")&&u&&y.loop&&(l=!0),t&&S.init()&&S.update(!0),r&&(d.controller.control=y.controller.control),i&&(d.isElement&&(!v||"string"==typeof v)&&((v=document.createElement("div")).classList.add("swiper-pagination"),v.part.add("pagination"),d.el.appendChild(v)),v&&(y.pagination.el=v),w.init(),w.render(),w.update()),n&&(d.isElement&&(!m||"string"==typeof m)&&((m=document.createElement("div")).classList.add("swiper-scrollbar"),m.part.add("scrollbar"),d.el.appendChild(m)),m&&(y.scrollbar.el=m),E.init(),E.updateSize(),E.setTranslate()),s&&(d.isElement&&(h&&"string"!=typeof h||((h=document.createElement("div")).classList.add("swiper-button-next"),h.innerHTML=d.hostEl.constructor.nextButtonSvg,h.part.add("button-next"),d.el.appendChild(h)),f&&"string"!=typeof f||((f=document.createElement("div")).classList.add("swiper-button-prev"),f.innerHTML=d.hostEl.constructor.prevButtonSvg,f.part.add("button-prev"),d.el.appendChild(f))),h&&(y.navigation.nextEl=h),f&&(y.navigation.prevEl=f),b.init(),b.update()),p.includes("allowSlideNext")&&(d.allowSlideNext=c.allowSlideNext),p.includes("allowSlidePrev")&&(d.allowSlidePrev=c.allowSlidePrev),p.includes("direction")&&d.changeDirection(c.direction,!1),(a||l)&&d.loopDestroy(),(o||l)&&d.loopCreate(),d.update()}({swiper:w.current,slides:L,passedParams:C,changedParams:e,nextEl:T.current,prevEl:S.current,scrollbarEl:k.current,paginationEl:x.current}),()=>{U()}}),$(()=>{V(w.current)},[h]),a.createElement(n,G({ref:y,className:R(`${c}${i?` ${i}`:""}`)},M),a.createElement(H.Provider,{value:w.current},q["container-start"],a.createElement(s,{className:(void 0===(r=P.wrapperClass)&&(r=""),r)?r.includes("swiper-wrapper")?r:`swiper-wrapper ${r}`:"swiper-wrapper"},q["wrapper-start"],P.virtual?function(e,t,r){if(!r)return null;let i=e=>{let r=e;return e<0?r=t.length+e:r>=t.length&&(r-=t.length),r},n=e.isHorizontal()?{[e.rtlTranslate?"right":"left"]:`${r.offset}px`}:{top:`${r.offset}px`},{from:s,to:o}=r,l=e.params.loop?-t.length:0,d=e.params.loop?2*t.length:t.length,u=[];for(let e=l;e<d;e+=1)e>=s&&e<=o&&u.push(t[i(e)]);return u.map((t,r)=>a.cloneElement(t,{swiper:e,style:n,key:t.props.virtualIndex||t.key||`slide-${r}`}))}(w.current,L,h):L.map((e,t)=>a.cloneElement(e,{swiper:w.current,swiperSlideIndex:t})),q["wrapper-end"]),z(P)&&a.createElement(a.Fragment,null,a.createElement("div",{ref:S,className:"swiper-button-prev"}),a.createElement("div",{ref:T,className:"swiper-button-next"})),F(P)&&a.createElement("div",{ref:k,className:"swiper-scrollbar"}),D(P)&&a.createElement("div",{ref:x,className:"swiper-pagination"}),q["container-end"]))});W.displayName="Swiper";let Y=(0,a.forwardRef)(function(e,t){let{tag:r="div",children:i,className:n="",swiper:s,zoom:o,lazy:l,virtualIndex:d,swiperSlideIndex:u,...c}=void 0===e?{}:e,p=(0,a.useRef)(null),[h,f]=(0,a.useState)("swiper-slide"),[m,v]=(0,a.useState)(!1);function g(e,t,r){t===p.current&&f(r)}$(()=>{if(void 0!==u&&(p.current.swiperSlideIndex=u),t&&(t.current=p.current),p.current&&s){if(s.destroyed){"swiper-slide"!==h&&f("swiper-slide");return}return s.on("_slideClass",g),()=>{s&&s.off("_slideClass",g)}}}),$(()=>{s&&p.current&&!s.destroyed&&f(s.getSlideClasses(p.current))},[s]);let y={isActive:h.indexOf("swiper-slide-active")>=0,isVisible:h.indexOf("swiper-slide-visible")>=0,isPrev:h.indexOf("swiper-slide-prev")>=0,isNext:h.indexOf("swiper-slide-next")>=0},w=()=>"function"==typeof i?i(y):i;return a.createElement(r,G({ref:p,className:R(`${h}${n?` ${n}`:""}`),"data-swiper-slide-index":d,onLoad:()=>{v(!0)}},c),o&&a.createElement(q.Provider,{value:y},a.createElement("div",{className:"swiper-zoom-container","data-swiper-zoom":"number"==typeof o?o:void 0},w(),l&&!m&&a.createElement("div",{className:"swiper-lazy-preloader"}))),!o&&a.createElement(q.Provider,{value:y},w(),l&&!m&&a.createElement("div",{className:"swiper-lazy-preloader"})))});Y.displayName="SwiperSlide"}}]);