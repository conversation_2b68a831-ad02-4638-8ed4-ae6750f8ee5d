try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="c436f2a9-1e9e-4244-85c8-2729d7b3a67f",e._sentryDebugIdIdentifier="sentry-dbid-c436f2a9-1e9e-4244-85c8-2729d7b3a67f")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4924],{79372:function(e,t,r){r.d(t,{Z:function(){return i}});var n=r(67294);function i(){return(0,n.useState)(null)}},34924:function(e,t,r){r.d(t,{Z:function(){return el}});var n=r(67294),i=r(93967),o=r.n(i),l=r(73935),s=r(79372),a=r(38490),u=Object.prototype.hasOwnProperty;function f(e,t,r){for(r of e.keys())if(c(r,t))return r}function c(e,t){var r,n,i;if(e===t)return!0;if(e&&t&&(r=e.constructor)===t.constructor){if(r===Date)return e.getTime()===t.getTime();if(r===RegExp)return e.toString()===t.toString();if(r===Array){if((n=e.length)===t.length)for(;n--&&c(e[n],t[n]););return -1===n}if(r===Set){if(e.size!==t.size)return!1;for(n of e)if((i=n)&&"object"==typeof i&&!(i=f(t,i))||!t.has(i))return!1;return!0}if(r===Map){if(e.size!==t.size)return!1;for(n of e)if((i=n[0])&&"object"==typeof i&&!(i=f(t,i))||!c(n[1],t.get(i)))return!1;return!0}if(r===ArrayBuffer)e=new Uint8Array(e),t=new Uint8Array(t);else if(r===DataView){if((n=e.byteLength)===t.byteLength)for(;n--&&e.getInt8(n)===t.getInt8(n););return -1===n}if(ArrayBuffer.isView(e)){if((n=e.byteLength)===t.byteLength)for(;n--&&e[n]===t[n];);return -1===n}if(!r||"object"==typeof e){for(r in n=0,e)if(u.call(e,r)&&++n&&!u.call(t,r)||!(r in t)||!c(e[r],t[r]))return!1;return Object.keys(t).length===n}}return e!=e&&t!=t}var d=r(61218),p=function(e){let t=(0,d.Z)();return[e[0],(0,n.useCallback)(r=>{if(t())return e[1](r)},[t,e[1]])]},v=r(66896),b=r(36531),y=r(82372),g=r(68855),m=r(19892),w=r(82122),h=r(77421),O=r(394);let Z=(0,r(45704).kZ)({defaultModifiers:[m.Z,h.Z,b.Z,y.Z,w.Z,g.Z,O.Z,v.Z]}),E=["enabled","placement","strategy","modifiers"],j={name:"applyStyles",enabled:!1,phase:"afterWrite",fn:()=>void 0},k={name:"ariaDescribedBy",enabled:!0,phase:"afterWrite",effect:({state:e})=>()=>{let{reference:t,popper:r}=e.elements;if("removeAttribute"in t){let e=(t.getAttribute("aria-describedby")||"").split(",").filter(e=>e.trim()!==r.id);e.length?t.setAttribute("aria-describedby",e.join(",")):t.removeAttribute("aria-describedby")}},fn:({state:e})=>{var t;let{popper:r,reference:n}=e.elements,i=null==(t=r.getAttribute("role"))?void 0:t.toLowerCase();if(r.id&&"tooltip"===i&&"setAttribute"in n){let e=n.getAttribute("aria-describedby");if(e&&-1!==e.split(",").indexOf(r.id))return;n.setAttribute("aria-describedby",e?`${e},${r.id}`:r.id)}}},_=[];var x=function(e,t,r={}){let{enabled:i=!0,placement:o="bottom",strategy:l="absolute",modifiers:s=_}=r,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(r,E),u=(0,n.useRef)(s),f=(0,n.useRef)(),d=(0,n.useCallback)(()=>{var e;null==(e=f.current)||e.update()},[]),v=(0,n.useCallback)(()=>{var e;null==(e=f.current)||e.forceUpdate()},[]),[b,y]=p((0,n.useState)({placement:o,update:d,forceUpdate:v,attributes:{},styles:{popper:{},arrow:{}}})),g=(0,n.useMemo)(()=>({name:"updateStateModifier",enabled:!0,phase:"write",requires:["computeStyles"],fn:({state:e})=>{let t={},r={};Object.keys(e.elements).forEach(n=>{t[n]=e.styles[n],r[n]=e.attributes[n]}),y({state:e,styles:t,attributes:r,update:d,forceUpdate:v,placement:e.placement})}}),[d,v,y]),m=(0,n.useMemo)(()=>(c(u.current,s)||(u.current=s),u.current),[s]);return(0,n.useEffect)(()=>{f.current&&i&&f.current.setOptions({placement:o,strategy:l,modifiers:[...m,g,j]})},[l,o,g,i,m]),(0,n.useEffect)(()=>{if(i&&null!=e&&null!=t)return f.current=Z(e,t,Object.assign({},a,{placement:o,strategy:l,modifiers:[...m,k,g]})),()=>{null!=f.current&&(f.current.destroy(),f.current=void 0,y(e=>Object.assign({},e,{attributes:{},styles:{popper:{}}})))}},[i,e,t]),b},N=r(72950),A=r(67216),R=r(67177),C=r(90424),S=r(42473),P=r.n(S);let T=()=>{},D=e=>e&&("current"in e?e.current:e),F={click:"mousedown",mouseup:"mousedown",pointerup:"pointerdown"};var L=function(e,t=T,{disabled:r,clickTrigger:i="click"}={}){let o=(0,n.useRef)(!1),l=(0,n.useRef)(!1),s=(0,n.useCallback)(t=>{let r=D(e);P()(!!r,"ClickOutside captured a close event but does not have a ref to compare it to. useClickOutside(), should be passed a ref that resolves to a DOM node"),o.current=!r||!!(t.metaKey||t.altKey||t.ctrlKey||t.shiftKey)||0!==t.button||!!(0,C.Z)(r,t.target)||l.current,l.current=!1},[e]),a=(0,R.Z)(t=>{let r=D(e);r&&(0,C.Z)(r,t.target)?l.current=!0:l.current=!1}),u=(0,R.Z)(e=>{o.current||t(e)});(0,n.useEffect)(()=>{var t,n;if(r||null==e)return;let o=(0,A.Z)(D(e)),l=o.defaultView||window,f=null!=(t=l.event)?t:null==(n=l.parent)?void 0:n.event,c=null;F[i]&&(c=(0,N.Z)(o,F[i],a,!0));let d=(0,N.Z)(o,i,s,!0),p=(0,N.Z)(o,i,e=>{if(e===f){f=void 0;return}u(e)}),v=[];return"ontouchstart"in o.documentElement&&(v=[].slice.call(o.body.children).map(e=>(0,N.Z)(e,"mousemove",T))),()=>{null==c||c(),d(),p(),v.forEach(e=>e())}},[e,r,i,s,a,u])},I=r(96899);let U=()=>{};var z=function(e,t,{disabled:r,clickTrigger:i}={}){let o=t||U;L(e,o,{disabled:r,clickTrigger:i});let l=(0,R.Z)(e=>{(0,I.kl)(e)&&o(e)});(0,n.useEffect)(()=>{if(r||null==e)return;let t=(0,A.Z)(D(e)),n=(t.defaultView||window).event,i=(0,N.Z)(t,"keyup",e=>{if(e===n){n=void 0;return}l(e)});return()=>{i()}},[e,r,l])},M=r(54194),B=r(40655);let H=n.forwardRef((e,t)=>{let{flip:r,offset:i,placement:o,containerPadding:u,popperConfig:f={},transition:c,runTransition:d}=e,[p,v]=(0,s.Z)(),[b,y]=(0,s.Z)(),g=(0,a.Z)(v,t),m=(0,M.Z)(e.container),w=(0,M.Z)(e.target),[h,O]=(0,n.useState)(!e.show),Z=x(w,p,function({enabled:e,enableEvents:t,placement:r,flip:n,offset:i,fixed:o,containerPadding:l,arrowElement:s,popperConfig:a={}}){var u,f,c,d,p;let v=function(e){let t={};return Array.isArray(e)?(null==e||e.forEach(e=>{t[e.name]=e}),t):e||t}(a.modifiers);return Object.assign({},a,{placement:r,enabled:e,strategy:o?"fixed":a.strategy,modifiers:function(e={}){return Array.isArray(e)?e:Object.keys(e).map(t=>(e[t].name=t,e[t]))}(Object.assign({},v,{eventListeners:{enabled:t,options:null==(u=v.eventListeners)?void 0:u.options},preventOverflow:Object.assign({},v.preventOverflow,{options:l?Object.assign({padding:l},null==(f=v.preventOverflow)?void 0:f.options):null==(c=v.preventOverflow)?void 0:c.options}),offset:{options:Object.assign({offset:i},null==(d=v.offset)?void 0:d.options)},arrow:Object.assign({},v.arrow,{enabled:!!s,options:Object.assign({},null==(p=v.arrow)?void 0:p.options,{element:s})}),flip:Object.assign({enabled:!!n},v.flip)}))})}({placement:o,enableEvents:!!e.show,containerPadding:u||5,flip:r,offset:i,arrowElement:b,popperConfig:f}));e.show&&h&&O(!1);let E=e.show||!h;if(z(p,e.onHide,{disabled:!e.rootClose||e.rootCloseDisabled,clickTrigger:e.rootCloseEvent}),!E)return null;let{onExit:j,onExiting:k,onEnter:_,onEntering:N,onEntered:A}=e,R=e.children(Object.assign({},Z.attributes.popper,{style:Z.styles.popper,ref:g}),{popper:Z,placement:o,show:!!e.show,arrowProps:Object.assign({},Z.attributes.arrow,{style:Z.styles.arrow,ref:y})});return R=(0,B.sD)(c,d,{in:!!e.show,appear:!0,mountOnEnter:!0,unmountOnExit:!0,children:R,onExit:j,onExiting:k,onExited:(...t)=>{O(!0),e.onExited&&e.onExited(...t)},onEnter:_,onEntering:N,onEntered:A}),m?l.createPortal(R,m):null});H.displayName="Overlay";var K=r(51176);let V=void 0!==r.g&&r.g.navigator&&"ReactNative"===r.g.navigator.product;var W="undefined"!=typeof document||V?n.useLayoutEffect:n.useEffect,$=r(35654),q=r(11132),G=r(97400),J=r(85893);let Q=n.forwardRef((e,t)=>{let{className:r,bsPrefix:n,as:i="div",...l}=e;return n=(0,G.vE)(n,"popover-header"),(0,J.jsx)(i,{ref:t,className:o()(r,n),...l})});Q.displayName="PopoverHeader";let X=n.forwardRef((e,t)=>{let{className:r,bsPrefix:n,as:i="div",...l}=e;return n=(0,G.vE)(n,"popover-body"),(0,J.jsx)(i,{ref:t,className:o()(r,n),...l})});X.displayName="PopoverBody";var Y=r(56308),ee=r(67209),et=Object.assign(n.forwardRef((e,t)=>{let{bsPrefix:r,placement:n="right",className:i,style:l,children:s,body:a,arrowProps:u,hasDoneInitialMeasure:f,popper:c,show:d,...p}=e,v=(0,G.vE)(r,"popover"),b=(0,G.SC)(),[y]=(null==n?void 0:n.split("-"))||[],g=(0,Y.z)(y,b),m=l;return d&&!f&&(m={...l,...(0,ee.Z)(null==c?void 0:c.strategy)}),(0,J.jsxs)("div",{ref:t,role:"tooltip",style:m,"x-placement":y,className:o()(i,v,y&&"bs-popover-".concat(g)),...p,children:[(0,J.jsx)("div",{className:"popover-arrow",...u}),a?(0,J.jsx)(X,{children:s}):s]})}),{Header:Q,Body:X,POPPER_OFFSET:[0,8]}),er=r(46350),en=r(63730),ei=r(73497);let eo=n.forwardRef((e,t)=>{let{children:r,transition:i=en.Z,popperConfig:l={},rootClose:s=!1,placement:a="top",show:u=!1,...f}=e,c=(0,n.useRef)({}),[d,p]=(0,n.useState)(null),[v,b]=function(e){let t=(0,n.useRef)(null),r=(0,G.vE)(void 0,"popover"),i=(0,G.vE)(void 0,"tooltip"),o=(0,n.useMemo)(()=>({name:"offset",options:{offset:()=>{if(e)return e;if(t.current){if((0,q.Z)(t.current,r))return et.POPPER_OFFSET;if((0,q.Z)(t.current,i))return er.Z.TOOLTIP_OFFSET}return[0,0]}}}),[e,r,i]);return[t,[o]]}(f.offset),y=(0,$.Z)(t,v),g=!0===i?en.Z:i||void 0,m=(0,K.Z)(e=>{p(e),null==l||null==l.onFirstUpdate||l.onFirstUpdate(e)});return W(()=>{d&&f.target&&(null==c.current.scheduleUpdate||c.current.scheduleUpdate())},[d,f.target]),(0,n.useEffect)(()=>{u||p(null)},[u]),(0,J.jsx)(H,{...f,ref:y,popperConfig:{...l,modifiers:b.concat(l.modifiers||[]),onFirstUpdate:m},transition:g,rootClose:s,placement:a,show:u,children:(e,t)=>{var s;let{arrowProps:a,popper:u,show:f}=t;!function(e,t){let{ref:r}=e,{ref:n}=t;e.ref=r.__wrapped||(r.__wrapped=e=>r((0,ei.Z)(e))),t.ref=n.__wrapped||(n.__wrapped=e=>n((0,ei.Z)(e)))}(e,a);let p=null==u?void 0:u.placement,v=Object.assign(c.current,{state:null==u?void 0:u.state,scheduleUpdate:null==u?void 0:u.update,placement:p,outOfBoundaries:(null==u||null==(s=u.state)||null==(s=s.modifiersData.hide)?void 0:s.isReferenceHidden)||!1,strategy:l.strategy}),b=!!d;return"function"==typeof r?r({...e,placement:p,show:f,...!i&&f&&{className:"show"},popper:v,arrowProps:a,hasDoneInitialMeasure:b}):n.cloneElement(r,{...e,placement:p,arrowProps:a,popper:v,hasDoneInitialMeasure:b,className:o()(r.props.className,!i&&f&&"show"),style:{...r.props.style,...e.style}})}})});eo.displayName="Overlay";var el=eo},46350:function(e,t,r){var n=r(93967),i=r.n(n),o=r(67294),l=r(97400),s=r(56308),a=r(67209),u=r(85893);let f=o.forwardRef((e,t)=>{let{bsPrefix:r,placement:n="right",className:o,style:f,children:c,arrowProps:d,hasDoneInitialMeasure:p,popper:v,show:b,...y}=e;r=(0,l.vE)(r,"tooltip");let g=(0,l.SC)(),[m]=(null==n?void 0:n.split("-"))||[],w=(0,s.z)(m,g),h=f;return b&&!p&&(h={...f,...(0,a.Z)(null==v?void 0:v.strategy)}),(0,u.jsxs)("div",{ref:t,style:h,role:"tooltip","x-placement":m,className:i()(o,r,"bs-tooltip-".concat(w)),...y,children:[(0,u.jsx)("div",{className:"tooltip-arrow",...d}),(0,u.jsx)("div",{className:"".concat(r,"-inner"),children:c})]})});f.displayName="Tooltip",t.Z=Object.assign(f,{TOOLTIP_OFFSET:[0,6]})},67209:function(e,t,r){r.d(t,{Z:function(){return n}});function n(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"absolute";return{position:e,top:"0",left:"0",opacity:"0",pointerEvents:"none"}}},56308:function(e,t,r){function n(e,t){let r=e;return"left"===e?r=t?"end":"start":"right"===e&&(r=t?"start":"end"),r}r.d(t,{z:function(){return n}}),r(67294)}}]);