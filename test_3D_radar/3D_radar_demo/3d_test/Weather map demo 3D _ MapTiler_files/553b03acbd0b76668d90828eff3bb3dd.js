!function(){var t=function(){var t='[data-cookiescript="accepted"]',e=function(t,e){try{var a;"function"==typeof Event?a=new CustomEvent(t,{bubbles:!0,cancelable:!0,detail:e}):(a=document.createEvent("CustomEvent")).initCustomEvent(t,!0,!0,e),window.document.dispatchEvent(a)}catch(t){console.warn("Warning: You browser not support dispatch event")}},a=function(t,e){t.insertAdjacentHTML("afterend",e),t.parentNode.removeChild(t)},o=function(t,e){var a=!1;return t.indexOf(e)>=0&&(a=!0),a},r=function(t){window.dataLayer=window.dataLayer||[],window.gtag=window.gtag||function(){dataLayer.push(arguments)};var e=function(){if(window.google_tag_data&&window.google_tag_data.ics&&window.google_tag_data.ics.entries){var t=Object.keys(window.google_tag_data.ics.entries);return t.length>0&&t}return!1}();if(window.gtag){const a={ad_storage:"granted",analytics_storage:"granted"};e?(o(e,"ad_personalization")&&(a.ad_personalization="granted"),o(e,"ad_user_data")&&(a.ad_user_data="granted")):(a.ad_personalization="granted",a.ad_user_data="granted"),a.functionality_storage="granted",a.personalization_storage="granted",a.security_storage="granted",gtag("consent",t,a)}};void 0===window.CookieScript&&(!function(){window.dataLayer=window.dataLayer||[],window.gtag=window.gtag||function(){dataLayer.push(arguments)};var t=!1,e=!1;if(window.google_tag_data&&window.google_tag_data.ics&&(t=!0===window.google_tag_data.ics.usedDefault),window.dataLayer)for(let t=0;t<window.dataLayer.length;t++)"consent"===window.dataLayer[t][0]&&"default"===window.dataLayer[t][1]&&(e=!0);t||gtag("set","developer_id.dMmY1Mm",!0),t||e||r("default")}(),function(){window.CookieScriptGeo={cmpId:374};var t=document.createElement("script");t.setAttribute("type","text/javascript"),t.src="https://cdn.cookie-script.com/iabtcf/2.2/sdk_cmp.js",t.onload=function(){new window.CookieScriptCMP.iabCMP(!0,!0),e("CookieScriptCMPLoaded")},document.head.appendChild(t)}(),function(t){var e=document.querySelectorAll("img"+t);if(e)for(var a=0;a<e.length;a++){var o=e[a];o.setAttribute("src",o.getAttribute("data-src")),o.removeAttribute("data-src"),o.removeAttribute("data-cookiescript")}}(t),function(t){var e,a,o=document.querySelectorAll('script[type="text/plain"]'+t);if(o)for(var r=0;r<o.length;r++){var i=o[r],n=i.cloneNode(!0);n.setAttribute("type","text/javascript"),n.removeAttribute("data-cookiescript"),a=n,(e=i).insertAdjacentElement("afterend",a),e.parentNode.removeChild(e)}}(t),function(t){var e=document.querySelectorAll("iframe"+t);if(e)for(var a=0;a<e.length;a++){var o=e[a];o.setAttribute("src",o.getAttribute("data-src")),o.removeAttribute("data-src"),o.removeAttribute("data-cookiescript")}}(t),function(t){var e=document.querySelectorAll("embed"+t);if(e)for(var o=0;o<e.length;o++){var r=e[o];r.setAttribute("src",r.getAttribute("data-src")),r.removeAttribute("data-src"),r.removeAttribute("data-cookiescript");var i=r.outerHTML;a(r,i)}}(t),function(t){var e=document.querySelectorAll("object"+t);if(e)for(var o=0;o<e.length;o++){var r=e[o];r.setAttribute("data",r.getAttribute("data-data")),r.removeAttribute("data-data"),r.removeAttribute("data-cookiescript");var i=r.outerHTML;a(r,i)}}(t),function(t){var e=document.querySelectorAll("link"+t);if(e)for(var a=0;a<e.length;a++){var o=e[a];o.setAttribute("href",o.getAttribute("data-href")),o.removeAttribute("data-href"),o.removeAttribute("data-cookiescript")}}(t),function(){window.dataLayer=window.dataLayer||[];for(var t=["CookieScriptCategory-strict","CookieScriptCategory-targeting","CookieScriptCategory-performance","CookieScriptCategory-functionality","CookieScriptCategory-unclassified","CookieScriptCategory-all"],e=0;e<t.length;e++)window.dataLayer.push({event:t[e]})}(),r("update"),window.fbq&&fbq("consent","grant"),function(){for(var t=["CookieScriptCategory-strict","CookieScriptCategory-targeting","CookieScriptCategory-performance","CookieScriptCategory-functionality","CookieScriptCategory-unclassified","CookieScriptCategory-all"],a=0;a<t.length;a++)e(t[a])}(),e("CookieScriptOutsideGeoLoaded"))};!function(){"use strict";function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}var e;e=function(){for(var e,a,o=[],r=window,i=r;i;){try{if(i.frames.__tcfapiLocator){e=i;break}}catch(e){}if(i===r.top)break;i=i.parent}e||(function t(){var e=r.document,a=!!r.frames.__tcfapiLocator;if(!a)if(e.body){var o=e.createElement("iframe");o.style.cssText="display:none",o.name="__tcfapiLocator",e.body.appendChild(o)}else setTimeout(t,5);return!a}(),r.__tcfapi=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return o;"setGdprApplies"===e[0]?e.length>3&&2===parseInt(e[1],10)&&"boolean"==typeof e[3]&&(a=e[3],"function"==typeof e[2]&&e[2]("set",!0)):"ping"===e[0]?"function"==typeof e[2]&&e[2]({gdprApplies:a,cmpLoaded:!1,cmpStatus:"stub"}):o.push(e)},r.addEventListener("message",(function(e){var a="string"==typeof e.data,o={};if(a)try{o=JSON.parse(e.data)}catch(e){}else o=e.data;var r="object"===t(o)&&null!==o?o.__tcfapiCall:null;r&&window.__tcfapi(r.command,r.version,(function(t,o){var i={__tcfapiReturn:{returnValue:t,success:o,callId:r.callId}};e&&e.source&&e.source.postMessage&&e.source.postMessage(a?JSON.stringify(i):i,"*")}),r.parameter)}),!1))},"undefined"!=typeof module?module.exports=e:e()}(),"complete"===document.readyState?t():window.addEventListener("load",t)}();