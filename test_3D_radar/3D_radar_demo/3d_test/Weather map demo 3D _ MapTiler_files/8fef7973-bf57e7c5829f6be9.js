try{let t="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},e=(new t.Error).stack;e&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[e]="bb5468d3-9cb8-4604-a858-c0105bc5c4d8",t._sentryDebugIdIdentifier="sentry-dbid-bb5468d3-9cb8-4604-a858-c0105bc5c4d8")}catch(t){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[652],{32608:function(t,e,n){!function(t){"use strict";var e,r,i=function(){return(i=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)};function s(t,e,n,r){return new(n||(n=Promise))(function(i,s){function a(t){try{o(r.next(t))}catch(t){s(t)}}function u(t){try{o(r.throw(t))}catch(t){s(t)}}function o(t){var e;t.done?i(t.value):((e=t.value)instanceof n?e:new n(function(t){t(e)})).then(a,u)}o((r=r.apply(t,e||[])).next())})}function a(t,e){var n,r,i,s,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return s={next:u(0),throw:u(1),return:u(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function u(u){return function(o){return function(u){if(n)throw TypeError("Generator is already executing.");for(;s&&(s=0,u[0]&&(a=0)),a;)try{if(n=1,r&&(i=2&u[0]?r.return:u[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,u[1])).done)return i;switch(r=0,i&&(u=[2&u[0],i.value]),u[0]){case 0:case 1:i=u;break;case 4:return a.label++,{value:u[1],done:!1};case 5:a.label++,r=u[1],u=[0];continue;case 7:u=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===u[0]||2===u[0])){a=0;continue}if(3===u[0]&&(!i||u[1]>i[0]&&u[1]<i[3])){a.label=u[1];break}if(6===u[0]&&a.label<i[1]){a.label=i[1],i=u;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(u);break}i[2]&&a.ops.pop(),a.trys.pop();continue}u=e.call(t,a)}catch(t){u=[6,t],r=0}finally{n=i=0}if(5&u[0])throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}([u,o])}}}"function"==typeof SuppressedError&&SuppressedError,"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:void 0!==n.g?n.g:"undefined"!=typeof self&&self;var u=(e=r={exports:{}},function(t){var e,n,r,i,s,a,u,o,c,p,h,d,l,f,v,y,_,g,w,m,x,b,k,A,I,T,E,S,R,D,N,O,q,P,L,C,M,U,F,j,V,K,H,G,B,Y,X,W,Q,z,J,Z,$,tt,te,tn,tr,ti,ts,ta,tu,to,tc,tp,th,td,tl,tf,tv,ty,t_,tg,tw,tm,tx,tb,tk,tA,tI,tT,tE,tS,tR,tD,tN,tO,tq,tP,tL,tC,tM,tU,tF,tj,tV,tK,tH,tG,tB,tY,tX,tW,tQ,tz,tJ,tZ,t$,t0,t3,t1,t4,t2,t5,t6,t8,t7,t9,et,ee,en,er,ei,es,ea,eu,eo,ec,ep,eh,ed,el,ef,ev,ey,e_={TASK_SUCCEEDED:"succeeded",TASK_PROCESSING:"processing",TASK_FAILED:"failed",TASK_ENQUEUED:"enqueued",TASK_CANCELED:"canceled"},eg={INDEX_CREATION_FAILED:"index_creation_failed",MISSING_INDEX_UID:"missing_index_uid",INDEX_ALREADY_EXISTS:"index_already_exists",INDEX_NOT_FOUND:"index_not_found",INVALID_INDEX_UID:"invalid_index_uid",INDEX_NOT_ACCESSIBLE:"index_not_accessible",INVALID_INDEX_OFFSET:"invalid_index_offset",INVALID_INDEX_LIMIT:"invalid_index_limit",INVALID_STATE:"invalid_state",PRIMARY_KEY_INFERENCE_FAILED:"primary_key_inference_failed",INDEX_PRIMARY_KEY_ALREADY_EXISTS:"index_primary_key_already_exists",INVALID_INDEX_PRIMARY_KEY:"invalid_index_primary_key",DOCUMENTS_FIELDS_LIMIT_REACHED:"document_fields_limit_reached",MISSING_DOCUMENT_ID:"missing_document_id",INVALID_DOCUMENT_ID:"invalid_document_id",INVALID_CONTENT_TYPE:"invalid_content_type",MISSING_CONTENT_TYPE:"missing_content_type",INVALID_DOCUMENT_FIELDS:"invalid_document_fields",INVALID_DOCUMENT_LIMIT:"invalid_document_limit",INVALID_DOCUMENT_OFFSET:"invalid_document_offset",INVALID_DOCUMENT_FILTER:"invalid_document_filter",MISSING_DOCUMENT_FILTER:"missing_document_filter",INVALID_DOCUMENT_VECTORS_FIELD:"invalid_document_vectors_field",PAYLOAD_TOO_LARGE:"payload_too_large",MISSING_PAYLOAD:"missing_payload",MALFORMED_PAYLOAD:"malformed_payload",NO_SPACE_LEFT_ON_DEVICE:"no_space_left_on_device",INVALID_STORE_FILE:"invalid_store_file",INVALID_RANKING_RULES:"missing_document_id",INVALID_REQUEST:"invalid_request",INVALID_DOCUMENT_GEO_FIELD:"invalid_document_geo_field",INVALID_SEARCH_Q:"invalid_search_q",INVALID_SEARCH_OFFSET:"invalid_search_offset",INVALID_SEARCH_LIMIT:"invalid_search_limit",INVALID_SEARCH_PAGE:"invalid_search_page",INVALID_SEARCH_HITS_PER_PAGE:"invalid_search_hits_per_page",INVALID_SEARCH_ATTRIBUTES_TO_RETRIEVE:"invalid_search_attributes_to_retrieve",INVALID_SEARCH_ATTRIBUTES_TO_CROP:"invalid_search_attributes_to_crop",INVALID_SEARCH_CROP_LENGTH:"invalid_search_crop_length",INVALID_SEARCH_ATTRIBUTES_TO_HIGHLIGHT:"invalid_search_attributes_to_highlight",INVALID_SEARCH_SHOW_MATCHES_POSITION:"invalid_search_show_matches_position",INVALID_SEARCH_FILTER:"invalid_search_filter",INVALID_SEARCH_SORT:"invalid_search_sort",INVALID_SEARCH_FACETS:"invalid_search_facets",INVALID_SEARCH_HIGHLIGHT_PRE_TAG:"invalid_search_highlight_pre_tag",INVALID_SEARCH_HIGHLIGHT_POST_TAG:"invalid_search_highlight_post_tag",INVALID_SEARCH_CROP_MARKER:"invalid_search_crop_marker",INVALID_SEARCH_MATCHING_STRATEGY:"invalid_search_matching_strategy",INVALID_SEARCH_VECTOR:"invalid_search_vector",INVALID_SEARCH_ATTRIBUTES_TO_SEARCH_ON:"invalid_search_attributes_to_search_on",BAD_REQUEST:"bad_request",DOCUMENT_NOT_FOUND:"document_not_found",INTERNAL:"internal",INVALID_API_KEY:"invalid_api_key",INVALID_API_KEY_DESCRIPTION:"invalid_api_key_description",INVALID_API_KEY_ACTIONS:"invalid_api_key_actions",INVALID_API_KEY_INDEXES:"invalid_api_key_indexes",INVALID_API_KEY_EXPIRES_AT:"invalid_api_key_expires_at",API_KEY_NOT_FOUND:"api_key_not_found",IMMUTABLE_API_KEY_UID:"immutable_api_key_uid",IMMUTABLE_API_KEY_ACTIONS:"immutable_api_key_actions",IMMUTABLE_API_KEY_INDEXES:"immutable_api_key_indexes",IMMUTABLE_API_KEY_EXPIRES_AT:"immutable_api_key_expires_at",IMMUTABLE_API_KEY_CREATED_AT:"immutable_api_key_created_at",IMMUTABLE_API_KEY_UPDATED_AT:"immutable_api_key_updated_at",MISSING_AUTHORIZATION_HEADER:"missing_authorization_header",UNRETRIEVABLE_DOCUMENT:"unretrievable_document",MAX_DATABASE_SIZE_LIMIT_REACHED:"database_size_limit_reached",TASK_NOT_FOUND:"task_not_found",DUMP_PROCESS_FAILED:"dump_process_failed",DUMP_NOT_FOUND:"dump_not_found",INVALID_SWAP_DUPLICATE_INDEX_FOUND:"invalid_swap_duplicate_index_found",INVALID_SWAP_INDEXES:"invalid_swap_indexes",MISSING_SWAP_INDEXES:"missing_swap_indexes",MISSING_MASTER_KEY:"missing_master_key",INVALID_TASK_TYPES:"invalid_task_types",INVALID_TASK_UIDS:"invalid_task_uids",INVALID_TASK_STATUSES:"invalid_task_statuses",INVALID_TASK_LIMIT:"invalid_task_limit",INVALID_TASK_FROM:"invalid_task_from",INVALID_TASK_CANCELED_BY:"invalid_task_canceled_by",MISSING_TASK_FILTERS:"missing_task_filters",TOO_MANY_OPEN_FILES:"too_many_open_files",IO_ERROR:"io_error",INVALID_TASK_INDEX_UIDS:"invalid_task_index_uids",IMMUTABLE_INDEX_UID:"immutable_index_uid",IMMUTABLE_INDEX_CREATED_AT:"immutable_index_created_at",IMMUTABLE_INDEX_UPDATED_AT:"immutable_index_updated_at",INVALID_SETTINGS_DISPLAYED_ATTRIBUTES:"invalid_settings_displayed_attributes",INVALID_SETTINGS_SEARCHABLE_ATTRIBUTES:"invalid_settings_searchable_attributes",INVALID_SETTINGS_FILTERABLE_ATTRIBUTES:"invalid_settings_filterable_attributes",INVALID_SETTINGS_SORTABLE_ATTRIBUTES:"invalid_settings_sortable_attributes",INVALID_SETTINGS_RANKING_RULES:"invalid_settings_ranking_rules",INVALID_SETTINGS_STOP_WORDS:"invalid_settings_stop_words",INVALID_SETTINGS_SYNONYMS:"invalid_settings_synonyms",INVALID_SETTINGS_DISTINCT_ATTRIBUTE:"invalid_settings_distinct_attribute",INVALID_SETTINGS_TYPO_TOLERANCE:"invalid_settings_typo_tolerance",INVALID_SETTINGS_FACETING:"invalid_settings_faceting",INVALID_SETTINGS_PAGINATION:"invalid_settings_pagination",INVALID_SETTINGS_SEARCH_CUTOFF_MS:"invalid_settings_search_cutoff_ms",INVALID_SETTINGS_LOCALIZED_ATTRIBUTES:"invalid_settings_localized_attributes",INVALID_TASK_BEFORE_ENQUEUED_AT:"invalid_task_before_enqueued_at",INVALID_TASK_AFTER_ENQUEUED_AT:"invalid_task_after_enqueued_at",INVALID_TASK_BEFORE_STARTED_AT:"invalid_task_before_started_at",INVALID_TASK_AFTER_STARTED_AT:"invalid_task_after_started_at",INVALID_TASK_BEFORE_FINISHED_AT:"invalid_task_before_finished_at",INVALID_TASK_AFTER_FINISHED_AT:"invalid_task_after_finished_at",MISSING_API_KEY_ACTIONS:"missing_api_key_actions",MISSING_API_KEY_INDEXES:"missing_api_key_indexes",MISSING_API_KEY_EXPIRES_AT:"missing_api_key_expires_at",INVALID_API_KEY_LIMIT:"invalid_api_key_limit",INVALID_API_KEY_OFFSET:"invalid_api_key_offset",INVALID_FACET_SEARCH_FACET_NAME:"invalid_facet_search_facet_name",MISSING_FACET_SEARCH_FACET_NAME:"missing_facet_search_facet_name",INVALID_FACET_SEARCH_FACET_QUERY:"invalid_facet_search_facet_query",INVALID_SEARCH_RANKING_SCORE_THRESHOLD:"invalid_search_ranking_score_threshold",INVALID_SIMILAR_RANKING_SCORE_THRESHOLD:"invalid_similar_ranking_score_threshold"};function ew(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function em(t,e,n,r,i,s,a){try{var u=t[s](a),o=u.value}catch(t){return void n(t)}u.done?e(o):Promise.resolve(o).then(r,i)}function ex(t){return function(){var e=this,n=arguments;return new Promise(function(r,i){var s=t.apply(e,n);function a(t){em(s,r,i,a,u,"next",t)}function u(t){em(s,r,i,a,u,"throw",t)}a(void 0)})}}function eb(t,e,n){return e=eE(e),function(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,eR()?Reflect.construct(e,n||[],eE(t).constructor):e.apply(t,n))}function ek(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}function eA(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,eP(r.key),r)}}function eI(t,e,n){return e&&eA(t.prototype,e),n&&eA(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function eT(t,e,n){return(e=eP(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function eE(t){return(eE=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function eS(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&eq(t,e)}function eR(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(eR=function(){return!!t})()}function eD(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function eN(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?eD(Object(n),!0).forEach(function(e){eT(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):eD(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function eO(){eO=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(t,e,n){t[e]=n.value},s="function"==typeof Symbol?Symbol:{},a=s.iterator||"@@iterator",u=s.asyncIterator||"@@asyncIterator",o=s.toStringTag||"@@toStringTag";function c(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,n){return t[e]=n}}function p(e,n,r,s){var a,u,o=Object.create((n&&n.prototype instanceof y?n:y).prototype);return i(o,"_invoke",{value:(a=new E(s||[]),u=d,function(n,i){if(u===l)throw Error("Generator is already running");if(u===f){if("throw"===n)throw i;return{value:t,done:!0}}for(a.method=n,a.arg=i;;){var s=a.delegate;if(s){var o=function e(n,r){var i=r.method,s=n.iterator[i];if(s===t)return r.delegate=null,"throw"===i&&n.iterator.return&&(r.method="return",r.arg=t,e(n,r),"throw"===r.method)||"return"!==i&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+i+"' method")),v;var a=h(s,n.iterator,r.arg);if("throw"===a.type)return r.method="throw",r.arg=a.arg,r.delegate=null,v;var u=a.arg;return u?u.done?(r[n.resultName]=u.value,r.next=n.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):u:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,v)}(s,a);if(o){if(o===v)continue;return o}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(u===d)throw u=f,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);u=l;var c=h(e,r,a);if("normal"===c.type){if(u=a.done?f:"suspendedYield",c.arg===v)continue;return{value:c.arg,done:a.done}}"throw"===c.type&&(u=f,a.method="throw",a.arg=c.arg)}})}),o}function h(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=p;var d="suspendedStart",l="executing",f="completed",v={};function y(){}function _(){}function g(){}var w={};c(w,a,function(){return this});var m=Object.getPrototypeOf,x=m&&m(m(S([])));x&&x!==n&&r.call(x,a)&&(w=x);var b=g.prototype=y.prototype=Object.create(w);function k(t){["next","throw","return"].forEach(function(e){c(t,e,function(t){return this._invoke(e,t)})})}function A(t,e){var n;i(this,"_invoke",{value:function(i,s){function a(){return new e(function(n,a){!function n(i,s,a,u){var o=h(t[i],t,s);if("throw"!==o.type){var c=o.arg,p=c.value;return p&&"object"==typeof p&&r.call(p,"__await")?e.resolve(p.__await).then(function(t){n("next",t,a,u)},function(t){n("throw",t,a,u)}):e.resolve(p).then(function(t){c.value=t,a(c)},function(t){return n("throw",t,a,u)})}u(o.arg)}(i,s,n,a)})}return n=n?n.then(a,a):a()}})}function I(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function T(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(I,this),this.reset(!0)}function S(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,s=function n(){for(;++i<e.length;)if(r.call(e,i))return n.value=e[i],n.done=!1,n;return n.value=t,n.done=!0,n};return s.next=s}}throw TypeError(typeof e+" is not iterable")}return _.prototype=g,i(b,"constructor",{value:g,configurable:!0}),i(g,"constructor",{value:_,configurable:!0}),_.displayName=c(g,o,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===_||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,g):(t.__proto__=g,c(t,o,"GeneratorFunction")),t.prototype=Object.create(b),t},e.awrap=function(t){return{__await:t}},k(A.prototype),c(A.prototype,u,function(){return this}),e.AsyncIterator=A,e.async=function(t,n,r,i,s){void 0===s&&(s=Promise);var a=new A(p(t,n,r,i),s);return e.isGeneratorFunction(n)?a:a.next().then(function(t){return t.done?t.value:a.next()})},k(b),c(b,o,"Generator"),c(b,a,function(){return this}),c(b,"toString",function(){return"[object Generator]"}),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=S,E.prototype={constructor:E,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(T),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function i(r,i){return u.type="throw",u.arg=e,n.next=r,i&&(n.method="next",n.arg=t),!!i}for(var s=this.tryEntries.length-1;s>=0;--s){var a=this.tryEntries[s],u=a.completion;if("root"===a.tryLoc)return i("end");if(a.tryLoc<=this.prev){var o=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(o&&c){if(this.prev<a.catchLoc)return i(a.catchLoc,!0);if(this.prev<a.finallyLoc)return i(a.finallyLoc)}else if(o){if(this.prev<a.catchLoc)return i(a.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return i(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var s=i;break}}s&&("break"===t||"continue"===t)&&s.tryLoc<=e&&e<=s.finallyLoc&&(s=null);var a=s?s.completion:{};return a.type=t,a.arg=e,s?(this.method="next",this.next=s.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),T(n),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var i=r.arg;T(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:S(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),v}},e}function eq(t,e){return(eq=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function eP(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}function eL(t){return(eL="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function eC(t,e){if(t){if("string"==typeof t)return ew(t,e);var n=({}).toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ew(t,e):void 0}}function eM(t){var e="function"==typeof Map?new Map:void 0;return(eM=function(t){if(null===t||!function(t){try{return -1!==Function.toString.call(t).indexOf("[native code]")}catch(e){return"function"==typeof t}}(t))return t;if("function"!=typeof t)throw TypeError("Super expression must either be null or a function");if(void 0!==e){if(e.has(t))return e.get(t);e.set(t,n)}function n(){return function(t,e,n){if(eR())return Reflect.construct.apply(null,arguments);var r=[null];r.push.apply(r,e);var i=new(t.bind.apply(t,r));return n&&eq(i,n.prototype),i}(t,arguments,eE(this).constructor)}return n.prototype=Object.create(t.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),eq(n,t)})(t)}var eU=function(t){function e(){var t;ek(this,e);for(var n=arguments.length,r=Array(n),i=0;i<n;i++)r[i]=arguments[i];return eT(t=eb(this,e,[].concat(r)),"name","MeiliSearchError"),t}return eS(e,t),eI(e)}(eM(Error)),eF=function(t){function e(t,n){var r,i;return ek(this,e),eT(i=eb(this,e,[null!==(r=null==n?void 0:n.message)&&void 0!==r?r:"".concat(t.status,": ").concat(t.statusText)]),"name","MeiliSearchApiError"),eT(i,"cause",void 0),eT(i,"response",void 0),i.response=t,void 0!==n&&(i.cause=n),i}return eS(e,t),eI(e)}(eU),ej=function(t){function e(t,n){var r;return ek(this,e),eT(r=eb(this,e,["Request to ".concat(t," has failed"),{cause:n}]),"name","MeiliSearchRequestError"),r}return eS(e,t),eI(e)}(eU),eV=function(t){function e(t){var n;return ek(this,e),eT(n=eb(this,e,[t]),"name","MeiliSearchTimeOutError"),n}return eS(e,t),eI(e)}(eU);function eK(t,e){return"".concat(t,"\nHint: It might not be working because maybe you're not up to date with the Meilisearch version that ").concat(e," call requires.")}function eH(t){return Object.entries(t).reduce(function(t,e){var n=function(t){if(Array.isArray(t))return t}(e)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,s,a,u=[],o=!0,c=!1;try{for(s=(n=n.call(t)).next;!(o=(r=s.call(n)).done)&&(u.push(r.value),2!==u.length);o=!0);}catch(t){c=!0,i=t}finally{try{if(!o&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw i}}return u}}(e,2)||eC(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),r=n[0],i=n[1];return void 0!==i&&(t[r]=i),t},{})}function eG(){return(eG=ex(eO().mark(function t(e){return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,new Promise(function(t){return setTimeout(t,e)});case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}},t)}))).apply(this,arguments)}function eB(t){return Object.keys(t).reduce(function(e,n){var r=t[n];return void 0===r?e:Array.isArray(r)?eN(eN({},e),{},eT({},n,r.join(","))):r instanceof Date?eN(eN({},e),{},eT({},n,r.toISOString())):eN(eN({},e),{},eT({},n,r))},{})}var eY=eI(function t(e){ek(this,t),eT(this,"headers",void 0),eT(this,"url",void 0),eT(this,"requestConfig",void 0),eT(this,"httpClient",void 0),eT(this,"requestTimeout",void 0),this.headers=function(t){var e,n,r="X-Meilisearch-Client",i="Meilisearch JavaScript (v".concat("0.47.0",")"),s="Authorization",a=function(t){if(Array.isArray(t))return t.reduce(function(t,e){return t[e[0]]=e[1],t},{});if(!("has"in t))return Object.assign({},t);var e={};return t.forEach(function(t,n){return e[n]=t}),e}(null!==(e=null===(n=t.requestConfig)||void 0===n?void 0:n.headers)&&void 0!==e?e:{});if(t.apiKey&&!a[s]&&(a[s]="Bearer ".concat(t.apiKey)),a["Content-Type"]||(a["Content-Type"]="application/json"),t.clientAgents&&Array.isArray(t.clientAgents)){var u=t.clientAgents.concat(i);a[r]=u.join(" ; ")}else if(t.clientAgents&&!Array.isArray(t.clientAgents))throw new eU('Meilisearch: The header "'.concat(r,'" should be an array of string(s).\n'));else a[r]=i;return a}(e),this.requestConfig=e.requestConfig,this.httpClient=e.httpClient,this.requestTimeout=e.timeout;try{var n=function(t){try{var e,n;return(n=t=(e=t).startsWith("https://")||e.startsWith("http://")?e:"http://".concat(e)).endsWith("/")||(n+="/"),t=n}catch(t){throw new eU("The provided host is not valid.")}}(e.host);this.url=new URL(n)}catch(t){throw new eU("The provided host is not valid.")}},[{key:"request",value:(e=ex(eO().mark(function t(e){var n,r,i,s,a,u,o,c,p,h,d,l,f,v;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return r=e.method,i=e.url,s=e.params,a=e.body,o=void 0===(u=e.config)?{}:u,c=new URL(i,this.url),s&&(p=new URLSearchParams,Object.keys(s).filter(function(t){return null!==s[t]}).map(function(t){return p.set(t,s[t])}),c.search=p.toString()),null!==(n=o.headers)&&void 0!==n&&n["Content-Type"]||(a=JSON.stringify(a)),h=eN(eN({},this.headers),o.headers),d=this.fetchWithTimeout(c.toString(),eN(eN(eN({},o),this.requestConfig),{},{method:r,body:a,headers:h}),this.requestTimeout),t.next=8,d.catch(function(t){throw new ej(c.toString(),t)});case 8:if(l=t.sent,!(void 0!==this.httpClient)){t.next=11;break}return t.abrupt("return",l);case 11:return t.next=13,l.text();case 13:if(v=""===(f=t.sent)?void 0:JSON.parse(f),l.ok){t.next=17;break}throw new eF(l,v);case 17:return t.abrupt("return",v);case 18:case"end":return t.stop()}},t,this)})),function(t){return e.apply(this,arguments)})},{key:"fetchWithTimeout",value:(n=ex(eO().mark(function t(e,n,r){var i=this;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",new Promise(function(t,s){var a,u=[(i.httpClient?i.httpClient:fetch)(e,n)];if(r){var o=new Promise(function(t,e){a=setTimeout(function(){e(Error("Error: Request Timed Out"))},r)});u.push(o)}Promise.race(u).then(t).catch(s).finally(function(){clearTimeout(a)})}));case 1:case"end":return t.stop()}},t)})),function(t,e,r){return n.apply(this,arguments)})},{key:"get",value:(r=ex(eO().mark(function t(e,n,r){return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.request({method:"GET",url:e,params:n,config:r});case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}},t,this)})),function(t,e,n){return r.apply(this,arguments)})},{key:"post",value:(i=ex(eO().mark(function t(e,n,r,i){return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.request({method:"POST",url:e,body:n,params:r,config:i});case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}},t,this)})),function(t,e,n,r){return i.apply(this,arguments)})},{key:"put",value:(s=ex(eO().mark(function t(e,n,r,i){return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.request({method:"PUT",url:e,body:n,params:r,config:i});case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}},t,this)})),function(t,e,n,r){return s.apply(this,arguments)})},{key:"patch",value:(a=ex(eO().mark(function t(e,n,r,i){return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.request({method:"PATCH",url:e,body:n,params:r,config:i});case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}},t,this)})),function(t,e,n,r){return a.apply(this,arguments)})},{key:"delete",value:(u=ex(eO().mark(function t(e,n,r,i){return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.request({method:"DELETE",url:e,body:n,params:r,config:i});case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}},t,this)})),function(t,e,n,r){return u.apply(this,arguments)})}]),eX=eI(function t(e){ek(this,t),eT(this,"taskUid",void 0),eT(this,"indexUid",void 0),eT(this,"status",void 0),eT(this,"type",void 0),eT(this,"enqueuedAt",void 0),this.taskUid=e.taskUid,this.indexUid=e.indexUid,this.status=e.status,this.type=e.type,this.enqueuedAt=new Date(e.enqueuedAt)}),eW=eI(function t(e){ek(this,t),eT(this,"indexUid",void 0),eT(this,"status",void 0),eT(this,"type",void 0),eT(this,"uid",void 0),eT(this,"batchUid",void 0),eT(this,"canceledBy",void 0),eT(this,"details",void 0),eT(this,"error",void 0),eT(this,"duration",void 0),eT(this,"startedAt",void 0),eT(this,"enqueuedAt",void 0),eT(this,"finishedAt",void 0),this.indexUid=e.indexUid,this.status=e.status,this.type=e.type,this.uid=e.uid,this.batchUid=e.batchUid,this.details=e.details,this.canceledBy=e.canceledBy,this.error=e.error,this.duration=e.duration,this.startedAt=new Date(e.startedAt),this.enqueuedAt=new Date(e.enqueuedAt),this.finishedAt=new Date(e.finishedAt)}),eQ=eI(function t(e){ek(this,t),eT(this,"httpRequest",void 0),this.httpRequest=new eY(e)},[{key:"getTask",value:(o=ex(eO().mark(function t(e){var n,r;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n="tasks/".concat(e),t.next=3,this.httpRequest.get(n);case 3:return r=t.sent,t.abrupt("return",new eW(r));case 5:case"end":return t.stop()}},t,this)})),function(t){return o.apply(this,arguments)})},{key:"getTasks",value:(c=ex(eO().mark(function t(){var e,n,r,i=arguments;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e=i.length>0&&void 0!==i[0]?i[0]:{},n="tasks",t.next=4,this.httpRequest.get(n,eB(e));case 4:return r=t.sent,t.abrupt("return",eN(eN({},r),{},{results:r.results.map(function(t){return new eW(t)})}));case 6:case"end":return t.stop()}},t,this)})),function(){return c.apply(this,arguments)})},{key:"waitForTask",value:(p=ex(eO().mark(function t(e){var n,r,i,s,a,u,o,c=arguments;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:i=void 0===(r=(n=c.length>1&&void 0!==c[1]?c[1]:{}).timeOutMs)?5e3:r,a=void 0===(s=n.intervalMs)?50:s,u=Date.now();case 2:if(!(Date.now()-u<i)){t.next=12;break}return t.next=5,this.getTask(e);case 5:if(o=t.sent,[e_.TASK_ENQUEUED,e_.TASK_PROCESSING].includes(o.status)){t.next=8;break}return t.abrupt("return",o);case 8:return t.next=10,function(t){return eG.apply(this,arguments)}(a);case 10:t.next=2;break;case 12:throw new eV("timeout of ".concat(i,"ms has exceeded on process ").concat(e," when waiting a task to be resolved."));case 13:case"end":return t.stop()}},t,this)})),function(t){return p.apply(this,arguments)})},{key:"waitForTasks",value:(h=ex(eO().mark(function t(e){var n,r,i,s,a,u,o,c,p,h,d=arguments;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:i=void 0===(r=(n=d.length>1&&void 0!==d[1]?d[1]:{}).timeOutMs)?5e3:r,a=void 0===(s=n.intervalMs)?50:s,u=[],o=function(t,e){var n="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!n){if(Array.isArray(t)||(n=eC(t))){n&&(t=n);var r=0,i=function(){};return{s:i,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:i}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,a=!0,u=!1;return{s:function(){n=n.call(t)},n:function(){var t=n.next();return a=t.done,t},e:function(t){u=!0,s=t},f:function(){try{a||null==n.return||n.return()}finally{if(u)throw s}}}}(e),t.prev=3,o.s();case 5:if((c=o.n()).done){t.next=13;break}return p=c.value,t.next=9,this.waitForTask(p,{timeOutMs:i,intervalMs:a});case 9:h=t.sent,u.push(h);case 11:t.next=5;break;case 13:t.next=18;break;case 15:t.prev=15,t.t0=t.catch(3),o.e(t.t0);case 18:return t.prev=18,o.f(),t.finish(18);case 21:return t.abrupt("return",u);case 22:case"end":return t.stop()}},t,this,[[3,15,18,21]])})),function(t){return h.apply(this,arguments)})},{key:"cancelTasks",value:(d=ex(eO().mark(function t(){var e,n,r,i=arguments;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e=i.length>0&&void 0!==i[0]?i[0]:{},n="tasks/cancel",t.next=4,this.httpRequest.post(n,{},eB(e));case 4:return r=t.sent,t.abrupt("return",new eX(r));case 6:case"end":return t.stop()}},t,this)})),function(){return d.apply(this,arguments)})},{key:"deleteTasks",value:(l=ex(eO().mark(function t(){var e,n,r,i=arguments;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e=i.length>0&&void 0!==i[0]?i[0]:{},n="tasks",t.next=4,this.httpRequest.delete(n,{},eB(e));case 4:return r=t.sent,t.abrupt("return",new eX(r));case 6:case"end":return t.stop()}},t,this)})),function(){return l.apply(this,arguments)})}]),ez=eI(function t(e,n,r){ek(this,t),eT(this,"uid",void 0),eT(this,"primaryKey",void 0),eT(this,"createdAt",void 0),eT(this,"updatedAt",void 0),eT(this,"httpRequest",void 0),eT(this,"tasks",void 0),this.uid=n,this.primaryKey=r,this.httpRequest=new eY(e),this.tasks=new eQ(e)},[{key:"search",value:(f=ex(eO().mark(function t(e,n,r){var i;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return i="indexes/".concat(this.uid,"/search"),t.next=3,this.httpRequest.post(i,eH(eN({q:e},n)),void 0,r);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(t,e,n){return f.apply(this,arguments)})},{key:"searchGet",value:(v=ex(eO().mark(function t(e,n,r){var i,s,a,u,o,c,p,h,d,l;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return h="indexes/".concat(this.uid,"/search"),d=function(t){if("string"==typeof t)return t;if(Array.isArray(t))throw new eU("The filter query parameter should be in string format when using searchGet")},l=eN(eN({q:e},n),{},{filter:d(null==n?void 0:n.filter),sort:null==n||null===(i=n.sort)||void 0===i?void 0:i.join(","),facets:null==n||null===(s=n.facets)||void 0===s?void 0:s.join(","),attributesToRetrieve:null==n||null===(a=n.attributesToRetrieve)||void 0===a?void 0:a.join(","),attributesToCrop:null==n||null===(u=n.attributesToCrop)||void 0===u?void 0:u.join(","),attributesToHighlight:null==n||null===(o=n.attributesToHighlight)||void 0===o?void 0:o.join(","),vector:null==n||null===(c=n.vector)||void 0===c?void 0:c.join(","),attributesToSearchOn:null==n||null===(p=n.attributesToSearchOn)||void 0===p?void 0:p.join(",")}),t.next=5,this.httpRequest.get(h,eH(l),r);case 5:return t.abrupt("return",t.sent);case 6:case"end":return t.stop()}},t,this)})),function(t,e,n){return v.apply(this,arguments)})},{key:"searchForFacetValues",value:(y=ex(eO().mark(function t(e,n){var r;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return r="indexes/".concat(this.uid,"/facet-search"),t.next=3,this.httpRequest.post(r,eH(e),void 0,n);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(t,e){return y.apply(this,arguments)})},{key:"searchSimilarDocuments",value:(_=ex(eO().mark(function t(e){var n;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n="indexes/".concat(this.uid,"/similar"),t.next=3,this.httpRequest.post(n,eH(e),void 0);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(t){return _.apply(this,arguments)})},{key:"getRawInfo",value:(g=ex(eO().mark(function t(){var e,n;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid),t.next=3,this.httpRequest.get(e);case 3:return n=t.sent,this.primaryKey=n.primaryKey,this.updatedAt=new Date(n.updatedAt),this.createdAt=new Date(n.createdAt),t.abrupt("return",n);case 8:case"end":return t.stop()}},t,this)})),function(){return g.apply(this,arguments)})},{key:"fetchInfo",value:(w=ex(eO().mark(function t(){return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.getRawInfo();case 2:return t.abrupt("return",this);case 3:case"end":return t.stop()}},t,this)})),function(){return w.apply(this,arguments)})},{key:"fetchPrimaryKey",value:(m=ex(eO().mark(function t(){return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.getRawInfo();case 2:return this.primaryKey=t.sent.primaryKey,t.abrupt("return",this.primaryKey);case 4:case"end":return t.stop()}},t,this)})),function(){return m.apply(this,arguments)})},{key:"update",value:(x=ex(eO().mark(function t(e){var n,r;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n="indexes/".concat(this.uid),t.next=3,this.httpRequest.patch(n,e);case 3:return(r=t.sent).enqueuedAt=new Date(r.enqueuedAt),t.abrupt("return",r);case 6:case"end":return t.stop()}},t,this)})),function(t){return x.apply(this,arguments)})},{key:"delete",value:(b=ex(eO().mark(function t(){var e,n;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid),t.next=3,this.httpRequest.delete(e);case 3:return n=t.sent,t.abrupt("return",new eX(n));case 5:case"end":return t.stop()}},t,this)})),function(){return b.apply(this,arguments)})},{key:"getTasks",value:(k=ex(eO().mark(function t(){var e,n=arguments;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e=n.length>0&&void 0!==n[0]?n[0]:{},t.next=3,this.tasks.getTasks(eN(eN({},e),{},{indexUids:[this.uid]}));case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(){return k.apply(this,arguments)})},{key:"getTask",value:(A=ex(eO().mark(function t(e){return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.tasks.getTask(e);case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}},t,this)})),function(t){return A.apply(this,arguments)})},{key:"waitForTasks",value:(I=ex(eO().mark(function t(e){var n,r,i,s,a,u=arguments;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return i=void 0===(r=(n=u.length>1&&void 0!==u[1]?u[1]:{}).timeOutMs)?5e3:r,a=void 0===(s=n.intervalMs)?50:s,t.next=3,this.tasks.waitForTasks(e,{timeOutMs:i,intervalMs:a});case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(t){return I.apply(this,arguments)})},{key:"waitForTask",value:(T=ex(eO().mark(function t(e){var n,r,i,s,a,u=arguments;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return i=void 0===(r=(n=u.length>1&&void 0!==u[1]?u[1]:{}).timeOutMs)?5e3:r,a=void 0===(s=n.intervalMs)?50:s,t.next=3,this.tasks.waitForTask(e,{timeOutMs:i,intervalMs:a});case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(t){return T.apply(this,arguments)})},{key:"getStats",value:(E=ex(eO().mark(function t(){var e;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/stats"),t.next=3,this.httpRequest.get(e);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(){return E.apply(this,arguments)})},{key:"getDocuments",value:(S=ex(eO().mark(function t(){var e,n,r,i,s,a,u=arguments;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(!(void 0!==(e=eH(e=u.length>0&&void 0!==u[0]?u[0]:{})).filter)){t.next=16;break}return t.prev=3,n="indexes/".concat(this.uid,"/documents/fetch"),t.next=7,this.httpRequest.post(n,e);case 7:case 20:return t.abrupt("return",t.sent);case 10:throw t.prev=10,t.t0=t.catch(3),t.t0 instanceof ej?t.t0.message=eK(t.t0.message,"getDocuments"):t.t0 instanceof eF&&(t.t0.message=eK(t.t0.message,"getDocuments")),t.t0;case 14:t.next=21;break;case 16:return s="indexes/".concat(this.uid,"/documents"),a=Array.isArray(null===(r=e)||void 0===r?void 0:r.fields)?{fields:null===(i=e)||void 0===i||null===(i=i.fields)||void 0===i?void 0:i.join(",")}:{},t.next=20,this.httpRequest.get(s,eN(eN({},e),a));case 21:case"end":return t.stop()}},t,this,[[3,10]])})),function(){return S.apply(this,arguments)})},{key:"getDocument",value:(R=ex(eO().mark(function t(e,n){var r,i;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return r="indexes/".concat(this.uid,"/documents/").concat(e),i=function(){if(Array.isArray(null==n?void 0:n.fields)){var t;return null==n||null===(t=n.fields)||void 0===t?void 0:t.join(",")}}(),t.next=4,this.httpRequest.get(r,eH(eN(eN({},n),{},{fields:i})));case 4:return t.abrupt("return",t.sent);case 5:case"end":return t.stop()}},t,this)})),function(t,e){return R.apply(this,arguments)})},{key:"addDocuments",value:(D=ex(eO().mark(function t(e,n){var r,i;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return r="indexes/".concat(this.uid,"/documents"),t.next=3,this.httpRequest.post(r,e,n);case 3:return i=t.sent,t.abrupt("return",new eX(i));case 5:case"end":return t.stop()}},t,this)})),function(t,e){return D.apply(this,arguments)})},{key:"addDocumentsFromString",value:(N=ex(eO().mark(function t(e,n,r){var i,s;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return i="indexes/".concat(this.uid,"/documents"),t.next=3,this.httpRequest.post(i,e,r,{headers:{"Content-Type":n}});case 3:return s=t.sent,t.abrupt("return",new eX(s));case 5:case"end":return t.stop()}},t,this)})),function(t,e,n){return N.apply(this,arguments)})},{key:"addDocumentsInBatches",value:(O=ex(eO().mark(function t(e){var n,r,i,s,a=arguments;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:n=a.length>1&&void 0!==a[1]?a[1]:1e3,r=a.length>2?a[2]:void 0,i=[],s=0;case 4:if(!(s<e.length)){t.next=13;break}return t.t0=i,t.next=8,this.addDocuments(e.slice(s,s+n),r);case 8:t.t1=t.sent,t.t0.push.call(t.t0,t.t1);case 10:s+=n,t.next=4;break;case 13:return t.abrupt("return",i);case 14:case"end":return t.stop()}},t,this)})),function(t){return O.apply(this,arguments)})},{key:"updateDocuments",value:(q=ex(eO().mark(function t(e,n){var r,i;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return r="indexes/".concat(this.uid,"/documents"),t.next=3,this.httpRequest.put(r,e,n);case 3:return i=t.sent,t.abrupt("return",new eX(i));case 5:case"end":return t.stop()}},t,this)})),function(t,e){return q.apply(this,arguments)})},{key:"updateDocumentsInBatches",value:(P=ex(eO().mark(function t(e){var n,r,i,s,a=arguments;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:n=a.length>1&&void 0!==a[1]?a[1]:1e3,r=a.length>2?a[2]:void 0,i=[],s=0;case 4:if(!(s<e.length)){t.next=13;break}return t.t0=i,t.next=8,this.updateDocuments(e.slice(s,s+n),r);case 8:t.t1=t.sent,t.t0.push.call(t.t0,t.t1);case 10:s+=n,t.next=4;break;case 13:return t.abrupt("return",i);case 14:case"end":return t.stop()}},t,this)})),function(t){return P.apply(this,arguments)})},{key:"updateDocumentsFromString",value:(L=ex(eO().mark(function t(e,n,r){var i,s;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return i="indexes/".concat(this.uid,"/documents"),t.next=3,this.httpRequest.put(i,e,r,{headers:{"Content-Type":n}});case 3:return s=t.sent,t.abrupt("return",new eX(s));case 5:case"end":return t.stop()}},t,this)})),function(t,e,n){return L.apply(this,arguments)})},{key:"deleteDocument",value:(C=ex(eO().mark(function t(e){var n,r;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n="indexes/".concat(this.uid,"/documents/").concat(e),t.next=3,this.httpRequest.delete(n);case 3:return(r=t.sent).enqueuedAt=new Date(r.enqueuedAt),t.abrupt("return",r);case 6:case"end":return t.stop()}},t,this)})),function(t){return C.apply(this,arguments)})},{key:"deleteDocuments",value:(M=ex(eO().mark(function t(e){var n,r,i,s;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return r=(n=!Array.isArray(e)&&"object"===eL(e))?"documents/delete":"documents/delete-batch",i="indexes/".concat(this.uid,"/").concat(r),t.prev=3,t.next=6,this.httpRequest.post(i,e);case 6:return s=t.sent,t.abrupt("return",new eX(s));case 10:throw t.prev=10,t.t0=t.catch(3),t.t0 instanceof ej&&n?t.t0.message=eK(t.t0.message,"deleteDocuments"):t.t0 instanceof eF&&(t.t0.message=eK(t.t0.message,"deleteDocuments")),t.t0;case 14:case"end":return t.stop()}},t,this,[[3,10]])})),function(t){return M.apply(this,arguments)})},{key:"deleteAllDocuments",value:(U=ex(eO().mark(function t(){var e,n;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/documents"),t.next=3,this.httpRequest.delete(e);case 3:return(n=t.sent).enqueuedAt=new Date(n.enqueuedAt),t.abrupt("return",n);case 6:case"end":return t.stop()}},t,this)})),function(){return U.apply(this,arguments)})},{key:"updateDocumentsByFunction",value:(F=ex(eO().mark(function t(e){var n,r;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n="indexes/".concat(this.uid,"/documents/edit"),t.next=3,this.httpRequest.post(n,e);case 3:return r=t.sent,t.abrupt("return",new eX(r));case 5:case"end":return t.stop()}},t,this)})),function(t){return F.apply(this,arguments)})},{key:"getSettings",value:(j=ex(eO().mark(function t(){var e;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings"),t.next=3,this.httpRequest.get(e);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(){return j.apply(this,arguments)})},{key:"updateSettings",value:(V=ex(eO().mark(function t(e){var n,r;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n="indexes/".concat(this.uid,"/settings"),t.next=3,this.httpRequest.patch(n,e);case 3:return(r=t.sent).enqueued=new Date(r.enqueuedAt),t.abrupt("return",r);case 6:case"end":return t.stop()}},t,this)})),function(t){return V.apply(this,arguments)})},{key:"resetSettings",value:(K=ex(eO().mark(function t(){var e,n;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings"),t.next=3,this.httpRequest.delete(e);case 3:return(n=t.sent).enqueuedAt=new Date(n.enqueuedAt),t.abrupt("return",n);case 6:case"end":return t.stop()}},t,this)})),function(){return K.apply(this,arguments)})},{key:"getPagination",value:(H=ex(eO().mark(function t(){var e;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/pagination"),t.next=3,this.httpRequest.get(e);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(){return H.apply(this,arguments)})},{key:"updatePagination",value:(G=ex(eO().mark(function t(e){var n,r;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n="indexes/".concat(this.uid,"/settings/pagination"),t.next=3,this.httpRequest.patch(n,e);case 3:return r=t.sent,t.abrupt("return",new eX(r));case 5:case"end":return t.stop()}},t,this)})),function(t){return G.apply(this,arguments)})},{key:"resetPagination",value:(B=ex(eO().mark(function t(){var e,n;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/pagination"),t.next=3,this.httpRequest.delete(e);case 3:return n=t.sent,t.abrupt("return",new eX(n));case 5:case"end":return t.stop()}},t,this)})),function(){return B.apply(this,arguments)})},{key:"getSynonyms",value:(Y=ex(eO().mark(function t(){var e;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/synonyms"),t.next=3,this.httpRequest.get(e);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(){return Y.apply(this,arguments)})},{key:"updateSynonyms",value:(X=ex(eO().mark(function t(e){var n,r;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n="indexes/".concat(this.uid,"/settings/synonyms"),t.next=3,this.httpRequest.put(n,e);case 3:return r=t.sent,t.abrupt("return",new eX(r));case 5:case"end":return t.stop()}},t,this)})),function(t){return X.apply(this,arguments)})},{key:"resetSynonyms",value:(W=ex(eO().mark(function t(){var e,n;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/synonyms"),t.next=3,this.httpRequest.delete(e);case 3:return(n=t.sent).enqueuedAt=new Date(n.enqueuedAt),t.abrupt("return",n);case 6:case"end":return t.stop()}},t,this)})),function(){return W.apply(this,arguments)})},{key:"getStopWords",value:(Q=ex(eO().mark(function t(){var e;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/stop-words"),t.next=3,this.httpRequest.get(e);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(){return Q.apply(this,arguments)})},{key:"updateStopWords",value:(z=ex(eO().mark(function t(e){var n,r;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n="indexes/".concat(this.uid,"/settings/stop-words"),t.next=3,this.httpRequest.put(n,e);case 3:return r=t.sent,t.abrupt("return",new eX(r));case 5:case"end":return t.stop()}},t,this)})),function(t){return z.apply(this,arguments)})},{key:"resetStopWords",value:(J=ex(eO().mark(function t(){var e,n;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/stop-words"),t.next=3,this.httpRequest.delete(e);case 3:return(n=t.sent).enqueuedAt=new Date(n.enqueuedAt),t.abrupt("return",n);case 6:case"end":return t.stop()}},t,this)})),function(){return J.apply(this,arguments)})},{key:"getRankingRules",value:(Z=ex(eO().mark(function t(){var e;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/ranking-rules"),t.next=3,this.httpRequest.get(e);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(){return Z.apply(this,arguments)})},{key:"updateRankingRules",value:($=ex(eO().mark(function t(e){var n,r;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n="indexes/".concat(this.uid,"/settings/ranking-rules"),t.next=3,this.httpRequest.put(n,e);case 3:return r=t.sent,t.abrupt("return",new eX(r));case 5:case"end":return t.stop()}},t,this)})),function(t){return $.apply(this,arguments)})},{key:"resetRankingRules",value:(tt=ex(eO().mark(function t(){var e,n;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/ranking-rules"),t.next=3,this.httpRequest.delete(e);case 3:return(n=t.sent).enqueuedAt=new Date(n.enqueuedAt),t.abrupt("return",n);case 6:case"end":return t.stop()}},t,this)})),function(){return tt.apply(this,arguments)})},{key:"getDistinctAttribute",value:(te=ex(eO().mark(function t(){var e;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/distinct-attribute"),t.next=3,this.httpRequest.get(e);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(){return te.apply(this,arguments)})},{key:"updateDistinctAttribute",value:(tn=ex(eO().mark(function t(e){var n,r;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n="indexes/".concat(this.uid,"/settings/distinct-attribute"),t.next=3,this.httpRequest.put(n,e);case 3:return r=t.sent,t.abrupt("return",new eX(r));case 5:case"end":return t.stop()}},t,this)})),function(t){return tn.apply(this,arguments)})},{key:"resetDistinctAttribute",value:(tr=ex(eO().mark(function t(){var e,n;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/distinct-attribute"),t.next=3,this.httpRequest.delete(e);case 3:return(n=t.sent).enqueuedAt=new Date(n.enqueuedAt),t.abrupt("return",n);case 6:case"end":return t.stop()}},t,this)})),function(){return tr.apply(this,arguments)})},{key:"getFilterableAttributes",value:(ti=ex(eO().mark(function t(){var e;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/filterable-attributes"),t.next=3,this.httpRequest.get(e);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(){return ti.apply(this,arguments)})},{key:"updateFilterableAttributes",value:(ts=ex(eO().mark(function t(e){var n,r;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n="indexes/".concat(this.uid,"/settings/filterable-attributes"),t.next=3,this.httpRequest.put(n,e);case 3:return r=t.sent,t.abrupt("return",new eX(r));case 5:case"end":return t.stop()}},t,this)})),function(t){return ts.apply(this,arguments)})},{key:"resetFilterableAttributes",value:(ta=ex(eO().mark(function t(){var e,n;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/filterable-attributes"),t.next=3,this.httpRequest.delete(e);case 3:return(n=t.sent).enqueuedAt=new Date(n.enqueuedAt),t.abrupt("return",n);case 6:case"end":return t.stop()}},t,this)})),function(){return ta.apply(this,arguments)})},{key:"getSortableAttributes",value:(tu=ex(eO().mark(function t(){var e;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/sortable-attributes"),t.next=3,this.httpRequest.get(e);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(){return tu.apply(this,arguments)})},{key:"updateSortableAttributes",value:(to=ex(eO().mark(function t(e){var n,r;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n="indexes/".concat(this.uid,"/settings/sortable-attributes"),t.next=3,this.httpRequest.put(n,e);case 3:return r=t.sent,t.abrupt("return",new eX(r));case 5:case"end":return t.stop()}},t,this)})),function(t){return to.apply(this,arguments)})},{key:"resetSortableAttributes",value:(tc=ex(eO().mark(function t(){var e,n;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/sortable-attributes"),t.next=3,this.httpRequest.delete(e);case 3:return(n=t.sent).enqueuedAt=new Date(n.enqueuedAt),t.abrupt("return",n);case 6:case"end":return t.stop()}},t,this)})),function(){return tc.apply(this,arguments)})},{key:"getSearchableAttributes",value:(tp=ex(eO().mark(function t(){var e;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/searchable-attributes"),t.next=3,this.httpRequest.get(e);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(){return tp.apply(this,arguments)})},{key:"updateSearchableAttributes",value:(th=ex(eO().mark(function t(e){var n,r;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n="indexes/".concat(this.uid,"/settings/searchable-attributes"),t.next=3,this.httpRequest.put(n,e);case 3:return r=t.sent,t.abrupt("return",new eX(r));case 5:case"end":return t.stop()}},t,this)})),function(t){return th.apply(this,arguments)})},{key:"resetSearchableAttributes",value:(td=ex(eO().mark(function t(){var e,n;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/searchable-attributes"),t.next=3,this.httpRequest.delete(e);case 3:return(n=t.sent).enqueuedAt=new Date(n.enqueuedAt),t.abrupt("return",n);case 6:case"end":return t.stop()}},t,this)})),function(){return td.apply(this,arguments)})},{key:"getDisplayedAttributes",value:(tl=ex(eO().mark(function t(){var e;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/displayed-attributes"),t.next=3,this.httpRequest.get(e);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(){return tl.apply(this,arguments)})},{key:"updateDisplayedAttributes",value:(tf=ex(eO().mark(function t(e){var n,r;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n="indexes/".concat(this.uid,"/settings/displayed-attributes"),t.next=3,this.httpRequest.put(n,e);case 3:return r=t.sent,t.abrupt("return",new eX(r));case 5:case"end":return t.stop()}},t,this)})),function(t){return tf.apply(this,arguments)})},{key:"resetDisplayedAttributes",value:(tv=ex(eO().mark(function t(){var e,n;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/displayed-attributes"),t.next=3,this.httpRequest.delete(e);case 3:return(n=t.sent).enqueuedAt=new Date(n.enqueuedAt),t.abrupt("return",n);case 6:case"end":return t.stop()}},t,this)})),function(){return tv.apply(this,arguments)})},{key:"getTypoTolerance",value:(ty=ex(eO().mark(function t(){var e;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/typo-tolerance"),t.next=3,this.httpRequest.get(e);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(){return ty.apply(this,arguments)})},{key:"updateTypoTolerance",value:(t_=ex(eO().mark(function t(e){var n,r;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n="indexes/".concat(this.uid,"/settings/typo-tolerance"),t.next=3,this.httpRequest.patch(n,e);case 3:return(r=t.sent).enqueuedAt=new Date(r.enqueuedAt),t.abrupt("return",r);case 6:case"end":return t.stop()}},t,this)})),function(t){return t_.apply(this,arguments)})},{key:"resetTypoTolerance",value:(tg=ex(eO().mark(function t(){var e,n;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/typo-tolerance"),t.next=3,this.httpRequest.delete(e);case 3:return(n=t.sent).enqueuedAt=new Date(n.enqueuedAt),t.abrupt("return",n);case 6:case"end":return t.stop()}},t,this)})),function(){return tg.apply(this,arguments)})},{key:"getFaceting",value:(tw=ex(eO().mark(function t(){var e;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/faceting"),t.next=3,this.httpRequest.get(e);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(){return tw.apply(this,arguments)})},{key:"updateFaceting",value:(tm=ex(eO().mark(function t(e){var n,r;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n="indexes/".concat(this.uid,"/settings/faceting"),t.next=3,this.httpRequest.patch(n,e);case 3:return r=t.sent,t.abrupt("return",new eX(r));case 5:case"end":return t.stop()}},t,this)})),function(t){return tm.apply(this,arguments)})},{key:"resetFaceting",value:(tx=ex(eO().mark(function t(){var e,n;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/faceting"),t.next=3,this.httpRequest.delete(e);case 3:return n=t.sent,t.abrupt("return",new eX(n));case 5:case"end":return t.stop()}},t,this)})),function(){return tx.apply(this,arguments)})},{key:"getSeparatorTokens",value:(tb=ex(eO().mark(function t(){var e;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/separator-tokens"),t.next=3,this.httpRequest.get(e);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(){return tb.apply(this,arguments)})},{key:"updateSeparatorTokens",value:(tk=ex(eO().mark(function t(e){var n,r;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n="indexes/".concat(this.uid,"/settings/separator-tokens"),t.next=3,this.httpRequest.put(n,e);case 3:return r=t.sent,t.abrupt("return",new eX(r));case 5:case"end":return t.stop()}},t,this)})),function(t){return tk.apply(this,arguments)})},{key:"resetSeparatorTokens",value:(tA=ex(eO().mark(function t(){var e,n;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/separator-tokens"),t.next=3,this.httpRequest.delete(e);case 3:return(n=t.sent).enqueuedAt=new Date(n.enqueuedAt),t.abrupt("return",n);case 6:case"end":return t.stop()}},t,this)})),function(){return tA.apply(this,arguments)})},{key:"getNonSeparatorTokens",value:(tI=ex(eO().mark(function t(){var e;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/non-separator-tokens"),t.next=3,this.httpRequest.get(e);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(){return tI.apply(this,arguments)})},{key:"updateNonSeparatorTokens",value:(tT=ex(eO().mark(function t(e){var n,r;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n="indexes/".concat(this.uid,"/settings/non-separator-tokens"),t.next=3,this.httpRequest.put(n,e);case 3:return r=t.sent,t.abrupt("return",new eX(r));case 5:case"end":return t.stop()}},t,this)})),function(t){return tT.apply(this,arguments)})},{key:"resetNonSeparatorTokens",value:(tE=ex(eO().mark(function t(){var e,n;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/non-separator-tokens"),t.next=3,this.httpRequest.delete(e);case 3:return(n=t.sent).enqueuedAt=new Date(n.enqueuedAt),t.abrupt("return",n);case 6:case"end":return t.stop()}},t,this)})),function(){return tE.apply(this,arguments)})},{key:"getDictionary",value:(tS=ex(eO().mark(function t(){var e;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/dictionary"),t.next=3,this.httpRequest.get(e);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(){return tS.apply(this,arguments)})},{key:"updateDictionary",value:(tR=ex(eO().mark(function t(e){var n,r;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n="indexes/".concat(this.uid,"/settings/dictionary"),t.next=3,this.httpRequest.put(n,e);case 3:return r=t.sent,t.abrupt("return",new eX(r));case 5:case"end":return t.stop()}},t,this)})),function(t){return tR.apply(this,arguments)})},{key:"resetDictionary",value:(tD=ex(eO().mark(function t(){var e,n;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/dictionary"),t.next=3,this.httpRequest.delete(e);case 3:return(n=t.sent).enqueuedAt=new Date(n.enqueuedAt),t.abrupt("return",n);case 6:case"end":return t.stop()}},t,this)})),function(){return tD.apply(this,arguments)})},{key:"getProximityPrecision",value:(tN=ex(eO().mark(function t(){var e;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/proximity-precision"),t.next=3,this.httpRequest.get(e);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(){return tN.apply(this,arguments)})},{key:"updateProximityPrecision",value:(tO=ex(eO().mark(function t(e){var n,r;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n="indexes/".concat(this.uid,"/settings/proximity-precision"),t.next=3,this.httpRequest.put(n,e);case 3:return r=t.sent,t.abrupt("return",new eX(r));case 5:case"end":return t.stop()}},t,this)})),function(t){return tO.apply(this,arguments)})},{key:"resetProximityPrecision",value:(tq=ex(eO().mark(function t(){var e,n;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/proximity-precision"),t.next=3,this.httpRequest.delete(e);case 3:return(n=t.sent).enqueuedAt=new Date(n.enqueuedAt),t.abrupt("return",n);case 6:case"end":return t.stop()}},t,this)})),function(){return tq.apply(this,arguments)})},{key:"getEmbedders",value:(tP=ex(eO().mark(function t(){var e;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/embedders"),t.next=3,this.httpRequest.get(e);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(){return tP.apply(this,arguments)})},{key:"updateEmbedders",value:(tL=ex(eO().mark(function t(e){var n,r;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n="indexes/".concat(this.uid,"/settings/embedders"),t.next=3,this.httpRequest.patch(n,e);case 3:return r=t.sent,t.abrupt("return",new eX(r));case 5:case"end":return t.stop()}},t,this)})),function(t){return tL.apply(this,arguments)})},{key:"resetEmbedders",value:(tC=ex(eO().mark(function t(){var e,n;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/embedders"),t.next=3,this.httpRequest.delete(e);case 3:return(n=t.sent).enqueuedAt=new Date(n.enqueuedAt),t.abrupt("return",n);case 6:case"end":return t.stop()}},t,this)})),function(){return tC.apply(this,arguments)})},{key:"getSearchCutoffMs",value:(tM=ex(eO().mark(function t(){var e;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/search-cutoff-ms"),t.next=3,this.httpRequest.get(e);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(){return tM.apply(this,arguments)})},{key:"updateSearchCutoffMs",value:(tU=ex(eO().mark(function t(e){var n,r;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n="indexes/".concat(this.uid,"/settings/search-cutoff-ms"),t.next=3,this.httpRequest.put(n,e);case 3:return r=t.sent,t.abrupt("return",new eX(r));case 5:case"end":return t.stop()}},t,this)})),function(t){return tU.apply(this,arguments)})},{key:"resetSearchCutoffMs",value:(tF=ex(eO().mark(function t(){var e,n;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/search-cutoff-ms"),t.next=3,this.httpRequest.delete(e);case 3:return n=t.sent,t.abrupt("return",new eX(n));case 5:case"end":return t.stop()}},t,this)})),function(){return tF.apply(this,arguments)})},{key:"getLocalizedAttributes",value:(tj=ex(eO().mark(function t(){var e;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/localized-attributes"),t.next=3,this.httpRequest.get(e);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(){return tj.apply(this,arguments)})},{key:"updateLocalizedAttributes",value:(tV=ex(eO().mark(function t(e){var n,r;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n="indexes/".concat(this.uid,"/settings/localized-attributes"),t.next=3,this.httpRequest.put(n,e);case 3:return r=t.sent,t.abrupt("return",new eX(r));case 5:case"end":return t.stop()}},t,this)})),function(t){return tV.apply(this,arguments)})},{key:"resetLocalizedAttributes",value:(tK=ex(eO().mark(function t(){var e,n;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/localized-attributes"),t.next=3,this.httpRequest.delete(e);case 3:return n=t.sent,t.abrupt("return",new eX(n));case 5:case"end":return t.stop()}},t,this)})),function(){return tK.apply(this,arguments)})},{key:"getFacetSearch",value:(tH=ex(eO().mark(function t(){var e;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/facet-search"),t.next=3,this.httpRequest.get(e);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(){return tH.apply(this,arguments)})},{key:"updateFacetSearch",value:(tG=ex(eO().mark(function t(e){var n,r;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n="indexes/".concat(this.uid,"/settings/facet-search"),t.next=3,this.httpRequest.put(n,e);case 3:return r=t.sent,t.abrupt("return",new eX(r));case 5:case"end":return t.stop()}},t,this)})),function(t){return tG.apply(this,arguments)})},{key:"resetFacetSearch",value:(tB=ex(eO().mark(function t(){var e,n;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/facet-search"),t.next=3,this.httpRequest.delete(e);case 3:return n=t.sent,t.abrupt("return",new eX(n));case 5:case"end":return t.stop()}},t,this)})),function(){return tB.apply(this,arguments)})},{key:"getPrefixSearch",value:(tY=ex(eO().mark(function t(){var e;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/prefix-search"),t.next=3,this.httpRequest.get(e);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(){return tY.apply(this,arguments)})},{key:"updatePrefixSearch",value:(tX=ex(eO().mark(function t(e){var n,r;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n="indexes/".concat(this.uid,"/settings/prefix-search"),t.next=3,this.httpRequest.put(n,e);case 3:return r=t.sent,t.abrupt("return",new eX(r));case 5:case"end":return t.stop()}},t,this)})),function(t){return tX.apply(this,arguments)})},{key:"resetPrefixSearch",value:(tW=ex(eO().mark(function t(){var e,n;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="indexes/".concat(this.uid,"/settings/prefix-search"),t.next=3,this.httpRequest.delete(e);case 3:return n=t.sent,t.abrupt("return",new eX(n));case 5:case"end":return t.stop()}},t,this)})),function(){return tW.apply(this,arguments)})}],[{key:"create",value:(tQ=ex(eO().mark(function t(e){var n,r,i,s,a,u=arguments;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n=u.length>1&&void 0!==u[1]?u[1]:{},r=u.length>2?u[2]:void 0,i="indexes",s=new eY(r),t.next=6,s.post(i,eN(eN({},n),{},{uid:e}));case 6:return a=t.sent,t.abrupt("return",new eX(a));case 8:case"end":return t.stop()}},t)})),function(t){return tQ.apply(this,arguments)})}]),eJ=eI(function t(e){ek(this,t),eT(this,"uid",void 0),eT(this,"details",void 0),eT(this,"stats",void 0),eT(this,"startedAt",void 0),eT(this,"finishedAt",void 0),eT(this,"duration",void 0),eT(this,"progress",void 0),this.uid=e.uid,this.details=e.details,this.stats=e.stats,this.startedAt=e.startedAt,this.finishedAt=e.finishedAt,this.duration=e.duration,this.progress=e.progress}),eZ=eI(function t(e){ek(this,t),eT(this,"httpRequest",void 0),this.httpRequest=new eY(e)},[{key:"getBatch",value:(tz=ex(eO().mark(function t(e){var n,r;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n="batches/".concat(e),t.next=3,this.httpRequest.get(n);case 3:return r=t.sent,t.abrupt("return",new eJ(r));case 5:case"end":return t.stop()}},t,this)})),function(t){return tz.apply(this,arguments)})},{key:"getBatches",value:(tJ=ex(eO().mark(function t(){var e,n,r,i=arguments;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e=i.length>0&&void 0!==i[0]?i[0]:{},n="batches",t.next=4,this.httpRequest.get(n,eB(e));case 4:return r=t.sent,t.abrupt("return",eN(eN({},r),{},{results:r.results.map(function(t){return new eJ(t)})}));case 6:case"end":return t.stop()}},t,this)})),function(){return tJ.apply(this,arguments)})}]),e$=eI(function t(e){ek(this,t),eT(this,"config",void 0),eT(this,"httpRequest",void 0),eT(this,"tasks",void 0),eT(this,"batches",void 0),this.config=e,this.httpRequest=new eY(e),this.tasks=new eQ(e),this.batches=new eZ(e)},[{key:"index",value:function(t){return new ez(this.config,t)}},{key:"getIndex",value:(tZ=ex(eO().mark(function t(e){return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",new ez(this.config,e).fetchInfo());case 1:case"end":return t.stop()}},t,this)})),function(t){return tZ.apply(this,arguments)})},{key:"getRawIndex",value:(t$=ex(eO().mark(function t(e){return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",new ez(this.config,e).getRawInfo());case 1:case"end":return t.stop()}},t,this)})),function(t){return t$.apply(this,arguments)})},{key:"getIndexes",value:(t0=ex(eO().mark(function t(){var e,n,r,i=this,s=arguments;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e=s.length>0&&void 0!==s[0]?s[0]:{},t.next=3,this.getRawIndexes(e);case 3:return r=(n=t.sent).results.map(function(t){return new ez(i.config,t.uid,t.primaryKey)}),t.abrupt("return",eN(eN({},n),{},{results:r}));case 6:case"end":return t.stop()}},t,this)})),function(){return t0.apply(this,arguments)})},{key:"getRawIndexes",value:(t3=ex(eO().mark(function t(){var e,n,r=arguments;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e=r.length>0&&void 0!==r[0]?r[0]:{},n="indexes",t.next=4,this.httpRequest.get(n,e);case 4:return t.abrupt("return",t.sent);case 5:case"end":return t.stop()}},t,this)})),function(){return t3.apply(this,arguments)})},{key:"createIndex",value:(t1=ex(eO().mark(function t(e){var n,r=arguments;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n=r.length>1&&void 0!==r[1]?r[1]:{},t.next=3,ez.create(e,n,this.config);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(t){return t1.apply(this,arguments)})},{key:"updateIndex",value:(t4=ex(eO().mark(function t(e){var n,r=arguments;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n=r.length>1&&void 0!==r[1]?r[1]:{},t.next=3,new ez(this.config,e).update(n);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(t){return t4.apply(this,arguments)})},{key:"deleteIndex",value:(t2=ex(eO().mark(function t(e){return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,new ez(this.config,e).delete();case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}},t,this)})),function(t){return t2.apply(this,arguments)})},{key:"deleteIndexIfExists",value:(t5=ex(eO().mark(function t(e){return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.deleteIndex(e);case 3:return t.abrupt("return",!0);case 6:if(t.prev=6,t.t0=t.catch(0),t.t0.code!==eg.INDEX_NOT_FOUND){t.next=10;break}return t.abrupt("return",!1);case 10:throw t.t0;case 11:case"end":return t.stop()}},t,this,[[0,6]])})),function(t){return t5.apply(this,arguments)})},{key:"swapIndexes",value:(t6=ex(eO().mark(function t(e){var n;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n="/swap-indexes",t.next=3,this.httpRequest.post(n,e);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(t){return t6.apply(this,arguments)})},{key:"multiSearch",value:(t8=ex(eO().mark(function t(e,n){var r;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return r="multi-search",t.next=3,this.httpRequest.post(r,e,void 0,n);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(t,e){return t8.apply(this,arguments)})},{key:"getTasks",value:(t7=ex(eO().mark(function t(){var e,n=arguments;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e=n.length>0&&void 0!==n[0]?n[0]:{},t.next=3,this.tasks.getTasks(e);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(){return t7.apply(this,arguments)})},{key:"getTask",value:(t9=ex(eO().mark(function t(e){return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.tasks.getTask(e);case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}},t,this)})),function(t){return t9.apply(this,arguments)})},{key:"waitForTasks",value:(et=ex(eO().mark(function t(e){var n,r,i,s,a,u=arguments;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return i=void 0===(r=(n=u.length>1&&void 0!==u[1]?u[1]:{}).timeOutMs)?5e3:r,a=void 0===(s=n.intervalMs)?50:s,t.next=3,this.tasks.waitForTasks(e,{timeOutMs:i,intervalMs:a});case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(t){return et.apply(this,arguments)})},{key:"waitForTask",value:(ee=ex(eO().mark(function t(e){var n,r,i,s,a,u=arguments;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return i=void 0===(r=(n=u.length>1&&void 0!==u[1]?u[1]:{}).timeOutMs)?5e3:r,a=void 0===(s=n.intervalMs)?50:s,t.next=3,this.tasks.waitForTask(e,{timeOutMs:i,intervalMs:a});case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(t){return ee.apply(this,arguments)})},{key:"cancelTasks",value:(en=ex(eO().mark(function t(e){return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.tasks.cancelTasks(e);case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}},t,this)})),function(t){return en.apply(this,arguments)})},{key:"deleteTasks",value:(er=ex(eO().mark(function t(){var e,n=arguments;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e=n.length>0&&void 0!==n[0]?n[0]:{},t.next=3,this.tasks.deleteTasks(e);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(){return er.apply(this,arguments)})},{key:"getBatches",value:(ei=ex(eO().mark(function t(){var e,n=arguments;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e=n.length>0&&void 0!==n[0]?n[0]:{},t.next=3,this.batches.getBatches(e);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(){return ei.apply(this,arguments)})},{key:"getBatch",value:(es=ex(eO().mark(function t(e){return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.batches.getBatch(e);case 2:return t.abrupt("return",t.sent);case 3:case"end":return t.stop()}},t,this)})),function(t){return es.apply(this,arguments)})},{key:"getKeys",value:(ea=ex(eO().mark(function t(){var e,n,r,i=arguments;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e=i.length>0&&void 0!==i[0]?i[0]:{},n="keys",t.next=4,this.httpRequest.get(n,e);case 4:return(r=t.sent).results=r.results.map(function(t){return eN(eN({},t),{},{createdAt:new Date(t.createdAt),updatedAt:new Date(t.updatedAt)})}),t.abrupt("return",r);case 7:case"end":return t.stop()}},t,this)})),function(){return ea.apply(this,arguments)})},{key:"getKey",value:(eu=ex(eO().mark(function t(e){var n;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n="keys/".concat(e),t.next=3,this.httpRequest.get(n);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(t){return eu.apply(this,arguments)})},{key:"createKey",value:(eo=ex(eO().mark(function t(e){var n;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n="keys",t.next=3,this.httpRequest.post(n,e);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(t){return eo.apply(this,arguments)})},{key:"updateKey",value:(ec=ex(eO().mark(function t(e,n){var r;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return r="keys/".concat(e),t.next=3,this.httpRequest.patch(r,n);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(t,e){return ec.apply(this,arguments)})},{key:"deleteKey",value:(ep=ex(eO().mark(function t(e){var n;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return n="keys/".concat(e),t.next=3,this.httpRequest.delete(n);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(t){return ep.apply(this,arguments)})},{key:"health",value:(eh=ex(eO().mark(function t(){var e;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="health",t.next=3,this.httpRequest.get(e);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(){return eh.apply(this,arguments)})},{key:"isHealthy",value:(ed=ex(eO().mark(function t(){var e;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,e="health",t.next=4,this.httpRequest.get(e);case 4:return t.abrupt("return",!0);case 7:return t.prev=7,t.t0=t.catch(0),t.abrupt("return",!1);case 10:case"end":return t.stop()}},t,this,[[0,7]])})),function(){return ed.apply(this,arguments)})},{key:"getStats",value:(el=ex(eO().mark(function t(){var e;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="stats",t.next=3,this.httpRequest.get(e);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(){return el.apply(this,arguments)})},{key:"getVersion",value:(ef=ex(eO().mark(function t(){var e;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="version",t.next=3,this.httpRequest.get(e);case 3:return t.abrupt("return",t.sent);case 4:case"end":return t.stop()}},t,this)})),function(){return ef.apply(this,arguments)})},{key:"createDump",value:(ev=ex(eO().mark(function t(){var e,n;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="dumps",t.next=3,this.httpRequest.post(e);case 3:return n=t.sent,t.abrupt("return",new eX(n));case 5:case"end":return t.stop()}},t,this)})),function(){return ev.apply(this,arguments)})},{key:"createSnapshot",value:(ey=ex(eO().mark(function t(){var e,n;return eO().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e="snapshots",t.next=3,this.httpRequest.post(e);case 3:return n=t.sent,t.abrupt("return",new eX(n));case 5:case"end":return t.stop()}},t,this)})),function(){return ey.apply(this,arguments)})}]);t.ContentTypeEnum={JSON:"application/json",CSV:"text/csv",NDJSON:"application/x-ndjson"},t.EnqueuedTask=eX,t.ErrorStatusCode=eg,t.Index=ez,t.MatchingStrategies={ALL:"all",LAST:"last",FREQUENCY:"frequency"},t.MeiliSearch=e$,t.MeiliSearchApiError=eF,t.MeiliSearchError=eU,t.MeiliSearchRequestError=ej,t.MeiliSearchTimeOutError=eV,t.Meilisearch=e$,t.Task=eW,t.TaskClient=eQ,t.TaskStatus=e_,t.TaskTypes={DOCUMENTS_ADDITION_OR_UPDATE:"documentAdditionOrUpdate",DOCUMENT_DELETION:"documentDeletion",DUMP_CREATION:"dumpCreation",INDEX_CREATION:"indexCreation",INDEX_DELETION:"indexDeletion",INDEXES_SWAP:"indexSwap",INDEX_UPDATE:"indexUpdate",SETTINGS_UPDATE:"settingsUpdate",SNAPSHOT_CREATION:"snapshotCreation",TASK_CANCELATION:"taskCancelation",TASK_DELETION:"taskDeletion"},t.default=e$,t.versionErrorHintMessage=eK,Object.defineProperty(t,"__esModule",{value:!0})}(r.exports),r.exports),o=u&&u.__esModule&&Object.prototype.hasOwnProperty.call(u,"default")?u.default:u,c=Object.freeze(Object.assign(Object.create(null),u,{default:o}));function p(t){return"object"==typeof t&&!Array.isArray(t)&&null!==t}var h=/([\\"])/g;function d(t){return t.replace(h,"\\$1")}function l(t){var e=d(t),n=e.indexOf(":"),r=e.slice(0,n),i=e.slice(n+1);return'"'.concat(r,'"="').concat(i,'"')}function f(t){var e=function(){var e=t.match(/^([^<!>:=]*)([<!>:=]+)(.*)$/);if(e){var n=e.slice(1),r=n[0],i=n[1],s=n[2],a=(i.match(/^([<!>]?=|<|>|:){1}(.*)/)||["",""]).slice(1);return[r,a[0],a[1]+s]}return[t,"",""]}(),n=e[0],r=e[1],i=e[2],s=d(n);return'"'.concat(s.trim(),'"').concat(":"===r?" ":r).concat(i.trim())}function v(t,e){return"string"==typeof e?t(e):e.map(function(e){return"string"==typeof e?t(e):e.map(function(e){return t(e)})})}function y(t){return"string"==typeof t?[t]:t}function _(t){var e,n,r,i,s,a=t.query,u=t.indexUid,o=t.facets,c=t.attributesToSnippet,p=t.snippetEllipsisText,h=t.filters,d=t.numericFilters,_=t.facetFilters,g=t.attributesToRetrieve,w=t.attributesToHighlight,m=t.highlightPreTag,x=t.highlightPostTag,b=t.placeholderSearch,k=t.pagination,A=t.sort,I=t.restrictSearchableAttributes,T=t.meiliSearchParams,E={indexUid:u},S=(e=void 0!==d?v(f,d):d,n=void 0!==_?v(l,_):_,r=y(e),i=y(n),s=[],void 0!==h&&s.push(h),void 0!==r&&s.push.apply(s,r),void 0!==i&&s.push.apply(s,i),s);return{getParams:function(){return E},addQuery:function(){E.q=a},addFacets:function(){void 0!==o&&(E.facets="string"==typeof o?[o]:o)},addAttributesToCrop:function(){var t,e=null!==(t=null==T?void 0:T.attributesToCrop)&&void 0!==t?t:c;void 0!==e&&(E.attributesToCrop=e)},addCropLength:function(){var t=null==T?void 0:T.cropLength;void 0!==t&&(E.cropLength=t)},addCropMarker:function(){var t,e=null!==(t=null==T?void 0:T.cropMarker)&&void 0!==t?t:p;void 0!==e&&(E.cropMarker=e)},addFilters:function(){S.length&&(E.filter=S)},addAttributesToRetrieve:function(){var t,e=null!==(t=null==T?void 0:T.attributesToRetrieve)&&void 0!==t?t:g;void 0!==e&&(E.attributesToRetrieve=e)},addAttributesToHighlight:function(){var t,e;E.attributesToHighlight=null!==(e=null!==(t=null==T?void 0:T.attributesToHighlight)&&void 0!==t?t:w)&&void 0!==e?e:["*"]},addPreTag:function(){var t,e;E.highlightPreTag=null!==(e=null!==(t=null==T?void 0:T.highlightPreTag)&&void 0!==t?t:m)&&void 0!==e?e:"__ais-highlight__"},addPostTag:function(){var t,e;E.highlightPostTag=null!==(e=null!==(t=null==T?void 0:T.highlightPostTag)&&void 0!==t?t:x)&&void 0!==e?e:"__/ais-highlight__"},addPagination:function(){var t=!(!b&&!a&&(!S||0===S.length));if(k.finite){var e,n,r=(e=k.page,n=k.hitsPerPage,t?{hitsPerPage:n,page:e+1}:{hitsPerPage:0,page:e+1}),i=r.hitsPerPage,s=r.page;E.hitsPerPage=i,E.page=s}else{var u,o,c=(u=k.page,o=k.hitsPerPage,t?{limit:o+1,offset:u*o}:{limit:0,offset:0}),p=c.limit,h=c.offset;E.limit=p,E.offset=h}},addSort:function(){(null==A?void 0:A.length)&&(E.sort=Array.isArray(A)?A:[A])},addGeoSearchFilter:function(){var e=function(t){var e,n,r,i=t.insideBoundingBox,s=t.aroundLatLng,a=t.aroundRadius,u=t.minimumAroundRadius;if(s){var o=s.split(",").map(function(t){return Number.parseFloat(t).toFixed(5)}),c=o[0],p=o[1];e=[c,p]}if((null!=a||null!=u)&&("all"===a?console.warn("instant-meilisearch is not compatible with the `all` value on the aroundRadius parameter"):n=null!=a?a:u),i&&"string"==typeof i){var h=i.split(",").map(function(t){return parseFloat(t)}),d=h[0],l=h[1],f=h[2],v=h[3];r="_geoBoundingBox([".concat(d,", ").concat(l,"], [").concat(f,", ").concat(v,"])")}else if(null!=e&&null!=n){var c=e[0],p=e[1];r="_geoRadius(".concat(c,", ").concat(p,", ").concat(n,")")}return r}({insideBoundingBox:t.insideBoundingBox,aroundLatLng:t.aroundLatLng,aroundRadius:t.aroundRadius,minimumAroundRadius:t.minimumAroundRadius});void 0!==e&&(Array.isArray(E.filter)?E.filter.unshift(e):E.filter=[e])},addShowMatchesPosition:function(){var t=null==T?void 0:T.showMatchesPosition;void 0!==t&&(E.showMatchesPosition=t)},addMatchingStrategy:function(){var t=null==T?void 0:T.matchingStrategy;void 0!==t&&(E.matchingStrategy=t)},addShowRankingScore:function(){var t=null==T?void 0:T.showRankingScore;void 0!==t&&(E.showRankingScore=t)},addAttributesToSearchOn:function(){var t=(null==T?void 0:T.attributesToSearchOn)!==void 0?T.attributesToSearchOn:I;void 0!==t&&(E.attributesToSearchOn=t)},addHybridSearch:function(){var t=null==T?void 0:T.hybrid;void 0!==t&&(E.hybrid=t)},addDistinct:function(){var t=null==T?void 0:T.distinct;void 0!==t&&(E.distinct=t)},addRankingScoreThreshold:function(){var t=null==T?void 0:T.rankingScoreThreshold;void 0!==t&&(E.rankingScoreThreshold=t)}}}function g(t){var e=_(t);return e.addQuery(),e.addFacets(),e.addAttributesToCrop(),e.addCropLength(),e.addCropMarker(),e.addFilters(),e.addAttributesToRetrieve(),e.addAttributesToHighlight(),e.addPreTag(),e.addPostTag(),e.addPagination(),e.addSort(),e.addGeoSearchFilter(),e.addShowMatchesPosition(),e.addMatchingStrategy(),e.addShowRankingScore(),e.addAttributesToSearchOn(),e.addHybridSearch(),e.addDistinct(),e.addRankingScoreThreshold(),e.getParams()}function w(t){if(!t)return[];for(var e,n=/[^:]+:(?:asc|desc)/g,r=[];null!==(e=n.exec(t));)r.push(e[0]);return r.map(function(t){return t.replace(/^,+|,+$/,"")})}function m(t,e,n){return{hitsPerPage:void 0===e?20:e,page:n||0,finite:!!t}}function x(t){var e=t.indexOf(":");return -1===e?{indexUid:t,sortBy:""}:{indexUid:t.substring(0,e),sortBy:t.substring(e+1)}}t.instantMeiliSearch=function(t,e,n){void 0===e&&(e=""),void 0===n&&(n={}),function(t,e,n){var r=n.requestConfig,i=n.httpClient;if("string"!=typeof t)throw TypeError("Provided hostUrl value (1st parameter) is not a string, expected string");if("string"!=typeof e&&"function"!=typeof e)throw TypeError("Provided apiKey value (2nd parameter) is not a string or a function, expected string or function");if(void 0!==r&&!p(r))throw TypeError("Provided requestConfig should be an object");if(i&&"function"!=typeof i)throw TypeError("Provided custom httpClient should be a function")}(t,e,n);var r,o,c,h,d={host:t,apiKey:e=function(t){if("function"==typeof t){var e=t();if("string"!=typeof e)throw TypeError("Provided apiKey function (2nd parameter) did not return a string, expected string");return e}return t}(e),clientAgents:(void 0===(r=n.clientAgents)&&(r=[]),r.concat("Meilisearch instant-meilisearch (v".concat("0.23.0",")")))};void 0!==n.httpClient&&(d.httpClient=n.httpClient),void 0!==n.requestConfig&&(d.requestConfig=n.requestConfig);var l=new u.MeiliSearch(d),f=(void 0===o&&(o={}),c=o,{getEntry:function(t){if(c[t])try{return JSON.parse(c[t])}catch(t){}},formatKey:function(t){return t.reduce(function(t,e){return t+JSON.stringify(e)},"")},setEntry:function(t,e){c[t]=JSON.stringify(e)},clearCache:function(){c={}}}),v={multiSearch:function(t,e){return s(this,void 0,void 0,function(){var n,r,s;return a(this,function(a){switch(a.label){case 0:if(n=f.formatKey([t]),r=f.getEntry(n))return[2,r];return[4,l.multiSearch({queries:t})];case 1:return s=a.sent().results.map(function(t,n){return i(i({},t),{pagination:e[n]||{}})}),f.setEntry(n,s),[2,s]}})})}},y={},b=(h=n,i(i({},{placeholderSearch:!0,keepZeroFacets:!1,clientAgents:[],finitePagination:!1}),h));return{meiliSearchInstance:l,setMeiliSearchParams:function(t){var e=n.meiliSearchParams;n.meiliSearchParams=void 0===e?t:i(i({},e),t)},searchClient:{clearCache:function(){return f.clearCache()},search:function(t){return s(this,void 0,void 0,function(){var e,r,u,o,c,h,d,l,f,k;return a(this,function(A){switch(A.label){case 0:for(A.trys.push([0,3,,4]),e=[],r=[],u=[],o=0,c=t;o<c.length;o++)d=g(h=function(t,e){var n=t.query,r=t.indexName,s=t.params,a=x(r),u=a.indexUid,o=a.sortBy,c=m(e.finitePagination,null==s?void 0:s.hitsPerPage,null==s?void 0:s.page);return i(i(i(i({},e),{query:n}),s),{sort:w(o),indexUid:u,pagination:c,placeholderSearch:!1!==e.placeholderSearch,keepZeroFacets:!!e.keepZeroFacets})}(c[o],n)),e.push(d),l=function(t){var e=_(i(i({},t),{placeholderSearch:!0,query:""}));return e.addFacets(),e.addPagination(),e.getParams()}(h),u.push(l),r.push(h.pagination);return[4,function(t,e,n){return s(this,void 0,void 0,function(){var r,i,s,u,o;return a(this,function(a){switch(a.label){case 0:var c,p;if(c="indexUid",p=[],r=function(t){return!p.includes(t[c])&&(p.push(t[c]),!0)},0===(i=e.filter(r).filter(function(t){var e=t.indexUid;return!Object.keys(n).includes(e)})).length)return[2,n];return[4,t.multiSearch(i,[])];case 1:for(s=0,u=a.sent();s<u.length;s++)n[(o=u[s]).indexUid]=o.facetDistribution||{};return[2,n]}})})}(v,u,y)];case 1:return y=A.sent(),[4,v.multiSearch(e,r)];case 2:var I;return f=A.sent(),I=y=function(t,e){for(var n=0;n<e.length;n++){var r=e[n];t[r.indexUid]=i(i({},r.facetDistribution||{}),t[r.indexUid]||{})}return t}(y,f),[2,{results:f.map(function(t){var e,n,r,s,a,u,o,c,h,d,l,f,v,y,_,g,w,m,x,k,A,T,E,S,R,D,N,O,q;return e=t,n=I[t.indexUid],r=b,w=e.processingTimeMs,m=e.query,x=e.indexUid,A=void 0===(k=e.facetDistribution)?{}:k,T=e.facetStats,E=Object.keys(A),R=(S=(a=(s=e.pagination).hitsPerPage,{page:s.page,nbPages:function(t,e){if(null!=t.totalPages)return t.totalPages;if(0===e)return 0;var n=t.limit,r=t.offset;return(void 0===r?0:r)/e+1+(t.hits.length>=(void 0===n?20:n)?1:0)}(e,a),hitsPerPage:a})).hitsPerPage,D=S.page,N=S.nbPages,O=(u=e.hits,o=e.pagination.hitsPerPage,c=r.finitePagination,h=r.primaryKey,!c&&u.length>o&&u.splice(u.length-1,1),function(t){for(var e,n=0;n<t.length;n++){var r="".concat(n+1e6*Math.random());t[n]._geo&&(t[n]._geoloc=t[n]._geo,t[n].objectID||(t[n].objectID=r)),(null===(e=t[n]._formatted)||void 0===e?void 0:e._geo)&&(t[n]._formatted._geoloc=t[n]._formatted._geo,t[n]._formatted.objectID||(t[n]._formatted.objectID=r))}return t}(u.map(function(t){var e;if(Object.keys(t).length>0){var n=t._formatted,i=t._matchesPosition,s=Object.assign(function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&0>e.indexOf(r)&&(n[r]=t[r]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,r=Object.getOwnPropertySymbols(t);i<r.length;i++)0>e.indexOf(r[i])&&Object.prototype.propertyIsEnumerable.call(t,r[i])&&(n[r[i]]=t[r[i]]);return n}(t,["_formatted","_matchesPosition"]),function(t){if(!t)return{};var e=function t(e){return Array.isArray(e)?e.map(function(e){return t(e)}):p(e)?Object.keys(e).reduce(function(n,r){return n[r]=t(e[r]),n},{}):{value:"string"==typeof e?e:void 0===e?JSON.stringify(null):JSON.stringify(e)}}(t);return{_highlightResult:e,_snippetResult:e}}(n));return(null===(e=null==r?void 0:r.meiliSearchParams)||void 0===e?void 0:e.showMatchesPosition)&&(s._matchesPosition=i),h&&(s.objectID=t[h]),s}return t}))),q=(d=e.hitsPerPage,l=e.totalPages,f=e.estimatedTotalHits,v=e.totalHits,null!=f?f:null!=v?v:(void 0===d?0:d)*(void 0===l?0:l)),{index:x,hitsPerPage:R,page:D,facets:(y=r.keepZeroFacets,_=A,y?function(t,e,n){for(var r=t?"string"==typeof t?[t]:t:[],i={},s=0;s<r.length;s++){var a=r[s];for(var u in e[a])i[a]||(i[a]=n[a]||{}),i[a][u]?i[a][u]=n[a][u]:i[a][u]=0}return i}(E,n,_=_||{}):_),nbPages:N,nbHits:q,processingTimeMS:w,query:m,hits:O,params:"",exhaustiveNbHits:!1,facets_stats:Object.keys(g=void 0===T?{}:T).reduce(function(t,e){return t[e]=i(i({},g[e]),{avg:0,sum:0}),t},{})}})}];case 3:throw console.error(k=A.sent()),Error(k);case 4:return[2]}})})},searchForFacetValues:function(t){return s(this,void 0,void 0,function(){var e,r,s,u,o,c,p,h,d;return a(this,function(a){switch(a.label){case 0:e=[],r=0,s=t,a.label=1;case 1:var f,v,y,_,b,k,A;if(!(r<s.length))return[3,4];return f=u=s[r],v=n,_=(y=x(f.indexName)).indexUid,b=y.sortBy,k=f.params,A=m(v.finitePagination,null==k?void 0:k.hitsPerPage,null==k?void 0:k.page),c=g(o=i(i(i({},v),k),{sort:w(b),indexUid:_,pagination:A,placeholderSearch:!1!==v.placeholderSearch,keepZeroFacets:!!v.keepZeroFacets})),p=i(i({},c),{facetQuery:u.params.facetQuery,facetName:u.params.facetName}),delete p.indexUid,[4,l.index(o.indexUid).searchForFacetValues(p)];case 2:d={facetHits:(h=a.sent()).facetHits.map(function(t){return i(i({},t),{highlighted:t.value})}),exhaustiveFacetsCount:!1,processingTimeMS:h.processingTimeMs},e.push(d),a.label=3;case 3:return r++,[3,1];case 4:return[2,e]}})})}}}},t.meilisearch=c,Object.defineProperty(t,"__esModule",{value:!0})}(e)}}]);