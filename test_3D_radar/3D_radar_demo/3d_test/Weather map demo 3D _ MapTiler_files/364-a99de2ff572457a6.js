try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},a=(new e.Error).stack;a&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[a]="45b5b85b-f848-4b3d-910a-3c1466055ce8",e._sentryDebugIdIdentifier="sentry-dbid-45b5b85b-f848-4b3d-910a-3c1466055ce8")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[364],{80643:function(e,a,t){"use strict";var r=t(85893),n=t(41664),s=t.n(n);let l=e=>e.startsWith("/"),o=e=>{var a;if(null===(a=e.maps)||void 0===a?void 0:a.data)return"map"===e.maps.data.attributes.type?"https://cloud.maptiler.com/maps/"+e.maps.data.attributes.mapID:"https://cloud.maptiler.com/tiles/"+e.maps.data.attributes.mapID;switch(e.url){case"/#contact":return"#contact";case"cloud":return"https://cloud.maptiler.com/";case"blog":return"https://www.maptiler.com/news/";case"customize":return"https://cloud.maptiler.com/maps/editor/";case"home":return"https://www.maptiler.com";case"support":return"https://documentation.maptiler.com/hc/en-us/requests/new";default:return e.url}};a.Z=e=>{let{data:a,children:t,...n}=e,i=o(a);return i&&l(i)?(0,r.jsx)(s(),{...n,href:i,children:t}):(0,r.jsx)("a",{...n,href:i,rel:"noopener",children:t})}},67221:function(e,a,t){"use strict";var r=t(85893);a.Z=e=>{let{className:a,name:t,width:n=24,height:s=24,...l}=e;return(0,r.jsx)("svg",{className:a,version:"1.1",xmlns:"http://www.w3.org/2000/svg",width:n,height:s,viewBox:"0 0 24 24",...l,children:(0,r.jsx)("use",{href:"/styles/style/icon/icon.svg#"+t})})}},13484:function(e,a,t){"use strict";t.d(a,{Z:function(){return X}});var r=t(85893),n=t(2962),s=t(11163),l=t.n(s),o=t(67294),i=t(97375),c=t(90273),d=t(25675),m=t.n(d),u=t(11656),p=t.n(u),h=e=>{var a;let{data:t}=e;if(!t)return null;let{url:n,ext:s}=t;return(0,r.jsx)("div",{className:"container-fluid ".concat(p().headerShape," ").concat(null!==(a=p()[s.substring(1)])&&void 0!==a?a:""),children:(0,r.jsx)(m(),{priority:!0,src:n,width:"100",height:"100",alt:"Header Rectangle"})})},b=t(2626),g=t(50259),v=t(45087),x=t(80129),f=t.n(x);let j=e=>e.startsWith("/engine")?"engine":e.startsWith("/cloud")?"cloud":e.startsWith("/server")?"server":e.startsWith("/data")?"data":e.startsWith("/sdk")?"sdk":e.startsWith("/mobile")?"mobile":"",w=e=>{let{data:a,setShowContactModal:t,setNotification:n,linkUrl:l}=e,{name:i,email:c,phone:d,product:m,text:u,locale:p,other:h,send:b,infoNotification:x,successNotification:w,errorNotification:y}=a.data.attributes,k=(0,s.useRouter)(),{executeRecaptcha:N}=(0,g.xX)(),_=(0,o.useMemo)(()=>({cloud:"MapTiler Cloud",data:"MapTiler Data",engine:"MapTiler Engine",server:"MapTiler Server",other:h}),[h]),T=(0,o.useCallback)(function(e){var a;let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,r=e.split("#")[1];return _[null===(a=f().parse(r).product)||void 0===a?void 0:a.toString()]||_[j(t)]||_.cloud},[_]),[C,M]=(0,o.useState)({"mauticform[formId]":15,"mauticform[messenger]":15,"mauticform[formName]":"webcontactform","mauticform[f_name]":"","mauticform[email2]":"","mauticform[phone]":"","mauticform[lang]":p,"mauticform[product]":T(l||k.asPath,k.asPath),"mauticform[text1]":"","mauticform[pageuri]":window.location.toString()}),D=C["mauticform[f_name]"]&&(0,v.zL)(C["mauticform[email2]"])&&C["mauticform[text1]"],I=e=>{let{name:a,value:t}=e.target;M({...C,[a]:t})},S=async e=>{if(e.preventDefault(),D){let e=await N("contact_form_submit");try{if((await fetch("/api/recaptcha",{method:"POST",body:JSON.stringify({formData:C,captcha:e}),headers:{"Content-Type":"application/json"}})).ok)(0,v.iv)(C,w,y,n),t(!1);else throw Error("invalid reCaptcha")}catch(e){n({message:"reCaptcha verification failed, are you a bot?",type:"error",show:!0})}}else n({message:x,type:"info",show:!0})};return(0,r.jsxs)("form",{onSubmit:S,children:[(0,r.jsx)("input",{name:"mauticform[lang]",value:p,className:"mauticform-hidden",type:"hidden"}),(0,r.jsx)("label",{children:i}),(0,r.jsx)("input",{type:"text",name:"mauticform[f_name]",className:"form-control w-100",value:C["mauticform[f_name]"],onChange:I}),(0,r.jsx)("label",{children:c}),(0,r.jsx)("input",{type:"email",name:"mauticform[email2]",className:"form-control w-100",value:C["mauticform[email2]"],onChange:I}),(0,r.jsx)("label",{children:d}),(0,r.jsx)("input",{type:"tel",name:"mauticform[phone]",className:"form-control w-100",placeholder:"Optional",value:C["mauticform[phone]"],onChange:I}),(0,r.jsx)("label",{children:m}),(0,r.jsx)("select",{name:"mauticform[product]",className:"form-select w-100",value:C["mauticform[product]"],onChange:I,children:Object.values(_).map(e=>(0,r.jsx)("option",{value:e,children:e},e))}),(0,r.jsx)("label",{children:u}),(0,r.jsx)("textarea",{name:"mauticform[text1]",rows:6,className:"form-control w-100",value:C["mauticform[text1]"],onChange:I}),(0,r.jsx)("button",{disabled:!D,type:"submit",className:"btn btn-primary w-100",children:b})]})};var y=e=>{let{data:a}=e,[t,n]=(0,o.useState)(!1),[l,i]=(0,o.useState)(null),[c,d]=(0,o.useState)(""),m=(0,s.useRouter)();return(0,o.useEffect)(()=>{if(null==l?void 0:l.show){let e=setTimeout(()=>{i(e=>({...e,show:!1}))},2e3);return()=>clearTimeout(e)}},[l]),(0,o.useEffect)(()=>{let e=document.querySelectorAll('button[href*="#contact"], a[href*="#contact"]'),a=e=>{e.preventDefault(),d(e.target.href),n(!0)};return e.forEach(e=>{e.addEventListener("click",a)}),m.asPath.includes("#contact")&&n(!0),()=>{e.forEach(e=>{e.removeEventListener("click",a)})}},[m]),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(v.jf,{message:null==l?void 0:l.message,type:null==l?void 0:l.type,show:null==l?void 0:l.show}),(0,r.jsx)(b.Z,{contentClassName:"p-3 border-0 rounded",centered:!0,show:t,onHide:()=>n(!1),id:"contact-form-modal",children:(0,r.jsx)(g.pm,{reCaptchaKey:"6Lf-pZ8qAAAAAFNO6YT6TCOWk1NMRWe2l_hGnblS",language:m.locale,container:{element:"recaptcha-box",parameters:{badge:"bottomright"}},children:(0,r.jsx)(w,{data:a,setShowContactModal:n,setNotification:i,linkUrl:c})})}),(0,r.jsx)("div",{id:"recaptcha-box",className:t?"":"invisible"})]})};let k=t(41686).ZP.div.withConfig({componentId:"sc-e8ab0f94-0"})(["position:relative;overflow-x:clip;"]);var N=t(80643),_=t(67221),T=t(41664),C=t.n(T);let M=e=>{let{name:a}=e;return(0,r.jsx)("span",{className:"ms-auto float-end collapse-arrow",children:(0,r.jsx)(_.Z,{className:"me-0",name:a})})},D=()=>(0,r.jsx)(C(),{className:"navbar-brand pl-gutter product-brand track-link-header me-auto",href:"/","aria-label":"MapTiler",children:(0,r.jsx)("svg",{width:"165",height:"45",children:(0,r.jsx)("use",{href:"/styles/style/logo/maptiler-logo-adaptive.svg?123#maptilerLogo"})})}),I=()=>{let{asPath:e}=(0,s.useRouter)(),a=j(e),t="data"===a||"server"===a?"https://data.maptiler.com/my-extracts/":"https://cloud.maptiler.com/";return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("a",{href:t,type:"button",className:"btn btn-primary btn-sm me-o5 track-link-header",children:"Create account"}),(0,r.jsx)("a",{href:t,type:"button",className:"btn btn-lighter btn-sm track-link-header",children:"Log in"})]})},S=e=>{let{icon:a}=e;return a?a.includes(".svg")?(0,r.jsx)("svg",{className:"",version:"1.1",xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",children:(0,r.jsx)("use",{href:"/img/navbar-icon/".concat(a)})}):(0,r.jsx)("img",{width:"40",height:"40",className:"rounded-1",src:"/img/navbar-icon/".concat(a),alt:a.split(".")[0]}):null},E=e=>{let a=document.getElementById("".concat(e,"MobileOffcanvasClose"));a&&a.click()},L=e=>{let a,{id:t,icon:n,title:s,arrow:l,active:o}=e;return(0,r.jsxs)("a",{className:"dropdown-link dropdown-products-tab track-link-header ".concat(o?"active":""),id:"".concat(t,"-tab"),"data-bs-toggle":"pill","data-bs-target":"#".concat(t),type:"button",role:"tab","aria-controls":t,onClick:e=>e.stopPropagation(),onMouseEnter:e=>{let t=e.currentTarget;a=setTimeout(()=>{t&&t.click()},150)},onMouseLeave:()=>{clearTimeout(a)},children:[(0,r.jsx)(S,{icon:n}),s,l&&(0,r.jsx)(M,{name:"keyboard_arrow_right"})]})},Z=e=>{let{url:a,icon:t,title:n,arrow:s,badge:o,mobile:i=!1,categoryID:c,subcategoryID:d}=e;return(0,r.jsxs)(N.Z,{className:"dropdown-link track-link-header track-link-category-".concat(c," ").concat(d?"track-link-subcategory-".concat(d):""),data:{url:a},...i&&{"data-bs-toggle":"collapse","data-bs-target":"#Mobilenav",onClick:()=>{l().push(a),E(d)}},children:[(0,r.jsx)(S,{icon:t}),n,o&&(0,r.jsx)("span",{className:"badge bg-lighter text-secondary ms-1",children:o}),s&&(0,r.jsx)(M,{name:"keyboard_arrow_right"})]})},P=e=>{let{url:a,icon:t,title:n,arrow:s,categoryID:o}=e;return(0,r.jsxs)(N.Z,{className:"py-2 mx-2 h6 d-block my-0 link-secondary border-bottom border-gray track-link-header track-link-category-".concat(o),data:{url:a},"data-bs-toggle":"collapse","data-bs-target":"#Mobilenav",onClick:()=>l().push(a),children:[(0,r.jsx)(S,{icon:t}),n,"link-arrow"===s&&(0,r.jsx)(M,{name:"keyboard_arrow_right"})]})},R=e=>{let{id:a,icon:t,title:n,arrow:s}=e;return(0,r.jsxs)("a",{className:"dropdown-link dropdown-products-tab track-link-header",type:"button","data-bs-toggle":"offcanvas","data-bs-target":"#".concat(a,"Mobile"),"aria-controls":"".concat(a,"Mobile"),children:[(0,r.jsx)(S,{icon:t}),n,s&&(0,r.jsx)(M,{name:"keyboard_arrow_right"})]})};var F=e=>{let{type:a}=e;switch(a){case"tab-link":return(0,r.jsx)(L,{...e});case"page-link":return(0,r.jsx)(Z,{...e});case"mobile-link":return(0,r.jsx)(P,{...e});case"mobile-link-offcanvas":return(0,r.jsx)(R,{...e});default:return null}},O=e=>{var a,t;let{type:n,id:s,title:l,data:o,categoryID:i}=e;return"mobile-accordion"===n?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("a",{role:"button",type:"button","data-bs-toggle":"collapse",className:"py-2 mx-2 h6 d-block my-0 border-bottom border-light link-secondary track-link-header","data-bs-target":"#accordionMobile".concat(s),"aria-expanded":"false","aria-controls":"accordionMobile".concat(s),children:[l," ",(0,r.jsx)(M,{name:"keyboard_arrow_down"})]}),(0,r.jsx)("div",{id:"accordionMobile".concat(s),className:"accordion-collapse collapse","data-bs-parent":"#mobileNavigationAccordion","aria-labelledby":"accordionMobile".concat(s),children:(0,r.jsx)("div",{className:"accordion-body dropdown-products",children:null===(a=o.items)||void 0===a?void 0:a.map((e,a)=>(0,r.jsx)(F,{type:"page-link",url:e.path,title:e.title,mobile:!0,categoryID:i},a))})})]}):"desktop-dropdown"===n?(0,r.jsxs)("div",{className:"dropdown",children:[(0,r.jsxs)("a",{className:"nav-item nav-link",type:"button",id:"".concat(s,"Link"),"aria-controls":s,"data-bs-toggle":"dropdown","aria-expanded":"false",children:[l," ",(0,r.jsx)(M,{name:"keyboard_arrow_down"})]}),(0,r.jsx)("div",{className:"dropdown-menu dropdown-menu-navbar navbar-links dropdown-menu-navbar-animation slideIn","aria-labelledby":"".concat(s,"Link"),children:null===(t=o.items)||void 0===t?void 0:t.map((e,a)=>(0,r.jsx)(F,{type:"page-link",url:e.path,title:e.title,categoryID:i},a))})]}):null},A=e=>{let{type:a,id:t,active:n,contentTitle:s,contentDesc:l,data:o,categoryID:i}=e;return"tab-content"===a?(0,r.jsxs)("div",{className:"tab-pane fade ".concat(n?"show active":""),id:t,role:"tabpanel","aria-labelledby":"".concat(t,"-tab"),children:[(0,r.jsx)("h5",{className:"m-0",children:s}),(0,r.jsx)("p",{className:"mt-1 small",children:l}),(0,r.jsx)("div",{className:"dropdown-products",children:o.map((e,a)=>(0,r.jsx)(F,{icon:e.id,type:"page-link",url:e.path,title:e.title,badge:e.badge,arrow:!0,categoryID:i,subcategoryID:t},a))})]}):"offcanvas-content"===a?(0,r.jsxs)("div",{className:"offcanvas offcanvas-end position-absolute w-100 h-100 dropdown-products p-gutter",tabIndex:-1,"data-bs-backdrop":"false","data-bs-scroll":"true",id:"".concat(t,"Mobile"),children:[(0,r.jsxs)("a",{role:"button",type:"button",className:"py-2 mb-2 h6 d-block my-0 link-secondary dropdown-link px-0","data-bs-dismiss":"offcanvas",id:"".concat(t,"MobileOffcanvasClose"),children:[(0,r.jsx)(_.Z,{name:"arrow_back",width:24,height:24}),"Back"]}),(0,r.jsx)("h5",{className:"m-0",children:s}),(0,r.jsx)("p",{className:"mt-1 small",children:l}),(0,r.jsx)("div",{className:"dropdown-products",children:o.map((e,a)=>(0,r.jsx)(F,{type:"page-link",url:e.path,title:e.title,badge:e.badge,icon:e.id,arrow:!0,mobile:!0,categoryID:i,subcategoryID:t},a))})]}):null};let W=e=>{let{data:a}=e,t=(0,o.useRef)(null),{asPath:n}=(0,s.useRouter)(),l=j(n),i=a.find(e=>{var a;return null===(a=e.id)||void 0===a?void 0:a.includes("platform")}),c=a.find(e=>{var a;return null===(a=e.id)||void 0===a?void 0:a.includes("company")}),d=a.find(e=>{var a;return null===(a=e.id)||void 0===a?void 0:a.includes("pricing")}),m=a.find(e=>{var a;return null===(a=e.id)||void 0===a?void 0:a.includes("search")}),u=a.find(e=>{var a;return null===(a=e.id)||void 0===a?void 0:a.includes("resources")}),p=a.filter(e=>{var a;return null===(a=e.id)||void 0===a?void 0:a.includes("singleLink")}),h=(null==m?void 0:m.path)&&n.startsWith("/news")?"".concat(m.path,"?index=news"):null==m?void 0:m.path,b="server"===l||"data"===l?"/data/pricing/":"engine"===l?"/engine/pricing/":"/cloud/pricing/";return(0,o.useEffect)(()=>{let e=document.querySelectorAll(".offcanvas"),a=document.querySelector("#mobileDropdownContainer");e.forEach(e=>{e.addEventListener("shown.bs.offcanvas",()=>{a&&a.scrollTo({top:0,behavior:"smooth"})})})},[]),(0,o.useEffect)(()=>{let e=t.current,a=new ResizeObserver(e=>{for(let a of e){let{width:e,height:t}=a.contentRect;e>0&&t>0?document.body.classList.add("overflow-hidden"):document.body.classList.remove("overflow-hidden")}});return e&&a.observe(e),()=>{e&&a.unobserve(e)}},[]),(0,r.jsx)("nav",{className:"border-bottom border-light bg-white navbar-sticky-top",children:(0,r.jsxs)("div",{id:"navbar",className:"navbar navbar-expand-lg navbar-dark navbar-maptiler container-lg px-gutter",children:[(0,r.jsx)(D,{}),(0,r.jsxs)("div",{className:"navbar-nav d-none d-lg-flex",id:"Desktopnav",children:[i&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("a",{className:"nav-item nav-link",type:"button",id:"dropdownPlatformLink","aria-controls":"dropdownPlatform","data-bs-toggle":"dropdown","aria-expanded":"false",children:[i.title," ",(0,r.jsx)(M,{name:"keyboard_arrow_down"})]}),(0,r.jsxs)("div",{className:"dropdown-menu dropdown-menu-navbar dropdown-menu-navbar-platform dropdown-products p-3 dropdown-menu-navbar-animation slideIn","aria-labelledby":"dropdownPlatformLink",children:[(0,r.jsx)("div",{className:"nav flex-column pe-3",id:"v-pills-tab",role:"tablist","aria-orientation":"vertical",children:i.items.map((e,a)=>(0,r.jsx)(F,{icon:"".concat(e.id,".svg#").concat(e.id),type:"tab-link",id:e.id,title:e.title,active:0===a,arrow:!0},a))}),(0,r.jsx)("div",{className:"tab-content ps-3 border-start border-light",id:"platform-tabContent",children:i.items.map((e,a)=>(0,r.jsx)(A,{type:"tab-content",id:e.id,active:0===a,contentTitle:e.subnavTitle,contentDesc:e.subnavDescription,data:e.items,categoryID:i.id},a))})]})]}),c&&(0,r.jsx)(O,{type:"desktop-dropdown",id:"dropdownCompany",title:c.title,data:c,categoryID:c.id}),d&&(0,r.jsx)(C(),{className:"nav-item nav-link track-link-header",href:b,children:d.title}),u&&(0,r.jsx)(O,{type:"desktop-dropdown",id:"dropdownResources",title:u.title,data:u,categoryID:u.id}),m&&(0,r.jsx)(C(),{className:"d-none d-lg-block nav-item nav-link ps-0 pe-3 track-link-header",href:h,children:(0,r.jsx)("svg",{version:"1.1",xmlns:"http://www.w3.org/2000/svg",width:"20",height:"21",viewBox:"0 0 20 21",children:(0,r.jsx)("use",{href:"/img/navbar-icon/Search.svg#Search"})})}),p.map((e,a)=>(0,r.jsx)(N.Z,{className:"nav-item nav-link track-link-header",data:{url:e.path},children:e.title},a))]}),(0,r.jsxs)("div",{className:"collapse navbar-mobile justify-content-end",id:"Mobilenav",ref:t,children:[(0,r.jsxs)("div",{className:"col-12 d-flex justify-content-between d-lg-none navbar-mobile-dropdown align-items-center px-o5 px-gutter px-o5 px-sm-gutter",children:[(0,r.jsx)(D,{}),(0,r.jsx)("button",{className:"navbar-toggler text-secondary align-self-center",type:"button","data-bs-toggle":"collapse","data-bs-target":"#Mobilenav","aria-controls":"Mobilenav","aria-expanded":"false","aria-label":"Toggle navigation",children:(0,r.jsx)(_.Z,{name:"clear",width:40,height:40})})]}),(0,r.jsx)("div",{id:"mobileDropdownContainer",className:"navbar-nav border-top border-light overflow-x-hidden",children:(0,r.jsxs)("div",{className:"col-12 d-lg-none position-relative accordion",id:"mobileNavigationAccordion",children:[m&&(0,r.jsx)(F,{url:h,icon:"Search.svg#Search",type:"mobile-link",title:m.title}),i&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("a",{role:"button",type:"button","data-bs-toggle":"collapse",className:"py-2 mx-2 h6 d-block my-0 border-bottom border-light link-secondary track-link-header","data-bs-target":"#accordionMobilePlatform","aria-expanded":"false","aria-controls":"accordionMobilePlatform",children:[i.title," ",(0,r.jsx)(M,{name:"keyboard_arrow_down"})]}),(0,r.jsx)("div",{id:"accordionMobilePlatform",className:"accordion-collapse collapse","data-bs-parent":"#mobileNavigationAccordion","aria-labelledby":"accordionMobilePlatform",children:(0,r.jsx)("div",{className:"accordion-body dropdown-products",children:i.items.map((e,a)=>(0,r.jsx)(F,{icon:"".concat(e.id,".svg#").concat(e.id),type:"mobile-link-offcanvas",id:e.id,title:e.title,active:0===a,arrow:!0},a))})}),i.items.map((e,a)=>(0,r.jsx)(A,{type:"offcanvas-content",id:e.id,active:0===a,contentTitle:e.subnavTitle,contentDesc:e.subnavDescription,data:e.items,categoryID:i.id},a))]}),c&&(0,r.jsx)(O,{type:"mobile-accordion",id:"dropdownCompany",title:c.title,data:c,categoryID:c.id}),d&&(0,r.jsx)(F,{type:"mobile-link",title:d.title,url:b}),u&&(0,r.jsx)(O,{type:"mobile-accordion",id:"dropdownResources",title:u.title,data:u,categoryID:c.id}),p.map((e,a)=>(0,r.jsx)(F,{type:"mobile-link",title:e.title,url:e.path},a)),(0,r.jsx)("div",{className:"px-gutter py-2 pt-5",children:(0,r.jsx)(I,{})})]})})]}),(0,r.jsx)("div",{className:"d-none d-md-flex",children:(0,r.jsx)(I,{})}),(0,r.jsx)("div",{className:"d-lg-none d-flex ms-3",children:(0,r.jsx)("button",{className:"navbar-toggler text-secondary align-self-center",type:"button","data-bs-toggle":"collapse","data-bs-target":"#Mobilenav","aria-controls":"Mobilenav","aria-expanded":"false","aria-label":"Toggle navigation",children:(0,r.jsx)(_.Z,{name:"menu",width:40,height:40})})})]})})};var q=t(19101),B=t(68070),z=t(17137);let U={en:"English",cs:"Čeština",ja:"日本語",fr:"Fran\xe7ais"};var V=e=>{let{data:a,altLangs:t}=e,n=(0,s.useRouter)(),[l,i]=(0,o.useState)(n.locale);return(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"mb-1 ps-o5",children:null==a?void 0:a.title}),(0,r.jsx)(z.Z.Select,{className:"w-80 ms-o5","aria-label":"Language Selector",onChange:e=>{let a=e.target.value;i(a),e.target.blur(),(null==t?void 0:t.includes(a))?n.push(n.asPath,null,{locale:a}):n.push("/",null,{locale:a})},value:l,children:Object.entries(U).map(e=>{let[a,t]=e;return(0,r.jsx)("option",{value:a,children:t},a)})})]})};let J=e=>{let{data:a,altLangs:t}=e,n=new Date().getFullYear(),s=a.find(e=>"languageSelector"===e.id);return(0,r.jsxs)("footer",{className:"footer container-lg border-top border-light",children:[(0,r.jsxs)(q.Z,{className:"gap-md-3 pt-5 pb-4",children:[(0,r.jsxs)(B.Z,{xs:12,md:4,children:[(0,r.jsx)(C(),{className:"text-secondary track-link-footer","aria-label":"MapTiler",href:"/",children:(0,r.jsx)("svg",{className:"ms-ngutter",width:"165",height:"45",children:(0,r.jsx)("use",{href:"/styles/style/logo/maptiler-logo-adaptive.svg?123#maptilerLogo"})})}),(0,r.jsx)("p",{className:"text-secondary mb-1",children:"Follow\xa0us\xa0on"}),(0,r.jsxs)("div",{className:"social-circles ms-no5",children:[(0,r.jsx)("a",{target:"_blank",className:"twitter track-link-footer",href:"https://twitter.com/MapTiler","aria-label":"Twitter"}),(0,r.jsx)("a",{target:"_blank",className:"bsky track-link-footer",href:"https://bsky.app/profile/maptiler.bsky.social","aria-label":"Bluesky"}),(0,r.jsx)("a",{target:"_blank",className:"linkedin track-link-footer",href:"https://www.linkedin.com/company/maptiler","aria-label":"LinkedIn"}),(0,r.jsx)("a",{target:"_blank",className:"facebook track-link-footer",href:"https://www.facebook.com/maptiler/","aria-label":"Facebook"}),(0,r.jsx)("a",{target:"_blank",className:"instagram track-link-footer",href:"https://www.instagram.com/maptiler/","aria-label":"Instagram"}),(0,r.jsx)("a",{target:"_blank",className:"youtube track-link-footer",href:"https://www.youtube.com/channel/UCubcQeWuBKvqpMu172CLEXw","aria-label":"YouTube"})]}),(0,r.jsx)("div",{className:"ms-no5",children:(0,r.jsx)(V,{data:s,altLangs:t})})]}),null==a?void 0:a.map((e,a)=>{var t;return"languageSelector"!==e.id&&(0,r.jsxs)(B.Z,{xs:12,md:!0,className:"link-list",children:[(0,r.jsx)("h6",{children:e.title}),null===(t=e.items)||void 0===t?void 0:t.map((a,t)=>"INTERNAL"===a.type?(0,r.jsx)(C(),{href:a.path,className:"track-link-footer track-link-category-".concat(e.id),children:a.title},t):(0,r.jsx)("a",{href:a.path,className:"track-link-footer track-link-category-".concat(e.id),children:a.title},t))]},a)})]}),(0,r.jsxs)(q.Z,{className:"justify-content-center border-top border-light py-4",children:[(0,r.jsx)(B.Z,{md:6,className:"order-2 order-md-0 text-sm-center text-md-start",children:(0,r.jsxs)("p",{className:"text-secondary my-gutter",children:["\xa9\xa0",n,"\xa0MapTiler.\xa0All\xa0rights\xa0reserved."]})}),(0,r.jsxs)(B.Z,{md:6,className:"d-flex align-items-center gap-1 gap-sm-3 flex-column flex-sm-row justify-content-start justify-content-sm-center justify-content-md-end",children:[(0,r.jsx)(C(),{className:"text-secondary track-link-footer hover-primary me-auto mx-sm-0",href:"/privacy-policy/",children:"Privacy\xa0Policy"}),(0,r.jsx)(C(),{className:"text-secondary track-link-footer hover-primary me-auto mx-sm-0",href:"/terms/",children:"Terms\xa0of\xa0Use"}),(0,r.jsx)("a",{type:"button",className:"text-secondary track-link-footer hover-primary me-auto mx-sm-0",id:"csconsentlink",children:"Cookie\xa0settings"})]})]})]})};var X=e=>{var a;let{children:t,shape:l,settings:d,layoutData:m,altLangs:u}=e,p=d||{NavbarVisible:!0,FooterVisible:!0,ContainerEnabled:!0},{footerRes:b,navbarRes:g,contactFormRes:v}=m,x=(0,o.useContext)(c.k),f=(0,s.useRouter)(),j=f.asPath.split("?")[0],{seo:w,metaTitleSuffix:N}=x.attributes,{attributes:_}=w.metaImage.data,T="en"!==f.locale&&u&&!u.includes("en")?"/".concat(f.locale):"";return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.lX,{titleTemplate:"%s | ".concat(N),title:w.metaTitle,description:w.metaDescription,canonical:"https://www.maptiler.com"+T+j,twitter:{cardType:"summary_large_image"},openGraph:{title:w.metaTitle,description:w.metaDescription,...w.metaImage.data&&{images:[{url:_.url,width:_.width,height:_.height,alt:_.alternativeText}]}}}),p.NavbarVisible&&(0,r.jsx)(W,{data:g}),(0,r.jsx)(h,{data:null==l?void 0:null===(a=l.data)||void 0===a?void 0:a.attributes}),(0,r.jsx)(k,{children:p.ContainerEnabled?(0,r.jsx)(i.Z,{fluid:"lg",children:t}):t}),p.FooterVisible&&(0,r.jsx)(J,{data:b,altLangs:u}),v&&(0,r.jsx)(y,{data:v})]})}},45087:function(e,a,t){"use strict";t.d(a,{iv:function(){return l},jf:function(){return o},zL:function(){return s}});var r=t(85893);t(67294);var n=t(29156);let s=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),l=(e,a,t,r)=>{let s=window.MauticJS;s&&s.makeCORSRequest("POST","".concat((0,n.cl)("https://www.maptiler.com/m/mtc.js"),"/form/submit"),e,()=>{r&&r({message:a||"Message was successfully sent!",type:"success",show:!0})},()=>{r&&r({message:t||"There was an error sending your message!",type:"error",show:!0})})},o=e=>{let{message:a="notification",type:t="info",show:n}=e;return(0,r.jsx)("div",{id:"flashes",className:"flashes right ".concat(n?"":"closed"),children:(0,r.jsx)("div",{id:"emailNotification",className:"message ".concat(t),children:a})})}},29156:function(e,a,t){"use strict";async function r(e){return"clipboard"in navigator?await navigator.clipboard.writeText(e):document.execCommand("copy",!0,e)}t.d(a,{JU:function(){return s},TE:function(){return r},cl:function(){return n}});let n=e=>e.substring(0,e.lastIndexOf("/")),s=()=>navigator.language.split("-")[0]||"en"},11656:function(e){e.exports={headerShape:"styles_headerShape__7sTl9",svg:"styles_svg__S2bxx"}},24654:function(){}}]);