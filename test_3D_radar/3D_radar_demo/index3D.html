<!DOCTYPE html>
<html lang="en">
<head>
	<title>!!! KIEM TRA THAY DOI !!!</title>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">
	<style>
		body {
			color: #fff;
			font-family: Monospace;
			font-size: 13px;
			text-align: center;
			background-color: #050505;
			margin: 0px;
			overflow: hidden;
		}
		#info {
			position: absolute;
			top: 10px;
			width: 100%;
			text-align: center;
			z-index: 100;
			display: block;
			color: white;
		}
		a { color: skyblue; }
	</style>
</head>

<body>
	<div id="info">
		DAY LA PHIEN BAN MOI NHAT - NEU BAN THAY DONG NAY THI FILE DA DUOC CAP NHAT
	</div>

    <!-- SỬA 1: Cập nhật importmap để trỏ đến file local theo cấu trúc của bạn -->
	<script type="importmap">
		{
			"imports": {
				"three": "./build/three.module.js",
				"three/addons/": "./jsm/"
			}
		}
	</script>

	<script type="module">
		import * as THREE from 'three';
		import { GUI } from 'three/addons/libs/lil-gui.module.min.js';
		import { OrbitControls } from 'three/addons/controls/OrbitControls.js';

		let renderer, scene, camera, controls, material, mesh, clock;
		let volumeTextures = [];
        let texturesLoaded = false;

		const config = {
			renderSteps: 48,
			opacityFactor: 15.0,
			decodeMin: 0.0,
			decodeMax: 64.0,
			gradientSmooth: true,
            animationSpeed: 0.2,
            time: 0,
            play: true
		};

        const TILE_CONFIG = { columns: 3, rows: 4 };
        // SỬA 2: Dùng đúng tên file trong thư mục data của bạn
        const DATA_URLS = [
            'data/090000.jpg',
            'data/090600.jpg',
            'data/091200.jpg',
            'data/091800.jpg',
            'data/092400.jpg',
            'data/093000.jpg',
            'data/093600.jpg',
            'data/094200.jpg',
            'data/094800.jpg',
            'data/095400.jpg',
        ];

		init();

		function init() {
			scene = new THREE.Scene();
            clock = new THREE.Clock();

			renderer = new THREE.WebGLRenderer({ antialias: true });
			renderer.setPixelRatio(window.devicePixelRatio);
			renderer.setSize(window.innerWidth, window.innerHeight);
			document.body.appendChild(renderer.domElement);

			camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 2000);
			camera.position.set(0, 150, 400);

			controls = new OrbitControls(camera, renderer.domElement);
			controls.enablePan = false;
            controls.target.set(0, 50, 0);
			controls.update();

			const ambientLight = new THREE.AmbientLight(0xcccccc, 0.5);
			scene.add(ambientLight);
			const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(0.5, 1, 0.5);
			scene.add(directionalLight);

            const floor = new THREE.Mesh(
                new THREE.PlaneGeometry(500, 500),
                new THREE.MeshStandardMaterial({ color: 0x333333, roughness: 0.8, metalness: 0.2 })
            );
            floor.rotation.x = -Math.PI / 2;
            scene.add(floor);
            
            const grid = new THREE.GridHelper(500, 20, 0x555555, 0x555555);
            grid.material.opacity = 0.5;
            grid.material.transparent = true;
            scene.add(grid);

            loadAllVolumes().then(([firstTexture, volumeSize]) => {
                
                const colorStops = [
                    { value: 0,  color: [0, 0, 0, 0] }, { value: 5,  color: [56, 0, 112, 20] },
                    { value: 10, color: [6, 0, 240, 40] }, { value: 15, color: [0, 108, 192, 60] },
                    { value: 20, color: [0, 160, 0, 80] }, { value: 25, color: [0, 190, 0, 110] },
                    { value: 30, color: [50, 216, 0, 130] }, { value: 35, color: [220, 220, 0, 150] },
                    { value: 40, color: [255, 176, 0, 170] }, { value: 45, color: [255, 132, 0, 190] },
                    { value: 50, color: [255, 50, 0, 210] }, { value: 55, color: [170, 0, 0, 240] },
                    { value: 60, color: [255, 255, 255, 255] },
                ];
                
                const stopValues = colorStops.map(s => s.value);
                const stopColors = colorStops.map(s => new THREE.Vector4(s.color[0]/255, s.color[1]/255, s.color[2]/255, s.color[3]/255));
                
				material = new THREE.ShaderMaterial({
					uniforms: {
						u_data_0: { value: firstTexture }, u_data_1: { value: firstTexture },
                        u_interpolation: { value: 0.0 },
						u_size: { value: new THREE.Vector3(volumeSize.width, volumeSize.height, volumeSize.depth) },
						u_render_steps: { value: config.renderSteps }, u_opacity_factor: { value: config.opacityFactor },
						u_decode_min: { value: config.decodeMin }, u_decode_max: { value: config.decodeMax },
                        u_num_color_stops: { value: colorStops.length }, u_color_stops_values: { value: stopValues },
                        u_color_stops_colors: { value: stopColors }, u_smooth_gradient: { value: config.gradientSmooth },
					},
					vertexShader: VERTEX_SHADER,
					fragmentShader: FRAGMENT_SHADER,
					side: THREE.BackSide,
                    transparent: true,
                    depthWrite: false,
				});

                const boxHeight = 100;
                const boxScale = boxHeight / volumeSize.height;
				const geometry = new THREE.BoxGeometry(volumeSize.width * boxScale, boxHeight, volumeSize.depth * boxScale);
				geometry.translate(0, boxHeight / 2, 0);

				mesh = new THREE.Mesh(geometry, material);
				scene.add(mesh);
                
                texturesLoaded = true;
                setupGUI();
                animate();
			}).catch(error => {
                console.error("Failed to load volume data:", error);
                document.getElementById('info').innerHTML = `Lỗi: Không thể tải dữ liệu. <br/> ${error}`;
            });
			window.addEventListener('resize', onWindowResize);
		}
        
        function loadVolumeFromJPG(url, tileConfig) {
            return new Promise((resolve, reject) => {
                const loader = new THREE.TextureLoader();
                loader.load(url, (imageTexture) => {
                    const image = imageTexture.image;
                    const canvas = document.createElement('canvas');
                    canvas.width = image.width;
                    canvas.height = image.height;
                    
                    // SỬA 3: Thêm thuộc tính willReadFrequently để tắt cảnh báo
                    const context = canvas.getContext('2d', { willReadFrequently: true });
                    context.drawImage(image, 0, 0);
                    
                    const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
                    const sliceWidth = canvas.width / tileConfig.columns;
                    const sliceHeight = canvas.height / tileConfig.rows;
                    const numSlices = tileConfig.columns * tileConfig.rows;
                    const volumeData = new Float32Array(sliceWidth * sliceHeight * numSlices);

                    for (let d = 0; d < numSlices; d++) {
                        const tileX = d % tileConfig.columns;
                        const tileY = Math.floor(d / tileConfig.columns);
                        for (let y = 0; y < sliceHeight; y++) {
                            for (let x = 0; x < sliceWidth; x++) {
                                const srcX = tileX * sliceWidth + x;
                                const srcY = tileY * sliceHeight + y;
                                const srcIndex = (srcY * canvas.width + srcX) * 4;
                                const destIndex = (d * sliceWidth * sliceHeight) + (y * sliceWidth) + x;
                                volumeData[destIndex] = imageData.data[srcIndex] / 255.0;
                            }
                        }
                    }

                    const texture = new THREE.Data3DTexture(volumeData, sliceWidth, sliceHeight, numSlices);
                    texture.format = THREE.RedFormat;
                    texture.type = THREE.FloatType;
                    texture.minFilter = THREE.LinearFilter;
                    texture.magFilter = THREE.LinearFilter;
                    texture.unpackAlignment = 1;
                    texture.needsUpdate = true;
                    
                    resolve({ texture, size: { width: sliceWidth, height: sliceHeight, depth: numSlices } });

                }, undefined, (err) => reject(`Could not load image: ${url}`));
            });
        }
        
        async function loadAllVolumes() {
            const promises = DATA_URLS.map(url => loadVolumeFromJPG(url, TILE_CONFIG));
            const results = await Promise.all(promises);
            volumeTextures = results.map(r => r.texture);
            return [volumeTextures[0], results[0].size];
        }

		function setupGUI() {
			const gui = new GUI();
            const renderFolder = gui.addFolder('Rendering');
			renderFolder.add(config, 'renderSteps', 8, 128, 1).onChange(updateUniforms);
			renderFolder.add(config, 'opacityFactor', 0, 50, 0.1).name('Opacity').onChange(updateUniforms);
            const dataFolder = gui.addFolder('Data Decoding');
            dataFolder.add(config, 'decodeMin', 0, 100, 1).name('Decode Min').onChange(updateUniforms);
            dataFolder.add(config, 'decodeMax', 0, 100, 1).name('Decode Max').onChange(updateUniforms);
            dataFolder.add(config, 'gradientSmooth').name('Smooth Gradient').onChange(updateUniforms);
            const animFolder = gui.addFolder('Animation');
            animFolder.add(config, 'play').name('Play/Pause');
            animFolder.add(config, 'animationSpeed', 0, 1, 0.01).name('Speed');
            animFolder.add(config, 'time', 0, DATA_URLS.length - 1, 0.01).name('Timeline').listen();
		}
        
		function updateUniforms() {
            if (!material) return;
			material.uniforms.u_render_steps.value = config.renderSteps;
			material.uniforms.u_opacity_factor.value = config.opacityFactor;
			material.uniforms.u_decode_min.value = config.decodeMin;
            material.uniforms.u_decode_max.value = config.decodeMax;
            material.uniforms.u_smooth_gradient.value = config.gradientSmooth;
		}

		function onWindowResize() {
			camera.aspect = window.innerWidth / window.innerHeight;
			camera.updateProjectionMatrix();
			renderer.setSize(window.innerWidth, window.innerHeight);
		}

		function animate() {
			requestAnimationFrame(animate);
            if (texturesLoaded && config.play) {
                const delta = clock.getDelta();
                config.time += delta * config.animationSpeed * (DATA_URLS.length);
                if (config.time > DATA_URLS.length - 1) {
                    config.time = 0;
                }
            }
            if (texturesLoaded && material) {
                const time = config.time;
                const index0 = Math.floor(time) % volumeTextures.length;
                const index1 = (index0 + 1) % volumeTextures.length;
                const interpolation = time - Math.floor(time);
                material.uniforms.u_data_0.value = volumeTextures[index0];
                material.uniforms.u_data_1.value = volumeTextures[index1];
                material.uniforms.u_interpolation.value = interpolation;
            }
			controls.update();
			renderer.render(scene, camera);
		}

        // --- SHADERS (Đã sửa lỗi) ---

        // SỬA 4: Xóa các khai báo uniform/attribute mặc định khỏi Vertex Shader
        const VERTEX_SHADER = /* glsl */`
            varying vec3 v_world_position;
            varying vec3 v_view_direction;

            void main() {
                // Các biến 'position', 'modelMatrix', 'cameraPosition', 
                // 'projectionMatrix', 'modelViewMatrix' đã có sẵn.
                v_world_position = (modelMatrix * vec4(position, 1.0)).xyz;
                v_view_direction = v_world_position - cameraPosition;
                gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
            }
        `;

        const FRAGMENT_SHADER = /* glsl */`
            precision highp float;
            precision highp sampler3D;
            varying vec3 v_world_position;
            varying vec3 v_view_direction;
            uniform sampler3D u_data_0;
            uniform sampler3D u_data_1;
            uniform float u_interpolation;
            uniform vec3 u_size;
            uniform int u_render_steps;
            uniform float u_opacity_factor;
            uniform float u_decode_min;
            uniform float u_decode_max;
            const int MAX_STOPS = 16;
            uniform int u_num_color_stops;
            uniform float u_color_stops_values[MAX_STOPS];
            uniform vec4 u_color_stops_colors[MAX_STOPS];
            uniform bool u_smooth_gradient;
            vec4 getColorFromGradient(float value) {
                if (value <= u_color_stops_values[0]) return u_color_stops_colors[0];
                for (int i = 1; i < MAX_STOPS; i++) {
                    if (i >= u_num_color_stops) break;
                    if (value <= u_color_stops_values[i]) {
                        float t = (value - u_color_stops_values[i-1]) / (u_color_stops_values[i] - u_color_stops_values[i-1]);
                        if (u_smooth_gradient) return mix(u_color_stops_colors[i-1], u_color_stops_colors[i], t);
                        else return u_color_stops_colors[i-1];
                    }
                }
                return u_color_stops_colors[u_num_color_stops - 1];
            }
            void main() {
                vec3 rayDir = normalize(v_view_direction);
                vec3 invDir = 1.0 / rayDir;
                vec3 tmin = (-0.5 - v_world_position) * invDir;
                vec3 tmax = (0.5 - v_world_position) * invDir;
                vec3 t1 = min(tmin, tmax);
                vec3 t2 = max(tmin, tmax);
                float tnear = max(max(t1.x, t1.y), t1.z);
                float tfar = min(min(t2.x, t2.y), t2.z);
                if (tnear >= tfar) discard;
                tnear = max(tnear, 0.0);
                vec4 accumulatedColor = vec4(0.0);
                float stepSize = (tfar - tnear) / float(u_render_steps);
                for (int i = 0; i < u_render_steps; i++) {
                    float t = tnear + float(i) * stepSize;
                    vec3 currentPos = v_world_position + t * rayDir;
                    vec3 texPos = currentPos + 0.5;
                    float value0 = texture(u_data_0, texPos).r;
                    float value1 = texture(u_data_1, texPos).r;
                    float rawValue = mix(value0, value1, u_interpolation);
                    float decodedValue = u_decode_min + rawValue * (u_decode_max - u_decode_min);
                    vec4 sampleColor = getColorFromGradient(decodedValue);
                    sampleColor.a *= u_opacity_factor / float(u_render_steps);
                    accumulatedColor.rgb += sampleColor.rgb * sampleColor.a * (1.0 - accumulatedColor.a);
                    accumulatedColor.a += sampleColor.a * (1.0 - accumulatedColor.a);
                    if (accumulatedColor.a > 0.99) break;
                }
                gl_FragColor = accumulatedColor;
            }
        `;
	</script>
</body>
</html>