<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Radar - Alpha Masking Logic</title>
    <style>
        body { margin: 0; overflow: hidden; background-color: #111; color: #fff; font-family: monospace; }
        canvas { display: block; }
        #loader {
            position: absolute; top: 50%; left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.5em; text-align: center;
            background-color: rgba(0,0,0,0.8); padding: 20px; border-radius: 10px;
            width: 500px;
        }
    </style>
</head>
<body>
    <div id="loader">
        <p id="loader-text">Đang tải dữ liệu radar...</p>
        <p id="loader-status" style="font-size: 0.7em;"></p>
    </div>

    <script type="importmap">
        {
            "imports": {
                "three": "https://unpkg.com/three@0.165.0/build/three.module.js",
                "three/addons/": "https://unpkg.com/three@0.165.0/examples/jsm/",
                "lil-gui": "https://unpkg.com/lil-gui@0.19.2/dist/lil-gui.esm.js"
            }
        }
    </script>

    <script type="module">
        import * as THREE from 'three';
        import WebGPURenderer from 'three/addons/renderers/webgpu/WebGPURenderer.js';
        import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
        import GUI from 'lil-gui';

        // --- CẤU HÌNH ---
        const RADAR_IMAGE_URL = './radar_data/1.png';
        const IMAGE_DIMENSIONS = { width: 2310, height: 2310 };
        const TOTAL_LAYERS = 120; // Tăng số lớp để siêu mịn

        // --- BIẾN TOÀN CỤC ---
        let scene, camera, renderer, controls, gui;
        let floorMesh, gridHelper, roiBox, cloudLayersGroup;
        let colorTexture = null;
        let alphaMaskCanvases = [];
        let isDataReady = false;
        let isUpdateNeeded = true;
        const loaderTextEl = document.getElementById('loader-text');
        const loaderStatusEl = document.getElementById('loader-status');

        const dbzMap = [
            { r: 103, g: 213, b: 252, dbz: 10 }, { r: 2, g: 109, b: 249, dbz: 15 },
            { r: 6, g: 70, b: 248, dbz: 20 }, { r: 167, g: 251, b: 132, dbz: 25 },
            { r: 87, g: 250, b: 36, dbz: 30 }, { r: 6, g: 224, b: 50, dbz: 35 },
            { r: 255, g: 216, b: 0, dbz: 40 }, { r: 255, g: 166, b: 0, dbz: 45 },
            { r: 254, g: 130, b: 19, dbz: 55 }, { r: 255, g: 28, b: 0, dbz: 60 },
            { r: 204, g: 1, b: 113, dbz: 65 }, { r: 153, g: 0, b: 204, dbz: 70 },
            { r: 0, g: 0, b: 0, dbz: 75 }
        ];
        const colorCache = new Map();

        const params = {
            roiX: Math.floor(IMAGE_DIMENSIONS.width / 2 - 200),
            roiY: Math.floor(IMAGE_DIMENSIONS.height / 2 - 200),
            roiWidth: 400,
            roiHeight: 400,
            startHeight: 0, // <<< YÊU CẦU MỚI: Độ cao chân mây
            maxCloudHeight: 250,
            layerOpacity: 1.0, // Đặt là 1.0 vì alphaTest sẽ xử lý
            floorColor: '#222222',
            gridColor: '#444444',
        };

        init();
        
        async function init() {
            scene = new THREE.Scene();
            camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 1, 5000);
            camera.position.set(0, 800, 1500);

            renderer = new WebGPURenderer({ antialias: true });
            renderer.setPixelRatio(window.devicePixelRatio);
            renderer.setSize(window.innerWidth, window.innerHeight);
            document.body.appendChild(renderer.domElement);
            await renderer.init();

            controls = new OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.target.set(0, params.maxCloudHeight / 4, 0);

            gridHelper = new THREE.GridHelper(IMAGE_DIMENSIONS.width, 50, params.gridColor, params.gridColor);
            gridHelper.position.y = 0.1; scene.add(gridHelper);

            const floorGeometry = new THREE.PlaneGeometry(IMAGE_DIMENSIONS.width, IMAGE_DIMENSIONS.height);
            const floorMaterial = new THREE.MeshBasicMaterial({ color: params.floorColor, side: THREE.DoubleSide, transparent: true, opacity: 0.5 });
            floorMesh = new THREE.Mesh(floorGeometry, floorMaterial);
            floorMesh.rotation.x = -Math.PI / 2; scene.add(floorMesh);
            
            const roiGeometry = new THREE.BufferGeometry();
            roiGeometry.setAttribute('position', new THREE.BufferAttribute(new Float32Array(5 * 3), 3));
            roiBox = new THREE.LineLoop(roiGeometry, new THREE.LineBasicMaterial({ color: 0xffffff, fog: false }));
            roiBox.position.y = 0.2; scene.add(roiBox);

            cloudLayersGroup = new THREE.Group(); scene.add(cloudLayersGroup);

            setupGUI();
            await loadImageAndPrepareData();
            
            animate();
            window.addEventListener('resize', onWindowResize);
        }

        function setupGUI() {
            gui = new GUI();
            const roiFolder = gui.addFolder('Region of Interest (ROI)');
            roiFolder.add(params, 'roiX', 0, IMAGE_DIMENSIONS.width, 1).name('Vị trí X').listen().onChange(onRoiChange);
            roiFolder.add(params, 'roiY', 0, IMAGE_DIMENSIONS.height, 1).name('Vị trí Y').listen().onChange(onRoiChange);
            roiFolder.add(params, 'roiWidth', 100, 1000, 10).name('Chiều rộng').onChange(onRoiChange);
            roiFolder.add(params, 'roiHeight', 100, 1000, 10).name('Chiều cao').onChange(onRoiChange);
            
            const cloudFolder = gui.addFolder('Cloud Properties');
            cloudFolder.add(params, 'startHeight', 0, 200, 1).name('Độ cao chân mây').onChange(() => isUpdateNeeded = true);
            cloudFolder.add(params, 'maxCloudHeight', 50, 1000, 5).name('Chiều cao tối đa').onChange(() => isUpdateNeeded = true);
            cloudFolder.add(params, 'layerOpacity', 0.1, 1.0, 0.05).name('Độ trong suốt').onChange(() => isUpdateNeeded = true);
            
            const sceneFolder = gui.addFolder('Scene Settings');
            sceneFolder.addColor(params, 'floorColor').name('Màu nền').onChange(val => floorMesh.material.color.set(val));
            sceneFolder.addColor(params, 'gridColor').name('Màu lưới').onChange(val => gridHelper.material.color.set(val));
        }

        function onRoiChange() {
            params.roiX = Math.max(0, Math.min(params.roiX, IMAGE_DIMENSIONS.width - params.roiWidth));
            params.roiY = Math.max(0, Math.min(params.roiY, IMAGE_DIMENSIONS.height - params.roiHeight));
            isUpdateNeeded = true;
            updateRoiBox();
            gui.controllers.forEach(c => c.updateDisplay());
        }

        async function loadImageAndPrepareData() {
            colorTexture = await new THREE.TextureLoader().loadAsync(RADAR_IMAGE_URL);
            floorMesh.material.map = colorTexture; // Hiển thị ảnh radar lên sàn
            floorMesh.material.needsUpdate = true;

            const image = colorTexture.image;
            const canvas = document.createElement('canvas');
            canvas.width = image.width;
            canvas.height = image.height;
            const context = canvas.getContext('2d', { willReadFrequently: true });
            context.drawImage(image, 0, 0);
            const imageData = context.getImageData(0, 0, image.width, image.height);

            loaderTextEl.innerHTML = "Đang xử lý Mặt nạ Alpha...";
            loaderStatusEl.textContent = `Việc này có thể mất vài phút. XIN HÃY KIÊN NHẪN.`;

            await new Promise(resolve => setTimeout(() => {
                prepareAlphaMasks(imageData);
                setupCloudLayers();
                isDataReady = true;
                document.getElementById('loader').style.display = 'none';
                updateRoiBox();
                updateClouds();
                resolve();
            }, 50));
        }
        
        function getDBZFromColor(r, g, b) {
            const key = (r << 16) | (g << 8) | b;
            if (colorCache.has(key)) return colorCache.get(key);
            let minDistanceSq = Infinity, foundDBZ = 0;
            for (const colorInfo of dbzMap) {
                const distSq = (r - colorInfo.r)**2 + (g - colorInfo.g)**2 + (b - colorInfo.b)**2;
                if (distSq < minDistanceSq) {
                    minDistanceSq = distSq;
                    foundDBZ = colorInfo.dbz;
                }
            }
            colorCache.set(key, foundDBZ);
            return foundDBZ;
        }

        // TẠO MẶT NẠ ĐEN-TRẮNG
        function prepareAlphaMasks(imageData) {
            console.time('prepareAlphaMasks');
            const minDBZ = 10, maxDBZ = 75;

            const canvases = Array.from({ length: TOTAL_LAYERS }, () => {
                const canvas = document.createElement('canvas');
                canvas.width = imageData.width;
                canvas.height = imageData.height;
                return canvas;
            });
            const contexts = canvases.map(c => c.getContext('2d'));

            for (let y = 0; y < imageData.height; y++) {
                for (let x = 0; x < imageData.width; x++) {
                    const i = (y * imageData.width + x) * 4;
                    const a = imageData.data[i+3];
                    if (a < 50) continue;
                    
                    const pixelDBZ = getDBZFromColor(imageData.data[i], imageData.data[i+1], imageData.data[i+2]);
                    if (pixelDBZ < minDBZ) continue;

                    const heightRatio = (pixelDBZ - minDBZ) / (maxDBZ - minDBZ);
                    const maxLayerIndex = Math.floor(heightRatio * (TOTAL_LAYERS - 1));

                    for (let layerIndex = 0; layerIndex <= maxLayerIndex; layerIndex++) {
                        contexts[layerIndex].fillStyle = '#FFFFFF'; // Màu trắng = Hiển thị
                        contexts[layerIndex].fillRect(x, y, 1, 1);
                    }
                }
            }
            alphaMaskCanvases = canvases;
            console.timeEnd('prepareAlphaMasks');
        }

        function setupCloudLayers() {
            const sharedGeometry = new THREE.PlaneGeometry(1, 1);
            
            for (let i = 0; i < TOTAL_LAYERS; i++) {
                const alphaTexture = new THREE.CanvasTexture(alphaMaskCanvases[i]);
                alphaTexture.minFilter = THREE.LinearFilter;

                const material = new THREE.MeshBasicMaterial({
                    map: colorTexture, // <<< DÙNG CHUNG 1 ẢNH MÀU
                    alphaMap: alphaTexture, // <<< DÙNG MẶT NẠ RIÊNG
                    transparent: true,
                    depthWrite: false,
                    alphaTest: 0.5, // <<< LOẠI BỎ HIỆU ỨNG MỜ
                });
                const layerMesh = new THREE.Mesh(sharedGeometry, material);
                layerMesh.rotation.x = -Math.PI / 2;
                layerMesh.visible = false;
                cloudLayersGroup.add(layerMesh);
            }
        }
        
        function updateRoiBox() {
            const w = IMAGE_DIMENSIONS.width, h = IMAGE_DIMENSIONS.height;
            const x1 = params.roiX - w / 2, z1 = -(params.roiY - h / 2);
            const x2 = x1 + params.roiWidth, z2 = z1 - params.roiHeight;
            const positions = roiBox.geometry.attributes.position.array;
            [positions[0], positions[2]] = [x1, z1];
            [positions[3], positions[5]] = [x2, z1];
            [positions[6], positions[8]] = [x2, z2];
            [positions[9], positions[11]] = [x1, z2];
            [positions[12], positions[14]] = [x1, z1];
            roiBox.geometry.attributes.position.needsUpdate = true;
        }

        function updateClouds() {
            if (!isDataReady) return;
            
            const layerSpacing = params.maxCloudHeight / TOTAL_LAYERS;
            const w = IMAGE_DIMENSIONS.width, h = IMAGE_DIMENSIONS.height;
            const posX = params.roiX + params.roiWidth / 2 - w / 2;
            const posZ = -(params.roiY + params.roiHeight / 2 - h / 2);
            const uv_offsetX = params.roiX / w;
            const uv_offsetY = 1.0 - (params.roiY + params.roiHeight) / h;
            const uv_repeatX = params.roiWidth / w;
            const uv_repeatY = params.roiHeight / h;

            for (let i = 0; i < TOTAL_LAYERS; i++) {
                const layerMesh = cloudLayersGroup.children[i];
                // <<< ÁP DỤNG ĐỘ CAO CHÂN MÂY
                layerMesh.position.set(posX, params.startHeight + (i * layerSpacing), posZ);
                layerMesh.scale.set(params.roiWidth, params.roiHeight, 1);
                
                layerMesh.material.opacity = params.layerOpacity;

                // Cập nhật UV cho cả map và alphaMap
                layerMesh.material.map.offset.set(uv_offsetX, uv_offsetY);
                layerMesh.material.map.repeat.set(uv_repeatX, uv_repeatY);
                layerMesh.material.alphaMap.offset.set(uv_offsetX, uv_offsetY);
                layerMesh.material.alphaMap.repeat.set(uv_repeatX, uv_repeatY);
                
                layerMesh.visible = true;
            }
            isUpdateNeeded = false;
        }

        function animate() {
            requestAnimationFrame(animate);
            controls.update();
            if (isUpdateNeeded) {
                updateClouds();
            }
            renderer.render(scene, camera);
        }

        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }
    </script>
</body>
</html>