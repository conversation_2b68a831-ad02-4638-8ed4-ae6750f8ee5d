import json
import csv

input_json = '/Users/<USER>/Desktop/data/web_code/code2/data/lightning_test.json'
output_csv = '/Users/<USER>/Desktop/data/web_code/code2/data/lightning.csv'

with open(input_json, 'r', encoding='utf-8') as f:
    data = json.load(f)

header = ['time', 'giatri', 'sensor', 'dof', 'lat', 'long', 'style', 'loaiset']

features = data.get('features', [])
rows = []

# Duyệt từng nhóm 3 phần tử trong features
# Hàm chuyển epoch ms sang định dạng 'YYYY-MM-DD HH:MM:SS' (UTC+7)
def convert_time(epoch_ms):
    from datetime import datetime, timezone, timedelta
    try:
        # Chuyển về UTC (giảm 7 tiếng)
        dt = datetime.fromtimestamp(epoch_ms / 1000, tz=timezone.utc)
        return dt.strftime('%Y-%m-%d %H:%M:%S')
    except Exception:
        return str(epoch_ms)

for i in range(0, len(features), 3):
    try:
        time_raw = features[i]
        lat = features[i+1]
        lon = features[i+2]
        time_str = convert_time(time_raw)
        row = {
            'time': time_str,
            'giatri': 'N/A',
            'sensor': 'N/A',
            'dof': 'N/A',
            'lat': lat,
            'long': lon,
            'style': '5',
            'loaiset': '1'
        }
        rows.append(row)
    except IndexError:
        continue

with open(output_csv, 'w', newline='', encoding='utf-8') as f:
    writer = csv.DictWriter(f, fieldnames=header)
    writer.writeheader()
    writer.writerows(rows)