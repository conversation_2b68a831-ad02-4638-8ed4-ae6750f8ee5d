/* --- START OF FILE style.css --- */

/* --- General --- */
html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    font-family: Arial, sans-serif;
}

#map {
    width: 100%;
    height: 100%;
    position: relative; /* <PERSON>uan trọng để định vị các control con tuyệt đối */
}

/* --- MapLibre Controls (Common Base Styling) --- */
/* Áp dụng style nền tảng cho tất cả control của MapLibre VÀ control tự tạo */
.maplibregl-ctrl-group,
.maplibregl-ctrl,
#legend, /* Áp dụng cho legend */
#layer-controls, /* Áp dụng cho điều khiển lớp dữ liệu */
#time-selector, /* Áp dụng cho menu chọn thời gian */
.forecast-popup, /* Áp dụng cho bảng dự báo */
#forecast-toggle-button /* Á<PERSON> dụng cho nút mở dự báo */
{
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid #ccc;
    border-radius: 5px;
    box-shadow: 0 1px 5px rgba(0,0,0,0.4);
    padding: 6px 8px;
    font-size: 11px;
    line-height: 1.4;
    z-index: 800; /* z-index cơ bản */
    clear: both;
    box-sizing: border-box;
}

/* --- Vị trí các Control MẶC ĐỊNH của MapLibre --- */
/* MapLibre tự xử lý vị trí các control nó tạo ra khi dùng .addTo(map) */
.maplibregl-ctrl-top-left { top: 10px; left: 10px; z-index: 1000; }
.maplibregl-ctrl-top-right { top: 10px; right: 10px; z-index: 1000; }
.maplibregl-ctrl-bottom-left { bottom: 10px; left: 10px; z-index: 1000; }
.maplibregl-ctrl-bottom-right { bottom: 10px; right: 10px; z-index: 1000; }

/* --- MapLibre Layer Controls --- */
.maplibregl-ctrl-group {
    max-height: 70vh;
    overflow-y: auto;
    border: 1px solid #bbb; /* Rõ ràng hơn */
}

/* Style cho các nút điều khiển của MapLibre */
.maplibregl-ctrl button {
    width: 30px;
    height: 30px;
    background-position: center;
    background-repeat: no-repeat;
    cursor: pointer;
}

/* --- MapLibre Geocoder --- */
.maplibregl-ctrl-geocoder {
    min-width: 38px;
    font-size: 14px;
    line-height: 20px;
    font-family: Arial, sans-serif;
    position: relative;
    background-color: #fff;
    width: 240px;
    min-width: 240px;
    z-index: 1;
    border-radius: 4px;
    transition: width .25s, min-width .25s;
    box-shadow: 0 0 10px 2px rgba(0,0,0,.1);
}

.maplibregl-ctrl-geocoder--input {
    padding-left: 32px !important;

    font-size: 14px;
    padding: 6px 35px 6px 10px;
    height: 38px;
    width: 100%;
    border: 0;
    background-color: transparent;
    margin: 0;
    color: rgba(0,0,0,.75);
    outline: none;
    box-sizing: border-box;
}

.maplibregl-ctrl-geocoder--icon {
    position: absolute;
    top: 10px;
    right: 10px;
    pointer-events: none;
}

.maplibregl-ctrl-geocoder--icon-search {
    position: absolute;
    top: 50%;
    left: 10px;
    transform: translateY(-50%);
    pointer-events: none;
}


.maplibregl-ctrl-geocoder--button {
    padding: 0;
    margin: 0;
    border: none;
    cursor: pointer;
    background-color: transparent;
    line-height: 1;
}

.maplibregl-ctrl-geocoder--results {
    background-color: #fff;
    width: 100%;
    position: absolute;
    top: 100%;
    left: 0;
    margin-top: 2px;
    padding: 0;
    max-height: 300px;
    overflow: auto;
    border-radius: 0 0 4px 4px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    list-style: none;
    z-index: 1005;
}

.maplibregl-ctrl-geocoder--result {
    padding: 8px 10px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    font-size: 13px;
    line-height: 1.4;
    color: #333;
    transition: background-color 0.2s ease;
}

.maplibregl-ctrl-geocoder--result:hover,
.maplibregl-ctrl-geocoder--result.active {
    background-color: #f5f5f5;
}

.maplibregl-ctrl-geocoder--result:last-child {
    border-bottom: none;
}

.maplibregl-ctrl-geocoder--suggestion-title {
    font-weight: bold;
}

.maplibregl-ctrl-geocoder--suggestion-address {
    color: #666;
}


/* --- Chú thích (Legend) (Dưới menu bật tắt layer - Định vị bằng CSS) --- */
#legend {
    position: absolute; /* Định vị tuyệt đối so với #map container */
    top: 180px; /* Đặt dưới menu bật tắt layer */
    right: 10px; 
    width: auto;
    min-width: 170px;
    max-width: 220px;
    max-height: 80vh; /* Chiều cao tối đa khi mở rộng */
    overflow: hidden; /* Ẩn overflow ban đầu */
    padding: 8px 10px;
    font-size: 10px;
    z-index: 1000; /* Đảm bảo hiển thị trên các lớp khác */
    transition: max-height 0.3s ease-in-out, padding 0.3s ease-in-out, min-width 0.3s ease-in-out; /* Thêm hiệu ứng */
    /* Kế thừa background, border, shadow từ .leaflet-control */
    z-index: 900; /* Đảm bảo legend dưới Geocoder results */
}

/* Nút thu nhỏ/mở rộng Legend */
#legend-toggle-button {
    position: absolute;
    top: 5px;
    right: 5px;
    background: none;
    border: none;
    font-size: 18px;
    font-weight: bold;
    color: #888;
    cursor: pointer;
    padding: 0 4px;
    line-height: 1;
    z-index: 10; /* Nổi trên nội dung legend */
}
#legend-toggle-button:hover {
    color: #333;
}

/* Container nội dung Legend */
#legend-content {
    display: block; /* Mặc định hiển thị */
    margin-top: 5px; /* Khoảng cách với tiêu đề/nút */
    max-height: 75vh; /* Giới hạn chiều cao nội dung */
    overflow-y: auto; /* Chỉ cuộn nội dung khi cần */
    opacity: 1;
    transition: max-height 0.3s ease-in-out, opacity 0.2s ease-in-out; /* Hiệu ứng */
}

/* Thanh timeline */
#timeline-container {
    position: absolute;
    bottom: 18px;
    left: 50%;
    transform: translateX(-50%);
    width: 390px;
    max-width: 92vw;
    margin: 0;
    padding: 14px 14px 18px 14px;
    min-height: 66px;
    z-index: 900;
    text-align: center;
    background: rgba(255,255,255,0.45);
    border-radius: 16px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.09);
    backdrop-filter: blur(6px);
}

#timeline-container > .progress-bar, #timeline-container .timeline-progress, #timeline-container .timeline-bg {
    display: none !important;
}

#timeline-container hr {
    display: none !important;
}

#timeline-slider::-webkit-slider-runnable-track {
    background: #e0e0e0 !important;
    height: 4px !important;
    border: none !important;
    box-shadow: none !important;
}
#timeline-slider::-moz-range-track {
    background: #e0e0e0 !important;
    height: 4px !important;
    border: none !important;
    box-shadow: none !important;
}
#timeline-slider::-ms-fill-lower,
#timeline-slider::-ms-fill-upper {
    background: #e0e0e0 !important;
    height: 4px !important;
    border: none !important;
    box-shadow: none !important;
}
#timeline-slider {
    background: #e0e0e0 !important;
    box-shadow: none !important;
    border: none !important;
}


#timeline-container,
#timeline-slider-container,
#timeline-header,
#timeline-labels {
    overflow-x: hidden !important;
}

#timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2px;
}

#timeline-header h4 {
    margin: 0;
    font-size: 14px;
    font-weight: bold;
    color: #333;
}

#timeline-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

#play-pause-btn {
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

#play-pause-btn:hover {
    background-color: #3e8e41;
}

#play-pause-btn.paused:before {
    content: '\25B6'; /* Play symbol */
}

#play-pause-btn.playing:before {
    content: '\275A\275A'; /* Pause symbol */
    font-size: 12px;
    letter-spacing: -3px;
}

#current-time-display {
    font-size: 13px;
    font-weight: bold;
    color: #333;
    min-width: 140px;
    text-align: left;
}

#timeline-slider-container {
    width: 100%;
    padding: 0;
}

#timeline-slider {
    width: 100%;
    margin: 0;
    padding: 0;
    height: 8px;
    -webkit-appearance: none;
    appearance: none;
    background: #e0e0e0 !important;
    border: none;
    outline: none;
    box-shadow: none;
    border-radius: 4px;
}

#timeline-slider::-webkit-slider-runnable-track {
    background: #e0e0e0 !important;
    height: 8px !important;
    border: none !important;
    box-shadow: none !important;
}
#timeline-slider::-moz-range-track {
    background: #e0e0e0 !important;
    height: 8px !important;
    border: none !important;
    box-shadow: none !important;
}
#timeline-slider::-ms-fill-lower,
#timeline-slider::-ms-fill-upper {
    background: #e0e0e0 !important;
    height: 8px !important;
    border: none !important;
    box-shadow: none !important;
}

#timeline-slider::-moz-range-thumb {
    margin-top: -2px !important;
}
#timeline-slider::-ms-thumb {
    margin-top: -2px !important;
}


#timeline-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #1976d2;
    cursor: pointer;
    border: 1.5px solid #fff;
    box-shadow: 0 0 2px rgba(0,0,0,0.08);
    margin-top: -2px !important;
}
#timeline-slider::-webkit-slider-thumb:after, #timeline-slider::-webkit-slider-thumb:before {
    display: none !important;
}
#timeline-slider::-webkit-slider-container {
    background: transparent !important;
}
#timeline-slider::-webkit-slider-thumb + * {
    display: none !important;
}


#timeline-slider::-moz-range-thumb {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #1976d2;
    cursor: pointer;
    border: 1.5px solid #fff;
    box-shadow: 0 0 2px rgba(0,0,0,0.08);
}

#timeline-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    font-size: 12px;
    color: #666;
}

.timeline-start-label, .timeline-end-label {
    padding: 0 5px;
}

/* Style khi legend bị thu nhỏ */
#legend.minimized {
    max-height: 40px; /* Giảm chiều cao khi thu nhỏ để vừa nút */
    min-width: 40px; /* Giảm chiều rộng tối thiểu */
    width: auto; /* Cho phép co lại */
    padding: 5px; /* Giảm padding */
    overflow: hidden; /* Đảm bảo ẩn hoàn toàn */
    display: flex; /* Sử dụng flexbox */
    align-items: center; /* Căn giữa theo chiều dọc */
}
#legend.minimized #legend-toggle-button {
    position: static; /* Không dùng absolute khi thu nhỏ */
    margin: 0; /* Xóa margin */
}
#legend.minimized #legend-content {
    max-height: 0; /* Thu nhỏ nội dung */
    opacity: 0;    /* Làm mờ nội dung */
    overflow: hidden; /* Quan trọng để ẩn nội dung */
    margin-top: 0; /* Bỏ margin khi ẩn */
    display: none; /* Hoàn toàn ẩn */
}
#legend.minimized h4 {
    display: none; /* Ẩn tiêu đề khi thu nhỏ */
}

/* Style tiêu đề Legend (khi không thu nhỏ) */
#legend h4 {
    margin-top: 0;
    margin-bottom: 8px;
    text-align: center;
    font-weight: bold;
    font-size: 13px;
    color: #333;
    padding-right: 20px; /* Đảm bảo không bị nút che */
}

/* Style các mục trong legend */
#legend strong { font-size: 11px; display: block; margin-top: 10px; margin-bottom: 4px; color: #555;}
#legend hr { margin: 8px 0; border: 0; border-top: 1px solid #ddd; }
#legend .legend-item { margin-bottom: 4px; display: flex; align-items: center; }
#legend .legend-color-box { width: 15px; height: 15px; display: inline-block; margin-right: 6px; border: 1px solid #999; flex-shrink: 0; }
#legend .legend-radar-box { height: 10px; }
#legend .legend-text { flex-grow: 1; }

/* Biểu tượng cột mưa trong bảng chú thích */
#legend .rain-column-icon {
    display: inline-block;
    width: 8px;
    height: 20px;
    margin-right: 8px;
    border-radius: 2px 2px 0 0;
    position: relative;
    flex-shrink: 0;
}

#legend .rain-column-icon::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: -2px;
    right: -2px;
    height: 2px;
    background-color: inherit;
    border-radius: 1px;
}
#legend .lightning-symbol { font-size: 14px; font-weight: bold; margin-right: 6px; display: inline-block; width: 15px; text-align: center; flex-shrink: 0; }
#legend i.note { display: block; font-size: 9px; margin-top: 5px; color: #666; }


/* --- Bảng dự báo (Góc dưới bên trái - Định vị bằng CSS) --- */
.forecast-popup {
    position: absolute; /* Định vị tuyệt đối so với #map */
    bottom: 10px;
    left: 10px;
    width: calc(100% - 40px); /* Trừ lề 2 bên */
    max-width: 480px; /* Giới hạn chiều rộng */
    max-height: 45vh; /* Giới hạn chiều cao */
    overflow-y: auto; /* Cho phép cuộn */
    padding: 12px 15px;
    font-size: 12px;
    line-height: 1.5;
    display: none; /* Ban đầu ẩn */
    z-index: 950; /* Cao hơn các control dưới khác, thấp hơn geocoder results */
    /* Kế thừa background, border, shadow từ .leaflet-control */
}
.forecast-popup h3 { font-size: 16px; margin-top: 0; margin-bottom: 10px; color: #333; padding-right: 25px; /* Chừa chỗ cho nút */}
.forecast-popup h4 { font-size: 13px; margin-top: 15px; margin-bottom: 5px; color: #555; border-bottom: 1px solid #eee; padding-bottom: 3px; }
.forecast-popup .forecast-minimize-button { position: absolute; top: 8px; right: 10px; font-size: 22px; font-weight: bold; line-height: 1; color: #aaa; background: none; border: none; cursor: pointer; padding: 0 5px; z-index: 10;}
.forecast-popup .forecast-minimize-button:hover { color: #555; }
.forecast-table { width: 100%; border-collapse: collapse; margin-top: 5px; }
.forecast-table th, .forecast-table td { border: 1px solid #ddd; padding: 6px 8px; text-align: left; font-size: 11px; vertical-align: top; }
.forecast-table th { background-color: #f2f2f2; font-weight: bold; }
.forecast-table td:first-child { font-weight: bold; background-color: #f9f9f9; min-width: 80px; white-space: nowrap;}
.daily-table td:first-child { min-width: 100px; }


/* --- Nút Mở Dự báo (Góc dưới bên trái - Định vị bằng CSS) --- */
#forecast-toggle-button {
    position: absolute; /* Định vị tuyệt đối so với #map */
    bottom: 10px;
    left: 10px;
    z-index: 940; /* Dưới bảng dự báo */
    background-color: #28a745;
    color: white;
    padding: 6px 12px;
    border: none; /* Ghi đè border của leaflet-control */
    cursor: pointer;
    font-size: 12px;
    font-weight: bold;
    display: none; /* Ban đầu ẩn */
    /* Kế thừa shadow, border-radius từ .leaflet-control */
}
#forecast-toggle-button:hover { background-color: #218838; }

/* --- Thanh tỷ lệ (Scale Control) (Góc dưới bên phải - MapLibre xử lý) --- */
.maplibregl-ctrl-scale {
    border: 2px solid #777; 
    border-top: none; 
    color: #333; 
    background-color: rgba(255, 255, 255, 0.7); 
    padding: 2px 5px; 
    font-size: 11px; 
    white-space: nowrap; 
    overflow: hidden; 
    box-sizing: content-box;
}

/* --- Custom Markers --- */
.maplibre-marker-icon { background: none; border: none; }
.maplibre-lightning-marker { font-weight: bold; text-shadow: 1px 1px 1px rgba(0,0,0,0.9); display: flex; align-items: center; justify-content: center; }

/* --- Static Markers (HS, TS Tooltip) --- */
.maplibre-static-label {
    background-color: rgba(255, 255, 255, 0.7);
    border: none;
    border-radius: 3px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    padding: 2px 5px;
    font-size: 12px;
    font-weight: bold;
    color: black;
    white-space: nowrap;
    pointer-events: none;
}

/* --- MapLibre Popup --- */
.maplibregl-popup-content { 
    border-radius: 8px; 
    padding: 10px 15px; 
    line-height: 1.5; 
}
.maplibregl-popup-content b { color: #333; }
.maplibregl-popup-content hr { margin: 8px 0; border: 0; border-top: 1px solid #eee; }

/* --- MapLibre Custom Tooltip --- */
.maplibre-tooltip { 
    border-radius: 4px; 
    font-size: 11px; 
    padding: 4px 8px; 
    background-color: rgba(255, 255, 255, 0.9); 
    pointer-events: none;
}

/* --- Layer Controls --- */
.map-overlay {
    position: absolute;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 3px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    font-size: 12px;
    overflow: auto;
    z-index: 1;
}

#layer-controls {
    top: 10px;
    right: 10px;
    padding: 10px;
    width: 150px;
    z-index: 1001;
}

#layer-controls h4 {
    margin: 0 0 10px 0;
    padding-bottom: 5px;
    border-bottom: 1px solid #ccc;
    text-align: center;
}

.layer-control-item {
    margin: 8px 0;
    display: flex;
    align-items: center;
}

.layer-control-item input[type="checkbox"] {
    margin-right: 8px;
    cursor: pointer;
}

.layer-control-item label {
    cursor: pointer;
    user-select: none;
}

/* --- END OF FILE style.css --- */