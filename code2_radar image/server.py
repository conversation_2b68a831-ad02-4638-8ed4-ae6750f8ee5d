#!/usr/bin/env python3
from http.server import HTTPServer, SimpleHTTPRequestHandler
import json
import os
import re
from datetime import datetime, timedelta
import urllib.parse
import subprocess
import threading

class RadarAPIHandler(SimpleHTTPRequestHandler):
    def do_POST(self):
        """Xử lý các yêu cầu POST"""
        if self.path == '/update_radar_images':
            # Chạy script cập nhật danh sách ảnh radar
            result = self.update_radar_images()
            
            # Trả về kết quả dưới dạng JSON
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(result).encode())
        else:
            self.send_error(404, "API endpoint không tồn tại")
    
    def update_radar_images(self):
        """Cập nhật danh sách ảnh radar"""
        try:
            # Chạy script scan_radar_images.py
            result = subprocess.run(['python3', 'scan_radar_images.py'], 
                                   capture_output=True, text=True, check=True)
            
            # Đọc kết quả từ file JSON
            with open('data/radar_timeline.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return {
                'success': True,
                'message': 'Đã cập nhật danh sách ảnh radar',
                'count': len(data['images']),
                'stdout': result.stdout
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'Lỗi khi cập nhật danh sách ảnh radar: {str(e)}'
            }
    
    def do_GET(self):
        """Xử lý các yêu cầu GET"""
        # Phân tích URL
        parsed_path = urllib.parse.urlparse(self.path)
        path = parsed_path.path
        
        # Xử lý yêu cầu API
        if path == '/api/radar/latest':
            self.send_latest_radar()
        else:
            # Xử lý các yêu cầu tĩnh như bình thường
            return SimpleHTTPRequestHandler.do_GET(self)
    
    def send_latest_radar(self):
        """Gửi thông tin về ảnh radar mới nhất"""
        try:
            # Đọc dữ liệu từ file JSON
            with open('data/radar_timeline.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if not data['images']:
                raise Exception("Không có ảnh radar nào")
            
            # Lấy ảnh mới nhất
            latest_image = data['images'][-1]
            
            # Trả về thông tin
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps({
                'success': True,
                'latest': latest_image
            }).encode())
        except Exception as e:
            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps({
                'success': False,
                'message': str(e)
            }).encode())

def run_server(port=8000):
    """Khởi động server"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, RadarAPIHandler)
    print(f"Khởi động server tại http://localhost:{port}")
    httpd.serve_forever()

def update_radar_images_periodically():
    """Cập nhật danh sách ảnh radar định kỳ"""
    while True:
        try:
            # Chạy trực tiếp script cập nhật
            result = subprocess.run(['python3', 'scan_radar_images.py'], 
                                  capture_output=True, text=True, check=True)
            print(f"Cập nhật tự động: {result.stdout}")
        except Exception as e:
            print(f"Lỗi khi cập nhật tự động: {str(e)}")
        
        # Chờ 1 phút trước khi cập nhật lại
        threading.Event().wait(60)

if __name__ == "__main__":
    # Khởi động thread cập nhật định kỳ
    updater_thread = threading.Thread(target=update_radar_images_periodically, daemon=True)
    updater_thread.start()
    
    # Khởi động server
    run_server()
