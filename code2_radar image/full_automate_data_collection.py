# -*- coding: utf-8 -*-
import json
import os
import requests
import math
from datetime import datetime, timezone, timedelta, MINYEAR # Import thêm MINYEAR
import pytz # Cần import pytz
from lxml import html
import pandas as pd
from template_reverse import ReverseTemplate
import time
from datetime import datetime, timedelta, timezone # Import timezone
import os
import sys
import requests
import ftplib


def should_run(last_run, interval_minutes):
    """Kiểm tra xem đã đến thời gian chạy tiếp theo chưa."""
    if last_run is None:
        return True  # Chưa chạy lần nào, chạy ngay
    
    current_time = datetime.now()
    time_diff = current_time - last_run
    
    # Chuyển thời gian thành phút
    minutes_passed = time_diff.total_seconds() / 60
    
    # Nếu đã qua khoảng thời gian cần thiết, trả về True
    return minutes_passed >= interval_minutes

def get_current_timestamp_iso():
    """Lấy timestamp UTC hiện tại theo định dạng ISO 8601."""
    return datetime.now(timezone.utc).isoformat(timespec='seconds')

def parse_timestamp(ts_str):
    """Phân tích chuỗi timestamp ISO hoặc định dạng cũ thành đối tượng datetime timezone-aware (UTC).
       Trả về datetime(MINYEAR, 1, 1, tzinfo=timezone.utc) nếu lỗi."""
    epoch_error_time = datetime(MINYEAR, 1, 1, tzinfo=timezone.utc) # Giá trị lỗi rõ ràng hơn epoch
    if not isinstance(ts_str, str):
        print(f"CẢNH BÁO: Giá trị timestamp không phải chuỗi: {ts_str}. Trả về thời gian lỗi.")
        return epoch_error_time
    try:
        return datetime.fromisoformat(ts_str.replace('Z', '+00:00'))
    except ValueError:
        try:
            dt_naive = datetime.strptime(ts_str, "%d/%m/%Y - %Hh")
            dt_aware_ict = dt_naive.replace(tzinfo=timezone(timedelta(hours=7)))
            return dt_aware_ict.astimezone(timezone.utc)
        except ValueError:
            print(f"CẢNH BÁO: Không thể phân tích timestamp '{ts_str}' với các định dạng đã biết. Trả về thời gian lỗi.")
            return epoch_error_time

def sort_timestamps_flexible(timestamps):
    """Sắp xếp danh sách các key timestamp, bỏ qua các giá trị lỗi khi sắp xếp."""
    valid_timestamps = [ts for ts in timestamps if isinstance(ts, str)]
    invalid_keys = [ts for ts in timestamps if not isinstance(ts, str)]
    if invalid_keys:
        print(f"Cảnh báo: Đã bỏ qua các key không phải chuỗi trong lịch sử: {invalid_keys}")
    # Sắp xếp dựa trên đối tượng datetime, các giá trị lỗi sẽ bị đẩy về đầu (do MINYEAR)
    return sorted(valid_timestamps, key=parse_timestamp)

def load_historical_data(filepath):
    """Tải dữ liệu lịch sử từ file JSON."""
    # (Giữ nguyên như trước)
    if os.path.exists(filepath):
        try:
            with open(filepath, 'r', encoding='utf-8') as f: data = json.load(f)
            if isinstance(data, dict): return data
            else: print(f"Lỗi: Dữ liệu trong file '{filepath}' không phải dict. Bắt đầu trống."); return {}
        except (json.JSONDecodeError, IOError) as e: print(f"Lỗi đọc file '{filepath}': {e}. Bắt đầu trống."); return {}
    else: print(f"Thông báo: File '{filepath}' không tồn tại. Bắt đầu trống."); return {}

def save_historical_data(filepath, data, max_history):
    """Lưu dữ liệu lịch sử vào file JSON."""
    # (Giữ nguyên như trước)
    try:
        output_dir = os.path.dirname(filepath)
        if output_dir and not os.path.exists(output_dir): os.makedirs(output_dir); print(f"Đã tạo thư mục: {output_dir}")
        with open(filepath, 'w', encoding='utf-8') as f: json.dump(data, f, ensure_ascii=False, indent=4)
        print(f"Đã lưu dữ liệu lịch sử vào '{filepath}' (tối đa {len(data)}/{max_history} bản ghi)")
        return True
    except IOError as e: print(f"LỖI ghi file '{filepath}': {e}"); return False
    except TypeError as e: print(f"LỖI kiểu dữ liệu khi lưu JSON: {e}"); return False

def simplify_snapshot(snapshot):
    """Đơn giản hóa snapshot để so sánh."""
    # (Giữ nguyên như trước)
    if not isinstance(snapshot, list): return set()
    simplified = set()
    keys_to_check = ('latitude', 'longitude', 'level', 'depth')
    for p in snapshot:
        if isinstance(p, dict) and all(k in p for k in keys_to_check):
            try:
                lat = p['latitude']; lon = p['longitude']
                if isinstance(lat, (int, float)) and isinstance(lon, (int, float)) and not (math.isnan(lat) or math.isnan(lon)):
                     simplified.add((round(lat, 5), round(lon, 5), p.get('level', 'Không xác định'), round(float(p.get('depth', 0.0)), 1)))
            except (TypeError, ValueError) as e: print(f"Cảnh báo: Lỗi giá trị trong điểm dữ liệu {p.get('name', 'N/A')}: {e}")
    return simplified

def process_raw_data(raw_data):
    """Xử lý dữ liệu thô từ API."""
    # (Giữ nguyên như trước)
    processed_points = []
    if not isinstance(raw_data, list): print("Cảnh báo: Dữ liệu thô không phải list."); return processed_points
    for item in raw_data:
        try:
            if isinstance(item, dict) and 'stat' in item and isinstance(item['stat'], dict):
                stat_data = item['stat']; level = stat_data.get('level')
                if level and level != "Không mưa":
                    if 'station' in stat_data and isinstance(stat_data['station'], dict):
                        station_info = stat_data['station']; lat = station_info.get('lat'); lng = station_info.get('lng')
                        if (isinstance(lat, (int, float)) and isinstance(lng, (int, float)) and not (math.isnan(lat) or math.isnan(lng))):
                            processed_points.append({'latitude': float(lat),'longitude': float(lng),'name': station_info.get('name', 'N/A'),'address': station_info.get('address', 'N/A'),'city': station_info.get('city', {}).get('name', 'N/A') if isinstance(station_info.get('city'), dict) else 'N/A','level': level,'depth': float(stat_data.get('sumDepth', 0.0))})
        except Exception as e: print(f"Cảnh báo: Lỗi xử lý mục dữ liệu thô: {e}")
    return processed_points

# --- HÀM CHỨC NĂNG CHÍNH (ĐÃ CẬP NHẬT) ---
def update_vrain_json_data(json_filepath, data_url, max_history):
    """
    Cập nhật file JSON dữ liệu mưa VRAIN: tải lịch sử, **lọc bỏ bản ghi cũ (>5h)**,
    lấy mới, so sánh, thêm mới, giới hạn số lượng, và lưu.

    Args:
        json_filepath (str): Đường dẫn file JSON để lưu lịch sử.
        data_url (str): URL API để lấy dữ liệu mới.
        max_history (int): Số lượng bản ghi lịch sử tối đa cần lưu.

    Returns:
        bool: True nếu quá trình thành công (kể cả khi không có gì thay đổi), False nếu có lỗi nghiêm trọng.
    """
    print("-" * 30)
    print(f"Bắt đầu cập nhật dữ liệu VRAIN vào file: {json_filepath}")
    any_change_made = False # Cờ theo dõi mọi thay đổi

    # 1. Tải dữ liệu lịch sử
    historical_data = load_historical_data(json_filepath)
    initial_history_count = len(historical_data)
    print(f"Đã tải {initial_history_count} bản ghi lịch sử.")

    # 2. Lọc dữ liệu lịch sử cũ (Xóa > 5 giờ trước)
    print("Lọc dữ liệu lịch sử (giữ lại tối đa 12 giờ gần nhất)...")
    cutoff_time_utc = datetime.now(pytz.utc) - timedelta(hours=max_history)
    print(f"Thời gian cắt (cutoff): {cutoff_time_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}")
    filtered_historical_data = {}
    keys_removed_by_age = []
    epoch_error_time = datetime(MINYEAR, 1, 1, tzinfo=timezone.utc) # Để so sánh lỗi parse

    for ts_key, snapshot in historical_data.items():
        dt_obj_utc = parse_timestamp(ts_key)
        # Chỉ giữ lại nếu parse thành công (không phải epoch_error_time) VÀ không cũ hơn cutoff
        if dt_obj_utc != epoch_error_time and dt_obj_utc >= cutoff_time_utc:
            filtered_historical_data[ts_key] = snapshot
        else:
            keys_removed_by_age.append(ts_key) # Thêm key bị xóa vào danh sách

    if keys_removed_by_age:
        print(f"=> Đã xóa {len(keys_removed_by_age)} bản ghi cũ hơn {max_history} giờ.")
        # print(f"   (Keys: {keys_removed_by_age})") # Bỏ comment để xem chi tiết
        any_change_made = True # Đánh dấu có thay đổi
    else:
        print(f"Không có bản ghi lịch sử nào cũ hơn {max_history} giờ bị xóa.")

    historical_data = filtered_historical_data # Cập nhật lại dictionary lịch sử đã lọc
    current_history_count_after_filter = len(historical_data)
    print(f"Còn lại {current_history_count_after_filter} bản ghi lịch sử sau khi lọc theo thời gian.")


    # 3. Lấy dữ liệu mới
    print(f"Đang lấy dữ liệu mới từ {data_url}...")
    new_raw_data = None
    try:
        response = requests.get(data_url, timeout=30)
        response.raise_for_status()
        new_raw_data = response.json()
        print("Lấy dữ liệu mới thành công!")
    except requests.exceptions.Timeout: print(f"LỖI: Hết thời gian chờ khi kết nối tới {data_url}.")
    except requests.exceptions.RequestException as e: print(f"LỖI khi lấy dữ liệu mới: {e}.")
    except json.JSONDecodeError as e: print(f"LỖI: Dữ liệu API không phải JSON hợp lệ: {e}")

    # 4. Xử lý dữ liệu mới
    new_rain_snapshot = None
    if new_raw_data:
        print("Đang xử lý dữ liệu mới...")
        new_rain_snapshot = process_raw_data(new_raw_data)
        if new_rain_snapshot: print(f"Đã xử lý được {len(new_rain_snapshot)} điểm mưa từ dữ liệu mới.")
        else: print("Không xử lý được điểm mưa nào từ dữ liệu mới."); new_rain_snapshot = [] # Đảm bảo là list rỗng
    else: print("Không có dữ liệu mới để xử lý."); new_rain_snapshot = [] # Đảm bảo là list rỗng

    # 5. So sánh và Thêm mới (nếu snapshot mới không rỗng)
    current_iso_timestamp = get_current_timestamp_iso()
    new_data_added = False
    # --- CODE CŨ: CHỈ GHI NẾU KHÁC DỮ LIỆU GẦN NHẤT ---
    '''
    if new_rain_snapshot: # Chỉ thực hiện nếu xử lý được snapshot mới không rỗng
        simplified_new_snapshot = simplify_snapshot(new_rain_snapshot)
        print(f"So sánh dữ liệu mới ({len(simplified_new_snapshot)} điểm đơn giản hóa)...")

        latest_timestamp_key = None
        sorted_timestamp_keys = sort_timestamps_flexible(list(historical_data.keys()))
        if sorted_timestamp_keys: latest_timestamp_key = sorted_timestamp_keys[-1]

        should_add_new = True
        if latest_timestamp_key:
            simplified_latest_stored = simplify_snapshot(historical_data.get(latest_timestamp_key, []))
            if simplified_new_snapshot == simplified_latest_stored:
                print("=> Dữ liệu mới GIỐNG HỆT lịch sử gần nhất (sau lọc). Không thêm.")
                should_add_new = False
            else:
                print("=> Dữ liệu mới KHÁC BIỆT. Sẽ thêm vào lịch sử.")
        else:
            print("Chưa có dữ liệu lịch sử (sau lọc). Thêm snapshot mới...")

        if should_add_new:
            historical_data[current_iso_timestamp] = new_rain_snapshot
            any_change_made = True # Đánh dấu có thay đổi
            new_data_added = True
            print(f"Đã thêm dữ liệu mới với timestamp: {current_iso_timestamp}")
    else:
         print("Không có dữ liệu mới hợp lệ để thêm vào lịch sử.")
    '''

    # --- CODE MỚI: LUÔN GHI SNAPSHOT MỚI VỚI TIMESTAMP MỚI ---
    if new_rain_snapshot: # Chỉ thực hiện nếu xử lý được snapshot mới không rỗng
        historical_data[current_iso_timestamp] = new_rain_snapshot
        any_change_made = True # Đánh dấu có thay đổi
        new_data_added = True
        print(f"Đã thêm dữ liệu mới với timestamp: {current_iso_timestamp}")
    else:
         print("Không có dữ liệu mới hợp lệ để thêm vào lịch sử.")


    # 6. Giới hạn số lượng lịch sử & Sắp xếp lại trước khi giới hạn
    print(f"Kiểm tra giới hạn lịch sử (tối đa {max_history} bản ghi)...")
    keys_removed_by_limit = 0
    # Luôn sắp xếp lại các keys hiện có trước khi giới hạn
    sorted_keys_final = sort_timestamps_flexible(list(historical_data.keys()))

    keys_to_keep = sorted_keys_final[-max_history:] # Lấy max_history keys mới nhất
    final_historical_data = {key: historical_data[key] for key in keys_to_keep if key in historical_data}

    # Kiểm tra xem việc giới hạn có thực sự xóa key nào không
    if len(final_historical_data) < len(historical_data):
        keys_removed_by_limit = len(historical_data) - len(final_historical_data)
        print(f"=> Đã xóa {keys_removed_by_limit} bản ghi cũ nhất do vượt giới hạn {max_history}.")
        any_change_made = True # Đánh dấu có thay đổi
    else:
         print("Số lượng bản ghi lịch sử hiện tại không vượt quá giới hạn.")

    historical_data = final_historical_data # Cập nhật dictionary cuối cùng

    # 7. Lưu file lịch sử (nếu có bất kỳ thay đổi nào)
    save_successful = False
    if any_change_made:
        print("Có thay đổi, đang lưu cập nhật vào file lịch sử...")
        # Truyền historical_data đã được lọc và giới hạn
        save_successful = save_historical_data(json_filepath, historical_data, max_history)
    else:
        print("Không có thay đổi nào được thực hiện. Không cần lưu file.")
        save_successful = True # Không cần làm gì cũng coi là thành công

    print(f"Kết thúc cập nhật. Trạng thái lưu file: {'Thành công' if save_successful else 'Thất bại'}")
    print("-" * 30)
    return save_successful

# Xử lý dữ liệu Lightning ----------------------------------------------------
# --- Helper Function: process_segments ---
def process_segments(segments3, lat_list, long_list, time_all_list, giatri_list, sensor_list, dof_list, style_list, loaiset_list):
    """
    Phân tích các đoạn chuỗi dữ liệu thô và điền vào các danh sách tương ứng.

    Args:
        segments3 (list): Danh sách các chuỗi con chứa dữ liệu sét thô.
        ... (các list): Các danh sách để lưu trữ dữ liệu đã phân tích.

    Returns:
        tuple: Tuple chứa các danh sách đã được cập nhật.
    """
    # Template nên được tạo một lần bên ngoài nếu có thể, nhưng để đây cho tính bao đóng
    rt = ReverseTemplate("nam:{x1},thang:{x2},ngay:{x3},gio:{x4},phut:{x5},giay:{x6},giatri:{x7},sensor:{x8},dof:{x9},lat:{x10},lng:{x11},style:{x12},loaiset:{x13}")
    for segment in segments3:
        try:
            values = rt.reverse(segment)
            # Kiểm tra giá trị hợp lệ trước khi ép kiểu int
            if "x1" in values and str(values["x1"]) != "undefined" and "x12" in values and values["x12"].isdigit():
                if int(values["x12"]) <= 30:
                    lat_list.append(values.get("x10")) # Dùng get để an toàn hơn
                    long_list.append(values.get("x11"))
                    # Ghép chuỗi ngày giờ và chuyển đổi, xử lý lỗi nếu định dạng sai
                    try:
                        date_str = f"{values.get('x3','01')}/{values.get('x2','01')}/{values.get('x1','1970')} {values.get('x4','00')}:{values.get('x5','00')}:{values.get('x6','00')}"
                        # Giả định thời gian trích xuất là UTC (quan trọng!)
                        date_time_obj = datetime.strptime(date_str, '%d/%m/%Y %H:%M:%S')
                        # Thêm thông tin timezone UTC
                        date_target = date_time_obj.replace(tzinfo=pytz.utc)
                        time_all_list.append(date_target)
                    except ValueError:
                         # print(f"Cảnh báo: Định dạng thời gian không hợp lệ trong segment: {segment}, bỏ qua bản ghi.")
                         # Nếu thời gian lỗi, không thêm các giá trị khác của bản ghi này
                         lat_list.pop()
                         long_list.pop()
                         continue # Chuyển sang segment tiếp theo

                    giatri_list.append(values.get("x7"))
                    sensor_list.append(values.get("x8"))
                    dof_list.append(values.get("x9"))
                    style_list.append(values.get("x12"))
                    loaiset_list.append(values.get("x13"))
                else:
                    # Style > 30, dừng xử lý các segment còn lại trong set này
                    break
            # else: # Bỏ qua nếu không có x1, x12 hoặc x12 không phải số
                 # print(f"Cảnh báo: Thiếu thông tin hoặc style không hợp lệ trong segment: {segment}")
                 # pass
        except Exception as e: # Bắt lỗi chung khi reverse hoặc xử lý values
            print(f"Lỗi xử lý segment '{segment}': {e}")
            continue

    # Trả về các list đã được cập nhật
    return lat_list, long_list, time_all_list, giatri_list, sensor_list, dof_list, style_list, loaiset_list

# --- Main Processing Function ---
def update_lightning_data(csv_filepath):
    """
    Thực hiện toàn bộ quá trình cập nhật dữ liệu sét: đọc file cũ,
    lấy dữ liệu mới, xử lý, kết hợp, lọc, sắp xếp và lưu lại.

    Args:
        csv_filepath (str): Đường dẫn đến file CSV lưu trữ dữ liệu.

    Returns:
        pandas.DataFrame or None: DataFrame chứa dữ liệu cuối cùng đã xử lý,
                                  hoặc None nếu có lỗi nghiêm trọng.
    """
    print(f"--- Bắt đầu quá trình cập nhật dữ liệu cho file: {csv_filepath} ---")
    expected_columns = ['time', 'giatri', 'sensor', 'dof', 'lat', 'long', 'style', 'loaiset']
    df_old = pd.DataFrame(columns=expected_columns) # Khởi tạo df rỗng

    # 1. Đọc dữ liệu cũ từ file CSV (nếu có)
    try:
        if os.path.exists(csv_filepath):
            df_old = pd.read_csv(csv_filepath, parse_dates=['time'])
            if not df_old.empty and 'time' in df_old.columns:
                if df_old['time'].dt.tz is None:
                    df_old['time'] = df_old['time'].dt.tz_localize('UTC')
                else:
                    df_old['time'] = df_old['time'].dt.tz_convert('UTC')
                print(f"Đã đọc {len(df_old)} bản ghi từ file cũ.")
            else:
                # Nếu file tồn tại nhưng rỗng hoặc không có cột time
                df_old = pd.DataFrame(columns=expected_columns)
                df_old['time'] = pd.to_datetime(df_old['time']).dt.tz_localize('UTC')
        else:
             print(f"File {csv_filepath} không tồn tại. Sẽ tạo file mới.")
             df_old['time'] = pd.to_datetime(df_old['time']).dt.tz_localize('UTC')

    except Exception as e:
        print(f"Lỗi khi đọc file {csv_filepath}: {e}. Bắt đầu với dữ liệu rỗng.")
        df_old = pd.DataFrame(columns=expected_columns)
        df_old['time'] = pd.to_datetime(df_old['time']).dt.tz_localize('UTC')

    # Khởi tạo danh sách tạm để lưu dữ liệu mới
    time_new, giatri_new, sensor_new, dof_new, lat_new, long_new, style_new, loaiset_new = [], [], [], [], [], [], [], []
    df_new = pd.DataFrame(columns=expected_columns) # Khởi tạo df_new rỗng

    # 2. Lấy và xử lý dữ liệu mới
    try:
        print("Đang lấy dữ liệu từ web...")
        page = requests.get('http://hymetnet.gov.vn/lightningmaps/', timeout=30)
        page.raise_for_status()
        print("Đã lấy dữ liệu web thành công.")
        tree = html.fromstring(page.content)
        level1 = tree.xpath('/html/body/div[2]/div/script[3]/text()')

        if not level1:
            print("Lỗi: Không tìm thấy thẻ script mục tiêu bằng XPath.")
            level2 = ""
        else:
            level2 = level1[0]

        set6_in = level2.find("set[6]")
        set6_end = level2.find("set[7]")

        if set6_in != -1 and set6_end != -1 and set6_end > set6_in:
            level3 = level2[(set6_in + len("set[6] = ")) : (set6_end - 1)].strip()
            if level3.startswith('[') and level3.endswith(';'):
                level4 = level3[1:-1].strip()
                if level4:
                    segments3 = level4.split('},{')
                    # Gọi hàm xử lý segments
                    lat_new, long_new, time_new, giatri_new, sensor_new, dof_new, style_new, loaiset_new = process_segments(
                        segments3, lat_new, long_new, time_new, giatri_new, sensor_new, dof_new, style_new, loaiset_new
                    )
                    if time_new:
                        data_new_dict = {'time':time_new,'giatri':giatri_new,'sensor':sensor_new,'dof':dof_new,'lat':lat_new,'long':long_new,'style':style_new,'loaiset':loaiset_new}
                        df_new = pd.DataFrame(data_new_dict)
                        print(f"Đã xử lý {len(df_new)} bản ghi mới.")
                    else:
                         print("Không xử lý được bản ghi mới nào hợp lệ.")
                else:
                     print("Cảnh báo: Dữ liệu 'set[6]' rỗng.")
            else:
                 print("Cảnh báo: Dữ liệu 'set[6]' không có định dạng mảng mong đợi.")
        else:
            print("Lỗi: Không tìm thấy dấu hiệu 'set[6]' hoặc 'set[7]' trong script.")

    except requests.exceptions.RequestException as e:
        print(f"Lỗi trong quá trình truy cập web: {e}")
    except Exception as e:
        print(f"Lỗi xảy ra trong quá trình xử lý dữ liệu mới: {e}")

    # 3. Kết hợp dữ liệu cũ và mới
    if not df_new.empty:
        df_new = df_new.reindex(columns=expected_columns, fill_value=None)
    if not df_old.empty:
        df_old = df_old.reindex(columns=expected_columns, fill_value=None)

    df_combined = pd.concat([df_old, df_new], ignore_index=True)

    # 4. Làm sạch và Lọc dữ liệu kết hợp
    if not df_combined.empty:
        print(f"Tổng số bản ghi trước khi lọc: {len(df_combined)}")

        # Đảm bảo cột 'time' là datetime và có múi giờ UTC
        df_combined['time'] = pd.to_datetime(df_combined['time'], errors='coerce', utc=True)
        df_combined.dropna(subset=['time'], inplace=True)

        if df_combined.empty:
            print("Không còn dữ liệu sau khi chuẩn hóa cột thời gian.")
        else:
            # Lọc thời gian: Xóa dữ liệu cũ hơn 12 giờ
            cutoff_time_utc = datetime.now(pytz.utc) - timedelta(hours=MAX_HISTORY_RECORDS)
            initial_count = len(df_combined)
            df_combined = df_combined[df_combined['time'] >= cutoff_time_utc].copy()
            print(f"Lọc thời gian: Đã xóa {initial_count - len(df_combined)} bản ghi cũ hơn {cutoff_time_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}")

            # Loại bỏ trùng lặp
            initial_count = len(df_combined)
            df_combined.drop_duplicates(inplace=True, keep='last')
            print(f"Loại bỏ trùng lặp: Đã xóa {initial_count - len(df_combined)} bản ghi.")

            # Sắp xếp theo thời gian tăng dần
            df_combined.sort_values(by='time', ascending=True, inplace=True)
            print("Đã sắp xếp dữ liệu theo thời gian.")

            # 5. Lưu dữ liệu đã xử lý
            try:
                # Đảm bảo thư mục tồn tại
                save_dir = os.path.dirname(csv_filepath)
                if save_dir and not os.path.exists(save_dir):
                    os.makedirs(save_dir)
                    print(f"Đã tạo thư mục: {save_dir}")

                df_combined.to_csv(csv_filepath, index=False, date_format='%Y-%m-%d %H:%M:%S') # Lưu định dạng chuẩn
                print(f"Đã xử lý và lưu thành công dữ liệu vào {csv_filepath}")
                print(f"Tổng số bản ghi trong file cuối cùng: {len(df_combined)}")
                print("\n5 dòng dữ liệu đầu tiên trong file:")
                print(df_combined.head())
                print("\n5 dòng dữ liệu cuối cùng trong file:")
                print(df_combined.tail())
            except Exception as e:
                print(f"Lỗi khi lưu dữ liệu vào {csv_filepath}: {e}")
                return None # Trả về None nếu lưu lỗi

            # Trả về DataFrame cuối cùng
            print(f"--- Kết thúc quá trình cập nhật ---")
            return df_combined

    else: # Trường hợp df_combined rỗng ngay từ đầu
        print("Không có dữ liệu (cũ hoặc mới) để xử lý hoặc lưu.")
        # Có thể chọn lưu file rỗng hoặc không làm gì cả
        try:
             # Lưu file rỗng để đảm bảo file tồn tại với đúng header
             pd.DataFrame(columns=expected_columns).to_csv(csv_filepath, index=False)
             print(f"Đã lưu file rỗng với header chuẩn vào {csv_filepath}")
        except Exception as e:
             print(f"Lỗi khi lưu file rỗng vào {csv_filepath}: {e}")
        print(f"--- Kết thúc quá trình cập nhật (không có dữ liệu) ---")
        return df_combined # Trả về df rỗng

# Xử lý dữ liệu Radar --------------------------------------------------------
# --- HÀM CHỨC NĂNG CHÍNH ---
def download_and_clean_radar(download_dir, max_history_records):
    print("-" * 30)
    print(f"Bắt đầu quá trình tải và dọn dẹp radar trong thư mục: {download_dir}")
    success = True # Giả định thành công ban đầU

    now = datetime.now()
    current_minutes = int(now.strftime("%M"))
    if int(current_minutes) != 9 and int(current_minutes) != 19 and int(current_minutes) != 29 and int(current_minutes) != 39 and int(current_minutes) != 49 and int(current_minutes) != 59:
        print("Đợi đến phút 09, 19, 29, 39, 49, hoặc 59 để tải file radar.")
        return False
    else: 
        print("Bắt đầu tải file radar...")
        des_time = now + timedelta(hours=-7)
        arg1 = des_time.strftime("%Y%m%d%H")
        arg2 = des_time.strftime("%Y%m%d")
        if int(current_minutes) == 9:
            arg3 = "00"
        elif int(current_minutes) == 19:
            arg3 = "10"
        elif int(current_minutes) == 29:
            arg3 = "20"
        elif int(current_minutes) == 39:
            arg3 = "30"
        elif int(current_minutes) == 49:
            arg3 = "40"
        elif int(current_minutes) == 59:
            arg3 = "50"
        radar_url = "http://222.255.11.69/dataout_web/COM/" + str(arg2) + "/COM_" + str(arg1) + str(arg3) + "_CMAX00.png"
        print(radar_url)
        # --- Ghi log tải file radar ---
        log_time = (des_time + timedelta(hours=7)).strftime("%Y/%m/%d-%H:%M:%S")
        log_line = f"{log_time}  : {radar_url}\n"
        with open(os.path.join(download_dir, "log.txt"), "a", encoding="utf-8") as logf:
            logf.write(log_line)
        



        download_file = os.path.join(download_dir, str(arg1) + str(arg3)+ "_CMAX00.png")  
        final_file = os.path.join(download_dir, "vn_mosaic_"+ str(arg1) + str(arg3) +"00-final.png")
        try:
            r = requests.get(radar_url, allow_redirects=True)
            if r.status_code == 200 and r.content and len(r.content) > 1024:  # ví dụ file phải lớn hơn 1KB
                with open(download_file, 'wb') as f:
                    f.write(r.content)
                print("Tải file radar hoàn tất.")
                print("Đổi tên file")
                try:
                    os.rename(download_file, final_file)
                    print("Đổi tên file thành công.")
                    log_line = f"{log_time}  : {radar_url}\n: Download OK : Convert OK"
                    with open(os.path.join(download_dir, "log.txt"), "a", encoding="utf-8") as logf:
                        logf.write(log_line)
                except Exception as e:
                    print(f"Lỗi đổi tên file: {e}")
                    log_line = f"{log_time}  : {radar_url}\n: Download OK : Convert error"
                    with open(os.path.join(download_dir, "log.txt"), "a", encoding="utf-8") as logf:
                        logf.write(log_line)
        except Exception as e:
            print(f"Lỗi tải file radar!")
            # Có thể ghi log lỗi nếu muốn
            log_line = log_line = f"{log_time}  : {radar_url}\n: Download error : {e}"
            with open(os.path.join(download_dir, "log.txt"), "a", encoding="utf-8") as logf:
                logf.write(log_line)
        

        # # --- 2. Dọn dẹp file cũ (> 12 giờ) ---
        # print("\nBắt đầu dọn dẹp các file cũ (> 12 giờ)...")
        # try:
        #     now_utc = datetime.now(timezone.utc)
        #     cutoff_time_utc = now_utc - timedelta(hours=max_history_records)
        #     print(f"Thời gian cắt (xóa file cũ hơn): {cutoff_time_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        #     files_deleted_count = 0

        #     # Đảm bảo thư mục tồn tại trước khi liệt kê
        #     if not os.path.isdir(download_dir):
        #         print(f"Thư mục đích '{download_dir}' không tồn tại. Không có gì để dọn dẹp.")
        #     else:
        #         for filename in os.listdir(download_dir):
        #             # Kiểm tra định dạng file
        #             if filename.startswith("vn_radar_") and filename.endswith("-final.png") and len(filename) == 27: # 4 + 12 + 9 = 25? No, 4+12+9 = 25. COM_YYYYMMDDHHMM_CMAX00.png -> 3+1+12+1+8=25? No, 3+1+12+1+7+4=28? Let's check: COM_YYYYMMDDHHMM_CMAX00.png -> 3+1+12+1+6+4=27. Correct.
        #                 full_path = os.path.join(download_dir, filename)
        #                 if os.path.isfile(full_path): # Chỉ xử lý file
        #                     # Trích xuất timestamp YYYYMMDDHHMM từ tên file
        #                     timestamp_str = filename[10:22] # Vị trí 4 đến 15 (12 ký tự)
        #                     try:
        #                         # Parse thành datetime naive
        #                         file_time_naive = datetime.strptime(timestamp_str, '%Y%m%d%H%M')
        #                         # Gán múi giờ UTC (vì tên file chứa giờ UTC)
        #                         file_time_utc = file_time_naive.replace(tzinfo=timezone.utc)

        #                         # So sánh với thời gian cắt
        #                         if file_time_utc < cutoff_time_utc:
        #                             try:
        #                                 os.remove(full_path)
        #                                 print(f"  Đã xóa file cũ: {filename} (Thời gian: {file_time_utc.strftime('%Y-%m-%d %H:%M:%S %Z')})")
        #                                 files_deleted_count += 1
        #                             except OSError as e:
        #                                 print(f"  LỖI: Không thể xóa file {filename}: {e}")
        #                                 success = False # Đánh dấu có lỗi
        #                     except ValueError:
        #                         print(f"  CẢNH BÁO: Không thể parse timestamp từ tên file: {filename}")
        #                     except Exception as e:
        #                         print(f"  LỖI không xác định khi xử lý file {filename}: {e}")
        #                         success = False

        #         print(f"Hoàn tất dọn dẹp. Đã xóa {files_deleted_count} file cũ.")

        # except OSError as e:
        #     print(f"LỖI: Không thể truy cập thư mục {download_dir} để dọn dẹp: {e}")
        #     success = False # Lỗi nghiêm trọng
        # except Exception as e:
        #     print(f"LỖI không xác định trong quá trình dọn dẹp: {e}")
        #     success = False

        print("-" * 30)
        return success

# --- HÀM KIỂM TRA THỜI GIAN CHẠY ---
def should_run(last_run_time, interval_minutes):
    """
    Kiểm tra xem đã đến thời gian chạy lại chưa dựa trên khoảng thời gian đã cấu hình.
    
    Args:
        last_run_time (datetime): Thời điểm chạy lần cuối
        interval_minutes (int): Khoảng thời gian giữa các lần chạy (phút)
        
    Returns:
        bool: True nếu đã đến thời gian chạy lại, False nếu chưa
    """
    if last_run_time is None:
        return True
    current_time = datetime.now()
    time_diff = current_time - last_run_time
    return time_diff.total_seconds() >= (interval_minutes * 60)

# --- KHỐI THỰC THI CHÍNH ---
if __name__ == "__main__":
    # --- CẤU HÌNH ---
    data_dir = '/Users/<USER>/Desktop/data/web_code/code2/data'
    lightning_csv_file = os.path.join(data_dir, 'lightning.csv')
    RADAR_DOWNLOAD_DIRECTORY = os.path.join(data_dir, 'radar_source')
    VRAIN_HISTORY_FILE = os.path.join(data_dir, 'vrain_data.json')
    VRAIN_API_URL = 'https://data.vrain.vn/public/current/summary.json'
    MAX_HISTORY_RECORDS = 12
    
    # Kiểm tra và tạo thư mục nếu chưa tồn tại
    for directory in [data_dir, RADAR_DOWNLOAD_DIRECTORY]:
        if not os.path.exists(directory):
            try:
                os.makedirs(directory)
                print(f"Đã tạo thư mục: {directory}")
            except Exception as e:
                print(f"Lỗi khi tạo thư mục {directory}: {e}")
    
    # Biến lưu thời gian chạy lần cuối
    last_vrain_run = None
    last_lightning_run = None
    last_radar_run = None
    
    # Cấu hình khoảng thời gian chạy (phút)
    VRAIN_INTERVAL_MINUTES = 60  # Chạy mỗi giờ
    LIGHTNING_INTERVAL_MINUTES = 15  # Chạy mỗi 15 phút
    RADAR_INTERVAL_MINUTES = 1  # Chạy mỗi 11 phút
    
    # Chạy lần đầu ngay khi khởi động
    current_time = datetime.now()
    print(f"\n--- Bắt đầu cập nhật dữ liệu lúc: {current_time.strftime('%Y-%m-%d %H:%M:%S')} ---")
    
    # 1. Xử lý dữ liệu VRAIN
    print("\nBắt đầu cập nhật dữ liệu VRAIN...")
    try:
        # Gọi hàm xử lý chính
        success = update_vrain_json_data(
            json_filepath=VRAIN_HISTORY_FILE,
            data_url=VRAIN_API_URL,
            max_history=MAX_HISTORY_RECORDS
        )
        # In trạng thái kết thúc
        if success: 
            print("\nTrạng thái: Quá trình cập nhật dữ liệu VRAIN hoàn tất thành công.")
        else: 
            print("\nTrạng thái: Quá trình cập nhật dữ liệu VRAIN gặp lỗi.")
        
        # Cập nhật thời gian chạy lần cuối
        last_vrain_run = datetime.now()
    except Exception as e:
        print(f"\nLỖI NGHIÊM TRỌNG khi cập nhật dữ liệu VRAIN: {e}")
        print("Tiếp tục chạy script mà không dừng lại...")
    
    # 2. Xử lý dữ liệu sét
    print("\nBắt đầu cập nhật dữ liệu sét...")
    try:
        # Gọi hàm xử lý chính
        final_dataframe = update_lightning_data(lightning_csv_file)
        # Kiểm tra kết quả trả về
        if final_dataframe is not None:
            if not final_dataframe.empty:
                print("\nTrạng thái: Quá trình cập nhật dữ liệu sét hoàn tất thành công.")
            else:
                print("\nTrạng thái: Quá trình cập nhật dữ liệu sét hoàn tất nhưng không có dữ liệu cuối cùng.")
        else:
            print("\nTrạng thái: Quá trình cập nhật dữ liệu sét gặp lỗi.")
        
        # Cập nhật thời gian chạy lần cuối
        last_lightning_run = datetime.now()
    except Exception as e:
        print(f"\nLỖI NGHIÊM TRỌNG khi cập nhật dữ liệu sét: {e}")
        print("Tiếp tục chạy script mà không dừng lại...")
    
    # 3. Xử lý dữ liệu Radar
    print("\nBắt đầu cập nhật dữ liệu radar...")
    try:
        # Gọi hàm xử lý chính
        radar_success = download_and_clean_radar(RADAR_DOWNLOAD_DIRECTORY, MAX_HISTORY_RECORDS)
        # In trạng thái kết thúc
        if radar_success:
            print("\nTrạng thái: Quá trình cập nhật dữ liệu radar hoàn tất thành công.")
        else:
            print("\nTrạng thái: Quá trình cập nhật dữ liệu radar gặp lỗi.")
        # Cập nhật thời gian chạy lần cuối
        last_radar_run = datetime.now()
    except Exception as e:
        print(f"\nLỖI NGHIÊM TRỌNG khi cập nhật dữ liệu radar: {e}")
        print("Tiếp tục chạy script mà không dừng lại...")
    
    print("\n--- Hoàn thành cập nhật ban đầu ---")
    print(f"Thời gian hoàn thành: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Đã cập nhật xong VRAIN, Lightning và Radar.")
    
    # Vào vòng lặp để kiểm tra thời gian định kỳ
    print("\nĐang chuyển sang chế độ kiểm tra định kỳ...")
    
    while True:
        current_time = datetime.now()
        print(f"\n--- Kiểm tra cập nhật dữ liệu lúc: {current_time.strftime('%Y-%m-%d %H:%M:%S')} ---")
        
        # Xử lý dữ liệu VRAIN (mỗi giờ)
        if should_run(last_vrain_run, VRAIN_INTERVAL_MINUTES):
            print("Bắt đầu script cập nhật dữ liệu VRAIN...")
            try:
                # Gọi hàm xử lý chính
                success = update_vrain_json_data(
                    json_filepath=VRAIN_HISTORY_FILE,
                    data_url=VRAIN_API_URL,
                    max_history=MAX_HISTORY_RECORDS
                )
                # In trạng thái kết thúc
                if success: 
                    print("\nTrạng thái: Quá trình cập nhật dữ liệu VRAIN hoàn tất.")
                else: 
                    print("\nTrạng thái: Quá trình cập nhật dữ liệu VRAIN gặp lỗi.")
                
                # Cập nhật thời gian chạy lần cuối
                last_vrain_run = datetime.now()
                print(f"Đã cập nhật VRAIN lúc: {last_vrain_run.strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"Lần cập nhật VRAIN tiếp theo dự kiến: {(last_vrain_run + timedelta(minutes=VRAIN_INTERVAL_MINUTES)).strftime('%Y-%m-%d %H:%M:%S')}")
            except Exception as e:
                print(f"\nLỖI NGHIÊM TRỌNG khi cập nhật dữ liệu VRAIN: {e}")
                print("Tiếp tục chạy script mà không dừng lại...")
        else:
            next_run = last_vrain_run + timedelta(minutes=VRAIN_INTERVAL_MINUTES)
            print(f"Chưa đến thời gian cập nhật VRAIN. Lần cập nhật tiếp theo: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Xử lý dữ liệu sét (mỗi 15 phút)
        if should_run(last_lightning_run, LIGHTNING_INTERVAL_MINUTES):
            print("\nBắt đầu script cập nhật dữ liệu sét...")
            try:
                # Gọi hàm xử lý chính
                final_dataframe = update_lightning_data(lightning_csv_file)
                # Kiểm tra kết quả trả về
                if final_dataframe is not None:
                    if not final_dataframe.empty:
                        print("\nTrạng thái: Quá trình cập nhật dữ liệu sét hoàn tất thành công.")
                    else:
                        print("\nTrạng thái: Quá trình cập nhật dữ liệu sét hoàn tất nhưng không có dữ liệu cuối cùng.")
                else:
                    print("\nTrạng thái: Quá trình cập nhật dữ liệu sét gặp lỗi.")
                
                # Cập nhật thời gian chạy lần cuối
                last_lightning_run = datetime.now()
                print(f"Đã cập nhật dữ liệu sét lúc: {last_lightning_run.strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"Lần cập nhật dữ liệu sét tiếp theo dự kiến: {(last_lightning_run + timedelta(minutes=LIGHTNING_INTERVAL_MINUTES)).strftime('%Y-%m-%d %H:%M:%S')}")
            except Exception as e:
                print(f"\nLỖI NGHIÊM TRỌNG khi cập nhật dữ liệu sét: {e}")
                print("Tiếp tục chạy script mà không dừng lại...")
        else:
            next_run = last_lightning_run + timedelta(minutes=LIGHTNING_INTERVAL_MINUTES) if last_lightning_run else datetime.now()
            print(f"Chưa đến thời gian cập nhật dữ liệu sét. Lần cập nhật tiếp theo: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Xử lý dữ liệu Radar (mỗi 11 phút)
        if should_run(last_radar_run, RADAR_INTERVAL_MINUTES):
            print("\nBắt đầu script cập nhật dữ liệu radar...")
            try:
                # Gọi hàm xử lý chính
                radar_success = download_and_clean_radar(RADAR_DOWNLOAD_DIRECTORY, MAX_HISTORY_RECORDS)
                # In trạng thái kết thúc
                if radar_success:
                    print("\nTrạng thái: Quá trình cập nhật dữ liệu radar hoàn tất thành công.")
                else:
                    print("\nTrạng thái: Quá trình cập nhật dữ liệu radar gặp lỗi.")
                
                # Cập nhật thời gian chạy lần cuối
                last_radar_run = datetime.now()
                print(f"Đã cập nhật dữ liệu radar lúc: {last_radar_run.strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"Lần cập nhật dữ liệu radar tiếp theo dự kiến: {(last_radar_run + timedelta(minutes=RADAR_INTERVAL_MINUTES)).strftime('%Y-%m-%d %H:%M:%S')}")
            except Exception as e:
                print(f"\nLỖI khi cập nhật dữ liệu radar: {e}")
                print("Tiếp tục chạy script mà không dừng lại...")
        else:
            next_run = last_radar_run + timedelta(minutes=RADAR_INTERVAL_MINUTES) if last_radar_run else datetime.now()
            print(f"Chưa đến thời gian cập nhật dữ liệu radar. Lần cập nhật tiếp theo: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Tạm dừng 1 phút trước khi kiểm tra lại
        print(f"Tạm dừng 1 phút trước khi kiểm tra lại...")
        time.sleep(60)  # Tạm dừng 60 giây