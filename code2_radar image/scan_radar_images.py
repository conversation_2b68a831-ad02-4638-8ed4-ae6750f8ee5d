#!/usr/bin/env python3
import os
import json
import re
from datetime import datetime, timedelta

def scan_radar_images(directory):
    """Quét thư mục chứa ảnh radar và tạo danh sách với thông tin thời gian"""
    
    # Ki<PERSON>m tra thư mục tồn tại
    if not os.path.exists(directory):
        print(f"Thư mục {directory} không tồn tại")
        return []
    
    # Mẫu regex để trích xuất thời gian từ tên file
    pattern = r'ph_mosaic_(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})-final\.png'
    
    radar_images = []
    
    # Quét tất cả các file trong thư mục
    for filename in sorted(os.listdir(directory)):
        if filename.endswith('.png'):
            match = re.match(pattern, filename)
            if match:
                year, month, day, hour, minute, second = match.groups()
                
                # Tạo đối tượng datetime (UTC)
                timestamp_utc = datetime(
                    int(year), int(month), int(day),
                    int(hour), int(minute), int(second)
                )
                
                # <PERSON><PERSON><PERSON><PERSON> đổi sang UTC+7 (giờ Việt Nam)
                timestamp_vn = timestamp_utc + timedelta(hours=7)
                
                # Định dạng thời gian hiển thị (UTC+7)
                display_time = timestamp_vn.strftime('%d/%m/%Y %H:%M')
                
                # Thêm vào danh sách
                radar_images.append({
                    'filename': filename,
                    'path': f'data/radar_source/{filename}',
                    'timestamp_utc': timestamp_utc.isoformat(),
                    'timestamp_vn': timestamp_vn.isoformat(),
                    'display_time': display_time
                })
    
    # Sắp xếp theo thời gian UTC
    radar_images.sort(key=lambda x: x['timestamp_utc'])
    
    return radar_images

def create_radar_json():
    directory = 'data/radar_source'
    radar_images = scan_radar_images(directory)
    
    # Tính toán khoảng thời gian (12 giờ trước đến hiện tại) theo giờ Việt Nam
    now = datetime.now()
    twelve_hours_ago = now - timedelta(hours=12)
    
    # In ra thời gian hiện tại để kiểm tra
    print(f"Thời gian hiện tại: {now.strftime('%d/%m/%Y %H:%M')} (giờ máy tính)")
    
    # Lọc ảnh trong khoảng thời gian (dựa vào giờ Việt Nam)
    filtered_images = [
        img for img in radar_images 
        if datetime.fromisoformat(img['timestamp_vn']) >= twelve_hours_ago
    ]
    
    # Sắp xếp theo thời gian tăng dần
    filtered_images.sort(key=lambda x: x['timestamp_vn'])
    
    # Tạo đối tượng JSON
    result = {
        'start_time': twelve_hours_ago.isoformat(),
        'end_time': now.isoformat(),
        'images': filtered_images
    }
    
    # Ghi ra file JSON
    with open('data/radar_timeline.json', 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    print(f"Đã quét {len(radar_images)} ảnh radar, lọc được {len(filtered_images)} ảnh trong khoảng 12 giờ qua")
    
    # Hiển thị thời gian của ảnh đầu tiên và cuối cùng để kiểm tra
    if filtered_images:
        print(f"\u1ea2nh đầu tiên: {filtered_images[0]['display_time']}")
        print(f"\u1ea2nh cuối cùng: {filtered_images[-1]['display_time']}")
    print(f"Đã lưu thông tin vào file data/radar_timeline.json")

if __name__ == "__main__":
    create_radar_json()
