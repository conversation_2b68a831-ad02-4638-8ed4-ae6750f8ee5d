import requests
from bs4 import BeautifulSoup
import pandas as pd

def check_url_ok(url):
    """
    Crawl HTML, trích xuất 2 pandas (DataFrame) cho dự báo ngày và 10 ngày,
    sau đó kiểm tra nội dung thực tế của từng DataFrame để xác định hợp lệ.
    """
    try:
        resp = requests.get(url, timeout=10)
        if resp.status_code != 200 or not resp.text.strip():
            return False
        soup = BeautifulSoup(resp.text, 'html.parser')
        # --- Dự báo ngày ---
        forecast_sections = soup.find_all('div', class_='text-weather-location fix-weather-location')
        daily_data = []
        for sec in forecast_sections:
            rows = []
            items = sec.select('ul.list-info-wt li')
            for item in items:
                label_div = item.select_one('div.uk-width-1-4')
                value_div = item.select_one('div.uk-width-3-4')
                label = label_div.get_text(strip=True) if label_div else ''
                value = value_div.get_text(strip=True) if value_div else ''
                rows.append({'label': label, 'value': value})
            daily_data.append(rows)
        # Chuyển sang DataFrame
        daily_df = pd.DataFrame([row for rows in daily_data for row in rows]) if daily_data else pd.DataFrame()
        # Bỏ 4 dòng đầu (dự báo hiện tại/ngày/đêm)
        daily_df = daily_df.iloc[4:].reset_index(drop=True)
        print("Daily DataFrame (sau khi loại 4 dòng đầu):", daily_df)
        # --- Dự báo 10 ngày ---
        ten_days_container = soup.find('div', class_='ten-days-forecast')
        ten_day_items = ten_days_container.find_all('div', class_='item-days-wt') if ten_days_container else []
        ten_day_data = []
        for item in ten_day_items:
            rows = []
            date_div = item.select_one('div.date-wt')
            date = date_div.get_text(strip=True) if date_div else ''
            for li in item.select('ul.list-info-wt li'):
                label_div = li.select_one('div.uk-width-1-4')
                value_div = li.select_one('div.uk-width-3-4')
                label = label_div.get_text(strip=True) if label_div else ''
                value = value_div.get_text(strip=True) if value_div else ''
                rows.append({'date': date, 'label': label, 'value': value})
            ten_day_data.extend(rows)
        ten_day_df = pd.DataFrame(ten_day_data) if ten_day_data else pd.DataFrame()
        print("Ten Day DataFrame:", ten_day_df)
        # --- Kiểm tra nội dung thực tế ---
        import re
        def is_real_value(val):
            # Chỉ hợp lệ nếu chứa ít nhất 1 chữ số
            return bool(re.search(r'\d', val))
        def is_valid_df(df):
            if df.empty:
                return False
            for val in df['value']:
                if is_real_value(val):
                    return True
            return False

        valid_today = is_valid_df(daily_df)
        valid_10days = is_valid_df(ten_day_df)
        if valid_today and valid_10days:
            return 'OK'
        elif valid_today:
            return 'OK'
        elif valid_10days:
            return 'OK'
        else:
            return 'False'

    except Exception as e:
        print(f"[ERROR] Lỗi kiểm tra URL: {e}")
    return False

import random

def get_random_url_from_file(txt_path):
    urls = []
    with open(txt_path, encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            # Hỗ trợ cả dạng: slug, url hoặc slug: url hoặc chỉ url
            if ',' in line:
                parts = line.split(',', 1)
                url = parts[1].strip()
            elif ':' in line:
                parts = line.split(':', 1)
                url = parts[1].strip()
            else:
                url = line.strip()
            if url.startswith('http'):
                urls.append(url)
    return random.choice(urls) if urls else None

if __name__ == '__main__':
    url_file = '/Users/<USER>/Desktop/data/web_code/code2/locations_urls.txt'
    url = get_random_url_from_file(url_file)
    print(f"URL random: {url}")
    ok = check_url_ok(url)
    print(f"Kết quả kiểm tra: {ok}")

