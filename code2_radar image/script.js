// --- START OF FILE script.js (MapLibre GL JS Version) ---

document.addEventListener('DOMContentLoaded', function() {
    // --- GLOBAL VARIABLES ---

// Khởi tạo mảng timelineSteps: 12 giờ, mỗi step 10 phút (tổng 72 mốc, UTC)
let timelineSteps = [];
(function initTimelineSteps() {
    let now = new Date();
    for (let i = 71; i >= 0; i--) {
        let d = new Date(now.getTime() - i * 10 * 60 * 1000);
        let utctime = d.getUTCFullYear().toString()
            + (d.getUTCMonth() + 1).toString().padStart(2, '0')
            + d.getUTCDate().toString().padStart(2, '0')
            + d.getUTCHours().toString().padStart(2, '0')
            + d.getUTCMinutes().toString().padStart(2, '0');
        timelineSteps.push(utctime);
    }
})();

    let map;                      // MapLibre GL JS map object
    // let layerControl;          // MapLibre doesn't have a direct equivalent built-in; manage layer visibility manually or via custom UI
    let geocoderControl;          // MapLibre Geocoder Control instance
    let allForecastData = {};     // Stores all forecast data from data.json
    let provinceFeaturesMap = {}; // Map from normalized province name -> { feature: geojsonFeature } (Leaflet layer reference removed)
    let highlightedProvinceName = null; // Stores the 'name' property of the currently highlighted province feature
    let forecastTableContainer = null; // DOM element div containing the forecast table
    let forecastToggleButton = null;   // DOM element button to reopen the forecast table
    let currentSearchMarker = null;    // MapLibre Marker object for the last search result
    let provincePopup = null;      // MapLibre Popup for province tooltips
    // Biến toàn cục cho timeline
    let radarImages = [];
    let currentImageIndex = 0;
    let isPlaying = false;
    let animationInterval = null;
    let radarBounds = null;
    let timeLayersData = []; // Lưu trữ dữ liệu các lớp theo thời gian
    let timelineTimestamps = [];
    let currentTimelineIndex = 0;
    // -- Layer Visibility State --
    let layerVisibility = {
        radar: true, // Giá trị ban đầu có thể đọc từ HTML sau
        vrain: true,
        lightning: true,
    };
    // Lưu cache các url radar đã update lên map
const loadedRadarImages = {};

    // --- MAP INITIALIZATION AND CONFIGURATION ---
    function initMap(config) {
        console.log("Starting initMap for MapLibre GL JS...");
        try {
            // 1. Initialize MapLibre GL JS Map Object
            // *** IMPORTANT: Replace with your preferred style URL ***
            // Option 1: MapLibre Demo Style (Basic)
            //const mapStyle = 'https://demotiles.maplibre.org/style.json';
            // Option 2: MapTiler Streets (Requires API Key - replace YOUR_API_KEY)
            const mapStyle = `https://api.maptiler.com/maps/01962ffa-24fd-7deb-9deb-99a5ed9cca6e/style.json?key=oinoatcrNmdCL1524DOl`; // Replace with your key!
            // Option 3: Stadia Maps Alidade Smooth (Requires API Key - replace YOUR_API_KEY)
            //const mapStyle = `https://tiles.stadiamaps.com/styles/alidade_smooth.json?api_key=KfrfTTJ7f1oCitMyqcRP`;
            // Option 4: Your custom style URL

            if (mapStyle.includes('get_your_free_key') || mapStyle.includes('YOUR_')) {
                 console.warn("Please replace the placeholder API key in the mapStyle URL in script.js for optimal performance and features.");
                 alert("Warning: Map style URL might require an API key. Please check script.js.");
            }

            // Ensure config.center is [longitude, latitude] for MapLibre
            const mapCenter = config.center ? [config.center[1], config.center[0]] : [105.8, 16.5]; // Default center if needed (Lon, Lat)
            const mapZoom = config.zoom || 5;

            map = new maplibregl.Map({
                container: 'map', // container id
                style: mapStyle,
                center: mapCenter, // longitude, latitude
                zoom: mapZoom,
                pitch: 45, // Add perspective tilt
                bearing: 0, // Initial bearing
                antialias: true // Optional: improve rendering quality
            });

            console.log("MapLibre GL JS map object created.");

            // 2. Add Basic Map Controls
            map.addControl(new maplibregl.NavigationControl({
                 visualizePitch: true // Show pitch control
            }), 'top-left'); // Di chuyển sang bên trái dưới thanh tìm kiếm
            console.log("Navigation Control added.");

            map.addControl(new maplibregl.ScaleControl({
                maxWidth: 100,
                unit: 'metric' // Use metric units
            }), 'bottom-right');
            console.log("Scale Control added.");
            
            map.on('load', function() {
                console.log("Map loaded, creating custom images...");
                // Load lightning icons PNG
                const lightningIcons = [
                    { name: 'lightningCG', url: 'assets/lightningCG.png' },
                    { name: 'lightningIC', url: 'assets/lightningIC.png' }
                ];
                lightningIcons.forEach(icon => {
                    map.loadImage(icon.url, (error, image) => {
                        if (error) {
                            console.error('Không thể load icon:', icon.url, error);
                        } else if (!map.hasImage(icon.name)) {
                            map.addImage(icon.name, image);
                            console.log('Đã add icon:', icon.name);
                        }
                    });
                });
                // // Thiết lập các nút bật/tắt lớp dữ liệu
                // setupLayerToggles();
            });

            // 3. Initialize Geocoder (MaplibreGeocoder)
            // Make sure maplibre-gl-geocoder library is included in your HTML
            if (typeof MaplibreGeocoder !== 'undefined') {
                // Sử dụng Nominatim API của OpenStreetMap làm geocoder
                const nominatimGeocoder = {
                    forwardGeocode: async (config) => {
                        const query = config.query;
                        console.log("Geocoding query:", query);
                        
                        if (!query) return { features: [] };
                        
                        // Tạo URL cho Nominatim API
                        // Giới hạn kết quả cho Việt Nam và ưu tiên tiếng Việt
                        const params = {
                            q: query,
                            format: 'jsonv2',
                            addressdetails: 1,
                            limit: 5,
                            countrycodes: 'vn',
                            'accept-language': 'vi',
                            polygon_geojson: 1
                        };
                        
                        const paramString = Object.keys(params)
                            .map(key => `${key}=${encodeURIComponent(params[key])}`)
                            .join('&');
                            
                        const url = `https://nominatim.openstreetmap.org/search.php?${paramString}`;
                        
                        
                        try {
                            // Thực hiện request với User-Agent header
                            const response = await fetch(url, {
                                headers: {
                                    'User-Agent': 'WeatherMapApplication/1.0'
                                }
                            });
                            const data = await response.json();
                            console.log("Nominatim API response:", data);
                            
                            // Kiểm tra xem có kết quả không
                            if (!data || data.length === 0) {
                                console.log("Không tìm thấy kết quả từ Nominatim API");
                                return { features: [] };
                            }
                            
                            // Chuyển đổi kết quả sang định dạng GeoJSON mà MapLibre Geocoder cần
                            const features = data.map(result => {
                                const [lng, lat] = [parseFloat(result.lon), parseFloat(result.lat)];
                                
                                // Tạo context cho kết quả
                                const context = [];
                                if (result.address) {
                                    if (result.address.country) {
                                        context.push({
                                            id: `country.${result.place_id}`,
                                            text: result.address.country
                                        });
                                    }
                                    if (result.address.state) {
                                        context.push({
                                            id: `region.${result.place_id}`,
                                            text: result.address.state
                                        });
                                    }
                                }
                                
                                // Xác định loại địa điểm
                                let placeType = ['place'];
                                if (result.category === 'boundary' || result.type === 'administrative') {
                                    placeType = ['region'];
                                }
                                
                                // Lưu trữ polygon_geojson để sử dụng sau này
                                // MapLibre Geocoder yêu cầu geometry là Point
                                const originalGeojson = result.geojson;
                                
                                // Tạo feature với định dạng mà MapLibre Geocoder mong đợi
                                const feature = {
                                    type: "Feature",
                                    geometry: {
                                        type: "Point",
                                        coordinates: [lng, lat]
                                    },
                                    properties: {
                                        name: result.name || result.display_name.split(',')[0],
                                        place_name: result.display_name,
                                        place_type: placeType,
                                        center: [lng, lat],
                                        bbox: result.boundingbox ? [
                                            parseFloat(result.boundingbox[2]), // minLon
                                            parseFloat(result.boundingbox[0]), // minLat
                                            parseFloat(result.boundingbox[3]), // maxLon
                                            parseFloat(result.boundingbox[1])  // maxLat
                                        ] : undefined,
                                        context: context,
                                        importance: result.importance,
                                        category: result.category,
                                        osm_type: result.osm_type,
                                        osm_id: result.osm_id,
                                        // Lưu trữ polygon_geojson trong properties để sử dụng sau này
                                        original_geojson: originalGeojson
                                    },
                                    text: result.name || result.display_name.split(',')[0],
                                    place_name: result.display_name,
                                    center: [lng, lat],
                                    bbox: result.boundingbox ? [
                                        parseFloat(result.boundingbox[2]), // minLon
                                        parseFloat(result.boundingbox[0]), // minLat
                                        parseFloat(result.boundingbox[3]), // maxLon
                                        parseFloat(result.boundingbox[1])  // maxLat
                                    ] : undefined,
                                    context: context
                                };
                                
                                return feature;
                            });
                            
                            return { features };
                        } catch (error) {
                            console.error("Error with Nominatim API:", error);
                            return { features: [] };
                        }
                    }
                };
                
                geocoderControl = new MaplibreGeocoder(nominatimGeocoder, {
                    maplibregl: maplibregl, // Pass the maplibregl object
                    placeholder: "Tìm kiếm Tỉnh, Thành phố...",
                    proximity: mapCenter, // Bias search results towards the initial map center
                    countries: 'vn', // Limit search to Vietnam
                    language: 'vi', // Prefer Vietnamese results
                    marker: false, // Disable default marker, we handle it in 'result' event
                    flyTo: false // Disable default flyTo, we handle it in 'result' event
                });

                map.addControl(geocoderControl, 'top-left');
                console.log("Nominatim MaplibreGeocoder Control added.");

                // Handle search result selection
                geocoderControl.on('result', handleSearchResult);
                geocoderControl.on('clear', clearHighlightAndForecast); // Also clear on explicit geocoder clear

            } else {
                console.error("MaplibreGeocoder is not defined. Please ensure the library is included.");
            }


            // 4. Create container and button for Forecast Table (using standard DOM methods)
            const bodyElement = document.body; // Or append to a specific container outside the map
            if (bodyElement) {
                 forecastTableContainer = document.createElement('div');
                 forecastTableContainer.className = 'forecast-popup maplibregl-ctrl'; // Use similar class names
                 forecastTableContainer.id = 'forecast-table-container';
                 forecastTableContainer.style.display = 'none'; // Initially hidden
                 bodyElement.appendChild(forecastTableContainer); // Append outside map

                 forecastToggleButton = document.createElement('button');
                 forecastToggleButton.className = 'maplibregl-ctrl'; // Use similar class names
                 forecastToggleButton.id = 'forecast-toggle-button';
                 forecastToggleButton.innerHTML = 'Xem Dự báo';
                 forecastToggleButton.onclick = function(event) {
                     if (event) { event.stopPropagation(); event.preventDefault(); }
                     showForecastTable(event);
                     return false;
                 };
                 forecastToggleButton.style.display = 'none'; // Initially hidden
                 bodyElement.appendChild(forecastToggleButton); // Append outside map

                 console.log("Forecast container and toggle button created using standard DOM.");
            } else {
                 console.error("Critical Error: Could not find document.body to append forecast elements.");
            }

            // 5. Locate Legend Container (should exist in HTML)
             const legendDiv = document.getElementById('legend');
             if (!legendDiv) {
                  console.error("Error: Could not find the div#legend element in HTML.");
             } else {
                 console.log("Legend will be populated later and positioned by CSS.");
             }

            // 6. Add map click listener for cleanup
            map.on('click', (event) => {
                // Check if the click was on a feature (like a province) - if so, don't clear immediately
                const features = map.queryRenderedFeatures(event.point, { layers: ['province-fills', 'province-boundaries'] }); // Check layers involved in highlighting/interaction
                if (!features.length) {
                    clearHighlightAndForecast();
                }
                 // If features are clicked, the 'handleSearchResult' or a feature-specific click handler
                 // should manage the state, so we don't clear unnecessarily here.
            });

            // --- IMPORTANT: Add data layers AFTER the map style has loaded ---
            map.on('load', () => {
                 console.log("Map style loaded. Proceeding to add data layers.");
                 // Call a function to load data and then add layers
                 loadDataAndAddLayers();
            });

            // Handle potential map loading errors
             map.on('error', (event) => {
                 console.error("MapLibre map error:", event.error);
                 const mapDiv = document.getElementById('map');
                 if(mapDiv) mapDiv.innerHTML = `<div style='color:red; padding:20px; border: 1px solid red;'>Map Loading Error: ${event.error?.message || 'Unknown error'}. Please check style URL, API key (if needed), and network connection.</div>`;
             });

            console.log("initMap for MapLibre GL JS completed setup (waiting for 'load' event for layers).");

        } catch (error) {
             console.error("Critical error during MapLibre initMap:", error);
             const mapDiv = document.getElementById('map');
             if(mapDiv) mapDiv.innerHTML = `<div style='color:red; padding:20px; border: 1px solid red;'>Critical Error Initializing Map: ${error.message}. Please check the console.</div>`;
        }
    }

    // --- DATA LOADING AND LAYER ADDITION (Called after map 'load') ---
    function loadDataAndAddLayers() {
        // Đảm bảo radar source/layer được khởi tạo trước khi load data.json
        // addRadarsource();
        addRadarSourceAsync().then((success) => {
            if (success && radarImages.length > 0) {
                console.log('Tạo source và layer radar thành công (async)');
            } else {
                // Thông báo lỗi rõ ràng ra UI hoặc log
                console.warn('Không thể khởi tạo radar layer: Không có ảnh radar hoặc lỗi dữ liệu.');
                const radarStatusDiv = document.getElementById('radar-status');
                if (radarStatusDiv) {
                    radarStatusDiv.innerHTML = "<span style='color:red'>Không thể khởi tạo lớp radar – dữ liệu ảnh radar không hợp lệ hoặc thiếu!</span>";
                }
                // Nếu muốn dừng các thao tác tiếp theo, có thể return hoặc throw error ở đây
            }
        });
        const cacheBuster = '?v=' + new Date().getTime();
        const dataUrl = 'data/data.json' + cacheBuster;
        console.log(`Loading data: ${dataUrl}`);
        fetch(dataUrl)
            .then(response => {
                if (!response.ok) throw new Error(`HTTP ${response.status} loading data.json`);
                const contentType = response.headers.get("content-type");
                if (!contentType?.includes("application/json")) {
                    throw new TypeError(`Expected JSON, got ${contentType}. Check data.json path and server config.`);
                }
                return response.json();
            })
            .then(data => {
                 console.log("JSON data loaded successfully.");
                 if (!data?.mapConfig || !data.tileLayer /* Keep tileLayer for reference? Maybe not needed */) {
                     console.warn("JSON structure issue: 'mapConfig' might be less relevant now, but ensure data structure is correct.");
                     // No hard failure here as MapLibre uses its own style primarily
                 }
                 allForecastData = data.forecastData || {};
                 console.log(`Stored forecast for ${Object.keys(allForecastData).length} locations.`);

                 // Add layers now that map is loaded and data is fetched
                 createLegend(data.legendData); // Create legend content
                 addProvinceBoundaries(data.provinceGeoJsonPath); // Add province boundaries
                 // Gán dữ liệu timeLayers vào biến toàn cục
                 if (data.timeLayers && data.timeLayers.length > 0) {
                     timeLayersData = data.timeLayers;
                     console.log("Đã gán timeLayersData:", timeLayersData);
                 } else {
                     console.warn("Không có dữ liệu timeLayers trong data.json.");
                 }
                 buildTimelineTimestamps();
                 updateTimelineUI();
                 setupTimelineEvents();
                 showTimeline(0);
                 

                 // Set initial map bounds after layers might influence view
                 if (data.mapConfig?.vietnamBounds) {
                     try {
                         // MapLibre expects [[SW lon, SW lat], [NE lon, NE lat]]
                         const boundsArray = [
                             [data.mapConfig.vietnamBounds[0][1], data.mapConfig.vietnamBounds[0][0]], // SW Lon, SW Lat
                             [data.mapConfig.vietnamBounds[1][1], data.mapConfig.vietnamBounds[1][0]]  // NE Lon, NE Lat
                         ];
                         map.fitBounds(boundsArray, {
                             padding: 20 // Add some padding
                         });
                         console.log("Initial fitBounds done for Vietnam using MapLibre format.");
                     } catch (e) {
                         console.error("Initial fitBounds error:", e);
                         // Fallback handled by initial map center/zoom
                     }
                 }
                 console.log("Map data layers added and initial view set.");
            })
            .catch(error => {
                console.error('FATAL ERROR loading or processing data:', error);
                const mapDiv = document.getElementById('map');
                if (mapDiv && !mapDiv.innerHTML.includes('Error')) { // Avoid overwriting map init errors
                    mapDiv.innerHTML = `<div style='color:red; padding:20px; border: 1px solid red; background: white;'>Fatal Error Loading/Processing Data: ${error.message}.<br>Check data.json, paths, server config, and Console (F12).</div>`;
                }
                const legendContainer = document.getElementById('legend');
                if (legendContainer) legendContainer.innerHTML = '<h4 style=\"color:red;\">Error Loading Data</h4>';
            });
            // in timeLayerdata ra console log
            console.log("Time layer data:", timeLayersData);
            // Thiết lập các nút bật/tắt lớp dữ liệu
            setupLayerToggles();
    }


    // --- LAYER ADDITION FUNCTIONS ---

    // --- STATIC MARKERS ---
    function loadAndAddStaticMarkers() {
        fetch('data/static_markers.json')
            .then(res => res.json())
            .then(data => {
                if (Array.isArray(data) && data.length > 0) {
                    addStaticMarkers(data);
                } else {
                    console.warn('Không có dữ liệu static_markers.json hoặc dữ liệu không hợp lệ');
                }
            })
            .catch(e => {
                console.error('Lỗi khi tải static_markers.json:', e);
            });
    }

    function createLegend(legendData) {
         const legendContainer = document.getElementById('legend');
         if (!legendContainer) { console.error("Cannot find legend container #legend."); return; }
         if (!legendData) { console.warn("Skipping legend creation: missing legendData."); legendContainer.innerHTML = '<h4 style="color:orange;">Missing Legend Data</h4>'; return; }

         try {
             let html = `<button id="legend-toggle-button" title="Collapse legend">×</button>`; // No inline onclick
             html += `<h4>Chú thích</h4>`;
             html += `<div id="legend-content">`;

             // VRAIN Section
             if (legendData.vrain && Object.keys(legendData.vrain).length > 0) {
                 html += `<strong>Trạm mưa (VRAIN):</strong><div>`;
                 const sortedVrainLevels = Object.entries(legendData.vrain).sort(([, a], [, b]) => a.height - b.height);
                 sortedVrainLevels.forEach(([level, config]) => {
                     if (config.height != null && config.height >= 0 && config.dark && config.light) {
                         // Hiển thị cột mưa với gradient
                         html += `<div class="legend-item">
                             <div class="legend-color-box" style='background: ${config.dark};'></div>
                             <span class="legend-text">${level} (Size indicates height)</span>
                         </div>`;
                     }
                 });
                 html += `</div>`;
             }

             // Lightning Section
             if (legendData.lightning) {
                 html += `<hr><strong>Sét (Lightning):</strong><div>`;
                 html += `<div class="legend-item"><img src='assets/lightningCG.png' style='width:20px;vertical-align:middle;margin-right:6px'><span class="legend-text">Sét đất (CG)</span></div>`;
                 html += `<div class="legend-item"><img src='assets/lightningIC.png' style='width:20px;vertical-align:middle;margin-right:6px'><span class="legend-text">Sét mây (IC/CC)</span></div>`;
                 html += `<i class="note">Size indicates intensity.</i>`;
                 html += `</div>`;
             }

             // Radar Section
             if (legendData.radar && legendData.radar.length > 0) {
                 html += `<hr><strong>Radar (dBZ - in image):</strong><div>`;
                 legendData.radar.forEach(item => {
                     if(item && item.length >= 2){
                        html += `<div class="legend-item"><i class="legend-color-box legend-radar-box" style='background:${item[0]};'></i><span class="legend-text">${item[1]} dBZ</span></div>`;
                     }
                 });
                 html += `</div>`;
             }

             html += `</div>`; // Close div#legend-content
             legendContainer.innerHTML = html;

             // Attach event listener using JS
             const toggleButton = document.getElementById('legend-toggle-button');
             if (toggleButton) {
                  toggleButton.onclick = function(event) {
                      if (event) { event.stopPropagation(); event.preventDefault(); }
                      toggleLegend(this); // `this` is the button
                      return false;
                  };
             } else {
                  console.error("Could not find legend-toggle-button after generating HTML.");
             }

             legendContainer.classList.remove('minimized'); // Initial state
             console.log("Legend created.");

         } catch (e) {
              console.error("Critical error generating legend HTML:", e);
              if(legendContainer) legendContainer.innerHTML = '<h4 style="color:red;">Error Creating Legend</h4>';
         }
     }

    function addProvinceBoundaries(geojsonPath) {
        if (!geojsonPath) { console.warn("No province GeoJSON path provided, skipping boundary layer."); return; }
        console.log("Fetching province GeoJSON from:", geojsonPath);
        fetch(geojsonPath)
            .then(response => { if (!response.ok) throw new Error(`HTTP ${response.status} loading ${geojsonPath}`); return response.json(); })
            .then(geojson => {
                if (!geojson?.features) throw new Error(`Invalid or empty GeoJSON data from ${geojsonPath}`);
                console.log(`Successfully loaded ${geojson.features.length} province GeoJSON features.`);

                provinceFeaturesMap = {}; // Reset lookup map
                geojson.features.forEach(feature => {
                    if (feature.properties?.name) {
                        const normalizedName = normalizeLocationNameJS(feature.properties.name);
                        if (normalizedName) {
                            provinceFeaturesMap[normalizedName] = { feature: feature }; // Store the original feature
                        }
                         // Ensure the feature has a unique ID for potential filtering/querying, use 'name' if no 'id' exists
                         if (!feature.id && feature.properties.name) {
                             feature.id = feature.properties.name; // Use name as ID if necessary (ensure uniqueness)
                         }
                    } else {
                        console.warn("GeoJSON feature missing 'properties.name':", feature);
                    }
                });
                 console.log(`Province lookup map created with ${Object.keys(provinceFeaturesMap).length} entries.`);

                // 1. Add GeoJSON Source
                if (map.getSource('provinces')) {
                    map.getSource('provinces').setData(geojson);
                    console.log("Updated existing 'provinces' source.");
                } else {
                    map.addSource('provinces', {
                        type: 'geojson',
                        data: geojson,
                        promoteId: 'name' // Use the 'name' property as the feature ID for easier lookup/styling
                    });
                    console.log("Added new 'provinces' source.");
                }

                // 2. Add Layer for Boundaries (Lines)
                if (!map.getLayer('province-boundaries')) {
                    map.addLayer({
                        id: 'province-boundaries',
                        type: 'line',
                        source: 'provinces',
                        layout: {},
                        paint: {
                            // Default style
                            'line-color': '#3388ff',
                            'line-width': 1,
                            'line-opacity': 0.6,
                            // Highlight style using a 'case' expression
                            // It checks if the feature's 'name' property matches the 'highlightedProvinceName' variable
                            'line-color': [
                                'case',
                                ['==', ['get', 'name'], (highlightedProvinceName || '')], // Check against global variable
                                'red', // Color if highlighted
                                '#3388ff' // Default color
                            ],
                            'line-width': [
                                'case',
                                ['==', ['get', 'name'], (highlightedProvinceName || '')],
                                3, // Width if highlighted
                                1  // Default width
                            ]
                        }
                    });
                    console.log("Added 'province-boundaries' layer.");
                } else {
                    console.log("'province-boundaries' layer already exists.");
                     // Ensure paint properties are updated if layer exists (e.g., during a hot reload)
                     map.setPaintProperty('province-boundaries', 'line-color', [
                         'case',
                         ['==', ['get', 'name'], (highlightedProvinceName || '')],
                         'red',
                         '#3388ff'
                     ]);
                      map.setPaintProperty('province-boundaries', 'line-width', [
                         'case',
                         ['==', ['get', 'name'], (highlightedProvinceName || '')],
                         3,
                         1
                     ]);
                }

                 // 3. Add Layer for Fills (for hover/click detection) - Invisible
                 if (!map.getLayer('province-fills')) {
                    map.addLayer({
                        id: 'province-fills',
                        type: 'fill',
                        source: 'provinces',
                        layout: {},
                        paint: {
                            'fill-color': 'transparent' // Make it invisible but interactive
                        }
                    }, 'province-boundaries'); // Add fills below boundaries if preferred
                    console.log("Added 'province-fills' layer for interaction.");
                 }


                // 4. Add Tooltip (Popup on Hover)
                provincePopup = new maplibregl.Popup({
                    closeButton: false,
                    closeOnClick: false,
                    anchor: 'top',
                    offset: [5, 25]
                });

                map.on('mousemove', 'province-fills', (e) => {
                    if (e.features.length > 0) {
                        map.getCanvas().style.cursor = 'pointer'; // Change cursor
                        const feature = e.features[0];
                        const provinceName = feature.properties.name || 'N/A';
                        provincePopup.setLngLat(e.lngLat)
                                     .setHTML(`<strong>${provinceName}</strong>`)
                                     .addTo(map);
                    }
                });

                map.on('mouseleave', 'province-fills', () => {
                    map.getCanvas().style.cursor = ''; // Reset cursor
                    if (provincePopup.isOpen()) {
                        provincePopup.remove();
                    }
                });

                // Optional: Handle click on province to potentially trigger search/highlight
                 map.on('click', 'province-fills', (e) => {
                     if (e.features.length > 0) {
                         const feature = e.features[0];
                         const provinceName = feature.properties.name;
                         if (provinceName) {
                              console.log(`Province clicked: ${provinceName}`);
                              // Trigger a search-like behavior for the clicked province
                              handleProvinceClick(provinceName);
                         }
                     }
                 });


                // Note: Layer Control integration is manual in MapLibre GL JS core.
                // You would need custom UI (buttons, checkboxes) to toggle layer visibility
                // using map.setLayoutProperty('layer-id', 'visibility', 'visible'/'none');

            })
            .catch(error => { console.error('Error loading or processing province GeoJSON:', error); });
    }


    // Build timelineTimestamps from all available time layers (radar, lightning, vrain, ...)

    function buildTimelineTimestamps() {
        // Build timeline every 10 minutes, from now - 12h to now (local time)
        const now = new Date();
        const start = new Date(now.getTime() - 12 * 60 * 60 * 1000);
        let arr = [];
        let t = new Date(start);
        while (t <= now) {
            arr.push(t.toISOString().slice(0,16)); // yyyy-mm-ddThh:mm
            t = new Date(t.getTime() + 10 * 60 * 1000);
        }
        timelineTimestamps = arr;
    }
    
    // Hàm tạo thanh timeline cho radar
    function addRadarsource() {
        // Tải dữ liệu radar từ file JSON
        fetch('data/radar_timeline.json')
            .then(response => response.json())
            .then(data => {
                radarImages = data.images;
                if (radarImages.length === 0) {
                    console.warn("Không có ảnh radar nào được tìm thấy");
                    return;
                }
                
                // Tạo source và layer cho radar nếu chưa tồn tại
                const sourceId = 'radar-timeline-source';
                const layerId = 'radar-timeline-layer';
                
                // Nếu chưa có radarBounds, sử dụng giá trị mặc định
                if (!radarBounds) {
                    // Giá trị mặc định cho Việt Nam
                    radarBounds = {
                        north: 25.2,  // Vĩ độ bắc của Việt Nam
                        south: 7.2,   // Vĩ độ nam của Việt Nam
                        east: 115.0,  // Kinh độ đông của Việt Nam
                        west: 97.0   // Kinh độ tây của Việt Nam
                    };
                }
                
                const coordinates = convertBoundsToCoordinates(radarBounds);
                
                if (coordinates) {
                    // Chỉ tạo source/layer nếu chưa tồn tại để tận dụng cache
                    if (!map.getSource(sourceId)) {
                        map.addSource(sourceId, {
                            type: 'image',
                        url: radarImages[radarImages.length - 1].path,
                            coordinates: coordinates
                        });
                    }
                    if (!map.getLayer(layerId)) {
                        map.addLayer({
                            id: layerId,
                            type: 'raster',
                            source: sourceId,
                            layout: {
                                visibility: 'none' // Luôn ẩn đi ban đầu, applyLayerVisibility sẽ xử lý
                            },
                            paint: {
                                'raster-opacity': 0.9,
                                'raster-fade-duration': 0
                            }
                        });
                    }
                    
                    console.log('Tạo source và layer radar thành công');
                }
            })
            .catch(error => {
                console.error("Lỗi khi tải dữ liệu radar:", error);
            });
    }
    
    // Hàm bất đồng bộ khởi tạo radar source/layer (Promise)
    function addRadarSourceAsync() {
        return new Promise((resolve, reject) => {
            fetch('data/radar_timeline.json')
                .then(response => response.json())
                .then(data => {
                    radarImages = data.images;
                    if (!radarImages || radarImages.length === 0) {
                        console.warn("Không có ảnh radar nào được tìm thấy");
                        resolve(false);
                        return;
                    }
                    const sourceId = 'radar-timeline-source';
                    const layerId = 'radar-timeline-layer';
                    if (!radarBounds) {
                        radarBounds = {
                            north: 25.2,
                            south: 7.2,
                            east: 115.0,
                            west: 97.0
                        };
                    }
                    const coordinates = convertBoundsToCoordinates(radarBounds);
                    if (coordinates) {
                        if (!map.getSource(sourceId)) {
                            map.addSource(sourceId, {
                                type: 'image',
                                url: radarImages[radarImages.length - 1].path,
                                coordinates: coordinates
                            });
                        }
                        if (!map.getLayer(layerId)) {
                            map.addLayer({
                                id: layerId,
                                type: 'raster',
                                source: sourceId,
                                layout: {
                                    visibility: 'none'
                                },
                                paint: {
                                    'raster-opacity': 0.9,
                                    'raster-fade-duration': 0
                                }
                            });
                        }
                        console.log('Tạo source và layer radar thành công (async)');
                        resolve(true);
                    } else {
                        console.error('Không xác định được toạ độ radar bounds');
                        resolve(false);
                    }
                })
                .catch(error => {
                    console.error("Lỗi khi tải dữ liệu radar:", error);
                    reject(error);
                });
        });
    }

    // Cập nhật giao diện thanh timeline
    function updateTimelineUI() {
        // Lấy thời điểm hiện tại theo UTC+7
        const now = new Date(Date.now());
        console.log("Current time (UTC+7):", now);
        const endTime = new Date(now);
        const startTime = new Date(now.getTime() - 12 * 60 * 60 * 1000);
 
        // Định dạng hiển thị (dd/MM/yyyy HH:mm)
        function formatVNTime(d) {
            return d.getDate().toString().padStart(2, '0') + '/' +
                (d.getMonth()+1).toString().padStart(2, '0') + '/' +
                d.getFullYear() + ' ' +
                d.getHours().toString().padStart(2, '0') + ':' +
                d.getMinutes().toString().padStart(2, '0');
        }

        // Cập nhật nhãn thời gian
        document.querySelector('.timeline-start-label').textContent = formatVNTime(startTime);
        document.querySelector('.timeline-end-label').textContent = formatVNTime(endTime);

        // Cập nhật thanh trượt
        const slider = document.getElementById('timeline-slider');
        slider.min = 0;
        slider.max = timelineSteps.length - 1;
        slider.value = timelineSteps.length - 1;
    }
    
    // Định dạng thời gian dạng ISO hoặc Date về dd/mm/yyyy HH:MM (UTC+7)
    function formatVNTime(ts) {
        let d = (ts instanceof Date) ? ts : new Date(ts);
        // Luôn cộng thêm 7 giờ để chuyển UTC -> UTC+7
        let d7 = new Date(d.getTime() + 7 * 60 * 60 * 1000);
        // Định dạng dd/mm/yyyy HH:MM
        let yyyy = d7.getFullYear();
        let mm = ('0' + (d7.getMonth() + 1)).slice(-2);
        let dd = ('0' + d7.getDate()).slice(-2);
        let HH = ('0' + d7.getHours()).slice(-2);
        let MM = ('0' + d7.getMinutes()).slice(-2);
        return `${dd}/${mm}/${yyyy} ${HH}:${MM}`;
    }
    
    // Thiết lập sự kiện cho thanh timeline
    function setupTimelineEvents() {
        const slider = document.getElementById('timeline-slider');
        const playPauseBtn = document.getElementById('play-pause-btn');
        
        // Sự kiện khi kéo thanh trượt
        slider.addEventListener('input', function() {
            currentTimelineIndex = parseInt(this.value);
            showTimeline(currentTimelineIndex);
            
            // Nếu đang chạy animation, dừng lại
            if (isPlaying) {
                stopAnimation();
                playPauseBtn.textContent = '\u25B6'; // Play symbol
                isPlaying = false;
            }
        });
        
        // Sự kiện khi nhấn nút play/pause
        playPauseBtn.addEventListener('click', function() {
            if (isPlaying) {
                stopAnimation();
                this.textContent = '\u25B6'; // Play symbol
            } else {
                startAnimation();
                this.textContent = '\u23F8'; // Pause symbol
            }
            isPlaying = !isPlaying;
        });
    }
    
    // Hiển thị dữ liệu theo index
    function showTimeline(index) {
        if (!timelineTimestamps || timelineTimestamps.length === 0) {
            console.error("Không có dữ liệu timeline");
            return;
        }
        // Kiểm tra index hợp lệ
        if (index < 0) index = 0;
        if (index >= timelineTimestamps.length) index = timelineTimestamps.length - 1;
        currentTimelineIndex = index;
        const currentTimestamp = timelineTimestamps[currentTimelineIndex];
        console.log(`Selected timeline time: ${currentTimestamp}`);
        document.getElementById('current-time-display').textContent = formatVNTime(currentTimestamp);
        document.getElementById('timeline-slider').value = currentTimelineIndex;
        // Sử dụng trực tiếp currentTimestamp (UTC) cho các bước tiếp theo
        //convert time từ dạng 2025-04-15T11:13  sang dạng 202504151113, không dùng các hàm thời gian, hãy chỉ xử lý ký tự
        const year = currentTimestamp.slice(0, 4);
        const month = currentTimestamp.slice(5, 7);
        const day = currentTimestamp.slice(8, 10);
        const hour = currentTimestamp.slice(11, 13);
        const minute = currentTimestamp.slice(14, 16);
        const utctime = `${year}${month}${day}${hour}${minute}00`;
        
        // applyLayerVisibility(utctime);
        
        if (layerVisibility.radar) {
            updateRadarLayer(utctime);
        }
        if (layerVisibility.lightning) {
            updateLightningLayerByTime(utctime);
        }
        if (layerVisibility.vrain) {
            updateVrainLayerByTime(utctime);
        }
        // Thử gọi hàm updateLayersVisibility từ layer_controls.js nếu có
        if (typeof window.updateLayersVisibility === 'function') {
            try {
                window.updateLayersVisibility();
            } catch (error) {
                console.error('Lỗi khi gọi updateLayersVisibility:', error);
            }
        }
    }
    function getCurrentSelectTime() {
        const slider = document.getElementById('timeline-slider');
        const idx = parseInt(slider.value, 10);
        return timelineSteps[idx]; // hoặc timelineSteps[idx].timestamp nếu là object
    }
    // Cập nhật lớp radar trên bản đồ
    // Giải phóng RAM cho radar: chỉ giữ ảnh đang hiển thị, clear ảnh cũ sau 1 phút
    let currentRadarUrl = null;
    let radarCleanupTimers = {};
    function updateRadarLayer(utctime) {
        const sourceId = 'radar-timeline-source';
        const layerId = 'radar-timeline-layer';
        if (!map.getSource(sourceId)) {
            console.warn('Radar source chưa được khởi tạo, không thể update.');
            return;
        }        // Tạo radarFilePath từ utctime (dạng 20250415111300)
        // Làm tròn phút về các mốc 00,10,20,30,40,50
        const year = utctime.slice(0, 4);
        const month = utctime.slice(4, 6);
        const day = utctime.slice(6, 8);
        const hour = utctime.slice(8, 10);
        const minute = utctime.slice(10, 12);
        let minuteInt = parseInt(minute, 10);
        let roundedMinute = Math.floor(minuteInt / 10) * 10;
        if (roundedMinute === 0) roundedMinute = "00";
        else roundedMinute = roundedMinute.toString().padStart(2, "0");
        const radarFileName = `ph_mosaic_${year}${month}${day}${hour}${roundedMinute}00-final.png`;
        const radarFilePath = `data/radar_source/${radarFileName}`;
        // Kiểm tra tồn tại file radar bằng HEAD trước khi update
        fetch(radarFilePath, { method: 'HEAD' })
            .then(response => {
                if (response.ok) {
                    // Ảnh tồn tại, cập nhật như bình thường
                    try {
                        // Luôn updateImage
                        map.getSource(sourceId).updateImage({ url: radarFilePath });
                        map.setLayoutProperty(layerId, 'visibility', 'visible');
                        // Đánh dấu url radar hiện tại
                        currentRadarUrl = radarFilePath;
                        // Đặt timer cleanup cho ảnh này
                        if (radarCleanupTimers[radarFilePath]) {
                            clearTimeout(radarCleanupTimers[radarFilePath]);
                        }
                        radarCleanupTimers[radarFilePath] = setTimeout(() => {
                            // Nếu ảnh này không còn là ảnh đang hiển thị nữa thì clear khỏi RAM
                            if (currentRadarUrl !== radarFilePath) {
                                try {
                                    map.getSource(sourceId).updateImage({ url: '' });
                                } catch (e) {}
                            }
                            delete radarCleanupTimers[radarFilePath];
                        }, 60 * 1000); // 1 phút
                    } catch (err) {
                        map.setLayoutProperty(layerId, 'visibility', 'none');
                        console.warn('Radar image lỗi, layer bị ẩn:', radarFilePath);
                    }
                } else {
                    // Ảnh không tồn tại, chỉ ẩn layer radar
                    if (map.getLayer(layerId)) {
                        map.setLayoutProperty(layerId, 'visibility', 'none');
                    }
                    console.warn('Radar image not found (HEAD), layer hidden:', radarFilePath);
                }
            })
            .catch(() => {
                if (map.getLayer(layerId)) {
                    map.setLayoutProperty(layerId, 'visibility', 'none');
                }
                console.warn('Radar image HEAD request fail, layer hidden:', radarFilePath);
            });
    }
    
    // Xuất hàm updateRadarLayer ra toàn cục để có thể gọi từ layer_controls.js
    window.updateRadarLayer = updateRadarLayer;
    
    // create fuction that call lightning layer with utctime as an input argument
    function updateLightningLayerByTime(utctime) {
        // Chuyển utctime sang Date
        const dt = typeof utctime === 'string'
            ? new Date(
                utctime.slice(0,4) + '-' +
                utctime.slice(4,6) + '-' +
                utctime.slice(6,8) + 'T' +
                utctime.slice(8,10) + ':' +
                utctime.slice(10,12) + ':' +
                utctime.slice(12,14) + '+07:00'
            )
            : new Date(utctime);
        // Mốc đầu giờ
        const start = new Date(dt); start.setMinutes(0,0,0);
        // Mốc cuối giờ
        const end = new Date(start); end.setHours(start.getHours()+1);

        // Lấy toàn bộ lightningStrikes trong khoảng thời gian
        let allStrikes = [];
        timeLayersData.forEach(layer => {
            if (Array.isArray(layer.lightningStrikes)) {
                layer.lightningStrikes.forEach(pt => {
                    if (pt.timestamp) {
                        const t = new Date(
                            pt.timestamp.slice(0,4) + '-' +
                            pt.timestamp.slice(4,6) + '-' +
                            pt.timestamp.slice(6,8) + 'T' +
                            pt.timestamp.slice(8,10) + ':' +
                            pt.timestamp.slice(10,12) + ':' +
                            pt.timestamp.slice(12,14) + '+07:00'
                        );
                        if (t >= start && t < end) {
                            // Lightning xuống đất: dùng symbol cho icon-image
                            if (pt["loại sét"] === 'CG') {
                                pt.symbol = 'lightningCG';
                            }
                            if (pt["loại sét"] === 'IC') {
                                pt.symbol = 'lightningIC';
                            }
                            allStrikes.push(pt);
                        }
                    }
                });
            }
        });
        // console.log("All strikes:", allStrikes);
        // Hiển thị lên bản đồ
        const lightningSourceId = 'lightning-source-filtered';
        const geojson = {
            type: 'FeatureCollection',
            features: allStrikes.map(pt => {
                let v = Math.abs(Number(pt.value));
                if (isNaN(v)) v = 1;
                if (v > 100) v = 100;
                if (v < 0) v = 0;
                return {
                    type: 'Feature',
                    geometry: { type: 'Point', coordinates: [pt.lon, pt.lat] },
                    properties: {
                        ...pt,
                        intensity: v
                    }
                }
            })
        };

        if (!map.getSource(lightningSourceId)) {
            map.addSource(lightningSourceId, { type: 'geojson', data: geojson });
        } else {
            map.getSource(lightningSourceId).setData(geojson);
        }
        // Gắn tooltip hover cho các điểm sét
        addTooltipOnHover('lightning-layer', 'tooltipHtml');
        // Chỉ còn 1 layer lightning duy nhất, icon lấy từ thuộc tính symbol của từng feature
        if (!map.getLayer('lightning-layer')) {
            map.addLayer({
                id: 'lightning-layer',
                type: 'symbol',
                source: lightningSourceId,
                layout: {
                    'icon-image': ['get', 'symbol'],
                    'icon-size': [
    'match', ['get', 'symbol'],
        'lightningCG', [
            '*', 0.02,
            [
                'interpolate', ['linear'], ['get', 'intensity'],
                    0, 0.7,
                    50, 1.5,
                    100, 3
            ]
        ],
        'lightningIC', [
            '*', 0.02,
            [
                'interpolate', ['linear'], ['get', 'intensity'],
                    0, 0.7,
                    50, 1.5,
                    100, 3
            ]
        ],
    1.0
],
                    'icon-allow-overlap': true
                }
            });
        }
        // Nếu đã có layer thì đảm bảo hiển thị
        if (map.getLayer('lightning-layer')) {
            map.setLayoutProperty('lightning-layer', 'visibility', 'visible');
        }
        
    
        // thêm log ở đây in ra có bao nhiêu lightning CG và GG
        console.log(`updateLightningLayerByTime called with: ${utctime}`);
        console.log(`updateLightningLayerByTime called with: ${allStrikes.length} lightning strikes`);
        console.log(`updateLightningLayerByTime called with: ${allStrikes.filter(pt => pt.type === 'CG').length} CG strikes`);
        console.log(`updateLightningLayerByTime called with: ${allStrikes.filter(pt => pt.type === 'IC').length} IC strikes`);
    }
    
    //create fuction that call vrain layer with utctime as an input argument
    function updateVrainLayerByTime(utctime) {
        console.log(`updateVrainLayerByTime called with: ${utctime}`);
        // Ẩn tất cả các lớp VRAIN trước
        // Ẩn duy nhất 1 layer VRAIN nếu đã tồn tại
    if (map.getLayer('vrain-layer')) {
        map.setLayoutProperty('vrain-layer', 'visibility', 'none');
    }
    // Định nghĩa id cho layer VRAIN
    const vrainLayerId = 'vrain-layer';
    // lấy số giờ trong utctime
    // Lấy phần yyyyMMddHH từ utctime
    const hourPrefix = utctime.slice(0, 10);
    // Lấy tất cả các layer có timestamp bắt đầu bằng hourPrefix
    const matchedLayers = timeLayersData.filter(layer => layer.timestamp && layer.timestamp.startsWith(hourPrefix));
    // Gom tất cả các điểm VRAIN từ các layer này
    const allVrainPoints = matchedLayers.flatMap(layer => Array.isArray(layer.vrainPoints) ? layer.vrainPoints : []);
    // In ra số lượng điểm vrain tại mỗi thời điểm
    matchedLayers.forEach(layer => {
        console.log(`timestamp: ${layer.timestamp} - ${Array.isArray(layer.vrainPoints) ? layer.vrainPoints.length : 0} điểm VRAIN`);
    });
    if (allVrainPoints.length > 0) {
        const vrainSourceId = 'vrain-source';
        // Chuẩn hóa dữ liệu GeoJSON
        let geojson;
        if (Array.isArray(allVrainPoints)) {
            geojson = {
                type: 'FeatureCollection',
                features: allVrainPoints.map(pt => ({
                    type: 'Feature',
                    geometry: {
                        type: 'Point',
                        coordinates: [pt.lon, pt.lat]
                    },
                    properties: {
                        ...pt,
                        tooltipHtml: (() => {
                        let address = pt.address;
                        if (!address && pt.popupHtml) {
                            const m = pt.popupHtml.match(/<b>Địa chỉ:<\/b>\s*([^<]+)/);
                            if (m) address = m[1].trim();
                        }
                        let stationName = pt.name;
                        if (!stationName && pt.popupHtml) {
                            const m = pt.popupHtml.match(/<b>Trạm:<\/b>\s*([^<]+)/);
                            if (m) stationName = m[1].trim();
                        }
                        if (!stationName) stationName = 'Không xác định';
                        let rainLevel = pt.level || 'Không xác định';
                        let rainValue = (pt.depth !== undefined && pt.depth !== null) ? pt.depth : (pt.value !== undefined && pt.value !== null) ? pt.value : (pt.rain !== undefined && pt.rain !== null) ? pt.rain : '';
                        if (!rainValue && pt.popupHtml) {
                            const m = pt.popupHtml.match(/<b>Lượng mưa:<\/b>\s*([^<]+)/);
                            if (m) rainValue = m[1].trim();
                        }
                        if (!rainValue) rainValue = '-';
                        let updateTime = pt.time;
                        if (!updateTime && pt.popupHtml) {
                            const m = pt.popupHtml.match(/Cập nhật VRAIN:<\/b>\s*([^<]+)/);
                            if (m) updateTime = m[1].trim();
                        }
                        if (!updateTime) updateTime = '-';
                        return `<b>Trạm:</b> ${stationName}<br>` +
                            `<b>Địa chỉ:</b> ${address || '-'}<br>` +
                            `<b>Mức mưa:</b> ${rainLevel}<br>` +
                            `<b>Giá trị:</b> ${rainValue} mm<br>` +
                            `<b>Cập nhật:</b> ${updateTime}`;
                                            })()
                                            }
                                        }))
                                    };
        } else if (allVrainPoints.vrainGeoJSON) {
            geojson = allVrainPoints.vrainGeoJSON;
        } else {
            geojson = { type: 'FeatureCollection', features: [] };
        }
        // Nếu chưa có source thì add
        if (!map.getSource(vrainSourceId)) {
            map.addSource(vrainSourceId, {
                type: 'geojson',
                data: geojson
            });
        } else {
            map.getSource(vrainSourceId).setData(geojson);
        }
        // Gắn tooltip hover cho các điểm VRAIN (chỉ cần gọi 1 lần sau khi đã add/update source/layer)
        setupSharedTooltip();
        addTooltipOnHover(vrainLayerId, 'tooltipHtml');
        // Nếu chưa có layer thì add
        if (!map.getLayer(vrainLayerId)) {
            map.addLayer({
                id: vrainLayerId,
                    type: 'symbol',
                    source: vrainSourceId,
                    layout: {
                        'icon-image': [
                            'match',
                            ['get', 'level'],
                            'Mưa nhỏ', 'rain-column-small',
                            'Mưa vừa', 'rain-column-medium',
                            'Mưa to', 'rain-column-large',
                            'Mưa rất to', 'rain-column-very-large',
                            'rain-column-small'
                        ],
                        'icon-size': [
                            'interpolate', ['linear'], ['get', 'height'],
                            5, 0.2,
                            10, 0.3,
                            18, 0.4,
                            25, 0.5
                        ],
                        'icon-allow-overlap': true,
                        'icon-ignore-placement': true,
                        'visibility': 'visible'
                    }
                });
            } else {
                map.setLayoutProperty(vrainLayerId, 'visibility', 'visible');
            }
            // Đảm bảo icon cột mưa đã tạo
            if (!map.hasImage('rain-column-small')) {
                createRainColumnByLevel('rain-column-small', '#0d47a1', '#64b5f6'); // Mưa nhỏ: xanh dương
            }
            if (!map.hasImage('rain-column-medium')) {
                createRainColumnByLevel('rain-column-medium', '#1b5e20', '#81c784'); // Mưa vừa: xanh lá cây
            }
            if (!map.hasImage('rain-column-large')) {
                createRainColumnByLevel('rain-column-large', '#b71c1c', '#ef5350'); // Mưa to: đỏ
            }
            if (!map.hasImage('rain-column-very-large')) {
                createRainColumnByLevel('rain-column-very-large', '#4a148c', '#9c27b0'); // Mưa rất to: tím
            }
            
        } else {
            console.log('Không có dữ liệu VRAIN cho thời điểm này');
        }
}

// Bắt đầu animation
    function startAnimation() {
        if (animationInterval) {
            clearInterval(animationInterval);
        }
        animationInterval = setInterval(() => {
            if (!Array.isArray(timelineSteps) || timelineSteps.length === 0) return;
            currentTimelineIndex = (typeof currentTimelineIndex === 'number' ? currentTimelineIndex : 0);
            currentTimelineIndex = (currentTimelineIndex + 1) % timelineSteps.length;
            showTimeline(currentTimelineIndex);
        }, 500); // 500ms mỗi frame
    }
    
    // Dừng animation
    function stopAnimation() {
        if (animationInterval) {
            clearInterval(animationInterval);
            animationInterval = null;
        }
    }
    
    // Kiểm tra và tải ảnh radar mới
    function checkForNewRadarImages() {
        console.log('Kiểm tra dữ liệu radar mới...');
        try {
            // Sử dụng đường dẫn tương đối thay vì tuyệt đối để tránh lỗi kết nối
            fetch('./data/radar_timeline.json?_=' + new Date().getTime(), {
                method: 'GET',
                headers: {
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Nhận được dữ liệu radar mới');
                // Kiểm tra xem có ảnh mới hay không
                const oldLength = radarImages.length;
                
                // Cập nhật danh sách ảnh
                radarImages = data.images;
                
                // Cập nhật giao diện
                updateTimelineUI(data.start_time, data.end_time);
                
                if (radarImages.length > oldLength) {
                    console.log(`Đã tìm thấy ${radarImages.length - oldLength} ảnh radar mới`);
                    
                    // Hiển thị thông tin về ảnh mới nhất
                    if (radarImages.length > 0) {
                        const latestImage = radarImages[radarImages.length - 1];
                        console.log(`Ảnh mới nhất: ${latestImage.filename} - ${latestImage.display_time}`);
                    }
                    
                    // Nếu không đang chạy animation, hiển thị ảnh mới nhất
                    if (!isPlaying) {
                        currentImageIndex = radarImages.length - 1;
                        showRadarImage(currentImageIndex);
                    }
                }
            })
            .catch(error => {
                console.error("Lỗi khi kiểm tra ảnh radar mới:", error);
            });
        } catch (error) {
            console.error("Lỗi ngoại lệ khi kiểm tra ảnh radar mới:", error);
        }
    }

     // --- Helper Function to convert Leaflet bounds to MapLibre coordinates ---
     function convertBoundsToCoordinates(bounds) {
         if (!bounds) return null;
         
         // Nếu bounds có dạng {north, south, east, west}
         if (bounds.north !== undefined && bounds.south !== undefined && 
             bounds.east !== undefined && bounds.west !== undefined) {
             return [
                 [bounds.west, bounds.north], // top-left (NW)
                 [bounds.east, bounds.north], // top-right (NE)
                 [bounds.east, bounds.south], // bottom-right (SE)
                 [bounds.west, bounds.south]  // bottom-left (SW)
             ];
         }
         
         // Nếu bounds có dạng [[lat_sw, lon_sw], [lat_ne, lon_ne]]
         try {
             const lat_sw = bounds[0][0];
             const lon_sw = bounds[0][1];
             const lat_ne = bounds[1][0];
             const lon_ne = bounds[1][1];

             // Check for valid numbers
             if ([lat_sw, lon_sw, lat_ne, lon_ne].some(isNaN)) {
                 throw new Error("Bounds contain non-numeric values.");
             }

             // Calculate the other two corners
             const lat_nw = lat_ne;
             const lon_nw = lon_sw;
             const lat_se = lat_sw;
             const lon_se = lon_ne;

             return [
                 [lon_nw, lat_nw],
                 [lon_ne, lat_ne],
                 [lon_se, lat_se],
                 [lon_sw, lat_sw]
             ];
         } catch (e) {
             console.error("Error converting bounds to coordinates:", bounds, e);
             return null; // Return null on error
         }
     }
     

     // --- Shared Tooltip Logic ---
     let sharedTooltip = null;

     function setupSharedTooltip() {
         if (!sharedTooltip) {
             sharedTooltip = new maplibregl.Popup({
                 closeButton: false,
                 closeOnClick: false,
                 anchor: 'bottom',
                 offset: 15 // Offset from the point
             });
         }
     }

     function addTooltipOnHover(layerId, tooltipProperty) {
         map.on('mousemove', layerId, (e) => {
             if (e.features?.length > 0) {
                 map.getCanvas().style.cursor = 'pointer';
                 const feature = e.features[0];
                 const tooltipHtml = feature.properties[tooltipProperty] || 'Info';

                 // Ensure popup doesn't open if already open with same content/location
                 if (!sharedTooltip.isOpen() || sharedTooltip.getLngLat().toString() !== e.lngLat.toString()) {
                    sharedTooltip.setLngLat(e.lngLat)
                                .setHTML(tooltipHtml)
                                .addTo(map);
                 }
             }
         });

         map.on('mouseleave', layerId, () => {
             map.getCanvas().style.cursor = '';
             if (sharedTooltip.isOpen()) {
                 // Ẩn tooltip ngay lập tức khi di chuột ra khỏi đối tượng
                 sharedTooltip.remove();
             }
         });
     }


    function addStaticMarkers(markersData) {
        if (!markersData?.length) { console.warn("No static marker data provided."); return;}
        console.log(`Adding ${markersData.length} static markers...`);
        markersData.forEach(markerData => {
            if (markerData.lat != null && markerData.lon != null) {
                try {
                    const lon = markerData.lon;
                    const lat = markerData.lat;

                    // Tạo marker icon (không gắn popup)
                    const marker = new maplibregl.Marker()
                        .setLngLat([lon, lat])
                        .addTo(map);

                    // Luôn hiển thị label cạnh marker
                    if (markerData.label || markerData.name) {
                        const labelElement = document.createElement('div');
                        labelElement.className = 'static-marker-label';
                        labelElement.style.position = 'absolute';
                        labelElement.style.background = 'rgba(255,255,255,0.85)';
                        labelElement.style.borderRadius = '4px';
                        labelElement.style.padding = '2px 8px';
                        labelElement.style.fontWeight = 'normal';
                        labelElement.style.fontSize = '14px';
                        labelElement.style.color = 'red';
                        labelElement.style.boxShadow = '0 1px 4px rgba(0,0,0,0.08)';
                        labelElement.style.cursor = 'pointer';
                        labelElement.innerText = markerData.label || markerData.name;

                        // Gắn sự kiện click vào label để mở popup
                        labelElement.addEventListener('click', function(e) {
                            e.stopPropagation();
                            document.querySelectorAll('.maplibregl-popup').forEach(p => p.remove());
                            const popup = new maplibregl.Popup({ offset: 25, anchor: 'bottom' })
                                .setLngLat([lon, lat])
                                .setHTML(markerData.popupHtml || 'Chi tiết')
                                .addTo(map);
                        });

                        new maplibregl.Marker({
                            element: labelElement,
                            anchor: 'left',
                            offset: [10, -7]
                        })
                        .setLngLat([lon, lat])
                        .addTo(map);
                    }
                } catch(e) {
                    console.error(`Error creating static marker at [${markerData.lon}, ${markerData.lat}]:`, e);
                }
            } else {
                console.warn("Static marker data missing coordinates:", markerData);
            }
        });
        console.log("Finished adding static markers.");
    }

    // Hàm tạo hình ảnh cột mưa theo level
    function createRainColumnByLevel(imageName, colorBottom, colorTop) {
        try {
            // Kiểm tra xem đã có hình ảnh này chưa
            if (map.hasImage(imageName)) {
                console.log(`Hình ảnh ${imageName} đã tồn tại`);
                return;
            }
            
            // Tạo canvas và vẽ hình cột mưa
            const width = 60; // Chiều rộng nhỏ
            const height = 80; // Chiều cao vừa phải
            const canvas = document.createElement('canvas');
            canvas.width = width;
            canvas.height = height;
            const ctx = canvas.getContext('2d');
            
            // Tạo gradient từ dưới lên trên
            const gradient = ctx.createLinearGradient(0, height, 0, 0);
            gradient.addColorStop(0, colorBottom); // Màu đậm ở dưới
            gradient.addColorStop(1, colorTop); // Màu nhạt ở trên
            
            // Vẽ bóng đổ mạnh hơn
            ctx.shadowColor = 'rgba(0, 0, 0, 0.7)';
            ctx.shadowBlur = 8;
            ctx.shadowOffsetX = 4;
            ctx.shadowOffsetY = 6;
            
            // Vẽ bóng đổ phía sau cột mưa
            ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
            ctx.beginPath();
            ctx.rect(8, 9, width - 8, height - 10);
            ctx.fill();
            
            // Vẽ hình cột mưa
            ctx.shadowColor = 'rgba(0, 0, 0, 0.7)';
            ctx.shadowBlur = 8;
            ctx.shadowOffsetX = 4;
            ctx.shadowOffsetY = 6;
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.rect(4, 5, width - 8, height - 10);
            ctx.fill();
            
            // Vẽ viền đen mỏng
            ctx.shadowColor = 'transparent'; // Tắt bóng đổ khi vẽ viền
            ctx.strokeStyle = 'rgba(0, 0, 0, 0.7)';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.rect(4, 5, width - 8, height - 10);
            ctx.stroke();
            
            // Thêm hình ảnh vào map
            const imageData = ctx.getImageData(0, 0, width, height);
            map.addImage(imageName, imageData, { pixelRatio: 2 });
            console.log(`Tạo hình ảnh ${imageName} thành công`);
        } catch (e) {
            console.error(`Lỗi khi tạo hình ảnh ${imageName}:`, e);
        }
    }

    function normalizeLocationNameJS(name) {
        if (typeof name !== 'string') return "";
        
        // Normalize Unicode NFD, remove diacritics, lowercase, trim
        let normalized = name.normalize("NFD").replace(/[\u0300-\u036f]/g, "").toLowerCase().trim();
        
        // Loại bỏ các tiền tố phổ biến như "Tỉnh", "Thành phố", v.v.
        const prefixesToRemove = ['tinh ', 'thanh pho ', 'tp ', 'huyen ', 'quan ', 'thi xa '];
        for (const prefix of prefixesToRemove) {
            if (normalized.startsWith(prefix)) {
                normalized = normalized.slice(prefix.length);
            }
        }
        
        // Loại bỏ các hậu tố phổ biến
        const suffixesToRemove = [' province', ' city', ' district', ' town', ' ward', ' county', ' region', ' area'];
        for (const suffix of suffixesToRemove) {
            if (normalized.endsWith(suffix)) {
                normalized = normalized.slice(0, -suffix.length);
            }
        }
        
        // Loại bỏ ký tự đặc biệt và khoảng trắng thừa
        normalized = normalized.replace(/[^a-z0-9\s]/g, '').replace(/\s+/g, ' ').trim();
        
        // Chỉ log khi có sự thay đổi đáng kể
        // if (normalized !== name.toLowerCase().trim()) {
        //     console.log(`DEBUG normalizeLocationNameJS: '${name}' -> '${normalized}'`);
        // }
        return normalized;
    }

    function createForecastTableHTML(locationData) {
        if (!locationData) return "<p>Không có dữ liệu dự báo cho địa điểm này.</p>";
        let html = `<div class="forecast-content">`;
        
        // Xử lý tên hiển thị địa điểm
        let displayName = locationData.vietnamese_display_name || locationData.location_display || 'Địa điểm được chọn';
        
        // Thêm tiền tố "Tỉnh" hoặc "Thành phố" nếu chưa có
        if (displayName && !displayName.match(/^(Tỉnh|Thành phố|Huyện|Quận|Thị xã)/i)) {
            // Danh sách các thành phố trực thuộc trung ương
            const centralCities = [
                'hà nội', 'hồ chí minh', 'hải phòng', 'đà nẵng', 'cần thơ'
            ];
            
            // Kiểm tra xem có phải là thành phố trực thuộc trung ương không
            const normalizedName = normalizeLocationNameJS(displayName);
            if (centralCities.some(city => normalizedName.includes(normalizeLocationNameJS(city)))) {
                displayName = `Thành phố ${displayName}`;
            } else {
                displayName = `Tỉnh ${displayName}`;
            }
        }
        
        html += `<h3 style="text-align: center; margin-top: 5px; margin-bottom: 10px;">Bảng Dự báo cho ${displayName}</h3>`;
        // Minimize button calls global function
        html += `<button title="Thu nhỏ" class="forecast-minimize-button" onclick="event.stopPropagation(); minimizeForecastTable(); return false;">−</button>`;

        html += `<h4>Hiện tại & Tối nay</h4>`;
        html += `<table class="forecast-table today-tonight-table"><thead><tr><th>Thời điểm</th><th>Nhiệt độ</th><th>Độ ẩm</th><th>Điều kiện</th><th>Gió</th></tr></thead><tbody>`;
        const today = locationData.today || {};
        html += `<tr><td>Ban ngày</td><td>${today.temp || '-'}</td><td>${today.humidity || '-'}</td><td>${today.condition || '-'}</td><td>${today.wind_dir || ''} ${today.wind_speed || ''}</td></tr>`;
        const tonight = locationData.tonight || {};
        html += `<tr><td>Tối/Đêm</td><td>${tonight.temp || '-'}</td><td>${tonight.humidity || '-'}</td><td>${tonight.condition || '-'}</td><td>${tonight.wind_dir || ''} ${tonight.wind_speed || ''}</td></tr>`;
        html += `</tbody></table>`;

        if (locationData.daily && locationData.daily.length > 0) {
            html += `<h4 style="margin-top: 15px;">Dự báo ${locationData.daily.length} ngày tới</h4>`;
            html += `<table class="forecast-table daily-table"><thead><tr><th>Ngày</th><th>Nhiệt độ (Cao/Thấp)</th><th>Khả năng mưa</th><th>Điều kiện</th><th>Gió</th></tr></thead><tbody>`;
            locationData.daily.forEach(day => {
                let displayDate = day.date || 'N/A';
                try {
                    if (day.date && day.date.includes('/')) { // Basic check for d/m/y format
                        const parts = day.date.split('/');
                        if (parts.length === 3) {
                            const [d, m, y] = parts;
                             // Use UTC to avoid timezone shifting date, important for consistency
                             const dateObj = new Date(Date.UTC(parseInt(y), parseInt(m) - 1, parseInt(d)));
                             const options = { weekday: 'short', day: '2-digit', month: '2-digit', timeZone: 'UTC' };
                             const locale = navigator.language.startsWith('vi') ? 'vi-VN' : 'en-US';
                             if (!isNaN(dateObj)) { // Check if date is valid
                                 displayDate = dateObj.toLocaleDateString(locale, options);
                             } else {
                                 console.warn("Invalid date created from:", day.date);
                                 displayDate = day.date; // Fallback to original string
                             }
                        } else {
                             displayDate = day.date; // Fallback if not d/m/y
                        }
                    }
                } catch (e) {
                    console.warn("Error formatting date:", day.date, e);
                    displayDate = day.date; // Fallback
                }
                html += `<tr><td>${displayDate}</td><td>${day.max_temp || '-'}/${day.min_temp || '-'}</td><td>${day.precip_prob || '-'}</td><td>${day.condition || '-'}</td><td>${day.wind_dir || ''} ${day.wind_speed || ''}</td></tr>`;
            });
            html += `</tbody></table>`;
        }
        html += `</div>`; // Close forecast-content
        return html;
    }

    // --- UI MANAGEMENT FUNCTIONS (Attached to window for onclick) ---
    window.minimizeForecastTable = function(event) {
        if (event && event.stopPropagation) { event.stopPropagation(); event.preventDefault(); }
        if (forecastTableContainer) forecastTableContainer.style.display = 'none';
        if (forecastToggleButton) {
            forecastToggleButton.style.display = 'block'; // Show the button
             // Ensure button is clickable (potentially adjust z-index via CSS if needed)
             // forecastToggleButton.style.zIndex = '1500'; // Example if needed
        }
        return false; // Prevent default behavior
    }

    window.showForecastTable = function(event) {
        if (event && event.stopPropagation) { event.stopPropagation(); event.preventDefault(); }
        if (forecastTableContainer) {
            // Ensure it's above other controls if needed (CSS is preferred)
            // forecastTableContainer.style.zIndex = '1500';
            forecastTableContainer.style.display = 'block'; // Show the table
            // Add click listener to prevent clicks inside table from reaching map
            if (!forecastTableContainer._hasClickHandler) {
                forecastTableContainer.addEventListener('click', function(e) { e.stopPropagation(); });
                forecastTableContainer._hasClickHandler = true;
            }
            // Provide default content if empty
            if (forecastTableContainer.innerHTML.trim() === "") {
                forecastTableContainer.innerHTML = '<div class="forecast-content" style="padding: 15px; text-align: center;">Chưa có dữ liệu dự báo. Vui lòng tìm kiếm một tỉnh/thành phố.</div>';
            }
            if (forecastToggleButton) forecastToggleButton.style.display = 'none'; // Hide the button
            console.log("Forecast table shown.");
        } else {
            console.error("Cannot show forecast table: container element not found.");
        }
        return false; // Prevent default behavior
    }

    window.toggleLegend = function(button) {
        if (event && event.stopPropagation) { event.stopPropagation(); event.preventDefault(); }
        const legendContainer = document.getElementById('legend');
        if (!legendContainer) { console.error("Cannot find #legend to toggle."); return false; }
        const isMinimized = legendContainer.classList.toggle('minimized');
        if (button) { // Update button text/title
            button.innerHTML = isMinimized ? '≡' : '×'; // Simple icons
            button.title = isMinimized ? 'Expand legend' : 'Collapse legend';
        }
        return false; // Prevent default behavior
    }

    // --- EVENT HANDLERS ---

    // Function to handle clicking directly on a province feature
     function handleProvinceClick(provinceName) {
         if (!map) return;
         console.log("Handling click on province feature:", provinceName);
         clearHighlightAndForecast(); // Clear previous state

         const normalizedName = normalizeLocationNameJS(provinceName);
         const provinceData = provinceFeaturesMap[normalizedName];

         if (provinceData && provinceData.feature) {
             // Highlight the province
             highlightProvince(provinceName); // Use the original name from feature properties

             // Find forecast data
             const locationForecastData = allForecastData[normalizedName];
             if (locationForecastData) {
                 const forecastHTML = createForecastTableHTML(locationForecastData);
                 if (forecastTableContainer) {
                     forecastTableContainer.innerHTML = forecastHTML;
                     showForecastTable(); // Show table
                 } else { console.error("forecastTableContainer missing!"); }
             } else {
                 // Show message: No forecast data
                 showNoForecastMessage(provinceName);
             }

             // Zoom to the province bounds if available
             if (provinceData.feature.bbox) {
                 try {
                     // MapLibre bbox format: [minLon, minLat, maxLon, maxLat]
                     map.fitBounds(provinceData.feature.bbox, { padding: 40 });
                     console.log(`Zoomed to bounds of clicked province: ${provinceName}`);
                 } catch (e) {
                     console.error("Error fitting bounds for clicked province:", e);
                     // Optionally flyTo center if bounds fail
                     if (provinceData.feature.geometry?.type === 'Point') {
                         map.flyTo({ center: provinceData.feature.geometry.coordinates, zoom: 9 });
                     } else {
                          // Attempt to calculate center if Polygon/MultiPolygon (more complex)
                          console.warn("Cannot easily determine center for non-Point feature without bounds.");
                     }
                 }
             } else {
                 console.warn("No bounding box found for clicked province feature:", provinceName);
                 // Fly to center if possible (e.g., if it's a Point feature representing the province)
                 if (provinceData.feature.geometry?.type === 'Point') {
                       map.flyTo({ center: provinceData.feature.geometry.coordinates, zoom: 9 });
                 }
             }
         } else {
             console.warn("No feature data found in provinceFeaturesMap for clicked name:", provinceName);
             showNoForecastMessage(provinceName); // Show generic message if province data is missing
         }
     }


    function handleSearchResult(event) {
        if (!map) { console.warn("Map not ready for search results."); return; }
        console.log("Handling geocoder 'result' event:", event);
        clearHighlightAndForecast(); // Clear previous marker, highlight, forecast

        const result = event.result;
        if (!result?.geometry) {
            console.warn("Geocoder result is missing geometry.");
            return;
        }
        console.log("Start handleSearchResult");

        
        // Lấy thông tin từ kết quả tìm kiếm
        const placeName = result.place_name || result.properties?.name || "Search Result";
        const bbox = result.bbox; // [minLon, minLat, maxLon, maxLat]
        
        // Lấy tọa độ trung tâm để đặt marker
        let centerCoordinates;
        if (result.center) {
            centerCoordinates = result.center;
        } else if (result.geometry.type === "Point") {
            centerCoordinates = result.geometry.coordinates;
        } else {
            // Nếu là polygon hoặc multipolygon, tính toán trung tâm từ bbox
            centerCoordinates = [
                (bbox[0] + bbox[2]) / 2, // trung bình của minLon và maxLon
                (bbox[1] + bbox[3]) / 2  // trung bình của minLat và maxLat
            ];
        }

        // 1. Add Marker at the result location
        try {
            // Use MapLibre Marker
            const popup = new maplibregl.Popup({ offset: 25, anchor: 'bottom' }).setHTML(`<b>${placeName}</b>`);
            currentSearchMarker = new maplibregl.Marker({ color: 'red' }) // Distinct color for search result
                .setLngLat(centerCoordinates)
                .setPopup(popup)
                .addTo(map)
                .togglePopup(); // Open popup immediately
        } catch (markerError) {
            console.error("Error adding search result marker:", markerError);
        }

        // 2. Zoom/Pan Map to the result
        // Luôn zoom khi user chọn kết quả (vì handleSearchResult chỉ được gọi khi user click dropdown)
        try {
            if (bbox && bbox.length === 4) {
                map.fitBounds(bbox, {
                    padding: 50, // Add padding around the bounds
                    maxZoom: 15 // Don't zoom in too far
                });
                console.log("Fitted map bounds to geocoder result (user select).");
            } else {
                map.flyTo({
                    center: centerCoordinates,
                    zoom: 13 // Default zoom if no bounds
                });
                console.log("Flew map to geocoder result center (user select).");
            }
        } catch (zoomError) {
            console.error("Error zooming/fitting map to result:", zoomError);
            // Fallback flyTo
            try { map.flyTo({ center: centerCoordinates, zoom: 13 }); } catch(e){}
        }
        
        // 3. Nếu kết quả có original_geojson trong properties, vẽ đường viền
        if (result.properties && result.properties.original_geojson) {
            try {
                // Xóa layer cũ nếu có
                if (map.getSource('search-result-boundary')) {
                    map.removeLayer('search-result-boundary-line');
                    if (map.getLayer('search-result-boundary-fill')) {
                        map.removeLayer('search-result-boundary-fill');
                    }
                    map.removeSource('search-result-boundary');
                }
                
                // Thêm source mới với geometry từ kết quả tìm kiếm
                map.addSource('search-result-boundary', {
                    type: 'geojson',
                    data: {
                        type: 'Feature',
                        geometry: result.properties.original_geojson,
                        properties: result.properties || {}
                    }
                });
                
                // Chỉ thêm layer line (đường viền), không tô nền
                map.addLayer({
                    id: 'search-result-boundary-line',
                    type: 'line',
                    source: 'search-result-boundary',
                    paint: {
                        'line-color': '#ff0000',
                        'line-width': 1.5,
                        'line-opacity': 0.8
                    }
                });
                
                console.log("Added boundary polygon from search result");
            } catch (error) {
                console.error("Error adding boundary polygon:", error);
            }
        }

        // 3. Find Province, Highlight, and Show Forecast
        let provinceNameGuess = '';
        
        // Ưu tiên lấy tên từ properties của kết quả tìm kiếm
        if (result.properties) {
            if (result.properties.name) {
                provinceNameGuess = result.properties.name;
            } else if (result.properties.place_name) {
                provinceNameGuess = result.properties.place_name.split(',')[0];
            }
        }
        
        // Nếu không có tên trong properties, thử lấy từ context
        if (!provinceNameGuess && result.context) {
            const provinceContext = result.context.find(ctx => ctx.id.startsWith('region') || ctx.id.startsWith('province') || ctx.id.startsWith('state'));
            if (provinceContext) {
                provinceNameGuess = provinceContext.text_vi || provinceContext.text;
            }
            // Fallback: look for city/county if region not found
            if (!provinceNameGuess) {
                const cityContext = result.context.find(ctx => ctx.id.startsWith('place') || ctx.id.startsWith('district'));
                if (cityContext) provinceNameGuess = cityContext.text_vi || cityContext.text;
            }
        }
        
        // Fallback: Use the main place name if context fails
        if (!provinceNameGuess && result.text) {
            provinceNameGuess = result.text_vi || result.text;
        }
        
        // Fallback: Use place_name if all else fails
        if (!provinceNameGuess && result.place_name) {
            provinceNameGuess = result.place_name.split(',')[0];
        }
        
        // Basic cleanup: Remove titles like "Tỉnh", "Thành phố"
        provinceNameGuess = provinceNameGuess ? provinceNameGuess.replace(/^(Thành phố|Tỉnh|Huyện|Quận|Thị xã)\s+/i, '').trim() : '';


        if (provinceNameGuess) {
            const normalizedName = normalizeLocationNameJS(provinceNameGuess);
            console.log(`Normalized Province/City Name: "${normalizedName}" (from: "${provinceNameGuess}")`); // Sửa lỗi thiếu dấu đóng ngoặc
            
            // DEBUG: In ra danh sách các khóa dự báo để kiểm tra
            console.log("Danh sách các khóa dự báo:", Object.keys(allForecastData).slice(0, 10));
            console.log("Kiểm tra xem '" + normalizedName + "' có trong dữ liệu dự báo không:", normalizedName in allForecastData);

            // Tìm kiếm dữ liệu dự báo với nhiều cách khác nhau
            let locationForecastData = null;
            let matchedName = "";
            
            // Cách 1: Tìm kiếm trực tiếp
            locationForecastData = allForecastData[normalizedName];
            if (locationForecastData) {
                matchedName = normalizedName;
                console.log(`Forecast found for: ${normalizedName}`);
            } 
            
            // Cách 2: Tìm kiếm với các phần của địa điểm (nếu có dấu phẩy)
            if (!locationForecastData && provinceNameGuess.includes(',')) {
                const parts = provinceNameGuess.split(',').map(part => part.trim());
                for (const part of parts) {
                    const normalizedPart = normalizeLocationNameJS(part);
                    if (normalizedPart in allForecastData) {
                        locationForecastData = allForecastData[normalizedPart];
                        matchedName = normalizedPart;
                        console.log(`Found forecast data using part: ${part} -> ${normalizedPart}`);
                        break;
                    }
                }
            }
            
            // Cách 3: Tìm kiếm dựa trên location_display và vietnamese_display_name
            if (!locationForecastData) {
                const allKeys = Object.keys(allForecastData);
                
                for (const key of allKeys) {
                    const entry = allForecastData[key];
                    
                    // Chuẩn hóa tên hiển thị để so sánh
                    if (entry.location_display) {
                        const normalizedDisplayName = normalizeLocationNameJS(entry.location_display);
                        if (normalizedDisplayName === normalizedName || 
                            normalizedDisplayName.includes(normalizedName) || 
                            normalizedName.includes(normalizedDisplayName)) {
                            locationForecastData = entry;
                            matchedName = key;
                            console.log(`Found forecast data using location_display match: ${entry.location_display}`);
                            break;
                        }
                    }
                    
                    // Thử với vietnamese_display_name
                    if (entry.vietnamese_display_name) {
                        const normalizedViName = normalizeLocationNameJS(entry.vietnamese_display_name);
                        if (normalizedViName === normalizedName || 
                            normalizedViName.includes(normalizedName) || 
                            normalizedName.includes(normalizedViName)) {
                            locationForecastData = entry;
                            matchedName = key;
                            console.log(`Found forecast data using vietnamese_display_name match: ${entry.vietnamese_display_name}`);
                            break;
                        }
                    }
                }
            }
            
            // Cách 4: Tìm kiếm mờ (fuzzy search) với các khóa
            if (!locationForecastData) {
                const allKeys = Object.keys(allForecastData);
                
                // Kiểm tra xem có key nào chứa normalizedName hoặc ngược lại
                for (const key of allKeys) {
                    if (key.includes(normalizedName) || normalizedName.includes(key)) {
                        locationForecastData = allForecastData[key];
                        matchedName = key;
                        console.log(`Found forecast data using fuzzy key match: ${key}`);
                        break;
                    }
                }
            }
            
            if (locationForecastData) {
                console.log(`Using forecast data for: ${locationForecastData.location_display || matchedName}`);
                const forecastHTML = createForecastTableHTML(locationForecastData);
                if (forecastTableContainer) {
                    forecastTableContainer.innerHTML = forecastHTML;
                    showForecastTable(); // Display the table
                } else { console.error("forecastContainer missing!"); }
            } else {
                console.log(`No forecast data found for: ${normalizedName}`);
                showNoForecastMessage(provinceNameGuess || normalizedName); // Show message
            }

            // Không cần highlight province riêng nếu đã vẽ đường viền từ GeoJSON
            // Chỉ highlight nếu kết quả không có original_geojson
            if (!(result.properties && result.properties.original_geojson)) {
                const provinceData = provinceFeaturesMap[normalizedName];
                if (provinceData?.feature?.properties?.name) {
                    highlightProvince(provinceData.feature.properties.name); // Use the name property from the feature
                } else {
                    console.log(`No matching GeoJSON feature found in provinceFeaturesMap for ${normalizedName} to highlight.`);
                }
            }

        } else {
            console.log("Could not determine Province/City name from geocoder result.");
             showNoForecastMessage("Địa điểm tìm kiếm"); // Generic message
        }

        console.log("Finished handleSearchResult.");
    }

     // Helper to display a generic "no forecast" message
     function showNoForecastMessage(locationName) {
         if (forecastTableContainer) {
             // Xử lý tên địa điểm để hiển thị đúng định dạng
             let displayName = locationName || 'địa điểm này';
             
             // Kiểm tra xem tên đã có tiền tố "Tỉnh", "Thành phố", "Huyện", v.v. chưa
             if (displayName && !displayName.match(/^(Tỉnh|Thành phố|Huyện|Quận|Thị xã)/i)) {
                 // Danh sách các thành phố trực thuộc trung ương
                 const centralCities = [
                     'hà nội', 'hồ chí minh', 'hải phòng', 'đà nẵng', 'cần thơ'
                 ];
                 
                 // Kiểm tra xem có phải là thành phố trực thuộc trung ương không
                 const normalizedName = normalizeLocationNameJS(displayName);
                 if (centralCities.some(city => normalizedName.includes(normalizeLocationNameJS(city)))) {
                     displayName = `Thành phố ${displayName}`;
                 } else {
                     displayName = `Tỉnh ${displayName}`;
                 }
             }
             
             forecastTableContainer.innerHTML = `<div class="forecast-content">
                 <h3 style="text-align: center; margin-top: 5px; margin-bottom: 10px;">Thông báo</h3>
                 <button title="Thu nhỏ" class="forecast-minimize-button" onclick="event.stopPropagation(); minimizeForecastTable(); return false;">−</button>
                 <p style="text-align: center; padding: 10px;">Không có dữ liệu dự báo chi tiết cho ${displayName}.</p>
             </div>`;
             showForecastTable();
         }
     }


    // --- CLEANUP FUNCTIONS ---

    function clearHighlightAndForecast() {
        // 1. Minimize forecast table if open
        if (forecastTableContainer && forecastTableContainer.style.display !== 'none') {
            minimizeForecastTable();
            // Optional: Clear its content?
            // forecastTableContainer.innerHTML = "";
        }

        // 2. Reset province highlight
        if (highlightedProvinceName && map.getLayer('province-boundaries')) {
            console.log("Clearing highlight for:", highlightedProvinceName);
            highlightedProvinceName = null; // Clear the tracked name FIRST

            // Trigger a repaint of the layer; the 'case' expression will now evaluate to the default style
            // We need to "nudge" the paint property slightly to force re-evaluation if the variable itself isn't reactive.
            // A common trick is to slightly change the expression or re-set it.
             try {
                  map.setPaintProperty('province-boundaries', 'line-color', [
                      'case',
                      ['==', ['get', 'name'], ''], // Match against empty string (or null) now
                      'red',
                      '#3388ff'
                  ]);
                   map.setPaintProperty('province-boundaries', 'line-width', [
                      'case',
                       ['==', ['get', 'name'], ''],
                      3,
                      1
                  ]);
                  // Force a re-render if needed (usually not necessary)
                  // map.triggerRepaint();
             } catch(error) {
                 console.error("Error resetting province highlight paint properties:", error);
             }
        }
         highlightedProvinceName = null; // Ensure it's cleared

        // 3. Remove the search marker
        if (currentSearchMarker) {
            currentSearchMarker.remove();
            currentSearchMarker = null;
            console.log("Removed previous search marker.");
        }
    }

     // Helper function to set the highlight
     function highlightProvince(provinceFeatureName) {
         if (highlightedProvinceName === provinceFeatureName) return; // Already highlighted

         console.log("Highlighting province:", provinceFeatureName);
         clearHighlightAndForecast(); // Clear previous state first

         highlightedProvinceName = provinceFeatureName; // Set the new name to track

         if (map.getLayer('province-boundaries')) {
              try {
                  // Update paint properties using the new highlightedProvinceName
                  map.setPaintProperty('province-boundaries', 'line-color', [
                      'case',
                      ['==', ['get', 'name'], highlightedProvinceName], // Use the variable
                      'red',
                      '#3388ff'
                  ]);
                   map.setPaintProperty('province-boundaries', 'line-width', [
                      'case',
                       ['==', ['get', 'name'], highlightedProvinceName],
                      3,
                      1
                  ]);
                  console.log(`Highlight style applied for: ${provinceFeatureName}`);
             } catch(error) {
                 console.error("Error setting province highlight paint properties:", error);
                  highlightedProvinceName = null; // Reset if error applying
             }
         } else {
             console.warn("Cannot highlight: 'province-boundaries' layer not found.");
             highlightedProvinceName = null; // Reset tracking
         }
     }

    // --- Layer Visibility Control ---
    // Sử dụng biến toàn cục window.layerVisibility được định nghĩa trong layer_controls.js
    
    // Hàm này được gọi khi cần hiển thị hình ảnh radar mới
    function showRadarImage(index) {
        if (!radarImages || radarImages.length === 0) return;
        
        // Cập nhật chỉ số hình ảnh hiện tại
        currentImageIndex = index;
        if (currentImageIndex < 0) currentImageIndex = 0;
        if (currentImageIndex >= radarImages.length) currentImageIndex = radarImages.length - 1;
        
        // Lấy hình ảnh hiện tại
        const currentImage = radarImages[currentImageIndex];
        console.log(`Hiển thị hình ảnh: ${currentImage.path} - ${currentImage.timestamp_vn}`);
        
        // Cập nhật hiển thị thời gian
        document.getElementById('current-time-display').textContent = currentImage.timestamp_vn;
        
        // Cập nhật vị trí thanh trượt
        document.getElementById('timeline-slider').value = currentImageIndex;
        
        // Cập nhật source ảnh radar
        updateRadarLayer(currentImage.path);
        
        // Gọi hàm cập nhật hiển thị các lớp
        applyLayerVisibility(getCurrentSelectTime());
    }
    
    // Hàm thiết lập các nút bật/tắt lớp dữ liệu
    function setupLayerToggles() {
        console.log('Thiết lập các nút bật/tắt lớp dữ liệu...');
        
        // Lấy các phần tử checkbox
        const radarToggle = document.getElementById('radar-toggle');
        const vrainToggle = document.getElementById('vrain-toggle');
        const lightningToggle = document.getElementById('lightning-toggle');
        
        if (!radarToggle || !vrainToggle || !lightningToggle) {
            console.error('Không tìm thấy các phần tử checkbox');
            return;
        }
        
        // Đọc trạng thái checked ban đầu của các checkbox
        layerVisibility.radar = radarToggle.checked;
        layerVisibility.vrain = vrainToggle.checked;
        layerVisibility.lightning = lightningToggle.checked;
        
        console.log('Trạng thái ban đầu của các lớp:', layerVisibility);
        //    
        // Gắn sự kiện change cho từng checkbox
        radarToggle.addEventListener('change', function() {
            layerVisibility.radar = this.checked;
            console.log('Radar toggle changed:', this.checked);
            applyLayerVisibility(getCurrentSelectTime());
        });
        
        vrainToggle.addEventListener('change', function() {
            layerVisibility.vrain = this.checked;
            console.log('VRAIN toggle changed:', this.checked);
            applyLayerVisibility(getCurrentSelectTime());
        });
        
        lightningToggle.addEventListener('change', function() {
            layerVisibility.lightning = this.checked;
            console.log('Lightning toggle changed:', this.checked);
            applyLayerVisibility(getCurrentSelectTime());
        });
        
        console.log('Các nút bật/tắt lớp dữ liệu đã được thiết lập');
    }
    
    // Hàm chính cập nhật visibility
    function applyLayerVisibility(timestamp) {
        console.log('Applying layer visibility...');
        // Kiểm tra điều kiện cần
        if (!map) {
            console.log('Không thể cập nhật hiển thị lớp: dữ liệu không hợp lệ');
            return;
        }
        // Lấy timestamp hiện tại
        const currentTimestampVN = timestamp;
        console.log('Current timestamp VN:', currentTimestampVN);
        // Xử lý Radar
        const radarLayerId = 'radar-timeline-layer';
        if (map.getLayer(radarLayerId)) {
            map.setLayoutProperty(radarLayerId, 'visibility', layerVisibility.radar ? 'visible' : 'none');
            console.log('Radar layer visibility:', layerVisibility.radar ? 'visible' : 'none');
        }
        // Tối ưu hóa VRAIN/Lightning
        // Ẩn hoặc hiện duy nhất 2 layer: vrain-layer và lightning-layer
        if (!layerVisibility.vrain && !layerVisibility.lightning) {
            if (map.getLayer('vrain-layer')) {
                try {
                    map.setLayoutProperty('vrain-layer', 'visibility', 'none');
                } catch (e) {
                    console.warn('Không thể set visibility cho vrain-layer:', e);
                }
            }
            if (map.getLayer('lightning-layer')) {
                try {
                    map.setLayoutProperty('lightning-layer', 'visibility', 'none');
                } catch (e) {
                    console.warn('Không thể set visibility cho lightning-layer:', e);
                }
            }
            return;
        }
        // Nếu ít nhất một VRAIN/Lightning được bật, chỉ set visibility cho 2 layer duy nhất
        if (map.getLayer('vrain-layer')) {
            try {
                map.setLayoutProperty('vrain-layer', 'visibility', layerVisibility.vrain ? 'visible' : 'none');
            } catch (e) {
                console.warn('Không thể set visibility cho vrain-layer:', e);
            }
        }
        if (map.getLayer('lightning-layer')) {
            try {
                map.setLayoutProperty('lightning-layer', 'visibility', layerVisibility.lightning ? 'visible' : 'none');
            } catch (e) {
                console.warn('Không thể set visibility cho lightning-layer:', e);
            }
        }
    }
    
    // --- STARTUP ---
    // Load configuration and initialize map (initial center/zoom might come from data.json)
    // Using placeholder config here, assuming data.json will provide specifics
    const initialConfig = {
        center: [16.5, 105.8], // Default Lat, Lon (Leaflet order initially)
        zoom: 5
        // vietnamBounds might be loaded later
    };
    initMap(initialConfig); // Start the initialization process

    // Thêm static markers sau khi map đã khởi tạo hoàn tất
    if (window.addEventListener) {
        window.addEventListener('load', function() {
            setTimeout(loadAndAddStaticMarkers, 1000);
        });
    } else {
        // fallback cho trình duyệt cũ
        setTimeout(loadAndAddStaticMarkers, 1500);
    }

}); // End DOMContentLoaded
// --- END OF FILE script.js (MapLibre GL JS Version) ---