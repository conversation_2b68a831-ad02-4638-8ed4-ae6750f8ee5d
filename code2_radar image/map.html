<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title><PERSON><PERSON>n đồ <PERSON>hời tiết Việt Nam (Nâng cao)</title> <!-- Sửa title nếu muốn -->

    <!-- MapLibre GL CSS -->
    <link href="https://unpkg.com/maplibre-gl@2.4.0/dist/maplibre-gl.css" rel="stylesheet" />
    <!-- MapLibre Geocoder CSS -->
    <link href="https://unpkg.com/@maplibre/maplibre-gl-geocoder@1.2.0/dist/maplibre-gl-geocoder.css" rel="stylesheet" />

    <!-- Custom CSS -->
    <link rel="stylesheet" href="style.css">

    <style>
        /* Chỉ giữ lại style cơ bản nhất */
        html, body { height: 100%; margin: 0; padding: 0; }
        #map { width: 100%; height: 100%; } /* Đảm bảo map chiếm toàn bộ không gian */
    </style>
    
    <!-- MapLibre GL JS -->
    <script src="https://unpkg.com/maplibre-gl@2.4.0/dist/maplibre-gl.js"></script>
    <!-- MapLibre Geocoder JS -->
    <script src="https://unpkg.com/@maplibre/maplibre-gl-geocoder@1.2.0/dist/maplibre-gl-geocoder.min.js"></script>
</head>
<body>



    <!-- Container cho bản đồ MapLibre -->
    <div id="map"></div>
    
    <!-- Bảng chú thích -->
    <div id="legend" class="map-overlay"></div>
    
    <!-- Điều khiển lớp dữ liệu -->
    <div id="layer-controls" class="map-overlay">
        <h4>Hiển thị dữ liệu</h4>
        <div class="layer-control-item">
            <input type="checkbox" id="radar-toggle" checked>
            <label for="radar-toggle">Radar</label>
        </div>
        <div class="layer-control-item">
            <input type="checkbox" id="vrain-toggle" checked>
            <label for="vrain-toggle">Lượng mưa</label>
        </div>
        <div class="layer-control-item">
            <input type="checkbox" id="lightning-toggle" checked>
            <label for="lightning-toggle">Sét</label>
        </div>
    </div>
    
    <!-- Thanh timeline -->
    
    <div id="timeline-container" class="map-overlay">
        <div id="timeline-header">
            <div id="timeline-controls">
                <button id="play-pause-btn" title="Play/Pause">&#9658;</button>
                <span id="current-time-display">--/--/---- --:--</span>
            </div>
        </div>
        <div id="timeline-slider-container">
            <input type="range" id="timeline-slider" min="0" max="100" value="100" step="1">
        </div>
        <div id="timeline-labels">
            <span class="timeline-start-label">--/--/---- --:--</span>
            <span class="timeline-end-label">--/--/---- --:--</span>
        </div>
    </div>
    
    <!-- Bảng dự báo thời tiết -->
    <div id="forecast-container" class="map-overlay forecast-overlay"></div>

    <!-- Custom JavaScript - Đảm bảo nó được tải sau khi MapLibre đã tải xong -->
    <script src="script.js"></script>

</body>
</html>