#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# ==============================================================================
#                            COMBINED SCRIPT
# ==============================================================================
# This script makes data.json and radar_timeline.json
# ==============================================================================

# --- Combined Imports ---
import glob
import json
import logging
import math
import natsort
import os
import pandas as pd
import random
import re
import requests
import shutil
import sys
import time
import unicodedata
from bs4 import BeautifulSoup
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
from datetime import datetime, timedelta
from datetime import datetime, timezone, timedelta
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry


# --- Configuration ---
MAX_WORKERS = 10; REQUEST_TIMEOUT = 20; RETRY_COUNT = 3; RETRY_BACKOFF = 0.5
MIN_DELAY = 0.5; MAX_DELAY = 1.5; DEFAULT_MISSING_VALUE = "Not available"
BASE_SAVE_DIR = "/Users/<USER>/Desktop/data/web_code/code2"
HTML_SAVE_DIR = os.path.join(BASE_SAVE_DIR, "hymetnet_63cities_source")

TRANSLATION_FILE = os.path.join(BASE_SAVE_DIR, "translations.txt")
URL_LIST_FILE = os.path.join(BASE_SAVE_DIR, "locations_urls.txt") # File chứa URL list
READ_FROM_LOCAL_FILES = False # Set to True if you want to read from local files

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(threadName)s - %(levelname)s - %(message)s')
class BlockingDetectedError(Exception): pass

USER_AGENTS = [ 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/111.0.0.0 Safari/537.36', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/111.0.0.0 Safari/537.36', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/111.0.0.0 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/111.0', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/111.0', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/111.0.1661.41', ]
HEADERS = { 'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8', 'Accept-Language': 'en-US,en;q=0.5', 'Connection': 'keep-alive', 'Upgrade-Insecure-Requests': '1', 'DNT': '1', 'Accept-Encoding': 'gzip, deflate, br', }

# --- Translation Mappings ---
ICON_CODE_TO_VI_DESC = { '0001': 'Quang mây, không mưa', '0002': 'Quang mây, ngày trời nắng', '0003': 'Quang mây, đêm không mưa', '0011': 'Quang mây, trời nắng nóng', '0012': 'Quang mây, ngày nắng nóng', '0021': 'Quang mây, trời nắng nóng gay gắt', '0022': 'Quang mây, ngày nắng nóng gay gắt', '0031': 'Quang mây, trời nắng nóng đặc biệt gay gắt', '0032': 'Quang mây, ngày nắng nóng đặc biệt gay gắt', '1001': 'Ít mây, không mưa', '1002': 'Ít mây, ngày trời nắng', '1003': 'Ít mây, đêm không mưa', '1011': 'Ít mây, trời nắng nóng', '1012': 'Ít mây, ngày nắng nóng', '1021': 'Ít mây, trời nắng nóng gay gắt', '1022': 'Ít mây, ngày nắng nóng gay gắt', '1031': 'Ít mây, trời nắng nóng đặc biệt gay gắt', '1032': 'Ít mây, ngày nắng nóng đặc biệt gay gắt', '1081': 'Ít mây, có sương mù', '1082': 'Ít mây, ngày có sương mù', '1083': 'Ít mây, đêm có sương mù', '2001': 'Có mây, không mưa', '2002': 'Có mây, ngày không mưa', '2003': 'Có mây, đêm không mưa', '2301': 'Có mây, có mưa rào', '2302': 'Có mây, ngày có mưa rào', '2303': 'Có mây, đêm có mưa rào', '2501': 'Có mây, có mưa rào và dông', '2502': 'Có mây, ngày có mưa rào và dông', '2503': 'Có mây, đêm có mưa rào và dông', '4001': 'Nhiều mây, không mưa', '4041': 'Nhiều mây, không mưa; trời rét', '4051': 'Nhiều mây, không mưa; trời rét đậm', '4061': 'Nhiều mây, không mưa; trời rét hại', '4341': 'Nhiều mây, có mưa; trời rét', '4351': 'Nhiều mây, có mưa; trời rét đậm', '4361': 'Nhiều mây, có mưa; trời rét hại', '4391': 'Nhiều mây, có mưa; trời rét hại kèm khả năng băng giá, mưa tuyết', '4091': 'Nhiều mây, không mưa; trời rét hại kèm khả năng sương muối', '4101': 'Nhiều mây, có mưa phùn', '4181': 'Nhiều mây, có mưa phùn và sương mù', '4201': 'Nhiều mây, có mưa nhỏ', '4301': 'Nhiều mây, có mưa, mưa rào', '4401': 'Nhiều mây, có mưa vừa', '4501': 'Nhiều mây, có mưa dông', '4601': 'Nhiều mây, có mưa to', '4701': 'Có mưa đá', '4571': 'Nhiều mây, có mưa dông; trong cơn dông có khả năng xảy ra tố, lốc, gió giật mạnh', '300': 'Mây thay đổi, trời nắng', '340': 'Ít mây, trời nắng', '350': 'Quang mây, trời nắng nóng', '370': 'Quang mây/Ít mây, trời nắng nóng gay gắt', '390': 'Quang mây/Ít mây, trời nắng nóng đặc biệt gay gắt', '200': 'Nhiều mây, không mưa; trời rét', '150': 'Nhiều mây, không mưa; trời rét đậm', '130': 'Nhiều mây, không mưa; trời rét hại', }
WIND_DIRECTION_CODE_TO_EN = { "N": "North", "NNE": "North-Northeast", "NE": "Northeast", "ENE": "East-Northeast", "E": "East", "ESE": "East-Southeast", "SE": "Southeast", "SSE": "South-Southeast", "S": "South", "SSW": "South-Southwest", "SW": "Southwest", "WSW": "West-Southwest", "W": "West", "WNW": "West-Northwest", "NW": "Northwest", "NNW": "North-Northwest", }

# --- CẤU HÌNH (Đã cập nhật) ---
SOURCE_IMAGE_DIR = '/Users/<USER>/Desktop/data/web_code/code2/data/radar_source/'
LIGHTNING_CSV_PATH = '/Users/<USER>/Desktop/data/web_code/code2/data/lightning.csv'
history_data_filepath = '/Users/<USER>/Desktop/data/web_code/code2/data/vrain_data.json'

# **ĐƯỜNG DẪN FILE JSON OUTPUT CUỐI CÙNG**
OUTPUT_JSON_PARENT_DIR = '/Users/<USER>/Desktop/data/web_code/code2/data'
OUTPUT_JSON_FILENAME = 'data.json'
output_json_filepath = os.path.join(OUTPUT_JSON_PARENT_DIR, OUTPUT_JSON_FILENAME)

# **ĐƯỜNG DẪN FILE GEOJSON NGUỒN VÀ TÊN FILE TƯƠNG ĐỐI ĐÍCH**
# SOURCE_GEOJSON_PATH_ABSOLUTE = "/Users/<USER>/Desktop/data/web_code/code2/vietnam_provinces_unioned_boundaries.geojson"
TARGET_GEOJSON_FILENAME_RELATIVE = "vietnam_provinces_unioned_boundaries.geojson"

# <<< THÊM ĐƯỜNG DẪN FILE DỰ BÁO >>>
FORECAST_DIR = '/Users/<USER>/Desktop/data/web_code/code2/data/'
CSV_SAVE_DIR = FORECAST_DIR
TODAY_FORECAST_CSV_PATH = os.path.join(FORECAST_DIR, 'todayforecast.csv')
TEN_DAYS_FORECAST_CSV_PATH = os.path.join(FORECAST_DIR, '10daysforecast.csv')
# <<< >>>

# (Cấu hình khác giữ nguyên hoặc điều chỉnh nếu cần)
MAX_HISTORY = 12 # Giới hạn số giờ lịch sử VRAIN/Lightning cần xử lý
IMAGE_BOUNDS = [[7.2, 97], [25.2, 115]] # Vẫn cần nếu bạn muốn tham chiếu trong JSON, dù không dùng cho GIF
vietnam_bounds = [[8.3, 102.1], [23.5, 109.5]] # Vẫn cần cho mapConfig
hoang_sa_coord = [16.5, 112.0]
truong_sa_coord = [9.166666666666666, 114.41666666666667]
ict = timezone(timedelta(hours=7))
level_config = { "Mưa nhỏ": {"height": 15, "dark": "#13759e", "light": "#87CEEB"}, "Mưa vừa": {"height": 25, "dark": "#006400", "light": "#90EE90"}, "Mưa to": {"height": 40, "dark": "#B22222", "light": "#FA8072"}, "Mưa rất to": {"height": 55, "dark": "#4B0082", "light": "#DA70D6"}, "Không xác định": {"height": 0,  "dark": "#696969", "light": "#D3D3D3"}}
default_config = level_config["Không xác định"]
column_width = 12 # Vẫn cần cho legendData
lightning_color_cg = "yellow"; lightning_color_ic = "white" # Vẫn cần cho legendData
LIGHTNING_SIZE_THRESHOLDS = { 20: 10, 50: 14, float('inf'): 18 } # Vẫn cần để tính size sét
default_lightning_size = 10
lightning_bolt_symbol = "lightningCG"; cloud_lightning_symbol = "lightningIC" # Vẫn cần cho legendData
radar_color_scale = [ ("#66D5FC", "< 10"), ("#026DF9", "10-15"), ("#0646F8", "15-20"), ("#A7FB84", "20-25"), ("#59FA23", "25-30"), ("#06E032", "30-35"), ("#FED703", "35-40"), ("#FFA600", "40-45"), ("#FD8113", "45-55"), ("#FF1C00", "55-60"), ("#CC0071", "60-65"), ("#9900CC", "65-70"), ("#000000", "> 70")] # Vẫn cần cho legendData


# =======================
def load_translations(filepath):
    # ... (giữ nguyên) ...
    translations = {};
    if not os.path.exists(filepath): 
        logging.warning(f"Translation file not found: {filepath}. Will create later.")
        return translations
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            for i, line in enumerate(f):
                line = line.strip();
                if line and not line.startswith('#'):
                    if '=' in line: 
                        parts = line.split('=', 1); 
                        key = parts[0].strip().lower(); 
                        value = parts[1].strip();
                    if key: 
                        translations[key] = value
                    else: 
                        logging.warning(f"Skipping invalid line {i+1} in {filepath}: No '=' found.")
    except Exception as e: 
        logging.error(f"Error loading {filepath}: {e}")
    logging.info(f"Loaded {len(translations)} translations from {filepath}"); 
    return translations

def append_new_terms(filepath, new_terms_set, existing_translations_map):
    # ... (giữ nguyên) ...
    genuinely_new_terms = set(); 
    existing_keys_lower = set(existing_translations_map.keys())
    for term in new_terms_set:
        if term.lower() not in existing_keys_lower: 
            genuinely_new_terms.add(term)
    if not genuinely_new_terms: 
        logging.info("No new terms to append.")
        return
    logging.info(f"Appending {len(genuinely_new_terms)} new term(s) to {filepath}...")
    try:
        # Check if file exists, if not create it with header
        if not os.path.exists(filepath):
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write("# Vietnamese Description=English Translation\n")
        
        # Append new terms to the file
        with open(filepath, 'a', encoding='utf-8') as f:
            f.write(f"\n# --- New terms added on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ---\n")
            for term in sorted(list(genuinely_new_terms)):
                f.write(f"{term.strip()}=\n")
        logging.info("Append successful. Update file with translations.")
    except Exception as e: 
        logging.error(f"Error appending new terms {filepath}: {e}")

def load_urls_from_file(filepath):
    """Loads location slugs and URLs from a 'slug: url' file."""
    url_data = []
    if not os.path.exists(filepath):
        logging.error(f"URL list file not found: {filepath}. Cannot proceed.")
        return url_data
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            for i, line in enumerate(f):
                line = line.strip()
                if line and not line.startswith('#'):
                    if ':' in line:
                        parts = line.split(':', 1); 
                        slug = parts[0].strip().lower(); 
                        url = parts[1].strip()
                        if slug and url.startswith(('http://', 'https://')): 
                            url_data.append((slug, url)) # Store as tuple
                        else: 
                            logging.warning(f"Skipping invalid line {i+1} in {filepath}: Invalid format or URL.")
                    else: 
                        logging.warning(f"Skipping invalid line {i+1} in {filepath}: No ':' found.")
    except Exception as e: 
        logging.error(f"Error loading URL list file {filepath}: {e}")
    logging.info(f"Loaded {len(url_data)} URLs from {filepath}"); 
    return url_data

def translate_description(desc_vi, translations_map, untranslated_set_ref):
    # ... (giữ nguyên) ...
    if not desc_vi: 
        return DEFAULT_MISSING_VALUE
    desc_vi_original_case = desc_vi.strip(); 
    desc_vi_clean = desc_vi_original_case.lower()
    if desc_vi_clean in translations_map: 
        english_translation = translations_map[desc_vi_clean]; 
        return english_translation if english_translation else desc_vi_original_case
    else: 
        logging.debug(f"New untranslated term: '{desc_vi_original_case}'")
        untranslated_set_ref.add(desc_vi_original_case); 
        return desc_vi_original_case

def get_wind_direction_name_from_url(icon_url):
    # ... (giữ nguyên) ...
    if not icon_url or not isinstance(icon_url, str) or '/' not in icon_url or icon_url == 'N/A': 
        return None
    try: 
        filename = os.path.basename(icon_url); 
        code = os.path.splitext(filename)[0].upper(); 
        return WIND_DIRECTION_CODE_TO_EN.get(code, None)
    except Exception: 
        return None

def parse_date_sort_key(date_str):
    # ... (giữ nguyên) ...
    try: 
        date_part = date_str.split(' ')[-1]; 
        return datetime.strptime(date_part, '%d/%m/%Y')
    except (ValueError, IndexError, TypeError): 
        return datetime.min

def requests_retry_session(session=None):
    # ... (giữ nguyên) ...
    session = session or requests.Session(); 
    retry = Retry( total=RETRY_COUNT, read=RETRY_COUNT, connect=RETRY_COUNT, backoff_factor=RETRY_BACKOFF, status_forcelist=(500, 502, 503, 504), allowed_methods=frozenset(['GET']) ); 
    adapter = HTTPAdapter(max_retries=retry); 
    session.mount('http://', adapter); 
    session.mount('https://', adapter); 
    return session

def fetch_html_robust(url, session):
    # ... (giữ nguyên) ...
    try:
        headers = HEADERS.copy(); 
        headers['User-Agent'] = random.choice(USER_AGENTS); 
        time.sleep(random.uniform(MIN_DELAY, MAX_DELAY))
        response = session.get(url, headers=headers, timeout=REQUEST_TIMEOUT)
        if response.status_code in [403, 429]: 
            raise BlockingDetectedError(f"Status {response.status_code}")
        response.raise_for_status(); 
        response.encoding = 'utf-8'
        if not response.text: 
            logging.warning(f"Empty content: {url}"); 
            return None, response.status_code
        return response.text, response.status_code
    except BlockingDetectedError: 
        raise
    except requests.exceptions.Timeout: 
        return None, "Timeout"
    except requests.exceptions.RequestException as e: 
        status = e.response.status_code if hasattr(e, 'response') and e.response is not None else "Request Error"; 
        logging.error(f"Request failed: {url} | Status: {status} | Error: {e}"); 
        return None, status
    except Exception as e: 
        logging.error(f"Unexpected fetch error: {url} | Error: {e}"); 
        return None, "Fetch Error"

# Hàm chuyển đổi toàn bộ key tiếng Việt trong dict weather_data sang tiếng Anh, giữ nguyên value
def translate_weather_data_keys_vi_to_en(weather_data, mapping=None):
    """
    Đổi tất cả key tiếng Việt trong dict weather_data sang tiếng Anh, value giữ nguyên.
    mapping: dict ánh xạ key tiếng Việt sang tiếng Anh (nếu không truyền sẽ dùng mặc định bên dưới)
    """
    if mapping is None:
        mapping = {
            'Nhiệt độ': 'Temperature',
            'Thời tiết': 'Condition',
            'Độ ẩm': 'Humidity',
            'Hướng gió': 'Wind Direction',
            'Tốc độ gió': 'Wind Speed',
            'Ngày': 'Date',
            'Dự báo ngày hôm nay': 'Today',
            'Dự báo đêm hôm nay': 'Tonight',
            'Thời tiết hiện tại': 'Current',
            'Khả năng mưa': 'Precip. Prob.',
            'Trạng thái thời tiết': 'Weather Condition',
            'Nhiệt độ cao nhất': 'Max Temp',
            'Nhiệt độ thấp nhất': 'Min Temp',
            'Địa điểm': 'Location',
            'lặng gió': 'Calm',
            # Bổ sung thêm nếu cần
        }
    if isinstance(weather_data, dict):
        new_dict = {}
        for k, v in weather_data.items():
            new_key = mapping.get(k, k)
            new_dict[new_key] = translate_weather_data_keys_vi_to_en(v, mapping)
        return new_dict
    elif isinstance(weather_data, list):
        return [translate_weather_data_keys_vi_to_en(item, mapping) for item in weather_data]
    else:
        return weather_data

def extract_weather_data_vi(html_content, location_name_input, translations_map, untranslated_terms_collector_set):
    """Extracts data. Uses location_name_input provided (derived from slug)."""
    # <<< Bỏ logic trích xuất tên từ H1, dùng location_name_input trực tiếp >>>
    if not html_content: 
        return None
    try:
        soup = BeautifulSoup(html_content, 'html.parser')
        weather_data = {'location': location_name_input, 'current': None, 'forecasts': [], 'ten_day': []}
        label_map = { "Nhiệt độ": "Temperature", "Thời tiết": "Condition", "Độ ẩm": "Humidity", "Hướng gió": "Wind" }
        title_map = { "Thời tiết hiện tại": "Current", "Dự báo ngày hôm nay": "Today", "Dự báo đêm hôm nay": "Tonight" }

        # --- Current/Today/Tonight ---
        # ... (Logic bên trong giữ nguyên, nó đã dùng location_name_input đúng cách) ...
        forecast_sections = soup.find_all('div', class_='text-weather-location fix-weather-location')
        for section in forecast_sections:
            section_details = {}; 
            title_tag = section.find('a'); 
            title_vi = title_tag.get_text(strip=True) if title_tag else "Unknown"; 
            forecast_type = title_map.get(title_vi, "Unknown")
            section_details['Forecast Type'] = forecast_type
            section_details.update({ k: DEFAULT_MISSING_VALUE for k in ['Condition', 'Temperature', 'Humidity', 'Wind Direction', 'Wind Speed']})
            condition_from_icon_translated = DEFAULT_MISSING_VALUE
            if forecast_type in ["Today", "Tonight"]:
                 try:
                    icon_container_div = section.find_parent(class_='uk-width-expand@s'); 
                    icon_div = icon_container_div.find_previous_sibling('div', class_='uk-width-auto@s') if icon_container_div else None
                    icon_img = icon_div.find('img') if icon_div else None
                    if icon_img and icon_img.get('src'):
                         icon_src = icon_img['src']; 
                         icon_filename = os.path.basename(icon_src); 
                         icon_code = os.path.splitext(icon_filename)[0]
                         detailed_vi_desc = ICON_CODE_TO_VI_DESC.get(icon_code)
                         if detailed_vi_desc: 
                             condition_from_icon_translated = translate_description(detailed_vi_desc, translations_map, untranslated_terms_collector_set)
                         else: 
                             logging.warning(f"[{location_name_input} - {forecast_type}] Icon code '{icon_code}' not in map.")
                 except Exception as e_icon: 
                     logging.error(f"[{location_name_input} - {forecast_type}] Error getting cond from icon: {e_icon}")
            list_items = section.find_all('li'); 
            condition_from_li_translated = DEFAULT_MISSING_VALUE
            for item in list_items:
                label_div = item.find('div', class_='uk-width-1-4'); 
                value_div = item.find('div', class_='uk-width-3-4')
                if label_div and value_div:
                    label_vi = label_div.get_text(strip=True); 
                    label_en = label_map.get(label_vi)
                    if label_en:
                        raw_value_text = value_div.get_text(strip=True); 
                        cleaned_value = raw_value_text.lstrip(':').strip() if raw_value_text else ""
                        if label_en == 'Wind':
                            # Xử lý hướng gió và tốc độ gió bằng tiếng Việt, giữ lại tiếng Anh làm chú thích
                            direction_vi, speed = DEFAULT_MISSING_VALUE, DEFAULT_MISSING_VALUE
                            # direction_en, speed = DEFAULT_MISSING_VALUE, DEFAULT_MISSING_VALUE  # (chú thích: gốc tiếng Anh)
                            if "lặng gió" in cleaned_value.lower(): 
                                direction_vi, speed = "Lặng gió", '0 m/s'
                                # direction_en = "Calm"  # (chú thích: gốc tiếng Anh)
                            else:
                                wind_img = value_div.find('img')
                                if wind_img:
                                    direction_icon_url = wind_img.get('src')
                                    # Nếu muốn mapping sang tiếng Việt, có thể tự định nghĩa hàm chuyển icon_code sang tiếng Việt
                                    # dir_name_from_code_vi = get_wind_direction_name_vi_from_url(direction_icon_url)
                                    dir_name_from_code_vi = cleaned_value  # Ưu tiên lấy text tiếng Việt từ HTML
                                    # direction_en = get_wind_direction_name_from_url(direction_icon_url)  # (chú thích: gốc tiếng Anh)
                                    direction_vi = dir_name_from_code_vi if dir_name_from_code_vi else DEFAULT_MISSING_VALUE
                                    speed_text_node = wind_img.next_sibling
                                    found_speed = DEFAULT_MISSING_VALUE
                                    if speed_text_node and isinstance(speed_text_node, str):
                                        speed_match = re.search(r'(\d+m/s)', speed_text_node)
                                        found_speed = speed_match.group(1) if speed_match else DEFAULT_MISSING_VALUE
                                    speed = found_speed
                            if direction_vi == "Lặng gió":
                                speed = '0 m/s'
                            section_details['Wind Direction'] = direction_vi  # Gán tiếng Việt
                            section_details['Wind Speed'] = speed
                            # section_details['Wind Direction (en)'] = direction_en  # (chú thích: nếu cần tiếng Anh)
                        elif label_en == 'Humidity': 
                            section_details[label_en] = cleaned_value + '%' if cleaned_value else DEFAULT_MISSING_VALUE
                        elif label_en == 'Condition': 
                            #condition_from_li_translated = translate_description(cleaned_value, translations_map, untranslated_terms_collector_set)
                            condition_from_li_translated = cleaned_value
                        else: 
                            section_details[label_en] = cleaned_value if cleaned_value else DEFAULT_MISSING_VALUE
            final_condition = DEFAULT_MISSING_VALUE
            if forecast_type in ["Today", "Tonight"]: 
                final_condition = condition_from_icon_translated if condition_from_icon_translated != DEFAULT_MISSING_VALUE else condition_from_li_translated
            elif forecast_type == "Current": 
                final_condition = condition_from_li_translated
            section_details['Condition'] = final_condition
            if forecast_type == "Current": 
                weather_data['current'] = section_details
            elif forecast_type in ["Today", "Tonight"]: 
                weather_data['forecasts'].append(section_details)

        # --- 10-Day Forecast ---
        # ... (Logic giữ nguyên, nó đã dùng translate_description đúng cách) ...
        ten_days_container = soup.find('div', class_='ten-days-weather')
        if ten_days_container:
            day_items = ten_days_container.find_all('div', class_='item-days-wt')
            for day_index, day_item in enumerate(day_items[:10]):
                day_data = {}; 
                date_wt_div = day_item.find('div', class_='date-wt'); 
                date_str = DEFAULT_MISSING_VALUE
                if date_wt_div: 
                    day_name_vi = date_wt_div.contents[0].strip() if date_wt_div.contents else ''; 
                    date_span = date_wt_div.find('span'); 
                    numeric_date = date_span.get_text(strip=True) if date_span else 'N/A'; 
                    day_map = {"Thứ hai": "Mon", "Thứ ba": "Tue", "Thứ tư": "Wed", "Thứ năm": "Thu", "Thứ sáu": "Fri", "Thứ bảy": "Sat", "Chủ nhật": "Sun"}; 
                    day_name_en = day_map.get(day_name_vi, day_name_vi); 
                    date_str = f"{day_name_en} {numeric_date}" if numeric_date != 'N/A' else DEFAULT_MISSING_VALUE
                day_data['Date'] = date_str; 
                desc_div_vi = day_item.find('div', class_='text-temp'); 
                desc_vi = desc_div_vi.get_text(strip=True) if desc_div_vi else None
                #day_data['Weather Condition'] = translate_description(desc_vi, translations_map, untranslated_terms_collector_set)
                day_data['Weather Condition'] = desc_vi
                high_temp_span = day_item.find('span', class_='large-temp'); 
                day_data['Max Temp'] = high_temp_span.get_text(strip=True) if high_temp_span else DEFAULT_MISSING_VALUE
                low_temp_span = None; 
                low_temp_img = day_item.find('img', src=lambda x: x and 'temperature_Lo.png' in x);
                if low_temp_img: 
                    low_temp_span = low_temp_img.find_next_sibling('span', class_='small-temp')
                day_data['Min Temp'] = low_temp_span.get_text(strip=True) if low_temp_span else DEFAULT_MISSING_VALUE
                precip_prob_span = None; 
                rain_prob_img = day_item.find('img', src=lambda x: x and 'probabilityofrain.png' in x);
                if rain_prob_img: 
                    precip_prob_span = rain_prob_img.find_next_sibling('span', class_='small-temp')
                prob_text = precip_prob_span.get_text(strip=True) if precip_prob_span else None; 
                prob_val = DEFAULT_MISSING_VALUE
                if prob_text: 
                    prob_match = re.search(r'(\d+)', prob_text); 
                    prob_val = f"{prob_match.group(1)}%" if prob_match else DEFAULT_MISSING_VALUE
                day_data['Precip. Prob.'] = prob_val; 
                # Xử lý hướng gió và tốc độ gió bằng tiếng Việt, giữ lại tiếng Anh làm chú thích nếu cần
                direction_vi, speed = DEFAULT_MISSING_VALUE, DEFAULT_MISSING_VALUE
                # direction_en, speed = DEFAULT_MISSING_VALUE, DEFAULT_MISSING_VALUE  # (chú thích: gốc tiếng Anh)
                wind_img_tag = day_item.find('img', style=lambda s: s and 'width:20px' in s)
                if wind_img_tag:
                    wind_direction_src = wind_img_tag.get('src')
                    # Ưu tiên lấy text tiếng Việt từ HTML cho hướng gió
                    dir_name_from_code_vi = desc_vi if desc_vi else DEFAULT_MISSING_VALUE
                    # direction_en = get_wind_direction_name_from_url(wind_direction_src)  # (chú thích: gốc tiếng Anh)
                    direction_vi = dir_name_from_code_vi
                    wind_speed_span = wind_img_tag.find_next_sibling('span', class_='small-temp')
                    found_speed = DEFAULT_MISSING_VALUE
                    if wind_speed_span:
                        extracted_speed = wind_speed_span.get_text(strip=True)
                        found_speed = extracted_speed if extracted_speed and extracted_speed != 'N/A' else DEFAULT_MISSING_VALUE
                    speed = found_speed
                else:
                    # Nếu không có icon, kiểm tra text "lặng gió" hoặc gán mặc định
                    if desc_vi and "lặng gió" in desc_vi.lower():
                        direction_vi, speed = "Lặng gió", '0 m/s'
                        # direction_en = "Calm"  # (chú thích: gốc tiếng Anh)
                    else:
                        direction_vi, speed = DEFAULT_MISSING_VALUE, DEFAULT_MISSING_VALUE
                if direction_vi == "Lặng gió":
                    speed = '0 m/s'
                day_data['Wind Direction'] = direction_vi  # Gán tiếng Việt
                day_data['Wind Speed'] = speed
                # day_data['Wind Direction (en)'] = direction_en  # (chú thích: nếu cần tiếng Anh)
                weather_data['ten_day'].append(day_data)
        #weather_data = translate_weather_data_keys_vi_to_en(weather_data)  # Chuyển đổi key sang tiếng Anh
        return weather_data
    except Exception as e: 
        logging.error(f"Error extracting data for {location_name_input}: {e}", exc_info=True); 
        return None





# ver englist extract weather data


def process_location(location_info, session, html_save_path, read_local=False, translations_map=None):
    """Fetches/Reads HTML, saves HTML, extracts data using name derived from slug."""
    location_slug, url = location_info # Unpack tuple
    # --- Generate location name ONLY from slug ---
    location_name = location_slug.replace('-', ' ').title()
    # --- End name generation ---

    html = None; 
    status = "Not Processed"; 
    html_filepath = os.path.join(html_save_path, f"{location_slug}.txt")
    # ... (Rest of the fetch/read local logic is the same) ...
    if read_local:
        if os.path.exists(html_filepath):
            try: 
                with open(html_filepath, 'r', encoding='utf-8') as f: 
                    html = f.read(); 
                status = "Read from local"; 
                logging.info(f"Read HTML locally for {location_name}")
            except Exception as e: 
                logging.error(f"Failed read local {html_filepath}: {e}"); 
                status = "Local Read Error"
        else: 
            logging.warning(f"Local file not found: {html_filepath}"); 
            status = "Local File Not Found"
    else:
        html, status = fetch_html_robust(url, session)
        if html:
            try: 
                os.makedirs(html_save_path, exist_ok=True)
            except OSError as e: 
                logging.error(f"Could not create HTML dir {html_save_path}: {e}")
            try: 
                with open(html_filepath, 'w', encoding='utf-8') as f: 
                    f.write(html)
            except Exception as e: 
                logging.error(f"Failed save HTML {html_filepath}: {e}")
    if not html: 
        return None, url, status, set()
    local_untranslated_terms = set()
    # Pass the generated location_name to extraction
    data = extract_weather_data_vi(html, location_name)
    if data: 
        logging.info(f"Successfully extracted: {data.get('location', location_name)}"); 
        return data, url, status, local_untranslated_terms
    else: 
        logging.warning(f"Failed extract data: {location_name} ({url})"); 
        return None, url, status, set()

def parse_timestamp(ts_str):
    formats_to_try = ["%Y-%m-%dT%H:%M:%S.%fZ", "%Y-%m-%dT%H:%M:%SZ", "%Y-%m-%d %H:%M:%S.%f", "%Y-%m-%d %H:%M:%S", "%d/%m/%Y - %Hh", "%d/%m/%Y %H:%M"] # Thêm định dạng không có giây
    for fmt in formats_to_try:
        try: dt = datetime.strptime(str(ts_str), fmt);
        except ValueError: continue
        if dt.tzinfo is None: dt = dt.replace(tzinfo=timezone.utc) # Giả định UTC nếu không có tz
        return dt.astimezone(timezone.utc)
    try: return datetime.fromisoformat(str(ts_str).replace('Z', '+00:00')).astimezone(timezone.utc)
    except ValueError:
        # Thử parse chỉ ngày tháng nếu có thể
        try:
             dt = datetime.strptime(str(ts_str).split()[0], '%d/%m/%Y') # Chỉ lấy phần ngày
             return dt.replace(tzinfo=timezone.utc) # Giả định đầu ngày UTC
        except ValueError:
             print(f"CẢNH BÁO: Không thể phân tích timestamp '{ts_str}'. Trả về epoch.");
             return datetime.fromtimestamp(0, tz=timezone.utc)

def sort_timestamps_flexible(timestamps):
    valid_datetimes = []
    for ts in timestamps:
        dt = parse_timestamp(ts)
        # So sánh với epoch để đảm bảo parse thành công
        if dt != datetime.fromtimestamp(0, tz=timezone.utc):
             valid_datetimes.append(dt)
        else:
             print(f"Loại bỏ timestamp không hợp lệ khỏi sắp xếp: {ts}")
    return sorted(valid_datetimes) # Chỉ sắp xếp các datetime hợp lệ

def load_historical_data(filepath):
    if os.path.exists(filepath):
        try:
            with open(filepath, 'r', encoding='utf-8') as f: return json.load(f)
        except (json.JSONDecodeError, IOError) as e: print(f"Lỗi đọc file lịch sử '{filepath}': {e}. Trả về dict trống."); return {}
    print(f"Cảnh báo: File lịch sử không tồn tại tại '{filepath}'. Trả về dict trống.")
    return {}

def get_lightning_size(giatri, thresholds):
    try: val = float(giatri)
    except (ValueError, TypeError): return default_lightning_size
    sorted_thresholds = sorted(thresholds.keys())
    for threshold in sorted_thresholds:
        if isinstance(threshold, (int, float)) and val < threshold: # Đảm bảo threshold là số
            return thresholds[threshold]
    # Nếu lớn hơn tất cả các ngưỡng số
    finite_thresholds = [k for k in sorted_thresholds if isinstance(k, (int, float)) and k != float('inf')]
    if finite_thresholds:
         largest_finite_threshold = max(finite_thresholds)
         return thresholds[largest_finite_threshold]
    elif float('inf') in thresholds: # Fallback cho inf nếu chỉ có nó
        return thresholds[float('inf')]
    else:
        return default_lightning_size

def load_vietnamese_display_names(txt_path):
    """
    Đọc file locations_urls.txt và trả về dict mapping từ tên không dấu chuẩn hóa sang tên tiếng Việt chuẩn hóa.
    """
    import unicodedata, re
    def normalize(s):
        # Bỏ dấu, lowercase, bỏ tiền tố, loại ký tự đặc biệt
        s = unicodedata.normalize('NFD', s)
        s = ''.join(c for c in s if unicodedata.category(c) != 'Mn')
        s = s.lower().strip()
        s = re.sub(r'^(tinh |thanh pho |tp )', '', s)
        s = re.sub(r'[^a-z0-9\s-]', '', s)
        s = re.sub(r'\s+', ' ', s).strip()
        return s
    mapping = {}
    with open(txt_path, encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line or line.startswith('#') or ':' not in line:
                continue
            display_name, _ = line.split(':', 1)
            display_name = display_name.strip()
            norm = normalize(display_name)
            mapping[norm] = display_name
    return mapping

def normalize_location_name(name):
    """Chuẩn hóa tên địa điểm: bỏ dấu, chuyển lowercase, bỏ khoảng trắng thừa, bỏ tiền tố/hậu tố"""
    if not isinstance(name, str):
        return ""
    # Bỏ dấu tiếng Việt
    s = unicodedata.normalize('NFD', name)
    s = ''.join(c for c in s if unicodedata.category(c) != 'Mn')
    # Chuyển lowercase và bỏ khoảng trắng đầu/cuối
    normalized = s.lower().strip()
    # Loại bỏ các tiền tố phổ biến
    prefixes_to_remove = ['tinh ', 'thanh pho ', 'tp ', 'huyen ', 'quan ', 'thi xa ']
    for prefix in prefixes_to_remove:
        if normalized.startswith(prefix):
            normalized = normalized[len(prefix):]
            break # Chỉ loại bỏ một tiền tố
    # Loại bỏ các hậu tố phổ biến (ít quan trọng hơn)
    suffixes_to_remove = [' province', ' city'] # Giảm bớt để tránh loại bỏ nhầm
    for suffix in suffixes_to_remove:
         if normalized.endswith(suffix):
             normalized = normalized[:-len(suffix)]
             break
    # Bỏ ký tự đặc biệt còn lại và khoảng trắng thừa giữa từ
    normalized = re.sub(r'[^a-z0-9\s-]', '', normalized) # Giữ lại dấu gạch nối
    normalized = re.sub(r'\s+', ' ', normalized).strip()
    return normalized

def process_forecast_data(today_csv_path, ten_days_csv_path):
    # ... (Giữ nguyên logic của hàm này như trước) ...
    print(f"   Bắt đầu xử lý dữ liệu dự báo...")
    forecast_data = {}
    # 1. Xử lý todayforecast.csv
    # --- Load mapping tên tiếng Việt chuẩn hóa ---
    VN_NAME_MAP = load_vietnamese_display_names(os.path.join(BASE_SAVE_DIR, "locations_urls.txt"))

    try:
        df_today = pd.read_csv(today_csv_path, encoding='utf-8-sig')
        df_today.columns = [col.strip().replace(' ', '_').replace('\ufeff', '') for col in df_today.columns]
        if 'Location' not in df_today.columns:
             potential_loc_cols = [col for col in df_today.columns if 'location' in col.lower()]
             if potential_loc_cols: df_today = df_today.rename(columns={potential_loc_cols[0]: 'Location'})
             else: raise ValueError("Không tìm thấy cột 'Location' trong todayforecast.csv")
        print(f"     Đọc todayforecast.csv: {len(df_today)} địa điểm.")
        for index, row in df_today.iterrows():
             location_raw = row.get('Location')
             if pd.isna(location_raw): continue
             location_norm = normalize_location_name(location_raw)
             if not location_norm: continue
             # Ưu tiên lấy tên tiếng Việt chuẩn hóa từ mapping
             vn_display_name = VN_NAME_MAP.get(location_norm, location_raw.strip())
             forecast_data[location_norm] = {
                 "location_display": location_raw.strip(),
                 "vietnamese_display_name": vn_display_name,
                 "today": { "condition": row.get('Condition_Today'), "temp": row.get('Temperature_Today'), "humidity": row.get('Humidity_Today'), "wind_dir": row.get('Wind_Direction_Today'), "wind_speed": row.get('Wind_Speed_Today') },
                 "tonight": { "condition": row.get('Condition_Tonight'), "temp": row.get('Temperature_Tonight'), "humidity": row.get('Humidity_Tonight'), "wind_dir": row.get('Wind_Direction_Tonight'), "wind_speed": row.get('Wind_Speed_Tonight') },
                 "daily": []
             }
    except FileNotFoundError: print(f"     LỖI: Không tìm thấy file {today_csv_path}")
    except Exception as e: print(f"     LỖI khi xử lý {today_csv_path}: {e}")
    # 2. Xử lý 10daysforecast.csv
    try:
        df_10days = pd.read_csv(ten_days_csv_path, header=[0, 1], encoding='utf-8-sig')
        cleaned_columns = []
        location_col_tuple = None
        date_cols = {} # Store date columns: {date_str: [(level0, level1), ...]}
        for col_level0, col_level1 in df_10days.columns:
             cleaned_level0 = str(col_level0).strip().replace('\ufeff', '')
             cleaned_level1 = str(col_level1).strip()
             if 'location' in cleaned_level0.lower():
                 location_col_tuple = ('Location', '')
                 cleaned_columns.append(location_col_tuple)
                 continue
             date_match = re.search(r'(\d{1,2}/\d{1,2}/\d{4})', cleaned_level0) # D/M/YYYY hoặc DD/MM/YYYY
             if date_match:
                 date_str = date_match.group(1)
                 # Chuẩn hóa sang DD/MM/YYYY
                 try:
                     d, m, y = map(int, date_str.split('/'))
                     formatted_date = f"{d:02d}/{m:02d}/{y}"
                     if formatted_date not in date_cols: date_cols[formatted_date] = []
                     col_tuple = (formatted_date, cleaned_level1)
                     date_cols[formatted_date].append(col_tuple)
                     cleaned_columns.append(col_tuple)
                 except ValueError:
                      print(f"     Cảnh báo: Bỏ qua cột ngày không hợp lệ: {cleaned_level0}")
                      cleaned_columns.append((cleaned_level0, cleaned_level1)) # Giữ nguyên nếu lỗi
             else: # Giữ nguyên các cột không phải Location hoặc ngày
                 cleaned_columns.append((cleaned_level0, cleaned_level1))
        if not location_col_tuple: raise ValueError("Không thể xác định cột Location trong 10daysforecast.csv")
        df_10days.columns = pd.MultiIndex.from_tuples(cleaned_columns)
        print(f"     Đọc 10daysforecast.csv: {len(df_10days)} địa điểm.")
        ordered_unique_dates = sorted(date_cols.keys(), key=lambda d: datetime.strptime(d, '%d/%m/%Y'))
        for index, row in df_10days.iterrows():
             location_raw = row.get(('Location', ''))
             if pd.isna(location_raw): continue
             location_norm = normalize_location_name(location_raw)
             if not location_norm: continue
             daily_forecasts = []
             for date_str in ordered_unique_dates:
                 day_data = {'date': date_str}
                 try:
                     # Tìm tên cột level 1 chính xác hơn
                     def get_col_val(sub_header_keyword):
                         for lvl0, lvl1 in date_cols[date_str]:
                             if sub_header_keyword in lvl1.lower():
                                 val = row.get((lvl0, lvl1))
                                 return None if pd.isna(val) else str(val)
                         return None

                     day_data['max_temp'] = get_col_val('max temp')
                     day_data['min_temp'] = get_col_val('min temp')
                     day_data['precip_prob'] = get_col_val('precip. prob.')
                     day_data['condition'] = get_col_val('condition')
                     day_data['wind_dir'] = get_col_val('wind direction')
                     day_data['wind_speed'] = get_col_val('wind speed')
                     daily_forecasts.append(day_data)
                 except KeyError as ke: continue
             if location_norm in forecast_data:
                 forecast_data[location_norm]['daily'] = daily_forecasts
             else:
                 forecast_data[location_norm] = { "location_display": location_raw.strip(), "vietnamese_display_name": location_raw.strip(), "today": {}, "tonight": {}, "daily": daily_forecasts }
    except FileNotFoundError: print(f"     LỖI: Không tìm thấy file {ten_days_csv_path}")
    except Exception as e: print(f"     LỖI khi xử lý {ten_days_csv_path}: {e}")
    print(f"   => Xử lý xong dữ liệu dự báo cho {len(forecast_data)} địa điểm.")
    return forecast_data

def check_forecast_files(today_path, ten_path):
    """
    Kiểm tra trạng thái 2 file dự báo todayforecast.csv và 10daysforecast.csv.
    Trả về tuple (status, today_is_today, ten_is_today):
      - status: 'none' (không tồn tại file nào)
      - status: 'today+10days' (cả 2 đều là hôm nay)
      - status: 'today' hoặc '10days' (chỉ 1 file là hôm nay nhưng cả 2 vẫn tồn tại, cần xóa file cũ)
    """
    def is_today(filepath):
        if not os.path.isfile(filepath):
            return False
        mtime = os.path.getmtime(filepath)
        file_dt = datetime.fromtimestamp(mtime)
        now = datetime.now()
        return file_dt.date() == now.date()
    
    # Kiểm tra trạng thái tồn tại của từng file
    today_exists = os.path.isfile(today_path)
    ten_exists = os.path.isfile(ten_path)
    today_is_today = is_today(today_path)
    ten_is_today = is_today(ten_path)
    
    # Xóa file không phải hôm nay (nếu có)
    if today_exists and not today_is_today:
        os.remove(today_path)
        today_exists = False
        today_is_today = False
    if ten_exists and not ten_is_today:
        os.remove(ten_path)
        ten_exists = False
        ten_is_today = False

    # Xác định trạng thái sau khi đã xóa file cũ
    if today_exists and ten_exists:
        status = 'today_10days'
    elif today_exists:
        status = 'today'
    elif ten_exists:
        status = '10days'
    else:
        status = 'none'
        today_is_today = False
        ten_is_today = False

    return status, today_is_today, ten_is_today

def check_url_ok(url):
    """
    Crawl HTML, trích xuất 2 pandas (DataFrame) cho dự báo ngày và 10 ngày,
    sau đó kiểm tra nội dung thực tế của từng DataFrame để xác định hợp lệ.
    """
    try:
        resp = requests.get(url, timeout=10)
        if resp.status_code != 200 or not resp.text.strip():
            return False
        soup = BeautifulSoup(resp.text, 'html.parser')
        # --- Dự báo ngày ---
        forecast_sections = soup.find_all('div', class_='text-weather-location fix-weather-location')
        daily_data = []
        for sec in forecast_sections:
            rows = []
            items = sec.select('ul.list-info-wt li')
            for item in items:
                label_div = item.select_one('div.uk-width-1-4')
                value_div = item.select_one('div.uk-width-3-4')
                label = label_div.get_text(strip=True) if label_div else ''
                value = value_div.get_text(strip=True) if value_div else ''
                rows.append({'label': label, 'value': value})
            daily_data.append(rows)
        # Chuyển sang DataFrame
        daily_df = pd.DataFrame([row for rows in daily_data for row in rows]) if daily_data else pd.DataFrame()
        # Bỏ 4 dòng đầu (dự báo hiện tại/ngày/đêm)
        daily_df = daily_df.iloc[4:].reset_index(drop=True)
        print("Daily DataFrame (sau khi loại 4 dòng đầu):", daily_df)
        # --- Dự báo 10 ngày ---
        ten_days_container = soup.find('div', class_='ten-days-forecast')
        ten_day_items = ten_days_container.find_all('div', class_='item-days-wt') if ten_days_container else []
        ten_day_data = []
        for item in ten_day_items:
            rows = []
            date_div = item.select_one('div.date-wt')
            date = date_div.get_text(strip=True) if date_div else ''
            for li in item.select('ul.list-info-wt li'):
                label_div = li.select_one('div.uk-width-1-4')
                value_div = li.select_one('div.uk-width-3-4')
                label = label_div.get_text(strip=True) if label_div else ''
                value = value_div.get_text(strip=True) if value_div else ''
                rows.append({'date': date, 'label': label, 'value': value})
            ten_day_data.extend(rows)
        ten_day_df = pd.DataFrame(ten_day_data) if ten_day_data else pd.DataFrame()
        print("Ten Day DataFrame:", ten_day_df)
        # --- Kiểm tra nội dung thực tế ---
        import re
        def is_real_value(val):
            # Chỉ hợp lệ nếu chứa ít nhất 1 chữ số
            return bool(re.search(r'\d', val))
        def is_valid_df(df):
            if df.empty:
                return False
            for val in df['value']:
                if is_real_value(val):
                    return True
            return False

        valid_today = is_valid_df(daily_df)
        valid_10days = is_valid_df(ten_day_df)
        if valid_today and valid_10days:
            return 'OK_today_10days'
        elif valid_today:
            return 'OK_today'
        elif valid_10days:
            return 'OK_10days'
        else:
            return 'False'

    except Exception as e:
        print(f"[ERROR] Lỗi kiểm tra URL: {e}")
    return False

def get_random_url_from_file(txt_path):
    urls = []
    with open(txt_path, encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            # Hỗ trợ cả dạng: slug, url hoặc slug: url hoặc chỉ url
            if ',' in line:
                parts = line.split(',', 1)
                url = parts[1].strip()
            elif ':' in line:
                parts = line.split(':', 1)
                url = parts[1].strip()
            else:
                url = line.strip()
            if url.startswith('http'):
                urls.append(url)
    return random.choice(urls) if urls else None

def scan_radar_images(directory):
    """Quét thư mục chứa ảnh radar và tạo danh sách với thông tin thời gian"""
    
    # Kiểm tra thư mục tồn tại
    if not os.path.exists(directory):
        print(f"Thư mục {directory} không tồn tại")
        return []
    
    # Mẫu regex để trích xuất thời gian từ tên file
    pattern = r'ph_mosaic_(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})-final\.png'
    
    radar_images = []
    
    # Quét tất cả các file trong thư mục
    for filename in sorted(os.listdir(directory)):
        if filename.endswith('.png'):
            match = re.match(pattern, filename)
            if match:
                year, month, day, hour, minute, second = match.groups()
                
                # Tạo đối tượng datetime (UTC)
                timestamp_utc = datetime(
                    int(year), int(month), int(day),
                    int(hour), int(minute), int(second)
                )
                
                # Chuyển đổi sang UTC+7 (giờ Việt Nam)
                timestamp_vn = timestamp_utc + timedelta(hours=7)
                
                # Định dạng thời gian hiển thị (UTC+7)
                display_time = timestamp_vn.strftime('%d/%m/%Y %H:%M')
                
                # Thêm vào danh sách
                radar_images.append({
                    'filename': filename,
                    'path': f'data/radar_source/{filename}',
                    'timestamp_utc': timestamp_utc.isoformat(),
                    'timestamp_vn': timestamp_vn.isoformat(),
                    'display_time': display_time
                })
    
    # Sắp xếp theo thời gian UTC
    radar_images.sort(key=lambda x: x['timestamp_utc'])
    
    return radar_images

def create_radar_json(directory):
    radar_images = scan_radar_images(directory)
    
    # Tính toán khoảng thời gian (12 giờ trước đến hiện tại) theo giờ Việt Nam
    now = datetime.now()
    twelve_hours_ago = now - timedelta(hours=12)
    
    # In ra thời gian hiện tại để kiểm tra
    print(f"Thời gian hiện tại: {now.strftime('%d/%m/%Y %H:%M')} (giờ máy tính)")
    
    # Lọc ảnh trong khoảng thời gian (dựa vào giờ Việt Nam)
    filtered_images = [
        img for img in radar_images 
        if datetime.fromisoformat(img['timestamp_vn']) >= twelve_hours_ago
    ]
    
    # Sắp xếp theo thời gian tăng dần
    filtered_images.sort(key=lambda x: x['timestamp_vn'])
    
    # Tạo đối tượng JSON
    result = {
        'start_time': twelve_hours_ago.isoformat(),
        'end_time': now.isoformat(),
        'images': filtered_images
    }
    
    # Ghi ra file JSON
    with open('data/radar_timeline.json', 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    print(f"Đã quét {len(radar_images)} ảnh radar, lọc được {len(filtered_images)} ảnh trong khoảng 12 giờ qua")
    
    # Hiển thị thời gian của ảnh đầu tiên và cuối cùng để kiểm tra
    if filtered_images:
        print(f"\u1ea2nh đầu tiên: {filtered_images[0]['display_time']}")
        print(f"\u1ea2nh cuối cùng: {filtered_images[-1]['display_time']}")
    print(f"Đã lưu thông tin vào file data/radar_timeline.json")

def run_hymetnet_scraper():
    '''Contains the main execution logic from /Users/<USER>/Desktop/data/web_code/code2/hymetnet_63cities_report.py'''
    print("\n--- Running Logic from hymetnet_63cities_report.py ---")
    # --- Start of /Users/<USER>/Desktop/data/web_code/code2/hymetnet_63cities_report.py main logic ---
    try: 
            os.makedirs(HTML_SAVE_DIR, exist_ok=True); 
            os.makedirs(CSV_SAVE_DIR, exist_ok=True)
    except OSError as e: 
            logging.error(f"Could not create save directories: {e}"); 
            sys.exit(1)

    current_translations = load_translations(TRANSLATION_FILE)
    # --- Load URLs from File ---
    locations_to_process = load_urls_from_file(URL_LIST_FILE) # List of (slug, url) tuples
    # --- End Load URLs ---

    if not locations_to_process: 
        logging.critical("No URLs loaded from file. Exiting."); 
        sys.exit(1)

    logging.info(f"Starting processing for {len(locations_to_process)} locations. Read from local: {READ_FROM_LOCAL_FILES}")
    all_current_data = []; 
    all_forecast_data = []; 
    all_ten_day_data = []
    failed_urls_dict = {}; 
    stop_processing_flag = False; 
    blocking_url = None
    master_untranslated_terms = set(); 
    session = requests_retry_session()

    # --- ThreadPool Execution ---
    with ThreadPoolExecutor(max_workers=MAX_WORKERS, thread_name_prefix='Worker') as executor:
        future_to_url = {}
        for loc_info in locations_to_process: # Iterate through (slug, url) tuples
                if stop_processing_flag: 
                    break
                # Pass tuple to worker
                future = executor.submit(process_location, loc_info, session, HTML_SAVE_DIR, READ_FROM_LOCAL_FILES, current_translations)
                future_to_url[future] = loc_info[1] # Map future to URL for error reporting
        logging.info(f"Submitted {len(future_to_url)} tasks.")

        processed_count = 0; 
        total_tasks = len(future_to_url)
        for future in as_completed(future_to_url): # Process results...
            url = future_to_url[future]; 
            processed_count += 1
            if processed_count % 5 == 0 or processed_count == total_tasks: 
                logging.info(f"Processed {processed_count}/{total_tasks}...")
            if stop_processing_flag: 
                continue
            try:
                result_data, _, status, untranslated_set = future.result()
                if result_data:
                    location = result_data.get('location', 'Unknown Location') # Get name derived from slug
                    if result_data.get('current'): 
                        current_record = result_data['current']; 
                        current_record['Location'] = location; 
                        all_current_data.append(current_record)
                    for fc_rec in result_data.get('forecasts', []): 
                        fc_rec['Location'] = location; 
                        all_forecast_data.append(fc_rec)
                    for td_rec in result_data.get('ten_day', []): 
                        td_rec['Location'] = location; 
                        all_ten_day_data.append(td_rec)
                    master_untranslated_terms.update(untranslated_set)
                else:
                    if url not in failed_urls_dict: 
                        failed_urls_dict[url] = status
            except BlockingDetectedError as exc: 
                logging.error(f"*** BLOCKING DETECTED *** from URL {url}. Halting. Error: {exc}"); 
                stop_processing_flag = True; 
                blocking_url = url; 
                failed_urls_dict[url] = f"Blocking Detected ({exc})"
            except Exception as exc: 
                logging.error(f'URL {url} exception during result processing: {exc}', exc_info=False);
            if url not in failed_urls_dict: 
                failed_urls_dict[url] = "Processing Error"

        session.close(); 
        logging.info("--- Session Closed ---")
        if master_untranslated_terms: 
            append_new_terms(TRANSLATION_FILE, master_untranslated_terms, load_translations(TRANSLATION_FILE))
        logging.info("--- Processing Attempt Finished ---")
        processed_locations = set(d['Location'] for d in all_ten_day_data if 'Location' in d); 
        total_processed_successfully = len(processed_locations); 
        logging.info(f"Successfully processed data for at least {total_processed_successfully} locations.")
        if stop_processing_flag: 
            logging.warning(f"\n*** Processing HALTED due to blocking at URL: {blocking_url} ***")
        if failed_urls_dict:
            logging.warning(f"\nFailed to fully process {len(failed_urls_dict)} URLs:")
            for failed_url, reason in failed_urls_dict.items(): 
                logging.warning(f"- {failed_url} (Reason: {reason})")

        # --- DataFrame Restructuring and Saving ---
        # ... (Logic này giữ nguyên, vì nó đã dùng tên Location được trả về từ process_location) ...
        combined_dfs = {}; 
        csv_files_saved = []
        if all_forecast_data: # Today/Tonight
            logging.info("Restructuring Today/Tonight forecast...")
            try:
                today_tonight_flat_df = pd.DataFrame(all_forecast_data)
                if not today_tonight_flat_df.empty:
                    today_tonight_pivoted = today_tonight_flat_df.set_index(['Location', 'Forecast Type']).unstack(); 
                    today_tonight_pivoted.columns = [f'{val}_{idx}' for val, idx in today_tonight_pivoted.columns]; 
                    today_tonight_pivoted.reset_index(inplace=True)
                    today_cols_order = ['Location'] + [f'{val}_Today' for val in ['Condition', 'Temperature', 'Humidity', 'Wind Direction', 'Wind Speed']] + [f'{val}_Tonight' for val in ['Condition', 'Temperature', 'Humidity', 'Wind Direction', 'Wind Speed']]
                    today_tonight_final_df = today_tonight_pivoted.reindex(columns=today_cols_order, fill_value=DEFAULT_MISSING_VALUE)
                    print("\n\n--- TODAY & TONIGHT FORECAST (Combined & Restructured) ---"); 
                    print(today_tonight_final_df.to_string(index=False))
                    combined_dfs['Today/Tonight'] = today_tonight_final_df; 
                    csv_path = os.path.join(CSV_SAVE_DIR, "todayforecast.csv")
                    try: 
                        today_tonight_final_df.to_csv(csv_path, index=False, encoding='utf-8-sig'); 
                        logging.info(f"Saved: {csv_path}"); 
                        csv_files_saved.append(csv_path)
                    except Exception as e: 
                        logging.error(f"Failed to save Today/Tonight CSV: {e}")
                else: 
                    logging.warning("Today/Tonight data list empty.")
            except Exception as e: 
                logging.error(f"Error restructuring Today/Tonight DF: {e}", exc_info=True)
        else: 
            print("\nNo Today/Tonight data collected.")
        if all_ten_day_data: # 10-Day
            logging.info("Restructuring 10-Day forecast...")
            try:
                ten_day_flat_df = pd.DataFrame(all_ten_day_data)
                if 'Date' not in ten_day_flat_df.columns or ten_day_flat_df.empty: 
                    logging.warning("Cannot restructure 10-Day: 'Date' column missing or DataFrame empty.")
                else:
                    metrics = ['Weather Condition', 'Max Temp', 'Min Temp', 'Precip. Prob.', 'Wind Direction', 'Wind Speed']; 
                    metrics_exist = [m for m in metrics if m in ten_day_flat_df.columns]
                    if not metrics_exist: 
                        logging.warning("Cannot restructure 10-Day: No metric columns found.")
                    else:
                        ten_day_pivoted = ten_day_flat_df.pivot_table(index='Location', columns='Date', values=metrics_exist, aggfunc='first')
                        if isinstance(ten_day_pivoted.columns, pd.MultiIndex):
                            date_columns = ten_day_pivoted.columns.get_level_values(1).unique(); 
                            valid_date_columns = [d for d in date_columns if d != DEFAULT_MISSING_VALUE]
                            sorted_date_columns = sorted(valid_date_columns, key=parse_date_sort_key)
                            if DEFAULT_MISSING_VALUE in date_columns: 
                                sorted_date_columns.append(DEFAULT_MISSING_VALUE)
                            new_cols_order = pd.MultiIndex.from_product([metrics_exist, sorted_date_columns], names=[None, 'Date']); 
                            existing_new_cols = [col for col in new_cols_order if col in ten_day_pivoted.columns]
                            ten_day_sorted_df = ten_day_pivoted.reindex(columns=existing_new_cols)
                            ten_day_final_df = ten_day_sorted_df.swaplevel(0, 1, axis=1); 
                            ten_day_final_df = ten_day_final_df.sort_index(axis=1, level=0, key=lambda idx: idx.map(parse_date_sort_key))
                        else: 
                            logging.warning("10-Day pivoted columns not MultiIndex."); 
                            ten_day_final_df = ten_day_pivoted
                        ten_day_final_df.reset_index(inplace=True)
                        print("\n\n--- 10-DAY FORECAST (Combined & Restructured by Date) ---");
                        with pd.option_context('display.max_rows', None, 'display.max_columns', None, 'display.width', 250): 
                            print(ten_day_final_df)
                        combined_dfs['10-Day'] = ten_day_final_df; 
                        csv_path = os.path.join(CSV_SAVE_DIR, "10daysforecast.csv")
                        try: 
                            ten_day_final_df.to_csv(csv_path, index=False, encoding='utf-8-sig'); 
                            logging.info(f"Saved: {csv_path}"); 
                            csv_files_saved.append(csv_path)
                        except Exception as e: 
                            logging.error(f"Failed to save 10-Day CSV: {e}")
            except Exception as e: 
                logging.error(f"Error restructuring 10-Day DF: {e}", exc_info=True)
        else: 
            print("\nNo 10-Day data collected.")

        # --- Missing Data Summary ---
        print("\n\n--- MISSING DATA SUMMARY ---"); 
        found_missing = False
        for name, df in combined_dfs.items():
             try:
                if df.empty: 
                    logging.info(f"Skipping missing summary (empty DF): {name}"); 
                    continue
                missing_counts = df.isin([DEFAULT_MISSING_VALUE]).sum(); 
                missing_summary = missing_counts[missing_counts > 0]
                if not missing_summary.empty:
                    found_missing = True; 
                    print(f"\nMissing values ('{DEFAULT_MISSING_VALUE}') in {name} DataFrame (Column: Count):")
                    if isinstance(missing_summary.index, pd.MultiIndex): 
                        print(missing_summary.to_string())
                    else: 
                        print(missing_summary.to_string())
             except Exception as e: 
                logging.error(f"Error missing data summary {name}: {e}")
        if not found_missing and combined_dfs: 
            print(f"No missing data ('{DEFAULT_MISSING_VALUE}') detected.")
        elif not combined_dfs: 
            print("No dataframes created.")
        logging.info(f"Script finished. CSV files saved: {csv_files_saved}")
    # --- End of /Users/<USER>/Desktop/data/web_code/code2/hymetnet_63cities_report.py main logic ---
    print("--- Finished Logic from hymetnet_63cities_report.py ---")

def run_generate_data():
    print("--- Bắt đầu quá trình tạo data.json (phiên bản không GIF) ---")
    # --- 1. KIỂM TRA THƯ MỤC & ĐỌC DỮ LIỆU CORE ---
    print("\n1. Kiểm tra thư mục và đọc dữ liệu bản đồ cốt lõi...")
    # ... (Logic kiểm tra thư mục nguồn ảnh và output JSON giữ nguyên) ...
    if not os.path.isdir(SOURCE_IMAGE_DIR): print(f"LỖI: Không tìm thấy thư mục ảnh nguồn '{SOURCE_IMAGE_DIR}'"); sys.exit()
    # os.makedirs(OUTPUT_PLOT_DIR, exist_ok=True) # <<< XÓA
    os.makedirs(OUTPUT_JSON_PARENT_DIR, exist_ok=True)
    print(f"   Thư mục nguồn ảnh Radar: {SOURCE_IMAGE_DIR}")
    print(f"   File JSON Output sẽ được lưu tại: {output_json_filepath}")

    # ... (Logic copy GeoJSON giữ nguyên) ...
    dest_geojson_path = os.path.join(OUTPUT_JSON_PARENT_DIR, os.path.basename(TARGET_GEOJSON_FILENAME_RELATIVE))
    province_geojson_relative_path_for_json = TARGET_GEOJSON_FILENAME_RELATIVE
    # try:
    #     if os.path.exists(SOURCE_GEOJSON_PATH_ABSOLUTE): shutil.copy2(SOURCE_GEOJSON_PATH_ABSOLUTE, dest_geojson_path); print(f"   Đã copy file GeoJSON tới: {dest_geojson_path}")
    #     else: print(f"   CẢNH BÁO: Không tìm thấy file GeoJSON nguồn tại '{SOURCE_GEOJSON_PATH_ABSOLUTE}'."); province_geojson_relative_path_for_json = None
    # except Exception as e: print(f"   Lỗi khi copy file GeoJSON: {e}"); province_geojson_relative_path_for_json = None

    # ... (Logic đọc thời gian, lọc VRAIN, đọc Sét giữ nguyên) ...
    now_utc_init = datetime.now(timezone.utc); now_ict_init = now_utc_init.astimezone(ict)
    time_limit_utc = now_utc_init - timedelta(hours=MAX_HISTORY)
    print(f"   Thời gian chạy script: {now_ict_init.strftime('%Y-%m-%d %H:%M:%S')} ICT")
    print(f"   Xử lý dữ liệu VRAIN/Sét từ {time_limit_utc.astimezone(ict).strftime('%Y-%m-%d %H:%M:%S')} ICT trở đi.")
    print(f"   Đang đọc dữ liệu VRAIN từ: {history_data_filepath}")
    historical_data_vrain_all = load_historical_data(history_data_filepath); historical_data_vrain = {}
    # Cải thiện lọc VRAIN để chỉ lấy các bản ghi trong MAX_HISTORY giờ qua
    valid_vrain_keys = []
    for ts_key, data in historical_data_vrain_all.items():
        dt = parse_timestamp(ts_key)
        if dt >= time_limit_utc and dt != datetime.fromtimestamp(0, tz=timezone.utc):
            historical_data_vrain[ts_key] = data
            valid_vrain_keys.append(ts_key)
    print(f"   => Đã tải và lọc {len(historical_data_vrain)} bản ghi VRAIN.")
    sorted_vrain_keys_filtered = sort_timestamps_flexible(valid_vrain_keys) # Sắp xếp các key hợp lệ

    print(f"   Đang đọc dữ liệu sét từ: {LIGHTNING_CSV_PATH}")
    lightning_df = None
    if os.path.exists(LIGHTNING_CSV_PATH):
        try:
            # ... (Logic đọc và xử lý lightning_df giữ nguyên) ...
            lightning_df = pd.read_csv(LIGHTNING_CSV_PATH, encoding='utf-8-sig')
            # Rename and process columns robustly
            col_map = { 'time': ['time', 'Datetime'], 'lat': ['lat', 'latitude'], 'lon': ['long', 'lng', 'longitude'], 'giatri': ['giatri', 'value'], 'loaiset': ['loaiset', 'type'] }
            rename_dict = {}
            missing_cols = []
            for target, sources in col_map.items():
                found = False
                for src in sources:
                    actual_col = next((c for c in lightning_df.columns if src == c.strip().replace('\ufeff', '')), None)
                    if actual_col:
                        rename_dict[actual_col] = target
                        found = True
                        break
                if not found and target not in ['giatri']: # giatri is optional
                    missing_cols.append(target)
            if missing_cols and 'loaiset' in missing_cols: # loaiset is mandatory
                print(f"   LỖI: Thiếu cột sét bắt buộc: {missing_cols}"); lightning_df = None
            else:
                lightning_df = lightning_df.rename(columns=rename_dict)
                lightning_df['datetime_utc'] = lightning_df['time'].apply(lambda x: parse_timestamp(str(x)))
                if 'giatri' not in lightning_df.columns: lightning_df['giatri'] = 10 # Default value if missing
                lightning_df['giatri'] = pd.to_numeric(lightning_df['giatri'], errors='coerce').fillna(10)
                lightning_df['lat'] = pd.to_numeric(lightning_df['lat'], errors='coerce')
                lightning_df['lon'] = pd.to_numeric(lightning_df['lon'], errors='coerce')
                lightning_df['loaiset'] = pd.to_numeric(lightning_df.get('loaiset', -1), errors='coerce').fillna(-1).astype(int) # Handle missing loaiset
                lightning_df.dropna(subset=['lat', 'lon', 'datetime_utc', 'loaiset'], inplace=True)
                lightning_df = lightning_df[lightning_df['datetime_utc'] >= time_limit_utc].copy()
                lightning_df = lightning_df.set_index('datetime_utc').sort_index()
                print(f"   => Đã đọc và xử lý {len(lightning_df)} điểm sét hợp lệ.")
        except Exception as e: print(f"   LỖI khi đọc file CSV sét: {e}"); lightning_df = None
    else: print(f"   Cảnh báo: Không tìm thấy file CSV sét.")

    # --- 2. XỬ LÝ DỮ LIỆU DỰ BÁO ---
    print("\n2. Xử lý dữ liệu dự báo thời tiết...")
    forecast_data_processed = process_forecast_data(TODAY_FORECAST_CSV_PATH, TEN_DAYS_FORECAST_CSV_PATH)

    # --- 3. CHUẨN BỊ CẤU TRÚC DỮ LIỆU JSON CUỐI CÙNG ---
    print("\n4. Chuẩn bị cấu trúc dữ liệu JSON cuối cùng...")
    now_utc = datetime.now(timezone.utc); now_ict = now_utc.astimezone(ict)

    map_data = {
        "mapConfig": { "center": [16.0, 108.0], "zoom": 6, "imageBounds": IMAGE_BOUNDS, "vietnamBounds": vietnam_bounds },
        "tileLayer": { "url": "https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png", "attribution": '© <a href="https://www.openstreetmap.org/copyright">OSM</a> contributors © <a href="https://carto.com/attributions">CARTO</a>'},
        "provinceGeoJsonPath": province_geojson_relative_path_for_json,
        "timeLayers": [], # <<< SẼ ĐƯỢC ĐIỀN BỞI VÒNG LẶP DƯỚI ĐÂY >>>
        "staticMarkers": [],
        "legendData": {
            "vrain": {level: config for level, config in level_config.items() if level != "Không xác định"},
            "lightning": {"cgColor": lightning_color_cg, "icColor": lightning_color_ic, "cgSymbol": lightning_bolt_symbol, "icSymbol": cloud_lightning_symbol},
            "radar": radar_color_scale
        },
        "vrainColumnWidth": column_width,
        "forecastData": forecast_data_processed # Dữ liệu dự báo từ Bước 2
    }

    # ... (Logic thêm marker tĩnh giữ nguyên) ...
    hoang_sa_popup_html = '<div style="background: linear-gradient(135deg, #d31145, #ff0000); ... </div>'
    truong_sa_popup_html = '<div style="background: linear-gradient(135deg, #d31145, #ff0000); ... </div>'
    map_data["staticMarkers"].append({"lat": hoang_sa_coord[0], "lon": hoang_sa_coord[1], "tooltipText": "Hoàng Sa", "popupHtml": hoang_sa_popup_html})
    map_data["staticMarkers"].append({"lat": truong_sa_coord[0], "lon": truong_sa_coord[1], "tooltipText": "Trường Sa", "popupHtml": truong_sa_popup_html})
    print("   Đã thêm cấu hình, chú thích, marker tĩnh, dữ liệu dự báo.")

    # Xác định các khoảng thời gian dựa trên dữ liệu VRAIN và Sét (không cần dựa vào GIF nữa)
    print("   Xác định khoảng thời gian hiển thị dựa trên VRAIN/Sét...")
    all_relevant_timestamps = set(sorted_vrain_keys_filtered) # Bắt đầu với VRAIN timestamps
    if lightning_df is not None:
        # Chuyển index của lightning_df (là datetime_utc) thành chuỗi ISO để thêm vào set
        lightning_timestamps_iso = [ts.isoformat() for ts in lightning_df.index]
        all_relevant_timestamps.update(lightning_timestamps_iso)

    # Chuyển đổi tất cả thành đối tượng datetime và sắp xếp
    sorted_all_dt_utc = sort_timestamps_flexible(list(all_relevant_timestamps))

    # Giới hạn dữ liệu trong 12h gần nhất
    TIME_LIMIT_HOURS = 12
    now_utc = datetime.now(timezone.utc)
    time_limit_utc_12h = now_utc - timedelta(hours=TIME_LIMIT_HOURS)

    # Nhóm thành các khoảng thời gian (ví dụ: 15 phút hoặc 1 giờ)
    # Cách tiếp cận: Tạo các slot 1 giờ trong MAX_HISTORY và gán VRAIN/Sét vào đó
    hourly_time_slots = []
    current_hour_start_utc = now_utc.replace(minute=0, second=0, microsecond=0)

    for i in range(MAX_HISTORY + 1): # +1 để bao gồm giờ hiện tại
        slot_end_utc = current_hour_start_utc - timedelta(hours=i)
        slot_start_utc = slot_end_utc - timedelta(hours=1)
        # Chỉ giữ lại slot nếu slot nằm trong 12h gần nhất
        if slot_end_utc < time_limit_utc_12h:
            break

        # Đảm bảo slot_start_utc không cũ hơn time_limit_utc_12h
        actual_slot_start_utc = max(slot_start_utc, time_limit_utc_12h)

        # Chỉ thêm slot nếu có dữ liệu VRAIN hoặc Sét trong khoảng đó
        has_data_in_slot = False
        # Kiểm tra VRAIN
        for vrain_key in historical_data_vrain.keys():
            vrain_dt = parse_timestamp(vrain_key)
            if actual_slot_start_utc <= vrain_dt < slot_end_utc:
                has_data_in_slot = True; break
        # Kiểm tra Sét nếu chưa tìm thấy dữ liệu
        if not has_data_in_slot and lightning_df is not None:
            if not lightning_df[(lightning_df.index >= actual_slot_start_utc) & (lightning_df.index < slot_end_utc)].empty:
                has_data_in_slot = True

        if has_data_in_slot:
            slot_start_ict = actual_slot_start_utc.astimezone(ict)
            slot_end_ict = slot_end_utc.astimezone(ict)
            # Tạo tên và timestamp đại diện (ví dụ: thời điểm bắt đầu slot)
            range_name = f"{slot_start_ict.strftime('%d/%m %H:%M')} - {slot_end_ict.strftime('%H:%M')} ICT"
            representative_timestamp_iso = actual_slot_start_utc.isoformat() # Dùng thời điểm bắt đầu làm timestamp chính

            hourly_time_slots.append({
                "name": range_name,
                "timestamp": representative_timestamp_iso, # <<< TIMESTAMP QUAN TRỌNG CHO FRONTEND
                "start_utc": actual_slot_start_utc.isoformat(),
                "end_utc": slot_end_utc.isoformat(),
                "is_current": (i == 0) # Giờ gần nhất là hiện tại
            })
        else:
            print(f"   Bỏ qua slot {slot_start_ict.strftime('%H:%M')} - {slot_end_ict.strftime('%H:%M')} vì không có VRAIN/Sét.")

    # Sắp xếp lại các slot theo thời gian tăng dần
    hourly_time_slots.sort(key=lambda x: x["timestamp"])

    print(f"   Đã xác định {len(hourly_time_slots)} khoảng thời gian có dữ liệu VRAIN/Sét.")

    # --- 5. TẠO DỮ LIỆU CHI TIẾT CHO TỪNG LỚP THỜI GIAN ---
    print("   Tạo dữ liệu chi tiết cho từng lớp thời gian...")
    processed_layers = 0
    def format_timestamp_compact(dt):
        return dt.strftime('%Y%m%d%H%M00')

    for i, time_slot in enumerate(hourly_time_slots):
        layer_name = f"Data: {time_slot['name']}"
        # Dùng timestamp đại diện đã xác định
        layer_timestamp_iso = time_slot["timestamp"]
        range_start_utc_dt = datetime.fromisoformat(time_slot["start_utc"])
        range_end_utc_dt = datetime.fromisoformat(time_slot["end_utc"])
        layer_timestamp_compact = format_timestamp_compact(range_start_utc_dt)

        layer_data = {
            "name": layer_name,
            "timestamp": layer_timestamp_compact, # CHUẨN HÓA ĐỊNH DẠNG
            "showDefault": (i == len(hourly_time_slots) - 1),
            "vrainPoints": [],
            "lightningStrikes": [],
        }

        # --- Thêm dữ liệu VRAIN ---
        # Tìm bản ghi VRAIN gần nhất với *cuối* khoảng thời gian này
        target_time_for_vrain = range_end_utc_dt
        relevant_vrain_keys_in_slot = [
            k for k in historical_data_vrain.keys()
            if range_start_utc_dt <= parse_timestamp(k) < range_end_utc_dt
        ]
        closest_vrain_data = []
        closest_vrain_key = None
        if relevant_vrain_keys_in_slot:
            # Tìm key có timestamp gần nhất với target_time_for_vrain
            best_key = min(relevant_vrain_keys_in_slot, key=lambda k: abs(target_time_for_vrain - parse_timestamp(k)))
            closest_vrain_key = best_key
            closest_vrain_data = historical_data_vrain.get(best_key, [])

        if closest_vrain_data:
            closest_vrain_timestamp_dt = parse_timestamp(closest_vrain_key)
            update_time_ict_str = closest_vrain_timestamp_dt.astimezone(ict).strftime('%d/%m/%Y %H:%M:%S')
            update_time_short_ict_str = closest_vrain_timestamp_dt.astimezone(ict).strftime('%H:%M %d/%m')
            for point in closest_vrain_data:
                level = point.get('level', "Không xác định"); config = level_config.get(level, default_config)
                popup_html = f"<b>Trạm:</b> {point.get('name', 'N/A')}<br><b>Địa chỉ:</b> {point.get('address', 'N/A')}, {point.get('city', 'N/A')}<hr style='margin: 5px 0;'><b>Tình trạng:</b> {level}<br><b>Lượng mưa:</b> {point.get('depth', 0.0):.2f} mm<br><b>Cập nhật VRAIN:</b> {update_time_ict_str}"
                tooltip_html = f"<b>Trạm:</b> {point.get('name', 'N/A')}<br><b>Mức:</b> {level} ({point.get('depth', 0.0):.1f}mm)<br><b>Cập nhật:</b> {update_time_short_ict_str}"
                layer_data["vrainPoints"].append({
                    "lat": point.get('latitude'), "lon": point.get('longitude'), "level": level,
                    "height": config["height"], "darkColor": config["dark"], "lightColor": config["light"],
                    "popupHtml": popup_html, "tooltipHtml": tooltip_html,
                    "timestamp": format_timestamp_compact(closest_vrain_timestamp_dt)
                })

        # --- Thêm dữ liệu Sét ---
        if lightning_df is not None and not lightning_df.empty:
            mask = (lightning_df.index >= range_start_utc_dt) & (lightning_df.index < range_end_utc_dt)
            lightning_in_range = lightning_df.loc[mask]
            if not lightning_in_range.empty:
                for strike_time_utc, strike in lightning_in_range.iterrows():
                    lat = strike['lat']; lon = strike['lon']; loaiset = strike.get('loaiset', -1); giatri = strike.get('giatri', 10)
                    if pd.isna(lat) or pd.isna(lon): continue
                    font_size = get_lightning_size(giatri, LIGHTNING_SIZE_THRESHOLDS); icon_char = ""; icon_color = "grey"; loaiset_text = "Không xác định"; strike_type = "Unknown"
                    if loaiset == 0: icon_char = lightning_bolt_symbol; icon_color = lightning_color_cg; loaiset_text = "Sét đất (CG)"; strike_type = "CG"
                    elif loaiset == 1: icon_char = cloud_lightning_symbol; icon_color = lightning_color_ic; loaiset_text = "Sét mây (IC/CC)"; strike_type = "IC"
                    strike_time_ict = strike_time_utc.astimezone(ict)
                    tooltip_lightning_html = f"<b>Thời gian:</b> {strike_time_ict.strftime('%H:%M:%S')}<br><b>Loại:</b> {loaiset_text}<br><b>Giá trị:</b> {giatri:.1f} kA"
                    layer_data["lightningStrikes"].append({
                        "lat": lat, "lon": lon, "type": strike_type, "value": giatri, "size": font_size,
                        "color": icon_color, "symbol": icon_char, "tooltipHtml": tooltip_lightning_html,
                        "timestamp": format_timestamp_compact(strike_time_utc)
                    })

        # Chỉ thêm layer nếu có dữ liệu VRAIN hoặc Sét
        if layer_data["vrainPoints"] or layer_data["lightningStrikes"]:
            map_data["timeLayers"].append(layer_data)
            processed_layers += 1
        # else:
            # print(f"     Bỏ qua slot {time_slot['name']} vì không có dữ liệu VRAIN/Sét cuối cùng được xử lý.")

    print(f"   => Đã tạo xong dữ liệu chi tiết cho {processed_layers} lớp thời gian có VRAIN/Sét.")

    # --- 6. LƯU FILE JSON ---
    print(f"\n6. Lưu dữ liệu vào file JSON: {output_json_filepath}")
    try:
        with open(output_json_filepath, 'w', encoding='utf-8') as f:
            # Sử dụng indent=None và separators để tiết kiệm dung lượng tối đa
            json.dump(map_data, f, ensure_ascii=False, indent=None, separators=(',', ':'))
        print("   Lưu file JSON thành công!")
        file_size_json_mb = os.path.getsize(output_json_filepath) / (1024 * 1024)
        print(f"   Kích thước file JSON: {file_size_json_mb:.2f} MB")
    except Exception as e:
        print(f"   LỖI khi lưu file JSON: {e}")

    # --- End of /Users/<USER>/Desktop/data/web_code/code2/generate_data.py main logic ---
    print("--- Finished Logic from generate_data.py ---")

# ---=================================================================---
# ---                     MAIN EXECUTION BLOCK                      ---
# ---=================================================================---

def main():
    '''Main function to run the combined workflow'''
    print("==============================================")
    print("          STARTING COMBINED WORKFLOW          ")
    print("==============================================")

    # Step 1: Kiểm tra file và crawl nếu cần
    try:
        status, today_is_today, ten_is_today = check_forecast_files(TODAY_FORECAST_CSV_PATH, TEN_DAYS_FORECAST_CSV_PATH)
        print(f"\nStatus of forecast files: {status}_{today_is_today}_{ten_is_today}")
        if status == 'today_10days' or status == 'today' or status == '10days':
            print("skip scraper")
        else:
            try:
                print("Checking URL")
                url = get_random_url_from_file(URL_LIST_FILE)
                print(f"URL random: {url}")
                ok = check_url_ok(url)
                print(f"Kết quả kiểm tra: Scraper link is {ok}")
                if ok == 'OK':
                    print("\nStep 1: Executing run_hymetnet_scraper...")
                    run_hymetnet_scraper()
                else:
                    print("Scraper link is not OK, skip scraper")
            except Exception as e:
                print("[ERROR] Lỗi khi kiểm tra/crawl URL:")
                import traceback
                traceback.print_exc()
    except Exception as e:
        print("[ERROR] Lỗi khi kiểm tra file dự báo:")
        import traceback
        traceback.print_exc()

    # Step 2: Generate data (luôn chạy dù bước trên có lỗi)
    try:
        print(f"\nStep 2: Executing run_generate_data...")
        run_generate_data()
        print("Step 2: Completed.")
    except Exception as e:
        print("[ERROR] Lỗi khi generate data:")
        import traceback
        traceback.print_exc()

    # Step 3: Create radar JSON
    try:
        create_radar_json(SOURCE_IMAGE_DIR)
    except Exception as e:
        print("[ERROR] Lỗi khi tạo JSON radar:")
        import traceback
        traceback.print_exc()

    print("\n==============================================")
    print("           COMBINED WORKFLOW FINISHED           ")
    print("==============================================")

if __name__ == '__main__':
    main()
