<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>🌤️ Weather App Vietnam - MapTiler SDK</title>
    
    <!-- MapTiler SDK -->
    <script src="https://cdn.maptiler.com/maptiler-sdk-js/v3.2.0/maptiler-sdk.umd.min.js"></script>
    <link href="https://cdn.maptiler.com/maptiler-sdk-js/v3.2.0/maptiler-sdk.css" rel="stylesheet" />
    <!-- MapTiler Weather -->
    <script src="https://cdn.maptiler.com/maptiler-weather/v3.0.1/maptiler-weather.umd.min.js"></script>
    <!-- FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    
    <!-- App Styles -->
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h2>🌤️ Weather App</h2>
            <p>Đang khởi tạo bản đồ thời tiết</p>
        </div>
    </div>
    
    <!-- Status Messages -->
    <div class="status-message" id="statusMessage"></div>
    
    <div class="app-container">
        <!-- Layer Toggle Buttons - Left Side -->
        <div class="layer-controls" id="layerButtons">
            <button class="layer-btn" id="temperature" data-layer="temperature">
                <i class="fas fa-thermometer-half icon"></i>
                <span class="text">Temperature</span>
            </button>
            <button class="layer-btn" id="precipitation" data-layer="precipitation">
                <i class="fas fa-cloud-rain icon"></i>
                <span class="text">Precipitation</span>
            </button>
            <button class="layer-btn active" id="wind" data-layer="wind">
                <i class="fas fa-wind icon"></i>
                <span class="text">Wind</span>
            </button>
            <button class="layer-btn" id="pressure" data-layer="pressure">
                <i class="fas fa-tachometer-alt icon"></i>
                <span class="text">Pressure</span>
            </button>
            <button class="layer-btn" id="radar" data-layer="radar">
                <i class="fas fa-satellite-dish icon"></i>
                <span class="text">Radar</span>
            </button>
            <button class="layer-btn" id="isobar" data-layer="isobar">
                <i class="fas fa-map icon"></i>
                <span class="text">Isobar</span>
            </button>
            <button class="layer-btn" id="arrow" data-layer="arrow">
                <i class="fas fa-location-arrow icon"></i>
                <span class="text">Arrow</span>
            </button>
        </div>

        <!-- Map Container -->
        <div class="map-container">
            <div id="map"></div>
            
            <!-- Current Layer Name Display -->
            <div class="current-layer" id="currentLayer">Wind</div>
            
            <!-- Weather Data Tooltip -->
            <div class="hover-tooltip" id="hoverTooltip"></div>
            
            <!-- Opacity Control - Compact Vertical -->
            <div class="opacity-control" id="opacityControl">
                <i class="fas fa-adjust opacity-icon"></i>
                <input type="range" class="opacity-slider" id="opacitySlider" min="0" max="100" value="75" orient="vertical">
            </div>
            
            <!-- Unit Toggle Control - Separate Component -->
            <div class="unit-control" id="unitControl">
                <button class="units-toggle" id="unitsToggle">m/s</button>
            </div>
            
            <!-- Color Scale Control - Separate Component -->
            <div class="scale-control" id="scaleControl">
                <div class="scale-bar" id="scaleBar">
                    <!-- Thang màu MapTiler sẽ được tạo động ở đây -->
                </div>
            </div>
            
            <!-- Right Side Legend Container - Now only for scale bar -->
            <div class="legend-container" id="legendContainer">
                <div class="legend" id="legendScale">
                    <!-- Chỉ chứa scale bar, unit toggle đã được tách ra -->
                </div>
            </div>
        </div>
        
        <!-- Timeline Container -->
        <div class="timeline-container" id="timelineContainer">
            <!-- Play/Pause Button -->
            <button class="timeline-play-btn" id="playButton">
                <i class="fas fa-play"></i>
            </button>
            
            <!-- Timeline Track -->
            <div class="timeline-track">
                <!-- Progress Line -->
                <div class="timeline-progress-line"></div>
                
                <!-- Day Markers -->
                <div class="timeline-markers">
                    <div class="timeline-marker" data-day="-1">
                        <div class="marker-dot"></div>
                        <div class="marker-label">Ngày 21</div>
                    </div>
                    <div class="timeline-marker" data-day="0">
                        <div class="marker-dot"></div>
                        <div class="marker-label">Ngày 22</div>
                    </div>
                    <div class="timeline-marker" data-day="1">
                        <div class="marker-dot"></div>
                        <div class="marker-label">Ngày 23</div>
                    </div>
                    <div class="timeline-marker" data-day="2">
                        <div class="marker-dot"></div>
                        <div class="marker-label">Ngày 24</div>
                    </div>
                </div>
                
                <!-- Timeline Slider -->
                <input type="range" class="timeline-slider" id="timelineSlider" min="0" max="3" value="1" step="0.01">
                
                <!-- Time Tooltip -->
                <div class="time-tooltip" id="timeTooltip">
                    <div class="tooltip-content">Sun, 22 Jun, 07:15</div>
                    <div class="tooltip-arrow"></div>
                </div>
            </div>
            
            <!-- Time Step Control (Custom Dropdown) -->
            <div class="time-step-control">
                <div class="custom-select-container" id="customTimeStepSelect">
                    <button class="custom-select-trigger" id="timeStepTrigger">
                        <span>Giờ</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="custom-select-options">
                        <div class="custom-select-option" data-value="minute">Phút</div>
                        <div class="custom-select-option" data-value="hour">Giờ</div>
                        <div class="custom-select-option" data-value="day">Ngày</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- App Scripts - Load in correct order -->
    <script src="config.js"></script>
    <script src="weather-layers.js"></script>
    <script src="legend.js"></script>
    <script src="timeline.js"></script>
    <script src="map-manager.js"></script>
    <script src="ui-controls.js"></script>
    <script src="app.js"></script>
</body>
</html> 