// Legend Management and Unit Conversion
class LegendManager {
    constructor(weatherLayers) {
        this.weatherLayers = weatherLayers;
        this.unitConverter = new UnitConverter();
    }

    // Update legend scale based on active layer
    updateLegendScale(layerType) {
        try {
            const scaleBar = document.getElementById('scaleBar');
            const unitsToggle = document.getElementById('unitsToggle');
            
            if (!scaleBar || !unitsToggle) return;
            
            // Map aliases 
            let scaleType = layerType;
            if (layerType === 'isobar') scaleType = 'pressure';
            if (layerType === 'arrow') scaleType = 'wind';
            
            // Get the actual weather layer object
            const actualLayer = this.weatherLayers.getLayer(scaleType);
            
            console.log('🔍 Debug actualLayer:', scaleType, {
                actualLayer: !!actualLayer,
                hasGetColorRamp: !!(actualLayer && actualLayer.getColorRamp),
                layerConstructor: actualLayer?.constructor?.name
            });
            
            if (actualLayer && actualLayer.getColorRamp) {
                try {
                    // Get the ACTUAL colorramp from layer
                    const actualColorramp = actualLayer.getColorRamp();
                    console.log('🎨 Got actualColorramp:', !!actualColorramp);
                    
                    if (actualColorramp && actualColorramp.getCanvasStrip) {
                        // Create color gradient canvas from actual colorramp 
                        const canvas = actualColorramp.getCanvasStrip({
                            horizontal: false,
                            size: 8
                        });
                        console.log('📊 Created canvas:', canvas.width, 'x', canvas.height);
                        
                        canvas.style.width = 'calc(var(--control-width) - 4px)';
                        canvas.style.height = 'var(--scale-height)';
                        canvas.style.borderRadius = '4px';
                        canvas.style.border = 'none';
                        canvas.style.outline = '1px solid rgba(255,255,255,0.5)';
                        canvas.style.transform = (scaleType === 'radar') ? 'rotate(180deg)' : 'rotate(180deg)';
                        canvas.style.backgroundColor = 'white';
                        
                        // Get values exactly like MapTiler
                        const values = this.getMapTilerLegendValues(scaleType);
                        
                        // Update legend with actual colorramp
                        this.updateLegendWithActualColorramp(scaleType, canvas, values);
                        
                        // Show the controls
                        const scaleControl = document.getElementById('scaleControl');
                        const unitControl = document.getElementById('unitControl');
                        if (scaleControl) scaleControl.style.display = 'flex';
                        if (unitControl) unitControl.style.display = 'flex';
                        return;
                    }
                } catch (error) {
                    console.error('❌ Error getting colorramp:', error);
                }
            }
            
            // Fallback: Create colorramp manually
            console.warn('🔄 Creating manual colorramp for', scaleType);
            this.createFallbackColorramp(scaleType);
            
        } catch (error) {
            console.error('UpdateLegendScale error:', error);
        }
    }

    // Create fallback colorramp if layer method failed
    createFallbackColorramp(scaleType) {
        try {
            let fallbackColorramp;
            
            switch (scaleType) {
                case 'wind':
                    fallbackColorramp = maptilerweather.ColorRamp.builtin.VIRIDIS.scale(0, 40);
                    break;
                case 'temperature':
                    fallbackColorramp = maptilerweather.ColorRamp.builtin.TEMPERATURE_2.scale(-50, 50);
                    break;
                case 'precipitation':
                    fallbackColorramp = maptilerweather.ColorRamp.builtin.PRECIPITATION;
                    break;
                case 'pressure':
                    fallbackColorramp = maptilerweather.ColorRamp.builtin.PRESSURE_2;
                    break;
                case 'radar':
                    fallbackColorramp = maptilerweather.ColorRamp.builtin.RADAR;
                    break;
            }
            
            if (fallbackColorramp && fallbackColorramp.getCanvasStrip) {
                const canvas = fallbackColorramp.getCanvasStrip({
                    horizontal: false,
                    size: 8
                });
                
                canvas.style.width = 'calc(var(--control-width) - 4px)';
                canvas.style.height = 'var(--scale-height)';
                canvas.style.borderRadius = '4px';
                canvas.style.border = 'none';
                canvas.style.outline = '1px solid rgba(255,255,255,0.5)';
                canvas.style.transform = (scaleType === 'radar') ? 'rotate(180deg)' : 'rotate(180deg)';
                canvas.style.backgroundColor = 'white';
                
                const values = this.getMapTilerLegendValues(scaleType);
                this.updateLegendWithActualColorramp(scaleType, canvas, values);
                
                // Show the controls
                const scaleControl = document.getElementById('scaleControl');
                const unitControl = document.getElementById('unitControl');
                if (scaleControl) scaleControl.style.display = 'flex';
                if (unitControl) unitControl.style.display = 'flex';
            }
        } catch (fallbackError) {
            console.error('❌ Fallback colorramp failed:', fallbackError);
        }
    }

    // Get MapTiler values exactly like their function
    getMapTilerLegendValues(scaleType) {
        switch(scaleType) {
            case 'temperature':
                return [40, 20, 10, 0, -10, -20, -40];
            case 'precipitation':
            case 'wind': 
                return [40, 30, 20, 15, 10, 5, 0];
            case 'pressure':
                return [1050, 1030, 1010, 990, 970, 950, 930];
            case 'radar':
                return [60, 50, 40, 25, 15, 10, 0];
            default:
                return [40, 30, 20, 15, 10, 5, 0];
        }
    }

    // Update legend with actual colorramp from layer
    updateLegendWithActualColorramp(layerType, canvas, values) {
        const scaleBar = document.getElementById('scaleBar');
        const unitsToggle = document.getElementById('unitsToggle');
        
        // Clear old scale
        if (scaleBar) {
            scaleBar.innerHTML = '';
        }
        
        // Create new container for colorramp
        const scaleContainer = document.createElement('div');
        scaleContainer.style.position = 'relative';
        scaleContainer.style.display = 'flex';
        scaleContainer.style.justifyContent = 'center';
        scaleContainer.id = layerType + 'Scale';
        
        // Add canvas to container
        scaleContainer.appendChild(canvas);
        
        // Add scale container to scaleBar instead of legendScale
        if (scaleBar) {
            scaleBar.appendChild(scaleContainer);
        }
        
        // Create labels container with position absolute
        const labelsContainer = document.createElement('div');
        labelsContainer.style.position = 'absolute';
        labelsContainer.style.left = '0';
        labelsContainer.style.top = '0';
        labelsContainer.style.width = 'calc(var(--control-width) - 4px)';
        labelsContainer.style.height = 'var(--scale-height)';
        labelsContainer.style.color = 'white';
        labelsContainer.style.fontSize = '10px';
        labelsContainer.style.fontWeight = '600';
        labelsContainer.style.textShadow = '0px 0px 3px rgba(0,0,0,0.8), 1px 1px 0px rgba(0,0,0,0.5)';
        labelsContainer.style.zIndex = '900';
        labelsContainer.style.pointerEvents = 'none';
        labelsContainer.id = layerType + 'Labels';
        
        // Update labels with converted values
        const config = WeatherLayers.LEGEND_CONFIGS[layerType];
        const currentUnitKey = this.weatherLayers.currentUnits[layerType] || (config && config.defaultUnit) || 'default';
        const convertedValues = values.map(val => {
            // Special handling for radar intensity unit
            if (layerType === 'radar' && currentUnitKey === 'rainIntensity') {
                return this.unitConverter.convertRadarToIntensity(val);
            }
            const converted = this.convertValue(val, layerType, currentUnitKey);
            return Math.round(converted * 10) / 10;
        });
        
        // Create spans with position absolute
        convertedValues.forEach((value, index) => {
            const span = document.createElement('span');
            span.textContent = value;
            span.style.position = 'absolute';
            span.style.left = '50%';
            span.style.transform = 'translateX(-50%)';
            span.style.lineHeight = '1';
            span.style.textAlign = 'center';
            
            // Calculate position like MapTiler - text inset from edges  
            const availableHeight = parseInt(getComputedStyle(document.documentElement).getPropertyValue('--scale-height'));
            const textPadding = 15;
            const usableHeight = availableHeight - (textPadding * 2);
            const spacing = convertedValues.length > 1 ? usableHeight / (convertedValues.length - 1) : 0;
            const topPosition = textPadding + (index * spacing);
            span.style.top = topPosition + 'px';
            
            labelsContainer.appendChild(span);
        });
        
        // Add labels container to scale container
        scaleContainer.appendChild(labelsContainer);
        
        // Update units button
        const unitText = config && config.units ? 
            config.units[currentUnitKey] : 
            this.weatherLayers.getCurrentUnit(layerType);
        unitsToggle.textContent = unitText;
        
        // Enable/disable units toggle
        if (config && config.units && Object.keys(config.units).length > 1) {
            unitsToggle.classList.remove('disabled');
            unitsToggle.style.opacity = '1';
        } else {
            unitsToggle.classList.add('disabled'); 
            unitsToggle.style.opacity = '0.6';
        }
    }

    // Convert value based on layer type and unit
    convertValue(value, layerType, unitKey) {
        if (!value) return null;
        
        const config = WeatherLayers.LEGEND_CONFIGS[layerType];
        if (!config || !config.units) return value;
        
        switch(layerType) {
            case 'temperature':
                if (unitKey === 'fahrenheit') {
                    this.unitConverter.celsius = value;
                    return this.unitConverter.fahrenheit;
                }
                return value; // celsius
                
            case 'wind':
                this.unitConverter.metersPerSecond = value;
                switch(unitKey) {
                    case 'speedKilometersPerHour': return this.unitConverter.kilometersPerHour;
                    case 'speedMilesPerHour': return this.unitConverter.milesPerHour;
                    case 'speedFeetPerSecond': return this.unitConverter.feetPerSecond;
                    case 'speedKnots': return this.unitConverter.knots;
                    default: return value;
                }
                
            case 'precipitation':
                this.unitConverter.millimetersPerHour = value;
                switch(unitKey) {
                    case 'inchesPerHour': return this.unitConverter.inchesPerHour;
                    default: return value;
                }
                
            case 'pressure':
                this.unitConverter.hectopascals = value;
                switch(unitKey) {
                    case 'millibars': return this.unitConverter.millibars;
                    case 'inchesOfMercury': return this.unitConverter.inchesOfMercury;
                    case 'kiloPascals': return this.unitConverter.kiloPascals;
                    default: return value;
                }
                
            case 'radar':
                switch(unitKey) {
                    case 'millimetersPerHour': return this.unitConverter.convertRadarToRainRate(value);
                    case 'rainIntensity': return this.unitConverter.convertRadarToIntensity(value);
                    default: return value;
                }
                
            default:
                return value;
        }
    }

    // Setup units toggle button
    setupUnitsToggle() {
        const unitsToggle = document.getElementById('unitsToggle');
        
        unitsToggle.addEventListener('click', () => {
            // Only allow toggle if button is not disabled
            if (unitsToggle.classList.contains('disabled')) {
                return;
            }
            
            const activeLayerType = this.weatherLayers.getActiveLayerType();
            const newUnit = this.weatherLayers.cycleUnits(activeLayerType);
            
            if (newUnit) {
                unitsToggle.textContent = newUnit;
                
                // Update legend values for new unit
                this.updateLegendScale(activeLayerType);
                
                // Show status
                if (window.showStatus) {
                    window.showStatus(`📊 Units: ${newUnit}`, 'info', 1500);
                }
            }
        });
    }
}

// Unit Converter Class
class UnitConverter {
    constructor() {
        this.tempValue = 0;
        this.speedValue = 0;
        this.precipValue = 0;
        this.pressureValue = 0;
    }

    // Temperature conversions
    get celsius() { return this.tempValue - 273.15; }
    get fahrenheit() { return 1.8 * this.celsius + 32; }
    get kelvin() { return this.tempValue < 0 ? 0 : this.tempValue; }
    set celsius(c) { this.tempValue = c + 273.15; }
    set fahrenheit(f) { this.tempValue = (f - 32) / 1.8 + 273.15; }
    set kelvin(k) { this.tempValue = k; }
    
    // Speed conversions  
    get metersPerSecond() { return this.speedValue; }
    get kilometersPerHour() { return 3600 * this.speedValue / 1000; }
    get milesPerHour() { return 2.237 * this.speedValue; }
    get knots() { return 1.94384 * this.speedValue; }
    get feetPerSecond() { return 3.28084 * this.speedValue; }
    set metersPerSecond(ms) { this.speedValue = ms; }
    set kilometersPerHour(kmph) { this.speedValue = 1000 * kmph / 3600; }
    set milesPerHour(mph) { this.speedValue = mph / 2.237; }
    set knots(knots) { this.speedValue = knots / 1.94384; }
    set feetPerSecond(ftps) { this.speedValue = ftps / 3.28084; }
    
    // Precipitation conversions (mm/h base)
    get millimetersPerHour() { return this.precipValue; }
    get inchesPerHour() { return this.precipValue / 25.4; }
    set millimetersPerHour(mmph) { this.precipValue = mmph; }
    set inchesPerHour(inph) { this.precipValue = inph * 25.4; }
    
    // Pressure conversions (hPa base)
    get hectopascals() { return this.pressureValue; }
    get millibars() { return this.pressureValue; } // hPa = mbar
    get inchesOfMercury() { return this.pressureValue / 33.8639; }
    get kiloPascals() { return this.pressureValue / 10; }
    set hectopascals(hpa) { this.pressureValue = hpa; }
    set millibars(mbar) { this.pressureValue = mbar; }
    set inchesOfMercury(inhg) { this.pressureValue = inhg * 33.8639; }
    set kiloPascals(kpa) { this.pressureValue = kpa * 10; }
    
    // Radar conversions (dBZ to rain rate approximations)
    convertRadarToRainRate(dbz) {
        // Marshall-Palmer relationship: Z = 200 * R^1.6
        if (dbz <= 0) return 0;
        const z = Math.pow(10, dbz / 10);
        return Math.pow(z / 200, 1 / 1.6);
    }
    
    convertRadarToIntensity(dbz) {
        if (dbz < 10) return "None";
        if (dbz < 15) return "Maybe";
        if (dbz < 25) return "Light";
        if (dbz < 40) return "Moderate"; 
        if (dbz < 50) return "Heavy";
        return "Extreme";
    }
}

// Export
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { LegendManager, UnitConverter };
} else {
    window.LegendManager = LegendManager;
    window.UnitConverter = UnitConverter;
} 