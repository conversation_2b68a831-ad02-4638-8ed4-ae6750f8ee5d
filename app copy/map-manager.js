// Map Manager - Initialize Map and Static Markers
class MapManager {
    constructor() {
        this.map = null;
        this.hoverTooltip = null;
        this.staticMarkerPopupTimer = null; // Timer for auto-hide static marker popups
    }

    // Initialize map
    async initializeMap() {
        try {
            if (window.showStatus) {
                window.showStatus('Khởi tạo MapTiler SDK...', 'info');
            }
            
            if (typeof maptilersdk === 'undefined') {
                throw new Error('MapTiler SDK không được load');
            }
            
            // Set API key from config
            maptilersdk.config.apiKey = CONFIG.MAPTILER_API_KEY;
            
            this.map = new maptilersdk.Map({
                container: 'map',
                style: maptilersdk.MapStyle[CONFIG.MAP_STYLE],
                center: CONFIG.MAP_CENTER,
                zoom: CONFIG.MAP_ZOOM,
                projection: CONFIG.MAP_PROJECTION
            });
            
            if (window.showStatus) {
                window.showStatus('✅ Bản đồ đã được tạo!', 'success');
            }
            
            // Map event listeners
            this.map.on('load', () => {
                if (window.showStatus) {
                    window.showStatus('🎉 Bản đồ đã sẵn sàng!', 'success');
                }
                this.updateMapInfo();
                
                // Make water transparent so weather data shows through
                try {
                    this.map.setPaintProperty("Water", 'fill-opacity', 0);
                    this.map.setPaintProperty("Water", 'fill-outline-color', "#666666");
                } catch (error) {
                    console.log('Water layer styling error:', error);
                }
                
                // Add static markers
                this.addStaticMarkers();
                
                // Hide loading screen
                this.hideLoading();
            });
            
            this.map.on('move', () => this.updateMapInfo());
            this.map.on('zoom', () => this.updateMapInfo());
            
            this.map.on('error', (error) => {
                if (window.showStatus) {
                    window.showStatus(`❌ Lỗi bản đồ: ${error.error?.message || error}`, 'error');
                }
                console.error('Map error:', error);
            });
            
            // Setup mouse events for weather data
            this.setupMouseEvents();
            
            return this.map;
            
        } catch (error) {
            if (window.showStatus) {
                window.showStatus(`❌ Lỗi khởi tạo: ${error.message}`, 'error');
            }
            console.error('Initialization error:', error);
            this.hideLoading();
            throw error;
        }
    }

    // Setup mouse events for hover tooltip
    setupMouseEvents() {
        this.map.on('mousemove', (e) => {
            this.updateHoverTooltip(e.lngLat, e.point);
        });
        
        this.map.on('mouseout', (e) => {
            this.hideHoverTooltip();
        });
    }

    // Add static markers from config
    addStaticMarkers() {
        try {
            const staticMarkers = CONFIG.STATIC_MARKERS;

            staticMarkers.forEach((markerData, index) => {
                try {
                    const lon = markerData.lon;
                    const lat = markerData.lat;

                    // Create main marker icon
                    const marker = new maptilersdk.Marker({
                        color: '#da251d' // Red color
                    })
                    .setLngLat([lon, lat])
                    .addTo(this.map);

                    // Create separate label element for hover
                    if (markerData.label || markerData.name) {
                        const labelElement = document.createElement('div');
                        labelElement.className = 'static-marker-label';
                        labelElement.style.position = 'absolute';
                        labelElement.style.background = 'rgba(218, 37, 29, 0.95)';
                        labelElement.style.borderRadius = '6px';
                        labelElement.style.padding = '4px 8px';
                        labelElement.style.fontWeight = '600';
                        labelElement.style.fontSize = '12px';
                        labelElement.style.color = 'white';
                        labelElement.style.boxShadow = '0 2px 8px rgba(0,0,0,0.4)';
                        labelElement.style.cursor = 'pointer';
                        labelElement.style.border = '2px solid white';
                        labelElement.style.zIndex = '999';
                        labelElement.style.pointerEvents = 'auto';
                        labelElement.innerText = `🏳️ ${markerData.label || markerData.name}`;

                        // Bỏ hover tooltip cho static markers vì tên đã có sẵn trên map
                        // Không cần mouseenter/mouseleave events nữa

                        // Click event
                        labelElement.addEventListener('click', (e) => {
                            e.stopPropagation();
                            
                            // Store reference to MapManager instance
                            const mapManager = this;
                            
                            // Hide weather data tooltip immediately when showing popup
                            mapManager.hideHoverTooltip();
                            
                            // Close all other popups
                            document.querySelectorAll('.maptiler-popup, .maplibregl-popup').forEach(p => p.remove());
                            
                            // Clear any existing popup timer
                            if (mapManager.staticMarkerPopupTimer) {
                                clearTimeout(mapManager.staticMarkerPopupTimer);
                                mapManager.staticMarkerPopupTimer = null;
                            }
                            
                            // Create and show popup
                            const popup = new maptilersdk.Popup({ 
                                offset: 25, 
                                closeButton: true,
                                closeOnClick: false
                            })
                            .setLngLat([lon, lat])
                            .setHTML(markerData.popupHtml || 'Chi tiết')
                            .addTo(mapManager.map);
                            
                            console.log('Static marker popup shown, setting auto-hide timer for 4s');
                            
                            // Set auto-hide timer for popup (4 seconds)
                            mapManager.staticMarkerPopupTimer = setTimeout(() => {
                                console.log('Auto-hiding static marker popup and weather tooltip after 4s');
                                popup.remove();
                                mapManager.hideHoverTooltip(); // Ẩn weather data tooltip
                                mapManager.staticMarkerPopupTimer = null;
                            }, 4000);
                        });

                        // Create label marker
                        new maptilersdk.Marker({
                            element: labelElement,
                            anchor: 'left',
                            offset: [15, -10] // Positioned to the right of marker
                        })
                        .setLngLat([lon, lat])
                        .addTo(this.map);
                    }

                    console.log(`✅ Added static marker: ${markerData.name}`);
                    
                } catch (error) {
                    console.error(`❌ Error adding marker ${markerData.name}:`, error);
                }
            });

            if (window.showStatus) {
                window.showStatus(`🏳️ Đã thêm ${staticMarkers.length} static markers`, 'success', 2000);
            }
            
        } catch (error) {
            console.error('Error adding static markers:', error);
            if (window.showStatus) {
                window.showStatus('❌ Lỗi thêm static markers', 'error');
            }
        }
    }

    // Update hover tooltip - follow mouse cursor
    updateHoverTooltip(lngLat, point, weatherLayers) {
        try {
            if (!lngLat || !point || !weatherLayers) return;
            
            const tooltip = document.getElementById('hoverTooltip');
            if (!tooltip) {
                console.warn('hoverTooltip element not found');
                return;
            }
            
            // Get ONLY the active layer
            const activeLayerType = weatherLayers.getActiveLayerType();
            let dataText = '';
            
            if (activeLayerType) {
                const value = weatherLayers.getDataAtPoint(activeLayerType, lngLat.lng, lngLat.lat);
                
                if (value) {
                    const unitText = weatherLayers.getCurrentUnit(activeLayerType);
                    
                    if (activeLayerType === 'wind' && value.speedMetersPerSecond !== undefined) {
                        const convertedValue = this.convertValue(value.speedMetersPerSecond, activeLayerType, weatherLayers);
                        dataText = `💨 ${convertedValue.toFixed(1)} ${unitText}`;
                    } else if (activeLayerType === 'temperature' && (value.celsius !== undefined || value.value !== undefined)) {
                        const tempValue = value.celsius !== undefined ? value.celsius : value.value;
                        const convertedValue = this.convertValue(tempValue, activeLayerType, weatherLayers);
                        dataText = `🌡️ ${convertedValue.toFixed(1)}${unitText}`;
                    } else if (activeLayerType === 'precipitation' && (value.millimeters !== undefined || value.value !== undefined)) {
                        const precipValue = value.millimeters !== undefined ? value.millimeters : value.value;
                        const convertedValue = this.convertValue(precipValue, activeLayerType, weatherLayers);
                        dataText = `🌧️ ${convertedValue.toFixed(1)} ${unitText}`;
                    } else if (activeLayerType === 'pressure' && (value.hectoPascals !== undefined || value.value !== undefined)) {
                        const pressureValue = value.hectoPascals !== undefined ? value.hectoPascals : value.value;
                        const convertedValue = this.convertValue(pressureValue, activeLayerType, weatherLayers);
                        dataText = `🌀 ${convertedValue.toFixed(0)} ${unitText}`;
                    } else if (activeLayerType === 'radar' && (value.dbz !== undefined || value.value !== undefined)) {
                        const radarValue = value.dbz !== undefined ? value.dbz : value.value;
                        const convertedValue = this.convertValue(radarValue, activeLayerType, weatherLayers);
                        dataText = `📡 ${convertedValue.toFixed(1)} ${unitText}`;
                    }
                }
            }
            
            if (dataText) {
                tooltip.textContent = dataText;
                tooltip.style.left = point.x + 'px';
                tooltip.style.top = point.y + 'px';
                tooltip.classList.add('visible');
            } else {
                tooltip.classList.remove('visible');
            }
            
        } catch (error) {
            console.error('Hover tooltip update error:', error);
        }
    }

    // Convert value for tooltip display
    convertValue(value, layerType, weatherLayers) {
        // This would use the legend manager's conversion logic
        // For now, return the raw value
        return value;
    }

    // Hide hover tooltip
    hideHoverTooltip() {
        const tooltip = document.getElementById('hoverTooltip');
        if (tooltip) {
            tooltip.classList.remove('visible');
        }
    }

    // Update map info display
    updateMapInfo() {
        if (!this.map) return;
        
        const center = this.map.getCenter();
        const zoom = this.map.getZoom();
        
        // Update current layer display if element exists
        const currentLayerEl = document.getElementById('currentLayer');
        if (currentLayerEl) {
            // This will be updated by the layer controls
        }
    }

    // Hide loading screen
    hideLoading() {
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.style.opacity = '0';
            setTimeout(() => {
                loadingScreen.style.display = 'none';
            }, 500);
        }
    }

    // Get map instance
    getMap() {
        return this.map;
    }

    // Handle window resize
    handleResize() {
        if (this.map) {
            this.map.resize();
        }
    }
}

// Export
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MapManager;
} else {
    window.MapManager = MapManager;
} 