<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>🌤️ Weather App Vietnam - MapTiler SDK</title>
    
    <!-- MapTiler SDK -->
    <script src="https://cdn.maptiler.com/maptiler-sdk-js/v3.2.0/maptiler-sdk.umd.min.js"></script>
    <link href="https://cdn.maptiler.com/maptiler-sdk-js/v3.2.0/maptiler-sdk.css" rel="stylesheet" />
    <!-- MapTiler Weather -->
    <script src="https://cdn.maptiler.com/maptiler-weather/v3.0.1/maptiler-weather.umd.min.js"></script>
    <!-- FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            color: #333;
            overflow: hidden;
            margin: 0;
            padding: 0;
        }
        
        .app-container {
            position: relative;
            height: 100vh;
            width: 100vw;
        }
        
        /* ===== RESPONSIVE DESIGN - MapTiler Style ===== */
        
        /* ===== BASE DESIGN - FIXED 10PX FROM EDGES ===== */
        .layer-controls {
            position: absolute;
            left: 10px;
            bottom: 70px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .layer-btn {
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 4px;
            padding: 6px 10px;
            min-width: 100px;
            height: 30px;
            display: flex;
            align-items: center;
            gap: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 1px 6px rgba(0,0,0,0.1);
            backdrop-filter: blur(8px);
            font-size: 12px;
            font-weight: 500;
        }
        
        .layer-btn.active {
            background: #3174ff;
            color: white;
            border-color: transparent;
        }
        
        .layer-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 10px rgba(0,0,0,0.15);
        }
        
        .layer-btn .icon {
            font-size: 14px;
            flex-shrink: 0;
        }
        
        .layer-btn .text {
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            transition: all 0.2s ease;
        }
        
        .opacity-control {
            position: absolute;
            right: 10px;
            bottom: 180px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.95);
            padding: 12px;
            border-radius: 6px;
            backdrop-filter: blur(8px);
            box-shadow: 0 1px 6px rgba(0,0,0,0.1);
            min-width: 160px;
        }
        
        .legend {
            position: absolute;
            right: 10px;
            bottom: 70px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 6px;
        }
        
        .timeline-control {
            position: absolute;
            left: 10px;
            right: 10px;
            bottom: 10px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.95);
            padding: 12px;
            border-radius: 6px;
            backdrop-filter: blur(8px);
            box-shadow: 0 1px 6px rgba(0,0,0,0.1);
            height: 50px;
        }
        
        /* ===== SIMPLIFIED 2-BREAKPOINT RESPONSIVE SYSTEM ===== */
        
        /* BREAKPOINT 1: Mobile (≤768px) - Square Icon Buttons */
        @media (max-width: 768px) {
            /* Layer controls - FIXED left: 10px, no jumping */
            .layer-controls {
                left: 10px;
                bottom: 60px;
            }
            
            /* Layer buttons become 35px squares with icons only */
            .layer-btn {
                min-width: 35px;
                width: 35px;
                height: 35px;
                padding: 8px;
                justify-content: center;
            }
            
            .layer-btn .text {
                display: none; /* Hide text on mobile */
            }
            
            .layer-btn .icon {
                font-size: 16px;
            }
            
            /* Right side controls - FIXED right: 10px, no jumping */
            .opacity-control {
                right: 10px;
                bottom: 150px;
                min-width: 140px;
                padding: 10px;
            }
            
            .legend {
                right: 10px;
                bottom: 60px;
                padding: 8px;
                gap: 4px;
            }
            
            .legend canvas {
                width: 25px !important;
                height: 120px !important;
            }
            
            .legend-text {
                font-size: 10px;
                padding: 10px 0;
            }
            
            /* Timeline - FIXED left/right: 10px, no jumping */
            .timeline-control {
                left: 10px;
                right: 10px;
                bottom: 10px;
                height: 40px;
                padding: 8px;
            }
            
            .timeline-buttons {
                gap: 4px;
            }
            
            .timeline-btn {
                padding: 4px 6px;
                font-size: 11px;
                height: 24px;
            }
        }
        
        /* BREAKPOINT 2: Desktop (≥769px) - Full Buttons with Text */
        @media (min-width: 769px) {
            /* Layer controls - FIXED left: 10px, no jumping */
            .layer-controls {
                left: 10px;
                bottom: 70px;
            }
            
            /* Layer buttons show text */
            .layer-btn {
                min-width: 100px;
                height: 30px;
                padding: 6px 10px;
                justify-content: flex-start;
            }
            
            .layer-btn .text {
                display: block;
            }
            
            /* Right side controls - FIXED right: 10px, no jumping */
            .opacity-control {
                right: 10px;
                bottom: 180px;
                min-width: 160px;
                padding: 12px;
            }
            
            .legend {
                right: 10px;
                bottom: 70px;
                padding: 12px;
            }
            
            .legend canvas {
                width: 30px !important;
                height: 160px !important;
            }
            
            .legend-text {
                font-size: 11px;
                padding: 12px 0;
            }
            
            /* Timeline - FIXED left/right: 10px, no jumping */
            .timeline-control {
                left: 10px;
                right: 10px;
                bottom: 10px;
                height: 50px;
                padding: 12px;
            }
        }
        
        /* ===== ANIMATIONS & TRANSITIONS ===== */
        
        /* Smooth transitions for all responsive changes */
        .layer-btn,
        .legend,
        .opacity-control,
        .timeline-control {
            transition: all 0.2s ease;
        }
        
        /* Current Layer Display */
        .current-layer {
            position: fixed;
            top: 15px;
            left: 15px;
            z-index: 1000;
            color: white;
            font-size: 20px;
            font-weight: 500;
            text-shadow: 0 0 10px rgba(0,0,0,0.7);
            text-transform: capitalize;
        }
        
        /* Weather Data Tooltip - Below cursor */
        .hover-tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            z-index: 1000;
            pointer-events: none;
            white-space: nowrap;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            transform: translate(10px, 10px); /* Below cursor */
            opacity: 0;
            transition: opacity 0.2s ease;
        }
        
        .hover-tooltip.visible {
            opacity: 1;
        }

        /* Static Marker Labels */
        .static-marker-label {
            white-space: nowrap;
            transition: transform 0.2s ease;
        }

        .static-marker-label:hover {
            transform: scale(1.05);
        }

        /* Popup Override */
        .maplibregl-popup {
            z-index: 1003 !important;
        }
        
        /* Animation Controls */
        .animation-controls {
            text-align: center;
        }
        
        .play-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 25px;
            width: 50px;
            height: 50px;
            font-size: 18px;
            cursor: pointer;
            margin: 10px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            transition: transform 0.2s ease;
        }
        
        .play-button:hover {
            transform: scale(1.1);
        }
        
        .radar-prev-btn, .radar-next-btn {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            margin: 0 2px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(240, 147, 251, 0.4);
        }
        
        .radar-prev-btn:hover, .radar-next-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(240, 147, 251, 0.6);
        }
        
        .speed-control {
            margin: 10px 0;
        }
        
        .speed-control input {
            width: 100%;
            margin: 5px 0;
        }
        
        /* Timeline Controls */
        .timeline-controls {
            background: rgba(255,255,255,0.9);
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
        }
        
        .timeline-slider {
            width: 100%;
            margin: 10px 0;
        }
        
        .time-display {
            text-align: center;
            font-size: 14px;
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 10px;
        }
        
        /* Legacy legend classes - now handled by main .legend */
        .legend-scale-right {
            /* Remove duplicate - using main .legend now */
        }
        
        /* Units Toggle Button */
        .units-toggle {
            background: rgba(255, 255, 255, 0.95);
            color: #6c757d;
            border: none;
            border-radius: 4px;
            padding: 8px 12px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 45px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            backdrop-filter: blur(10px);
        }
        
        .units-toggle:hover {
            background: rgba(255, 255, 255, 1);
            color: #3174ff;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        
        .scale-bar {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 2px;
            padding: 1px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            backdrop-filter: blur(10px);
        }
        
        /* Timeline Content */
        .timeline-content {
            display: flex;
            align-items: center;
            gap: 12px;
            width: 100%;
        }
        
        .timeline-play-btn {
            background: #3174ff;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            flex-shrink: 0;
        }
        
        .timeline-play-btn:hover {
            background: #275dcc;
            transform: scale(1.05);
        }
        
        .time-display {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            min-width: 120px;
            text-align: center;
            flex-shrink: 0;
        }
        
        .time-step-select {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 6px 10px;
            font-size: 12px;
            cursor: pointer;
            min-width: 80px;
            flex-shrink: 0;
        }
        
        /* Opacity Control Styling - Integrated with responsive system */
        .opacity-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }
        
        .opacity-icon {
            margin-right: 8px;
            font-size: 14px;
        }
        
        .opacity-slider-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .opacity-slider {
            flex: 1;
            height: 4px;
            border-radius: 2px;
            appearance: none;
            background: #e9ecef;
            outline: none;
            cursor: pointer;
        }
        
        .opacity-slider::-webkit-slider-thumb {
            appearance: none;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #3174ff;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .opacity-slider::-moz-range-thumb {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #3174ff;
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .opacity-value {
            font-size: 12px;
            font-weight: 600;
            color: #666;
            min-width: 35px;
            text-align: right;
        }
        

        

        
        /* Đã xóa hết CSS duplicate của thang cũ */
        

        
        /* Bottom Timeline Bar - MapTiler Style */
        .timeline-bar {
            position: absolute;
            left: 16px;
            bottom: 16px;
            right: 8px;
            background: rgba(255, 255, 255, 0.5);
            padding: 8px 12px;
            z-index: 1000;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-radius: 4px;
            height: 60px;
        }
        
        .timeline-content {
            display: flex;
            align-items: center;
            gap: 12px;
            width: 100%;
            flex: 1;
        }
        
        .timeline-play-btn {
            background: #ffffff;
            color: #6c757d;
            border: none;
            border-radius: 20px;
            width: 40px;
            height: 40px;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }
        
        .timeline-play-btn:hover {
            color: #007bff;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        
        .timeline-slider {
            flex: 1;
            height: 4px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 2px;
            outline: none;
            cursor: pointer;
            appearance: none;
            width: 100%;
        }
        
        .timeline-slider::-webkit-slider-thumb {
            appearance: none;
            width: 16px;
            height: 16px;
            background: #007bff;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .timeline-slider::-moz-range-thumb {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #007bff;
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .time-display {
            font-size: 12px;
            font-weight: 600;
            color: #ffffff;
            text-shadow: 1px 1px 0 #6c757d;
            flex-shrink: 0;
        }
        
        .time-step-control {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 11px;
            color: #ffffff;
            text-shadow: 1px 1px 0 #6c757d;
        }
        
        .time-step-select {
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            font-size: 12px;
            font-weight: 600;
            color: #333;
            cursor: pointer;
            min-width: 60px;
        }
        
        .time-step-select:focus {
            outline: none;
            border-color: #4285f4;
        }
        
        /* Map Container */
        .map-container {
            position: relative;
            height: 100vh;
            width: 100vw;
        }
        
        #map {
            width: 100%;
            height: 100%;
        }
        
        /* Weather Info Display */
        .weather-info {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            padding: 10px 15px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            z-index: 500;
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
        }
        
        .pointer-data {
            position: absolute;
            top: 60px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            padding: 8px 12px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            z-index: 500;
            font-size: 14px;
            font-weight: 500;
            color: #4299e1;
            min-width: 120px;
            white-space: pre-line;
        }
        
        /* Loading Screen */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.5s ease;
        }
        
        .loading-content {
            text-align: center;
            color: white;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255,255,255,0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Status Messages */
        .status-message {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.95);
            padding: 10px 15px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            z-index: 1000;
            max-width: 300px;
            transform: translateX(320px);
            transition: transform 0.3s ease;
        }
        
        .status-message.show {
            transform: translateX(0);
        }
        
        .status-message.success {
            border-left: 4px solid #48bb78;
        }
        
        .status-message.error {
            border-left: 4px solid #f56565;
        }
        
        .status-message.info {
            border-left: 4px solid #4299e1;
        }
        
        /* Weather Legend */
        .weather-legend {
            position: absolute;
            bottom: 20px;
            right: 0px;
            background: rgba(255,255,255,0.95);
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            z-index: 1000;
            max-width: 300px;
            backdrop-filter: blur(10px);
        }
        
        .legend-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            border-bottom: 1px solid #e2e8f0;
            font-weight: 600;
            color: #2d3748;
        }
        
        .legend-toggle {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.2s ease;
        }
        
        .legend-toggle:hover {
            background: rgba(0,0,0,0.1);
            transform: scale(1.1);
        }
        
        .legend-content {
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .legend-content.collapsed {
            display: none;
        }
        
        .legend-item {
            margin-bottom: 15px;
        }
        
        .legend-item h4 {
            margin: 0 0 8px 0;
            font-size: 14px;
            font-weight: 600;
            color: #4a5568;
        }
        
        /* Temperature Legend */
        .temp-scale {
            display: flex;
            flex-direction: column;
        }
        
        .temp-bar {
            height: 20px;
            border-radius: 10px;
            margin-bottom: 5px;
        }
        
        .temp-labels {
            display: flex;
            justify-content: space-between;
            font-size: 11px;
            color: #718096;
        }
        
        /* Wind, Precipitation, Pressure, Radar Legends */
        .wind-scale, .precip-scale, .pressure-scale, .radar-scale {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .wind-item, .precip-item, .pressure-item, .radar-item {
            display: flex;
            align-items: center;
            font-size: 12px;
            color: #4a5568;
        }
        
        .wind-color, .precip-color, .pressure-color, .radar-color {
            width: 16px;
            height: 16px;
            border-radius: 3px;
            margin-right: 8px;
            border: 1px solid rgba(0,0,0,0.1);
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .layer-buttons {
                left: 10px;
                top: 90px;
            }
            
            .layer-btn {
                min-width: 120px;
                padding: 10px 12px;
                font-size: 12px;
            }
            
            .layer-icon {
                font-size: 16px;
                margin-right: 6px;
            }
            
            .current-layer {
                top: 20px;
                font-size: 16px;
            }
            
            .pointer-data {
                top: 45px;
                font-size: 16px;
            }
            
            .legend-scale-right {
                right: 10px;
                padding: 8px;
            }
            
            .scale-bar {
                width: 25px;
                height: 150px;
            }
            
            .timeline-bar {
                padding: 10px 15px;
            }
            
            .timeline-content {
                gap: 8px;
            }
            
            .timeline-play-btn {
                width: 35px;
                height: 35px;
                font-size: 14px;
            }
            
            .time-step-control {
                font-size: 11px;
            }
            
            .time-step-select {
                width: 80px;
                font-size: 10px;
            }
        }
        

    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h2>🌤️ Weather App Vietnam</h2>
            <p>Đang khởi tạo bản đồ thời tiết với MapTiler SDK...</p>
        </div>
    </div>
    
    <!-- Status Messages -->
    <div class="status-message" id="statusMessage"></div>
    
    <div class="app-container">
        <!-- Layer Toggle Buttons - Left Side -->
        <div class="layer-controls" id="layerButtons">
            <button class="layer-btn" id="temperature" data-layer="temperature">
                <i class="fas fa-thermometer-half layer-icon"></i>
                <span class="text">Temperature</span>
            </button>
            <button class="layer-btn" id="precipitation" data-layer="precipitation">
                <i class="fas fa-cloud-rain layer-icon"></i>
                <span class="text">Precipitation</span>
            </button>
            <button class="layer-btn active" id="wind" data-layer="wind">
                <i class="fas fa-wind layer-icon"></i>
                <span class="text">Wind</span>
            </button>
            <button class="layer-btn" id="pressure" data-layer="pressure">
                <i class="fas fa-tachometer-alt layer-icon"></i>
                <span class="text">Pressure</span>
            </button>
            <button class="layer-btn" id="radar" data-layer="radar">
                <i class="fas fa-satellite-dish layer-icon"></i>
                <span class="text">Radar</span>
            </button>
            <button class="layer-btn" id="isobar" data-layer="isobar">
                <i class="fas fa-map layer-icon"></i>
                <span class="text">Isobar</span>
            </button>
            <button class="layer-btn" id="arrow" data-layer="arrow">
                <i class="fas fa-location-arrow layer-icon"></i>
                <span class="text">Arrow</span>
            </button>
        </div>
        

        
        <!-- Map Container -->
        <div class="map-container">
            <div id="map"></div>
            
            <!-- Current Layer Name Display -->
            <div class="current-layer" id="currentLayer">Wind</div>
            
            <!-- Weather Data Tooltip -->
            <div class="hover-tooltip" id="hoverTooltip"></div>
            
            <!-- Opacity Control -->
            <div class="opacity-control" id="opacityControl">
                <div class="opacity-header">
                    <i class="fas fa-adjust opacity-icon"></i>
                    <span id="opacityLayerName">Layer Opacity</span>
                </div>
                <div class="opacity-slider-container">
                    <input type="range" class="opacity-slider" id="opacitySlider" min="0" max="100" value="75">
                    <span class="opacity-value" id="opacityValue">75%</span>
                </div>
            </div>
            
            <!-- Right Side Legend Scale -->
            <div class="legend" id="legendScale">
                <button class="units-toggle" id="unitsToggle">m/s</button>
                <div class="scale-bar" id="scaleBar">
                    <!-- Thang màu MapTiler sẽ được tạo động ở đây -->
                </div>
            </div>
        </div>
        
        <!-- Bottom Timeline Bar -->
        <div class="timeline-control" id="timelineBar">
            <div class="timeline-content">
                <button class="timeline-play-btn" id="playButton"><i class="fas fa-play"></i></button>
                <div class="time-display" id="timeDisplay">Loading...</div>
                <input type="range" class="timeline-slider" id="timelineSlider" min="0" max="100" value="0">
                <div class="time-step-control">
                    <select id="timeStepSelect" class="time-step-select">
                        <option value="minute">Phút</option>
                        <option value="hour" selected>Giờ</option>
                        <option value="day">Ngày</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <script>
        console.log('🚀 Starting Weather App Vietnam with MapTiler SDK...');
        
        // Global variables
        let map;
        let activeLayers = new Set();
        let addedLayers = new Set(); // Track which layers are added to map
        let isPlaying = false;
        let animationSpeed = 1000;
        let timeStep = 'hour'; // 'minute', 'hour', 'day'
        let animationInterval = null;
        
        // MapTiler Official Legend Configurations - Exact Copy from 2811.6e69ec22f85859b6.js
        const legendConfigs = {
            wind: {
                colorramp: 'VIRIDIS',
                scale: [0, 40],
                values: [40, 30, 20, 15, 10, 5, 0],
                units: {
                    speedMetersPerSecond: "m/s",
                    speedKilometersPerHour: "km/h", 
                    speedMilesPerHour: "mph",
                    speedFeetPerSecond: "ft/s",
                    speedKnots: "knots"
                },
                defaultUnit: 'speedKilometersPerHour'
            },
            temperature: {
                colorramp: 'TEMPERATURE_2',
                scale: [-50, 50],
                values: [40, 20, 10, 0, -10, -20, -40],
                units: {
                    celsius: "°C",
                    fahrenheit: "°F"
                },
                defaultUnit: 'celsius'
            },
            precipitation: {
                colorramp: 'PRECIPITATION',
                values: [40, 30, 20, 15, 10, 5, 0],
                units: {
                    millimetersPerHour: "mm/h",
                    inchesPerHour: "in/h"
                },
                defaultUnit: 'millimetersPerHour'
            },
            pressure: {
                colorramp: 'PRESSURE_2',
                values: [1050, 1030, 1010, 990, 970, 950, 930],
                units: {
                    hectopascals: "hPa",
                    millibars: "mbar",
                    inchesOfMercury: "inHg",
                    kiloPascals: "kPa"
                },
                defaultUnit: 'hectopascals'
            },
            radar: {
                colorramp: 'RADAR',
                values: [60, 50, 40, 25, 15, 10, 0],
                units: {
                    decibelsZ: "dBZ",
                    millimetersPerHour: "mm/h",
                    rainIntensity: "Light/Moderate/Heavy"
                },
                defaultUnit: 'decibelsZ'
            }
        };
        
        // Weather layers
        let windLayer = null;
        let temperatureLayer = null;
        let precipitationLayer = null;
        let pressureLayer = null;
        let radarLayer = null;
        
        // Radar animation variables
        let radarFrames = [];
        let radarAnimationTimer = null;
        
        // Unit conversion system - MapTiler Style
        const unitConverter = (() => {
            let tempValue = 0;
            let speedValue = 0;
            let precipValue = 0;
            let pressureValue = 0;
            
            return {
                // Temperature conversions
                get celsius() { return tempValue - 273.15; },
                get fahrenheit() { return 1.8 * this.celsius + 32; },
                get kelvin() { return tempValue < 0 ? 0 : tempValue; },
                set celsius(c) { tempValue = c + 273.15; },
                set fahrenheit(f) { tempValue = (f - 32) / 1.8 + 273.15; },
                set kelvin(k) { tempValue = k; },
                
                // Speed conversions  
                get metersPerSecond() { return speedValue; },
                get kilometersPerHour() { return 3600 * speedValue / 1000; },
                get milesPerHour() { return 2.237 * speedValue; },
                get knots() { return 1.94384 * speedValue; },
                get feetPerSecond() { return 3.28084 * speedValue; },
                set metersPerSecond(ms) { speedValue = ms; },
                set kilometersPerHour(kmph) { speedValue = 1000 * kmph / 3600; },
                set milesPerHour(mph) { speedValue = mph / 2.237; },
                set knots(knots) { speedValue = knots / 1.94384; },
                set feetPerSecond(ftps) { speedValue = ftps / 3.28084; },
                
                // Precipitation conversions (mm/h base)
                get millimetersPerHour() { return precipValue; },
                get inchesPerHour() { return precipValue / 25.4; },
                set millimetersPerHour(mmph) { precipValue = mmph; },
                set inchesPerHour(inph) { precipValue = inph * 25.4; },
                
                // Pressure conversions (hPa base)
                get hectopascals() { return pressureValue; },
                get millibars() { return pressureValue; }, // hPa = mbar
                get inchesOfMercury() { return pressureValue / 33.8639; },
                get kiloPascals() { return pressureValue / 10; },
                set hectopascals(hpa) { pressureValue = hpa; },
                set millibars(mbar) { pressureValue = mbar; },
                set inchesOfMercury(inhg) { pressureValue = inhg * 33.8639; },
                set kiloPascals(kpa) { pressureValue = kpa * 10; },
                
                // Radar conversions (dBZ to rain rate approximations)
                convertRadarToRainRate(dbz) {
                    // Marshall-Palmer relationship: Z = 200 * R^1.6
                    // Where Z = 10^(dBZ/10), R = rain rate in mm/h
                    if (dbz <= 0) return 0;
                    const z = Math.pow(10, dbz / 10);
                    return Math.pow(z / 200, 1 / 1.6);
                },
                convertRadarToIntensity(dbz) {
                    if (dbz < 10) return "None";
                    if (dbz < 15) return "Maybe"; // 10-14 dBZ: có khả năng mưa nhẹ
                    if (dbz < 25) return "Light";
                    if (dbz < 40) return "Moderate"; 
                    if (dbz < 50) return "Heavy";
                    return "Extreme";
                }
            };
        })();
        

        
        // Show status message
        function showStatus(message, type = 'info', duration = 3000) {
            const statusEl = document.getElementById('statusMessage');
            statusEl.textContent = message;
            statusEl.className = `status-message show ${type}`;
            
            setTimeout(() => {
                statusEl.classList.remove('show');
            }, duration);
        }
        
        // Hide loading screen
        function hideLoading() {
            const loadingScreen = document.getElementById('loadingScreen');
            loadingScreen.style.opacity = '0';
            setTimeout(() => {
                loadingScreen.style.display = 'none';
            }, 500);
        }
        
        // Initialize map
        async function initializeMap() {
            try {
                showStatus('Khởi tạo MapTiler SDK...', 'info');
                
                if (typeof maptilersdk === 'undefined') {
                    throw new Error('MapTiler SDK không được load');
                }
                
                // Set API key
                maptilersdk.config.apiKey = 'oinoatcrNmdCL1524DOl';
                
                map = new maptilersdk.Map({
                    container: 'map',
                    style: maptilersdk.MapStyle.BACKDROP,
                    center: [105.8542, 21.0285], // Vietnam center
                    zoom: 6,
                    projection: 'mercator'
                });
                
                showStatus('✅ Bản đồ đã được tạo!', 'success');
                
                // Map event listeners
                map.on('load', () => {
                    showStatus('🎉 Bản đồ đã sẵn sàng!', 'success');
                    updateMapInfo();
                    
                    // Make water transparent so weather data shows through, keep only borders
                    try {
                        map.setPaintProperty("Water", 'fill-opacity', 0);
                        map.setPaintProperty("Water", 'fill-outline-color', "#666666");
                    } catch (error) {
                        console.log('Water layer styling error:', error);
                    }
                    
                    // Add static markers
                    addStaticMarkers();
                    
                    // Initialize weather layers
                    initializeWeatherLayers();
                    hideLoading();
                });
                
                map.on('move', updateMapInfo);
                map.on('zoom', updateMapInfo);
                
                map.on('error', (error) => {
                    showStatus(`❌ Lỗi bản đồ: ${error.error?.message || error}`, 'error');
                    console.error('Map error:', error);
                });
                
                // Mouse events for weather data
                map.on('mousemove', (e) => {
                    updateHoverTooltip(e.lngLat, e.point);
                });
                
                map.on('mouseout', (e) => {
                    hideHoverTooltip();
                });
                
                return map;
                
            } catch (error) {
                showStatus(`❌ Lỗi khởi tạo: ${error.message}`, 'error');
                console.error('Initialization error:', error);
                hideLoading();
                throw error;
            }
        }
        

        
        // Add static markers from JSON
        async function addStaticMarkers() {
            try {
                // Static markers data
                const staticMarkers = [
                    {
                        "name": "QĐ. Hoàng Sa",
                        "lat": 16.5,
                        "lon": 112.0,
                        "label": "QĐ. Hoàng Sa",
                        "popupHtml": "<div style='min-width:180px; background:#fff; border-radius:8px; padding:0; color:#222; text-align:center;'><div style='width:100%;height:38px;background:#da251d;border-radius:6px 6px 6px 6px;display:flex;align-items:center;justify-content:center;'><svg width='32' height='32' viewBox='0 0 32 32'><polygon points='16,5 17.8,13 26,13 19.5,17.5 21.5,25 16,20.5 10.5,25 12.5,17.5 6,13 14.2,13' fill='#ff0'/></svg></div><div style='padding:10px 8px 8px 8px;'><b style='color:#d32;'>QĐ. Hoàng Sa</b> - Là của Việt Nam</div></div>"
                    },
                    {
                        "name": "QĐ. Trường Sa",
                        "lat": 9.166666666666666,
                        "lon": 114.41666666666667,
                        "label": "QĐ. Trường Sa",
                        "popupHtml": "<div style='min-width:180px; background:#fff; border-radius:8px; padding:0; color:#222; text-align:center;'><div style='width:100%;height:38px;background:#da251d;border-radius:6px 6px 6px 6px;display:flex;align-items:center;justify-content:center;'><svg width='32' height='32' viewBox='0 0 32 32'><polygon points='16,5 17.8,13 26,13 19.5,17.5 21.5,25 16,20.5 10.5,25 12.5,17.5 6,13 14.2,13' fill='#ff0'/></svg></div><div style='padding:10px 8px 8px 8px;'><b style='color:#d32;'>QĐ. Trường Sa</b> - Là của Việt Nam</div></div>"
                    }
                ];

                // Add markers to map - EXACTLY like script.js approach
                staticMarkers.forEach((markerData, index) => {
                    try {
                        const lon = markerData.lon;
                        const lat = markerData.lat;

                        // Create main marker icon (like script.js - simple approach)
                        const marker = new maptilersdk.Marker({
                            color: '#da251d' // Red color
                        })
                        .setLngLat([lon, lat])
                        .addTo(map);

                        // Create separate label element for hover (like script.js)
                        if (markerData.label || markerData.name) {
                            const labelElement = document.createElement('div');
                            labelElement.className = 'static-marker-label';
                            labelElement.style.position = 'absolute';
                            labelElement.style.background = 'rgba(218, 37, 29, 0.95)';
                            labelElement.style.borderRadius = '6px';
                            labelElement.style.padding = '4px 8px';
                            labelElement.style.fontWeight = '600';
                            labelElement.style.fontSize = '12px';
                            labelElement.style.color = 'white';
                            labelElement.style.boxShadow = '0 2px 8px rgba(0,0,0,0.4)';
                            labelElement.style.cursor = 'pointer';
                            labelElement.style.border = '2px solid white';
                            labelElement.style.zIndex = '1004';
                            labelElement.style.pointerEvents = 'auto';
                            labelElement.innerText = `🏳️ ${markerData.label || markerData.name}`;

                            // SIMPLE click event - exactly like script.js
                            labelElement.addEventListener('click', function(e) {
                                e.stopPropagation();
                                
                                // Close all other popups
                                document.querySelectorAll('.maptiler-popup, .maplibregl-popup').forEach(p => p.remove());
                                
                                // Create and show popup
                                const popup = new maptilersdk.Popup({ 
                                    offset: 25, 
                                    closeButton: true,
                                    closeOnClick: false
                                })
                                .setLngLat([lon, lat])
                                .setHTML(markerData.popupHtml || 'Chi tiết')
                                .addTo(map);
                            });

                            // Create label marker - exactly like script.js
                            new maptilersdk.Marker({
                                element: labelElement,
                                anchor: 'left',
                                offset: [15, -10] // Positioned to the right of marker
                            })
                            .setLngLat([lon, lat])
                            .addTo(map);
                        }

                        console.log(`✅ Added static marker: ${markerData.name}`);
                        
                    } catch (error) {
                        console.error(`❌ Error adding marker ${markerData.name}:`, error);
                    }
                });

                showStatus(`🏳️ Đã thêm ${staticMarkers.length} static markers`, 'success', 2000);
                
            } catch (error) {
                console.error('Error adding static markers:', error);
                showStatus('❌ Lỗi thêm static markers', 'error');
            }
        }

        // Initialize weather layers
        function initializeWeatherLayers() {
            try {
                if (typeof maptilerweather === 'undefined') {
                    showStatus('❌ MapTiler Weather SDK không được load', 'error');
                    return;
                }
                
                // Initialize all weather layers with EXACT MapTiler built-in colorramps
                windLayer = new maptilerweather.WindLayer({
                    colorramp: maptilerweather.ColorRamp.builtin.VIRIDIS.scale(0, 40)
                });
                temperatureLayer = new maptilerweather.TemperatureLayer({
                    colorramp: maptilerweather.ColorRamp.builtin.TEMPERATURE_2.scale(-50, 50)
                });
                precipitationLayer = new maptilerweather.PrecipitationLayer({
                    colorramp: maptilerweather.ColorRamp.builtin.PRECIPITATION
                });
                pressureLayer = new maptilerweather.PressureLayer({
                    colorramp: maptilerweather.ColorRamp.builtin.PRESSURE_2
                });
                radarLayer = new maptilerweather.RadarLayer({
                    colorramp: maptilerweather.ColorRamp.builtin.RADAR
                });
                
                // Debug: check all layers are created
                console.log('🌤️ Weather layers created:', {
                    wind: !!windLayer,
                    temperature: !!temperatureLayer, 
                    precipitation: !!precipitationLayer,
                    pressure: !!pressureLayer,
                    radar: !!radarLayer,
                    windGetColorRamp: !!(windLayer && windLayer.getColorRamp),
                    windLayerType: windLayer?.constructor?.name
                });
                
                // Setup wind layer events (primary layer for timeline control)
                windLayer.on("sourceReady", () => {
                    setupTimelineControls(windLayer);
                    showStatus('💨 Wind layer ready!', 'success', 2000);
                });
                
                windLayer.on("tick", () => {
                    refreshTime();
                });
                
                windLayer.on("animationTimeSet", () => {
                    refreshTime();
                });
                
                // Setup all layers events EXACTLY like wind2.html
                temperatureLayer.on("sourceReady", () => {
                    showStatus('🌡️ Temperature layer ready!', 'success', 2000);
                });
                
                temperatureLayer.on("tick", () => {
                    refreshTime();
                });
                
                temperatureLayer.on("animationTimeSet", () => {
                    refreshTime();
                });
                
                precipitationLayer.on("sourceReady", () => {
                    showStatus('🌧️ Precipitation layer ready!', 'success', 2000);
                });
                
                precipitationLayer.on("tick", () => {
                    refreshTime();
                });
                
                precipitationLayer.on("animationTimeSet", () => {
                    refreshTime();
                });
                
                pressureLayer.on("sourceReady", () => {
                    showStatus('🌀 Pressure layer ready!', 'success', 2000);
                });
                
                pressureLayer.on("tick", () => {
                    refreshTime();
                });
                
                pressureLayer.on("animationTimeSet", () => {
                    refreshTime();
                });
                

                
                // Setup radar layer events
                radarLayer.on("sourceReady", () => {
                    showStatus('📡 MapTiler Radar ready!', 'success', 2000);
                });
                
                showStatus('🌤️ Weather layers initialized!', 'success');
                
            } catch (error) {
                showStatus(`❌ Lỗi khởi tạo weather layers: ${error.message}`, 'error');
                console.error('Weather layers error:', error);
            }
        }

        
        // Setup timeline controls - ONLY ONCE
        let timelineInitialized = false;
        function setupTimelineControls(layer) {
            try {
                const timelineSlider = document.getElementById('timelineSlider');
                
                // Only setup event listener once to avoid conflicts
                if (!timelineInitialized) {
                    console.log('Setting up timeline event listener...');
                    
                    // Clear any existing listeners
                    timelineSlider.replaceWith(timelineSlider.cloneNode(true));
                    const newTimelineSlider = document.getElementById('timelineSlider');
                    
                    // Setup single event listener EXACTLY like wind2.html
                    newTimelineSlider.addEventListener('input', (evt) => {
                        // EXACT same as wind2.html line 56: parseInt(timeSlider.value / 1000)
                        const newTime = parseInt(evt.target.value / 1000);
                        console.log('Timeline changed to:', newTime, new Date(newTime));
                        
                        // Get active layer and update it
                        const activeLayer = Array.from(activeLayers)[0];
                        console.log('Active layer:', activeLayer);
                        
                        if (activeLayer === 'wind' && windLayer) {
                            windLayer.setAnimationTime(newTime);
                        } else if (activeLayer === 'temperature' && temperatureLayer) {
                            temperatureLayer.setAnimationTime(newTime);
                        } else if (activeLayer === 'precipitation' && precipitationLayer) {
                            precipitationLayer.setAnimationTime(newTime);
                        } else if (activeLayer === 'pressure' && pressureLayer) {
                            pressureLayer.setAnimationTime(newTime);
                        } else if (activeLayer === 'radar' && radarLayer) {
                            radarLayer.setAnimationTime(newTime);
                        }
                    });
                    
                    timelineInitialized = true;
                }
                
                // Setup timeline range
                const startDate = layer.getAnimationStartDate();
                const endDate = layer.getAnimationEndDate();
                const currentDate = layer.getAnimationTimeDate();
                
                console.log('Timeline setup:', {startDate, endDate, currentDate});
                
                if (startDate && endDate && currentDate) {
                    const timelineSlider = document.getElementById('timelineSlider');
                    timelineSlider.min = +startDate;
                    timelineSlider.max = +endDate;
                    timelineSlider.value = +currentDate;
                    
                    console.log('Timeline range set:', timelineSlider.min, timelineSlider.max, timelineSlider.value);
                }
                
                refreshTime();
                
            } catch (error) {
                console.error('Timeline setup error:', error);
                showStatus('❌ Lỗi setup timeline', 'error', 2000);
            }
        }
        
        // Update time display - EXACT wind2.html style
        function refreshTime() {
            try {
                // Get current date from active layer
                const activeLayer = Array.from(activeLayers)[0];
                let activeLayerObj = null;
                
                if (activeLayer === 'wind' && windLayer) {
                    activeLayerObj = windLayer;
                } else if (activeLayer === 'temperature' && temperatureLayer) {
                    activeLayerObj = temperatureLayer;
                } else if (activeLayer === 'precipitation' && precipitationLayer) {
                    activeLayerObj = precipitationLayer;
                } else if (activeLayer === 'pressure' && pressureLayer) {
                    activeLayerObj = pressureLayer;
                } else if (activeLayer === 'radar' && radarLayer) {
                    activeLayerObj = radarLayer;
                } else if (windLayer) {
                    // Fallback to wind layer if no active layer
                    activeLayerObj = windLayer;
                }
                
                if (activeLayerObj) {
                    // EXACT same as wind2.html
                    const d = activeLayerObj.getAnimationTimeDate();
                    const timeDisplay = document.getElementById('timeDisplay');
                    const timelineSlider = document.getElementById('timelineSlider');
                    
                    console.log('RefreshTime called:', activeLayer, d);
                    
                    if (d && timeDisplay) {
                        timeDisplay.textContent = d.toLocaleString('vi-VN');
                    }
                    
                    // EXACT same as wind2.html: timeSlider.value = +d;
                    if (d && timelineSlider) {
                        timelineSlider.value = +d;
                    }
                }
            } catch (error) {
                console.error('RefreshTime error:', error);
            }
        }
        
        // Setup timeline for specific layer (wind2.html style)
        function setupTimelineForActiveLayer(layer) {
            try {
                const timelineSlider = document.getElementById('timelineSlider');
                if (!layer || !timelineSlider) return;
                
                const startDate = layer.getAnimationStartDate();
                const endDate = layer.getAnimationEndDate();
                const currentDate = layer.getAnimationTimeDate();
                
                if (startDate && endDate && currentDate) {
                    timelineSlider.min = +startDate;
                    timelineSlider.max = +endDate;
                    timelineSlider.value = +currentDate;
                    
                    // Update display
                    refreshTime();
                }
            } catch (error) {
                console.error('Setup timeline for active layer error:', error);
            }
        }
        
        // Setup timeline with CURRENT time (not end time) - FIX cho default
        function setupTimelineWithCurrentTime(layer) {
            try {
                const timelineSlider = document.getElementById('timelineSlider');
                if (!layer || !timelineSlider) return;
                
                const startDate = layer.getAnimationStartDate();
                const endDate = layer.getAnimationEndDate();
                
                if (startDate && endDate) {
                    timelineSlider.min = +startDate;
                    timelineSlider.max = +endDate;
                    
                    // Set to CURRENT time (gần nhất với now), không phải end time
                    const now = new Date();
                    let currentTime;
                    
                    if (now < startDate) {
                        currentTime = +startDate;
                    } else if (now > endDate) {
                        currentTime = +endDate;
                    } else {
                        currentTime = +now;
                    }
                    
                    timelineSlider.value = currentTime;
                    
                    // Set layer time to current time
                    layer.setAnimationTime(parseInt(currentTime / 1000));
                    
                    console.log('Timeline setup with current time:', new Date(currentTime));
                    
                    // Update display
                    refreshTime();
                }
            } catch (error) {
                console.error('Setup timeline with current time error:', error);
            }
        }
        
        // Keep updateTimeDisplay for backward compatibility
        function updateTimeDisplay() {
            refreshTime();
        }
        
        // Stop radar animation
        function stopRadarAnimation() {
            if (radarAnimationTimer) {
                clearInterval(radarAnimationTimer);
                radarAnimationTimer = null;
            }
        }
        
        // Start radar animation
        function startRadarAnimation() {
            // For MapTiler radar layer, use the built-in animation
            if (radarLayer) {
                radarLayer.animateByFactor(animationSpeed);
            }
        }
        
        // Sync timeline with active layer - SIMPLE VERSION
        function syncTimelineWithActiveLayer() {
            try {
                const activeLayer = Array.from(activeLayers)[0];
                console.log('Syncing timeline with:', activeLayer);
                
                if (!activeLayer) return;
                
                let layer = null;
                if (activeLayer === 'wind') layer = windLayer;
                else if (activeLayer === 'temperature') layer = temperatureLayer;
                else if (activeLayer === 'precipitation') layer = precipitationLayer;
                else if (activeLayer === 'pressure') layer = pressureLayer;
                else if (activeLayer === 'radar') layer = radarLayer;
                
                if (layer && layer.getAnimationStartDate) {
                    // Wait a bit for layer to be fully ready
                    setTimeout(() => {
                        setupTimelineControls(layer);
                        showStatus(`📅 Timeline synced with ${activeLayer}`, 'success', 1500);
                    }, 500);
                }
            } catch (error) {
                console.error('Timeline sync error:', error);
            }
        }
        
        // Update hover tooltip - follow mouse cursor
        function updateHoverTooltip(lngLat, point) {
            try {
                if (!lngLat || !point) return;
                
                const tooltip = document.getElementById('hoverTooltip');
                if (!tooltip) {
                    console.warn('hoverTooltip element not found');
                    return;
                }
                
                // Get ONLY the active layer like wind2.html
                const activeLayer = Array.from(activeLayers)[0];
                let dataText = '';
                
                console.log('UpdateHoverTooltip for active layer:', activeLayer);
                console.log('Available layers:', {
                    wind: !!windLayer,
                    temperature: !!temperatureLayer, 
                    precipitation: !!precipitationLayer,
                    pressure: !!pressureLayer,
                    radar: !!radarLayer
                });
                
                // Make sure layer is added to map before pickAt
                let layerToCheck = null;
                if (activeLayer === 'wind' && windLayer) {
                    layerToCheck = windLayer;
                    // Ensure wind layer is added to map
                    if (!addedLayers.has('wind')) {
                        console.log('Adding wind layer to map for data access...');
                        map.addLayer(windLayer, 'Water');
                        addedLayers.add('wind');
                    }
                } else if (activeLayer === 'temperature' && temperatureLayer) {
                    layerToCheck = temperatureLayer;
                    if (!addedLayers.has('temperature')) {
                        console.log('Adding temperature layer to map for data access...');
                        map.addLayer(temperatureLayer, 'Water');
                        addedLayers.add('temperature');
                    }
                } else if (activeLayer === 'precipitation' && precipitationLayer) {
                    layerToCheck = precipitationLayer;
                    if (!addedLayers.has('precipitation')) {
                        console.log('Adding precipitation layer to map for data access...');
                        map.addLayer(precipitationLayer, 'Water');
                        addedLayers.add('precipitation');
                    }
                } else if (activeLayer === 'pressure' && pressureLayer) {
                    layerToCheck = pressureLayer;
                    if (!addedLayers.has('pressure')) {
                        console.log('Adding pressure layer to map for data access...');
                        map.addLayer(pressureLayer, 'Water');
                        addedLayers.add('pressure');
                    }
                } else if (activeLayer === 'radar' && radarLayer) {
                    layerToCheck = radarLayer;
                    if (!addedLayers.has('radar')) {
                        console.log('Adding radar layer to map for data access...');
                        map.addLayer(radarLayer, 'Water');
                        addedLayers.add('radar');
                    }
                }
                
                if (layerToCheck) {
                    try {
                        console.log(`Trying pickAt for ${activeLayer} at:`, lngLat.lng, lngLat.lat);
                        const value = layerToCheck.pickAt(lngLat.lng, lngLat.lat);
                        console.log(`${activeLayer} pickAt result:`, value);
                        
                        if (activeLayer === 'wind' && value && value.speedMetersPerSecond !== undefined) {
                            const convertedValue = convertValue(value.speedMetersPerSecond, 'wind', currentUnits.wind);
                            const unit = getCurrentUnit('wind');
                            dataText = `💨 ${convertedValue.toFixed(1)} ${unit}`;
                        } else if (activeLayer === 'temperature' && value && (value.celsius !== undefined || value.value !== undefined)) {
                            const tempValue = value.celsius !== undefined ? value.celsius : value.value;
                            const convertedValue = convertValue(tempValue, 'temperature', currentUnits.temperature);
                            const unit = getCurrentUnit('temperature');
                            dataText = `🌡️ ${convertedValue.toFixed(1)}${unit}`;
                        } else if (activeLayer === 'precipitation' && value && (value.millimeters !== undefined || value.value !== undefined)) {
                            const precipValue = value.millimeters !== undefined ? value.millimeters : value.value;
                            const convertedValue = convertValue(precipValue, 'precipitation', currentUnits.precipitation);
                            const unit = getCurrentUnit('precipitation');
                            dataText = `🌧️ ${convertedValue.toFixed(1)} ${unit}`;
                        } else if (activeLayer === 'pressure' && value && (value.hectoPascals !== undefined || value.value !== undefined)) {
                            const pressureValue = value.hectoPascals !== undefined ? value.hectoPascals : value.value;
                            const convertedValue = convertValue(pressureValue, 'pressure', currentUnits.pressure);
                            const unit = getCurrentUnit('pressure');
                            dataText = `🌀 ${convertedValue.toFixed(0)} ${unit}`;
                        } else if (activeLayer === 'radar' && value && (value.dbz !== undefined || value.value !== undefined)) {
                            const radarValue = value.dbz !== undefined ? value.dbz : value.value;
                            const convertedValue = convertValue(radarValue, 'radar', currentUnits.radar);
                            const unit = getCurrentUnit('radar');
                            dataText = `📡 ${convertedValue.toFixed(1)} ${unit}`;
                        }
                        
                        if (!dataText) {
                            console.log(`No valid data found for ${activeLayer}:`, value);
                        }
                        
                    } catch (e) {
                        console.warn(`${activeLayer} data error:`, e);
                    }
                }
                
                if (dataText) {
                    tooltip.textContent = dataText;
                    tooltip.style.left = point.x + 'px';
                    tooltip.style.top = point.y + 'px';
                    tooltip.classList.add('visible');
                    console.log('Tooltip data set to:', dataText, 'at', point.x, point.y);
                } else {
                    tooltip.classList.remove('visible');
                }
                
            } catch (error) {
                console.error('Hover tooltip update error:', error);
            }
        }
        
        // Hide hover tooltip
        function hideHoverTooltip() {
            const tooltip = document.getElementById('hoverTooltip');
            if (tooltip) {
                tooltip.classList.remove('visible');
            }
        }


        
        // Update map info display
        function updateMapInfo() {
            if (!map) return;
            
            const center = map.getCenter();
            const zoom = map.getZoom();
            
            // Update current layer display if element exists
            const currentLayerEl = document.getElementById('currentLayer');
            if (currentLayerEl && activeLayers.size > 0) {
                const activeLayer = Array.from(activeLayers)[0];
                currentLayerEl.textContent = activeLayer.charAt(0).toUpperCase() + activeLayer.slice(1);
            } else if (currentLayerEl) {
                currentLayerEl.textContent = 'No layer selected';
            }
            
            // Console log for debugging (since map info panel is removed)
            // console.log(`Map center: ${center.lat.toFixed(4)}, ${center.lng.toFixed(4)}, Zoom: ${zoom.toFixed(2)}`);
        }
        
        // Hide all weather layers
        function hideAllLayers() {
            if (windLayer && addedLayers.has('wind') && windLayer.setOpacity) {
                windLayer.setOpacity(0);
            }
            if (temperatureLayer && addedLayers.has('temperature') && temperatureLayer.setOpacity) {
                temperatureLayer.setOpacity(0);
            }
            if (precipitationLayer && addedLayers.has('precipitation') && precipitationLayer.setOpacity) {
                precipitationLayer.setOpacity(0);
            }
            if (pressureLayer && addedLayers.has('pressure') && pressureLayer.setOpacity) {
                pressureLayer.setOpacity(0);
            }
            if (radarLayer && addedLayers.has('radar') && radarLayer.setOpacity) {
                radarLayer.setOpacity(0);
            }
        }
        
        // Generate legend values like MapTiler - Exact Implementation
        function generateLegendValues(layerType, currentUnit) {
            console.log('Generating legend values for:', layerType, 'unit:', currentUnit);
            
            const config = legendConfigs[layerType];
            if (!config) return config?.values || [];
            
            let values = [...config.values]; // Copy original values
            
            // Apply unit conversion like MapTiler
            if (config.units) {
                if (layerType === 'temperature' && currentUnit === 'fahrenheit') {
                    values = values.map(tempC => {
                        unitConverter.celsius = tempC;
                        return Math.round(unitConverter.fahrenheit / 10) * 10; // Round to 10s like MapTiler
                    });
                } else if (layerType === 'wind' && currentUnit !== 'speedMetersPerSecond') {
                    values = values.map(speedMs => {
                        unitConverter.metersPerSecond = speedMs;
                        let convertedValue;
                        switch(currentUnit) {
                            case 'speedKilometersPerHour': convertedValue = unitConverter.kilometersPerHour; break;
                            case 'speedMilesPerHour': convertedValue = unitConverter.milesPerHour; break;
                            case 'speedFeetPerSecond': convertedValue = unitConverter.feetPerSecond; break;
                            case 'speedKnots': convertedValue = unitConverter.knots; break;
                            default: convertedValue = speedMs;
                        }
                        return Math.round(convertedValue / 10) * 10; // Round to 10s like MapTiler
                    });
                } else if (layerType === 'precipitation' && currentUnit !== 'millimetersPerHour') {
                    values = values.map(mmph => {
                        unitConverter.millimetersPerHour = mmph;
                        let convertedValue;
                        switch(currentUnit) {
                            case 'inchesPerHour': convertedValue = unitConverter.inchesPerHour; break;
                            default: convertedValue = mmph;
                        }
                        return Math.round(convertedValue * 100) / 100; // Round to 2 decimals for precipitation
                    });
                } else if (layerType === 'pressure' && currentUnit !== 'hectopascals') {
                    values = values.map(hpa => {
                        unitConverter.hectopascals = hpa;
                        let convertedValue;
                        switch(currentUnit) {
                            case 'millibars': convertedValue = unitConverter.millibars; break;
                            case 'inchesOfMercury': convertedValue = unitConverter.inchesOfMercury; break;
                            case 'kiloPascals': convertedValue = unitConverter.kiloPascals; break;
                            default: convertedValue = hpa;
                        }
                        return Math.round(convertedValue * 100) / 100; // Round to 2 decimals for pressure
                    });
                } else if (layerType === 'radar' && currentUnit !== 'decibelsZ') {
                    values = values.map(dbz => {
                        switch(currentUnit) {
                            case 'millimetersPerHour':
                                return Math.round(unitConverter.convertRadarToRainRate(dbz) * 10) / 10;
                            case 'rainIntensity':
                                return unitConverter.convertRadarToIntensity(dbz);
                            default: 
                                return dbz;
                        }
                    });
                }
            }
            
            return values;
        }
        
        // Update legend scale based on active layer - EXACTLY like MapTiler 
        function updateLegendScale(layerType) {
            try {
                const legendScale = document.getElementById('legendScale');
                const unitsToggle = document.getElementById('unitsToggle');
                
                if (!legendScale || !unitsToggle) return;
                
                // Hide all scales first
                document.querySelectorAll('.scale-gradient, .scale-labels').forEach(el => {
                    el.style.display = 'none';
                });
                
                // Get actual layer and its colorramp - EXACTLY like MapTiler does
                let actualLayer = null;
                let scaleType = layerType;
                
                // Map aliases 
                if (layerType === 'isobar') scaleType = 'pressure';
                if (layerType === 'arrow') scaleType = 'wind';
                
                // Get the actual weather layer object
                switch(scaleType) {
                    case 'wind': actualLayer = windLayer; break;
                    case 'temperature': actualLayer = temperatureLayer; break;
                    case 'precipitation': actualLayer = precipitationLayer; break;
                    case 'pressure': actualLayer = pressureLayer; break;
                    case 'radar': actualLayer = radarLayer; break;
                }
                
                console.log('🔍 Debug actualLayer:', scaleType, {
                    actualLayer: !!actualLayer,
                    hasGetColorRamp: !!(actualLayer && actualLayer.getColorRamp),
                    layerConstructor: actualLayer?.constructor?.name
                });
                
                if (actualLayer && actualLayer.getColorRamp) {
                    try {
                        // Get the ACTUAL colorramp from layer - just like MapTiler's I(e) function
                        const actualColorramp = actualLayer.getColorRamp();
                        console.log('🎨 Got actualColorramp:', !!actualColorramp);
                        
                        if (actualColorramp && actualColorramp.getCanvasStrip) {
                            // Create color gradient canvas from actual colorramp 
                            const canvas = actualColorramp.getCanvasStrip({
                                horizontal: false,
                                size: 8
                            });
                            console.log('📊 Created canvas:', canvas.width, 'x', canvas.height);
                            
                            canvas.style.width = '37px';
                            canvas.style.height = '240px'; // Đúng chiều cao như thang cũ
                            canvas.style.borderRadius = '4px';
                            canvas.style.border = 'none';
                            canvas.style.outline = '1px solid rgba(255,255,255,0.5)';
                            // Radar cần rotate ngược lại để đúng thứ tự
                            canvas.style.transform = (scaleType === 'radar') ? 'rotate(180deg)' : 'rotate(180deg)';
                            canvas.style.backgroundColor = 'white';
                            
                            // Get values exactly like MapTiler does in function I(e)
                            const values = getMapTilerLegendValues(scaleType);
                            
                            // Update legend with actual colorramp
                            updateLegendWithActualColorramp(scaleType, canvas, values);
                            
                            legendScale.classList.add('visible');
                            showStatus(`🎨 Legend updated with actual ${scaleType} colorramp`, 'info', 1500);
                            return;
                        } else {
                            console.warn('❌ actualColorramp.getCanvasStrip not available');
                        }
                    } catch (error) {
                        console.error('❌ Error getting colorramp:', error);
                    }
                } else {
                    console.warn('❌ Layer or getColorRamp not available for', scaleType);
                }
                
                // Fallback: Create colorramp manually if layer method failed
                console.warn('🔄 Creating manual colorramp for', scaleType);
                let fallbackColorramp;
                try {
                    switch (scaleType) {
                        case 'wind':
                            fallbackColorramp = maptilerweather.ColorRamp.builtin.VIRIDIS.scale(0, 40);
                            break;
                        case 'temperature':
                            fallbackColorramp = maptilerweather.ColorRamp.builtin.TEMPERATURE_2.scale(-50, 50);
                            break;
                        case 'precipitation':
                            fallbackColorramp = maptilerweather.ColorRamp.builtin.PRECIPITATION;
                            break;
                        case 'pressure':
                            fallbackColorramp = maptilerweather.ColorRamp.builtin.PRESSURE_2;
                            break;
                        case 'radar':
                            fallbackColorramp = maptilerweather.ColorRamp.builtin.RADAR;
                            break;
                    }
                    
                    if (fallbackColorramp && fallbackColorramp.getCanvasStrip) {
                        const canvas = fallbackColorramp.getCanvasStrip({
                            horizontal: false,
                            size: 8
                        });
                        console.log('🎯 Created fallback canvas:', canvas.width, 'x', canvas.height);
                        
                        canvas.style.width = '37px';
                        canvas.style.height = '240px';
                        canvas.style.borderRadius = '4px';
                        canvas.style.border = 'none';
                        canvas.style.outline = '1px solid rgba(255,255,255,0.5)';
                        canvas.style.transform = (scaleType === 'radar') ? 'rotate(180deg)' : 'rotate(180deg)';
                        canvas.style.backgroundColor = 'white';
                        
                        const values = getMapTilerLegendValues(scaleType);
                        updateLegendWithActualColorramp(scaleType, canvas, values);
                        
                        legendScale.classList.add('visible');
                        showStatus(`🎨 Manual colorramp created for ${scaleType}`, 'info', 1500);
                        return;
                    }
                } catch (fallbackError) {
                    console.error('❌ Fallback colorramp failed:', fallbackError);
                }
                
                // Last resort: disable legend
                console.warn(`❌ All colorramp methods failed for ${scaleType}`);
                showStatus(`⚠️ Could not create legend for ${scaleType}`, 'warning', 2000);
                
            } catch (error) {
                console.error('UpdateLegendScale error:', error);
                // Use fallback
                updateLegendScaleFallback(layerType);
            }
        }
        
        // Get MapTiler values exactly like their I(e) function
        function getMapTilerLegendValues(scaleType) {
            // EXACT same logic as MapTiler's function I(e) 
            switch(scaleType) {
                case 'temperature':
                    return [40, 20, 10, 0, -10, -20, -40];
                case 'precipitation':
                case 'wind': 
                    return [40, 30, 20, 15, 10, 5, 0];
                case 'pressure':
                    return [1050, 1030, 1010, 990, 970, 950, 930];
                case 'radar':
                    return [60, 50, 40, 25, 15, 10, 0];
                default:
                    return [40, 30, 20, 15, 10, 5, 0];
            }
        }
        
        // Update legend with actual colorramp from layer
        function updateLegendWithActualColorramp(layerType, canvas, values) {
            const legendScale = document.getElementById('legendScale');
            const unitsToggle = document.getElementById('unitsToggle');
            
            // XÓA HOÀN TOÀN tất cả thang cũ - trước tiên clear hết scaleBar
            const scaleBar = document.getElementById('scaleBar');
            if (scaleBar) {
                scaleBar.innerHTML = ''; // XÓA HẾT thang cũ
            }
            
            // Tạo mới hoàn toàn container cho colorramp
            const scaleContainer = document.createElement('div');
            scaleContainer.style.position = 'relative';
            scaleContainer.style.display = 'flex';
            scaleContainer.style.justifyContent = 'center';
            scaleContainer.id = layerType + 'Scale';
            
            // Thêm canvas vào container
            scaleContainer.appendChild(canvas);
            
            // Tạo labels container với position absolute để overlay trên canvas
            const labelsContainer = document.createElement('div');
            labelsContainer.style.position = 'absolute';
            labelsContainer.style.left = '0';
            labelsContainer.style.top = '0';
            labelsContainer.style.width = '37px';
            labelsContainer.style.height = '240px';
            labelsContainer.style.color = 'white';
            labelsContainer.style.fontSize = '10px';
            labelsContainer.style.fontWeight = '600';
            labelsContainer.style.textShadow = '0px 0px 3px rgba(0,0,0,0.8), 1px 1px 0px rgba(0,0,0,0.5)';
            labelsContainer.style.zIndex = '900';
            labelsContainer.style.pointerEvents = 'none';
            labelsContainer.id = layerType + 'Labels';
            
            // Update labels with converted values
            const config = legendConfigs[layerType];
            const currentUnitKey = currentUnits[layerType] || (config && config.defaultUnit) || 'default';
            const convertedValues = values.map(val => {
                // Special handling for radar intensity unit
                if (layerType === 'radar' && currentUnitKey === 'rainIntensity') {
                    return unitConverter.convertRadarToIntensity(val);
                }
                const converted = convertValue(val, layerType, currentUnitKey);
                return Math.round(converted * 10) / 10; // Round to 1 decimal like MapTiler
            });
            
            // Tạo spans với position absolute - ĐÚNG như MapTiler
            convertedValues.forEach((value, index) => {
                const span = document.createElement('span');
                span.textContent = value;
                span.style.position = 'absolute';
                span.style.left = '50%';
                span.style.transform = 'translateX(-50%)';
                span.style.lineHeight = '1';
                span.style.textAlign = 'center';
                
                // Tính toán position ĐÚNG như MapTiler - text KHÔNG ở tận cạnh
                // Text cách cạnh trên/dưới một khoảng, thang màu thực tế mở rộng hơn
                const availableHeight = 240;
                const textPadding = 20; // Text cách cạnh 15px như MapTiler
                const usableHeight = availableHeight - (textPadding * 2); // 210px cho text
                const spacing = convertedValues.length > 1 ? usableHeight / (convertedValues.length - 1) : 0;
                const topPosition = textPadding + (index * spacing);
                span.style.top = topPosition + 'px';
                
                labelsContainer.appendChild(span);
            });
            
            // Thêm labels container vào scale container
            scaleContainer.appendChild(labelsContainer);
            
            // Thêm scale container vào scaleBar
            if (scaleBar) {
                scaleBar.appendChild(scaleContainer);
            }
            
            // Update units button
            const unitText = config && config.units ? 
                config.units[currentUnitKey] : 
                getCurrentUnit(layerType);
            unitsToggle.textContent = unitText;
            
            // Enable/disable units toggle
            if (config && config.units && Object.keys(config.units).length > 1) {
                unitsToggle.classList.remove('disabled');
                unitsToggle.style.opacity = '1';
            } else {
                unitsToggle.classList.add('disabled'); 
                unitsToggle.style.opacity = '0.6';
            }
        }
        
        // Fallback method if can't get actual colorramp - XÓA HẾT vì không cần nữa
        function updateLegendScaleFallback(layerType) {
            showStatus('⚠️ Could not get actual colorramp, using fallback disabled', 'warning', 2000);
            console.warn('Fallback legend method disabled - should use actual colorramp only');
        }
        
        // MapTiler Style Units State - Default wind to m/s like MapTiler
        let currentUnits = {
            temperature: 'celsius',
            wind: 'speedMetersPerSecond', // Change to m/s như MapTiler default
            precipitation: 'millimetersPerHour',
            pressure: 'hectopascals',
            radar: 'decibelsZ'
        };
        
        // MapTiler Style Value Conversion
        function convertValue(value, layerType, unitKey) {
            if (!value) return null;
            
            const config = legendConfigs[layerType];
            if (!config || !config.units) return value;
            
            switch(layerType) {
                case 'temperature':
                    if (unitKey === 'fahrenheit') {
                        unitConverter.celsius = value;
                        return unitConverter.fahrenheit;
                    }
                    return value; // celsius
                    
                case 'wind':
                    unitConverter.metersPerSecond = value;
                    switch(unitKey) {
                        case 'speedKilometersPerHour': return unitConverter.kilometersPerHour;
                        case 'speedMilesPerHour': return unitConverter.milesPerHour;
                        case 'speedFeetPerSecond': return unitConverter.feetPerSecond;
                        case 'speedKnots': return unitConverter.knots;
                        default: return value;
                    }
                    
                case 'precipitation':
                    unitConverter.millimetersPerHour = value;
                    switch(unitKey) {
                        case 'inchesPerHour': return unitConverter.inchesPerHour;
                        default: return value;
                    }
                    
                case 'pressure':
                    unitConverter.hectopascals = value;
                    switch(unitKey) {
                        case 'millibars': return unitConverter.millibars;
                        case 'inchesOfMercury': return unitConverter.inchesOfMercury;
                        case 'kiloPascals': return unitConverter.kiloPascals;
                        default: return value;
                    }
                    
                case 'radar':
                    switch(unitKey) {
                        case 'millimetersPerHour': return unitConverter.convertRadarToRainRate(value);
                        case 'rainIntensity': return unitConverter.convertRadarToIntensity(value);
                        default: return value;
                    }
                    
                default:
                    return value;
            }
        }
        
        // Get current unit string for layer - MapTiler Style
        function getCurrentUnit(layerType) {
            const config = legendConfigs[layerType];
            if (!config) return '';
            
            if (config.units) {
                const currentUnitKey = currentUnits[layerType];
                return config.units[currentUnitKey] || Object.values(config.units)[0];
            }
            
            return config.unit || '';
        }
        
        // Setup units toggle - MapTiler Style
        function setupUnitsToggle() {
            const unitsToggle = document.getElementById('unitsToggle');
            
            unitsToggle.addEventListener('click', () => {
                // Only allow toggle if button is not disabled
                if (unitsToggle.classList.contains('disabled')) {
                    return;
                }
                
                const activeLayer = Array.from(activeLayers)[0];
                const config = legendConfigs[activeLayer];
                
                if (activeLayer && config && config.units && Object.keys(config.units).length > 1) {
                    const unitKeys = Object.keys(config.units);
                    const currentIndex = unitKeys.indexOf(currentUnits[activeLayer]);
                    const nextIndex = (currentIndex + 1) % unitKeys.length;
                    const nextUnitKey = unitKeys[nextIndex];
                    
                    currentUnits[activeLayer] = nextUnitKey;
                    unitsToggle.textContent = config.units[nextUnitKey];
                    
                    // Update legend values for new unit
                    updateLegendScale(activeLayer);
                    
                    showStatus(`📊 Units: ${unitsToggle.textContent}`, 'info', 1500);
                }
            });
        }
        
        // Toggle weather layer (always show, never hide in new single-selection mode)
        function toggleWeatherLayer(layerType, isActive) {
            try {
                showStatus(`✅ Đã chọn lớp ${layerType}`, 'success', 2000);
                
                // Show the selected layer
                switch(layerType) {
                    case 'wind':
                        if (windLayer && map) {
                            if (!addedLayers.has('wind')) {
                                map.addLayer(windLayer, 'Water');
                                addedLayers.add('wind');
                            }
                            if (windLayer.setOpacity) {
                                windLayer.setOpacity(0.75);
                            }
                        }
                        break;
                    case 'temperature':
                        if (temperatureLayer && map) {
                            if (!addedLayers.has('temperature')) {
                                map.addLayer(temperatureLayer, 'Water');
                                addedLayers.add('temperature');
                            }
                            if (temperatureLayer.setOpacity) {
                                temperatureLayer.setOpacity(0.75);
                            }
                        }
                        break;
                    case 'precipitation':
                        if (precipitationLayer && map) {
                            if (!addedLayers.has('precipitation')) {
                                map.addLayer(precipitationLayer, 'Water');
                                addedLayers.add('precipitation');
                            }
                            if (precipitationLayer.setOpacity) {
                                precipitationLayer.setOpacity(0.75);
                            }
                        }
                        break;
                    case 'pressure':
                        if (pressureLayer && map) {
                            if (!addedLayers.has('pressure')) {
                                map.addLayer(pressureLayer, 'Water');
                                addedLayers.add('pressure');
                            }
                            if (pressureLayer.setOpacity) {
                                pressureLayer.setOpacity(0.75);
                            }
                        }
                        break;
                    case 'radar':
                        if (radarLayer && map) {
                            if (!addedLayers.has('radar')) {
                                map.addLayer(radarLayer, 'Water');
                                addedLayers.add('radar');
                            }
                            if (radarLayer.setOpacity) {
                                radarLayer.setOpacity(0.75);
                            }
                            showStatus('📡 MapTiler radar activated', 'success', 2000);
                        }
                        break;
                    case 'isobar':
                        // Placeholder for isobar layer
                        showStatus('⏲ Isobar layer (PLUS feature)', 'info', 2000);
                        break;
                    case 'arrow':
                        // Placeholder for arrow layer
                        showStatus('💨 Arrow layer (PLUS feature)', 'info', 2000);
                        break;
                }
                
                updateMapInfo();
                
            } catch (error) {
                showStatus(`❌ Lỗi toggle layer: ${error.message}`, 'error');
                console.error('Layer toggle error:', error);
            }
        }
        
        // Check if mobile screen (MapTiler style)
        function isMobile() {
            return window.matchMedia("(max-width: 575.98px)").matches;
        }
        
        // Setup layer controls
        function setupLayerControls() {
            const layerButtons = document.querySelectorAll('.layer-btn');
            
            layerButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const layerType = button.dataset.layer;
                    
                    // Mobile label animation (MapTiler style)
                    if (isMobile()) {
                        button.classList.add('show-label');
                        setTimeout(() => {
                            button.classList.remove('show-label');
                        }, 3000); // Show for 3 seconds
                    }
                    
                    // Remove active class from all buttons
                    layerButtons.forEach(btn => btn.classList.remove('active'));
                    
                    // Add active class to clicked button
                    button.classList.add('active');
                    
                    // Update active layers - clear and add new one
                    activeLayers.clear();
                    activeLayers.add(layerType);
                    
                    // Hide all layers first, then show selected one
                    hideAllLayers();
                    toggleWeatherLayer(layerType, true);
                    
                    // Update current layer display
                    document.getElementById('currentLayer').textContent = layerType.charAt(0).toUpperCase() + layerType.slice(1);
                    
                                    // Update legend
                updateLegendScale(layerType);
                
                // Update opacity control
                updateOpacityControl(layerType);
                
                // Update timeline to sync with new layer
                updateTimeDisplay();
                syncTimelineWithActiveLayer();
                });
            });
            
            // Initialize with wind layer (change from pressure to wind)  
            activeLayers.add('wind');
            
            // Delay để đảm bảo wind layer đã được tạo xong
            setTimeout(() => {
                // Debug: check wind layer status before update legend
                console.log('🌪️ Debug wind layer for legend (delayed):', {
                    windLayer: !!windLayer,
                    hasGetColorRamp: !!(windLayer && windLayer.getColorRamp),
                    layerConstructor: windLayer?.constructor?.name
                });
                
                updateLegendScale('wind');
                updateOpacityControl('wind');
            }, 500);
            
            // Update button states - remove active from pressure, add to wind
            document.querySelectorAll('.layer-btn').forEach(btn => btn.classList.remove('active'));
            const windButton = document.querySelector('[data-layer="wind"]');
            if (windButton) {
                windButton.classList.add('active');
            }
            
            // Add and setup initial wind layer
            setTimeout(() => {
                if (windLayer && map) {
                    if (!addedLayers.has('wind')) {
                        map.addLayer(windLayer, 'Water');
                        addedLayers.add('wind');
                    }
                    if (windLayer.setOpacity) {
                        windLayer.setOpacity(0.75);
                    }
                    
                    // Setup timeline với current time (không phải end time)
                    setupTimelineWithCurrentTime(windLayer);
                }
            }, 1000);
        }
        
        // Setup radar controls
        function setupRadarControls() {
            // Radar controls are now integrated into main animation controls
            // No separate radar buttons in new layout
            console.log('Radar controls integrated with main timeline');
        }
        
        // Setup animation controls
        function setupAnimationControls() {
            const playButton = document.getElementById('playButton');
            const timeStepSelect = document.getElementById('timeStepSelect');
            
            if (!playButton || !timeStepSelect) {
                console.warn('Animation control elements not found');
                return;
            }
            
            playButton.addEventListener('click', () => {
                isPlaying = !isPlaying;
                playButton.innerHTML = isPlaying ? '<i class="fas fa-pause"></i>' : '<i class="fas fa-play"></i>';
                
                if (isPlaying) {
                    showStatus(`▶️ Bắt đầu animation theo ${timeStep}`, 'info', 2000);
                    startCustomAnimation();
                } else {
                    showStatus('⏸️ Dừng animation', 'info', 2000);
                    stopCustomAnimation();
                }
            });
            
            timeStepSelect.addEventListener('change', (e) => {
                timeStep = e.target.value;
                showStatus(`⏱️ Thay đổi bước thời gian: ${timeStep}`, 'info', 1500);
                
                // Restart animation with new time step if playing
                if (isPlaying) {
                    stopCustomAnimation();
                    startCustomAnimation();
                }
            });
        }
        
        // Custom animation system with time steps
        function startCustomAnimation() {
            stopCustomAnimation(); // Clear any existing animation
            
            const activeLayer = Array.from(activeLayers)[0];
            if (!activeLayer) return;
            
            let activeLayerObj = getActiveLayerObject(activeLayer);
            if (!activeLayerObj) return;
            
            // Get time boundaries
            const startTime = activeLayerObj.getAnimationStartDate();
            const endTime = activeLayerObj.getAnimationEndDate();
            let currentTime = activeLayerObj.getAnimationTimeDate();
            
            if (!startTime || !endTime || !currentTime) return;
            
            // Calculate time increment based on step
            let timeIncrement;
            switch (timeStep) {
                case 'minute':
                    timeIncrement = 60 * 1000; // 1 minute
                    break;
                case 'hour':
                    timeIncrement = 60 * 60 * 1000; // 1 hour
                    break;
                case 'day':
                    timeIncrement = 24 * 60 * 60 * 1000; // 1 day
                    break;
                default:
                    timeIncrement = 60 * 60 * 1000; // Default 1 hour
            }
            
            // Animation interval
            animationInterval = setInterval(() => {
                if (!isPlaying) return;
                
                // Get current time
                currentTime = new Date(currentTime.getTime() + timeIncrement);
                
                // Check if reached end
                if (currentTime >= endTime) {
                    currentTime = new Date(startTime.getTime()); // Loop back to start
                }
                
                // Update active layer
                activeLayerObj = getActiveLayerObject(Array.from(activeLayers)[0]);
                if (activeLayerObj) {
                    activeLayerObj.setAnimationTime(+currentTime / 1000);
                }
            }, 1000); // Update every 1 second
        }
        
        function stopCustomAnimation() {
            if (animationInterval) {
                clearInterval(animationInterval);
                animationInterval = null;
            }
        }
        
        function getActiveLayerObject(layerType) {
            switch(layerType) {
                case 'wind': return windLayer;
                case 'temperature': return temperatureLayer;
                case 'precipitation': return precipitationLayer;
                case 'pressure': return pressureLayer;
                case 'radar': return radarLayer;
                default: return null;
            }
        }
        
        // Setup legend controls
        function setupLegendControls() {
            const legendToggle = document.getElementById('legendToggle');
            const legendContent = document.getElementById('legendContent');
            let isLegendCollapsed = false;
            
            legendToggle.addEventListener('click', () => {
                isLegendCollapsed = !isLegendCollapsed;
                
                if (isLegendCollapsed) {
                    legendContent.classList.add('collapsed');
                    legendToggle.textContent = '📈';
                    legendToggle.title = 'Show Legend';
                } else {
                    legendContent.classList.remove('collapsed');
                    legendToggle.textContent = '📊';
                    legendToggle.title = 'Hide Legend';
                }
            });
            
            // Initialize legend display
            updateLegendDisplay();
        }
        
        // Setup sidebar toggle
        function setupSidebarToggle() {
            const sidebar = document.getElementById('sidebar');
            const toggleButton = document.getElementById('sidebarToggle');
            
            toggleButton.addEventListener('click', () => {
                sidebar.classList.toggle('collapsed');
                toggleButton.textContent = sidebar.classList.contains('collapsed') ? '☰' : '✕';
            });
        }
        
        // Setup opacity controls
        function setupOpacityControls() {
            const opacityControl = document.getElementById('opacityControl');
            const opacitySlider = document.getElementById('opacitySlider');
            const opacityValue = document.getElementById('opacityValue');
            const opacityLayerName = document.getElementById('opacityLayerName');
            
            if (!opacitySlider || !opacityValue || !opacityLayerName) {
                console.warn('Opacity control elements not found');
                return;
            }
            
            // Update opacity when slider changes
            opacitySlider.addEventListener('input', (e) => {
                const opacity = parseInt(e.target.value) / 100;
                opacityValue.textContent = `${e.target.value}%`;
                
                // Apply opacity to active layer
                const activeLayer = Array.from(activeLayers)[0];
                if (activeLayer) {
                    setLayerOpacity(activeLayer, opacity);
                    showStatus(`🎨 ${activeLayer} opacity: ${e.target.value}%`, 'info', 1000);
                }
            });
        }
        
        // Set opacity for specific layer
        function setLayerOpacity(layerType, opacity) {
            try {
                switch(layerType) {
                    case 'wind':
                        if (windLayer && addedLayers.has('wind') && windLayer.setOpacity) {
                            windLayer.setOpacity(opacity);
                        }
                        break;
                    case 'temperature':
                        if (temperatureLayer && addedLayers.has('temperature') && temperatureLayer.setOpacity) {
                            temperatureLayer.setOpacity(opacity);
                        }
                        break;
                    case 'precipitation':
                        if (precipitationLayer && addedLayers.has('precipitation') && precipitationLayer.setOpacity) {
                            precipitationLayer.setOpacity(opacity);
                        }
                        break;
                    case 'pressure':
                        if (pressureLayer && addedLayers.has('pressure') && pressureLayer.setOpacity) {
                            pressureLayer.setOpacity(opacity);
                        }
                        break;
                    case 'radar':
                        if (radarLayer && addedLayers.has('radar') && radarLayer.setOpacity) {
                            radarLayer.setOpacity(opacity);
                        }
                        break;
                }
            } catch (error) {
                console.error('Error setting layer opacity:', error);
            }
        }
        
        // Update opacity control for active layer
        function updateOpacityControl(layerType) {
            const opacityLayerName = document.getElementById('opacityLayerName');
            const opacitySlider = document.getElementById('opacitySlider');
            const opacityValue = document.getElementById('opacityValue');
            
            if (!opacityLayerName) return;
            
            if (layerType) {
                // Update layer name in opacity control
                opacityLayerName.textContent = `${layerType.charAt(0).toUpperCase() + layerType.slice(1)} Opacity`;
                
                // Reset to 75% for new layer
                if (opacitySlider) {
                    opacitySlider.value = 75;
                }
                if (opacityValue) {
                    opacityValue.textContent = '75%';
                }
                
                // Apply 75% opacity immediately to the layer
                setLayerOpacity(layerType, 0.75);
            }
        }
        

        
        // Initialize everything
        async function init() {
            try {
                // Initialize map
                await initializeMap();
                
                // Setup UI controls
                setupLayerControls();
                setupAnimationControls();
                setupRadarControls();
                setupUnitsToggle();
                setupOpacityControls();
                
                showStatus('🚀 Weather App đã sẵn sàng!', 'success');
                
            } catch (error) {
                console.error('Failed to initialize app:', error);
                showStatus('❌ Không thể khởi tạo ứng dụng', 'error');
            }
        }
        
        // Start the application
        document.addEventListener('DOMContentLoaded', init);
        
        // Handle window resize
        window.addEventListener('resize', () => {
            if (map) {
                map.resize();
            }
        });
    </script>
</body>
</html> 