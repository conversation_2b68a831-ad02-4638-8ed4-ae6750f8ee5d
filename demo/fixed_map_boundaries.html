<!DOCTYPE html>
<html>
<head>
  <title>MapTiler Wind + Temperature (Fixed Boundaries)</title>
  <meta charset="utf-8" />
  <meta name="viewport" content="initial-scale=1,maximum-scale=1,user-scalable=no" />
  <script src="https://cdn.maptiler.com/maptiler-sdk-js/v3.2.0/maptiler-sdk.umd.min.js"></script>
  <link href="https://cdn.maptiler.com/maptiler-sdk-js/v3.2.0/maptiler-sdk.css" rel="stylesheet" />
  <script src="https://cdn.maptiler.com/maptiler-weather/v3.0.1/maptiler-weather.umd.min.js"></script>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: sans-serif;
    }

    #map {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 100%;
      background-color: #444952;
      background-image: url('night-stars.webp');
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
    }

    #time-info {
      position: fixed;
      width: 60vw;
      bottom: 0;
      z-index: 1;
      margin: 10px;
      text-shadow: 0px 0px 5px black;
      color: white;
      font-size: 20px;
      font-weight: 500;
      text-align: center;
      left: 0;
      right: 0;
      margin: auto;
      padding: 20px;
    }

    #time-slider {
      width: 100%;
      height: fit-content;
      left: 0;
      right: 0;
      z-index: 1;
      filter: drop-shadow(0 0 7px #000a);
      margin-top: 10px;
    }

    #pointer-data {
      z-index: 1;
      position: fixed;
      font-size: 50px;
      font-weight: 900;
      margin: 27px 0px 0px 10px;
      color: #fff;
      text-shadow: 0px 0px 10px #0007;
      white-space: pre;
    }

    #variable-name {
      z-index: 1;
      position: fixed;
      font-size: 20px;
      font-weight: 500;
      margin: 5px 0px 0px 10px;
      color: #fff;
      text-shadow: 0px 0px 10px #0007;
    }

    .button {
      cursor: pointer;
      width: auto;
      padding: 8px;
      border-radius: 3px;
      margin: 10px 0 0 0;
      font-size: 12px;
      text-align: center;
      color: #fff;
      background: #3174ff;
      font-family: sans-serif;
      font-weight: bold;
    }

    #color-scale-container {
      position: fixed;
      right: 30px;
      top: 50%;
      transform: translateY(-50%);
      z-index: 10;
      display: flex;
      flex-direction: column;
      align-items: center;
      background: rgba(0,0,0,0.15);
      border-radius: 10px;
      padding: 12px 8px;
      box-shadow: 0 2px 8px #0003;
    }
    #color-scale-canvas {
      display: block;
      width: 32px;
      height: 256px;
      border-radius: 8px;
      margin-bottom: 8px;
      margin-top: 8px;
    }
    .scale-label {
      color: #fff;
      font-size: 15px;
      font-weight: bold;
      text-shadow: 0 1px 4px #000a;
      margin: 0 0 2px 0;
      user-select: none;
    }
    .scale-label.min {
      margin-bottom: 4px;
    }
    .scale-label.max {
      margin-top: 4px;
    }
  </style>
</head>

<body>
  <div id="time-info">
    <span id="time-text"></span>
    <button id="play-pause-bt" class="button">Play 3600x</button>
    <button id="toggle-temp-bt" class="button">Ẩn nhiệt độ</button>
    <button id="auto-scale-bt" class="button">Cập nhật Auto-scale</button>
    <button id="hillshade-toggle-bt" class="button">Ẩn bóng địa hình</button>
    <input type="range" id="time-slider" min="0" max="11" step="1">
  </div>

  <div id="variable-name">Temperature + Wind</div>
  <div id="pointer-data"></div>
  <div id="map"></div>

  <!-- Color scale UI -->
  <div id="color-scale-container">
    <canvas id="color-scale-canvas" width="32" height="256"></canvas>
    <div class="scale-label max" id="scale-max-label">40°C</div>
    <div class="scale-label min" id="scale-min-label">0°C</div>
  </div>

  <!-- Status message -->
  <div id="status-message" style="position:fixed;right:30px;bottom:30px;z-index:100;color:#fff;background:rgba(0,0,0,0.7);padding:8px 16px;border-radius:8px;font-size:16px;display:none"></div>
  
  <script>
    // --- DOM Elements ---
    const timeInfoContainer = document.getElementById("time-info");
    const timeTextDiv = document.getElementById("time-text");
    const timeSlider = document.getElementById("time-slider");
    const playPauseButton = document.getElementById("play-pause-bt");
    const toggleTempButton = document.getElementById("toggle-temp-bt");
    const pointerDataDiv = document.getElementById("pointer-data");
    const colorScaleCanvas = document.getElementById('color-scale-canvas');
    const scaleMinLabel = document.getElementById('scale-min-label');
    const scaleMaxLabel = document.getElementById('scale-max-label');
    const autoScaleButton = document.getElementById('auto-scale-bt');
    const hillshadeToggleButton = document.getElementById('hillshade-toggle-bt');

    // --- State Variables ---
    let pointerLngLat = null;
    let tempLayerVisible = true;
    let isPlaying = false;
    let mapLoaded = false;
    let colorScale = [];
    const TEMP_LAYER_ID = 'temperature-layer';
    let hillshadeVisible = true;
    let weatherLayersAdded = false;

    // --- Tạo color scale mặc định ---
    colorScale = [
      { min: -40, max: -30, color: '#1e3a8a' },
      { min: -30, max: -20, color: '#3b82f6' },
      { min: -20, max: -10, color: '#06b6d4' },
      { min: -10, max: 0, color: '#22d3ee' },
      { min: 0, max: 5, color: '#34d399' },
      { min: 5, max: 10, color: '#84cc16' },
      { min: 10, max: 15, color: '#eab308' },
      { min: 15, max: 20, color: '#f59e0b' },
      { min: 20, max: 25, color: '#f97316' },
      { min: 25, max: 30, color: '#ef4444' },
      { min: 30, max: 35, color: '#dc2626' },
      { min: 35, max: 40, color: '#991b1b' }
    ];

    // --- MapTiler Initialization ---
    maptilersdk.config.apiKey = 'oinoatcrNmdCL1524DOl';

    const map = new maptilersdk.Map({
      container: document.getElementById('map'),
      hash: true,
      zoom: 2,
      center: [0, 40],
      style: maptilersdk.MapStyle.BACKDROP,
      projectionControl: true,
      projection: 'globe'
    });

    // --- Layer Initialization ---
    let layerBg;
    
    const layer = new maptilerweather.WindLayer({
      id: "Wind Particles",
      colorramp: maptilerweather.ColorRamp.builtin.NULL,
      speed: 0.002, fadeFactor: 0.03, maxAmount: 256, density: 400,
      color: [255, 0, 0, 30], fastColor: [0, 255, 0, 100],
    });

    // ===== HÀM CHÍNH ĐỂ ĐẢMBẢO RANH GIỚI LUÔN HIỂN THỊ =====
    function ensureBoundariesOnTop() {
        if (!map.isStyleLoaded()) return;

        const layers = map.getStyle().layers;
        console.log('All layers:', layers.map(l => ({ id: l.id, type: l.type })));
        
        // Tìm TẤT CẢ layer có thể là ranh giới hoặc nhãn
        const boundaryAndLabelLayers = layers.filter(layer => {
            const id = layer.id.toLowerCase();
            const type = layer.type;
            
            // Điều kiện mở rộng để bắt tất cả các loại ranh giới và nhãn
            return (
                // Các layer ranh giới
                id.includes('boundary') || 
                id.includes('admin') || 
                id.includes('border') || 
                id.includes('country') || 
                id.includes('state') || 
                id.includes('province') ||
                id.includes('outline') ||
                id.includes('stroke') ||
                // Các layer nhãn
                type === 'symbol' ||
                id.includes('label') || 
                id.includes('text') || 
                id.includes('place') ||
                id.includes('city') ||
                id.includes('town') ||
                // Các layer line có thể là ranh giới
                (type === 'line' && (
                    id.includes('line') || 
                    id.includes('admin') || 
                    id.includes('boundary')
                ))
            );
        });

        console.log('Boundary and label layers found:', boundaryAndLabelLayers.map(l => l.id));

        // Di chuyển tất cả lên trên cùng, giữ nguyên thứ tự tương đối
        boundaryAndLabelLayers.forEach(layer => {
            try {
                if (map.getLayer(layer.id)) {
                    // Xóa fill color cho các layer fill/polygon
                    if (layer.type === 'fill') {
                        map.setPaintProperty(layer.id, 'fill-opacity', 0);
                        map.setPaintProperty(layer.id, 'fill-color', 'transparent');
                        // Đảm bảo stroke hiển thị
                        if (map.getPaintProperty(layer.id, 'fill-outline-color')) {
                            map.setPaintProperty(layer.id, 'fill-outline-color', '#666666');
                        }
                        console.log(`Removed fill from layer ${layer.id}`);
                    }
                    
                    map.moveLayer(layer.id);
                    console.log(`Moved layer ${layer.id} to top`);
                }
            } catch (e) {
                console.log(`Failed to move layer ${layer.id}:`, e.message);
            }
        });
    }

    // Hàm thêm custom layers
    function addCustomLayers() {
        try {
            // 1. Thêm hillshade trước
            map.addSource('dem', {
                type: 'raster-dem',
                url: `https://api.maptiler.com/tiles/terrain-rgb/tiles.json?key=${maptilersdk.config.apiKey}`,
                tileSize: 256, 
                maxzoom: 12
            });

            map.addLayer({
                id: 'hillshade-shadows', 
                type: 'hillshade', 
                source: 'dem',
                layout: { visibility: hillshadeVisible ? 'visible' : 'none' },
                paint: { 
                    'hillshade-exaggeration': 0.65, 
                    'hillshade-illumination-direction': 315, 
                    'hillshade-highlight-color': 'rgba(0, 0, 0, 0)', 
                    'hillshade-shadow-color': '#252525' 
                }
            });
            
            map.addLayer({
                id: 'hillshade-highlights', 
                type: 'hillshade', 
                source: 'dem',
                layout: { visibility: hillshadeVisible ? 'visible' : 'none' },
                paint: { 
                    'hillshade-exaggeration': 0.65, 
                    'hillshade-illumination-direction': 315, 
                    'hillshade-shadow-color': 'rgba(0, 0, 0, 0)', 
                    'hillshade-highlight-color': '#FFFFFF', 
                    'hillshade-opacity': 0.2 
                }
            });

            // 2. Thêm temperature layer
            updateTemperatureColorRamp();

            // 3. Thêm wind layer
            map.addLayer(layer);

            // 4. QUAN TRỌNG: Đảm bảo ranh giới lên trên
            ensureBoundariesOnTop();

            mapLoaded = true;
            console.log('All custom layers added successfully');
            
        } catch (error) {
            console.error('Error adding custom layers:', error);
        }
    }

    // --- Map Events ---
    map.on('load', function () {
        console.log('Map loaded');
        try {
            // Xóa fill cho Water layer, chỉ giữ outline
            map.setPaintProperty("Water", 'fill-opacity', 0);
            map.setPaintProperty("Water", 'fill-outline-color', "#666666");
            
            // Xử lý các layer khác có thể có fill
            const layers = map.getStyle().layers;
            layers.forEach(layer => {
                if (layer.type === 'fill') {
                    const id = layer.id.toLowerCase();
                    // Kiểm tra nếu là layer ranh giới/admin
                    if (id.includes('admin') || id.includes('boundary') || id.includes('country') || id.includes('state')) {
                        try {
                            map.setPaintProperty(layer.id, 'fill-opacity', 0);
                            map.setPaintProperty(layer.id, 'fill-color', 'transparent');
                            console.log(`Removed fill from ${layer.id} on load`);
                        } catch (e) {
                            console.log(`Could not modify ${layer.id}:`, e.message);
                        }
                    }
                }
            });
        } catch (error) { 
            console.log('Layer styling error:', error); 
        }
    });

    // Sử dụng event 'idle' để đảm bảo map đã sẵn sàng hoàn toàn
    map.on('idle', function() {
        if (!weatherLayersAdded && map.isStyleLoaded()) {
            weatherLayersAdded = true;
            console.log('Map is idle and style loaded, adding custom layers...');
            
            // Delay nhỏ để đảm bảo style đã ổn định hoàn toàn
            setTimeout(() => {
                addCustomLayers();
            }, 100);
        }
    });

    // Event để đảm bảo ranh giới luôn hiển thị khi style thay đổi
    map.on('styledata', function() {
        if (mapLoaded) {
            console.log('Style changed, ensuring boundaries on top...');
            setTimeout(() => {
                ensureBoundariesOnTop();
                // Thêm step để xóa fill cho các layer mới load
                removeFillFromBoundaryLayers();
            }, 50);
        }
    });

    // Hàm riêng để xóa fill từ boundary layers
    function removeFillFromBoundaryLayers() {
        if (!map.isStyleLoaded()) return;
        
        const layers = map.getStyle().layers;
        layers.forEach(layer => {
            if (layer.type === 'fill') {
                const id = layer.id.toLowerCase();
                // Kiểm tra nếu là layer ranh giới/admin
                if (id.includes('admin') || id.includes('boundary') || 
                    id.includes('country') || id.includes('state') || 
                    id.includes('province') || id.includes('border')) {
                    try {
                        map.setPaintProperty(layer.id, 'fill-opacity', 0);
                        map.setPaintProperty(layer.id, 'fill-color', 'transparent');
                        // Đảm bảo outline hiển thị
                        if (map.getPaintProperty(layer.id, 'fill-outline-color')) {
                            map.setPaintProperty(layer.id, 'fill-outline-color', '#888888');
                        }
                        console.log(`Removed fill from boundary layer ${layer.id}`);
                    } catch (e) {
                        console.log(`Could not modify boundary layer ${layer.id}:`, e.message);
                    }
                }
            }
        });
    }

    layer.on("sourceReady", () => {
        console.log('Wind layer source ready');
        refreshTimeUI();
        const startDate = layer.getAnimationStartDate();
        const endDate = layer.getAnimationEndDate();
        timeSlider.min = +startDate;
        timeSlider.max = +endDate;
    });

    // --- UI Event Listeners ---
    timeSlider.addEventListener("input", () => {
      const time = parseInt(timeSlider.value);
      layer.setAnimationTime(time / 1000);
      if (layerBg) layerBg.setAnimationTime(time / 1000);
    });

    playPauseButton.addEventListener("click", () => {
      isPlaying = !isPlaying;
      if (isPlaying) {
        layer.animateByFactor(3600); 
        if (layerBg) layerBg.animateByFactor(3600);
        playPauseButton.innerText = "Pause";
      } else {
        layer.animateByFactor(0); 
        if (layerBg) layerBg.animateByFactor(0);
        playPauseButton.innerText = "Play 3600x";
      }
    });

    toggleTempButton.addEventListener("click", () => {
      tempLayerVisible = !tempLayerVisible;
      if (layerBg) {
        const visibility = tempLayerVisible ? 'visible' : 'none';
        map.setLayoutProperty(TEMP_LAYER_ID, 'visibility', visibility);
        toggleTempButton.innerText = tempLayerVisible ? "Ẩn nhiệt độ" : "Hiện nhiệt độ";
      }
    });

    autoScaleButton.style.display = 'none';

    hillshadeToggleButton.addEventListener('click', function() {
      hillshadeVisible = !hillshadeVisible;
      const visibility = hillshadeVisible ? 'visible' : 'none';
      if (map.getLayer('hillshade-shadows')) { 
        map.setLayoutProperty('hillshade-shadows', 'visibility', visibility); 
      }
      if (map.getLayer('hillshade-highlights')) { 
        map.setLayoutProperty('hillshade-highlights', 'visibility', visibility); 
      }
      hillshadeToggleButton.innerText = hillshadeVisible ? 'Ẩn bóng địa hình' : 'Hiện bóng địa hình';
    });

    map.on('mousemove', (e) => updatePointerValue(e.lngLat));
    timeInfoContainer.addEventListener("mouseenter", () => pointerDataDiv.innerText = "");
    
    // --- Helper Functions ---
    function updateTemperatureColorRamp() {
        if (!colorScale.length) return;
        
        // Remove existing temperature layer if present
        if (map.getLayer(TEMP_LAYER_ID)) {
            map.removeLayer(TEMP_LAYER_ID);
        }
        
        const ramp = colorScale.map(entry => ({ 
            value: entry.min, 
            color: hexToRgb(entry.color) 
        }));
        
        layerBg = new maptilerweather.TemperatureLayer({
            id: TEMP_LAYER_ID, 
            opacity: 0.7, // Giảm opacity để ranh giới dễ nhìn hơn
            colorramp: ramp,
        });
        
        // Thêm temperature layer vào vị trí thích hợp
        if (map.getLayer('hillshade-shadows')) {
            map.addLayer(layerBg, 'hillshade-shadows');
        } else {
            map.addLayer(layerBg);
        }
        
        // Đảm bảo ranh giới lên trên sau khi thêm temperature layer
        setTimeout(ensureBoundariesOnTop, 50);

        drawColorScale(ramp.map(r => r.color));
        scaleMinLabel.innerText = `${colorScale[0].min}°C`;
        scaleMaxLabel.innerText = `${colorScale[colorScale.length-1].max}°C`;
        
        console.log('Temperature layer updated');
    }

    function refreshTimeUI() {
      const d = layer.getAnimationTimeDate();
      if (d) {
        timeTextDiv.innerText = d.toString();
        timeSlider.value = +d;
      }
      updatePointerValue(pointerLngLat);
    }

    function updatePointerValue(lngLat) {
      if (!lngLat || !mapLoaded) return;
      if (!layerBg && tempLayerVisible) return;
      
      pointerLngLat = lngLat;
      const valueWind = layer.pickAt(lngLat.lng, lngLat.lat);
      let valueTemp = null;
      if(layerBg) {
        valueTemp = layerBg.pickAt(lngLat.lng, lngLat.lat);
      }

      let tempText = tempLayerVisible && valueTemp && valueTemp.value != null ? `${valueTemp.value.toFixed(1)}°C\n` : "";
      let windText = valueWind ? `${valueWind.speedKilometersPerHour.toFixed(1)} km/h` : "";
      
      pointerDataDiv.innerText = tempText + windText;
    }

    function hexToRgb(hex) {
      const v = hex.replace('#', '');
      return [parseInt(v.substring(0,2),16), parseInt(v.substring(2,4),16), parseInt(v.substring(4,6),16)];
    }

    function drawColorScale(colors) {
      const ctx = colorScaleCanvas.getContext('2d');
      ctx.clearRect(0, 0, colorScaleCanvas.width, colorScaleCanvas.height);
      for (let i = 0; i < colors.length; i++) {
        ctx.fillStyle = `rgb(${colors[i][0]},${colors[i][1]},${colors[i][2]})`;
        const y = colorScaleCanvas.height - (i + 1) * (colorScaleCanvas.height / colors.length);
        const h = colorScaleCanvas.height / colors.length + 1;
        ctx.fillRect(0, y, colorScaleCanvas.width, h);
      }
    }
  </script>
</body>
</html>