/**
 * Offline Scene Manager for Weather App
 */
class OfflineSceneManager {
  constructor() {
    this.db = null;
    this.isProcessing = false;
    this.tempTiles = new Map();
    
    // Constants
    this.MAX_SCENES = 5;
    this.SHORT_SAVE_TTL = 3 * 60 * 60 * 1000; // 3 hours
    this.TILE_SIZE_ESTIMATE = 50 * 1024; // 50KB
    
    this.init();
  }

  async init() {
    try {
      await this.initIndexedDB();
      await this.registerServiceWorker();
      await this.cleanupExpiredShortSaves();
      
      // Setup offline protocol để intercept tile requests
      this.setupOfflineProtocol();
      
      console.log('✅ Offline Scene Manager initialized');
    } catch (error) {
      console.error('❌ Failed to initialize Offline Scene Manager:', error);
    }
  }

  async initIndexedDB() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('WeatherOfflineDB', 4); // Tăng version

      request.onerror = () => reject(request.error);

      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = event.target.result;

        // Global tile pool (basic/terrain/border tiles)
        if (!db.objectStoreNames.contains('global_tile_pool')) {
          const globalStore = db.createObjectStore('global_tile_pool', { keyPath: 'url' });
          globalStore.createIndex('sceneId', 'sceneId', { multiEntry: true });
          globalStore.createIndex('timestamp', 'timestamp');
        }

        // Wind tiles store
        if (!db.objectStoreNames.contains('wind_tiles')) {
          const windStore = db.createObjectStore('wind_tiles', { keyPath: 'url' });
          windStore.createIndex('datasetId', 'datasetId');
          windStore.createIndex('sceneId', 'sceneId');
          windStore.createIndex('coordinates', ['zoom', 'x', 'y']);
        }

        // *** NEW: Temperature tiles store ***
        if (!db.objectStoreNames.contains('temperature_tiles')) {
          const tempStore = db.createObjectStore('temperature_tiles', { keyPath: 'url' });
          tempStore.createIndex('datasetId', 'datasetId');
          tempStore.createIndex('sceneId', 'sceneId');
          tempStore.createIndex('coordinates', ['zoom', 'x', 'y']);
        }

        // Scenes metadata
        if (!db.objectStoreNames.contains('scenes')) {
          const scenesStore = db.createObjectStore('scenes', { keyPath: 'id' });
          scenesStore.createIndex('type', 'type');
          scenesStore.createIndex('timestamp', 'timestamp');
        }
      };
    });
  }

  async registerServiceWorker() {
    if (!('serviceWorker' in navigator)) return;
    
    try {
      await navigator.serviceWorker.register('/demo/tile-cache-worker.js');
      console.log('✅ Service Worker registered');
    } catch (error) {
      console.warn('Service Worker failed:', error);
    }
  }

  async cleanupExpiredShortSaves() {
    if (!this.db) return;
    
    try {
      const transaction = this.db.transaction(['scenes'], 'readwrite');
      const store = transaction.objectStore('scenes');
      const index = store.index('type');
      const request = index.getAll('short');
      
      request.onsuccess = () => {
        const shortScenes = request.result;
        const now = Date.now();
        
        shortScenes.forEach(scene => {
          if (now - scene.timestamp > this.SHORT_SAVE_TTL) {
            console.log(`🗑️ Cleaning expired: ${scene.id}`);
            store.delete(scene.id);
            this.updateSceneButtonUI(scene.id, 'empty');
          }
        });
      };
    } catch (error) {
      console.error('Cleanup failed:', error);
    }
  }

  // Save scene with short save (metadata only)
  async saveShortScene(sceneId, sceneName, mapState) {
    if (!this.db) return false;
    
    try {
      const sceneData = {
        id: sceneId,
        type: 'short',
        name: sceneName,
        timestamp: Date.now(),
        mapState: mapState
      };

      const transaction = this.db.transaction(['scenes'], 'readwrite');
      const store = transaction.objectStore('scenes');
      await this.putInStore(store, sceneData);

      this.updateSceneButtonUI(sceneId, 'short', sceneName);
      
      // Add timeline marker
      if (window.timelineManager) {
        window.timelineManager.addSceneMarker(sceneId, 'short', mapState.time, sceneName);
      }
      
      this.showToast('Short save completed', 'success');
      return true;
    } catch (error) {
      console.error('Short save failed:', error);
      this.showToast('Save failed', 'error');
      return false;
    }
  }

  // Save scene with long save (metadata + tiles + wind data)
  async saveLongScene(sceneId, sceneName, mapState, map, activeLayers) {
    if (this.isProcessing || !this.db) return false;
    
    this.isProcessing = true;
    let cancelled = false;
    
    try {
      // *** NEW: INSPECT TẤT CẢ METADATA TRƯỚC ***
      console.log('\n🔍 ===== INSPECTING ALL LAYER METADATA =====');
      await this.inspectAllLayerMetadata(map);
      console.log('🔍 =========================================\n');
      
      // Get viewport tiles (basic, terrain, border only)
      const tileUrls = this.getViewportTiles(map, activeLayers.filter(l => l !== 'wind' && l !== 'temperature'));
      console.log(`📦 Processing ${tileUrls.length} basic tiles`);
      
      // *** NEW: Calculate wind tiles estimate ***
      const windTilesEstimate = this.estimateWindTiles(map);
      const totalTiles = tileUrls.length + windTilesEstimate;
      console.log(`🌪️ + ${windTilesEstimate} wind tiles = ${totalTiles} total`);

      // Check storage quota (including wind tiles)
      const hasQuota = await this.checkStorageQuota(totalTiles);
      if (!hasQuota) {
        this.showToast('Storage full - clear a scene', 'error');
        return false;
      }

      // Show modal với total tiles count
      const modal = this.showSaveModal(totalTiles, () => { cancelled = true; });
      
      // Process basic tiles
      let processed = 0;
      for (const tileUrl of tileUrls) {
        if (cancelled) break;
        
        await this.processTileForLongSave(tileUrl, sceneId);
        processed++;
        this.updateProgressModal(modal, processed, totalTiles, 'Basic tiles');
      }

      // *** NEW: Process wind tiles ***
      if (!cancelled) {
        this.updateProgressModalStatus(modal, 'Extracting wind dataset info...');
        
        const windDatasets = await this.extractWindDatasets();
        
        if (windDatasets && windDatasets.length > 0) {
          console.log(`🌪️ Found ${windDatasets.length} wind datasets:`, windDatasets);
          
          this.updateProgressModalStatus(modal, 'Downloading wind tiles...');
          const windSuccess = await this.downloadWindTilesWithDatasets(map, sceneId, windDatasets);
          
          if (windSuccess) {
            processed += windTilesEstimate;
            this.updateProgressModal(modal, processed, totalTiles, 'Wind tiles');
            console.log('✅ Wind tiles downloaded successfully');
          } else {
            console.warn('⚠️ Wind tiles download failed, continuing...');
          }
        } else {
          console.warn('⚠️ No wind datasets found, skipping wind tiles...');
        }
      }

      // *** NEW: Process temperature tiles ***
      if (!cancelled && activeLayers.includes('temperature')) {
        this.updateProgressModalStatus(modal, 'Extracting temperature dataset info...');
        
        const temperatureDatasets = await this.extractTemperatureDatasets();
        
        if (temperatureDatasets && temperatureDatasets.length > 0) {
          console.log(`🌡️ Found ${temperatureDatasets.length} temperature datasets:`, temperatureDatasets);
          
          this.updateProgressModalStatus(modal, 'Downloading temperature tiles...');
          const tempSuccess = await this.downloadTemperatureTilesWithDatasets(map, sceneId, temperatureDatasets);
          
          if (tempSuccess) {
            console.log('✅ Temperature tiles downloaded successfully');
          } else {
            console.warn('⚠️ Temperature tiles download failed, continuing...');
          }
        } else {
          console.warn('⚠️ No temperature datasets found, skipping temperature tiles...');
        }
      }

      if (cancelled) {
        await this.revertTempTiles(sceneId);
        this.hideSaveModal(modal);
        this.showToast('Save cancelled', 'info');
        return false;
      }

      // Finalize
      await this.finalizeLongSave(sceneId, sceneName, mapState, tileUrls);
      this.hideSaveModal(modal);
      this.updateSceneButtonUI(sceneId, 'long', sceneName);
      
      // Add timeline marker
      if (window.timelineManager) {
        window.timelineManager.addSceneMarker(sceneId, 'long', mapState.time, sceneName);
      }
      
      this.showToast('Long save completed (with weather data)', 'success');
      return true;

    } catch (error) {
      console.error('Long save failed:', error);
      this.showToast('Save failed', 'error');
      return false;
    } finally {
      this.tempTiles.clear();
      this.isProcessing = false;
    }
  }

  getViewportTiles(map, activeLayers) {
    const bounds = map.getBounds();
    const zoom = Math.floor(map.getZoom());
    const tiles = [];
    const apiKey = window.maptilersdk?.config?.apiKey || 'oinoatcrNmdCL1524DOl';

    const ne = bounds.getNorthEast();
    const sw = bounds.getSouthWest();
    const scale = Math.pow(2, zoom);
    
    const minX = Math.floor((sw.lng + 180) / 360 * scale);
    const maxX = Math.floor((ne.lng + 180) / 360 * scale);
    const minY = Math.floor((1 - Math.log(Math.tan(ne.lat * Math.PI / 180) + 1 / Math.cos(ne.lat * Math.PI / 180)) / Math.PI) / 2 * scale);
    const maxY = Math.floor((1 - Math.log(Math.tan(sw.lat * Math.PI / 180) + 1 / Math.cos(sw.lat * Math.PI / 180)) / Math.PI) / 2 * scale);

    activeLayers.forEach(layer => {
      for (let x = minX; x <= maxX; x++) {
        for (let y = minY; y <= maxY; y++) {
          let tileUrl = '';
          
          switch (layer) {
            case 'basic':
              tileUrl = `https://api.maptiler.com/tiles/backdrop/${zoom}/${x}/${y}.png?key=${apiKey}`;
              break;
            case 'terrain':
              tileUrl = `https://api.maptiler.com/tiles/terrain-rgb-v2/${zoom}/${x}/${y}.webp?key=${apiKey}`;
              break;
            case 'temperature':
              // Temperature tiles sẽ được xử lý riêng trong downloadTemperatureTilesWithDatasets
              console.log('⏭️ Skipping temperature - will be handled by downloadTemperatureTilesWithDatasets');
              continue;
            case 'wind':
              // Wind tiles sẽ được xử lý riêng trong downloadWindTilesWithDatasets
              continue;
            default:
              tileUrl = `https://api.maptiler.com/tiles/${layer}/${zoom}/${x}/${y}.png?key=${apiKey}`;
              break;
          }
          
          if (tileUrl) {
            tiles.push(tileUrl);
          }
        }
      }
    });

    console.log(`📦 Generated ${tiles.length} tile URLs for viewport`);
    return tiles;
  }

  async checkStorageQuota(estimatedTiles) {
    try {
      if ('storage' in navigator && 'estimate' in navigator.storage) {
        const estimate = await navigator.storage.estimate();
        const available = (estimate.quota || 0) - (estimate.usage || 0);
        const needed = estimatedTiles * this.TILE_SIZE_ESTIMATE;
        return available > needed * 1.2;
      }
      return true;
    } catch (error) {
      return true;
    }
  }

  async processTileForLongSave(tileUrl, sceneId) {
    try {
      const transaction = this.db.transaction(['global_tile_pool'], 'readwrite');
      const store = transaction.objectStore('global_tile_pool');
      const existing = await this.getFromStore(store, tileUrl);

      if (existing) {
        existing.refCount++;
        if (!existing.usedByScenes.includes(sceneId)) {
          existing.usedByScenes.push(sceneId);
        }
        this.tempTiles.set(tileUrl, existing);
        console.log(`♻️ Reusing existing tile: ${tileUrl}`);
      } else {
        const tileData = await this.fetchTileData(tileUrl);
        
        // Bỏ qua tile nếu fetch thất bại
        if (!tileData) {
          console.warn(`⏭️ Skipping failed tile: ${tileUrl}`);
          return;
        }
        
        const tileRecord = {
          url: tileUrl,
          data: tileData,
          refCount: 1,
          usedByScenes: [sceneId],
          timestamp: Date.now(),
          size: tileData.byteLength
        };
        this.tempTiles.set(tileUrl, tileRecord);
        console.log(`💾 Cached new tile: ${Math.round(tileData.byteLength / 1024)}KB`);
      }
    } catch (error) {
      console.error(`❌ Tile processing failed: ${tileUrl}`, error);
    }
  }

  async fetchTileData(tileUrl) {
    try {
      console.log(`🌐 Fetching tile: ${tileUrl}`);
      const response = await fetch(tileUrl);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const arrayBuffer = await response.arrayBuffer();
      console.log(`✅ Fetched tile: ${Math.round(arrayBuffer.byteLength / 1024)}KB`);
      return arrayBuffer;
      
    } catch (error) {
      console.error(`❌ Failed to fetch tile ${tileUrl}:`, error);
      // Return null instead of dummy data khi lỗi
      return null;
    }
  }

  async finalizeLongSave(sceneId, sceneName, mapState, tileUrls) {
    const transaction = this.db.transaction(['scenes', 'global_tile_pool'], 'readwrite');
    const scenesStore = transaction.objectStore('scenes');
    const tilesStore = transaction.objectStore('global_tile_pool');

    // Save tiles
    for (const [url, tileData] of this.tempTiles) {
      await this.putInStore(tilesStore, tileData);
    }

    // Save scene
    const sceneData = {
      id: sceneId,
      type: 'long',
      name: sceneName,
      timestamp: Date.now(),
      mapState: { ...mapState, tileUrls }
    };
    await this.putInStore(scenesStore, sceneData);
  }

  async revertTempTiles(sceneId) {
    console.log('Reverting temp tiles...');
  }

  async deleteScene(sceneId) {
    if (!this.db) return;
    
    try {
      const transaction = this.db.transaction(['scenes', 'global_tile_pool'], 'readwrite');
      const scenesStore = transaction.objectStore('scenes');
      
      const scene = await this.getFromStore(scenesStore, sceneId);
      if (scene) {
        await scenesStore.delete(sceneId);
        this.updateSceneButtonUI(sceneId, 'empty');
        
        // Remove timeline marker
        if (window.timelineManager) {
          window.timelineManager.removeSceneMarker(sceneId);
        }
        
        this.showToast('Scene deleted', 'info');
      }
    } catch (error) {
      console.error('Delete failed:', error);
    }
  }

  async loadScene(sceneId) {
    if (!this.db) return null;
    
    try {
      const transaction = this.db.transaction(['scenes', 'wind_tiles'], 'readonly');
      const scenesStore = transaction.objectStore('scenes');
      const windStore = transaction.objectStore('wind_tiles');
      
      const scene = await this.getFromStore(scenesStore, sceneId);
      
      if (scene) {
        console.log(`📦 Loading scene from IndexedDB: ${scene.name} (${scene.type})`);
        
        // Check for offline wind data
        const windTiles = await this.getWindTilesForScene(windStore, sceneId);
        const windDatasets = new Set();
        windTiles.forEach(tile => {
          if (tile.datasetId) windDatasets.add(tile.datasetId);
        });
        
        if (windDatasets.size > 0) {
          console.log(`🌪️ Scene has ${windTiles.length} wind tiles from ${windDatasets.size} datasets`);
          console.log(`📊 Wind datasets:`, Array.from(windDatasets));
          
          // Add wind info to scene metadata
          scene.windInfo = {
            tilesCount: windTiles.length,
            datasets: Array.from(windDatasets),
            hasOfflineWind: true
          };
        } else {
          console.log(`📊 No offline wind data found for scene ${sceneId}`);
          scene.windInfo = {
            hasOfflineWind: false
          };
        }

        // Nếu scene type là 'long' (có offline data), chuyển map sang offline mode
        if (scene.type === 'long' && window.map) {
          console.log(`🔄 Scene is long save type, switching to offline mode...`);
          const offlineSuccess = await this.switchToOfflineMode(window.map, sceneId);
          scene.isOfflineMode = offlineSuccess;
        }
        
        return scene.mapState; // Return the mapState for compatibility
      }
      
      return null;
    } catch (error) {
      console.error('Load scene failed:', error);
      return null;
    }
  }

  async getWindTilesForScene(windStore, sceneId) {
    return new Promise((resolve, reject) => {
      const index = windStore.index('sceneId');
      const request = index.getAll(sceneId);
      
      request.onsuccess = () => {
        resolve(request.result || []);
      };
      
      request.onerror = () => {
        reject(request.error);
      };
    });
  }

  async loadSavedScenesUI() {
    if (!this.db) return;
    
    try {
      const transaction = this.db.transaction(['scenes'], 'readonly');
      const store = transaction.objectStore('scenes');
      const allScenes = await this.getAllFromStore(store);
      
      console.log(`📦 Found ${allScenes.length} scenes in IndexedDB:`, allScenes);
      
      // Update UI for each scene
      allScenes.forEach(scene => {
        this.updateSceneButtonUI(scene.id, scene.type, scene.name);
        console.log(`📦 Updated UI for ${scene.id}: ${scene.name} (${scene.type})`);
      });
      
    } catch (error) {
      console.error('Load saved scenes UI failed:', error);
    }
  }

  async loadTimelineMarkers(timelineManager) {
    if (!this.db || !timelineManager) return;
    
    try {
      const transaction = this.db.transaction(['scenes'], 'readonly');
      const store = transaction.objectStore('scenes');
      const allScenes = await this.getAllFromStore(store);
      
      console.log(`📍 Loading ${allScenes.length} IndexedDB scenes to timeline...`);
      
      // Add timeline markers for each scene
      allScenes.forEach(scene => {
        if (scene.mapState && scene.mapState.time) {
          timelineManager.addSceneMarker(
            scene.id,
            scene.type, // 'short' or 'long'
            scene.mapState.time,
            scene.name
          );
          
          console.log(`📍 Added timeline marker: ${scene.name} (${scene.type}) at ${new Date(scene.mapState.time)}`);
        }
      });
      
    } catch (error) {
      console.error('Load timeline markers failed:', error);
    }
  }

  async clearAllScenes() {
    if (!this.db) return;
    
    try {
      console.log('🗑️ Clearing all offline scenes...');
      
      const transaction = this.db.transaction(['scenes', 'global_tile_pool', 'wind_tiles'], 'readwrite');
      const scenesStore = transaction.objectStore('scenes');
      const tilesStore = transaction.objectStore('global_tile_pool');
      const windTilesStore = transaction.objectStore('wind_tiles');
      
      // Get all scenes first
      const allScenes = await this.getAllFromStore(scenesStore);
      
      // Delete all scenes
      for (const scene of allScenes) {
        await scenesStore.delete(scene.id);
        console.log(`🗑️ Deleted scene: ${scene.id}`);
      }
      
      // Clear all tiles (since no scenes reference them anymore)
      await tilesStore.clear();
      console.log('🗑️ Cleared all tiles from global pool');
      
      // Clear all wind tiles
      await windTilesStore.clear();
      console.log('🗑️ Cleared all wind tiles');
      
      // Update UI for all scene buttons
      for (let i = 1; i <= this.MAX_SCENES; i++) {
        this.updateSceneButtonUI(`scene-${i}`, 'empty');
      }
      
      // Remove any remaining delete buttons in body
      const allDeleteBtns = document.querySelectorAll('.delete-btn[data-scene-id]');
      allDeleteBtns.forEach(btn => btn.remove());
      
      // Clear timeline markers
      if (window.timelineManager) {
        window.timelineManager.clearAllMarkers();
      }
      
      this.showToast('All offline scenes cleared', 'success');
      console.log('✅ All offline scenes cleared successfully');
      
    } catch (error) {
      console.error('Clear all failed:', error);
      this.showToast('Failed to clear scenes', 'error');
    }
  }

  // UI Methods
  showSaveModal(totalTiles, onCancel) {
    const modal = document.createElement('div');
    modal.className = 'offline-save-modal';
    modal.innerHTML = `
      <div class="modal-overlay" style="position:fixed;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,0.7);z-index:10000;display:flex;align-items:center;justify-content:center;">
        <div class="modal-content" style="background:white;padding:24px;border-radius:12px;max-width:400px;width:90%;">
          <h3>💾 Saving Scene Offline</h3>
          <p>Downloading ${totalTiles} tiles...</p>
          <div class="progress-container" style="margin:16px 0;">
            <div class="progress-bar" style="background:#eee;height:8px;border-radius:4px;overflow:hidden;">
              <div class="progress-fill" style="background:#4CAF50;height:100%;width:0%;transition:width 0.3s;"></div>
            </div>
            <div class="progress-text" style="margin-top:8px;">0 / ${totalTiles}</div>
          </div>
          <button class="cancel-btn" style="background:#f44336;color:white;border:none;padding:8px 16px;border-radius:4px;cursor:pointer;">Cancel</button>
        </div>
      </div>
    `;
    
    modal.querySelector('.cancel-btn').onclick = onCancel;
    document.body.appendChild(modal);
    return modal;
  }

  updateProgressModal(modal, completed, total, type = 'tiles') {
    const fill = modal.querySelector('.progress-fill');
    const text = modal.querySelector('.progress-text');
    fill.style.width = (completed / total * 100) + '%';
    text.textContent = `${completed} / ${total} ${type}`;
  }

  updateProgressModalStatus(modal, status) {
    const text = modal.querySelector('.progress-text');
    if (text) {
      text.textContent = status;
    }
  }

  hideSaveModal(modal) {
    if (modal && modal.parentElement) {
      modal.parentElement.removeChild(modal);
    }
  }

  updateSceneButtonUI(sceneId, type, name = '') {
    const button = document.getElementById(sceneId);
    if (!button) return;

    button.classList.remove('short-save', 'long-save', 'empty-scene');
    
    // Remove existing delete button (now in document body, not in button)
    this.removeDeleteButton(sceneId);

    // Add active indicator if not exists
    if (!button.querySelector('.active-indicator')) {
      const indicator = document.createElement('div');
      indicator.className = 'active-indicator';
      button.appendChild(indicator);
    }

    switch (type) {
      case 'short':
        button.classList.add('short-save');
        button.textContent = name;
        this.addDeleteButton(button, sceneId);
        break;
      case 'long':
        button.classList.add('long-save');
        button.textContent = name;
        this.addDeleteButton(button, sceneId);
        break;
      default:
        button.classList.add('empty-scene');
        button.textContent = sceneId.replace('scene-', 'Scene ');
    }
  }

  removeDeleteButton(sceneId) {
    // Find and remove delete button by data attribute or class
    const existingDeleteBtn = document.querySelector(`.delete-btn[data-scene-id="${sceneId}"]`);
    if (existingDeleteBtn) {
      existingDeleteBtn.remove();
    }
  }

  addDeleteButton(button, sceneId) {
    const deleteBtn = document.createElement('span');
    deleteBtn.className = 'delete-btn';
    deleteBtn.setAttribute('data-scene-id', sceneId);
    deleteBtn.innerHTML = '×';
    
    // Calculate position relative to button
    const buttonRect = button.getBoundingClientRect();
    const deleteX = buttonRect.right - 9; // 18px width / 2 = 9px from right edge
    const deleteY = buttonRect.top - 9;   // 18px height / 2 = 9px from top edge
    
    deleteBtn.style.cssText = `position:fixed;top:${deleteY}px;left:${deleteX}px;background:#f44336;color:white;border-radius:50%;width:18px;height:18px;font-size:12px;display:flex;align-items:center;justify-content:center;cursor:pointer;border:2px solid white;font-weight:bold;z-index:1001;`;
    deleteBtn.onclick = (e) => {
      e.stopPropagation();
      this.deleteScene(sceneId);
    };
    
    // Append to body instead of button
    document.body.appendChild(deleteBtn);
  }

  /**
   * Hiển thị thông báo toast trên UI
   */
  showToast(message, type = 'info') {
    // Tạo toast element
    const toast = document.createElement('div');
    toast.className = `offline-toast offline-toast-${type}`;
    toast.textContent = message;
    
    // Style cho toast
    toast.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${type === 'info' ? '#17a2b8' : type === 'warning' ? '#ffc107' : '#dc3545'};
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      font-weight: 500;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      z-index: 10000;
      max-width: 300px;
      word-wrap: break-word;
      transform: translateX(100%);
      transition: transform 0.3s ease-in-out;
    `;
    
    // Thêm vào DOM
    document.body.appendChild(toast);
    
    // Animate in
    setTimeout(() => {
      toast.style.transform = 'translateX(0)';
    }, 100);
    
    // Auto remove sau 3 giây
    setTimeout(() => {
      toast.style.transform = 'translateX(100%)';
      setTimeout(() => {
        if (toast.parentNode) {
          document.body.removeChild(toast);
        }
      }, 300);
    }, 3000);
    
    console.log(`📢 Toast: ${message} (${type})`);
  }

  showNoDataToast(message = 'Không có dữ liệu bổ sung cho thời điểm này') {
    this.showToast(message, 'warning');
  }

  // Helper methods
  async getFromStore(store, key) {
    return new Promise((resolve, reject) => {
      const request = store.get(key);
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  async getAllFromStore(store) {
    return new Promise((resolve, reject) => {
      if (typeof store.getAll === 'function') {
        const request = store.getAll();
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
      } else {
        // Fallback: dùng cursor
        const result = [];
        const request = store.openCursor();
        request.onsuccess = function(event) {
          const cursor = event.target.result;
          if (cursor) {
            result.push(cursor.value);
            cursor.continue();
          } else {
            resolve(result);
          }
        };
        request.onerror = () => reject(request.error);
      }
    });
  }

  async putInStore(store, data) {
    return new Promise((resolve, reject) => {
      const request = store.put(data);
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  async addToStore(store, data) {
    return new Promise((resolve, reject) => {
      const request = store.add(data);
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  // *** NEW: Wind support methods ***
  estimateWindTiles(map) {
    const bounds = map.getBounds();
    const zoom = Math.floor(map.getZoom());
    const scale = Math.pow(2, zoom);
    
    const ne = bounds.getNorthEast();
    const sw = bounds.getSouthWest();
    
    const minX = Math.floor((sw.lng + 180) / 360 * scale);
    const maxX = Math.floor((ne.lng + 180) / 360 * scale);
    const minY = Math.floor((1 - Math.log(Math.tan(ne.lat * Math.PI / 180) + 1 / Math.cos(ne.lat * Math.PI / 180)) / Math.PI) / 2 * scale);
    const maxY = Math.floor((1 - Math.log(Math.tan(sw.lat * Math.PI / 180) + 1 / Math.cos(sw.lat * Math.PI / 180)) / Math.PI) / 2 * scale);

    const windTiles = (maxX - minX + 1) * (maxY - minY + 1);
    return windTiles;
  }

  async extractWindDatasets() {
    try {
      // Method 1: Direct access từ WeatherDataHandler
      if (typeof maptilerweather !== 'undefined' && maptilerweather.WeatherDataHandler) {
        const handler = maptilerweather.WeatherDataHandler;
        console.log('🔍 Attempting to extract wind from WeatherDataHandler...');
        
        if (handler.hasData && handler.hasData()) {
          const windData = handler.getWindData();
          if (windData && windData.metadata) {
            const datasetId = this.extractDatasetIdFromMetadata(windData.metadata);
            
            return [{
              id: datasetId,
              type: 'wind',
              metadata: windData.metadata,
              tiles: windData.tiles || [],
              bounds: windData.bounds || [],
              sourceUrl: this.buildDatasetBaseUrl(datasetId)
            }];
          }
        }
      }

      // Method 2: Access global layer variable (fallback)
      if (window.layer && window.layer.getIsSourceReady && window.layer.getIsSourceReady()) {
        console.log('🔍 Attempting to extract from global layer...');
        const datasets = await this.extractFromWindLayer(window.layer);
        if (datasets) return datasets;
      }

      // Method 3: Intercept network requests (last resort)
      console.log('🔍 Attempting to extract from network requests...');
      return await this.extractFromNetworkRequests();

    } catch (error) {
      console.error('❌ Failed to extract wind datasets:', error);
      return null;
    }
  }

  extractDatasetIdFromMetadata(metadata) {
    // Priority 1: Use title from weather metadata (real dataset ID)
    if (metadata.title) {
      console.log(`🎯 Using weather dataset title: ${metadata.title}`);
      return metadata.title;
    }
    
    // Priority 2: Extract UUID from tiles array
    if (metadata.tiles && metadata.tiles.length > 0) {
      const tileUrl = metadata.tiles[0];
      const match = tileUrl.match(/\/tiles\/([0-9a-f-]{36})\//);
      if (match) {
        console.log(`🎯 Using UUID from tiles: ${match[1]}`);
        return match[1];
      }
    }

    // Priority 3: Extract UUID from source
    if (metadata.source) {
      const match = metadata.source.match(/\/tiles\/([0-9a-f-]{36})\//);
      if (match) {
        console.log(`🎯 Using UUID from source: ${match[1]}`);
        return match[1];
      }
    }

    console.warn('⚠️ Using fallback dataset ID');
    return '0197cd81-8cf8-71e7-adaf-08a8555890eb';
  }

  async extractFromNetworkRequests() {
    console.log('🔍 Monitoring network requests for dataset IDs...');
    
    // Get existing datasets từ browser DevTools Network tab
    const existingDatasets = this.extractDatasetsFromNetworkTab();
    if (existingDatasets.length > 0) {
      console.log(`🎯 Found ${existingDatasets.length} datasets from Network tab:`, existingDatasets);
      return existingDatasets;
    }
    
    // Fallback: Monitor new requests
    return new Promise((resolve) => {
      const datasets = [];
      const maxWait = 3000; // Increase timeout

      const originalFetch = window.fetch;
      window.fetch = function(url, options) {
        if (typeof url === 'string' && url.includes('api.maptiler.com/tiles/')) {
          const match = url.match(/\/tiles\/([0-9a-f-]{36})\//);
          if (match) {
            const datasetId = match[1];
            if (!datasets.find(d => d.id === datasetId)) {
              // Determine dataset type từ URL patterns
              let type = 'unknown';
              if (url.includes('wind') || url.includes('particle')) {
                type = 'wind';
              } else if (url.includes('temperature') || url.includes('temp')) {
                type = 'temperature';
              } else if (url.includes('terrain') || url.includes('hillshade')) {
                type = 'terrain';
              } else if (url.includes('basic') || url.includes('raster')) {
                type = 'basic';
              } else {
                // Try to guess từ tile content
                type = 'weather'; // Assume weather-related
              }
              
              datasets.push({
                id: datasetId,
                type: type,
                sourceUrl: url
              });
              console.log(`🎯 Captured dataset: ${datasetId} (${type})`);
            }
          }
        }
        return originalFetch.apply(this, arguments);
      };

      setTimeout(() => {
        window.fetch = originalFetch;
        resolve(datasets.length > 0 ? datasets : null);
      }, maxWait);
    });
  }

  extractDatasetsFromNetworkTab() {
    const datasets = [];
    
    try {
      // Try to access performance entries
      const performanceEntries = performance.getEntriesByType('resource');
      const maptilerRequests = performanceEntries.filter(entry => 
        entry.name.includes('api.maptiler.com/tiles/') && 
        entry.name.includes('.png')
      );
      
      const uniqueDatasets = new Set();
      
      maptilerRequests.forEach(entry => {
        const match = entry.name.match(/\/tiles\/([0-9a-f-]{36})\//);
        if (match) {
          const datasetId = match[1];
          if (!uniqueDatasets.has(datasetId)) {
            uniqueDatasets.add(datasetId);
            
            // Determine type từ URL
            let type = 'weather';
            if (entry.name.includes('wind')) type = 'wind';
            else if (entry.name.includes('temperature')) type = 'temperature';
            else if (entry.name.includes('terrain') || entry.name.includes('hillshade')) type = 'terrain';
            else if (entry.name.includes('basic')) type = 'basic';
            
            datasets.push({
              id: datasetId,
              type: type,
              sourceUrl: entry.name
            });
          }
        }
      });
      
      console.log(`📊 Extracted ${datasets.length} datasets from Performance API`);
      return datasets;
      
    } catch (error) {
      console.warn('Could not access performance entries:', error);
      return [];
    }
  }

  async downloadWindTilesWithDatasets(map, sceneId, datasets) {
    try {
      const apiKey = window.maptilersdk?.config?.apiKey;
      const sessionId = map.getMaptilerSessionId?.() || 'offline';
      let downloaded = 0;
      for (const dataset of datasets) {
        // Lấy keyframes từ WeatherDataHandler
        if (!dataset.metadata) continue;
        const keyframes = (typeof maptilerweather !== 'undefined' && maptilerweather.WeatherDataHandler && maptilerweather.WeatherDataHandler.getSourcesAndTimestamps)
          ? maptilerweather.WeatherDataHandler.getSourcesAndTimestamps(dataset.metadata, apiKey, sessionId)
          : [];
        for (const kf of keyframes) {
          // Lấy tile URL gốc từ kf.source, thay {z}/{x}/{y}
          const bounds = map.getBounds();
          const zoom = Math.floor(map.getZoom());
          const tiles = this.calculateTileGrid(bounds, zoom);
          for (const tile of tiles) {
            const tileUrl = kf.source.replace('{z}', tile.z).replace('{x}', tile.x).replace('{y}', tile.y);
            console.log('[WIND] Download tile:', tileUrl);
            // Check nếu tile đã có trong DB thì bỏ qua
            const existing = await new Promise((resolve, reject) => {
              const transaction = this.db.transaction(['wind_tiles'], 'readonly');
              const store = transaction.objectStore('wind_tiles');
              const request = store.get(tileUrl);
              request.onsuccess = () => resolve(request.result);
              request.onerror = () => reject(request.error);
            });
            if (existing) continue;
            try {
              const response = await fetch(tileUrl);
              if (response.ok) {
                const tileData = await response.arrayBuffer();
                await new Promise((resolve, reject) => {
                  const transaction = this.db.transaction(['wind_tiles'], 'readwrite');
                  const store = transaction.objectStore('wind_tiles');
                  const request = store.put({
                    url: tileUrl,
                    data: tileData,
                    zoom: tile.z,
                    x: tile.x,
                    y: tile.y,
                    datasetId: dataset.id,
                    timestamp: Date.now(),
                    sceneId: sceneId
                  });
                  request.onsuccess = () => resolve();
                  request.onerror = () => reject(request.error);
                });
                downloaded++;
              }
            } catch (err) {
              console.warn('❌ Lỗi khi download wind tile:', tileUrl, err);
            }
          }
        }
      }
      console.log(`🎉 Đã lưu ${downloaded} wind tile vào IndexedDB!`);
      return downloaded > 0;
    } catch (error) {
      console.error('❌ Wind tiles download failed:', error);
      return false;
    }
  }

  async getDatasetTileCount(datasetId) {
    try {
      const transaction = this.db.transaction(['wind_tiles'], 'readonly');
      const store = transaction.objectStore('wind_tiles');
      const index = store.index('datasetId');
      
      return new Promise((resolve, reject) => {
        const request = index.count(datasetId);
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      return 0;
    }
  }

  async downloadTilesForDataset(dataset, tiles, sceneId, apiKey, sessionId) {
    let downloaded = 0;
    const batchSize = 5; // Process tiles in small batches
    
    for (let i = 0; i < tiles.length; i += batchSize) {
      const batch = tiles.slice(i, i + batchSize);
      
      // Create fresh transaction for each batch
      const transaction = this.db.transaction(['wind_tiles'], 'readwrite');
      const store = transaction.objectStore('wind_tiles');
      
      try {
        for (const tile of batch) {
          const url = this.buildWindTileUrl(tile, dataset, apiKey, sessionId);
          
          // Check if tile already exists
          const existing = await new Promise((resolve, reject) => {
            const request = store.get(url);
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
          });

          if (!existing) {
            const tileData = await this.fetchWindTile(url);
            if (tileData) {
              await new Promise((resolve, reject) => {
                const request = store.put({
                  url: url,
                  data: tileData,
                  zoom: tile.z,
                  x: tile.x,
                  y: tile.y,
                  datasetId: dataset.id,
                  datasetType: dataset.type,
                  timestamp: Date.now(),
                  sceneId: sceneId
                });
                request.onsuccess = () => resolve();
                request.onerror = () => reject(request.error);
              });
              downloaded++;
            }
          }
        }
        
        await transaction.complete;
        
      } catch (error) {
        console.warn(`Batch ${i}-${i+batchSize} failed:`, error);
        transaction.abort();
      }
    }
    
    return downloaded;
  }

  calculateTileGrid(bounds, zoom) {
    const ne = bounds.getNorthEast();
    const sw = bounds.getSouthWest();
    const scale = Math.pow(2, zoom);
    
    const minX = Math.floor((sw.lng + 180) / 360 * scale);
    const maxX = Math.floor((ne.lng + 180) / 360 * scale);
    const minY = Math.floor((1 - Math.log(Math.tan(ne.lat * Math.PI / 180) + 1 / Math.cos(ne.lat * Math.PI / 180)) / Math.PI) / 2 * scale);
    const maxY = Math.floor((1 - Math.log(Math.tan(sw.lat * Math.PI / 180) + 1 / Math.cos(sw.lat * Math.PI / 180)) / Math.PI) / 2 * scale);

    const tiles = [];
    for (let x = minX; x <= maxX; x++) {
      for (let y = minY; y <= maxY; y++) {
        tiles.push({ z: zoom, x: x, y: y });
      }
    }
    
    return tiles;
  }

  buildWindTileUrl(tile, dataset, apiKey, sessionId) {
    // Use actual URL pattern từ dataset sourceUrl if available
    if (dataset.sourceUrl) {
      return `${dataset.sourceUrl}/${tile.z}/${tile.x}/${tile.y}.png?key=${apiKey}&mtsid=${sessionId}`;
    }
    
    // For weather datasets (weather_gfs_* format)
    if (dataset.id.startsWith('weather_gfs_')) {
      return `https://api.maptiler.com/tiles/${dataset.id}/${tile.z}/${tile.x}/${tile.y}.png?key=${apiKey}&mtsid=${sessionId}`;
    }
    
    // Use original URL template from dataset if available
    if (dataset.sourceUrl) {
      // Extract base URL pattern from original
      const baseUrl = dataset.sourceUrl.replace(/\/\d+\/\d+\/\d+\.png.*$/, '');
      const params = new URL(dataset.sourceUrl).searchParams;
      
      // Build new URL with correct tile coordinates but same parameters
      let newUrl = `${baseUrl}/${tile.z}/${tile.x}/${tile.y}.png`;
      
      // Preserve original parameters
      const urlParams = new URLSearchParams();
      for (const [key, value] of params) {
        urlParams.set(key, value);
      }
      
      // Update with current session
      urlParams.set('mtsid', sessionId);
      
      const finalUrl = `${newUrl}?${urlParams.toString()}`;
      
      // Debug log to show URL template usage
      console.log(`🔗 Using template from ${dataset.sourceUrl.split('/').slice(-3).join('/')}`);
      console.log(`➡️ Generated: ${finalUrl.split('/').slice(-3).join('/')}`);
      
      return finalUrl;
    }
    
    // Fallback to manual construction for UUID datasets
    const fallbackUrl = `https://api.maptiler.com/tiles/${dataset.id}/${tile.z}/${tile.x}/${tile.y}.png?key=${apiKey}&mtsid=${sessionId}`;
    console.log(`⚠️ Using fallback for dataset ${dataset.id}`);
    return fallbackUrl;
  }

  async fetchWindTile(url) {
    try {
      const response = await fetch(url);
      if (response.ok) {
        return await response.arrayBuffer();
      }
      return null;
    } catch (error) {
      console.error(`Failed to fetch wind tile: ${url}`, error);
      return null;
    }
  }

  /**
   * Setup offline protocol để intercept tile requests
   */
  setupOfflineProtocol() {
    // Method 1: Sử dụng addProtocol nếu có
    if (window.maplibregl && typeof window.maplibregl.addProtocol === 'function') {
      console.log('🔧 Setting up offline protocol using maplibregl.addProtocol...');
      
      window.maplibregl.addProtocol('offline', (params, callback) => {
        this.handleOfflineProtocol(params, callback);
      });
    } 
    // Method 2: Sử dụng maptilersdk.addProtocol nếu có
    else if (window.maptilersdk && typeof window.maptilersdk.addProtocol === 'function') {
      console.log('🔧 Setting up offline protocol using maptilersdk.addProtocol...');
      
      window.maptilersdk.addProtocol('offline', (params, callback) => {
        this.handleOfflineProtocol(params, callback);
      });
    }
    // Method 3: Monkey patch fetch
    else {
      console.log('🔧 Setting up offline protocol using fetch intercept...');
      this.setupFetchIntercept();
    }
  }

  /**
   * Handle offline protocol requests
   */
  async handleOfflineProtocol(params, callback) {
    try {
      const url = params.url;
      console.log('🔍 Offline protocol request:', url);
      
      // Parse offline URL: offline://layer/z/x/y
      const match = url.match(/offline:\/\/(\w+)\/(\d+)\/(\d+)\/(\d+)/);
      if (!match) {
        callback(new Error('Invalid offline tile URL format'));
        return;
      }
      
      const [_, layerType, z, x, y] = match;
      const tileData = await this.getTileFromIndexedDB(layerType, parseInt(z), parseInt(x), parseInt(y));
      
      if (tileData) {
        // Chuyển ArrayBuffer thành Response-compatible format
        const uint8Array = new Uint8Array(tileData);
        callback(null, uint8Array, null, null);
      } else {
        if (window.offlineSceneManager) {
          window.offlineSceneManager.showNoDataToast('Không có dữ liệu bổ sung cho thời điểm này');
        }
        callback(new Error('Tile not found in offline storage'));
      }
    } catch (error) {
      console.error('❌ Offline protocol error:', error);
      callback(error);
    }
  }

  /**
   * Setup fetch intercept for browsers không hỗ trợ addProtocol
   */
  setupFetchIntercept() {
    if (this.originalFetch) return; // Đã setup rồi
    
    this.originalFetch = window.fetch;
    window.fetch = async (url, options) => {
      if (typeof url === 'string' && url.startsWith('offline://')) {
        console.log('🔍 Fetch intercept offline request:', url);
        
        const match = url.match(/offline:\/\/(\w+)\/(\d+)\/(\d+)\/(\d+)/);
        if (match) {
          const [_, layerType, z, x, y] = match;
          const tileData = await this.getTileFromIndexedDB(layerType, parseInt(z), parseInt(x), parseInt(y));
          
          if (tileData) {
            return new Response(tileData, {
              status: 200,
              headers: {
                'Content-Type': 'image/png',
                'Cache-Control': 'max-age=3600'
              }
            });
          } else {
            if (window.offlineSceneManager) {
              window.offlineSceneManager.showNoDataToast('Không có dữ liệu bổ sung cho thời điểm này');
            }
            return new Response(null, { status: 404 });
          }
        }
      }
      
      // Fallback to original fetch
      return this.originalFetch.call(window, url, options);
    };
  }

  /**
   * Lấy tile từ IndexedDB
   */
  async getTileFromIndexedDB(layerType, z, x, y) {
    if (!this.db) return null;
    
    try {
      let tileData = null;
      
      // Tìm tile trong các stores phù hợp
      const transaction = this.db.transaction(['global_tile_pool', 'wind_tiles', 'temperature_tiles'], 'readonly');
      
      if (layerType === 'wind') {
        // Lấy wind tile
        const windStore = transaction.objectStore('wind_tiles');
        const windTiles = await this.getAllFromStore(windStore);
        const tile = windTiles.find(t => t.zoom === z && t.x === x && t.y === y);
        if (tile && tile.data) {
          tileData = tile.data;
        }
      } else if (layerType === 'temperature') {
        // Lấy temperature tile
        const tempStore = transaction.objectStore('temperature_tiles');
        const tempTiles = await this.getAllFromStore(tempStore);
        const tile = tempTiles.find(t => t.zoom === z && t.x === x && t.y === y);
        if (tile && tile.data) {
          tileData = tile.data;
        }
      } else {
        // Lấy basic/terrain/border tile
        const tilesStore = transaction.objectStore('global_tile_pool');
        const allTiles = await this.getAllFromStore(tilesStore);
        
        // Tìm tile phù hợp
        const tile = allTiles.find(t => {
          const urlMatch = t.url && t.url.includes(`${z}/${x}/${y}`);
          const typeMatch = this.matchLayerType(t.url, layerType);
          return urlMatch && typeMatch;
        });
        
        if (tile && tile.data) {
          tileData = tile.data;
        }
      }
      
      if (tileData) {
        console.log(`📦 Found offline tile: ${layerType}/${z}/${x}/${y}`);
        return tileData;
      } else {
        console.warn(`⚠️ No offline tile: ${layerType}/${z}/${x}/${y}`);
        return null;
      }
    } catch (error) {
      console.error('❌ Error getting tile from IndexedDB:', error);
      return null;
    }
  }

  /**
   * Kiểm tra layer type từ URL
   */
  matchLayerType(url, layerType) {
    if (!url) return false;
    
    switch (layerType) {
      case 'terrain':
        return url.includes('terrain') || url.includes('hillshade') || url.includes('dem');
      case 'temperature':
        return url.includes('temperature') || url.includes('temp');
      case 'basic':
        return url.includes('basic') || url.includes('raster') || url.includes('backdrop');
      case 'border':
        return url.includes('countries') || url.includes('boundary') || url.includes('admin');
      default:
        return false;
    }
  }

  /**
   * Chuyển map sang chế độ offline khi load scene
   */
  async switchToOfflineMode(map, sceneId) {
    if (!map || !this.db) return false;
    
    console.log(`🔄 Switching to offline mode for scene ${sceneId}...`);
    
    try {
      // 1. Đổi terrain source
      if (map.getSource('dem')) {
        map.removeSource('dem');
      }
      
      map.addSource('dem', {
        type: 'raster-dem',
        tiles: [`offline://terrain/{z}/{x}/{y}`],
        tileSize: 256,
        maxzoom: 12,
        attribution: 'Offline Terrain Data'
      });
      
      // 2. Đổi temperature source
      if (map.getSource('temperature')) {
        map.removeSource('temperature');
      }
      
      map.addSource('temperature', {
        type: 'raster',
        tiles: [`offline://temperature/{z}/{x}/{y}`],
        tileSize: 256,
        attribution: 'Offline Temperature Data'
      });
      
      // 3. Đổi countries source
      if (map.getSource('maptiler-countries')) {
        map.removeSource('maptiler-countries');
      }
      
      map.addSource('maptiler-countries', {
        type: 'vector',
        tiles: [`offline://border/{z}/{x}/{y}`],
        attribution: 'Offline Border Data'
      });
      
      // 4. Đổi style source (basic tiles)
      const currentStyle = map.getStyle();
      if (currentStyle && currentStyle.sources) {
        Object.keys(currentStyle.sources).forEach(sourceId => {
          const source = currentStyle.sources[sourceId];
          if (source.type === 'raster' && source.url && source.url.includes('api.maptiler.com')) {
            map.removeSource(sourceId);
            map.addSource(sourceId, {
              type: 'raster',
              tiles: [`offline://basic/{z}/{x}/{y}`],
              tileSize: source.tileSize || 256,
              attribution: 'Offline Map Data'
            });
          }
        });
      }
      
      console.log('✅ Successfully switched to offline mode');
      return true;
      
    } catch (error) {
      console.error('❌ Failed to switch to offline mode:', error);
      return false;
    }
  }

  /**
   * Khôi phục map về chế độ online
   */
  async switchToOnlineMode(map) {
    if (!map) return;
    
    console.log('🌐 Switching back to online mode...');
    
    try {
      // Restore original fetch if monkey-patched
      if (this.originalFetch) {
        window.fetch = this.originalFetch;
        this.originalFetch = null;
      }
      
      // Reset sources về online URLs
      const apiKey = window.maptilersdk?.config?.apiKey || 'oinoatcrNmdCL1524DOl';
      
      // Restore terrain
      if (map.getSource('dem')) {
        map.removeSource('dem');
        map.addSource('dem', {
          type: 'raster-dem',
          url: `https://api.maptiler.com/tiles/terrain-rgb/tiles.json?key=${apiKey}`,
          tileSize: 256,
          maxzoom: 12
        });
      }
      
      // Restore temperature
      if (map.getSource('temperature')) {
        map.removeSource('temperature');
        map.addSource('temperature', {
          type: 'raster',
          tiles: [`https://api.maptiler.com/tiles/temperature/{z}/{x}/{y}.png?key=${apiKey}`],
          tileSize: 256
        });
      }
      
      // Restore countries
      if (map.getSource('maptiler-countries')) {
        map.removeSource('maptiler-countries');
        map.addSource('maptiler-countries', {
          type: 'vector',
          url: `https://api.maptiler.com/tiles/countries/tiles.json?key=${apiKey}`
        });
      }
      
      console.log('✅ Successfully switched to online mode');
      
    } catch (error) {
      console.error('❌ Failed to switch to online mode:', error);
    }
  }

  /**
   * Extract temperature dataset ID từ layerBg (TemperatureLayer)
   */
  async extractTemperatureDatasets() {
    try {
      // Method 1: Direct access từ WeatherDataHandler
      if (typeof maptilerweather !== 'undefined' && maptilerweather.WeatherDataHandler) {
        const handler = maptilerweather.WeatherDataHandler;
        console.log('🔍 Attempting to extract temperature from WeatherDataHandler...');
        
        if (handler.hasData && handler.hasData()) {
          const tempData = handler.getTemperatureData && handler.getTemperatureData();
          if (tempData && tempData.metadata) {
            const datasetId = this.extractDatasetIdFromMetadata(tempData.metadata);
            
            return [{
              id: datasetId,
              type: 'temperature',
              metadata: tempData.metadata,
              tiles: tempData.tiles || [],
              bounds: tempData.bounds || [],
              sourceUrl: this.buildDatasetBaseUrl(datasetId)
            }];
          }
        }
      }

      // Method 2: Extract từ global layerBg variable (fallback)
      if (window.layerBg && window.layerBg.getIsSourceReady && window.layerBg.getIsSourceReady()) {
        console.log('🔍 Attempting to extract temperature datasets from layerBg...');
        const datasets = await this.extractFromTemperatureLayer(window.layerBg);
        if (datasets) return datasets;
      }

      // Method 3: Monitor network cho temperature requests (last resort)
      console.log('🔍 Monitoring network for temperature dataset...');
      return await this.extractTemperatureFromNetwork();

    } catch (error) {
      console.error('❌ Failed to extract temperature datasets:', error);
      return null;
    }
  }

  async extractFromTemperatureLayer(tempLayer) {
    try {
      if (typeof maptilerweather !== 'undefined' && maptilerweather.WeatherDataHandler) {
        const handler = maptilerweather.WeatherDataHandler;
        
        if (handler.hasData && handler.hasData()) {
          const tempData = handler.getTemperatureData && handler.getTemperatureData();
          if (tempData && tempData.metadata) {
            return [{
              id: this.extractDatasetIdFromMetadata(tempData.metadata),
              type: 'temperature',
              metadata: tempData.metadata
            }];
          }
        }
      }
      return null;
    } catch (error) {
      console.error('❌ Failed to extract from TemperatureLayer:', error);
      return null;
    }
  }

  async extractTemperatureFromNetwork() {
    return new Promise((resolve) => {
      const datasets = [];
      const maxWait = 3000;

      const originalFetch = window.fetch;
      window.fetch = function(url, options) {
        if (typeof url === 'string' && url.includes('api.maptiler.com/tiles/')) {
          const match = url.match(/\/tiles\/([0-9a-f-]{36})\//);
          if (match) {
            const datasetId = match[1];
            
            // Nếu request có chứa temperature-related keywords
            if (url.includes('temp') || url.toLowerCase().includes('weather')) {
              if (!datasets.find(d => d.id === datasetId)) {
                datasets.push({
                  id: datasetId,
                  type: 'temperature',
                  sourceUrl: url
                });
                console.log(`🌡️ Captured temperature dataset: ${datasetId}`);
              }
            }
          }
        }
        return originalFetch.apply(this, arguments);
      };

      setTimeout(() => {
        window.fetch = originalFetch;
        resolve(datasets.length > 0 ? datasets : null);
      }, maxWait);
    });
  }

  /**
   * Download temperature tiles với dataset ID thực tế
   */
  async downloadTemperatureTilesWithDatasets(map, sceneId, temperatureDatasets) {
    if (!temperatureDatasets || temperatureDatasets.length === 0) {
      console.warn('⚠️ No temperature datasets provided');
      return false;
    }
    try {
      const apiKey = window.maptilersdk?.config?.apiKey;
      const sessionId = map.getMaptilerSessionId?.() || 'offline';
      let downloaded = 0;
      for (const dataset of temperatureDatasets) {
        if (!dataset.metadata) continue;
        const keyframes = (typeof maptilerweather !== 'undefined' && maptilerweather.WeatherDataHandler && maptilerweather.WeatherDataHandler.getSourcesAndTimestamps)
          ? maptilerweather.WeatherDataHandler.getSourcesAndTimestamps(dataset.metadata, apiKey, sessionId)
          : [];
        for (const kf of keyframes) {
          const bounds = map.getBounds();
          const zoom = Math.floor(map.getZoom());
          const tiles = this.calculateTileGrid(bounds, zoom);
          for (const tile of tiles) {
            const tileUrl = kf.source.replace('{z}', tile.z).replace('{x}', tile.x).replace('{y}', tile.y);
            console.log('[TEMP] Download tile:', tileUrl);
            const existing = await new Promise((resolve, reject) => {
              const transaction = this.db.transaction(['temperature_tiles'], 'readonly');
              const store = transaction.objectStore('temperature_tiles');
              const request = store.get(tileUrl);
              request.onsuccess = () => resolve(request.result);
              request.onerror = () => reject(request.error);
            });
            if (existing) continue;
            try {
              const response = await fetch(tileUrl);
              if (response.ok) {
                const tileData = await response.arrayBuffer();
                await new Promise((resolve, reject) => {
                  const transaction = this.db.transaction(['temperature_tiles'], 'readwrite');
                  const store = transaction.objectStore('temperature_tiles');
                  const request = store.put({
                    url: tileUrl,
                    data: tileData,
                    zoom: tile.z,
                    x: tile.x,
                    y: tile.y,
                    datasetId: dataset.id,
                    sceneId: sceneId,
                    timestamp: Date.now(),
                    type: 'temperature'
                  });
                  request.onsuccess = () => resolve();
                  request.onerror = () => reject(request.error);
                });
                downloaded++;
              }
            } catch (error) {
              console.warn(`⚠️ Failed to download temperature tile ${tile.z}/${tile.x}/${tile.y}:`, error);
            }
          }
        }
      }
      console.log(`✅ Downloaded ${downloaded} temperature tiles`);
      return downloaded > 0;
    } catch (error) {
      console.error('❌ Temperature tiles download failed:', error);
      return false;
    }
  }

  buildTemperatureTileUrl(tile, dataset, apiKey, sessionId) {
    // Use actual URL pattern từ dataset sourceUrl if available  
    if (dataset.sourceUrl) {
      return `${dataset.sourceUrl}/${tile.z}/${tile.x}/${tile.y}.png?key=${apiKey}&mtsid=${sessionId}`;
    }
    
    // For weather datasets (weather_gfs_* format)
    if (dataset.id.startsWith('weather_gfs_')) {
      return `https://api.maptiler.com/tiles/${dataset.id}/${tile.z}/${tile.x}/${tile.y}.png?key=${apiKey}&mtsid=${sessionId}`;
    }
    
    // Fallback: Dùng dataset ID cho UUID format
    return `https://api.maptiler.com/tiles/${dataset.id}/${tile.z}/${tile.x}/${tile.y}.png?key=${apiKey}&mtsid=${sessionId}`;
  }

  // *** NEW: INSPECT ALL LAYER METADATA ***
  async inspectAllLayerMetadata(map) {
    console.log('🔍 Inspecting all layers and sources...');
    
    try {
      // 1. Inspect map style sources
      const style = map.getStyle();
      console.log('📊 Map style sources:', Object.keys(style.sources));
      
      Object.entries(style.sources).forEach(([sourceId, source]) => {
        console.log(`\n📋 Source: ${sourceId}`);
        console.log(`   Type: ${source.type}`);
        
        if (source.url) {
          console.log(`   URL: ${source.url}`);
        }
        if (source.tiles) {
          console.log(`   Tiles: ${source.tiles[0]} (${source.tiles.length} templates)`);
        }
        if (source.data) {
          console.log(`   Data: ${typeof source.data === 'string' ? source.data : 'GeoJSON object'}`);
        }
      });

      // 2. Inspect weather layers
      console.log('\n🌡️ Temperature Layer (layerBg):');
      if (window.layerBg) {
        try {
          console.log(`   ID: ${window.layerBg.id}`);
          console.log(`   Ready: ${window.layerBg.getIsSourceReady?.()}`);
          
          // Try to access metadata if available
          if (window.layerBg._impl?.source?.metadata) {
            console.log(`   Metadata:`, window.layerBg._impl.source.metadata);
          }
          if (window.layerBg.metadata) {
            console.log(`   Direct metadata:`, window.layerBg.metadata);
          }
        } catch (e) {
          console.warn(`   Error accessing layerBg:`, e.message);
        }
      } else {
        console.log('   No layerBg found');
      }

      console.log('\n🌪️ Wind Layer (layer):');
      if (window.layer) {
        try {
          console.log(`   ID: ${window.layer.id}`);
          console.log(`   Ready: ${window.layer.getIsSourceReady?.()}`);
          
          // Try to access metadata if available
          if (window.layer._impl?.source?.metadata) {
            console.log(`   Metadata:`, window.layer._impl.source.metadata);
          }
          if (window.layer.metadata) {
            console.log(`   Direct metadata:`, window.layer.metadata);
          }
        } catch (e) {
          console.warn(`   Error accessing layer:`, e.message);
        }
      } else {
        console.log('   No layer found');
      }

      // 3. Inspect MapTiler Weather API data
      console.log('\n🌐 MapTiler Weather Handler:');
      if (typeof maptilerweather !== 'undefined' && maptilerweather.WeatherDataHandler) {
        const handler = maptilerweather.WeatherDataHandler;
        console.log(`   Handler available: true`);
        console.log(`   Has data: ${handler.hasData?.()}`);
        
        try {
          if (handler.hasData?.()) {
            const windData = handler.getWindData?.();
            const tempData = handler.getTemperatureData?.();
            
            if (windData) {
              console.log(`   Wind data:`, windData);
              if (windData.metadata) {
                console.log(`   Wind metadata:`, windData.metadata);
              }
            }
            
            if (tempData) {
              console.log(`   Temperature data:`, tempData);
              if (tempData.metadata) {
                console.log(`   Temperature metadata:`, tempData.metadata);
              }
            }
          }
        } catch (e) {
          console.warn(`   Error accessing WeatherDataHandler:`, e.message);
        }
      } else {
        console.log('   Weather handler not available');
      }

      // 4. Inspect performance entries for recent network requests
      console.log('\n📡 Recent MapTiler network requests:');
      try {
        const entries = performance.getEntriesByType('resource');
        const maptilerEntries = entries
          .filter(entry => entry.name.includes('api.maptiler.com/tiles/'))
          .slice(-10); // Last 10 requests
          
        maptilerEntries.forEach(entry => {
          const match = entry.name.match(/\/tiles\/([0-9a-f-]{36})\/(\d+)\/(\d+)\/(\d+)\.(png|webp)/);
          if (match) {
            const [_, datasetId, z, x, y, ext] = match;
            console.log(`   ${datasetId.substring(0, 8)}... ${z}/${x}/${y}.${ext} (${entry.transferSize} bytes)`);
          }
        });
      } catch (e) {
        console.warn(`   Error accessing performance entries:`, e.message);
      }

    } catch (error) {
      console.error('❌ Error during metadata inspection:', error);
    }
  }

  buildDatasetBaseUrl(datasetId) {
    const apiKey = window.maptilersdk?.config?.apiKey || 'oinoatcrNmdCL1524DOl';
    return `https://api.maptiler.com/tiles/${datasetId}`;
  }

  async extractFromWindLayer(windLayer) {
    try {
      if (typeof maptilerweather !== 'undefined' && maptilerweather.WeatherDataHandler) {
        const handler = maptilerweather.WeatherDataHandler;
        
        if (handler.hasData && handler.hasData()) {
          const windData = handler.getWindData();
          if (windData && windData.metadata) {
            const datasetId = this.extractDatasetIdFromMetadata(windData.metadata);
            
            return [{
              id: datasetId,
              type: 'wind',
              metadata: windData.metadata,
              tiles: windData.tiles || [],
              bounds: windData.bounds || [],
              sourceUrl: this.buildDatasetBaseUrl(datasetId)
            }];
          }
        }
      }
      return null;
    } catch (error) {
      console.error('❌ Failed to extract from WindLayer:', error);
      return null;
    }
  }

  async downloadBorderTiles(map, sceneId) {
    // Border tiles là vector tile (pbf) từ source countries
    try {
      const apiKey = window.maptilersdk?.config?.apiKey;
      const bounds = map.getBounds();
      const zoom = Math.floor(map.getZoom());
      const tiles = this.calculateTileGrid(bounds, zoom);
      let downloaded = 0;
      const transaction = this.db.transaction(['global_tile_pool'], 'readwrite');
      const store = transaction.objectStore('global_tile_pool');
      for (const tile of tiles) {
        // MapTiler countries vector tile URL
        const tileUrl = `https://api.maptiler.com/tiles/countries/${tile.z}/${tile.x}/${tile.y}.pbf?key=${apiKey}`;
        console.log('[BORDER] Download tile:', tileUrl);
        const existing = await new Promise((resolve, reject) => {
          const request = store.get(tileUrl);
          request.onsuccess = () => resolve(request.result);
          request.onerror = () => reject(request.error);
        });
        if (existing) continue;
        try {
          const response = await fetch(tileUrl);
          if (response.ok) {
            const tileData = await response.arrayBuffer();
            await new Promise((resolve, reject) => {
              const request = store.put({
                url: tileUrl,
                data: tileData,
                zoom: tile.z,
                x: tile.x,
                y: tile.y,
                type: 'border',
                sceneId: sceneId,
                timestamp: Date.now()
              });
              request.onsuccess = () => resolve();
              request.onerror = () => reject(request.error);
            });
            downloaded++;
          }
        } catch (error) {
          console.warn('❌ Lỗi khi download border tile:', tileUrl, error);
        }
      }
      console.log(`✅ Downloaded ${downloaded} border tiles`);
      return downloaded > 0;
    } catch (error) {
      console.error('❌ Border tiles download failed:', error);
      return false;
    }
  }
}

// Initialize global instance
window.offlineSceneManager = new OfflineSceneManager();

// Debug methods for testing wind functionality
window.debugWind = {
  async stats() {
    const manager = window.offlineSceneManager;
    if (!manager.db) {
      console.log('❌ Database not initialized');
      return;
    }
    
    const transaction = manager.db.transaction(['wind_tiles'], 'readonly');
    const store = transaction.objectStore('wind_tiles');
    
    const allTiles = await new Promise((resolve, reject) => {
      const request = store.getAll();
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
    
    const datasetCounts = {};
    allTiles.forEach(tile => {
      const id = tile.datasetId;
      if (!datasetCounts[id]) datasetCounts[id] = 0;
      datasetCounts[id]++;
    });
    
    console.log('🌪️ Wind Cache Stats:');
    console.log(`Total tiles: ${allTiles.length}`);
    console.log('Datasets:', datasetCounts);
    
    return { totalTiles: allTiles.length, datasets: datasetCounts };
  },
  
  async extract() {
    const manager = window.offlineSceneManager;
    console.log('🔍 Extracting current datasets...');
    
    const datasets = await manager.extractWindDatasets();
    console.log('📊 Found datasets:', datasets);
    
    return datasets;
  },
  
  async clearDB() {
    const manager = window.offlineSceneManager;
    if (!manager.db) {
      console.log('❌ Database not initialized');
      return;
    }
    
    const transaction = manager.db.transaction(['wind_tiles'], 'readwrite');
    const store = transaction.objectStore('wind_tiles');
    
    await new Promise((resolve, reject) => {
      const request = store.clear();
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
    
    console.log('🗑️ Wind cache cleared');
  },
  
  async testDataset(datasetId) {
    const map = window.map;
    const manager = window.offlineSceneManager;
    const apiKey = map.getSdkConfig().apiKey;
    const sessionId = map.getMaptilerSessionId();
    
    // Test with center tile
    const center = map.getCenter();
    const zoom = Math.floor(map.getZoom());
    const scale = Math.pow(2, zoom);
    const x = Math.floor((center.lng + 180) / 360 * scale);
    const y = Math.floor((1 - Math.log(Math.tan(center.lat * Math.PI / 180) + 1 / Math.cos(center.lat * Math.PI / 180)) / Math.PI) / 2 * scale);
    
    // Try to find dataset with sourceUrl first
    const datasets = await manager.extractWindDatasets();
    const dataset = datasets ? datasets.find(d => d.id === datasetId) : null;
    
    let testUrl;
    if (dataset && dataset.sourceUrl) {
      // Use buildWindTileUrl logic
      testUrl = manager.buildWindTileUrl({ z: zoom, x: x, y: y }, dataset, apiKey, sessionId);
    } else {
      // Fallback to manual construction
      testUrl = `https://api.maptiler.com/tiles/${datasetId}/${zoom}/${x}/${y}.png?key=${apiKey}&mtsid=${sessionId}`;
    }
    
    try {
      const response = await fetch(testUrl);
      console.log(`🧪 Testing dataset ${datasetId}: ${response.status} ${response.statusText}`);
      console.log(`🔗 Test URL: ${testUrl}`);
      return response.ok;
    } catch (error) {
      console.error(`❌ Test failed:`, error);
      return false;
    }
  }
};

// ... existing code ...
// Handle long save (with tiles) - CHỈ sử dụng CompleteOfflineDownloader
function handleLongSave(sceneId) {
  const sceneIndex = parseInt(sceneId.replace('scene-', ''));
  const sceneName = prompt('📦 Scene name (offline):', `Offline ${sceneIndex}`);
  if (!sceneName) {
    if (typeof resetSaveMode === 'function') resetSaveMode();
    return;
  }

  if (window.CompleteOfflineDownloader && window.map) {
    const downloader = new window.CompleteOfflineDownloader();
    // Hiện loading UI
    const loading = document.createElement('div');
    loading.innerHTML = '<div style="position:fixed;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,0.5);z-index:99999;display:flex;align-items:center;justify-content:center;"><div style="background:white;padding:32px 48px;border-radius:12px;font-size:20px;font-weight:bold;box-shadow:0 4px 24px #0005;">Đang lưu cảnh offline...<br><span id="offline-progress"></span></div></div>';
    document.body.appendChild(loading);
    
    downloader.performLongSave(sceneName, window.map).then(result => {
      document.body.removeChild(loading);
      if (result.success) {
        alert('✅ Lưu cảnh offline thành công!');
      } else {
        alert('❌ Lưu thất bại!');
      }
      if (typeof resetSaveMode === 'function') resetSaveMode();
    });
    return;
  }

  alert('❌ CompleteOfflineDownloader not available');
  if (typeof resetSaveMode === 'function') resetSaveMode();
}
// ... existing code ...
