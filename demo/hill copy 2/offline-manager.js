/**
 * Complete Offline Manager
 * <PERSON>ế<PERSON> hợp chức năng từ complete-offline-downloader.js và offline-scene-manager.js
 */
class CompleteOfflineManager {
  constructor() {
    // Từ complete-offline-downloader.js
    this.apiKey = window.maptilersdk?.config?.apiKey || 'oinoatcrNmdCL1524DOl';
    this.sessionId = null;
    this.metadata = {};
    
    // Từ offline-scene-manager.js
    this.db = null;
    this.isProcessing = false;
    this.tempTiles = new Map();
    
    // Constants
    this.dbName = 'MapTilerOfflineCache';
    this.dbVersion = 5;
    this.MAX_SCENES = 5;
    this.SHORT_SAVE_TTL = 3 * 60 * 60 * 1000; // 3 hours
    this.TILE_SIZE_ESTIMATE = 50 * 1024; // 50KB
    
    // Initialize
    this.init();
  }

  async init() {
    try {
      await this.initIndexedDB();
      await this.registerServiceWorker();
      await this.cleanupExpiredShortSaves();
      this.setupOfflineProtocol();
      console.log('✅ Offline Manager initialized');
    } catch (error) {
      console.error('❌ Failed to initialize Offline Manager:', error);
    }
  }

  async initIndexedDB() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);

      request.onerror = () => reject(request.error);

      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = event.target.result;

        // Global tile pool (basic/terrain/border tiles)
        if (!db.objectStoreNames.contains('global_tile_pool')) {
          const globalStore = db.createObjectStore('global_tile_pool', { keyPath: 'url' });
          globalStore.createIndex('sceneId', 'sceneId', { multiEntry: true });
          globalStore.createIndex('timestamp', 'timestamp');
        }

        // Wind tiles store
        if (!db.objectStoreNames.contains('wind_tiles')) {
          const windStore = db.createObjectStore('wind_tiles', { keyPath: 'url' });
          windStore.createIndex('datasetId', 'datasetId');
          windStore.createIndex('sceneId', 'sceneId');
          windStore.createIndex('coordinates', ['zoom', 'x', 'y']);
        }

        // Temperature tiles store
        if (!db.objectStoreNames.contains('temperature_tiles')) {
          const tempStore = db.createObjectStore('temperature_tiles', { keyPath: 'url' });
          tempStore.createIndex('datasetId', 'datasetId');
          tempStore.createIndex('sceneId', 'sceneId');
          tempStore.createIndex('coordinates', ['zoom', 'x', 'y']);
        }

        // Scenes metadata
        if (!db.objectStoreNames.contains('scenes')) {
          const scenesStore = db.createObjectStore('scenes', { keyPath: 'id' });
          scenesStore.createIndex('type', 'type');
          scenesStore.createIndex('timestamp', 'timestamp');
        }
      };
    });
  }

  // ===== Các phương thức từ complete-offline-downloader.js =====
  async downloadAllMetadataFiles() {
    console.log('\n📋 ===== DOWNLOADING METADATA FILES =====');
    await this.initIndexedDB();
    
    const metadataUrls = [
      { name: 'latest.json', url: `https://api.maptiler.com/weather/latest.json?key=${this.apiKey}&mtsid=${this.sessionId}` },
      { name: 'terrain-rgb_tiles.json', url: `https://api.maptiler.com/tiles/terrain-rgb/tiles.json?key=${this.apiKey}&mtsid=${this.sessionId}` },
      { name: 'terrain-rgb-v2_tiles.json', url: `https://api.maptiler.com/tiles/terrain-rgb-v2/tiles.json?key=${this.apiKey}&mtsid=${this.sessionId}` },
      { name: 'v3_tiles.json', url: `https://api.maptiler.com/tiles/v3/tiles.json?key=${this.apiKey}&mtsid=${this.sessionId}` },
      { name: 'countries_tiles.json', url: `https://api.maptiler.com/tiles/countries/tiles.json?key=${this.apiKey}&mtsid=${this.sessionId}` }
    ];

    for (const fileInfo of metadataUrls) {
      console.log(`🌐 Downloading: ${fileInfo.url}`);
      try {
        const response = await fetch(fileInfo.url);
        if (!response.ok) throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        
        const data = await response.json();
        this.metadata[fileInfo.name] = data;
        await this.saveMetadata(fileInfo.name, data);
        console.log(`✅ ${fileInfo.name}: OK`);
      } catch (error) {
        console.error(`❌ ${fileInfo.name}: FAILED - ${error.message}`);
        throw error;
      }
    }
    return true;
  }

  // ===== Các phương thức từ offline-scene-manager.js =====
  async saveShortScene(sceneId, sceneName, mapState) {
    if (!this.db) return false;
    
    try {
      const sceneData = {
        id: sceneId,
        type: 'short',
        name: sceneName,
        timestamp: Date.now(),
        mapState: mapState
      };

      const transaction = this.db.transaction(['scenes'], 'readwrite');
      const store = transaction.objectStore('scenes');
      await this.putInStore(store, sceneData);

      this.updateSceneButtonUI(sceneId, 'short', sceneName);
      
      if (window.timelineManager) {
        window.timelineManager.addSceneMarker(sceneId, 'short', mapState.time, sceneName);
      }
      
      this.showToast('Short save completed', 'success');
      return true;
    } catch (error) {
      console.error('Short save failed:', error);
      this.showToast('Save failed', 'error');
      return false;
    }
  }

  // ===== Các helper methods =====
  async putInStore(store, data) {
    return new Promise((resolve, reject) => {
      const request = store.put(data);
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  async getFromStore(store, key) {
    return new Promise((resolve) => {
      const request = store.get(key);
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => resolve(null);
    });
  }

  showToast(message, type = 'info') {
    // Implement toast notification
    console.log(`[${type.toUpperCase()}] ${message}`);
  }
}

// Export to global
window.offlineManager = new CompleteOfflineManager();
