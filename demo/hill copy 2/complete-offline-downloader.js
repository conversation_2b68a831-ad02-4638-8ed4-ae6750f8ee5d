class CompleteOfflineDownloader {
  constructor() {
    this.apiKey = window.maptilersdk?.config?.apiKey || 'oinoatcrNmdCL1524DOl';
    this.sessionId = null;
    this.metadata = {};
    this.downloadStats = {
      total: 0,
      success: 0,
      failed: 0,
      failedUrls: []
    };
    this.db = null;
    this.dbName = 'MapTilerOfflineCache';
    this.dbVersion = 5;
  }

  // Khởi tạo session ID từ map
  initSessionId(map) {
    this.sessionId = map.getMaptilerSessionId?.() || this.generateSessionId();
    console.log(`🔗 Session ID: ${this.sessionId}`);
  }

  generateSessionId() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c == 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  // Bước 1: <PERSON><PERSON><PERSON> tất cả file JS metadata cần thiết
  async downloadAllMetadataFiles() {
    console.log('\n📋 ===== BẮT ĐẦU TẢI CÁC FILE JS METADATA =====');
    
    // Khởi tạo IndexedDB trước
    await this.initIndexedDB();
    
    const metadataUrls = [
      {
        name: 'latest.json',
        url: `https://api.maptiler.com/weather/latest.json?key=${this.apiKey}&mtsid=${this.sessionId}`
      },
      {
        name: 'terrain-rgb_tiles.json', 
        url: `https://api.maptiler.com/tiles/terrain-rgb/tiles.json?key=${this.apiKey}&mtsid=${this.sessionId}`
      },
      {
        name: 'terrain-rgb-v2_tiles.json',
        url: `https://api.maptiler.com/tiles/terrain-rgb-v2/tiles.json?key=${this.apiKey}&mtsid=${this.sessionId}`
      },
      {
        name: 'v3_tiles.json',
        url: `https://api.maptiler.com/tiles/v3/tiles.json?key=${this.apiKey}&mtsid=${this.sessionId}`
      },
      {
        name: 'countries_tiles.json',
        url: `https://api.maptiler.com/tiles/countries/tiles.json?key=${this.apiKey}&mtsid=${this.sessionId}`
      }
    ];

    // Tải từng file metadata
    for (const fileInfo of metadataUrls) {
      console.log(`🌐 Tải file: ${fileInfo.url}`);
      try {
        const response = await fetch(fileInfo.url);
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const data = await response.json();
        this.metadata[fileInfo.name] = data;
        
        // Lưu vào IndexedDB
        await this.saveMetadata(fileInfo.name, data);
        
        console.log(`✅ ${fileInfo.name}: OK`);
      } catch (error) {
        console.log(`❌ ${fileInfo.name}: FAILED - ${error.message}`);
        console.log('🛑 DỪNG TẠI ĐÂY - KHÔNG THỂ TẢI METADATA');
        throw error;
      }
    }

    console.log('✅ TẤT CẢ FILE METADATA ĐÃ TẢI THÀNH CÔNG');
    return true;
  }

  // Bước 2: Khởi tạo metadata cho scene
  initSceneMetadata(sceneName, map) {
    console.log(`\n📝 ===== KHỞI TẠO METADATA CHO SCENE: ${sceneName} =====`);
    
    const bounds = map.getBounds();
    const zoom = Math.floor(map.getZoom());
    const center = map.getCenter();
    
    // Lấy dataset IDs từ latest.json
    const latest = this.metadata['latest.json'];
    const temperatureDatasets = this.extractTemperatureDatasets(latest);
    const windDatasets = this.extractWindDatasets(latest);
    
    // Tính toán tiles cần tải
    const tiles = this.calculateTileGrid(bounds, zoom);
    
    const sceneMetadata = {
      sceneName: sceneName,
      timestamp: new Date().toISOString(),
      viewport: {
        center: [center.lng, center.lat],
        zoom: zoom,
        bounds: [bounds.getWest(), bounds.getSouth(), bounds.getEast(), bounds.getNorth()]
      },
      datasets: {
        temperature: ['terrain-rgb'],
        wind: windDatasets.map(d => d.id),
        terrain: ['terrain-rgb-v2'],
        basic: ['v3'],
        border: ['countries']
      },
      tileGrid: {
        zoom: zoom,
        totalTiles: tiles.length,
        bounds: [bounds.getWest(), bounds.getSouth(), bounds.getEast(), bounds.getNorth()]
      },
      metadata: this.metadata
    };

    console.log(`+ Terrain: ${sceneMetadata.datasets.terrain.join(', ')}`);
    console.log(`+ Temperature: ${sceneMetadata.datasets.temperature.join(', ')}`);
    console.log(`+ Wind: ${sceneMetadata.datasets.wind.join(', ')}`);
    console.log(`+ Basic: ${sceneMetadata.datasets.basic.join(', ')}`);
    console.log(`+ Border: ${sceneMetadata.datasets.border.join(', ')}`);
    console.log(`+ Timestamp: ${sceneMetadata.timestamp}`);
    console.log(`+ Zoom level: ${zoom}`);
    console.log(`+ Total tiles to download: ${tiles.length}`);

    return { sceneMetadata, tiles, temperatureDatasets, windDatasets };
  }

  // Extract temperature datasets từ latest.json
  extractTemperatureDatasets(latest) {
    // Temperature chính là terrain-rgb tiles, không cần extract từ datasets
    return [{
      id: 'terrain-rgb', 
      type: 'temperature',
      metadata: { name: 'temperature', tileset: 'terrain-rgb' }
    }];
  }

  // Extract wind datasets từ latest.json (UUID thực tế)
  extractWindDatasets(latest) {
    const datasets = [];
    console.log('🔍 Extracting wind datasets from latest.json...');
    
    // Lấy timestamp hiện tại từ timeline
    const currentTimestamp = this.getCurrentTimestamp();
    console.log(`⏰ Current timestamp: ${currentTimestamp}`);
    
    if (latest && latest.variables) {
      console.log(`📊 Total variables found: ${latest.variables.length}`);
      
      for (const variable of latest.variables) {
        if (variable.metadata && variable.metadata.weather_variable) {
          const weatherVar = variable.metadata.weather_variable.name;
          console.log(`🌪️ Variable: ${variable.metadata.title} - weather_variable = ${weatherVar}`);
          
          // Tìm wind variables
          if (weatherVar === 'Wind' || weatherVar === 'wind_u' || weatherVar === 'wind_v' || weatherVar === 'wind_speed') {
            console.log(`✅ Found wind variable: ${variable.metadata.title}`);
            
            // Lấy keyframes (chứa UUID thực tế)
            if (variable.keyframes && variable.keyframes.length > 0) {
              // Tìm keyframe gần nhất với timestamp hiện tại
              const currentKeyframe = this.getCurrentKeyframe(variable.keyframes, currentTimestamp);
              
              if (currentKeyframe) {
                console.log(`🎯 Using keyframe UUID: ${currentKeyframe.id} for timestamp: ${currentKeyframe.timestamp}`);
                
                datasets.push({
                  id: currentKeyframe.id, // ĐÂY MỚI LÀ UUID THỰC TẾ!
                  title: variable.metadata.title,
                  weatherVariable: weatherVar,
                  metadata: variable.metadata,
                  keyframes: variable.keyframes,
                  currentKeyframe: currentKeyframe,
                  currentTimestamp: currentKeyframe.timestamp
                });
              }
            }
          }
        }
      }
    }
    
    // Fallback: lấy từ WeatherDataHandler nếu có
    if (datasets.length === 0 && typeof maptilerweather !== 'undefined' && maptilerweather.WeatherDataHandler) {
      console.log('🔄 Fallback: Trying WeatherDataHandler...');
      const handler = maptilerweather.WeatherDataHandler;
      if (handler.hasData && handler.hasData()) {
        const windData = handler.getWindData();
        if (windData && windData.metadata && windData.metadata.title) {
          console.log(`✅ Adding wind dataset from handler: ${windData.metadata.title}`);
          datasets.push({
            id: windData.metadata.title, // UUID từ WeatherDataHandler
            metadata: windData.metadata,
            keyframes: []
          });
        }
      }
    }
    
    console.log(`🎯 Final wind datasets: ${datasets.map(d => `${d.title} -> ${d.id} (${d.currentTimestamp})`).join(', ')}`);
    return datasets;
  }

  // Tính toán grid tiles cần tải
  calculateTileGrid(bounds, zoom) {
    const tiles = [];
    const nw = this.deg2tile(bounds.getNorth(), bounds.getWest(), zoom);
    const se = this.deg2tile(bounds.getSouth(), bounds.getEast(), zoom);
    
    for (let x = nw.x; x <= se.x; x++) {
      for (let y = nw.y; y <= se.y; y++) {
        tiles.push({ x, y, z: zoom });
      }
    }
    return tiles;
  }

  deg2tile(lat_deg, lon_deg, zoom) {
    const lat_rad = lat_deg * Math.PI / 180;
    const n = Math.pow(2, zoom);
    const x = Math.floor((lon_deg + 180) / 360 * n);
    const y = Math.floor((1 - Math.asinh(Math.tan(lat_rad)) / Math.PI) / 2 * n);
    return { x, y };
  }

  // Bước 3: Tải tiles cho temperature (terrain-rgb)
  async downloadTemperatureTiles(tiles, temperatureDatasets) {
    console.log(`\n🌡️ ===== BẮT ĐẦU TẢI FILE TILES CHO TEMPERATURE =====`);
    
    // Temperature = terrain-rgb tiles (chỉ cần tải 1 lần)
    console.log(`📦 Dataset: terrain-rgb (temperature data)`);
    for (const tile of tiles) {
      const url = `https://api.maptiler.com/tiles/terrain-rgb/${tile.z}/${tile.x}/${tile.y}.png?key=${this.apiKey}&mtsid=${this.sessionId}`;
      await this.downloadSingleTile(url, 'temperature', 'terrain-rgb', tile);
    }
  }

  // Bước 4: Tải tiles cho wind (UUID tiles)
  async downloadWindTiles(tiles, windDatasets) {
    console.log(`\n🌪️ ===== BẮT ĐẦU TẢI FILE TILES CHO WIND =====`);
    
    for (const dataset of windDatasets) {
      console.log(`📦 Dataset: ${dataset.id} (wind data)`);
      for (const tile of tiles) {
        // UUID tiles format
        const url = `https://api.maptiler.com/tiles/${dataset.id}/${tile.z}/${tile.x}/${tile.y}.png?key=${this.apiKey}&mtsid=${this.sessionId}`;
        await this.downloadSingleTile(url, 'wind', dataset.id, tile);
      }
    }
  }

  // Bước 5: Tải tiles cho terrain (chỉ terrain-rgb-v2 vì terrain-rgb đã tải ở temperature)
  async downloadTerrainTiles(tiles) {
    console.log(`\n⛰️ ===== BẮT ĐẦU TẢI FILE TILES CHO TERRAIN =====`);
    
    // Terrain RGB v2 (webp) - cho DEM/elevation data
    console.log(`📦 Terrain RGB v2 (webp) - DEM data`);
    for (const tile of tiles) {
      const url = `https://api.maptiler.com/tiles/terrain-rgb-v2/${tile.z}/${tile.x}/${tile.y}.webp?key=${this.apiKey}&mtsid=${this.sessionId}`;
      await this.downloadSingleTile(url, 'terrain', 'terrain-rgb-v2', tile);
    }
    
    // Lưu ý: terrain-rgb (png) đã được tải ở phần temperature
    console.log(`📋 Note: terrain-rgb (png) đã được tải ở phần temperature`);
  }

  // Bước 6: Tải tiles cho basic/border
  async downloadBasicAndBorderTiles(tiles) {
    console.log(`\n🗺️ ===== BẮT ĐẦU TẢI FILE TILES CHO BASIC/BORDER =====`);
    
    // Basic tiles (v3, pbf)
    console.log(`📦 Basic tiles (v3)`);
    for (const tile of tiles) {
      const url = `https://api.maptiler.com/tiles/v3/${tile.z}/${tile.x}/${tile.y}.pbf?key=${this.apiKey}&mtsid=${this.sessionId}`;
      await this.downloadSingleTile(url, 'basic', 'v3', tile);
    }

    // Border tiles (countries, pbf)
    console.log(`📦 Border tiles (countries)`);
    for (const tile of tiles) {
      const url = `https://api.maptiler.com/tiles/countries/${tile.z}/${tile.x}/${tile.y}.pbf?key=${this.apiKey}&mtsid=${this.sessionId}`;
      await this.downloadSingleTile(url, 'border', 'countries', tile);
    }
  }

  // Tải một tile đơn lẻ và lưu vào IndexedDB
  async downloadSingleTile(url, layerType, datasetId, tile) {
    this.downloadStats.total++;
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }
      
      const data = await response.arrayBuffer();
      console.log(`✅ Link ${this.downloadStats.total}: OK - ${url}`);
      this.downloadStats.success++;
      
      // Lưu vào IndexedDB theo loại tile
      try {
        switch (layerType) {
          case 'wind':
            await this.saveWindTile(datasetId, tile, data, url);
            break;
          case 'temperature':
            await this.saveTemperatureTile(tile, data, url);
            break;
          case 'terrain':
            await this.saveTerrainTile(tile, data, url);
            break;
          case 'basic':
            await this.saveVectorTile('v3', tile, data, url);
            break;
          case 'border':
            await this.saveVectorTile('countries', tile, data, url);
            break;
          default:
            console.warn(`⚠️ Unknown layer type: ${layerType}`);
        }
        console.log(`💾 Đã lưu ${layerType} tile: ${tile.z}/${tile.x}/${tile.y}`);
      } catch (saveError) {
        console.error(`❌ Lỗi lưu tile ${layerType}:`, saveError);
        // Vẫn coi như thành công download, chỉ lỗi save
      }
      
    } catch (error) {
      console.log(`❌ Link ${this.downloadStats.total}: FAILED - ${url} - ${error.message}`);
      this.downloadStats.failed++;
      this.downloadStats.failedUrls.push(url);
    }
  }

  // Hàm chính để thực hiện toàn bộ quá trình
  async performLongSave(sceneName, map) {
    try {
      console.log(`🚀 ===== BẮT ĐẦU LONG SAVE CHO SCENE: ${sceneName} =====`);
      
      // Khởi tạo session
      this.initSessionId(map);
      
      // Bước 1: Tải metadata
      await this.downloadAllMetadataFiles();
      
      // Bước 2: Khởi tạo scene metadata
      const { sceneMetadata, tiles, temperatureDatasets, windDatasets } = this.initSceneMetadata(sceneName, map);
      
      // Reset stats
      this.downloadStats = { total: 0, success: 0, failed: 0, failedUrls: [] };
      
      // Bước 3-6: Tải tiles
      await this.downloadTemperatureTiles(tiles, temperatureDatasets);
      await this.downloadWindTiles(tiles, windDatasets);
      await this.downloadTerrainTiles(tiles);
      await this.downloadBasicAndBorderTiles(tiles);
      
      // Lưu scene metadata vào IndexedDB
      await this.saveSceneMetadata(sceneMetadata);
      
      // Kết quả cuối cùng
      console.log(`\n📊 ===== KẾT QUẢ CUỐI CÙNG =====`);
      console.log(`✅ Thành công: ${this.downloadStats.success}/${this.downloadStats.total}`);
      console.log(`❌ Thất bại: ${this.downloadStats.failed}/${this.downloadStats.total}`);
      
      if (this.downloadStats.failed > 0) {
        console.log(`🚨 FAILED - Có ${this.downloadStats.failed} tiles tải thất bại`);
        console.log('🔗 Các URL thất bại:', this.downloadStats.failedUrls);
        return { success: false, stats: this.downloadStats, metadata: sceneMetadata };
      } else {
        console.log(`🎉 HOÀN THÀNH - Tất cả tiles đã tải thành công`);
        console.log(`💾 Scene '${sceneName}' đã được lưu vào IndexedDB`);
        return { success: true, stats: this.downloadStats, metadata: sceneMetadata };
      }
      
    } catch (error) {
      console.log(`💥 LỖI NGHIÊM TRỌNG: ${error.message}`);
      console.log(`🚨 FAILED - Quá trình tải bị dừng`);
      return { success: false, error: error.message };
    }
  }

  // Lấy timestamp hiện tại từ timeline
  getCurrentTimestamp() {
    // Thử lấy từ timeline slider hoặc weather handler
    if (typeof maptilerweather !== 'undefined' && maptilerweather.WeatherDataHandler) {
      const handler = maptilerweather.WeatherDataHandler;
      if (handler.getCurrentTimestamp) {
        return handler.getCurrentTimestamp();
      }
    }
    
    // Fallback: timestamp hiện tại
    return new Date().toISOString();
  }

  // Tìm keyframe gần nhất với timestamp đó
  getCurrentKeyframe(keyframes, targetTimestamp) {
    if (!keyframes || keyframes.length === 0) {
      return null;
    }
    
    // Nếu không có targetTimestamp, lấy keyframe đầu tiên
    if (!targetTimestamp) {
      return keyframes[0];
    }
    
    const targetTime = new Date(targetTimestamp).getTime();
    let closestKeyframe = keyframes[0];
    let minDiff = Math.abs(new Date(closestKeyframe.timestamp).getTime() - targetTime);
    
    for (const keyframe of keyframes) {
      const keyframeTime = new Date(keyframe.timestamp).getTime();
      const diff = Math.abs(keyframeTime - targetTime);
      
      if (diff < minDiff) {
        minDiff = diff;
        closestKeyframe = keyframe;
      }
    }
    
    return closestKeyframe;
  }

  // Khởi tạo IndexedDB
  async initIndexedDB() {
    return new Promise((resolve, reject) => {
      console.log('🗄️ Khởi tạo IndexedDB...');
      
      const request = indexedDB.open(this.dbName, this.dbVersion);
      
      request.onerror = () => {
        console.error('❌ Lỗi khởi tạo IndexedDB:', request.error);
        reject(request.error);
      };
      
      request.onsuccess = () => {
        this.db = request.result;
        console.log('✅ IndexedDB đã sẵn sàng');
        resolve(this.db);
      };
      
      request.onupgradeneeded = (event) => {
        console.log('🔄 Cập nhật cấu trúc IndexedDB...');
        const db = event.target.result;
        
        // Store cho metadata các file JSON
        if (!db.objectStoreNames.contains('metadata')) {
          const metadataStore = db.createObjectStore('metadata', { keyPath: 'name' });
          console.log('📋 Tạo store: metadata');
        }
        
        // Store cho scene metadata
        if (!db.objectStoreNames.contains('scenes')) {
          const scenesStore = db.createObjectStore('scenes', { keyPath: 'sceneName' });
          console.log('🎬 Tạo store: scenes');
        }
        
        // Store cho wind tiles (UUID-based)
        if (!db.objectStoreNames.contains('wind_tiles')) {
          const windStore = db.createObjectStore('wind_tiles', { keyPath: 'id' });
          windStore.createIndex('datasetId', 'datasetId', { unique: false });
          windStore.createIndex('coordinates', ['z', 'x', 'y'], { unique: false });
          console.log('🌪️ Tạo store: wind_tiles');
        }
        
        // Store cho temperature tiles (terrain-rgb)
        if (!db.objectStoreNames.contains('temperature_tiles')) {
          const tempStore = db.createObjectStore('temperature_tiles', { keyPath: 'id' });
          tempStore.createIndex('coordinates', ['z', 'x', 'y'], { unique: false });
          console.log('🌡️ Tạo store: temperature_tiles');
        }
        
        // Store cho terrain tiles (terrain-rgb-v2)
        if (!db.objectStoreNames.contains('terrain_tiles')) {
          const terrainStore = db.createObjectStore('terrain_tiles', { keyPath: 'id' });
          terrainStore.createIndex('coordinates', ['z', 'x', 'y'], { unique: false });
          console.log('⛰️ Tạo store: terrain_tiles');
        }
        
        // Store cho basic/border tiles (pbf)
        if (!db.objectStoreNames.contains('vector_tiles')) {
          const vectorStore = db.createObjectStore('vector_tiles', { keyPath: 'id' });
          vectorStore.createIndex('tileType', 'tileType', { unique: false });
          vectorStore.createIndex('coordinates', ['z', 'x', 'y'], { unique: false });
          console.log('🗺️ Tạo store: vector_tiles');
        }
      };
    });
  }

  // Lưu metadata vào IndexedDB
  async saveMetadata(name, data) {
    if (!this.db) await this.initIndexedDB();
    
    const transaction = this.db.transaction(['metadata'], 'readwrite');
    const store = transaction.objectStore('metadata');
    
    const metadataRecord = {
      name: name,
      data: data,
      timestamp: new Date().toISOString()
    };
    
    await new Promise((resolve, reject) => {
      const request = store.put(metadataRecord);
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
    
    console.log(`💾 Đã lưu metadata: ${name}`);
  }

  // Lưu scene metadata
  async saveSceneMetadata(sceneMetadata) {
    if (!this.db) await this.initIndexedDB();
    
    const transaction = this.db.transaction(['scenes'], 'readwrite');
    const store = transaction.objectStore('scenes');
    
    await new Promise((resolve, reject) => {
      const request = store.put(sceneMetadata);
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
    
    console.log(`🎬 Đã lưu scene metadata: ${sceneMetadata.sceneName}`);
  }

  // Lưu wind tile
  async saveWindTile(datasetId, tile, data, url) {
    if (!this.db) await this.initIndexedDB();
    
    const transaction = this.db.transaction(['wind_tiles'], 'readwrite');
    const store = transaction.objectStore('wind_tiles');
    
    const tileRecord = {
      id: `${datasetId}_${tile.z}_${tile.x}_${tile.y}`,
      datasetId: datasetId,
      z: tile.z,
      x: tile.x,
      y: tile.y,
      data: data,
      url: url,
      timestamp: new Date().toISOString()
    };
    
    await new Promise((resolve, reject) => {
      const request = store.put(tileRecord);
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  // Lưu temperature tile
  async saveTemperatureTile(tile, data, url) {
    if (!this.db) await this.initIndexedDB();
    
    const transaction = this.db.transaction(['temperature_tiles'], 'readwrite');
    const store = transaction.objectStore('temperature_tiles');
    
    const tileRecord = {
      id: `terrain-rgb_${tile.z}_${tile.x}_${tile.y}`,
      z: tile.z,
      x: tile.x,
      y: tile.y,
      data: data,
      url: url,
      timestamp: new Date().toISOString()
    };
    
    await new Promise((resolve, reject) => {
      const request = store.put(tileRecord);
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  // Lưu terrain tile
  async saveTerrainTile(tile, data, url) {
    if (!this.db) await this.initIndexedDB();
    
    const transaction = this.db.transaction(['terrain_tiles'], 'readwrite');
    const store = transaction.objectStore('terrain_tiles');
    
    const tileRecord = {
      id: `terrain-rgb-v2_${tile.z}_${tile.x}_${tile.y}`,
      z: tile.z,
      x: tile.x,
      y: tile.y,
      data: data,
      url: url,
      timestamp: new Date().toISOString()
    };
    
    await new Promise((resolve, reject) => {
      const request = store.put(tileRecord);
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  // Lưu vector tile (basic/border)
  async saveVectorTile(tileType, tile, data, url) {
    if (!this.db) await this.initIndexedDB();
    
    const transaction = this.db.transaction(['vector_tiles'], 'readwrite');
    const store = transaction.objectStore('vector_tiles');
    
    const tileRecord = {
      id: `${tileType}_${tile.z}_${tile.x}_${tile.y}`,
      tileType: tileType,
      z: tile.z,
      x: tile.x,
      y: tile.y,
      data: data,
      url: url,
      timestamp: new Date().toISOString()
    };
    
    await new Promise((resolve, reject) => {
      const request = store.put(tileRecord);
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  // Kiểm tra thông tin storage
  async getStorageInfo() {
    if (!this.db) await this.initIndexedDB();
    
    const storeNames = ['metadata', 'scenes', 'wind_tiles', 'temperature_tiles', 'terrain_tiles', 'vector_tiles'];
    const info = {};
    
    for (const storeName of storeNames) {
      const transaction = this.db.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      
      const count = await new Promise((resolve) => {
        const request = store.count();
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => resolve(0);
      });
      
      info[storeName] = { count };
    }
    
    return info;
  }

  // Hiển thị thông tin storage
  async showStorageInfo() {
    console.log('\n📊 ===== THÔNG TIN INDEXEDDB =====');
    const info = await this.getStorageInfo();
    
    for (const [storeName, data] of Object.entries(info)) {
      console.log(`📦 ${storeName}: ${data.count} records`);
    }
    
    return info;
  }
}

// Export để sử dụng
window.CompleteOfflineDownloader = CompleteOfflineDownloader;

// Hàm tiện ích để test
window.testLongSave = async function(sceneName, map) {
  const downloader = new CompleteOfflineDownloader();
  return await downloader.performLongSave(sceneName || 'Scene1', map);
};

// Hàm kiểm tra storage
window.showStorageInfo = async function() {
  const downloader = new CompleteOfflineDownloader();
  return await downloader.showStorageInfo();
}; 