// LongSave-loader.js
// Chỉ dùng để load scene dạng Long Save từ IndexedDB (MapTilerOfflineCache)
// Không fetch online, không dính localStorage, không dính logic cũ

const LONGSAVE_DB_NAME = 'MapTilerOfflineCache';
const LONGSAVE_DB_VERSION = 5;
const SCENE_STORE = 'scenes';

/**
 * Load scene metadata từ IndexedDB theo sceneName
 * @param {string} sceneName - Tên scene (ví dụ: 'Offline 1')
 * @returns {Promise<object|null>} - Resolve object scene hoặc null nếu không có
 */
function loadLongSaveScene(sceneName) {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open(LONGSAVE_DB_NAME, LONGSAVE_DB_VERSION);
    request.onerror = () => reject(request.error);
    request.onsuccess = () => {
      const db = request.result;
      const tx = db.transaction([SCENE_STORE], 'readonly');
      const store = tx.objectStore(SCENE_STORE);
      const getReq = store.get(sceneName);
      getReq.onsuccess = () => {
        resolve(getReq.result || null);
      };
      getReq.onerror = () => reject(getReq.error);
    };
  });
}

// Export global cho dễ test
window.loadLongSaveScene = loadLongSaveScene; 