/**
 * Offline Wind Layer - Custom WindLayer sử dụng cached wind tiles
 * Hoạt động hoàn toàn offline mà không cần MapTiler API
 */
class OfflineWindLayer {
  constructor(options = {}) {
    this.id = options.id || 'offline-wind';
    this.windDecoder = null;
    this.map = null;
    this.isReady = false;
    this.isVisible = true;
    this.currentSceneId = null;
    
    // Wind visualization options
    this.particleOptions = {
      speed: options.speed || 0.002,
      fadeFactor: options.fadeFactor || 0.03,
      maxAmount: options.maxAmount || 256,
      density: options.density || 400,
      color: options.color || [255, 0, 0, 30],
      fastColor: options.fastColor || [0, 255, 0, 100],
      ...options
    };
    
    this.events = {};
  }

  async onAdd(map, gl) {
    this.map = map;
    this.gl = gl;
    
    try {
      // Initialize wind decoder với current scene ID
      this.windDecoder = new window.OfflineWindDecoder(this.currentSceneId);
      await this.windDecoder.init();
      
      // Check if we have wind data available
      const availableDatasets = this.windDecoder.getAvailableDatasets();
      if (availableDatasets.length === 0) {
        console.warn('⚠️ No cached wind datasets found for offline wind layer');
        this.emit('no-data', { sceneId: this.currentSceneId });
      } else {
        console.log(`✅ Offline Wind Layer ready with ${availableDatasets.length} datasets:`, availableDatasets);
        this.emit('datasets-found', { datasets: availableDatasets, sceneId: this.currentSceneId });
      }
      
      this.isReady = true;
      this.emit('sourceReady', { map, layer: this });
      
    } catch (error) {
      console.error('❌ Offline Wind Layer failed to initialize:', error);
      this.emit('error', { error, sceneId: this.currentSceneId });
    }
  }

  onRemove() {
    this.isReady = false;
    this.windDecoder = null;
    this.map = null;
  }

  /**
   * Set scene ID để load wind data từ cached tiles
   */
  async setSceneId(sceneId) {
    this.currentSceneId = sceneId;
    console.log(`🌪️ Wind layer switching to scene: ${sceneId}`);
    
    // Reinitialize decoder với new scene
    if (this.windDecoder) {
      try {
        this.windDecoder = new window.OfflineWindDecoder(sceneId);
        await this.windDecoder.init();
        
        const availableDatasets = this.windDecoder.getAvailableDatasets();
        console.log(`🔄 Scene ${sceneId} loaded - ${availableDatasets.length} wind datasets available`);
        this.emit('scene-changed', { sceneId, datasets: availableDatasets });
        
      } catch (error) {
        console.error(`❌ Failed to switch to scene ${sceneId}:`, error);
        this.emit('scene-error', { sceneId, error });
      }
    }
  }

  /**
   * Pick wind data tại một location
   */
  async pickAt(lng, lat, options = {}) {
    if (!this.windDecoder || !this.map) return null;
    
    try {
      const zoom = Math.floor(this.map.getZoom());
      const windData = await this.windDecoder.decodeWindAt(lng, lat, zoom);
      
      if (windData) {
        return {
          speedMetersPerSecond: windData.speedMetersPerSecond,
          speedKilometersPerHour: windData.speedKilometersPerHour,
          speedMilesPerHour: windData.speedMilesPerHour,
          speedFeetPerSecond: windData.speedFeetPerSecond,
          speedKnots: windData.speedKnots,
          directionAngle: windData.directionAngle,
          compassDirection: windData.compassDirection
        };
      }
      
      return null;
    } catch (error) {
      console.error('Wind pick failed:', error);
      return null;
    }
  }

  /**
   * Set visibility
   */
  setVisible(visible) {
    this.isVisible = visible;
    if (this.map) {
      const visibility = visible ? 'visible' : 'none';
      try {
        this.map.setLayoutProperty(this.id, 'visibility', visibility);
      } catch (error) {
        // Layer might not be added to map yet
        console.warn('Failed to set wind layer visibility:', error);
      }
    }
  }

  /**
   * Animation time methods (for compatibility)
   */
  setAnimationTime(time) {
    // Store current animation time
    this.currentAnimationTime = time;
    this.emit('animationTimeSet', { time });
  }

  getAnimationTime() {
    return this.currentAnimationTime || Date.now() / 1000;
  }

  getAnimationStartDate() {
    // Return a date 24 hours ago (placeholder)
    return new Date(Date.now() - 24 * 60 * 60 * 1000);
  }

  getAnimationEndDate() {
    // Return current date (placeholder)
    return new Date();
  }

  getAnimationTimeDate() {
    return new Date(this.getAnimationTime() * 1000);
  }

  /**
   * Play/pause methods (for compatibility)
   */
  play() {
    this.isPlaying = true;
    this.emit('play', {});
  }

  pause() {
    this.isPlaying = false;
    this.emit('pause', {});
  }

  isPlaying() {
    return this.isPlaying || false;
  }

  /**
   * Event system
   */
  on(event, callback) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(callback);
  }

  emit(event, data) {
    if (this.events[event]) {
      this.events[event].forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Event callback error for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Get source ready status
   */
  getIsSourceReady() {
    return this.isReady;
  }

  /**
   * Create offline wind layer source cho map
   */
  static createOfflineSource(sceneId) {
    return {
      type: 'geojson',
      data: {
        type: 'FeatureCollection',
        features: []
      }
    };
  }

  /**
   * Static method để tạo offline wind layer từ cached scene
   */
  static async createFromScene(sceneId, options = {}) {
    const layer = new OfflineWindLayer({
      id: `offline-wind-${sceneId}`,
      ...options
    });
    
    layer.setSceneId(sceneId);
    return layer;
  }

  /**
   * Simulate particle animation bằng canvas overlay
   */
  createParticleOverlay() {
    if (!this.map) return;
    
    const canvas = document.createElement('canvas');
    canvas.style.position = 'absolute';
    canvas.style.top = '0';
    canvas.style.left = '0';
    canvas.style.pointerEvents = 'none';
    canvas.style.zIndex = '1';
    
    const container = this.map.getContainer();
    container.appendChild(canvas);
    
    // Resize canvas khi map resize
    const resizeCanvas = () => {
      canvas.width = container.clientWidth;
      canvas.height = container.clientHeight;
    };
    
    resizeCanvas();
    this.map.on('resize', resizeCanvas);
    
    // Simple particle animation
    const ctx = canvas.getContext('2d');
    const particles = [];
    
    // Create initial particles
    for (let i = 0; i < this.particleOptions.maxAmount; i++) {
      particles.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        vx: (Math.random() - 0.5) * 2,
        vy: (Math.random() - 0.5) * 2,
        life: Math.random()
      });
    }
    
    // Animation loop
    const animate = () => {
      if (!this.isVisible) {
        requestAnimationFrame(animate);
        return;
      }
      
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      particles.forEach(particle => {
        // Update position
        particle.x += particle.vx * this.particleOptions.speed * 1000;
        particle.y += particle.vy * this.particleOptions.speed * 1000;
        
        // Wrap around edges
        if (particle.x < 0) particle.x = canvas.width;
        if (particle.x > canvas.width) particle.x = 0;
        if (particle.y < 0) particle.y = canvas.height;
        if (particle.y > canvas.height) particle.y = 0;
        
        // Update life
        particle.life -= this.particleOptions.fadeFactor;
        if (particle.life <= 0) {
          particle.life = 1;
          particle.x = Math.random() * canvas.width;
          particle.y = Math.random() * canvas.height;
        }
        
        // Draw particle
        const [r, g, b, a] = this.particleOptions.color;
        ctx.fillStyle = `rgba(${r}, ${g}, ${b}, ${a * particle.life / 255})`;
        ctx.fillRect(particle.x, particle.y, 2, 2);
      });
      
      requestAnimationFrame(animate);
    };
    
    animate();
    return canvas;
  }

  /**
   * Compatibility method for existing code
   */
  async onSourceReadyAsync() {
    return new Promise((resolve) => {
      if (this.isReady) {
        resolve();
      } else {
        this.on('sourceReady', () => resolve());
      }
    });
  }
}

// Export for global use
window.OfflineWindLayer = OfflineWindLayer; 