<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MapLibre GL JS với MapTiler - Simple Backup</title>
    
    <!-- MapLibre GL JS CSS -->
    <script src='https://unpkg.com/maplibre-gl@latest/dist/maplibre-gl.js'></script>
    <link href='https://unpkg.com/maplibre-gl@latest/dist/maplibre-gl.css' rel='stylesheet' />
    
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        #map {
            width: 100%;
            height: 500px;
            border: 1px solid #ccc;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .status {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .weather-controls {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .layer-toggle {
            margin: 10px;
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .layer-toggle.active {
            background: #007cba;
            color: white;
        }
        
        .layer-toggle.inactive {
            background: #ddd;
            color: #666;
        }
        
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="status">
        <h1>🗺️ MapLibre GL JS với MapTiler Style - BACKUP VERSION</h1>
        <div id="status">🔄 Initializing...</div>
    </div>
    
    <div class="weather-controls">
        <h3>🌤️ Weather Layers (Future)</h3>
        <button class="layer-toggle inactive" id="temp-toggle">🌡️ Temperature</button>
        <button class="layer-toggle inactive" id="wind-toggle">💨 Wind</button>
        <button class="layer-toggle inactive" id="rain-toggle">🌧️ Precipitation</button>
        <button class="layer-toggle inactive" id="pressure-toggle">🌀 Pressure</button>
        <button class="layer-toggle inactive" id="radar-toggle">📡 Radar</button>
    </div>
    
    <div id="map"></div>

    <script>
        console.log('🚀 MapLibre GL JS test started...');
        
        const statusEl = document.getElementById('status');
        
        function updateStatus(message, type = 'info') {
            console.log(message);
            statusEl.innerHTML += `<div class="${type}">📍 ${message}</div>`;
        }
        
        try {
            updateStatus('Initializing MapLibre GL JS...', 'info');
            
            // Kiểm tra MapLibre GL JS có sẵn không
            if (typeof maplibregl === 'undefined') {
                throw new Error('MapLibre GL JS không được load');
            }
            
            updateStatus('✅ MapLibre GL JS loaded successfully!', 'success');
            
            // Tạo map với MapTiler style (giống như trong script.js)
            const mapStyle = `https://api.maptiler.com/maps/01962ffa-24fd-7deb-9deb-99a5ed9cca6e/style.json?key=oinoatcrNmdCL1524DOl`;
            
            updateStatus('Creating map with MapTiler style...', 'info');
            
            const map = new maplibregl.Map({
                container: 'map',
                style: mapStyle,
                center: [105.8542, 21.0285], // Hanoi, Vietnam [longitude, latitude]
                zoom: 6,
                pitch: 0, // Start flat
                bearing: 0,
                antialias: true
            });
            
            updateStatus('✅ Map created!', 'success');
            
            // Add controls
            map.addControl(new maplibregl.NavigationControl({
                visualizePitch: true
            }), 'top-left');
            
            map.addControl(new maplibregl.ScaleControl({
                maxWidth: 100,
                unit: 'metric'
            }), 'bottom-right');
            
            updateStatus('✅ Controls added!', 'success');
            
            // Map event listeners
            map.on('load', () => {
                updateStatus('🎉 Map loaded successfully!', 'success');
                updateStatus('🌏 Hiển thị bản đồ Việt Nam thành công!', 'success');
                updateStatus('🌤️ Ready for weather layers!', 'info');
            });
            
            map.on('error', (error) => {
                updateStatus(`❌ Map error: ${error.error?.message || error}`, 'error');
            });
            
            // Add click handler for coordinates
            map.on('click', (e) => {
                const { lng, lat } = e.lngLat;
                updateStatus(`📍 Clicked: ${lat.toFixed(4)}, ${lng.toFixed(4)}`, 'info');
            });
            
            // Setup layer toggle buttons (for future weather implementation)
            const toggleButtons = document.querySelectorAll('.layer-toggle');
            toggleButtons.forEach(button => {
                button.addEventListener('click', () => {
                    if (button.classList.contains('active')) {
                        button.classList.remove('active');
                        button.classList.add('inactive');
                    } else {
                        button.classList.remove('inactive');
                        button.classList.add('active');
                    }
                    
                    const layerType = button.id.replace('-toggle', '');
                    updateStatus(`🔄 ${layerType} layer ${button.classList.contains('active') ? 'enabled' : 'disabled'}`, 'info');
                    
                    // Future: Add actual weather layer logic here
                });
            });
            
            // Test marker
            setTimeout(() => {
                const marker = new maplibregl.Marker({ color: 'red' })
                    .setLngLat([105.8542, 21.0285])
                    .setPopup(new maplibregl.Popup().setHTML('<h3>Hà Nội</h3><p>Thủ đô Việt Nam</p>'))
                    .addTo(map);
                
                updateStatus('✅ Test marker added at Hanoi!', 'success');
            }, 2000);
            
        } catch (error) {
            updateStatus(`❌ Error: ${error.message}`, 'error');
            console.error('MapLibre error:', error);
        }
    </script>
</body>
</html> 