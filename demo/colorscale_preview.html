<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>g màu nhi<PERSON>t độ sáng và rực rỡ</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .colorscale-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .color-bar {
            display: flex;
            height: 40px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .color-segment {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(255,255,255,0.8);
            font-size: 10px;
            transition: transform 0.2s;
        }
        
        .color-segment:hover {
            transform: scaleY(1.1);
            z-index: 10;
            position: relative;
        }
        
        .legend {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            font-size: 16px;
            font-weight: bold;
        }
        
        .temp-label {
            background: rgba(0,0,0,0.1);
            padding: 8px 16px;
            border-radius: 20px;
            color: #333;
        }
        
        .color-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 30px;
        }
        
        .color-item {
            background: white;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        
        .color-item:hover {
            transform: translateY(-5px);
        }
        
        .color-preview {
            width: 100%;
            height: 60px;
            border-radius: 8px;
            margin-bottom: 10px;
            border: 2px solid #eee;
        }
        
        .color-info {
            text-align: center;
        }
        
        .temp-range {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .color-code {
            font-family: monospace;
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌡️ Thang màu nhiệt độ sáng và rực rỡ</h1>
        
        <div class="legend">
            <div class="temp-label">❄️ Lạnh (-73.3°C)</div>
            <div class="temp-label">🌡️ Trung bình</div>
            <div class="temp-label">🔥 Nóng (65.6°C)</div>
        </div>
        
        <div class="colorscale-container">
            <div class="color-bar" id="colorBar"></div>
        </div>
        
        <div class="color-grid" id="colorGrid"></div>
    </div>

    <script>
        const colorScale = [
      // ==========================================================
      // Dải màu LẠNH (Từ xanh đậm đến xanh da trời)
      // Càng lạnh, màu càng đậm
      // ==========================================================
      { min: -73.3, max: -51.1, color: '#E5F0FF' },
      { min: -51.1, max: -48.3, color: '#DBE9FB' },
      { min: -48.3, max: -45.6, color: '#D3E2F7' },
      { min: -45.6, max: -42.8, color: '#CCDBF4' },
      { min: -42.8, max: -40.0, color: '#C0D4ED' },
      { min: -40.0, max: -37.2, color: '#B8CDEA' },
      { min: -37.2, max: -34.4, color: '#AFC6E7' },
      { min: -34.4, max: -31.7, color: '#A7BFE3' },
      { min: -31.7, max: -28.9, color: '#9CB8DE' },
      { min: -28.9, max: -26.1, color: '#93B1D7' },
      { min: -26.1, max: -23.3, color: '#89A5CD' },
      { min: -23.3, max: -20.6, color: '#7F9BC3' },
      { min: -20.6, max: -17.8, color: '#7591B9' },
      { min: -17.8, max: -15.0, color: '#607BA6' },
      { min: -15.0, max: -12.2, color: '#56719D' },
      { min: -12.2, max: -9.4, color: '#4D6591' },
      { min: -9.4, max: -6.7, color: '#415C88' },
      { min: -6.7, max: -3.9, color: '#39517F' },
      { min: -3.9, max: -1.1, color: '#2F4775' },
      { min: -1.1, max: 1.7, color: '#26426F' },
      { min: 1.7, max: 4.4, color: '#254F77' },
      { min: 4.4, max: 7.2, color: '#295B7D' },
      { min: 7.2, max: 10.0, color: '#276889' },
      { min: 10.0, max: 12.8, color: '#227891' },
      { min: 12.8, max: 15.6, color: '#438190' },
      { min: 15.6, max: 18.3, color: '#648D89' },
      { min: 18.3, max: 21.1, color: '#869B83' },
      { min: 21.1, max: 23.9, color: '#AAA87D' },
      { min: 23.9, max: 26.7, color: '#C2AC76' },
      { min: 26.7, max: 29.4, color: '#C29D61' },
      { min: 29.4, max: 32.2, color: '#C38A54' },
      { min: 32.2, max: 35.0, color: '#BE704D' },
      { min: 35.0, max: 37.8, color: '#AF4D4C' },
      { min: 37.8, max: 40.6, color: '#9F294C' },
      { min: 40.6, max: 43.3, color: '#86203E' },
      { min: 43.3, max: 46.1, color: '#6E1531' },
      { min: 46.1, max: 48.9, color: '#570C25' },
      { min: 48.9, max: 65.6, color: '#3D0216' }
];

        // Tạo thanh màu liên tục
        const colorBar = document.getElementById('colorBar');
        colorScale.forEach((item, index) => {
            const segment = document.createElement('div');
            segment.className = 'color-segment';
            segment.style.backgroundColor = item.color;
            segment.textContent = `${item.min.toFixed(1)}°`;
            segment.title = `${item.min}°C đến ${item.max}°C`;
            colorBar.appendChild(segment);
        });

        // Tạo lưới màu chi tiết
        const colorGrid = document.getElementById('colorGrid');
        colorScale.forEach((item, index) => {
            const colorItem = document.createElement('div');
            colorItem.className = 'color-item';
            
            colorItem.innerHTML = `
                <div class="color-preview" style="background-color: ${item.color}"></div>
                <div class="color-info">
                    <div class="temp-range">${item.min}°C → ${item.max}°C</div>
                    <div class="color-code">${item.color}</div>
                </div>
            `;
            
            colorGrid.appendChild(colorItem);
        });
    </script>
</body>
</html>