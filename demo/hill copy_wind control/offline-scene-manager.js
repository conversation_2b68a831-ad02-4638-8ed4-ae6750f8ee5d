/**
 * Offline Scene Manager for Weather App
 */
class OfflineSceneManager {
  constructor() {
    this.db = null;
    this.isProcessing = false;
    this.tempTiles = new Map();
    
    // Constants
    this.MAX_SCENES = 5;
    this.SHORT_SAVE_TTL = 3 * 60 * 60 * 1000; // 3 hours
    this.TILE_SIZE_ESTIMATE = 50 * 1024; // 50KB
    
    this.init();
  }

  async init() {
    try {
      await this.initIndexedDB();
      await this.registerServiceWorker();
      await this.cleanupExpiredShortSaves();
      console.log('✅ Offline Scene Manager ready');
    } catch (error) {
      console.error('❌ Init failed:', error);
    }
  }

  async initIndexedDB() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('WeatherAppOffline', 2);
      
      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };
      
      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        
        // Scenes table
        if (!db.objectStoreNames.contains('scenes')) {
          const scenesStore = db.createObjectStore('scenes', { keyPath: 'id' });
          scenesStore.createIndex('type', 'type', { unique: false });
        }
        
        // Global tile pool
        if (!db.objectStoreNames.contains('global_tile_pool')) {
          const tilesStore = db.createObjectStore('global_tile_pool', { keyPath: 'url' });
          tilesStore.createIndex('refCount', 'refCount', { unique: false });
        }

        // Wind tiles store  
        if (!db.objectStoreNames.contains('wind_tiles')) {
          const windTilesStore = db.createObjectStore('wind_tiles', { keyPath: 'url' });
          windTilesStore.createIndex('sceneId', 'sceneId', { unique: false });
          windTilesStore.createIndex('datasetId', 'datasetId', { unique: false });
        }
      };
    });
  }

  async registerServiceWorker() {
    if (!('serviceWorker' in navigator)) return;
    
    try {
      await navigator.serviceWorker.register('/demo/tile-cache-worker.js');
      console.log('✅ Service Worker registered');
    } catch (error) {
      console.warn('Service Worker failed:', error);
    }
  }

  async cleanupExpiredShortSaves() {
    if (!this.db) return;
    
    try {
      const transaction = this.db.transaction(['scenes'], 'readwrite');
      const store = transaction.objectStore('scenes');
      const index = store.index('type');
      const request = index.getAll('short');
      
      request.onsuccess = () => {
        const shortScenes = request.result;
        const now = Date.now();
        
        shortScenes.forEach(scene => {
          if (now - scene.timestamp > this.SHORT_SAVE_TTL) {
            console.log(`🗑️ Cleaning expired: ${scene.id}`);
            store.delete(scene.id);
            this.updateSceneButtonUI(scene.id, 'empty');
          }
        });
      };
    } catch (error) {
      console.error('Cleanup failed:', error);
    }
  }

  // Save scene with short save (metadata only)
  async saveShortScene(sceneId, sceneName, mapState) {
    if (!this.db) return false;
    
    try {
      const sceneData = {
        id: sceneId,
        type: 'short',
        name: sceneName,
        timestamp: Date.now(),
        mapState: mapState
      };

      const transaction = this.db.transaction(['scenes'], 'readwrite');
      const store = transaction.objectStore('scenes');
      await this.putInStore(store, sceneData);

      this.updateSceneButtonUI(sceneId, 'short', sceneName);
      
      // Add timeline marker
      if (window.timelineManager) {
        window.timelineManager.addSceneMarker(sceneId, 'short', mapState.time, sceneName);
      }
      
      this.showToast('Short save completed', 'success');
      return true;
    } catch (error) {
      console.error('Short save failed:', error);
      this.showToast('Save failed', 'error');
      return false;
    }
  }

  // Save scene with long save (metadata + tiles + wind data)
  async saveLongScene(sceneId, sceneName, mapState, map, activeLayers) {
    if (this.isProcessing || !this.db) return false;
    
    this.isProcessing = true;
    let cancelled = false;

    try {
      // Get viewport tiles
      const tileUrls = this.getViewportTiles(map, activeLayers);
      console.log(`📦 Processing ${tileUrls.length} basic tiles`);

      // *** NEW: Calculate wind tiles estimate ***
      const windTilesEstimate = this.estimateWindTiles(map);
      const totalTiles = tileUrls.length + windTilesEstimate;
      console.log(`🌪️ + ${windTilesEstimate} wind tiles = ${totalTiles} total`);

      // Check storage quota (including wind tiles)
      const hasQuota = await this.checkStorageQuota(totalTiles);
      if (!hasQuota) {
        this.showToast('Storage full - clear a scene', 'error');
        return false;
      }

      // Show modal với total tiles count
      const modal = this.showSaveModal(totalTiles, () => { cancelled = true; });
      
      // Process basic tiles
      let processed = 0;
      for (const tileUrl of tileUrls) {
        if (cancelled) break;
        
        await this.processTileForLongSave(tileUrl, sceneId);
        processed++;
        this.updateProgressModal(modal, processed, totalTiles, 'Basic tiles');
      }

      // *** NEW: Process wind tiles ***
      if (!cancelled) {
        this.updateProgressModalStatus(modal, 'Extracting wind dataset info...');
        
        // Extract dataset info từ active WindLayer
        const windDatasets = await this.extractWindDatasets();
        
        if (windDatasets && windDatasets.length > 0) {
          console.log(`🌪️ Found ${windDatasets.length} wind datasets:`, windDatasets);
          
          this.updateProgressModalStatus(modal, 'Downloading wind tiles...');
          const windSuccess = await this.downloadWindTilesWithDatasets(map, sceneId, windDatasets);
          
          if (windSuccess) {
            processed += windTilesEstimate;
            this.updateProgressModal(modal, processed, totalTiles, 'Wind tiles');
            console.log('✅ Wind tiles downloaded successfully');
          } else {
            console.warn('⚠️ Wind tiles download failed, continuing...');
          }
        } else {
          console.warn('⚠️ No wind datasets found, skipping wind tiles...');
        }
      }

      if (cancelled) {
        await this.revertTempTiles(sceneId);
        this.hideSaveModal(modal);
        this.showToast('Save cancelled', 'info');
        return false;
      }

      // Finalize
      await this.finalizeLongSave(sceneId, sceneName, mapState, tileUrls);
      this.hideSaveModal(modal);
      this.updateSceneButtonUI(sceneId, 'long', sceneName);
      
      // Add timeline marker
      if (window.timelineManager) {
        window.timelineManager.addSceneMarker(sceneId, 'long', mapState.time, sceneName);
      }
      
      this.showToast('Long save completed (with wind data)', 'success');
      return true;

    } catch (error) {
      console.error('Long save failed:', error);
      this.showToast('Save failed', 'error');
      return false;
    } finally {
      this.tempTiles.clear();
      this.isProcessing = false;
    }
  }

  getViewportTiles(map, activeLayers) {
    const bounds = map.getBounds();
    const zoom = Math.floor(map.getZoom());
    const tiles = [];

    const ne = bounds.getNorthEast();
    const sw = bounds.getSouthWest();
    const scale = Math.pow(2, zoom);
    
    const minX = Math.floor((sw.lng + 180) / 360 * scale);
    const maxX = Math.floor((ne.lng + 180) / 360 * scale);
    const minY = Math.floor((1 - Math.log(Math.tan(ne.lat * Math.PI / 180) + 1 / Math.cos(ne.lat * Math.PI / 180)) / Math.PI) / 2 * scale);
    const maxY = Math.floor((1 - Math.log(Math.tan(sw.lat * Math.PI / 180) + 1 / Math.cos(sw.lat * Math.PI / 180)) / Math.PI) / 2 * scale);

    activeLayers.forEach(layer => {
      for (let x = minX; x <= maxX; x++) {
        for (let y = minY; y <= maxY; y++) {
          tiles.push(`${layer}/${zoom}/${x}/${y}`);
        }
      }
    });

    return tiles;
  }

  async checkStorageQuota(estimatedTiles) {
    try {
      if ('storage' in navigator && 'estimate' in navigator.storage) {
        const estimate = await navigator.storage.estimate();
        const available = (estimate.quota || 0) - (estimate.usage || 0);
        const needed = estimatedTiles * this.TILE_SIZE_ESTIMATE;
        return available > needed * 1.2;
      }
      return true;
    } catch (error) {
      return true;
    }
  }

  async processTileForLongSave(tileUrl, sceneId) {
    try {
      const transaction = this.db.transaction(['global_tile_pool'], 'readwrite');
      const store = transaction.objectStore('global_tile_pool');
      const existing = await this.getFromStore(store, tileUrl);

      if (existing) {
        existing.refCount++;
        if (!existing.usedByScenes.includes(sceneId)) {
          existing.usedByScenes.push(sceneId);
        }
        this.tempTiles.set(tileUrl, existing);
      } else {
        const tileData = await this.fetchTileData(tileUrl);
        const tileRecord = {
          url: tileUrl,
          data: tileData,
          refCount: 1,
          usedByScenes: [sceneId]
        };
        this.tempTiles.set(tileUrl, tileRecord);
      }
    } catch (error) {
      console.error(`Tile processing failed: ${tileUrl}`, error);
    }
  }

  async fetchTileData(tileUrl) {
    // Simulate tile fetching - replace with actual implementation
    return new ArrayBuffer(this.TILE_SIZE_ESTIMATE);
  }

  async finalizeLongSave(sceneId, sceneName, mapState, tileUrls) {
    const transaction = this.db.transaction(['scenes', 'global_tile_pool'], 'readwrite');
    const scenesStore = transaction.objectStore('scenes');
    const tilesStore = transaction.objectStore('global_tile_pool');

    // Save tiles
    for (const [url, tileData] of this.tempTiles) {
      await this.putInStore(tilesStore, tileData);
    }

    // Save scene
    const sceneData = {
      id: sceneId,
      type: 'long',
      name: sceneName,
      timestamp: Date.now(),
      mapState: { ...mapState, tileUrls }
    };
    await this.putInStore(scenesStore, sceneData);
  }

  async revertTempTiles(sceneId) {
    console.log('Reverting temp tiles...');
  }

  async deleteScene(sceneId) {
    if (!this.db) return;
    
    try {
      const transaction = this.db.transaction(['scenes', 'global_tile_pool'], 'readwrite');
      const scenesStore = transaction.objectStore('scenes');
      
      const scene = await this.getFromStore(scenesStore, sceneId);
      if (scene) {
        await scenesStore.delete(sceneId);
        this.updateSceneButtonUI(sceneId, 'empty');
        
        // Remove timeline marker
        if (window.timelineManager) {
          window.timelineManager.removeSceneMarker(sceneId);
        }
        
        this.showToast('Scene deleted', 'info');
      }
    } catch (error) {
      console.error('Delete failed:', error);
    }
  }

  async loadScene(sceneId) {
    if (!this.db) return null;
    
    try {
      const transaction = this.db.transaction(['scenes', 'wind_tiles'], 'readonly');
      const scenesStore = transaction.objectStore('scenes');
      const windStore = transaction.objectStore('wind_tiles');
      
      const scene = await this.getFromStore(scenesStore, sceneId);
      
      if (scene) {
        console.log(`📦 Loading scene from IndexedDB: ${scene.name} (${scene.type})`);
        
        // Check for offline wind data
        const windTiles = await this.getWindTilesForScene(windStore, sceneId);
        const windDatasets = new Set();
        windTiles.forEach(tile => {
          if (tile.datasetId) windDatasets.add(tile.datasetId);
        });
        
        if (windDatasets.size > 0) {
          console.log(`🌪️ Scene has ${windTiles.length} wind tiles from ${windDatasets.size} datasets`);
          console.log(`📊 Wind datasets:`, Array.from(windDatasets));
          
          // Add wind info to scene metadata
          scene.windInfo = {
            tilesCount: windTiles.length,
            datasets: Array.from(windDatasets),
            hasOfflineWind: true
          };
        } else {
          console.log(`📊 No offline wind data found for scene ${sceneId}`);
          scene.windInfo = {
            hasOfflineWind: false
          };
        }
        
        return scene.mapState; // Return the mapState for compatibility
      }
      
      return null;
    } catch (error) {
      console.error('Load scene failed:', error);
      return null;
    }
  }

  async getWindTilesForScene(windStore, sceneId) {
    return new Promise((resolve, reject) => {
      const index = windStore.index('sceneId');
      const request = index.getAll(sceneId);
      
      request.onsuccess = () => {
        resolve(request.result || []);
      };
      
      request.onerror = () => {
        reject(request.error);
      };
    });
  }

  async loadSavedScenesUI() {
    if (!this.db) return;
    
    try {
      const transaction = this.db.transaction(['scenes'], 'readonly');
      const store = transaction.objectStore('scenes');
      const allScenes = await this.getAllFromStore(store);
      
      console.log(`📦 Found ${allScenes.length} scenes in IndexedDB:`, allScenes);
      
      // Update UI for each scene
      allScenes.forEach(scene => {
        this.updateSceneButtonUI(scene.id, scene.type, scene.name);
        console.log(`📦 Updated UI for ${scene.id}: ${scene.name} (${scene.type})`);
      });
      
    } catch (error) {
      console.error('Load saved scenes UI failed:', error);
    }
  }

  async loadTimelineMarkers(timelineManager) {
    if (!this.db || !timelineManager) return;
    
    try {
      const transaction = this.db.transaction(['scenes'], 'readonly');
      const store = transaction.objectStore('scenes');
      const allScenes = await this.getAllFromStore(store);
      
      console.log(`📍 Loading ${allScenes.length} IndexedDB scenes to timeline...`);
      
      // Add timeline markers for each scene
      allScenes.forEach(scene => {
        if (scene.mapState && scene.mapState.time) {
          timelineManager.addSceneMarker(
            scene.id,
            scene.type, // 'short' or 'long'
            scene.mapState.time,
            scene.name
          );
          
          console.log(`📍 Added timeline marker: ${scene.name} (${scene.type}) at ${new Date(scene.mapState.time)}`);
        }
      });
      
    } catch (error) {
      console.error('Load timeline markers failed:', error);
    }
  }

  async clearAllScenes() {
    if (!this.db) return;
    
    try {
      console.log('🗑️ Clearing all offline scenes...');
      
      const transaction = this.db.transaction(['scenes', 'global_tile_pool'], 'readwrite');
      const scenesStore = transaction.objectStore('scenes');
      const tilesStore = transaction.objectStore('global_tile_pool');
      
      // Get all scenes first
      const allScenes = await this.getAllFromStore(scenesStore);
      
      // Delete all scenes
      for (const scene of allScenes) {
        await scenesStore.delete(scene.id);
        console.log(`🗑️ Deleted scene: ${scene.id}`);
      }
      
      // Clear all tiles (since no scenes reference them anymore)
      await tilesStore.clear();
      console.log('🗑️ Cleared all tiles from global pool');
      
      // Update UI for all scene buttons
      for (let i = 1; i <= this.MAX_SCENES; i++) {
        this.updateSceneButtonUI(`scene-${i}`, 'empty');
      }
      
      // Clear timeline markers
      if (window.timelineManager) {
        window.timelineManager.clearAllMarkers();
      }
      
      this.showToast('All offline scenes cleared', 'success');
      console.log('✅ All offline scenes cleared successfully');
      
    } catch (error) {
      console.error('Clear all failed:', error);
      this.showToast('Failed to clear scenes', 'error');
    }
  }

  // UI Methods
  showSaveModal(totalTiles, onCancel) {
    const modal = document.createElement('div');
    modal.className = 'offline-save-modal';
    modal.innerHTML = `
      <div class="modal-overlay" style="position:fixed;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,0.7);z-index:10000;display:flex;align-items:center;justify-content:center;">
        <div class="modal-content" style="background:white;padding:24px;border-radius:12px;max-width:400px;width:90%;">
          <h3>💾 Saving Scene Offline</h3>
          <p>Downloading ${totalTiles} tiles...</p>
          <div class="progress-container" style="margin:16px 0;">
            <div class="progress-bar" style="background:#eee;height:8px;border-radius:4px;overflow:hidden;">
              <div class="progress-fill" style="background:#4CAF50;height:100%;width:0%;transition:width 0.3s;"></div>
            </div>
            <div class="progress-text" style="margin-top:8px;">0 / ${totalTiles}</div>
          </div>
          <button class="cancel-btn" style="background:#f44336;color:white;border:none;padding:8px 16px;border-radius:4px;cursor:pointer;">Cancel</button>
        </div>
      </div>
    `;
    
    modal.querySelector('.cancel-btn').onclick = onCancel;
    document.body.appendChild(modal);
    return modal;
  }

  updateProgressModal(modal, completed, total, type = 'tiles') {
    const fill = modal.querySelector('.progress-fill');
    const text = modal.querySelector('.progress-text');
    fill.style.width = (completed / total * 100) + '%';
    text.textContent = `${completed} / ${total} ${type}`;
  }

  updateProgressModalStatus(modal, status) {
    const text = modal.querySelector('.progress-text');
    if (text) {
      text.textContent = status;
    }
  }

  estimateWindTiles(map) {
    const bounds = map.getBounds();
    const zoom = Math.floor(map.getZoom());
    const scale = Math.pow(2, zoom);
    
    const ne = bounds.getNorthEast();
    const sw = bounds.getSouthWest();
    
    const minX = Math.floor((sw.lng + 180) / 360 * scale);
    const maxX = Math.floor((ne.lng + 180) / 360 * scale);
    const minY = Math.floor((1 - Math.log(Math.tan(ne.lat * Math.PI / 180) + 1 / Math.cos(ne.lat * Math.PI / 180)) / Math.PI) / 2 * scale);
    const maxY = Math.floor((1 - Math.log(Math.tan(sw.lat * Math.PI / 180) + 1 / Math.cos(sw.lat * Math.PI / 180)) / Math.PI) / 2 * scale);

    const windTiles = (maxX - minX + 1) * (maxY - minY + 1);
    return windTiles;
  }

  /**
   * Extract wind dataset information từ active WindLayer
   */
  async extractWindDatasets() {
    try {
      // Try multiple methods để lấy dataset info
      
      // Method 1: Access global layer variable
      if (window.layer && window.layer.getIsSourceReady && window.layer.getIsSourceReady()) {
        console.log('🔍 Attempting to extract from global layer...');
        const datasets = await this.extractFromWindLayer(window.layer);
        if (datasets) return datasets;
      }

      // Method 2: Intercept network requests
      console.log('🔍 Attempting to extract from network requests...');
      const datasets = await this.extractFromNetworkRequests();
      if (datasets) return datasets;

      // Method 3: Direct WeatherDataHandler access
      console.log('🔍 Attempting direct WeatherDataHandler access...');
      return await this.extractFromWeatherDataHandler();

    } catch (error) {
      console.error('❌ Failed to extract wind datasets:', error);
      return null;
    }
  }

  /**
   * Extract dataset info từ WindLayer instance
   */
  async extractFromWindLayer(windLayer) {
    try {
      // Try accessing internal WeatherDataHandler
      if (typeof maptilerweather !== 'undefined' && maptilerweather.WeatherDataHandler) {
        const handler = maptilerweather.WeatherDataHandler;
        
        // Check if handler has data
        if (handler.hasData && handler.hasData()) {
          const windData = handler.getWindData();
          if (windData && windData.metadata) {
            return [{
              id: this.extractDatasetIdFromMetadata(windData.metadata),
              type: 'wind',
              metadata: windData.metadata
            }];
          }
        }
      }

      return null;
    } catch (error) {
      console.error('❌ Failed to extract from WindLayer:', error);
      return null;
    }
  }

  /**
   * Extract dataset ID từ metadata hoặc URLs
   */
  extractDatasetIdFromMetadata(metadata) {
    // Try extracting từ tile URLs
    if (metadata.tiles && metadata.tiles.length > 0) {
      const tileUrl = metadata.tiles[0];
      const match = tileUrl.match(/\/tiles\/([0-9a-f-]{36})\//);
      if (match) return match[1];
    }

    // Try extracting từ source URL
    if (metadata.source) {
      const match = metadata.source.match(/\/tiles\/([0-9a-f-]{36})\//);
      if (match) return match[1];
    }

    // Fallback: use hardcoded ID (not ideal)
    console.warn('⚠️ Using fallback dataset ID');
    return '0197cd81-8cf8-71e7-adaf-08a8555890eb';
  }

  /**
   * Extract datasets bằng monitoring network requests
   */
  async extractFromNetworkRequests() {
    return new Promise((resolve) => {
      const datasets = [];
      let requestCount = 0;
      const maxWait = 2000; // 2 seconds timeout

      // Override fetch để capture requests
      const originalFetch = window.fetch;
      window.fetch = function(url, options) {
        if (typeof url === 'string' && url.includes('api.maptiler.com/tiles/')) {
          const match = url.match(/\/tiles\/([0-9a-f-]{36})\//);
          if (match) {
            const datasetId = match[1];
            if (!datasets.find(d => d.id === datasetId)) {
              datasets.push({
                id: datasetId,
                type: 'wind', // Assume wind for now
                sourceUrl: url
              });
              console.log(`🎯 Captured dataset ID: ${datasetId}`);
            }
          }
        }
        return originalFetch.apply(this, arguments);
      };

      // Wait for requests or timeout
      setTimeout(() => {
        window.fetch = originalFetch; // Restore original fetch
        resolve(datasets.length > 0 ? datasets : null);
      }, maxWait);
    });
  }

  /**
   * Direct access to WeatherDataHandler
   */
  async extractFromWeatherDataHandler() {
    try {
      if (typeof maptilerweather !== 'undefined' && maptilerweather.WeatherDataHandler) {
        const handler = maptilerweather.WeatherDataHandler;
        
        if (handler.hasData && handler.hasData()) {
          const allData = [];
          
          // Try getting different weather variables
          const windData = handler.getWindData();
          if (windData) {
            allData.push({
              id: this.extractDatasetIdFromMetadata(windData.metadata),
              type: 'wind',
              metadata: windData.metadata
            });
          }

          return allData.length > 0 ? allData : null;
        }
      }

      return null;
    } catch (error) {
      console.error('❌ WeatherDataHandler access failed:', error);
      return null;
    }
  }

  /**
   * Download wind tiles using extracted dataset information
   */
  async downloadWindTilesWithDatasets(map, sceneId, datasets) {
    try {
      const bounds = map.getBounds();
      const zoom = Math.floor(map.getZoom());
      const apiKey = map.getSdkConfig().apiKey;
      const sessionId = map.getMaptilerSessionId();

      // Calculate tile grid
      const tiles = this.calculateTileGrid(bounds, zoom);
      console.log(`🌪️ Downloading ${tiles.length} wind tiles for ${datasets.length} datasets...`);

      const transaction = this.db.transaction(['wind_tiles'], 'readwrite');
      const store = transaction.objectStore('wind_tiles');

      let downloaded = 0;
      
      // Download tiles for each dataset
      for (const dataset of datasets) {
        console.log(`📦 Processing dataset: ${dataset.id} (${dataset.type})`);
        
        for (const tile of tiles) {
          const url = this.buildWindTileUrl(tile, dataset.id, apiKey, sessionId);
          const existing = await this.getFromStore(store, url);

          if (!existing) {
            const tileData = await this.fetchWindTile(url);
            if (tileData) {
              await this.putInStore(store, {
                url: url,
                data: tileData,
                zoom: tile.z,
                x: tile.x,
                y: tile.y,
                datasetId: dataset.id,
                datasetType: dataset.type,
                timestamp: Date.now(),
                sceneId: sceneId
              });
              downloaded++;
            }
          }
        }
      }

      console.log(`✅ Downloaded ${downloaded} wind tiles`);
      return downloaded > 0;

    } catch (error) {
      console.error('❌ Wind tiles download failed:', error);
      return false;
    }
  }

  calculateTileGrid(bounds, zoom) {
    const ne = bounds.getNorthEast();
    const sw = bounds.getSouthWest();
    const scale = Math.pow(2, zoom);
    
    const minX = Math.floor((sw.lng + 180) / 360 * scale);
    const maxX = Math.floor((ne.lng + 180) / 360 * scale);
    const minY = Math.floor((1 - Math.log(Math.tan(ne.lat * Math.PI / 180) + 1 / Math.cos(ne.lat * Math.PI / 180)) / Math.PI) / 2 * scale);
    const maxY = Math.floor((1 - Math.log(Math.tan(sw.lat * Math.PI / 180) + 1 / Math.cos(sw.lat * Math.PI / 180)) / Math.PI) / 2 * scale);

    const tiles = [];
    for (let x = minX; x <= maxX; x++) {
      for (let y = minY; y <= maxY; y++) {
        tiles.push({ z: zoom, x: x, y: y });
      }
    }
    
    return tiles;
  }

  buildWindTileUrl(tile, datasetId, apiKey, sessionId) {
    return `https://api.maptiler.com/tiles/${datasetId}/${tile.z}/${tile.x}/${tile.y}.png?key=${apiKey}&mtsid=${sessionId}`;
  }

  async fetchWindTile(url) {
    try {
      const response = await fetch(url);
      if (response.ok) {
        return await response.arrayBuffer();
      }
      return null;
    } catch (error) {
      console.error(`Failed to fetch wind tile: ${url}`, error);
      return null;
    }
  }

  hideSaveModal(modal) {
    if (modal && modal.parentElement) {
      modal.parentElement.removeChild(modal);
    }
  }

  updateSceneButtonUI(sceneId, type, name = '') {
    const button = document.getElementById(sceneId);
    if (!button) return;

    button.classList.remove('short-save', 'long-save', 'empty-scene');
    
    const deleteBtn = button.querySelector('.delete-btn');
    if (deleteBtn) deleteBtn.remove();

    // Add active indicator if not exists
    if (!button.querySelector('.active-indicator')) {
      const indicator = document.createElement('div');
      indicator.className = 'active-indicator';
      button.appendChild(indicator);
    }

    switch (type) {
      case 'short':
        button.classList.add('short-save');
        button.textContent = name;
        this.addDeleteButton(button, sceneId);
        break;
      case 'long':
        button.classList.add('long-save');
        button.textContent = name;
        this.addDeleteButton(button, sceneId);
        break;
      default:
        button.classList.add('empty-scene');
        button.textContent = sceneId.replace('scene-', 'Scene ');
    }
  }

  addDeleteButton(button, sceneId) {
    const deleteBtn = document.createElement('span');
    deleteBtn.className = 'delete-btn';
    deleteBtn.innerHTML = '×';
    deleteBtn.style.cssText = 'position:absolute;top:-5px;right:-5px;background:#f44336;color:white;border-radius:50%;width:18px;height:18px;font-size:12px;display:flex;align-items:center;justify-content:center;cursor:pointer;';
    deleteBtn.onclick = (e) => {
      e.stopPropagation();
      this.deleteScene(sceneId);
    };
    button.style.position = 'relative';
    button.appendChild(deleteBtn);
  }

  showToast(message, type = 'info') {
    const colors = {
      success: '#4CAF50',
      error: '#f44336',
      info: '#2196F3',
      warning: '#FF9800'
    };
    
    const toast = document.createElement('div');
    toast.style.cssText = `
      position:fixed;top:20px;right:20px;background:${colors[type]};color:white;
      padding:12px 20px;border-radius:4px;z-index:10001;transform:translateX(100%);
      transition:transform 0.3s;max-width:300px;
    `;
    toast.textContent = message;
    
    document.body.appendChild(toast);
    setTimeout(() => toast.style.transform = 'translateX(0)', 100);
    setTimeout(() => {
      toast.style.transform = 'translateX(100%)';
      setTimeout(() => document.body.removeChild(toast), 300);
    }, 3000);
  }

  // Helper methods
  async getFromStore(store, key) {
    return new Promise((resolve, reject) => {
      const request = store.get(key);
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  async getAllFromStore(store) {
    return new Promise((resolve, reject) => {
      const request = store.getAll();
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  async putInStore(store, data) {
    return new Promise((resolve, reject) => {
      const request = store.put(data);
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }
}

// Initialize global instance
window.offlineSceneManager = new OfflineSceneManager();
