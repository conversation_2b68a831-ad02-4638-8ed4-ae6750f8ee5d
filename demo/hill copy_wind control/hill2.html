<!DOCTYPE html>
<html>
<head>
  <title>MapTiler Wind + Temperature (Fixed Boundaries)</title>
  <meta charset="utf-8" />
  <meta name="viewport" content="initial-scale=1,maximum-scale=1,user-scalable=no" />
  <script src="https://cdn.maptiler.com/maptiler-sdk-js/v3.2.0/maptiler-sdk.umd.min.js"></script>
  <link href="https://cdn.maptiler.com/maptiler-sdk-js/v3.2.0/maptiler-sdk.css" rel="stylesheet" />
  <script src="https://cdn.maptiler.com/maptiler-weather/v3.0.1/maptiler-weather.umd.min.js"></script>
  <script src="https://cdn.maptiler.com/maptiler-geocoding-control/v2.1.4/maptilersdk.umd.js"></script>
  <link href="https://cdn.maptiler.com/maptiler-geocoding-control/v2.1.4/style.css" rel="stylesheet">
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: sans-serif;
    }

    #map {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 100%;
      background-color: #444952;
      background-image: url('night-stars.webp');
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
    }

    #time-info {
      position: fixed;
      width: 60vw;
      bottom: 0;
      z-index: 1;
      margin: 10px;
      text-shadow: 0px 0px 5px black;
      color: white;
      font-size: 20px;
      font-weight: 500;
      text-align: center;
      left: 0;
      right: 0;
      margin: auto;
      padding: 20px;
    }

    #time-slider {
      width: 100%;
      height: fit-content;
      left: 0;
      right: 0;
      z-index: 1;
      filter: drop-shadow(0 0 7px #000a);
      margin-top: 10px;
      position: relative;
    }

    /* Timeline markers container */
    #timeline-markers {
      position: absolute;
      top: 55%; /* Hạ xuống gần timeline hơn */
      left: 0;
      right: 0;
      height: 60px; /* Tăng chiều cao để chứa stacked markers */
      pointer-events: none;
      z-index: 10;
      overflow: visible;
    }

    /* Scene markers on timeline */
    .timeline-marker {
      position: absolute;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      pointer-events: none;
      /* transform: translateX(-50%); - TẠM BỎ ĐỂ TEST */
      transition: all 0.2s ease;
    }

    .timeline-marker.short-save {
      background: #4CAF50;
      border: 2px solid white;
      box-shadow: 0 0 4px rgba(76, 175, 80, 0.8);
    }

    .timeline-marker.long-save {
      background: #f44336;
      border: 2px solid white;
      box-shadow: 0 0 4px rgba(244, 67, 54, 0.8);
    }

    .timeline-marker.localStorage-save {
      background: #2196F3;
      border: 2px solid white;
      box-shadow: 0 0 4px rgba(33, 150, 243, 0.8);
    }

    .timeline-marker.outside-left {
      left: -12px !important;
      width: 10px;
      height: 10px;
      top: -5px;
      transform: none;
      border-radius: 0 50% 50% 0;
    }

    #pointer-data {
      z-index: 1000;
      position: absolute;
      font-size: 14px;
      font-weight: 500;
      color: #fff;
      background: rgba(0, 0, 0, 0.85);
      padding: 6px 10px;
      border-radius: 4px;
      white-space: pre-line;
      pointer-events: none;
      display: none;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
      line-height: 1.4;
      text-align: left;
    }

    #pointer-data::after {
      content: '';
      position: absolute;
      bottom: 15px;
      left: -6px;
      width: 0;
      height: 0;
      border-top: 6px solid transparent;
      border-bottom: 6px solid transparent;
      border-right: 6px solid rgba(0, 0, 0, 0.85);
    }

    #variable-name {
      z-index: 1;
      position: fixed;
      font-size: 20px;
      font-weight: 500;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      color: #fff;
      text-shadow: 0px 0px 10px #0007;
    }

    .button {
      cursor: pointer;
      width: auto;
      padding: 8px;
      border-radius: 3px;
      margin: 10px 0 0 0;
      font-size: 12px;
      text-align: center;
      color: #fff;
      background: #3174ff;
      font-family: sans-serif;
      font-weight: bold;
    }

    #color-scale-container {
      position: fixed;
      right: 30px;
      top: 50%;
      transform: translateY(-50%);
      z-index: 10;
      display: flex;
      flex-direction: column;
      align-items: center;
      background: rgba(0,0,0,0.15);
      border-radius: 10px;
      padding: 12px 8px;
      box-shadow: 0 2px 8px #0003;
    }
    #color-scale-canvas {
      display: block;
      width: 32px;
      height: 256px;
      border-radius: 8px;
      margin-bottom: 8px;
      margin-top: 8px;
    }
    .scale-label {
      color: #fff;
      font-size: 15px;
      font-weight: bold;
      text-shadow: 0 1px 4px #000a;
      margin: 0 0 2px 0;
      user-select: none;
    }
    .scale-label.min {
      margin-bottom: 4px;
    }
    .scale-label.max {
      margin-top: 4px;
    }

    /* Tùy chỉnh chiều ngang của search box */
    .maplibregl-ctrl-geocoder {
      width: 310px !important; /* Thay đổi chiều ngang ở đây */
      min-width: 200px !important;
    }
    
    .maplibregl-ctrl-geocoder input {
      width: 100% !important;
    }

    /* Scene Management UI */
    #scene-controls {
      position: fixed;
      top: 20px;
      left: 20px;
      z-index: 1000;
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    /* Wind Animation Controls - removed, now handled by JS */

    .scene-button {
      background: rgba(49, 116, 255, 0.9);
      color: white;
      border: none;
      padding: 8px 12px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 12px;
      font-weight: bold;
      min-width: 80px;
      transition: all 0.2s;
      box-shadow: 0 2px 4px rgba(0,0,0,0.2);
      position: relative;
    }

    .scene-button:hover {
      background: rgba(49, 116, 255, 1);
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }

    .scene-button.saved {
      background: rgba(76, 175, 80, 0.9);
    }

    .scene-button.saved:hover {
      background: rgba(76, 175, 80, 1);
    }

    .scene-button.active {
      background: rgba(255, 152, 0, 0.9);
      animation: pulse 1.5s infinite;
    }

    @keyframes pulse {
      0%, 100% { opacity: 0.9; }
      50% { opacity: 1; }
    }

    /* Offline Scene Indicators */
    .scene-button.short-save {
      background: rgba(76, 175, 80, 0.9) !important;
      border-left: 4px solid #4CAF50;
    }

    .scene-button.short-save:hover {
      background: rgba(76, 175, 80, 1) !important;
    }

    .scene-button.long-save {
      background: rgba(244, 67, 54, 0.9) !important;
      border-left: 4px solid #f44336;
    }

    .scene-button.long-save:hover {
      background: rgba(244, 67, 54, 1) !important;
    }

    .scene-button.empty-scene {
      background: rgba(49, 116, 255, 0.9);
    }

    .scene-button .delete-btn {
      position: absolute;
      top: -5px;
      right: -5px;
      background: #f44336;
      color: white;
      border-radius: 50%;
      width: 18px;
      height: 18px;
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border: 2px solid white;
      font-weight: bold;
    }

    .scene-button .delete-btn:hover {
      background: #d32f2f;
      transform: scale(1.1);
    }

    /* Active scene indicator */
    .scene-button .active-indicator {
      position: absolute;
      top: 50%;
      right: -12px;
      transform: translateY(-50%);
      width: 0;
      height: 0;
      border-right: 8px solid #000000;
      border-top: 6px solid transparent;
      border-bottom: 6px solid transparent;
      display: none;
    }

    .scene-button.active .active-indicator {
      display: block;
    }
  </style>
</head>

<body>
  <div id="time-info">
    <span id="time-text"></span>
    <button id="play-pause-bt" class="button">Play 3600x</button>
    <button id="toggle-temp-bt" class="button">Ẩn nhiệt độ</button>
    <button id="hillshade-toggle-bt" class="button">Ẩn bóng địa hình</button>
    <input type="range" id="time-slider" min="0" max="11" step="1">
  </div>

  <div id="variable-name">Temperature + Wind</div>
  <div id="pointer-data"></div>
  <div id="map"></div>

  <!-- Scene Management Controls -->
  <div id="scene-controls">
    <button class="scene-button" id="scene-1">Scene 1<div class="active-indicator"></div></button>
    <button class="scene-button" id="scene-2">Scene 2<div class="active-indicator"></div></button>
    <button class="scene-button" id="scene-3">Scene 3<div class="active-indicator"></div></button>
    <button class="scene-button" id="scene-4">Scene 4<div class="active-indicator"></div></button>
    <button class="scene-button" id="scene-5">Scene 5<div class="active-indicator"></div></button>
    <button class="scene-button" id="save-scene" onclick="saveCurrentScene()" style="background:rgba(255,193,7,0.9);">💾 Save</button>
    <button class="scene-button" id="clear-scenes" onclick="clearAllScenes()" style="background:rgba(244,67,54,0.9);margin-top:8px;">🗑️ Clear All</button>
  </div>

  <!-- Color scale UI -->
  <div id="color-scale-container">
    <canvas id="color-scale-canvas" width="32" height="256"></canvas>
    <div class="scale-label max" id="scale-max-label">40°C</div>
    <div class="scale-label min" id="scale-min-label">0°C</div>
  </div>

  <!-- Projection notification -->
  <div id="projection-notification" style="position:fixed;top:80px;right:15px;z-index:1000;background:rgba(255,107,107,0.95);color:#fff;padding:12px 16px;border-radius:8px;font-size:14px;display:none;max-width:250px;box-shadow:0 4px 12px rgba(0,0,0,0.3);border:1px solid #ff6b6b;">
    ⚠️ Globe 3D tốn hiệu năng GPU đáng kể!
  </div>

  <!-- Location panel -->
  <div id="location-panel" style="position:fixed;top:328px;right:110px;z-index:900;background:rgba(255,255,255,0.95);color:#333;padding:12px;border-radius:10px;font-size:14px;min-width:180px;max-width:220px;box-shadow:0 4px 16px rgba(0,0,0,0.2);border:1px solid #ddd;display:none;">
    <div style="font-weight:bold;margin-bottom:8px;color:#666;font-size:12px;">📍 Khu vực lân cận tâm viewport</div>
    <div id="location-list"></div>
  </div>

  <!-- Location marker popup -->
  <div id="location-popup" style="position:absolute;background:rgba(255,255,255,0.95);color:#333;padding:8px 12px;border-radius:8px;font-size:14px;box-shadow:0 2px 8px rgba(0,0,0,0.3);border:1px solid #ddd;display:none;z-index:1000;max-width:200px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;">
    <div id="location-popup-text"></div>
  </div>
  
  <!-- Include Timeline Manager -->
  <script src="timeline-manager.js"></script>
  
      <!-- Include Offline Scene Manager -->
    <script src="offline-scene-manager.js"></script>
    
    <!-- Include Wind Controls Manager -->
    <script src="wind-controls.js"></script>
  
  <!-- Location Controls -->
  <div id="location-controls">
    <div class="location-control-header">🔍 Locations</div>
    <button class="location-btn" data-location="hanoi">Hanoi</button>
    <button class="location-btn" data-location="hochiminhcity">Ho Chi Minh City</button>
    <button class="location-btn" data-location="danang">Da Nang</button>
  </div>

  <!-- Wind Controls (Now handled by JS) -->
  
  <script>
    // --- DOM Elements ---
    const timeInfoContainer = document.getElementById("time-info");
    const timeTextDiv = document.getElementById("time-text");
    const timeSlider = document.getElementById("time-slider");
    const playPauseButton = document.getElementById("play-pause-bt");
    const toggleTempButton = document.getElementById("toggle-temp-bt");
    const pointerDataDiv = document.getElementById("pointer-data");
    const colorScaleCanvas = document.getElementById('color-scale-canvas');
    const scaleMinLabel = document.getElementById('scale-min-label');
    const scaleMaxLabel = document.getElementById('scale-max-label');
    const hillshadeToggleButton = document.getElementById('hillshade-toggle-bt');
    const projectionNotification = document.getElementById('projection-notification');
    const locationPanel = document.getElementById('location-panel');
    const locationList = document.getElementById('location-list');
    const locationPopup = document.getElementById('location-popup');
    const locationPopupText = document.getElementById('location-popup-text');

    // --- State Variables ---
    let pointerLngLat = null;
    let tempLayerVisible = true;
    let isPlaying = false;
    let mapLoaded = false;
    let colorScale = [];
    const TEMP_LAYER_ID = 'temperature-layer';
    let hillshadeVisible = true;
    let weatherLayersAdded = false;
    let locationUpdateTimeout = null;
    let currentLocationMarker = null;
    
    // Scene Management System
    let scenes = {};
    let currentSceneIndex = null;
    let selectedSceneToSave = null;

    // --- Tạo color scale mặc định ---
    colorScale = [
      // Dải màu cực lạnh: Từ trắng đến xanh nhạt
      { min: -73.3, max: -51.1, color: '#E5F0FF' },
      { min: -51.1, max: -48.3, color: '#DBE9FB' },
      { min: -48.3, max: -45.6, color: '#D3E2F7' },
      { min: -45.6, max: -42.8, color: '#CCDBF4' },
      { min: -42.8, max: -40.0, color: '#C0D4ED' },
      { min: -40.0, max: -37.2, color: '#B8CDEA' },
      { min: -37.2, max: -34.4, color: '#AFC6E7' },
      { min: -34.4, max: -31.7, color: '#A7BFE3' },
      { min: -31.7, max: -28.9, color: '#9CB8DE' },
      { min: -28.9, max: -26.1, color: '#93B1D7' },
      { min: -26.1, max: -23.3, color: '#89A5CD' },
      
      // Dải màu lạnh: Bắt đầu từ Xanh da trời nhạt, đậm dần thành Xanh dương
      { min: -23.3, max: -20.6, color:  '#04062E'},
      { min: -20.6, max: -17.8, color:  '#060A3F'},
      { min: -17.8, max: -15.0, color:  '#091057'},
      { min: -15.0, max: -12.2, color:  '#0D1871'}, // Xanh da trời
      { min: -12.2, max: -9.4, color:  '#11228B'},
      { min: -9.4, max: -6.7, color:  '#162DA6'},
      { min: -6.7, max: -3.9, color:  '#1C3AC2'},
      { min: -3.9, max: -1.1, color:  '#2348DE'}, 
      { min: -1.1, max: 1.7, color:  '#2F5BF0'},
      { min: 1.7, max: 4.4, color:  '#3B6EFF'},
      { min: 4.4, max: 7.2, color:  '#4981FF'},
      { min: 7.2, max: 10.0, color:  '#5793FF'},

      { min: 10.0, max: 12.8, color: '#66A6FF' }, // Xanh ngọc (Tile/Teal)
      { min: 12.8, max: 15.6, color:  '#A2D9D4'}, // Xanh lá cây pastel

      { min: 15.6, max: 18.3, color: '#B3E6B5' }, // Vàng chanh nhạt
      { min: 18.3, max: 21.1, color: '#FFE67F' },
      { min: 21.1, max: 22.5, color: '#FFE066' }, // Vàng tươi sáng nhất
      { min: 22.5, max: 23.9, color: '#FFD957' }, // Vàng tươi
      { min: 23.9, max: 25.2, color: '#FFD148' }, // Vàng hơi đậm
      { min: 25.2, max: 26.6, color: '#FFC83C' }, // Vàng cam nhạt
      { min: 26.6, max: 28.0, color: '#FFB82F' }, // Vàng cam
      { min: 28.0, max: 29.4, color: '#FFB52A' }, // Vàng cam đậm
      { min: 29.4, max: 30.8, color: '#FF9F1C' }, // Cam sáng
      { min: 30.8, max: 32.2, color: '#FF931A' }, // Cam
      { min: 32.2, max: 33.6, color: '#F98611' }, // Cam đậm
      { min: 33.6, max: 35.0, color: '#F67F0C' }, // Cam đỏ nhạt
      { min: 35.0, max: 36.4, color: '#F26A0A' }, // Cam đỏ
      { min: 36.4, max: 37.8, color: '#EE5808' }, // Cam đỏ đậm
      { min: 37.8, max: 40.6, color: '#E94E06' }, // Cam đỏ
      { min: 40.6, max: 43.3, color: '#DE3103' },
      { min: 43.3, max: 46.1, color: '#D21502' }, // Đỏ tươi
      { min: 46.1, max: 48.9, color: '#B70E02' },
      { min: 48.9, max: 65.6, color: '#8B0000' }  // Đỏ thẫm (Maroon)
    ];

    // --- MapTiler Initialization ---
    maptilersdk.config.apiKey = 'oinoatcrNmdCL1524DOl';

    const map = new maptilersdk.Map({
      container: document.getElementById('map'),
      hash: true,
      zoom: 2,
      center: [0, 40],
      style: maptilersdk.MapStyle.BACKDROP,
      projectionControl: true,
      projection: 'mercator'
    });

    // Thêm Geocoding Control - di chuyển vào sau khi map load
    let gc;
    
    // Hàm tạo location marker
    function createLocationMarker(lngLat, locationName) {
        // Xóa marker cũ nếu có
        if (currentLocationMarker) {
            map.removeLayer(currentLocationMarker.id);
            map.removeSource(currentLocationMarker.id);
        }
        
        // Tạo marker mới
        const markerId = 'location-marker-' + Date.now();
        
        // Thêm source cho marker
        map.addSource(markerId, {
            type: 'geojson',
            data: {
                type: 'Feature',
                geometry: {
                    type: 'Point',
                    coordinates: [lngLat.lng, lngLat.lat]
                },
                properties: {
                    name: locationName
                }
            }
        });
        
        // Thêm layer cho marker
        map.addLayer({
            id: markerId,
            type: 'circle',
            source: markerId,
            paint: {
                'circle-radius': 8,
                'circle-color': '#FF4444',
                'circle-stroke-color': '#FFFFFF',
                'circle-stroke-width': 2
            }
        });
        
        // Thêm layer cho icon
        map.addLayer({
            id: markerId + '-icon',
            type: 'symbol',
            source: markerId,
            layout: {
                'text-field': '📍',
                'text-size': 16,
                'text-offset': [0, -0.5]
            }
        });
        
        currentLocationMarker = { id: markerId, lngLat: lngLat, name: locationName };
        
        // Hiển thị popup
        showLocationPopup(lngLat, locationName);
        
        console.log(`📍 Location marker created at: ${locationName} (${lngLat.lng.toFixed(4)}, ${lngLat.lat.toFixed(4)})`);
    }
    
    // Hàm hiển thị popup
    function showLocationPopup(lngLat, locationName) {
        const point = map.project(lngLat);
        locationPopupText.textContent = locationName;
        locationPopup.style.left = point.x + 'px';
        locationPopup.style.top = (point.y - 40) + 'px';
        locationPopup.style.display = 'block';
        
        // Tự động ẩn sau 3 giây
        setTimeout(() => {
            locationPopup.style.display = 'none';
        }, 3000);
    }

    // --- Layer Initialization ---
    let layerBg;
    
    let layer = new maptilerweather.WindLayer({
      id: "Wind Particles",
      colorramp: maptilerweather.ColorRamp.builtin.NULL,
      speed: 0.002, fadeFactor: 0.03, maxAmount: 256, density: 400,
      color: [255, 0, 0, 30], fastColor: [0, 255, 0, 100],
    });

    // ===== HÀM CHÍNH ĐỂ ĐẢMBẢO RANH GIỚI LUÔN HIỂN THỊ =====
    function ensureBoundariesOnTop() {
        if (!map.isStyleLoaded()) return;

        const layers = map.getStyle().layers;
        // console.log('All layers:', layers.map(l => ({ id: l.id, type: l.type })));
        
        // Tìm TẤT CẢ layer có thể là ranh giới hoặc nhãn
        const boundaryAndLabelLayers = layers.filter(layer => {
            const id = layer.id.toLowerCase();
            const type = layer.type;
            
            // Điều kiện mở rộng để bắt tất cả các loại ranh giới và nhãn
            return (
                // Các layer ranh giới
                id.includes('boundary') || 
                id.includes('admin') || 
                id.includes('border') || 
                id.includes('country') || 
                id.includes('state') || 
                id.includes('province') ||
                id.includes('outline') ||
                id.includes('stroke') ||
                // Các layer nhãn
                type === 'symbol' ||
                id.includes('label') || 
                id.includes('text') || 
                id.includes('place') ||
                id.includes('city') ||
                id.includes('town') ||
                // Các layer line có thể là ranh giới
                (type === 'line' && (
                    id.includes('line') || 
                    id.includes('admin') || 
                    id.includes('boundary')
                ))
            );
        });

        // console.log('Boundary and label layers found:', boundaryAndLabelLayers.map(l => l.id));

        // Di chuyển tất cả lên trên cùng, giữ nguyên thứ tự tương đối
        boundaryAndLabelLayers.forEach(layer => {
            try {
                if (map.getLayer(layer.id)) {
                    // Xóa fill color cho các layer fill/polygon
                    if (layer.type === 'fill') {
                        map.setPaintProperty(layer.id, 'fill-opacity', 0);
                        map.setPaintProperty(layer.id, 'fill-color', 'transparent');
                        // Đảm bảo stroke hiển thị
                        if (map.getPaintProperty(layer.id, 'fill-outline-color')) {
                            map.setPaintProperty(layer.id, 'fill-outline-color', '#666666');
                        }
                        // console.log(`Removed fill from layer ${layer.id}`);
                    }
                    
                    map.moveLayer(layer.id);
                    // console.log(`Moved layer ${layer.id} to top`);
                }
            } catch (e) {
                console.log(`Failed to move layer ${layer.id}:`, e.message);
            }
        });
    }

    // Tạo Hàm riêng để đảm bảo borders luôn ở trên
    function ensureAllBordersOnTop() {
        if (!mapLoaded || !map.isStyleLoaded()) return;
        
        setTimeout(() => {
            ensureBoundariesOnTop();
            addCustomBorderLayers();
            console.log('All borders moved to top');
        }, 100);
    }

    // ===== HÀM THÊM BORDER LAYER RIÊNG =====
    function addCustomBorderLayers() {
        if (!map.isStyleLoaded()) return;
        
        try {
            // Thêm source cho ranh giới từ MapTiler Countries
            if (!map.getSource('maptiler-countries')) {
                map.addSource('maptiler-countries', {
                    type: 'vector',
                    url: `https://api.maptiler.com/tiles/countries/tiles.json?key=${maptilersdk.config.apiKey}`
                });
                console.log('Added MapTiler Countries source');
            }

            // 1. Thêm ranh giới quốc gia (admin_level=2)
            if (!map.getLayer('country-boundaries')) {
                map.addLayer({
                    id: 'country-boundaries',
                    type: 'line',
                    source: 'maptiler-countries',
                    'source-layer': 'boundary',
                    filter: ['==', 'admin_level', 2],
                    paint: {
                        'line-color': '#444444',
                        'line-width': [
                            'interpolate',
                            ['linear'],
                            ['zoom'],
                            0, 0.5,
                            4, 1,
                            8, 1.5,
                            12, 2
                        ],
                        'line-opacity': 0.8
                    }
                });
                console.log('Added country boundaries layer');
            }

            // 2. Thêm ranh giới cấp 1 (admin_level=4 - states/provinces)
            if (!map.getLayer('state-boundaries')) {
                map.addLayer({
                    id: 'state-boundaries',
                    type: 'line',
                    source: 'maptiler-countries',
                    'source-layer': 'boundary',
                    filter: ['==', 'admin_level', 4],
                    paint: {
                        'line-color': '#666666',
                        'line-width': [
                            'interpolate',
                            ['linear'],
                            ['zoom'],
                            4, 0.3,
                            8, 0.8,
                            12, 1.2
                        ],
                        'line-opacity': 0.6,
                        'line-dasharray': [2, 2]
                    }
                });
                console.log('Added state boundaries layer');
            }

            // 3. Đảm bảo các border layer luôn ở trên cùng
            setTimeout(() => {
                if (map.getLayer('state-boundaries')) {
                    map.moveLayer('state-boundaries');
                }
                if (map.getLayer('country-boundaries')) {
                    map.moveLayer('country-boundaries');
                }
                // console.log('Moved custom border layers to top');
            }, 100);

        } catch (error) {
            console.error('Error adding custom border layers:', error);
        }
    }



    // Hàm thêm custom layers
    function addCustomLayers() {
        try {
            // 1. Kiểm tra và thêm DEM source nếu chưa có
            if (!map.getSource('dem')) {
                map.addSource('dem', {
                    type: 'raster-dem',
                    url: `https://api.maptiler.com/tiles/terrain-rgb/tiles.json?key=${maptilersdk.config.apiKey}`,
                    tileSize: 256, 
                    maxzoom: 12
                });
            }

            // 2. Kiểm tra và thêm hillshade layers nếu chưa có
            if (!map.getLayer('hillshade-shadows')) {
                map.addLayer({
                    id: 'hillshade-shadows', 
                    type: 'hillshade', 
                    source: 'dem',
                    layout: { visibility: hillshadeVisible ? 'visible' : 'none' },
                    paint: { 
                        'hillshade-exaggeration': 0.65, 
                        'hillshade-illumination-direction': 315, 
                        'hillshade-highlight-color': 'rgba(0, 0, 0, 0)', 
                        'hillshade-shadow-color': '#252525' 
                    }
                });
            }
            
            if (!map.getLayer('hillshade-highlights')) {
                map.addLayer({
                    id: 'hillshade-highlights', 
                    type: 'hillshade', 
                    source: 'dem',
                    layout: { visibility: hillshadeVisible ? 'visible' : 'none' },
                    paint: { 
                        'hillshade-exaggeration': 0.65, 
                        'hillshade-illumination-direction': 315, 
                        'hillshade-shadow-color': 'rgba(0, 0, 0, 0)', 
                        'hillshade-highlight-color': '#FFFFFF', 
                        'hillshade-opacity': 0.2 
                    }
                });
            }

            // 3. Thêm temperature layer
            updateTemperatureColorRamp();

            // 4. Thêm wind layer nếu chưa có - QUAN TRỌNG: Đảm bảo luôn thêm
            if (!map.getLayer(layer.id)) {
                map.addLayer(layer);
                console.log('Wind layer added to map');
            } else {
                console.log('Wind layer already exists');
            }

            setTimeout(() => {
              console.log('Auto-clicking hillshade button...');
              
              // Click 2 lần để simulate user behavior
              hillshadeToggleButton.click(); // Ẩn
              setTimeout(() => {
                  hillshadeToggleButton.click(); // Hiện lại
                  mapLoaded = true;
                  console.log('Auto-click completed');
              }, 200);
              
          }, 500);
              

            // // 5. QUAN TRỌNG: Đảm bảo ranh giới lên trên
            // ensureBoundariesOnTop();

            // console.log('All custom layers added successfully');

            // // Cuối cùng đảm bảo borders lên trên
            // setTimeout(() => {
            //     ensureAllBordersOnTop();
            //     mapLoaded = true;
            // }, 300);
            
        } catch (error) {
            console.error('Error adding custom layers:', error);
        }
    }

    // --- Map Events ---
    map.on('load', function () {
        console.log('Map loaded');
        try {
            // Xử lý Water layer giống wind_temp.html
            map.setPaintProperty("Water", 'fill-color', "rgba(0, 0, 0, 0.6)");
        } catch (error) { 
            console.log('Water layer styling error:', error); 
        }
        
        // Thêm Geocoding Control sau khi map load
        gc = new maptilersdkMaptilerGeocoder.GeocodingControl({});
        map.addControl(gc, 'top-right');
        
        // Lắng nghe sự kiện khi user chọn địa điểm từ search box
        gc.on('result', function(e) {
            const result = e.result;
            const lngLat = result.center;
            const locationName = result.place_name || result.text;
            
            console.log('🔍 Search result selected:', locationName, lngLat);
            
            // Tạo marker cho địa điểm được chọn
            createLocationMarker(lngLat, locationName);
        });
        
        // Khởi tạo temperature layer ngay lập tức
        mapLoaded = true;
        if (colorScale.length) {
            updateTemperatureColorRamp();
        }
    });

    // Sử dụng event 'idle' để đảm bảo map đã sẵn sàng hoàn toàn
    map.on('idle', function() {
        if (!weatherLayersAdded && map.isStyleLoaded()) {
            weatherLayersAdded = true;
            console.log('Map is idle and style loaded, adding custom layers...');
            
            // Delay nhỏ để đảm bảo style đã ổn định hoàn toàn
            setTimeout(() => {
                addCustomLayers();
            }, 100);
        }
    });

    // Backup: Thêm wind layer nếu idle không trigger
    setTimeout(() => {
        if (!weatherLayersAdded && map.isStyleLoaded()) {
            weatherLayersAdded = true;
            console.log('Backup: Adding custom layers after timeout...');
            addCustomLayers();
        }
    }, 2000); // 2 giây backup

    // Event để đảm bảo ranh giới luôn hiển thị khi style thay đổi
    map.on('styledata', function() {
        if (mapLoaded) {
            setTimeout(() => {
                ensureBoundariesOnTop();
                addCustomBorderLayers();
            }, 50);
        }
    });

    layer.on("sourceReady", () => {
        console.log('Wind layer source ready');
        
        // Initialize fixed timeline using timeline manager
        if (window.timelineManager) {
            window.timelineManager.initializeFixedTimeline();
            
            // Set initial weather layer time to current
            const now = Date.now();
            layer.setAnimationTime(now / 1000);
            if (layerBg) layerBg.setAnimationTime(now / 1000);
            
            // Load existing scene markers
            setTimeout(() => {
                window.timelineManager.loadSavedScenesMarkers();
                
                // Initialize wind controls
                initializeWindControls();
            }, 500);
        }
    });

    // --- UI Event Listeners ---
    let timeSliderTimeout = null;
    
    timeSlider.addEventListener("input", () => {
      const time = parseInt(timeSlider.value);
      
      // Hủy timeout cũ nếu user vẫn đang kéo
      if (timeSliderTimeout) {
        clearTimeout(timeSliderTimeout);
      }
      
      // Chỉ update UI text ngay lập tức, không load data
      const d = new Date(time);
      if (d) {
        timeTextDiv.innerText = d.toString();
      }
      
      // Đặt timeout mới - chỉ load data khi dừng kéo > 1 giây
      timeSliderTimeout = setTimeout(() => {
        console.log('Loading data for time:', new Date(time).toString());
        layer.setAnimationTime(time / 1000);
        if (layerBg) layerBg.setAnimationTime(time / 1000);
        
        // Update pointer data với thời gian mới
        updatePointerValue(pointerLngLat);
        
        timeSliderTimeout = null;
      }, 1000); // Đợi 1 giây sau khi dừng kéo (timeline loading)
    });

    playPauseButton.addEventListener("click", () => {
      isPlaying = !isPlaying;
      if (isPlaying) {
        layer.animateByFactor(3600); 
        if (layerBg) layerBg.animateByFactor(3600);
        playPauseButton.innerText = "Pause";
      } else {
        layer.animateByFactor(0); 
        if (layerBg) layerBg.animateByFactor(0);
        playPauseButton.innerText = "Play 3600x";
      }
    });

    toggleTempButton.addEventListener("click", () => {
      tempLayerVisible = !tempLayerVisible;
      if (layerBg) {
        const visibility = tempLayerVisible ? 'visible' : 'none';
        map.setLayoutProperty(TEMP_LAYER_ID, 'visibility', visibility);
        toggleTempButton.innerText = tempLayerVisible ? "Ẩn nhiệt độ" : "Hiện nhiệt độ";
      }
    });

    hillshadeToggleButton.addEventListener('click', function() {
      hillshadeVisible = !hillshadeVisible;
      const visibility = hillshadeVisible ? 'visible' : 'none';
      if (map.getLayer('hillshade-shadows')) { 
        map.setLayoutProperty('hillshade-shadows', 'visibility', visibility); 
      }
      if (map.getLayer('hillshade-highlights')) { 
        map.setLayoutProperty('hillshade-highlights', 'visibility', visibility); 
      }
      hillshadeToggleButton.innerText = hillshadeVisible ? 'Ẩn bóng địa hình' : 'Hiện bóng địa hình';
    });

    // Lắng nghe sự kiện thay đổi projection từ icon có sẵn
    let currentProjection = 'mercator';
    
    // Theo dõi thay đổi projection
    setInterval(() => {
      const newProjection = map.getProjection();
      if (newProjection !== currentProjection) {
        currentProjection = newProjection;
        if (newProjection && newProjection !== 'mercator') {
          // Hiện notification khi chuyển sang projection khác mercator (như globe)
          projectionNotification.style.display = 'block';
          setTimeout(() => {
            projectionNotification.style.display = 'none';
          }, 5000); // Tự tắt sau 5 giây
        }
      }
    }, 100); // Kiểm tra mỗi 100ms

    // Mouse tracking for tooltip
    let isMouseOverMap = false;
    
    map.on('mousemove', (e) => {
      isMouseOverMap = true;
      updatePointerValue(e.lngLat);
      
      // Update tooltip position
      const point = map.project(e.lngLat);
      pointerDataDiv.style.left = (point.x + 15) + 'px'; // Offset 15px from cursor
      pointerDataDiv.style.top = (point.y - 35) + 'px';  // Offset 35px above cursor
      pointerDataDiv.style.display = 'block';
    });
    
    // Hide tooltip when mouse leaves map
    map.on('mouseleave', () => {
      isMouseOverMap = false;
      pointerDataDiv.style.display = 'none';
    });
    
    // Hide tooltip when map starts moving/dragging
    map.on('movestart', () => {
      pointerDataDiv.style.display = 'none';
    });
    
    map.on('dragstart', () => {
      pointerDataDiv.style.display = 'none';
    });
    
    // Hide tooltip during zoom
    map.on('zoomstart', () => {
      pointerDataDiv.style.display = 'none';
    });
    
    // Ensure tooltip stays hidden after map movement if mouse not over map
    map.on('moveend', () => {
      if (!isMouseOverMap) {
        pointerDataDiv.style.display = 'none';
      }
    });
    
    map.on('zoomend', () => {
      if (!isMouseOverMap) {
        pointerDataDiv.style.display = 'none';
      }
    });
    
    // Hide tooltip when hovering over UI elements
    const uiElements = [
      timeInfoContainer,
      document.getElementById('scene-controls'),
      document.getElementById('color-scale-container'),
      document.getElementById('location-panel'),
      // Wind controls now handled by JS
      document.querySelector('.maplibregl-ctrl-top-right'), // Geocoding control
      document.querySelector('.maplibregl-ctrl-top-left'),  // Other controls
      document.querySelector('.maplibregl-ctrl-bottom-right'), // Attribution
      document.getElementById('projection-notification'),
      document.getElementById('location-popup')
    ];
    
    uiElements.forEach(element => {
      if (element) {
        element.addEventListener("mouseenter", () => {
          pointerDataDiv.style.display = 'none';
        });
        
        element.addEventListener("mouseleave", () => {
          // Only show tooltip if mouse is still over map and not moving
          if (pointerLngLat && isMouseOverMap) {
            setTimeout(() => {
              if (isMouseOverMap) {
                pointerDataDiv.style.display = 'block';
              }
            }, 100);
          }
        });
      }
    });

    // Location panel functionality - simplified hierarchy display
    let moveTimer = null;
    let isMoving = false;

    async function updateLocationPanel() {
      const center = map.getCenter();
      
      console.log('🎯 Getting location hierarchy for center:', center.lng.toFixed(4), center.lat.toFixed(4));
      
      try {
        // Reverse geocoding để lấy toàn bộ hierarchy như demo
        const response = await fetch(
          `https://api.maptiler.com/geocoding/${center.lng},${center.lat}.json?key=${maptilersdk.config.apiKey}&language=en`
        );
        
        if (!response.ok) {
          console.log('❌ Reverse geocoding failed:', response.status, response.statusText);
          locationList.innerHTML = '<div style="padding:8px;color:#999;">Unable to detect location</div>';
          return;
        }
        
        const data = await response.json();
        console.log('🏠 Reverse geocoding result:', data);
        
        if (!data.features || data.features.length === 0) {
          locationList.innerHTML = '<div style="padding:8px;color:#999;">No location data found</div>';
          return;
        }
        
        // Hiển thị toàn bộ hierarchy (như demo) - từ chi tiết nhất đến tổng quát nhất
        locationList.innerHTML = data.features.map(feature => {
          const placeType = feature.place_type && feature.place_type[0] ? feature.place_type[0] : 'place';
          
          return `
            <div style="padding:4px 8px;margin:2px 0;cursor:pointer;border-radius:6px;transition:all 0.2s;border:1px solid transparent;" 
                 class="location-item" 
                 data-bbox='${JSON.stringify(feature.bbox)}'
                 data-name="${feature.place_name.replace(/"/g, '&quot;')}"
                 onmouseover="this.style.background='#e3f2fd';this.style.borderColor='#90caf9';" 
                 onmouseout="this.style.background='transparent';this.style.borderColor='transparent';">
              <div style="font-weight:500;color:#333;">${feature.text}</div>
              <div style="font-size:11px;color:#666;text-transform:capitalize;">${placeType}</div>
            </div>
          `;
        }).join('');
        
        // Add click events để zoom tới từng level
        document.querySelectorAll('.location-item').forEach(item => {
          item.addEventListener('click', () => {
            const bbox = JSON.parse(item.dataset.bbox);
            const name = item.dataset.name;
            
            console.log(`📍 Jumping to: ${name}, bbox:`, bbox);
            
            // Fit bounds với maxZoom để không zoom quá gần
            map.fitBounds(bbox, {
              maxZoom: 19,
              padding: 50,
              duration: 1500
            });
            
            // Tạo marker cho địa điểm được chọn
            const center = map.getCenter();
            createLocationMarker(center, name);
          });
        });
        
        locationPanel.style.display = 'block';
        console.log(`✅ Location panel updated with ${data.features.length} hierarchy levels`);
        
      } catch (error) {
        console.log('💥 Error updating location panel:', error);
        locationList.innerHTML = '<div style="padding:8px;color:#999;">Error loading locations</div>';
      }
    }

    // Lắng nghe sự kiện move với debounce 2 giây
    map.on('move', () => {
      isMoving = true;
      
      // Clear timer cũ
      if (moveTimer) {
        clearTimeout(moveTimer);
      }
      
      // Set timer mới - chỉ update khi dừng di chuyển > 4 giây
      moveTimer = setTimeout(() => {
        if (isMoving) {
          console.log('🕐 User stopped moving for 4s, updating location panel...');
          updateLocationPanel();
          isMoving = false;
        }
      }, 4000); // 4 giây
    });
    
    // Lắng nghe sự kiện zoom để cập nhật popup vị trí
    map.on('zoom', () => {
      if (currentLocationMarker && locationPopup.style.display === 'block') {
        const point = map.project(currentLocationMarker.lngLat);
        locationPopup.style.left = point.x + 'px';
        locationPopup.style.top = (point.y - 40) + 'px';
      }
    });
    
    // Update lần đầu khi map load xong
    map.on('load', () => {
      setTimeout(async () => {
        updateLocationPanel();
        
        // Load saved scenes from both localStorage and IndexedDB
        await loadSavedScenes();
      }, 1000);
    });
    
    // --- Helper Functions ---
    function updateTemperatureColorRamp() {
        if (!colorScale.length || !mapLoaded) return;
        if (map.getLayer(TEMP_LAYER_ID)) {
            map.removeLayer(TEMP_LAYER_ID);
        }
        const ramp = colorScale.map(entry => ({
            value: entry.min,
            color: hexToRgb(entry.color)
        }));
        layerBg = new maptilerweather.TemperatureLayer({
            id: TEMP_LAYER_ID,
            opacity: 1, // Giữ opacity = 1 như yêu cầu
            colorramp: ramp,
        });
        
        // Thêm temperature layer trước hillshade như hill2 copy.html
        map.addLayer(layerBg, 'hillshade-shadows');
        
        // Thêm layer border riêng lên trên temp layer
        addBorderLayer();

        // Đảm bảo custom border layers luôn ở trên cùng
        setTimeout(() => {
            if (map.getLayer('state-boundaries')) {
                map.moveLayer('state-boundaries');
            }
            if (map.getLayer('country-boundaries')) {
                map.moveLayer('country-boundaries');
            }
            if (map.getLayer('simple-country-borders')) {
                map.moveLayer('simple-country-borders');
            }
            console.log('Moved border layers to top after temperature layer');
        }, 50);
        
        drawColorScale(ramp.map(r => r.color));
        scaleMinLabel.innerText = `${colorScale[0].min}°C`;
        scaleMaxLabel.innerText = `${colorScale[colorScale.length-1].max}°C`;
    }

    // Hàm thêm layer border riêng
    function addBorderLayer() {
        // Kiểm tra và thêm các layer border có sẵn của MapTiler
        const borderLayers = ['boundary', 'admin', 'country-border'];
        
        borderLayers.forEach(layerName => {
            try {
                if (map.getLayer(layerName)) {
                    // Di chuyển layer border lên trên temp layer
                    map.moveLayer(layerName, TEMP_LAYER_ID);
                    console.log(`Moved border layer ${layerName} above temp layer`);
                }
            } catch (e) {
                console.log(`Could not move border layer ${layerName}:`, e.message);
            }
        });
    }

    function refreshTimeUI() {
      if (window.timelineManager) {
        window.timelineManager.refreshTimeUI();
      } else {
        // Fallback to old method
        const d = layer.getAnimationTimeDate();
        if (d) {
          timeTextDiv.innerText = d.toString();
          timeSlider.value = +d;
        }
      }
      updatePointerValue(pointerLngLat);
    }

    function updatePointerValue(lngLat) {
      if (!lngLat || !mapLoaded) return;
      if (!layerBg && tempLayerVisible) return;
      
      pointerLngLat = lngLat;
      const valueWind = layer.pickAt(lngLat.lng, lngLat.lat);
      let valueTemp = null;
      if(layerBg) {
        valueTemp = layerBg.pickAt(lngLat.lng, lngLat.lat);
      }

      // Build tooltip content like the image
      let lines = [];
      
      // Temperature line
      if (tempLayerVisible && valueTemp && valueTemp.value != null) {
        lines.push(`${valueTemp.value.toFixed(0)}°C`);
      }
      
      // Wind speed and direction line
      if (valueWind) {
        let windLine = `${valueWind.speedKilometersPerHour.toFixed(0)} km/h`;
        
        // Get wind direction from available fields
        let directionAngle = null;
        if (valueWind.directionAngle !== undefined && valueWind.directionAngle !== null) {
          directionAngle = valueWind.directionAngle;
        } else if (valueWind.compassDirection) {
          // Convert compass direction back to angle for rotation
          directionAngle = compassToAngle(valueWind.compassDirection);
        }
        
        if (directionAngle !== null) {
          const compassText = valueWind.compassDirection || getCompassFromAngle(directionAngle);
          // Use +270° rotation to match wind animation direction
          const flowAngle = (directionAngle + 270) % 360;
          windLine += ` <span style="display:inline-block;transform:rotate(${flowAngle}deg)">➤</span> ${compassText}`;
        }
        
        lines.push(windLine);
      }
      
      if (lines.length === 0) {
        lines.push("No data");
      }
      
      // Use innerHTML to support HTML styling for rotated arrow
      pointerDataDiv.innerHTML = lines.join('<br>');
    }
    
    // Convert wind direction degrees to compass direction
    function getCompassFromAngle(degrees) {
      const directions = ['N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE', 
                         'S', 'SSW', 'SW', 'WSW', 'W', 'WNW', 'NW', 'NNW'];
      const index = Math.round(degrees / 22.5) % 16;
      return directions[index];
    }
    
    // Convert compass direction to angle for rotation
    function compassToAngle(compass) {
      const compassMap = {
        'N': 0, 'NNE': 22.5, 'NE': 45, 'ENE': 67.5,
        'E': 90, 'ESE': 112.5, 'SE': 135, 'SSE': 157.5,
        'S': 180, 'SSW': 202.5, 'SW': 225, 'WSW': 247.5,
        'W': 270, 'WNW': 292.5, 'NW': 315, 'NNW': 337.5
      };
      return compassMap[compass] || 0;
    }

    function hexToRgb(hex) {
      const v = hex.replace('#', '');
      return [parseInt(v.substring(0,2),16), parseInt(v.substring(2,4),16), parseInt(v.substring(4,6),16)];
    }

    function drawColorScale(colors) {
      const ctx = colorScaleCanvas.getContext('2d');
      ctx.clearRect(0, 0, colorScaleCanvas.width, colorScaleCanvas.height);
      for (let i = 0; i < colors.length; i++) {
        ctx.fillStyle = `rgb(${colors[i][0]},${colors[i][1]},${colors[i][2]})`;
        const y = colorScaleCanvas.height - (i + 1) * (colorScaleCanvas.height / colors.length);
        const h = colorScaleCanvas.height / colors.length + 1;
        ctx.fillRect(0, y, colorScaleCanvas.width, h);
      }
    }

    // === SCENE MANAGEMENT SYSTEM ===
    
    // Capture current scene state
    function getCurrentSceneState() {
      // Get projection object and extract name
      let currentProjection;
      let projectionName;
      try {
        currentProjection = map.getProjection?.() || map.projection;
        if (currentProjection) {
          projectionName = currentProjection.name || currentProjection.type || JSON.stringify(currentProjection);
          console.log(`💾 Full projection object:`, currentProjection);
          console.log(`💾 Projection properties:`, Object.keys(currentProjection));
        } else {
          projectionName = 'mercator';
        }
      } catch (e) {
        projectionName = 'mercator'; // fallback
      }
      
      console.log(`💾 Saving scene with projection name: ${projectionName}`);
      
      // Get wind settings for debugging
      const windSettings = window.windControlsManager ? window.windControlsManager.getCurrentSettings() : null;
      if (windSettings) {
        console.log(`💨 Capturing wind settings:`, windSettings);
      }
      
      return {
        // Timeline
        time: parseInt(timeSlider.value),
        
        // Viewport
        center: map.getCenter(),
        zoom: map.getZoom(),
        bearing: map.getBearing(),
        pitch: map.getPitch(),
        
        // Layers
        tempLayerVisible: tempLayerVisible,
        hillshadeVisible: hillshadeVisible,
        isPlaying: isPlaying,
        
        // Additional state
        projection: projectionName,
        projectionObject: currentProjection, // Store full object too
        
        // Wind Settings (simplified)
        windSettings: windSettings,
        
        // Timestamp
        savedAt: new Date().toISOString()
      };
    }
    
    // Save scene to specific slot
    function saveScene(sceneIndex, sceneState) {
      scenes[sceneIndex] = sceneState;
      
      // Update button appearance
      const button = document.getElementById(`scene-${sceneIndex}`);
      if (button) {
        button.classList.add('saved');
        button.title = `Saved: ${new Date(sceneState.savedAt).toLocaleString()}`;
      }
      
      // Save to localStorage for persistence
      localStorage.setItem('weather-app-scenes', JSON.stringify(scenes));
      
      // Add timeline marker for localStorage save
      if (window.timelineManager) {
        window.timelineManager.addSceneMarker(
          `scene-${sceneIndex}`,
          'localStorage',
          sceneState.time,
          `Scene ${sceneIndex}`
        );
      }
      
      console.log(`💾 Scene ${sceneIndex} saved:`, sceneState);
    }
    
    // Scene loading debounce protection
    let sceneLoadingTimeout = null;
    let isLoadingScene = false;
    
    // Compare two scene states to see if they match
    function compareSceneStates(current, target) {
      // Define tolerance for floating point comparison
      const tolerance = 0.001;
      
      // Compare viewport
      if (Math.abs(current.center[0] - target.center[0]) > tolerance ||
          Math.abs(current.center[1] - target.center[1]) > tolerance ||
          Math.abs(current.zoom - target.zoom) > tolerance ||
          Math.abs((current.bearing || 0) - (target.bearing || 0)) > tolerance ||
          Math.abs((current.pitch || 0) - (target.pitch || 0)) > tolerance) {
        console.log(`🔍 Viewport mismatch:`, {
          current: { center: current.center, zoom: current.zoom, bearing: current.bearing, pitch: current.pitch },
          target: { center: target.center, zoom: target.zoom, bearing: target.bearing, pitch: target.pitch }
        });
        return false;
      }
      
      // Compare timeline (within 5 seconds tolerance)
      if (Math.abs(current.time - target.time) > 5000) {
        console.log(`⏰ Timeline mismatch:`, {
          current: new Date(current.time),
          target: new Date(target.time),
          diff: Math.abs(current.time - target.time) / 1000 + 's'
        });
        return false;
      }
      
      // Compare layer visibility
      if (current.tempLayerVisible !== target.tempLayerVisible ||
          current.hillshadeVisible !== target.hillshadeVisible ||
          current.isPlaying !== target.isPlaying) {
        console.log(`👁️ Layer visibility mismatch:`, {
          current: { temp: current.tempLayerVisible, hillshade: current.hillshadeVisible, playing: current.isPlaying },
          target: { temp: target.tempLayerVisible, hillshade: target.hillshadeVisible, playing: target.isPlaying }
        });
        return false;
      }
      
      // Compare projection
      if ((current.projection || 'mercator') !== (target.projection || 'mercator')) {
        console.log(`🗺️ Projection mismatch:`, {
          current: current.projection || 'mercator',
          target: target.projection || 'mercator'
        });
        return false;
      }
      
      // Compare wind settings
      if (current.windSettings && target.windSettings) {
        const currentWind = current.windSettings;
        const targetWind = target.windSettings;
        
        if (currentWind.visible !== targetWind.visible) {
          console.log(`💨 Wind visibility mismatch:`, {
            current: currentWind.visible,
            target: targetWind.visible
          });
          return false;
        }
        
        // Compare colors (allow small differences due to rounding)
        const colorTolerance = 2;
        if (currentWind.slowColor && targetWind.slowColor) {
          for (let i = 0; i < 4; i++) {
            if (Math.abs((currentWind.slowColor[i] || 0) - (targetWind.slowColor[i] || 0)) > colorTolerance) {
              console.log(`💨 Wind slow color mismatch:`, {
                current: currentWind.slowColor,
                target: targetWind.slowColor
              });
              return false;
            }
          }
        }
        
        if (currentWind.fastColor && targetWind.fastColor) {
          for (let i = 0; i < 4; i++) {
            if (Math.abs((currentWind.fastColor[i] || 0) - (targetWind.fastColor[i] || 0)) > colorTolerance) {
              console.log(`💨 Wind fast color mismatch:`, {
                current: currentWind.fastColor,
                target: targetWind.fastColor
              });
              return false;
            }
          }
        }
      } else if (current.windSettings || target.windSettings) {
        console.log(`💨 Wind settings existence mismatch:`, {
          current: !!current.windSettings,
          target: !!target.windSettings
        });
        return false;
      }
      
      console.log(`✅ Scene states match completely`);
      return true;
    }
    
    // Load scene from specific slot (check both localStorage and IndexedDB)
    async function loadScene(sceneIndex) {
      // Prevent multiple rapid clicks
      if (isLoadingScene) {
        console.log(`⏳ Scene loading in progress, ignoring click`);
        return;
      }
      
      // Check if current state matches the scene to load
      let shouldSkipLoad = false;
      if (currentSceneIndex === sceneIndex) {
        // Compare current state with scene state
        const currentState = getCurrentSceneState();
        
        // Try localStorage first for comparison
        let targetScene = scenes[sceneIndex];
        
        // If not in localStorage, try IndexedDB
        if (!targetScene && window.offlineSceneManager) {
          targetScene = await window.offlineSceneManager.loadScene(`scene-${sceneIndex}`);
        }
        
        if (targetScene) {
          const stateMatches = compareSceneStates(currentState, targetScene);
          if (stateMatches) {
            console.log(`📍 Already at Scene ${sceneIndex} with matching state, ignoring click`);
            shouldSkipLoad = true;
          } else {
            console.log(`🔄 At Scene ${sceneIndex} but state differs, reloading...`);
          }
        }
      }
      
      if (shouldSkipLoad) {
        return;
      }
      
      // Try localStorage first
      let sceneState = scenes[sceneIndex];
      
      // If not found in localStorage, try IndexedDB
      if (!sceneState && window.offlineSceneManager) {
        console.log(`🔍 Checking IndexedDB for scene-${sceneIndex}...`);
        sceneState = await window.offlineSceneManager.loadScene(`scene-${sceneIndex}`);
        
        if (sceneState) {
          console.log(`📦 Found scene in IndexedDB:`, sceneState);
        }
      }
      
      if (!sceneState) {
        console.log(`❌ Scene ${sceneIndex} is empty in both localStorage and IndexedDB`);
        return;
      }
      
      console.log(`🎬 Loading Scene ${sceneIndex}:`, sceneState);
      
      // Set loading flag
      isLoadingScene = true;
      
      // Clear any existing timeout
      if (sceneLoadingTimeout) {
        clearTimeout(sceneLoadingTimeout);
      }
      
      // Update current scene indicator
      updateActiveSceneButton(sceneIndex);
      
      // Apply timeline
      if (sceneState.time) {
        timeSlider.value = sceneState.time;
        layer.setAnimationTime(sceneState.time / 1000);
        if (layerBg) layerBg.setAnimationTime(sceneState.time / 1000);
        refreshTimeUI();
      }
      
      // Apply viewport
      map.easeTo({
        center: sceneState.center,
        zoom: sceneState.zoom,
        bearing: sceneState.bearing || 0,
        pitch: sceneState.pitch || 0,
        duration: 1500
      });
      
      // Apply layer visibility
      if (sceneState.tempLayerVisible !== tempLayerVisible) {
        toggleTempButton.click();
      }
      
      if (sceneState.hillshadeVisible !== hillshadeVisible) {
        hillshadeToggleButton.click();
      }
      
      // Apply playing state
      if (sceneState.isPlaying !== isPlaying) {
        playPauseButton.click();
      }
      
      // Apply projection (simulate user click on projection control)
      if (sceneState.projection) {
        setTimeout(() => {
          let currentProjectionObj = map.getProjection?.() || map.projection;
          let currentProjectionName = currentProjectionObj?.name || currentProjectionObj?.type || 'mercator';
          
          console.log(`🗺️ Current projection: ${currentProjectionName}, Target: ${sceneState.projection}`);
          
          if (sceneState.projection !== currentProjectionName) {
            console.log(`🔄 Simulating user click to change projection to: ${sceneState.projection}`);
            
            try {
              // Find projection control button and simulate click
              const projectionButton = document.querySelector('.maplibregl-ctrl-projection button, .maptiler-ctrl-projection button, button[data-projection], .projection-control button');
              
              if (projectionButton) {
                console.log(`📍 Found projection button:`, projectionButton);
                projectionButton.click();
                console.log(`✅ Projection button clicked`);
              } else {
                // Try to find any button containing projection-related text
                const allButtons = document.querySelectorAll('button');
                let foundButton = null;
                
                allButtons.forEach(btn => {
                  if (btn.textContent.toLowerCase().includes('globe') || 
                      btn.textContent.toLowerCase().includes('projection') ||
                      btn.title.toLowerCase().includes('globe') ||
                      btn.title.toLowerCase().includes('projection')) {
                    foundButton = btn;
                  }
                });
                
                if (foundButton) {
                  console.log(`📍 Found projection-related button:`, foundButton);
                  foundButton.click();
                  console.log(`✅ Projection-related button clicked`);
                } else {
                  console.log(`❌ Could not find projection control button`);
                  console.log(`📋 Available buttons:`, Array.from(allButtons).map(btn => ({
                    text: btn.textContent,
                    title: btn.title,
                    className: btn.className
                  })));
                }
              }
            } catch (e) {
              console.error('❌ Projection click simulation failed:', e);
            }
          } else {
            console.log('📍 Projection already matches, no change needed');
          }
        }, 100);
      }
      
      // Apply wind settings (with delay to ensure wind layer is ready)
      if (sceneState.windSettings && window.windControlsManager) {
        setTimeout(() => {
          console.log('💨 Restoring wind settings:', sceneState.windSettings);
          window.windControlsManager.loadSettings(sceneState.windSettings);
          
          // Trigger the appropriate actions to apply settings
          if (sceneState.windSettings.visible !== undefined) {
            // Apply visibility first
            const visibilitySettings = {
              visible: sceneState.windSettings.visible,
              type: 'visibility'
            };
            
            if (window.windControlsManager.onApplyCallback) {
              window.windControlsManager.onApplyCallback(visibilitySettings);
            }
            
            // Then apply colors if layer is visible
            if (sceneState.windSettings.visible && (sceneState.windSettings.slowColor || sceneState.windSettings.fastColor)) {
              setTimeout(() => {
                const colorSettings = {
                  slowColor: sceneState.windSettings.slowColor,
                  fastColor: sceneState.windSettings.fastColor,
                  visible: sceneState.windSettings.visible,
                  type: 'color'
                };
                
                if (window.windControlsManager.onApplyCallback) {
                  window.windControlsManager.onApplyCallback(colorSettings);
                }
              }, 100); // Small delay for visibility to take effect
            }
          }
        }, 200); // Wait for wind layer to be ready
      }
      
      currentSceneIndex = sceneIndex;
      
      // Reset loading flag after animation completes
      sceneLoadingTimeout = setTimeout(() => {
        isLoadingScene = false;
        console.log(`✅ Scene ${sceneIndex} loading completed`);
      }, 2000); // Wait for map animation (1500ms) + buffer
    }
    
    // Save current scene (with selection UI)
    function saveCurrentScene() {
      const saveButton = document.getElementById('save-scene');
      
      // Disable button temporarily to prevent spam clicks
      saveButton.disabled = true;
      
      if (selectedSceneToSave) {
        // Save to selected slot
        const sceneState = getCurrentSceneState();
        saveScene(selectedSceneToSave, sceneState);
        selectedSceneToSave = null;
        updateSaveButtonState();
      } else {
        // Show selection mode
        showSceneSelection();
      }
      
      // Re-enable button after short delay
      setTimeout(() => {
        saveButton.disabled = false;
      }, 300); // 0.3s cooldown
    }
    
    // Show scene selection for saving
    function showSceneSelection() {
      selectedSceneToSave = null;
      
      // Highlight save button
      const saveButton = document.getElementById('save-scene');
      saveButton.textContent = '📍 Select Slot';
      saveButton.style.background = 'rgba(255,87,34,0.9)';
      
      // Add visual indication to scene buttons
      for (let i = 1; i <= 5; i++) {
        const button = document.getElementById(`scene-${i}`);
        button.style.border = '2px dashed #FFC107';
        button.style.boxShadow = '0 0 10px rgba(255, 193, 7, 0.5)';
      }
      
      // Set flag to show save options when scene button is clicked
      window.saveSelectionMode = true;
    }
    
    // Update save button state
    function updateSaveButtonState() {
      const saveButton = document.getElementById('save-scene');
      
      if (selectedSceneToSave) {
        saveButton.textContent = `💾 Save → ${selectedSceneToSave}`;
        saveButton.style.background = 'rgba(76,175,80,0.9)';
      } else {
        saveButton.textContent = '💾 Save';
        saveButton.style.background = 'rgba(255,193,7,0.9)';
      }
    }
    
    // Update active scene button appearance
    function updateActiveSceneButton(sceneIndex) {
      // Remove active from all
      for (let i = 1; i <= 5; i++) {
        const button = document.getElementById(`scene-${i}`);
        if (button) {
          button.classList.remove('active');
          
          // Ensure active indicator exists
          if (!button.querySelector('.active-indicator')) {
            const indicator = document.createElement('div');
            indicator.className = 'active-indicator';
            button.appendChild(indicator);
          }
        }
      }
      
      // Add active to current
      const activeButton = document.getElementById(`scene-${sceneIndex}`);
      if (activeButton) {
        activeButton.classList.add('active');
        console.log(`🎯 Scene ${sceneIndex} is now active`);
      }
    }
    
    // Load scenes from both localStorage and IndexedDB on startup
    async function loadSavedScenes() {
      try {
        // Load from localStorage first
        const savedScenes = localStorage.getItem('weather-app-scenes');
        if (savedScenes) {
          scenes = JSON.parse(savedScenes);
          
          // Update button appearances for localStorage scenes
          Object.keys(scenes).forEach(sceneIndex => {
            const button = document.getElementById(`scene-${sceneIndex}`);
            if (button && scenes[sceneIndex]) {
              button.classList.add('saved');
              button.title = `Saved: ${new Date(scenes[sceneIndex].savedAt).toLocaleString()}`;
            }
          });
          
          console.log('🎬 Loaded localStorage scenes:', Object.keys(scenes));
        }
        
        // Load from IndexedDB
        if (window.offlineSceneManager) {
          console.log('📦 Loading IndexedDB scenes...');
          await window.offlineSceneManager.loadSavedScenesUI();
        }
        
      } catch (e) {
        console.log('Error loading saved scenes:', e);
      }
    }
    
    // Clear all saved scenes (both localStorage and IndexedDB)
    function clearAllScenes() {
      // Confirmation dialog
      const confirmClear = confirm('🗑️ Xóa tất cả scenes đã lưu?\n\nBao gồm cả Short Save và Long Save (offline).\nHành động này không thể hoàn tác!');
      
      if (confirmClear) {
        const clearButton = document.getElementById('clear-scenes');
        const originalText = clearButton.textContent;
        
        // Show loading state
        clearButton.textContent = '🔄 Clearing...';
        clearButton.style.background = 'rgba(255,152,0,0.9)';
        clearButton.disabled = true;
        
        // Clear localStorage first
        localStorage.removeItem('weather-app-scenes');
        scenes = {};
        currentSceneIndex = null;
        
        // Clear IndexedDB offline scenes
        if (window.offlineSceneManager) {
          console.log('🗑️ Clearing offline scenes from IndexedDB...');
          window.offlineSceneManager.clearAllScenes();
        }
        
        // Clear timeline markers
        if (window.timelineManager) {
          window.timelineManager.clearAllMarkers();
        }
        
        // Reset all button appearances
        for (let i = 1; i <= 5; i++) {
          const button = document.getElementById(`scene-${i}`);
          if (button) {
            button.classList.remove('saved', 'active', 'short-save', 'long-save');
            button.classList.add('empty-scene');
            button.title = '';
            button.textContent = `Scene ${i}`;
            button.style.border = 'none';
            button.style.boxShadow = '';
            
            // Remove delete buttons if any
            const deleteBtn = button.querySelector('.delete-btn');
            if (deleteBtn) deleteBtn.remove();
          }
        }
        
        // Reset save mode if active
        if (window.saveSelectionMode) {
          resetSaveMode();
        }
        
        console.log('🗑️ All scenes cleared (localStorage + IndexedDB)');
        
        // Show success message
        setTimeout(() => {
          clearButton.textContent = '✅ All Cleared!';
          clearButton.style.background = 'rgba(76,175,80,0.9)';
          
          setTimeout(() => {
            clearButton.textContent = originalText;
            clearButton.style.background = 'rgba(244,67,54,0.9)';
            clearButton.disabled = false;
          }, 2000);
        }, 500);
      }
    }

    // === OFFLINE SCENE MANAGER INTEGRATION ===
    
    // Show save options context menu
    function showSaveOptionsMenu(event, sceneId) {
      console.log(`🎯 showSaveOptionsMenu called for: ${sceneId}`, event);
      
      // Remove existing menu
      const existingMenu = document.querySelector('.save-options-menu');
      if (existingMenu) {
        existingMenu.remove();
      }

      const menu = document.createElement('div');
      menu.className = 'save-options-menu';
      menu.style.cssText = `
        position: fixed;
        top: ${event.clientY}px;
        left: ${event.clientX}px;
        background: rgba(0, 0, 0, 0.9);
        border: 1px solid #444;
        border-radius: 8px;
        padding: 8px 0;
        min-width: 200px;
        z-index: 10000;
        color: white;
        font-family: Arial, sans-serif;
        font-size: 14px;
      `;

      const shortSaveOption = document.createElement('div');
      shortSaveOption.style.cssText = `
        padding: 8px 16px;
        cursor: pointer;
        border-bottom: 1px solid #444;
      `;
      shortSaveOption.innerHTML = '💾 Short Save<br><small style="color: #aaa;">Metadata only (3h TTL)</small>';
      shortSaveOption.onclick = () => {
        handleShortSave(sceneId);
        menu.remove();
      };
      shortSaveOption.onmouseover = () => shortSaveOption.style.background = 'rgba(76, 175, 80, 0.3)';
      shortSaveOption.onmouseout = () => shortSaveOption.style.background = 'transparent';

      const longSaveOption = document.createElement('div');
      longSaveOption.style.cssText = `
        padding: 8px 16px;
        cursor: pointer;
      `;
      longSaveOption.innerHTML = '📦 Long Save<br><small style="color: #aaa;">With tiles (offline)</small>';
      longSaveOption.onclick = () => {
        handleLongSave(sceneId);
        menu.remove();
      };
      longSaveOption.onmouseover = () => longSaveOption.style.background = 'rgba(244, 67, 54, 0.3)';
      longSaveOption.onmouseout = () => longSaveOption.style.background = 'transparent';

      menu.appendChild(shortSaveOption);
      menu.appendChild(longSaveOption);
      document.body.appendChild(menu);

      // Close menu on click outside
      const closeMenu = (e) => {
        if (!menu.contains(e.target)) {
          menu.remove();
          document.removeEventListener('click', closeMenu);
        }
      };
      setTimeout(() => document.addEventListener('click', closeMenu), 100);
    }

    // Handle short save (metadata only)
    function handleShortSave(sceneId) {
      const sceneIndex = parseInt(sceneId.replace('scene-', ''));
      const sceneName = prompt('💾 Scene name:', `Scene ${sceneIndex}`);
      if (!sceneName) {
        resetSaveMode();
        return;
      }

      const mapState = getCurrentSceneState();
      
      if (window.offlineSceneManager) {
        window.offlineSceneManager.saveShortScene(sceneId, sceneName, mapState);
      } else {
        console.warn('Offline Scene Manager not available, falling back to localStorage');
        saveScene(sceneIndex, mapState);
      }
      
      // Reset save mode
      resetSaveMode();
    }

    // Handle long save (with tiles)
    function handleLongSave(sceneId) {
      const sceneIndex = parseInt(sceneId.replace('scene-', ''));
      const sceneName = prompt('📦 Scene name (offline):', `Offline ${sceneIndex}`);
      if (!sceneName) {
        resetSaveMode();
        return;
      }

      if (window.offlineSceneManager) {
        const mapState = getCurrentSceneState();
        const activeLayers = getActiveLayers();
        window.offlineSceneManager.saveLongScene(sceneId, sceneName, mapState, map, activeLayers);
      } else {
        alert('❌ Offline Scene Manager not available');
      }
      
      // Reset save mode
      resetSaveMode();
    }

    // Reset save selection mode
    function resetSaveMode() {
      window.saveSelectionMode = false;
      
      // Reset button appearances
      for (let i = 1; i <= 5; i++) {
        const button = document.getElementById(`scene-${i}`);
        if (button) {
          button.style.border = 'none';
          button.style.boxShadow = '';
        }
      }
      
      // Reset save button
      const saveButton = document.getElementById('save-scene');
      saveButton.textContent = '💾 Save';
      saveButton.style.background = 'rgba(255,193,7,0.9)';
      
      console.log('🔄 Save mode reset');
    }

    // Get currently active layers for tile downloading
    function getActiveLayers() {
      const activeLayers = [];
      
      // Add base map tiles
      activeLayers.push('basic');
      
      // Add weather layers if visible
      if (tempLayerVisible) {
        activeLayers.push('temperature');
      }
      
      if (map.getLayoutProperty(layer.id, 'visibility') === 'visible') {
        activeLayers.push('wind');
      }
      
      // Add terrain if visible
      if (hillshadeVisible) {
        activeLayers.push('terrain');
      }
      
      return activeLayers;
    }

    // Add both left-click and right-click handlers to scene buttons
    function addContextMenusToSceneButtons() {
      console.log('🔧 Adding event handlers to scene buttons...');
      
      // Only add to scene-1 through scene-5, not save/clear buttons
      for (let i = 1; i <= 5; i++) {
        const button = document.getElementById(`scene-${i}`);
        if (button) {
          console.log(`📱 Adding handlers to: ${button.id}`);
          
          // Left-click: Load scene OR show save options if in save mode
          button.addEventListener('click', (e) => {
            console.log(`👆 Left-click detected on: ${button.id}`);
            
            if (window.saveSelectionMode) {
              // In save selection mode - show save options popup
              console.log(`💾 Save mode active, showing options for: ${button.id}`);
              showSaveOptionsMenu(e, button.id);
            } else {
              // Normal mode - load scene
              loadScene(i);
            }
          });
          
          // Right-click: Disable default context menu
          button.addEventListener('contextmenu', (e) => {
            e.preventDefault();
          });
        }
      }
    }

    // Initialize context menus immediately when DOM is ready
    document.addEventListener('DOMContentLoaded', () => {
      console.log('🚀 DOM ready, adding context menus...');
      addContextMenusToSceneButtons();
      
      // ESC key to cancel save mode
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && window.saveSelectionMode) {
          console.log('⌨️ ESC pressed, canceling save mode');
          resetSaveMode();
        }
      });
    });

    // Also try after a delay in case DOM isn't ready yet
    setTimeout(() => {
      console.log('🔄 Backup: Adding context menus after delay...');
      addContextMenusToSceneButtons();
    }, 500);

    // Wind animation settings will be managed by WindControlsManager

    // Initialize simplified wind controls
    function initializeWindControls() {
      // Create new WindControlsManager instance
      window.windControlsManager = new WindControlsManager();
      
      // Setup callback for apply changes
      window.windControlsManager.onApply((settings) => {
        console.log('🎛️ Wind controls callback:', settings);
        
        if (settings.type === 'visibility') {
          // Visibility change - luôn apply ngay
          toggleWindVisibility(settings.visible);
        } else if (settings.type === 'color') {
          // Color change - chỉ apply nếu đang visible
          if (settings.visible) {
            console.log('🎨 Applying colors because layer is visible');
            updateWindColors(settings);
          } else {
            console.log('💤 Layer hidden, colors saved but not applied');
            // Lưu màu nhưng không apply vì layer đang ẩn
          }
        }
      });

      console.log('🌪️ Simplified wind controls initialized');
    }

    // Simple wind controls functions

    // Update wind colors by recreating layer safely
    function updateWindColors(colorSettings) {
      if (!layer || !map) {
        console.error('❌ Layer not ready for color update');
        return;
      }

      try {
        console.log('🎨 Updating wind colors:', colorSettings);
        
        // Store current state
        const isPlaying = layer.isPlaying();
        const currentTime = layer.getAnimationTime();
        const wasVisible = map.getLayoutProperty('Wind Particles', 'visibility') === 'visible';
        
        // Remove current layer
        map.removeLayer('Wind Particles');
        
        // Create new layer with updated colors
        layer = new maptilerweather.WindLayer({
          id: "Wind Particles",
          colorramp: maptilerweather.ColorRamp.builtin.NULL,
          speed: 0.002, 
          fadeFactor: 0.03, 
          maxAmount: 256, 
          density: 400,
          color: colorSettings.slowColor || [255, 0, 0, 30], 
          fastColor: colorSettings.fastColor || [0, 255, 0, 100],
        });
        
        // Add new layer to map
        map.addLayer(layer);
        
        // Restore state when layer is ready
        layer.on("sourceReady", () => {
          if (currentTime) {
            layer.setAnimationTime(currentTime);
          }
          if (isPlaying) {
            layer.play();
          }
          if (!wasVisible) {
            map.setLayoutProperty('Wind Particles', 'visibility', 'none');
          }
          
          console.log('✅ Wind colors updated successfully');
        });
        
        // Setup events for new layer
        setupWindLayerEvents();
        
      } catch (e) {
        console.error('❌ Error updating wind colors:', e);
      }
    }

    // Toggle wind layer visibility
    function toggleWindVisibility(visible) {
      if (!map || !map.getLayer('Wind Particles')) {
        console.error('❌ Wind layer not found');
        return;
      }

      try {
        const visibility = visible ? 'visible' : 'none';
        map.setLayoutProperty('Wind Particles', 'visibility', visibility);
        console.log(`👁️ Wind layer ${visible ? 'shown' : 'hidden'}`);
        
        // Nếu vừa show layer và có màu đã lưu, apply màu đó
        if (visible && window.windControlsManager) {
          const currentSettings = window.windControlsManager.getCurrentSettings();
          if (currentSettings.slowColor || currentSettings.fastColor) {
            console.log('🎨 Applying saved colors after showing layer');
            updateWindColors(currentSettings);
          }
        }
      } catch (e) {
        console.error('❌ Error toggling wind visibility:', e);
      }
    }

    // Setup wind layer events
    function setupWindLayerEvents() {
      if (!layer) return;

      layer.on("sourceReady", () => {
        refreshTimeUI();
        const startDate = layer.getAnimationStartDate();
        const endDate = layer.getAnimationEndDate();
        timeSlider.min = +startDate;
        timeSlider.max = +endDate;
        
        // Initialize timeline manager if available
        if (window.timelineManager) {
          window.timelineManager.initializeFixedTimeline();
        }
        
        console.log('🌪️ Wind layer source ready');
      });

      layer.on("tick", () => {
        if (window.timelineManager) {
          window.timelineManager.refreshTimeUI();
        }
      });
    }

    // Wind settings functions moved to WindControlsManager
  </script>
</body>
</html>