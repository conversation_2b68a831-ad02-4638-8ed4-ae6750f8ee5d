/**
 * Service Worker for Tile Caching
 */

const CACHE_NAME = 'weather-tiles-v1';
const TILE_CACHE_TTL = 60 * 60 * 1000; // 1 hour

let db = null;

// Initialize IndexedDB in service worker
async function initDB() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('WeatherAppOffline', 1);
    request.onsuccess = () => {
      db = request.result;
      resolve();
    };
    request.onerror = () => reject(request.error);
  });
}

// Check if URL is a weather tile
function isTileRequest(url) {
  return url.includes('api.maptiler.com/tiles') || 
         url.includes('weather') ||
         url.includes('.png') ||
         url.includes('.jpg');
}

// Get tile from IndexedDB global pool
async function getTileFromDB(url) {
  if (!db) return null;
  
  try {
    const transaction = db.transaction(['global_tile_pool'], 'readonly');
    const store = transaction.objectStore('global_tile_pool');
    
    return new Promise((resolve) => {
      const request = store.get(url);
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => resolve(null);
    });
  } catch (error) {
    console.error('DB tile fetch failed:', error);
    return null;
  }
}

// Handle tile requests with priority: IndexedDB -> Cache -> Network
async function handleTileRequest(request) {
  const url = request.url;
  
  try {
    // Priority 1: Check IndexedDB global tile pool
    const dbTile = await getTileFromDB(url);
    if (dbTile && dbTile.data) {
      console.log('📦 Serving from IndexedDB:', url);
      return new Response(dbTile.data, {
        headers: {
          'Content-Type': 'image/png',
          'Cache-Control': 'max-age=3600'
        }
      });
    }

    // Priority 2: Check runtime cache
    const cache = await caches.open(CACHE_NAME);
    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
      console.log('💾 Serving from cache:', url);
      return cachedResponse;
    }

    // Priority 3: Fetch from network and cache
    console.log('🌐 Fetching from network:', url);
    const response = await fetch(request);
    
    if (response.ok) {
      // Clone response for caching
      const responseClone = response.clone();
      cache.put(request, responseClone);
      
      // Set cache expiry
      setTimeout(() => {
        cache.delete(request);
      }, TILE_CACHE_TTL);
    }
    
    return response;
    
  } catch (error) {
    console.error('Tile request failed:', error);
    
    // Return placeholder or empty response
    return new Response(new ArrayBuffer(0), {
      status: 204,
      statusText: 'No Content'
    });
  }
}

// Install event
self.addEventListener('install', (event) => {
  console.log('🔧 Service Worker installing...');
  self.skipWaiting();
});

// Activate event
self.addEventListener('activate', (event) => {
  console.log('✅ Service Worker activated');
  
  event.waitUntil(
    initDB().then(() => {
      console.log('📚 Service Worker DB ready');
    }).catch(error => {
      console.error('Service Worker DB init failed:', error);
    })
  );
  
  return self.clients.claim();
});

// Fetch event - intercept tile requests
self.addEventListener('fetch', (event) => {
  const request = event.request;
  
  // Only handle tile requests
  if (request.method === 'GET' && isTileRequest(request.url)) {
    event.respondWith(handleTileRequest(request));
  }
});

// Message event for communication with main thread
self.addEventListener('message', (event) => {
  const { type, data } = event.data;
  
  switch (type) {
    case 'INIT_DB':
      initDB().then(() => {
        event.ports[0]?.postMessage({ success: true });
      }).catch(error => {
        event.ports[0]?.postMessage({ success: false, error });
      });
      break;
      
    case 'CLEAR_CACHE':
      caches.delete(CACHE_NAME).then(() => {
        event.ports[0]?.postMessage({ success: true });
      });
      break;
  }
});
