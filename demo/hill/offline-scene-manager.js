/**
 * Offline Scene Manager for Weather App
 * Vai trò: Nhạc trưởng - Điều phối toàn bộ hoạt động offline, quản lý UI và DB.
 */
class OfflineSceneManager {
  constructor() {
    this.db = null;
    this.isProcessing = false; // Cờ để ngăn các hành động xung đột

    // Hằng số
    this.DB_NAME = 'MapTilerOfflineCache';
    this.DB_VERSION = 5; // Phải đồng bộ với cấu trúc DB
    this.MAX_SCENES = 5;
    
    // Trạng thái của chế độ lưu
    this.isSaveModeActive = false;
    this.saveModeType = null; // 'short' hoặc 'long'

    // Bắt đầu khởi tạo
    this.init();
  }

  /**
   * Khởi tạo toàn bộ hệ thống
   */
  async init() {
    try {
      await this.initIndexedDB();
      this.setupOfflineProtocol();
      await this.loadAndRenderUI();
      console.log('✅ Offline Scene Manager đã khởi tạo thành công.');
    } catch (error) {
      console.error('❌ Lỗi khởi tạo Offline Scene Manager:', error);
    }
  }

  // =================================================================
  // KHỞI TẠO VÀ CÀI ĐẶT
  // =================================================================

  async initIndexedDB() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.DB_NAME, this.DB_VERSION);
      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        console.log('🗄️ Kết nối IndexedDB thành công.');
        resolve();
      };
      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        console.log('🔄 Cập nhật cấu trúc IndexedDB...');
        // Định nghĩa toàn bộ cấu trúc DB ở đây
        if (!db.objectStoreNames.contains('metadata')) db.createObjectStore('metadata', { keyPath: 'name' });
        if (!db.objectStoreNames.contains('scenes')) db.createObjectStore('scenes', { keyPath: 'sceneName' });
        if (!db.objectStoreNames.contains('wind_tiles')) db.createObjectStore('wind_tiles', { keyPath: 'id' }).createIndex('datasetId', 'datasetId');
        if (!db.objectStoreNames.contains('temperature_tiles')) db.createObjectStore('temperature_tiles', { keyPath: 'id' });
        if (!db.objectStoreNames.contains('terrain_tiles')) db.createObjectStore('terrain_tiles', { keyPath: 'id' });
        if (!db.objectStoreNames.contains('vector_tiles')) db.createObjectStore('vector_tiles', { keyPath: 'id' }).createIndex('tileType', 'tileType');
      };
    });
  }
  
  /** Tải các scene đã lưu và render UI lần đầu */
  async loadAndRenderUI() {
    const scenes = await this.getAllFromStore('scenes');
    const sceneMap = new Map(scenes.map(s => [s.id, s])); // Giả sử scene có 'id' để map với button

    for (let i = 1; i <= this.MAX_SCENES; i++) {
        const sceneId = `scene-${i}`;
        const scene = sceneMap.get(sceneId);
        if (scene) {
            this.updateSceneButtonUI(sceneId, scene.type, scene.sceneName);
        } else {
            this.updateSceneButtonUI(sceneId, 'empty');
        }
    }
  }

  // =================================================================
  // QUẢN LÝ CHẾ ĐỘ LƯU (SAVE MODE)
  // =================================================================
  
  /**
   * Kích hoạt chế độ lưu, cho phép người dùng chọn slot.
   * @param {'short' | 'long'} type - Loại lưu sẽ thực hiện.
   */
  enterSaveMode(type) {
    if (this.isProcessing) return;
    this.isSaveModeActive = true;
    this.saveModeType = type;
    document.body.classList.add('save-mode-active');
    this.showToast(`Chế độ lưu ${type === 'short' ? 'Nhanh' : 'Đầy đủ'}: Chọn một slot để lưu.`, 'info');
    
    // Thêm listener cho các nút scene
    for (let i = 1; i <= this.MAX_SCENES; i++) {
        const button = document.getElementById(`scene-${i}`);
        button.onclick = () => this.handleSlotSelection(`scene-${i}`);
    }
  }

  /** Thoát khỏi chế độ lưu */
  exitSaveMode() {
    this.isSaveModeActive = false;
    this.saveModeType = null;
    document.body.classList.remove('save-mode-active');
    // Gỡ listener hoặc reset lại onclick cho các nút
    this.loadAndRenderUI(); // Cách đơn giản để reset listener
  }

  /** Xử lý khi người dùng chọn một slot trong chế độ lưu */
  async handleSlotSelection(sceneId) {
    if (!this.isSaveModeActive || this.isProcessing) return;
    
    this.isProcessing = true; // Bắt đầu xử lý, khóa các hành động khác
    this.exitSaveMode(); // Thoát khỏi chế độ chọn

    const sceneName = prompt(`Nhập tên cho scene:`, `Scene ${sceneId.split('-')[1]}`);
    if (!sceneName) {
        this.isProcessing = false;
        return; // Người dùng hủy
    }

    const button = document.getElementById(sceneId);
    const oldSceneName = button.dataset.sceneName;
    const isOverwrite = !!oldSceneName;

    if (isOverwrite) {
        const confirmOverwrite = confirm(`Slot này đã có dữ liệu của scene '${oldSceneName}'. Bạn có chắc chắn muốn GHI ĐÈ?`);
        if (!confirmOverwrite) {
            this.isProcessing = false;
            return;
        }
    }

    // Cập nhật UI sang trạng thái "đang lưu"
    this.updateSceneButtonUI(sceneId, isOverwrite ? 'overwriting' : 'saving');

    // Bắt đầu quy trình lưu dựa trên loại đã chọn
    try {
        if (isOverwrite) {
            await this.cleanupForOverwrite(sceneId, oldSceneName, this.saveModeType);
        }
        
        // Gán id vào mapState để có thể map lại với button khi load
        const mapState = this.getCurrentMapState();
        mapState.id = sceneId;

        let success = false;
        if (this.saveModeType === 'short') {
            success = await this.saveShortScene(sceneId, sceneName, mapState);
        } else {
            success = await this.saveLongScene(sceneId, sceneName, window.map);
        }
        
        if (!success) { // Nếu lưu thất bại, trả về trạng thái rỗng
             this.updateSceneButtonUI(sceneId, 'empty');
        }
    } catch (error) {
        console.error(`Lỗi nghiêm trọng trong quá trình lưu ${sceneId}:`, error);
        this.showToast('Lưu thất bại!', 'error');
        this.updateSceneButtonUI(sceneId, 'empty');
    } finally {
        this.isProcessing = false;
    }
  }

  // =================================================================
  // LOGIC LƯU VÀ GHI ĐÈ
  // =================================================================

  async saveShortScene(sceneId, sceneName, mapState) {
    const sceneData = {
        sceneName, type: 'short', timestamp: Date.now(), mapState, id: sceneId
    };
    await this.putToStore('scenes', sceneData);
    this.updateSceneButtonUI(sceneId, 'short', sceneName);
    this.showToast('Lưu nhanh thành công!', 'success');
    return true;
  }

  async saveLongScene(sceneId, sceneName, map) {
    const modal = this.showSaveModal(sceneName);
    try {
        const downloader = new window.CompleteOfflineDownloader();
        const onProgress = (completed, total) => this.updateProgressModal(modal, completed, total);
        const result = await downloader.performLongSave(sceneName, map, this.db, onProgress);

        if (result.success) {
            // Cần gán id vào scene metadata để map với button khi load
            const finalSceneData = await this.getFromStore('scenes', sceneName);
            finalSceneData.id = sceneId;
            await this.putToStore('scenes', finalSceneData);
            
            this.updateSceneButtonUI(sceneId, 'long', sceneName);
            this.showToast('Lưu đầy đủ thành công!', 'success');
            return true;
        } else {
            throw new Error(result.error || 'Quá trình tải xuống gặp lỗi.');
        }
    } finally {
        this.hideSaveModal(modal);
    }
  }
  
  /**
   * Dọn dẹp dữ liệu cũ trước khi ghi đè
   * @param {string} sceneId - ID của button
   * @param {string} oldSceneName - Tên của scene cũ (key trong DB)
   * @param {'short' | 'long'} newSaveType - Loại lưu mới sẽ được thực hiện
   */
  async cleanupForOverwrite(sceneId, oldSceneName, newSaveType) {
      console.log(`🗑️ Bắt đầu dọn dẹp để ghi đè scene '${oldSceneName}'...`);
      const oldScene = await this.getFromStore('scenes', oldSceneName);
      if (!oldScene) return;

      // Trường hợp 1, 2, 3: Dọn dẹp nhanh và triệt để
      // Ghi đè BẰNG Short Save, hoặc ghi đè một Short Save
      if (newSaveType === 'short' || oldScene.type === 'short') {
          console.log("-> Áp dụng dọn dẹp nhanh/triệt để.");
          // TODO: Logic xóa các tile liên quan đến oldScene nếu nó là 'long'
          // ...
          await this.deleteFromStore('scenes', oldSceneName);
          return;
      }
      
      // Trường hợp 4: Long -> Long (dọn dẹp thông minh)
      if (oldScene.type === 'long' && newSaveType === 'long') {
          console.log("-> Áp dụng dọn dẹp thông minh (Long -> Long).");
          // Logic này phức tạp, sẽ được triển khai sau.
          // Tạm thời, chúng ta cũng dọn dẹp triệt để.
          // TODO: Logic so sánh, giảm refCount...
          await this.deleteFromStore('scenes', oldSceneName);
          return;
      }
  }

  // =================================================================
  // CHỨC NĂNG TẢI SCENE (LOAD)
  // =================================================================

  async loadScene(sceneId) {
    if (this.isProcessing) return;
    const button = document.getElementById(sceneId);
    const sceneName = button.dataset.sceneName;

    if (!sceneName) { // Slot rỗng
        this.showToast('Slot này rỗng, không có gì để tải.', 'info');
        return;
    }
    
    this.isProcessing = true;
    try {
        const scene = await this.getFromStore('scenes', sceneName);
        if (!scene) throw new Error(`Scene '${sceneName}' không tồn tại.`);

        console.log(`📦 Đang tải scene: ${scene.sceneName} (Loại: ${scene.type})`);
        
        // Gán id lại cho scene object nếu nó chưa có (để tương thích ngược)
        if (!scene.id) scene.id = sceneId;

        if (scene.type === 'short') {
            await this.loadShortScene(scene, window.map);
        } else {
            const loader = new window.OfflineSceneLoader();
            await loader.load(scene, window.map);
        }
        
        this.setActiveSceneButton(sceneId); // Highlight nút vừa được tải
    } catch (error) {
        console.error(`Lỗi khi tải scene ${sceneName}:`, error);
        this.showToast(`Tải scene thất bại: ${error.message}`, 'error');
    } finally {
        this.isProcessing = false;
    }
  }

  async loadShortScene(scene, map) {
      // Logic khôi phục về chế độ online và set view cho map
      console.log("-> Đang thực thi load short scene...");
      await this.switchToOnlineMode(map);
      map.flyTo({
          center: scene.mapState.center,
          zoom: scene.mapState.zoom,
          pitch: scene.mapState.pitch,
          bearing: scene.mapState.bearing,
          duration: 1500
      });
      this.showToast(`Đã tải scene (online): ${scene.sceneName}`, 'info');
  }
  
  async switchToOnlineMode(map) { /* ... Logic khôi phục source online ... */ }

  // =================================================================
  // CHỨC NĂNG XÓA SCENE (DELETE)
  // =================================================================

  async deleteScene(sceneId) {
    if (this.isProcessing) return;
    const button = document.getElementById(sceneId);
    const sceneName = button.dataset.sceneName;
    if (!sceneName) return;

    if (!confirm(`Bạn có chắc muốn XÓA vĩnh viễn scene '${sceneName}'?`)) return;

    this.isProcessing = true;
    this.updateSceneButtonUI(sceneId, 'deleting');

    try {
        // TODO: Logic dọn dẹp triệt để các tile liên quan
        await this.deleteFromStore('scenes', sceneName);
        this.updateSceneButtonUI(sceneId, 'empty');
        this.showToast(`Đã xóa scene '${sceneName}'.`, 'success');
    } catch (error) {
        console.error(`Lỗi xóa scene ${sceneName}:`, error);
        this.showToast('Xóa scene thất bại!', 'error');
        // Trả lại trạng thái cũ nếu xóa thất bại
        const scene = await this.getFromStore('scenes', sceneName);
        this.updateSceneButtonUI(sceneId, scene.type, scene.sceneName);
    } finally {
        this.isProcessing = false;
    }
  }

  // =================================================================
  // OFFLINE PROTOCOL HANDLER
  // =================================================================

  setupOfflineProtocol() { /* ... Giữ nguyên như phiên bản trước ... */ }
  async handleOfflineProtocol(params) { /* ... Giữ nguyên như phiên bản trước ... */ }

  // =================================================================
  // CÁC HÀM GIAO DIỆN (UI METHODS)
  // =================================================================
  
  updateSceneButtonUI(sceneId, type, name = '') {
      const button = document.getElementById(sceneId);
      if (!button) return;
      button.className = 'scene-btn'; // Reset class
      button.classList.add(`${type}-scene`);
      button.dataset.sceneName = name || '';
      button.disabled = false;
      let content = '';
      switch (type) {
          case 'short': content = `<span>${name}</span><i class="fas fa-bookmark"></i>`; break;
          case 'long': content = `<span>${name}</span><i class="fas fa-hdd"></i>`; break;
          case 'saving': case 'overwriting': case 'deleting':
              content = `<div class="spinner"></div><span>${type}...</span>`;
              button.disabled = true;
              break;
          default: content = `Scene ${sceneId.split('-')[1]}`;
      }
      button.innerHTML = content;
      // Cập nhật nút xóa
      this.updateDeleteButtonFor(button, type === 'short' || type === 'long');
  }

  updateDeleteButtonFor(button, shouldShow) { /* ... Logic thêm/xóa nút 'x' ... */ }
  setActiveSceneButton(activeSceneId) { /* ... Logic highlight nút được chọn ... */ }
  showSaveModal(sceneName) { /* ... */ }
  updateProgressModal(modal, completed, total) { /* ... */ }
  hideSaveModal(modal) { /* ... */ }
  showToast(message, type) { /* ... */ }

  // =================================================================
  // CÁC HÀM HELPER
  // =================================================================

  getCurrentMapState() {
    const map = window.map;
    return {
        center: map.getCenter().toArray(),
        zoom: map.getZoom(),
        pitch: map.getPitch(),
        bearing: map.getBearing(),
        time: window.timelineManager?.getTime() || Date.now(),
    };
  }

  // DB Helpers
  async getFromStore(storeName, key) { /* ... */ }
  async getAllFromStore(storeName) { /* ... */ }
  async putToStore(storeName, data) { /* ... */ }
  async deleteFromStore(storeName, key) { /* ... */ }
}

window.offlineSceneManager = new OfflineSceneManager();