Luồng Hoạt Động Hoàn Chỉnh (<PERSON><PERSON>n <PERSON>ố<PERSON>)
Các Module và Vai Trò Ch<PERSON>h:
OfflineSceneManager (Nhạc trưởng): <PERSON><PERSON><PERSON><PERSON> lý U<PERSON>, ra quyết định ở tầng cao, điều phối các module khác. Không biết gì về tile.
CompleteOfflineDownloader (Công nhân Tải xuống): Tự chủ trong việc tải dữ liệu. <PERSON><PERSON> cấp dịch vụ "Tải toàn bộ" và "Tải bổ sung".
SceneDeleter (Chuyên gia Dọn dẹp): Tự chủ trong việc xóa scene và các tile liên quan.
OfflineSceneLoader (Người Dựng cảnh & Phục vụ): Tự chủ trong việc dựng lại một cảnh offline, ki<PERSON><PERSON> kê, sửa chữa, và phục vụ tile cho cảnh đó.
LayerLoaders (<PERSON><PERSON><PERSON>ên nghiệp): <PERSON><PERSON><PERSON> hàm đơn giản chỉ để thêm source và layer vào bản đồ.

Kịch bản 1: <PERSON><PERSON><PERSON> một "Long Scene"
Người dùng -> Click nút "Lưu Đầy đủ" và chọn một slot trống.
Manager -> Nhận sự kiện, hỏi tên scene, cập nhật UI nút sang trạng thái "Đang lưu".
Manager -> Gọi Downloader và ra lệnh: "Hãy thực hiện 'Long Save' cho scene có tên X". Nó không cung cấp thêm chi tiết nào khác.
Downloader -> Nhận lệnh và bắt đầu hoạt động một cách độc lập:
a. Tự tải và phân tích các file metadata gốc (latest.json...).
b. Tự tính toán danh sách các tile cần thiết.
c. Tự tải từng tile (ưu tiên cache trình duyệt), lưu kết quả vào bộ đệm tạm trong bộ nhớ.
Downloader -> Sau khi 100% tile đã nằm trong bộ đệm tạm, nó thực hiện một lần ghi duy nhất, chuyển toàn bộ dữ liệu (metadata scene và tiles) vào IndexedDB.
Downloader -> Báo cáo lại cho Manager: "Nhiệm vụ hoàn thành. Đây là metadata của scene đã lưu và danh sách tile đã được ghi."
Manager -> Nhận báo cáo, cập nhật UI nút sang trạng thái "Đã lưu (Long)", và cập nhật tile_manifest (metadata tổng).

Kịch bản 2: Tải một "Long Scene" (Trường hợp lý tưởng, đủ tile)
Người dùng -> Click vào một nút scene "Long Save".
Manager -> Nhận sự kiện. Nó đọc metadata của scene từ DB để lấy sceneName.
Manager -> Gọi Loader và ra lệnh: "Hãy dựng lại scene có tên X".
Loader -> Nhận lệnh và bắt đầu hoạt động độc lập:
a. Đăng ký offline:// protocol, tự biến mình thành "server" cho phiên này.
b. Tự tạo danh sách các tile cần thiết dựa trên metadata của scene.
c. Tự kiểm kê trong IndexedDB và thấy rằng tất cả các tile đều có mặt.
d. Dọn dẹp bản đồ (xóa các layer/source cũ).
e. Gọi các LayerLoader tương ứng.
LayerLoaders -> Lần lượt thêm các source vào bản đồ với URL dạng offline://....
Bản đồ -> Yêu cầu tải tile từ các URL offline://....
Loader -> (Vì đã đăng ký protocol) bắt các yêu cầu này, tìm tile trong IndexedDB và trả dữ liệu về cho bản đồ.
Bản đồ -> Nhận dữ liệu tile và hiển thị các layer.
Loader -> Di chuyển camera đến vị trí đã lưu và báo cáo cho Manager: "Dựng cảnh thành công 100%".
Manager -> Nhận báo cáo và không cần làm gì thêm, chỉ cập nhật UI (ví dụ: highlight nút được chọn).

Kịch bản 3: Tải một "Long Scene" (Trường hợp thiếu tile)
Bước 1-4a: Giống hệt Kịch bản 2.
4b. Loader -> Tự kiểm kê và phát hiện một số tile bị thiếu.
4c. Loader -> Quyết định tự sửa chữa. Nó gọi Downloader và ra lệnh: "Hãy thực hiện 'Tải bổ sung' cho danh sách các tile bị thiếu này".
5. Downloader -> Nhận lệnh, thực hiện tải các tile cụ thể và ghi thẳng vào IndexedDB. Nó báo lại cho Loader: "Nhiệm vụ sửa chữa thất bại/thành công".
6. Loader -> Nhận kết quả sửa chữa:
Nếu thành công: Nó tiếp tục từ bước 4d của Kịch bản 2 (dựng cảnh với đầy đủ tile).
Nếu thất bại: Nó vẫn tiếp tục dựng cảnh với các tile đang có, nhưng đồng thời báo cáo lại cho Manager: "Dựng cảnh không hoàn hảo, có dữ liệu bị thiếu và không thể sửa."
7. Manager -> Nhận báo cáo "không hoàn hảo", nó sẽ hiển thị một thông báo cảnh báo cho người dùng, sau đó không làm gì nữa. Quyết định xóa hay lưu lại là của người dùng.

Kịch bản 4: Xóa một "Long Scene"
Người dùng -> Click nút "x" trên một scene.
Manager -> Nhận sự kiện, hỏi xác nhận.
Manager -> Gọi Deleter và ra lệnh: "Hãy xóa scene có tên X".
Deleter -> Nhận lệnh và bắt đầu hoạt động độc lập:
a. Tự đọc metadata của scene cần xóa và của tất cả các scene khác từ IndexedDB.
b. Tự so sánh để tìm ra những tile nào là duy nhất của scene này và cần bị xóa.
c. Tự thực hiện xóa các tile đó và metadata của scene khỏi IndexedDB.
d. Tự tính toán lại tile_manifest mới.
Deleter -> Báo cáo lại cho Manager: "Nhiệm vụ xóa hoàn tất. Đây là manifest mới."
Manager -> Nhận báo cáo, cập nhật tile_manifest trong DB và cập nhật UI nút về trạng thái "rỗng".

Kịch bản 5: Metadata bị lỗi
Người dùng -> Click load một scene.
Manager -> Đọc metadata từ DB và phát hiện file bị hỏng.
Manager -> Ngừng load. Nó đọc tile_manifest và metadata của tất cả các scene hợp lệ khác.
Manager -> Gọi Deleter và ra lệnh: "Hãy dọn dẹp dữ liệu mồ côi. Đây là danh sách toàn bộ tile và danh sách các tile còn hợp lệ."
Deleter -> Nhận lệnh, tự tính toán và xóa các tile mồ côi khỏi IndexedDB. Báo cáo manifest mới.
Manager -> Nhận manifest mới, cập nhật DB, tự xóa metadata bị lỗi và cập nhật UI nút về trạng-thái "rỗng", đồng thời thông báo cho người dùng.
Luồng này đảm bảo mỗi module có trách nhiệm rõ ràng và độc lập

Kịch bản 6: Mất kết nối khi đang tải
Người dùng -> Đang trong quá trình tải scene thì mất mạng.
Downloader -> Phát hiện mất kết nối, dừng tải và lưu lại:
   - Danh sách tile đã tải
   - Trạng thái tiến độ hiện tại
   - Thông báo lỗi "Mất kết nối"
Downloader -> Báo cáo cho Manager: "Tải bị gián đoạn do mất kết nối. Đã lưu lại tiến độ."
Manager -> Cập nhật UI: Hiển thị nút "Tiếp tục khi có mạng" và thông báo lỗi.

Kịch bản 7: Hết dung lượng lưu trữ
Downloader -> Phát hiện lỗi "hết dung lượng" khi lưu vào IndexedDB.
Downloader -> Dừng tải và báo cáo cho Manager: "Lỗi: Không đủ dung lượng lưu trữ."
Manager -> Gọi StorageManager (module mới): "Hãy tính toán dung lượng cần giải phóng và gợi ý xóa scene."
StorageManager -> Phân tích và trả về danh sách các scene có thể xóa, sắp xếp theo độ cũ.
Manager -> Hiển thị giao diện cho người dùng chọn scene để xóa.

Kịch bản 8: Hủy tác vụ đang thực hiện
Người dùng -> Nhấn nút "Hủy" khi đang tải/lưu.
Manager -> Gọi Downloader/Loader: "Hãy dừng công việc hiện tại."
Downloader/Loader -> Dừng tất cả các tác vụ đang chạy, dọn dẹp tài nguyên tạm.
Downloader/Loader -> Báo cáo: "Đã dừng công việc."
Manager -> Khôi phục UI về trạng thái trước khi thực hiện.

Kịch bản 9: Khởi động lại đột ngột
Hệ thống -> Khởi động lại sau khi bị crash.
Manager -> Khởi tạo và gọi RecoveryManager (module mới): "Kiểm tra phiên làm việc trước đó."
RecoveryManager -> Kiểm tra IndexedDB tìm phiên chưa hoàn tất.
   - Nếu có scene đang tải dở: Đánh dấu "cần tiếp tục"
   - Nếu có dữ liệu tạm: Dọn dẹp
RecoveryManager -> Báo cáo cho Manager: "Có phiên chưa hoàn tất. Có muốn tiếp tục?"

Kịch bản 10: Chuyển đổi Online/Offline
Hệ thống -> Phát hiện thay đổi trạng thái mạng.
NetworkManager (module mới) -> Thông báo cho Manager: "Đã chuyển sang chế độ offline/online".
Manager -> Nếu đang tải dở và chuyển sang online:
   - Gọi Downloader: "Tiếp tục tải các tile còn thiếu."
   - Downloader: Tiếp tục từ vị trí đã lưu.

Kịch bản 11: Xung đột phiên bản
Manager -> Khởi động và phát hiện phiên bản DB không tương thích.
Manager -> Gọi MigrationManager (module mới): "Cần nâng cấp DB từ vX lên vY".
MigrationManager -> Thực hiện tuần tự các bước nâng cấp cần thiết.
   - Sao lưu dữ liệu cũ
   - Áp dụng các thay đổi schema
   - Xác nhận tính toàn vẹn dữ liệu
MigrationManager -> Báo cáo kết quả cho Manager.

Kịch bản 12: Tối ưu hiệu suất
PerformanceMonitor (module mới) -> Theo dõi hiệu suất hệ thống.
Khi phát hiện hiệu suất thấp:
   1. Thông báo cho Manager: "Cần giảm chất lượng để cải thiện hiệu suất".
   2. Manager -> Gọi QualityManager: "Điều chỉnh chất lượng phù hợp".
   3. QualityManager -> Điều chỉnh mức độ chi tiết của bản đồ và thông báo lại cho Manager.

Nguyên tắc xử lý lỗi:
1. Mỗi module chỉ xử lý lỗi trong phạm vi trách nhiệm của mình.
2. Khi gặp lỗi ngoài phạm vi, phải báo cáo lên module cấp cao hơn.
3. Không được tự ý thay đổi trạng thái của các module khác.
4. Mọi thay đổi trạng thái phải thông qua các phương thức công khai của module đó.