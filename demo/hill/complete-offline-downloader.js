/**
 * Complete Offline Downloader
 * Vai trò: <PERSON><PERSON><PERSON> "công nhân" chuyên dụng để thực hiện một Long Save.
 * - Nhận kết nối DB từ bên ngoài.
 * - Tải xuống tất cả các tài nguyên cần thiết cho một cảnh offline.
 * - Sử dụng chiến lượ<PERSON> "Cache-First" để tránh tải lại các tile đã có trong cache của trình duyệt.
 */
class CompleteOfflineDownloader {
  constructor() {
    // Thông tin cơ bản để giao tiếp với API
    this.apiKey = window.maptilersdk?.config?.apiKey || 'oinoatcrNmdCL1524DOl';
    this.sessionId = null;

    // Các thuộc tính sẽ được khởi tạo khi chạy
    this.db = null; // Sẽ được cung cấp từ bên ngoài
    this.metadata = {};
    this.downloadStats = {
      total: 0,
      success: 0,
      failed: 0,
      failedUrls: []
    };

    // Tên cho cache của trình duyệt (sử dụng Cache API)
    this.browserCacheName = 'maptiler-tile-cache-v1';
  }

  /**
   * Hàm chính để thực hiện toàn bộ quá trình, được gọi bởi OfflineSceneManager.
   * @param {string} sceneName - Tên của scene.
   * @param {object} map - Đối tượng map MapLibre.
   * @param {IDBDatabase} db - Đối tượng kết nối IndexedDB được truyền vào.
   * @param {function} onProgress - Callback để báo cáo tiến trình (ví dụ: (completed, total) => {}).
   * @returns {Promise<object>} - Kết quả của quá trình tải, bao gồm cờ success và metadata.
   */
  async performLongSave(sceneName, map, db, onProgress = () => {}) {
    console.log(`🚀 ===== BẮT ĐẦU LONG SAVE CHO SCENE: ${sceneName} =====`);

    if (!db) {
      const errorMsg = "Lỗi nghiêm trọng: Không có kết nối IndexedDB được cung cấp.";
      console.error(errorMsg);
      return { success: false, error: errorMsg };
    }
    this.db = db;

    try {
      // Khởi tạo session ID để các yêu cầu API nhất quán
      this.initSessionId(map);

      // Bước 1: Tải tất cả các file metadata cấu hình
      await this.downloadAllMetadataFiles();

      // Bước 2: Khởi tạo scene metadata và tính toán danh sách các tile cần tải
      const { sceneMetadata, tiles, windDatasets } = this.initSceneMetadata(sceneName, map);

      // Reset thống kê tải và tính tổng số tile cần xử lý
      this.downloadStats = { total: 0, success: 0, failed: 0, failedUrls: [] };
      const totalTilesToProcess =
          tiles.length * 4 + // (temperature, terrain, basic, border)
          (tiles.length * windDatasets.length);

      // Bước 3-6: Tải tất cả các loại tiles
      let processedCount = 0;
      const reportProgress = () => {
        processedCount++;
        onProgress(processedCount, totalTilesToProcess);
      };

      await this.downloadTemperatureTiles(tiles, reportProgress);
      await this.downloadWindTiles(tiles, windDatasets, reportProgress);
      await this.downloadTerrainTiles(tiles, reportProgress);
      await this.downloadBasicAndBorderTiles(tiles, reportProgress);

      // Bước 7: Lưu scene metadata cuối cùng vào IndexedDB
      await this.saveSceneMetadata(sceneMetadata);

      // Báo cáo kết quả cuối cùng
      console.log(`\n📊 ===== KẾT QUẢ TẢI XUỐNG =====`);
      console.log(`✅ Thành công: ${this.downloadStats.success} / ${this.downloadStats.total}`);
      console.log(`❌ Thất bại: ${this.downloadStats.failed} / ${this.downloadStats.total}`);

      if (this.downloadStats.failed > 0) {
        console.warn(`🚨 CẢNH BÁO: Có ${this.downloadStats.failed} tiles tải thất bại.`);
        console.warn('🔗 Các URL thất bại:', this.downloadStats.failedUrls);
      }

      console.log(`🎉 HOÀN THÀNH - Scene '${sceneName}' đã được lưu vào IndexedDB.`);
      return { success: true, stats: this.downloadStats, metadata: sceneMetadata };

    } catch (error) {
      const errorMsg = `LỖI NGHIÊM TRỌNG: ${error.message}`;
      console.error(errorMsg, error);
      return { success: false, error: errorMsg };
    }
  }

  // =================================================================
  // CÁC HÀM NỘI BỘ (INTERNAL METHODS)
  // =================================================================

  /** Khởi tạo hoặc lấy session ID từ map */
  initSessionId(map) {
    this.sessionId = map.getMaptilerSessionId?.() || this.generateSessionId();
    console.log(`🔗 Session ID được sử dụng: ${this.sessionId}`);
  }

  /** Tạo session ID ngẫu nhiên nếu map không cung cấp */
  generateSessionId() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
      const r = (Math.random() * 16) | 0;
      const v = c === 'x' ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  }

  /** Tải các file JSON metadata cần thiết từ MapTiler */
  async downloadAllMetadataFiles() {
    console.log('\n📋 ===== 1. Tải các file JSON metadata =====');
    const metadataUrls = [
      { name: 'latest.json', url: `https://api.maptiler.com/weather/latest.json?key=${this.apiKey}&mtsid=${this.sessionId}`},
      { name: 'terrain-rgb_tiles.json', url: `https://api.maptiler.com/tiles/terrain-rgb/tiles.json?key=${this.apiKey}&mtsid=${this.sessionId}`},
      { name: 'terrain-rgb-v2_tiles.json', url: `https://api.maptiler.com/tiles/terrain-rgb-v2/tiles.json?key=${this.apiKey}&mtsid=${this.sessionId}`},
      { name: 'v3_tiles.json', url: `https://api.maptiler.com/tiles/v3/tiles.json?key=${this.apiKey}&mtsid=${this.sessionId}`},
      { name: 'countries_tiles.json', url: `https://api.maptiler.com/tiles/countries/tiles.json?key=${this.apiKey}&mtsid=${this.sessionId}`},
    ];

    for (const fileInfo of metadataUrls) {
      try {
        const response = await fetch(fileInfo.url);
        if (!response.ok) throw new Error(`HTTP ${response.status}`);
        const data = await response.json();
        this.metadata[fileInfo.name] = data;
        await this.putToStore('metadata', { name: fileInfo.name, data, timestamp: new Date().toISOString() });
        console.log(`✅ ${fileInfo.name}: OK`);
      } catch (error) {
        console.error(`❌ ${fileInfo.name}: FAILED - ${error.message}`);
        throw new Error(`Không thể tải metadata quan trọng: ${fileInfo.name}. Quá trình bị dừng.`);
      }
    }
  }

  /** Tạo metadata cho scene và tính toán các tile cần tải */
  initSceneMetadata(sceneName, map) {
    console.log(`\n📝 ===== 2. Khởi tạo metadata cho Scene: ${sceneName} =====`);
    const bounds = map.getBounds();
    const zoom = Math.floor(map.getZoom());
    const center = map.getCenter();
    const tiles = this.calculateTileGrid(bounds, zoom);
    const windDatasets = this.extractWindDatasets(this.metadata['latest.json']);

    const sceneMetadata = {
      sceneName, // Đây là keyPath của store 'scenes'
      type: 'long',
      timestamp: new Date().toISOString(),
      mapState: {
        center: [center.lng, center.lat],
        zoom,
        pitch: map.getPitch(),
        bearing: map.getBearing(),
        bounds: [bounds.getWest(), bounds.getSouth(), bounds.getEast(), bounds.getNorth()],
        time: this.getCurrentTimestamp(),
      },
      datasets: {
        temperature: ['terrain-rgb'],
        wind: windDatasets.map(d => d.id),
        terrain: ['terrain-rgb-v2'],
        basic: ['v3'],
        border: ['countries'],
      },
      // Thông tin này có thể hữu ích cho việc dọn dẹp sau này
      tileInfo: {
        zoom,
        tiles,
      },
      downloadedMetadata: this.metadata,
    };
    console.log(`+ Scene sẽ được lưu với ${tiles.length} tiles mỗi lớp dữ liệu tại zoom ${zoom}.`);
    return { sceneMetadata, tiles, windDatasets };
  }

  /**
   * Tải một tile đơn lẻ, với logic "Cache-First".
   * Đây là hàm trung tâm cho tất cả các hoạt động tải tile.
   */
  async downloadSingleTile(url, layerType, datasetId, tile, onProgress) {
    this.downloadStats.total++;
    let data = null;

    try {
      const cachedResponse = await caches.match(url);
      if (cachedResponse && cachedResponse.ok) {
        console.log(`♻️ [${layerType}] Cache HIT: ${tile.z}/${tile.x}/${tile.y}`);
        data = await cachedResponse.arrayBuffer();
      } else {
        console.log(`🌐 [${layerType}] Cache MISS, Tải mới: ${tile.z}/${tile.x}/${tile.y}`);
        const response = await fetch(url);
        if (!response.ok) throw new Error(`HTTP ${response.status}`);
        
        const responseClone = response.clone();
        data = await response.arrayBuffer();

        // Chủ động lưu vào cache của trình duyệt để tái sử dụng
        const cache = await caches.open(this.browserCacheName);
        await cache.put(url, responseClone);
      }

      if (data) {
        this.downloadStats.success++;
        await this.saveTileToDB(data, url, layerType, datasetId, tile);
      }
    } catch (error) {
      console.error(`❌ [${layerType}] FAILED - ${url} - ${error.message}`);
      this.downloadStats.failed++;
      this.downloadStats.failedUrls.push(url);
    } finally {
      // Luôn báo cáo tiến trình, dù thành công hay thất bại, để UI không bị treo
      onProgress();
    }
  }

  // Các hàm chuyên biệt để tải từng loại tile, chúng chỉ tạo URL và gọi `downloadSingleTile`
  async downloadTemperatureTiles(tiles, onProgress) {
    console.log(`\n🌡️ ===== 3. Tải Tiles cho Temperature (terrain-rgb) =====`);
    for (const tile of tiles) {
      const url = `https://api.maptiler.com/tiles/terrain-rgb/${tile.z}/${tile.x}/${tile.y}.png?key=${this.apiKey}&mtsid=${this.sessionId}`;
      await this.downloadSingleTile(url, 'temperature', 'terrain-rgb', tile, onProgress);
    }
  }

  async downloadWindTiles(tiles, windDatasets, onProgress) {
    console.log(`\n🌪️ ===== 4. Tải Tiles cho Wind =====`);
    for (const dataset of windDatasets) {
      console.log(`   - Đang xử lý wind dataset: ${dataset.id}`);
      for (const tile of tiles) {
        const url = `https://api.maptiler.com/tiles/${dataset.id}/${tile.z}/${tile.x}/${tile.y}.png?key=${this.apiKey}&mtsid=${this.sessionId}`;
        await this.downloadSingleTile(url, 'wind', dataset.id, tile, onProgress);
      }
    }
  }

  async downloadTerrainTiles(tiles, onProgress) {
    console.log(`\n⛰️ ===== 5. Tải Tiles cho Terrain (terrain-rgb-v2) =====`);
    for (const tile of tiles) {
      const url = `https://api.maptiler.com/tiles/terrain-rgb-v2/${tile.z}/${tile.x}/${tile.y}.webp?key=${this.apiKey}&mtsid=${this.sessionId}`;
      await this.downloadSingleTile(url, 'terrain', 'terrain-rgb-v2', tile, onProgress);
    }
  }

  async downloadBasicAndBorderTiles(tiles, onProgress) {
    console.log(`\n🗺️ ===== 6. Tải Tiles cho Basic & Border =====`);
    for (const tile of tiles) {
      const basicUrl = `https://api.maptiler.com/tiles/v3/${tile.z}/${tile.x}/${tile.y}.pbf?key=${this.apiKey}&mtsid=${this.sessionId}`;
      await this.downloadSingleTile(basicUrl, 'basic', 'v3', tile, onProgress);
      
      const borderUrl = `https://api.maptiler.com/tiles/countries/${tile.z}/${tile.x}/${tile.y}.pbf?key=${this.apiKey}&mtsid=${this.sessionId}`;
      await this.downloadSingleTile(borderUrl, 'border', 'countries', tile, onProgress);
    }
  }

  // =================================================================
  // CÁC HÀM HELPER VÀ TÍNH TOÁN (Định nghĩa đầy đủ)
  // =================================================================
  
  calculateTileGrid(bounds, zoom) {
    const tiles = [];
    const nw = this.deg2tile(bounds.getNorth(), bounds.getWest(), zoom);
    const se = this.deg2tile(bounds.getSouth(), bounds.getEast(), zoom);
    for (let x = nw.x; x <= se.x; x++) {
      for (let y = nw.y; y <= se.y; y++) {
        tiles.push({ x, y, z: zoom });
      }
    }
    return tiles;
  }

  deg2tile(lat_deg, lon_deg, zoom) {
    const lat_rad = lat_deg * Math.PI / 180;
    const n = Math.pow(2, zoom);
    const x = Math.floor((lon_deg + 180) / 360 * n);
    const y = Math.floor((1 - Math.asinh(Math.tan(lat_rad)) / Math.PI) / 2 * n);
    return { x, y };
  }
  
  extractWindDatasets(latest) {
    const datasets = [];
    if (!latest || !latest.variables) return datasets;

    const currentTimestamp = this.getCurrentTimestamp();
    for (const variable of latest.variables) {
        if (variable.metadata?.weather_variable?.name.toLowerCase().includes('wind')) {
            if (variable.keyframes && variable.keyframes.length > 0) {
                const keyframe = this.getCurrentKeyframe(variable.keyframes, currentTimestamp);
                if (keyframe) {
                    datasets.push({ id: keyframe.id, title: variable.metadata.title });
                }
            }
        }
    }
    return datasets;
  }

  getCurrentTimestamp() {
    if (typeof maptilerweather !== 'undefined' && maptilerweather.WeatherDataHandler?.getCurrentTimestamp) {
      return maptilerweather.WeatherDataHandler.getCurrentTimestamp();
    }
    return new Date().toISOString();
  }

  getCurrentKeyframe(keyframes, targetTimestamp) {
    if (!keyframes || keyframes.length === 0) return null;
    if (!targetTimestamp) return keyframes[0];
    const targetTime = new Date(targetTimestamp).getTime();
    return keyframes.reduce((prev, curr) => {
      const prevDiff = Math.abs(new Date(prev.timestamp).getTime() - targetTime);
      const currDiff = Math.abs(new Date(curr.timestamp).getTime() - targetTime);
      return currDiff < prevDiff ? curr : prev;
    });
  }

  // =================================================================
  // CÁC HÀM TƯƠNG TÁC VỚI INDEXEDDB
  // =================================================================

  /** Hàm helper để ghi dữ liệu vào một store cụ thể trong DB */
  async putToStore(storeName, record) {
    const transaction = this.db.transaction([storeName], 'readwrite');
    const store = transaction.objectStore(storeName);
    return new Promise((resolve, reject) => {
      const request = store.put(record);
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  /** Lưu tile vào DB dựa trên loại của nó */
  async saveTileToDB(data, url, layerType, datasetId, tile) {
    let record, storeName;
    const common = { ...tile, data, url, timestamp: new Date().toISOString() };
    switch (layerType) {
      case 'wind':
        storeName = 'wind_tiles';
        record = { id: `${datasetId}_${tile.z}_${tile.x}_${tile.y}`, datasetId, ...common };
        break;
      case 'temperature':
        storeName = 'temperature_tiles';
        record = { id: `terrain-rgb_${tile.z}_${tile.x}_${tile.y}`, ...common };
        break;
      case 'terrain':
        storeName = 'terrain_tiles';
        record = { id: `terrain-rgb-v2_${tile.z}_${tile.x}_${tile.y}`, ...common };
        break;
      case 'basic':
        storeName = 'vector_tiles';
        record = { id: `v3_${tile.z}_${tile.x}_${tile.y}`, tileType: 'v3', ...common };
        break;
      case 'border':
        storeName = 'vector_tiles';
        record = { id: `countries_${tile.z}_${tile.x}_${tile.y}`, tileType: 'countries', ...common };
        break;
      default:
        console.warn(`⚠️ Kiểu layer không xác định, không thể lưu tile: ${layerType}`);
        return;
    }
    await this.putToStore(storeName, record);
  }

  /** Lưu scene metadata vào 'scenes' store */
  async saveSceneMetadata(sceneMetadata) {
    await this.putToStore('scenes', sceneMetadata);
  }
}

// Gán vào window để OfflineSceneManager có thể gọi
window.CompleteOfflineDownloader = CompleteOfflineDownloader;