/**
 * Offline Wind Decoder - Decode cached wind tiles into particle animation
 * Hoạt động hoàn toàn offline mà không cần MapTiler API
 */
class OfflineWindDecoder {
  constructor(sceneId = null) {
    this.db = null;
    this.sceneId = sceneId;
    this.availableDatasets = new Set(); // Dynamic datasets từ IndexedDB
    this.cachedWindTiles = new Map();
    this.currentTimestamp = null;
    
    // Wind decode parameters (từ ParticleLayer.ts)
    this.decodeChannels = 'rg'; // Red=U, Green=V
    this.decodeMin = -40;       // m/s
    this.decodeMax = 40;        // m/s
    
    this.init();
  }

  async init() {
    try {
      await this.initIndexedDB();
      await this.loadAvailableDatasets();
      console.log(`✅ Offline Wind Decoder ready - Found ${this.availableDatasets.size} datasets`);
    } catch (error) {
      console.error('❌ Wind Decoder init failed:', error);
    }
  }

  /**
   * Load all available wind datasets từ IndexedDB
   */
  async loadAvailableDatasets(allowOnline = true, sceneTimestamp = null) {
    try {
      const allTiles = await window.offlineSceneManager.getAllFromStore(
        this.db.transaction(['wind_tiles'], 'readonly').objectStore('wind_tiles')
      );
      if (!allTiles || allTiles.length === 0) {
        // Nếu cho phép online và còn trong hạn 3h thì thử gọi API
        if (allowOnline && sceneTimestamp && (Date.now() - sceneTimestamp < 3 * 60 * 60 * 1000)) {
          // TODO: Thực hiện fetch API ở đây nếu cần
          // Nếu API trả về không có thì mới hiện thông báo
          if (window.offlineSceneManager) {
            window.offlineSceneManager.showNoDataToast('Không có dữ liệu bổ sung cho thời điểm này');
          } else {
            alert('Không có dữ liệu bổ sung cho thời điểm này');
          }
        } else {
          if (window.offlineSceneManager) {
            window.offlineSceneManager.showNoDataToast('Không có dữ liệu bổ sung cho thời điểm này');
          } else {
            alert('Không có dữ liệu bổ sung cho thời điểm này');
          }
        }
        this.availableDatasets = [];
        return [];
      }
      const datasetIds = [...new Set(allTiles.map(t => t.datasetId))];
      this.availableDatasets = datasetIds;
      return datasetIds;
    } catch (err) {
      console.error('❌ Failed to load available datasets:', err);
      this.availableDatasets = [];
      if (window.offlineSceneManager) {
        window.offlineSceneManager.showNoDataToast('Không có dữ liệu bổ sung cho thời điểm này');
      } else {
        alert('Không có dữ liệu bổ sung cho thời điểm này');
      }
      return [];
    }
  }

  async initIndexedDB() {
    // *** FIX: Use shared database instance instead of creating new one ***
    if (window.offlineSceneManager && window.offlineSceneManager.db) {
      console.log('🔗 Using shared database from OfflineSceneManager');
      this.db = window.offlineSceneManager.db;
      return;
    }
    
    // Fallback: create own database with same schema
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('WeatherAppOffline', 2);
      
      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };
      
      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        
        // Match OfflineSceneManager schema exactly
        if (!db.objectStoreNames.contains('scenes')) {
          const scenesStore = db.createObjectStore('scenes', { keyPath: 'id' });
          scenesStore.createIndex('type', 'type', { unique: false });
        }
        
        if (!db.objectStoreNames.contains('global_tile_pool')) {
          const tilesStore = db.createObjectStore('global_tile_pool', { keyPath: 'url' });
          tilesStore.createIndex('refCount', 'refCount', { unique: false });
        }

        if (!db.objectStoreNames.contains('wind_tiles')) {
          const windTilesStore = db.createObjectStore('wind_tiles', { keyPath: 'url' });
          windTilesStore.createIndex('sceneId', 'sceneId', { unique: false });
          windTilesStore.createIndex('datasetId', 'datasetId', { unique: false });
        }
      };
    });
  }

  /**
   * Download wind tiles cho current viewport (DEPRECATED - use offline-scene-manager.js)
   * Kept for backward compatibility
   */
  async downloadWindTiles(map, sceneId) {
    console.warn('⚠️ downloadWindTiles is deprecated - use offline-scene-manager.js downloadWindTilesWithDatasets instead');
    return false;
  }

  calculateTileGrid(bounds, zoom) {
    const ne = bounds.getNorthEast();
    const sw = bounds.getSouthWest();
    const scale = Math.pow(2, zoom);
    
    const minX = Math.floor((sw.lng + 180) / 360 * scale);
    const maxX = Math.floor((ne.lng + 180) / 360 * scale);
    const minY = Math.floor((1 - Math.log(Math.tan(ne.lat * Math.PI / 180) + 1 / Math.cos(ne.lat * Math.PI / 180)) / Math.PI) / 2 * scale);
    const maxY = Math.floor((1 - Math.log(Math.tan(sw.lat * Math.PI / 180) + 1 / Math.cos(sw.lat * Math.PI / 180)) / Math.PI) / 2 * scale);

    const tiles = [];
    for (let x = minX; x <= maxX; x++) {
      for (let y = minY; y <= maxY; y++) {
        tiles.push({ z: zoom, x: x, y: y });
      }
    }
    
    return tiles;
  }

  buildWindTileUrl(tile, datasetId, apiKey, sessionId) {
    return `https://api.maptiler.com/tiles/${datasetId}/${tile.z}/${tile.x}/${tile.y}.png?key=${apiKey}&mtsid=${sessionId}`;
  }

  async fetchWindTile(url) {
    try {
      const response = await fetch(url);
      if (response.ok) {
        return await response.arrayBuffer();
      }
      return null;
    } catch (error) {
      console.error(`Failed to fetch wind tile: ${url}`, error);
      return null;
    }
  }

  /**
   * Decode wind U/V từ cached tiles
   */
  async decodeWindAt(lng, lat, zoom) {
    if (!this.db || this.availableDatasets.size === 0) return null;
    
    try {
      // Calculate tile coordinates
      const scale = Math.pow(2, zoom);
      const x = Math.floor((lng + 180) / 360 * scale);
      const y = Math.floor((1 - Math.log(Math.tan(lat * Math.PI / 180) + 1 / Math.cos(lat * Math.PI / 180)) / Math.PI) / 2 * scale);
      
      // Try each available dataset để tìm cached tile
      const transaction = this.db.transaction(['wind_tiles'], 'readonly');
      const store = transaction.objectStore('wind_tiles');
      
      let cachedTile = null;
      for (const datasetId of this.availableDatasets) {
        const url = this.buildWindTileUrl({ z: zoom, x: x, y: y }, datasetId, 'cached', 'cached');
        cachedTile = await this.getFromStore(store, url);
        if (cachedTile) {
          console.log(`🎯 Found wind tile in dataset: ${datasetId}`);
          break;
        }
      }
      
      if (!cachedTile) return null;
      
      // Decode pixel data
      const imageData = await this.arrayBufferToImageData(cachedTile.data);
      const pixelU = this.getPixelValue(imageData, lng, lat, x, y, zoom, 'r'); // Red channel = U
      const pixelV = this.getPixelValue(imageData, lng, lat, x, y, zoom, 'g'); // Green channel = V
      
      // Convert pixel values to wind speed
      const windU = this.decodeValue(pixelU);
      const windV = this.decodeValue(pixelV);
      
      // Calculate wind speed and direction
      const windSpeed = Math.sqrt(windU * windU + windV * windV);
      const windDirection = Math.atan2(windU, windV) * 180 / Math.PI;
      
      return {
        speedMetersPerSecond: windSpeed,
        speedKilometersPerHour: windSpeed * 3.6,
        speedMilesPerHour: windSpeed * 2.23694,
        speedFeetPerSecond: windSpeed * 3.28084,
        speedKnots: windSpeed * 1.94384,
        directionAngle: windDirection,
        compassDirection: this.getCardinalDirection(windDirection + 180),
        u: windU,
        v: windV
      };
      
    } catch (error) {
      console.error('Wind decode failed:', error);
      return null;
    }
  }

  async arrayBufferToImageData(arrayBuffer) {
    return new Promise((resolve, reject) => {
      const blob = new Blob([arrayBuffer], { type: 'image/png' });
      const img = new Image();
      
      img.onload = () => {
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext('2d');
        ctx.drawImage(img, 0, 0);
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        resolve(imageData);
      };
      
      img.onerror = reject;
      img.src = URL.createObjectURL(blob);
    });
  }

  getPixelValue(imageData, lng, lat, tileX, tileY, zoom, channel) {
    // Calculate pixel position within tile
    const scale = Math.pow(2, zoom);
    const tileSize = 256; // Standard tile size
    
    // Convert lng/lat to pixel coordinates within tile
    const pixelX = Math.floor(((lng + 180) / 360 * scale - tileX) * tileSize);
    const pixelY = Math.floor(((1 - Math.log(Math.tan(lat * Math.PI / 180) + 1 / Math.cos(lat * Math.PI / 180)) / Math.PI) / 2 * scale - tileY) * tileSize);
    
    // Clamp to tile bounds
    const x = Math.max(0, Math.min(tileSize - 1, pixelX));
    const y = Math.max(0, Math.min(tileSize - 1, pixelY));
    
    // Get pixel data
    const index = (y * imageData.width + x) * 4;
    const channelIndex = channel === 'r' ? 0 : channel === 'g' ? 1 : channel === 'b' ? 2 : 3;
    
    return imageData.data[index + channelIndex];
  }

  decodeValue(pixelValue) {
    // Convert 0-255 pixel value to wind speed range
    return this.decodeMin + (pixelValue / 255) * (this.decodeMax - this.decodeMin);
  }

  getCardinalDirection(angle) {
    const directions = ['N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE', 'S', 'SSW', 'SW', 'WSW', 'W', 'WNW', 'NW', 'NNW'];
    const normalizedAngle = ((angle % 360) + 360) % 360;
    const index = Math.round(normalizedAngle / 22.5) % 16;
    return directions[index];
  }

  /**
   * Create offline wind source cho map layer
   */
  createOfflineWindSource(sceneId) {
    return {
      type: 'raster',
      tiles: [`offline://wind/${sceneId}/{z}/{x}/{y}`],
      tileSize: 256,
      attribution: 'Cached Wind Data'
    };
  }

  /**
   * Utils
   */
  async getFromStore(store, key) {
    return new Promise((resolve, reject) => {
      const request = store.get(key);
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  async putInStore(store, data) {
    return new Promise((resolve, reject) => {
      const request = store.put(data);
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Get cached wind tiles cho một scene
   */
  async getCachedWindTiles(sceneId, allowOnline = true, sceneTimestamp = null) {
    if (!this.db) return [];
    try {
      const transaction = this.db.transaction(['wind_tiles'], 'readonly');
      const store = transaction.objectStore('wind_tiles');
      return new Promise((resolve, reject) => {
        if (typeof store.getAll === 'function') {
          const request = store.getAll();
          request.onsuccess = () => {
            const tiles = request.result.filter(tile => tile.sceneId === sceneId);
            if (!tiles || tiles.length === 0) {
              // Nếu cho phép online và còn trong hạn 3h thì thử gọi API
              if (allowOnline && sceneTimestamp && (Date.now() - sceneTimestamp < 3 * 60 * 60 * 1000)) {
                // TODO: Thực hiện fetch API ở đây nếu cần
                if (window.offlineSceneManager) {
                  window.offlineSceneManager.showNoDataToast('Không có dữ liệu bổ sung cho thời điểm này');
                } else {
                  alert('Không có dữ liệu bổ sung cho thời điểm này');
                }
              } else {
                if (window.offlineSceneManager) {
                  window.offlineSceneManager.showNoDataToast('Không có dữ liệu bổ sung cho thời điểm này');
                } else {
                  alert('Không có dữ liệu bổ sung cho thời điểm này');
                }
              }
            }
            resolve(tiles);
          };
          request.onerror = () => reject(request.error);
        } else {
          // Fallback: dùng cursor
          const result = [];
          const request = store.openCursor();
          request.onsuccess = function(event) {
            const cursor = event.target.result;
            if (cursor) {
              if (cursor.value.sceneId === sceneId) {
                result.push(cursor.value);
              }
              cursor.continue();
            } else {
              if (result.length === 0) {
                if (allowOnline && sceneTimestamp && (Date.now() - sceneTimestamp < 3 * 60 * 60 * 1000)) {
                  // TODO: Thực hiện fetch API ở đây nếu cần
                  if (window.offlineSceneManager) {
                    window.offlineSceneManager.showNoDataToast('Không có dữ liệu bổ sung cho thời điểm này');
                  } else {
                    alert('Không có dữ liệu bổ sung cho thời điểm này');
                  }
                } else {
                  if (window.offlineSceneManager) {
                    window.offlineSceneManager.showNoDataToast('Không có dữ liệu bổ sung cho thời điểm này');
                  } else {
                    alert('Không có dữ liệu bổ sung cho thời điểm này');
                  }
                }
              }
              resolve(result);
            }
          };
          request.onerror = () => reject(request.error);
        }
      });
    } catch (error) {
      console.error('Failed to get cached wind tiles:', error);
      if (window.offlineSceneManager) {
        window.offlineSceneManager.showNoDataToast('Không có dữ liệu bổ sung cho thời điểm này');
      } else {
        alert('Không có dữ liệu bổ sung cho thời điểm này');
      }
      return [];
    }
  }

  /**
   * Clear cached wind tiles
   */
  async clearWindCache() {
    if (!this.db) return;
    
    try {
      const transaction = this.db.transaction(['wind_tiles'], 'readwrite');
      const store = transaction.objectStore('wind_tiles');
      await store.clear();
      console.log('🗑️ Wind cache cleared');
    } catch (error) {
      console.error('Failed to clear wind cache:', error);
    }
  }

  /**
   * Refresh available datasets từ IndexedDB
   */
  async refreshDatasets() {
    await this.loadAvailableDatasets();
    console.log(`🔄 Refreshed - Found ${this.availableDatasets.size} datasets`);
  }

  /**
   * Get list of available dataset IDs
   */
  getAvailableDatasets() {
    return Array.from(this.availableDatasets);
  }

  /**
   * Check if specific dataset is available
   */
  hasDataset(datasetId) {
    return this.availableDatasets.has(datasetId);
  }

  /**
   * Get wind data statistics
   */
  async getWindStats(allowOnline = true, sceneTimestamp = null) {
    if (!this.db) return null;
    try {
      const transaction = this.db.transaction(['wind_tiles'], 'readonly');
      const store = transaction.objectStore('wind_tiles');
      return new Promise((resolve, reject) => {
        if (typeof store.getAll === 'function') {
          const request = store.getAll();
          request.onsuccess = () => {
            const tiles = request.result;
            if (!tiles || tiles.length === 0) {
              if (allowOnline && sceneTimestamp && (Date.now() - sceneTimestamp < 3 * 60 * 60 * 1000)) {
                // TODO: Thực hiện fetch API ở đây nếu cần
                if (window.offlineSceneManager) {
                  window.offlineSceneManager.showNoDataToast('Không có dữ liệu bổ sung cho thời điểm này');
                } else {
                  alert('Không có dữ liệu bổ sung cho thời điểm này');
                }
              } else {
                if (window.offlineSceneManager) {
                  window.offlineSceneManager.showNoDataToast('Không có dữ liệu bổ sung cho thời điểm này');
                } else {
                  alert('Không có dữ liệu bổ sung cho thời điểm này');
                }
              }
            }
            const datasetStats = {};
            tiles.forEach(tile => {
              if (!datasetStats[tile.datasetId]) {
                datasetStats[tile.datasetId] = {
                  datasetId: tile.datasetId,
                  tileCount: 0,
                  scenes: new Set(),
                  minZoom: Infinity,
                  maxZoom: -Infinity,
                  lastUpdated: 0
                };
              }
              const stats = datasetStats[tile.datasetId];
              stats.tileCount++;
              if (tile.sceneId) stats.scenes.add(tile.sceneId);
              stats.minZoom = Math.min(stats.minZoom, tile.zoom || 0);
              stats.maxZoom = Math.max(stats.maxZoom, tile.zoom || 0);
              stats.lastUpdated = Math.max(stats.lastUpdated, tile.timestamp || 0);
            });
            Object.values(datasetStats).forEach(stats => {
              stats.scenes = Array.from(stats.scenes);
            });
            resolve({
              totalTiles: tiles.length,
              datasets: Object.values(datasetStats),
              availableDatasets: Array.from(this.availableDatasets)
            });
          };
          request.onerror = () => reject(request.error);
        } else {
          // Fallback: dùng cursor
          const tiles = [];
          const request = store.openCursor();
          request.onsuccess = function(event) {
            const cursor = event.target.result;
            if (cursor) {
              tiles.push(cursor.value);
              cursor.continue();
            } else {
              if (tiles.length === 0) {
                if (allowOnline && sceneTimestamp && (Date.now() - sceneTimestamp < 3 * 60 * 60 * 1000)) {
                  // TODO: Thực hiện fetch API ở đây nếu cần
                  if (window.offlineSceneManager) {
                    window.offlineSceneManager.showNoDataToast('Không có dữ liệu bổ sung cho thời điểm này');
                  } else {
                    alert('Không có dữ liệu bổ sung cho thời điểm này');
                  }
                } else {
                  if (window.offlineSceneManager) {
                    window.offlineSceneManager.showNoDataToast('Không có dữ liệu bổ sung cho thời điểm này');
                  } else {
                    alert('Không có dữ liệu bổ sung cho thời điểm này');
                  }
                }
              }
              const datasetStats = {};
              tiles.forEach(tile => {
                if (!datasetStats[tile.datasetId]) {
                  datasetStats[tile.datasetId] = {
                    datasetId: tile.datasetId,
                    tileCount: 0,
                    scenes: new Set(),
                    minZoom: Infinity,
                    maxZoom: -Infinity,
                    lastUpdated: 0
                  };
                }
                const stats = datasetStats[tile.datasetId];
                stats.tileCount++;
                if (tile.sceneId) stats.scenes.add(tile.sceneId);
                stats.minZoom = Math.min(stats.minZoom, tile.zoom || 0);
                stats.maxZoom = Math.max(stats.maxZoom, tile.zoom || 0);
                stats.lastUpdated = Math.max(stats.lastUpdated, tile.timestamp || 0);
              });
              Object.values(datasetStats).forEach(stats => {
                stats.scenes = Array.from(stats.scenes);
              });
              resolve({
                totalTiles: tiles.length,
                datasets: Object.values(datasetStats),
                availableDatasets: Array.from(this.availableDatasets)
              });
            }
          };
          request.onerror = () => reject(request.error);
        }
      });
    } catch (error) {
      console.error('Failed to get wind stats:', error);
      if (window.offlineSceneManager) {
        window.offlineSceneManager.showNoDataToast('Không có dữ liệu bổ sung cho thời điểm này');
      } else {
        alert('Không có dữ liệu bổ sung cho thời điểm này');
      }
      return null;
    }
  }
}

// Export for global use
window.OfflineWindDecoder = OfflineWindDecoder; 