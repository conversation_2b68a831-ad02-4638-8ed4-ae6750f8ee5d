<!DOCTYPE html>
<html>
<head>
  <title>MapTiler Wind + Temperature (Fixed Boundaries)</title>
  <meta charset="utf-8" />
  <meta name="viewport" content="initial-scale=1,maximum-scale=1,user-scalable=no" />
  <script src="https://cdn.maptiler.com/maptiler-sdk-js/v3.2.0/maptiler-sdk.umd.min.js"></script>
  <link href="https://cdn.maptiler.com/maptiler-sdk-js/v3.2.0/maptiler-sdk.css" rel="stylesheet" />
  <script src="https://cdn.maptiler.com/maptiler-weather/v3.0.1/maptiler-weather.umd.min.js"></script>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: sans-serif;
    }

    #map {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 100%;
      background-color: #444952;
      background-image: url('night-stars.webp');
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
    }

    #time-info {
      position: fixed;
      width: 60vw;
      bottom: 0;
      z-index: 1;
      margin: 10px;
      text-shadow: 0px 0px 5px black;
      color: white;
      font-size: 20px;
      font-weight: 500;
      text-align: center;
      left: 0;
      right: 0;
      margin: auto;
      padding: 20px;
    }

    #time-slider {
      width: 100%;
      height: fit-content;
      left: 0;
      right: 0;
      z-index: 1;
      filter: drop-shadow(0 0 7px #000a);
      margin-top: 10px;
    }

    #pointer-data {
      z-index: 1;
      position: fixed;
      font-size: 50px;
      font-weight: 900;
      margin: 27px 0px 0px 10px;
      color: #fff;
      text-shadow: 0px 0px 10px #0007;
      white-space: pre;
    }

    #variable-name {
      z-index: 1;
      position: fixed;
      font-size: 20px;
      font-weight: 500;
      margin: 5px 0px 0px 10px;
      color: #fff;
      text-shadow: 0px 0px 10px #0007;
    }

    .button {
      cursor: pointer;
      width: auto;
      padding: 8px;
      border-radius: 3px;
      margin: 10px 0 0 0;
      font-size: 12px;
      text-align: center;
      color: #fff;
      background: #3174ff;
      font-family: sans-serif;
      font-weight: bold;
    }

    #color-scale-container {
      position: fixed;
      right: 30px;
      top: 50%;
      transform: translateY(-50%);
      z-index: 10;
      display: flex;
      flex-direction: column;
      align-items: center;
      background: rgba(0,0,0,0.15);
      border-radius: 10px;
      padding: 12px 8px;
      box-shadow: 0 2px 8px #0003;
    }
    #color-scale-canvas {
      display: block;
      width: 32px;
      height: 256px;
      border-radius: 8px;
      margin-bottom: 8px;
      margin-top: 8px;
    }
    .scale-label {
      color: #fff;
      font-size: 15px;
      font-weight: bold;
      text-shadow: 0 1px 4px #000a;
      margin: 0 0 2px 0;
      user-select: none;
    }
    .scale-label.min {
      margin-bottom: 4px;
    }
    .scale-label.max {
      margin-top: 4px;
    }
  </style>
</head>

<body>
  <div id="time-info">
    <span id="time-text"></span>
    <button id="play-pause-bt" class="button">Play 3600x</button>
    <button id="toggle-temp-bt" class="button">Ẩn nhiệt độ</button>
    <button id="auto-scale-bt" class="button">Cập nhật Auto-scale</button>
    <button id="hillshade-toggle-bt" class="button">Ẩn bóng địa hình</button>
    <input type="range" id="time-slider" min="0" max="11" step="1">
  </div>

  <div id="variable-name">Temperature + Wind</div>
  <div id="pointer-data"></div>
  <div id="map"></div>

  <!-- Color scale UI -->
  <div id="color-scale-container">
    <canvas id="color-scale-canvas" width="32" height="256"></canvas>
    <div class="scale-label max" id="scale-max-label">40°C</div>
    <div class="scale-label min" id="scale-min-label">0°C</div>
  </div>

  <!-- Status message -->
  <div id="status-message" style="position:fixed;right:30px;bottom:30px;z-index:100;color:#fff;background:rgba(0,0,0,0.7);padding:8px 16px;border-radius:8px;font-size:16px;display:none"></div>
  
  <script>
    // --- DOM Elements ---
    const timeInfoContainer = document.getElementById("time-info");
    const timeTextDiv = document.getElementById("time-text");
    const timeSlider = document.getElementById("time-slider");
    const playPauseButton = document.getElementById("play-pause-bt");
    const toggleTempButton = document.getElementById("toggle-temp-bt");
    const pointerDataDiv = document.getElementById("pointer-data");
    const colorScaleCanvas = document.getElementById('color-scale-canvas');
    const scaleMinLabel = document.getElementById('scale-min-label');
    const scaleMaxLabel = document.getElementById('scale-max-label');
    const autoScaleButton = document.getElementById('auto-scale-bt');
    const hillshadeToggleButton = document.getElementById('hillshade-toggle-bt');

    // --- State Variables ---
    let pointerLngLat = null;
    let tempLayerVisible = true;
    let isPlaying = false;
    let mapLoaded = false;
    let colorScale = [];
    const TEMP_LAYER_ID = 'temperature-layer';
    let hillshadeVisible = true;
    let weatherLayersAdded = false;

    // --- Tạo color scale mặc định ---
    colorScale = [
      { min: -73.3, max: -51.1, color: '#E5F0FF' },
      { min: -51.1, max: -48.3, color: '#DBE9FB' },
      { min: -48.3, max: -45.6, color: '#D3E2F7' },
      { min: -45.6, max: -42.8, color: '#CCDBF4' },
      { min: -42.8, max: -40.0, color: '#C0D4ED' },
      { min: -40.0, max: -37.2, color: '#B8CDEA' },
      { min: -37.2, max: -34.4, color: '#AFC6E7' },
      { min: -34.4, max: -31.7, color: '#A7BFE3' },
      { min: -31.7, max: -28.9, color: '#9CB8DE' },
      { min: -28.9, max: -26.1, color: '#93B1D7' },
      { min: -26.1, max: -23.3, color: '#89A5CD' },
      { min: -23.3, max: -20.6, color: '#7F9BC3' },
      { min: -20.6, max: -17.8, color: '#7591B9' },
      { min: -17.8, max: -15.0, color: '#607BA6' },
      { min: -15.0, max: -12.2, color: '#56719D' },
      { min: -12.2, max: -9.4, color: '#4D6591' },
      { min: -9.4, max: -6.7, color: '#415C88' },
      { min: -6.7, max: -3.9, color: '#39517F' },
      { min: -3.9, max: -1.1, color: '#2F4775' },
      { min: -1.1, max: 1.7, color: '#26426F' },
      { min: 1.7, max: 4.4, color: '#254F77' },
      { min: 4.4, max: 7.2, color: '#295B7D' },
      { min: 7.2, max: 10.0, color: '#276889' },
      { min: 10.0, max: 12.8, color: '#227891' },
      { min: 12.8, max: 15.6, color: '#438190' },
      { min: 15.6, max: 18.3, color: '#648D89' },
      { min: 18.3, max: 21.1, color: '#869B83' },
      { min: 21.1, max: 23.9, color: '#AAA87D' },
      { min: 23.9, max: 26.7, color: '#C2AC76' },
      { min: 26.7, max: 29.4, color: '#C29D61' },
      { min: 29.4, max: 32.2, color: '#C38A54' },
      { min: 32.2, max: 35.0, color: '#BE704D' },
      { min: 35.0, max: 37.8, color: '#AF4D4C' },
      { min: 37.8, max: 40.6, color: '#9F294C' },
      { min: 40.6, max: 43.3, color: '#86203E' },
      { min: 43.3, max: 46.1, color: '#6E1531' },
      { min: 46.1, max: 48.9, color: '#570C25' },
      { min: 48.9, max: 65.6, color: '#3D0216' }
    ];

    // --- MapTiler Initialization ---
    maptilersdk.config.apiKey = 'oinoatcrNmdCL1524DOl';

    const map = new maptilersdk.Map({
      container: document.getElementById('map'),
      hash: true,
      zoom: 2,
      center: [0, 40],
      style: maptilersdk.MapStyle.BACKDROP,
      projectionControl: true,
      projection: 'globe'
    });

    // --- Layer Initialization ---
    let layerBg;
    
    const layer = new maptilerweather.WindLayer({
      id: "Wind Particles",
      colorramp: maptilerweather.ColorRamp.builtin.NULL,
      speed: 0.002, fadeFactor: 0.03, maxAmount: 256, density: 400,
      color: [255, 0, 0, 30], fastColor: [0, 255, 0, 100],
    });

    // ===== HÀM XỬ LÝ RANH GIỚI =====
    function removeFillFromAllBoundaryLayers() {
        if (!map.isStyleLoaded()) return;

        const layers = map.getStyle().layers;
        console.log('Processing layers for boundary cleanup...');
        
        layers.forEach(layer => {
            const id = layer.id.toLowerCase();
            
            // Xử lý các fill layer có thể ảnh hưởng đến hiển thị
            if (layer.type === 'fill') {
                const isBoundaryLayer = (
                    id.includes('admin') || 
                    id.includes('boundary') || 
                    id.includes('country') || 
                    id.includes('state') || 
                    id.includes('province') ||
                    id.includes('border') ||
                    id.includes('water') ||
                    id.includes('land') ||
                    id === 'water'
                );
                
                if (isBoundaryLayer) {
                    try {
                        // Loại bỏ hoàn toàn fill
                        map.setPaintProperty(layer.id, 'fill-opacity', 0);
                        map.setPaintProperty(layer.id, 'fill-color', 'rgba(0,0,0,0)');
                        
                        // Đảm bảo outline hiển thị rõ
                        const outlineProps = ['fill-outline-color'];
                        outlineProps.forEach(prop => {
                            if (map.getPaintProperty(layer.id, prop) !== undefined) {
                                map.setPaintProperty(layer.id, prop, '#888888');
                            }
                        });
                        
                        console.log(`✓ Removed fill from boundary layer: ${layer.id}`);
                    } catch (e) {
                        console.log(`⚠ Could not modify layer ${layer.id}:`, e.message);
                    }
                }
            }
        });
    }

    function addCustomBoundaryLayers() {
        try {
            // Thêm custom boundary layers với line style rõ ràng
            
            // 1. Country borders (dày nhất)
            map.addSource('country-boundaries', {
                type: 'vector',
                url: `https://api.maptiler.com/tiles/admin/tiles.json?key=${maptilersdk.config.apiKey}`
            });

            // Country borders với outline để tạo độ nổi bật
            map.addLayer({
                id: 'country-borders-outline',
                type: 'line',
                source: 'country-boundaries',
                'source-layer': 'admin',
                filter: ['==', 'admin_level', 2],
                paint: {
                    'line-color': '#000000',
                    'line-width': [
                        'interpolate',
                        ['linear'],
                        ['zoom'],
                        0, 2,
                        5, 4,
                        10, 6
                    ],
                    'line-opacity': 0.8
                }
            });

            map.addLayer({
                id: 'country-borders',
                type: 'line',
                source: 'country-boundaries',
                'source-layer': 'admin',
                filter: ['==', 'admin_level', 2],
                paint: {
                    'line-color': '#ffffff',
                    'line-width': [
                        'interpolate',
                        ['linear'],
                        ['zoom'],
                        0, 1.5,
                        5, 3,
                        10, 4.5
                    ],
                    'line-opacity': 1.0
                }
            });

            // 2. State/Province borders (mỏng hơn)
            map.addLayer({
                id: 'state-borders',
                type: 'line',
                source: 'country-boundaries',
                'source-layer': 'admin',
                filter: ['==', 'admin_level', 4],
                paint: {
                    'line-color': '#dddddd',
                    'line-width': [
                        'interpolate',
                        ['linear'],
                        ['zoom'],
                        3, 0.5,
                        6, 1,
                        10, 2
                    ],
                    'line-opacity': 0.8
                }
            });

            console.log('✓ Custom boundary layers added');
        } catch (error) {
            console.error('Error adding custom boundary layers:', error);
        }
    }

    function ensureBoundariesOnTop() {
        if (!map.isStyleLoaded()) return;

        // Danh sách các layer cần ưu tiên hiển thị trên cùng
        const priorityLayers = [
            'country-borders-outline',
            'country-borders',
            'state-borders'
        ];

        // Di chuyển các layer ưu tiên lên trên cùng
        priorityLayers.forEach(layerId => {
            try {
                if (map.getLayer(layerId)) {
                    map.moveLayer(layerId);
                    console.log(`✓ Moved ${layerId} to top`);
                }
            } catch (e) {
                console.log(`⚠ Failed to move layer ${layerId}:`, e.message);
            }
        });

        // Tìm và di chuyển các layer symbol/label lên trên
        const layers = map.getStyle().layers;
        const symbolLayers = layers.filter(layer => {
            const id = layer.id.toLowerCase();
            return (
                layer.type === 'symbol' ||
                id.includes('label') || 
                id.includes('text') || 
                id.includes('place') ||
                id.includes('city') ||
                id.includes('town')
            );
        });

        symbolLayers.forEach(layer => {
            try {
                if (map.getLayer(layer.id)) {
                    map.moveLayer(layer.id);
                    console.log(`✓ Moved symbol layer ${layer.id} to top`);
                }
            } catch (e) {
                console.log(`⚠ Failed to move symbol layer ${layer.id}:`, e.message);
            }
        });
    }

    // Hàm thêm custom layers
    function addCustomLayers() {
        try {
            console.log('Adding custom layers...');

            // 1. Xử lý fill layers trước
            removeFillFromAllBoundaryLayers();

            // 2. Thêm hillshade
            map.addSource('dem', {
                type: 'raster-dem',
                url: `https://api.maptiler.com/tiles/terrain-rgb/tiles.json?key=${maptilersdk.config.apiKey}`,
                tileSize: 256, 
                maxzoom: 12
            });

            map.addLayer({
                id: 'hillshade-shadows', 
                type: 'hillshade', 
                source: 'dem',
                layout: { visibility: hillshadeVisible ? 'visible' : 'none' },
                paint: { 
                    'hillshade-exaggeration': 0.65, 
                    'hillshade-illumination-direction': 315, 
                    'hillshade-highlight-color': 'rgba(0, 0, 0, 0)', 
                    'hillshade-shadow-color': '#252525' 
                }
            });
            
            map.addLayer({
                id: 'hillshade-highlights', 
                type: 'hillshade', 
                source: 'dem',
                layout: { visibility: hillshadeVisible ? 'visible' : 'none' },
                paint: { 
                    'hillshade-exaggeration': 0.65, 
                    'hillshade-illumination-direction': 315, 
                    'hillshade-shadow-color': 'rgba(0, 0, 0, 0)', 
                    'hillshade-highlight-color': '#FFFFFF', 
                    'hillshade-opacity': 0.2 
                }
            });

            // 3. Thêm temperature layer
            updateTemperatureColorRamp();

            // 4. Thêm wind layer
            map.addLayer(layer);

            // 5. Thêm custom boundary layers
            addCustomBoundaryLayers();

            // 6. Đảm bảo ranh giới lên trên
            setTimeout(() => {
                ensureBoundariesOnTop();
            }, 100);

            mapLoaded = true;
            console.log('✓ All custom layers added successfully');
            
        } catch (error) {
            console.error('❌ Error adding custom layers:', error);
        }
    }

    // --- Map Events ---
    map.on('load', function () {
        console.log('Map loaded, initial cleanup...');
        removeFillFromAllBoundaryLayers();
    });

    // Sử dụng event 'idle' để đảm bảo map đã sẵn sàng hoàn toàn
    map.on('idle', function() {
        if (!weatherLayersAdded && map.isStyleLoaded()) {
            weatherLayersAdded = true;
            console.log('Map is idle and style loaded, adding custom layers...');
            
            setTimeout(() => {
                addCustomLayers();
            }, 200);
        }
    });

    // Event để xử lý khi style thay đổi
    map.on('styledata', function() {
        if (mapLoaded) {
            console.log('Style changed, cleaning up...');
            setTimeout(() => {
                removeFillFromAllBoundaryLayers();
                ensureBoundariesOnTop();
            }, 100);
        }
    });

    // Event khi data load xong
    map.on('data', function(e) {
        if (e.dataType === 'source' && mapLoaded) {
            setTimeout(() => {
                removeFillFromAllBoundaryLayers();
            }, 50);
        }
    });

    layer.on("sourceReady", () => {
        console.log('Wind layer source ready');
        refreshTimeUI();
        const startDate = layer.getAnimationStartDate();
        const endDate = layer.getAnimationEndDate();
        timeSlider.min = +startDate;
        timeSlider.max = +endDate;
    });

    // --- UI Event Listeners ---
    timeSlider.addEventListener("input", () => {
      const time = parseInt(timeSlider.value);
      layer.setAnimationTime(time / 1000);
      if (layerBg) layerBg.setAnimationTime(time / 1000);
    });

    playPauseButton.addEventListener("click", () => {
      isPlaying = !isPlaying;
      if (isPlaying) {
        layer.animateByFactor(3600); 
        if (layerBg) layerBg.animateByFactor(3600);
        playPauseButton.innerText = "Pause";
      } else {
        layer.animateByFactor(0); 
        if (layerBg) layerBg.animateByFactor(0);
        playPauseButton.innerText = "Play 3600x";
      }
    });

    toggleTempButton.addEventListener("click", () => {
      tempLayerVisible = !tempLayerVisible;
      if (layerBg) {
        const visibility = tempLayerVisible ? 'visible' : 'none';
        map.setLayoutProperty(TEMP_LAYER_ID, 'visibility', visibility);
        toggleTempButton.innerText = tempLayerVisible ? "Ẩn nhiệt độ" : "Hiện nhiệt độ";
      }
    });

    autoScaleButton.style.display = 'none';

    hillshadeToggleButton.addEventListener('click', function() {
      hillshadeVisible = !hillshadeVisible;
      const visibility = hillshadeVisible ? 'visible' : 'none';
      if (map.getLayer('hillshade-shadows')) { 
        map.setLayoutProperty('hillshade-shadows', 'visibility', visibility); 
      }
      if (map.getLayer('hillshade-highlights')) { 
        map.setLayoutProperty('hillshade-highlights', 'visibility', visibility); 
      }
      hillshadeToggleButton.innerText = hillshadeVisible ? 'Ẩn bóng địa hình' : 'Hiện bóng địa hình';
    });

    map.on('mousemove', (e) => updatePointerValue(e.lngLat));
    timeInfoContainer.addEventListener("mouseenter", () => pointerDataDiv.innerText = "");
    
    // --- Helper Functions ---
    function updateTemperatureColorRamp() {
        if (!colorScale.length) return;
        
        // Remove existing temperature layer if present
        if (map.getLayer(TEMP_LAYER_ID)) {
            map.removeLayer(TEMP_LAYER_ID);
        }
        
        const ramp = colorScale.map(entry => ({ 
            value: entry.min, 
            color: hexToRgb(entry.color) 
        }));
        
        layerBg = new maptilerweather.TemperatureLayer({
            id: TEMP_LAYER_ID, 
            opacity: 1, // Tăng opacity lên 0.9 để màu đậm hơn
            colorramp: ramp,
        });
        
        // Thêm temperature layer vào vị trí thích hợp
        if (map.getLayer('hillshade-shadows')) {
            map.addLayer(layerBg, 'hillshade-shadows');
        } else {
            map.addLayer(layerBg);
        }
        
        drawColorScale(ramp.map(r => r.color));
        scaleMinLabel.innerText = `${colorScale[0].min}°C`;
        scaleMaxLabel.innerText = `${colorScale[colorScale.length-1].max}°C`;
        
        console.log('✓ Temperature layer updated');
    }

    function refreshTimeUI() {
      const d = layer.getAnimationTimeDate();
      if (d) {
        timeTextDiv.innerText = d.toString();
        timeSlider.value = +d;
      }
      updatePointerValue(pointerLngLat);
    }

    function updatePointerValue(lngLat) {
      if (!lngLat || !mapLoaded) return;
      if (!layerBg && tempLayerVisible) return;
      
      pointerLngLat = lngLat;
      const valueWind = layer.pickAt(lngLat.lng, lngLat.lat);
      let valueTemp = null;
      if(layerBg) {
        valueTemp = layerBg.pickAt(lngLat.lng, lngLat.lat);
      }

      let tempText = tempLayerVisible && valueTemp && valueTemp.value != null ? `${valueTemp.value.toFixed(1)}°C\n` : "";
      let windText = valueWind ? `${valueWind.speedKilometersPerHour.toFixed(1)} km/h` : "";
      
      pointerDataDiv.innerText = tempText + windText;
    }

    function hexToRgb(hex) {
      const v = hex.replace('#', '');
      return [parseInt(v.substring(0,2),16), parseInt(v.substring(2,4),16), parseInt(v.substring(4,6),16)];
    }

    function drawColorScale(colors) {
      const ctx = colorScaleCanvas.getContext('2d');
      ctx.clearRect(0, 0, colorScaleCanvas.width, colorScaleCanvas.height);
      for (let i = 0; i < colors.length; i++) {
        ctx.fillStyle = `rgb(${colors[i][0]},${colors[i][1]},${colors[i][2]})`;
        const y = colorScaleCanvas.height - (i + 1) * (colorScaleCanvas.height / colors.length);
        const h = colorScaleCanvas.height / colors.length + 1;
        ctx.fillRect(0, y, colorScaleCanvas.width, h);
      }
    }
  </script>
</body>
</html>