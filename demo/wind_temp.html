<!DOCTYPE html>
<html>
<head>
  <title>MapTiler Wind + Temperature (Final Version)</title>
  <meta charset="utf-8" />
  <meta name="viewport" content="initial-scale=1,maximum-scale=1,user-scalable=no" />
  <script src="https://cdn.maptiler.com/maptiler-sdk-js/v3.2.0/maptiler-sdk.umd.min.js"></script>
  <link href="https://cdn.maptiler.com/maptiler-sdk-js/v3.2.0/maptiler-sdk.css" rel="stylesheet" />
  <script src="https://cdn.maptiler.com/maptiler-weather/v3.0.1/maptiler-weather.umd.min.js"></script>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: sans-serif;
    }

    #map {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 100%;
      background-color: #444952;
    }

    #time-info {
      position: fixed;
      width: 60vw;
      bottom: 0;
      z-index: 1;
      margin: 10px;
      text-shadow: 0px 0px 5px black;
      color: white;
      font-size: 20px;
      font-weight: 500;
      text-align: center;
      left: 0;
      right: 0;
      margin: auto;
      padding: 20px;
    }

    #time-slider {
      width: 100%;
      height: fit-content;
      left: 0;
      right: 0;
      z-index: 1;
      filter: drop-shadow(0 0 7px #000a);
      margin-top: 10px;
    }

    #pointer-data {
      z-index: 1;
      position: fixed;
      font-size: 50px;
      font-weight: 900;
      margin: 27px 0px 0px 10px;
      color: #fff;
      text-shadow: 0px 0px 10px #0007;
      white-space: pre; /* Để hiển thị ký tự xuống dòng \n */
    }

    #variable-name {
      z-index: 1;
      position: fixed;
      font-size: 20px;
      font-weight: 500;
      margin: 5px 0px 0px 10px;
      color: #fff;
      text-shadow: 0px 0px 10px #0007;
    }

    .button {
      cursor: pointer;
      width: auto;
      padding: 8px;
      border-radius: 3px;
      margin: 10px 0 0 0;
      font-size: 12px;
      text-align: center;
      color: #fff;
      background: #3174ff;
      font-family: sans-serif;
      font-weight: bold;
    }

    #color-scale-container {
      position: fixed;
      right: 30px;
      top: 50%;
      transform: translateY(-50%);
      z-index: 10;
      display: flex;
      flex-direction: column;
      align-items: center;
      background: rgba(0,0,0,0.15);
      border-radius: 10px;
      padding: 12px 8px;
      box-shadow: 0 2px 8px #0003;
    }
    #color-scale-canvas {
      display: block;
      width: 32px;
      height: 256px;
      border-radius: 8px;
      margin-bottom: 8px;
      margin-top: 8px;
    }
    .scale-label {
      color: #fff;
      font-size: 15px;
      font-weight: bold;
      text-shadow: 0 1px 4px #000a;
      margin: 0 0 2px 0;
      user-select: none;
    }
    .scale-label.min {
      margin-bottom: 4px;
    }
    .scale-label.max {
      margin-top: 4px;
    }
  </style>
</head>

<body>
  <div id="time-info">
    <span id="time-text"></span>
    <button id="play-pause-bt" class="button">Play 3600x</button>
    <button id="toggle-temp-bt" class="button">Ẩn nhiệt độ</button>
    <button id="auto-scale-bt" class="button">Cập nhật Auto-scale</button>
    <button id="hillshade-toggle-bt" class="button">Ẩn bóng địa hình</button>
    <input type="range" id="time-slider" min="0" max="11" step="1">
  </div>

  <div id="variable-name">Temperature + Wind</div>
  <div id="pointer-data"></div>
  <div id="map"></div>

  <!-- Color scale UI -->
  <div id="color-scale-container">
    <canvas id="color-scale-canvas" width="32" height="256"></canvas>
    <div class="scale-label max" id="scale-max-label">40°C</div>
    <div class="scale-label min" id="scale-min-label">0°C</div>
  </div>

  <!-- Hidden palette image for color sampling -->
  <img id="palette-img" src="Color Hunt Palette 03a6a1ffe3bbffa673ff4f0f.png" style="display:none" crossorigin="anonymous" />

  <!-- Status message -->
  <div id="status-message" style="position:fixed;right:30px;bottom:30px;z-index:100;color:#fff;background:rgba(0,0,0,0.7);padding:8px 16px;border-radius:8px;font-size:16px;display:none"></div>
  
  <script>
    // --- DOM Elements ---
    const timeInfoContainer = document.getElementById("time-info");
    const timeTextDiv = document.getElementById("time-text");
    const timeSlider = document.getElementById("time-slider");
    const playPauseButton = document.getElementById("play-pause-bt");
    const toggleTempButton = document.getElementById("toggle-temp-bt");
    const pointerDataDiv = document.getElementById("pointer-data");
    const paletteImg = document.getElementById('palette-img');
    const colorScaleCanvas = document.getElementById('color-scale-canvas');
    const scaleMinLabel = document.getElementById('scale-min-label');
    const scaleMaxLabel = document.getElementById('scale-max-label');
    const autoScaleButton = document.getElementById('auto-scale-bt');
    const hillshadeToggleButton = document.createElement('button');
    hillshadeToggleButton.className = 'button';
    hillshadeToggleButton.style.marginLeft = '10px';
    hillshadeToggleButton.innerText = 'Ẩn bóng địa hình';
    let hillshadeVisible = true;
    document.getElementById('time-info').appendChild(hillshadeToggleButton);

    // --- State Variables ---
    let pointerLngLat = null;
    let tempLayerVisible = true;
    let isPlaying = false;
    let mapLoaded = false;
    let colorScale = [];
    let tempMin = -100;
    let tempMax = 150;
    const TEMP_LAYER_ID = 'temperature-layer';

    // --- Đọc color scale từ file txt (dùng fetch) ---
    fetch('color_scale.txt')
      .then(res => res.text())
      .then(txt => {
        colorScale = txt.trim().split('\n').map(line => {
          const [min, max, hex] = line.trim().split(/\s+/);
          return { min: +min, max: +max, color: hex };
        });
        if (mapLoaded) updateTemperatureColorRamp();
      });

    // --- MapTiler Initialization ---
    maptilersdk.config.apiKey = 'oinoatcrNmdCL1524DOl';

    const map = new maptilersdk.Map({
      container: document.getElementById('map'),
      hash: true,
      zoom: 2,
      center: [0, 40],
      style: maptilersdk.MapStyle.BACKDROP,
      projectionControl: true,
      projection: 'globe'
    });

    // --- Layer Initialization ---
    let layerBg;
    
    const layer = new maptilerweather.WindLayer({
      id: "Wind Particles",
      colorramp: maptilerweather.ColorRamp.builtin.NULL,
      speed: 0.002,
      fadeFactor: 0.03,
      maxAmount: 256,
      density: 400,
      color: [255, 0, 0, 30],
      fastColor: [0, 255, 0, 100],
    });

    // --- Map and Layer Events ---
    map.on('load', function () {
      map.setPaintProperty("Water", 'fill-color', "rgba(0, 0, 0, 0.6)");
      map.addLayer(layer);

      // Thêm DEM source và hillshade layer
      map.addSource('dem', {
        type: 'raster-dem',
        url: `https://api.maptiler.com/tiles/terrain-rgb/tiles.json?key=${maptilersdk.config.apiKey}`,
        tileSize: 256,
        maxzoom: 12
      });
      try {
        map.addLayer({
          id: 'hillshade',
          type: 'hillshade',
          source: 'dem',
          layout: {},
          paint: {
            'hillshade-exaggeration': 0.5,
            'hillshade-shadow-color': '#444',
            'hillshade-highlight-color': '#fff',
            'hillshade-accent-color': '#aaa',
            'hillshade-illumination-direction': 335,
            'hillshade-illumination-anchor': 'viewport',
            'hillshade-opacity': 0.3
          }
        });
        console.log('Đã thêm hillshade layer thành công!');
      } catch (e) {
        console.error('Lỗi khi thêm hillshade layer:', e);
      }

      mapLoaded = true;
      if (colorScale.length) updateTemperatureColorRamp();
    });

    // Events của WindLayer
    layer.on("sourceReady", () => {
      refreshTimeUI();
      const startDate = layer.getAnimationStartDate();
      const endDate = layer.getAnimationEndDate();
      timeSlider.min = +startDate;
      timeSlider.max = +endDate;
    });

    // --- UI Event Listeners ---
    timeSlider.addEventListener("input", () => {
      const time = parseInt(timeSlider.value);
      layer.setAnimationTime(time / 1000);
      if (layerBg) layerBg.setAnimationTime(time / 1000);
    });

    playPauseButton.addEventListener("click", () => {
      isPlaying = !isPlaying;
      if (isPlaying) {
        layer.animateByFactor(3600);
        if (layerBg) layerBg.animateByFactor(3600);
        playPauseButton.innerText = "Pause";
      } else {
        layer.animateByFactor(0);
        if (layerBg) layerBg.animateByFactor(0);
        playPauseButton.innerText = "Play 3600x";
      }
    });

    toggleTempButton.addEventListener("click", () => {
      tempLayerVisible = !tempLayerVisible;
      if (layerBg) {
        layerBg.setOpacity(tempLayerVisible ? 0.8 : 0);
        toggleTempButton.innerText = tempLayerVisible ? "Ẩn nhiệt độ" : "Hiện nhiệt độ";
      }
    });

    // Bỏ hoàn toàn autoscale
    autoScaleButton.style.display = 'none';

    map.on('mousemove', (e) => updatePointerValue(e.lngLat));
    timeInfoContainer.addEventListener("mouseenter", () => pointerDataDiv.innerText = "");
    
    // --- Helper Functions ---
    function refreshTimeUI() {
      const d = layer.getAnimationTimeDate();
      if (d) {
        timeTextDiv.innerText = d.toString();
        timeSlider.value = +d;
      }
      updatePointerValue(pointerLngLat);
    }

    function updatePointerValue(lngLat) {
      if (!lngLat || !layerBg) return;
      pointerLngLat = lngLat;
      const valueWind = layer.pickAt(lngLat.lng, lngLat.lat);
      const valueTemp = layerBg.pickAt(lngLat.lng, lngLat.lat);

      if (!valueWind || !valueTemp || valueTemp.value == null) {
        pointerDataDiv.innerText = "";
        return;
      }
      pointerDataDiv.innerText = `${valueTemp.value.toFixed(1)}°C\n${valueWind.speedKilometersPerHour.toFixed(1)} km/h`;
    }

    function updateTemperatureColorRamp() {
      if (!colorScale.length || !mapLoaded) return;
      if (map.getLayer(TEMP_LAYER_ID)) {
        map.removeLayer(TEMP_LAYER_ID);
      }
      // Tạo colorramp cho TemperatureLayer
      const ramp = colorScale.map(entry => ({
        value: entry.min,
        color: hexToRgb(entry.color)
      }));
      layerBg = new maptilerweather.TemperatureLayer({
        id: TEMP_LAYER_ID,
        opacity: tempLayerVisible ? 0.8 : 0,
        colorramp: ramp,
      });
      map.addLayer(layerBg, "Water");
      drawColorScale(ramp.map(r => r.color));
      scaleMinLabel.innerText = `${colorScale[0].min}°C`;
      scaleMaxLabel.innerText = `${colorScale[colorScale.length-1].max}°C`;
    }

    function hexToRgb(hex) {
      const v = hex.replace('#', '');
      return [parseInt(v.substring(0,2),16), parseInt(v.substring(2,4),16), parseInt(v.substring(4,6),16)];
    }

    function drawColorScale(colors) {
      const ctx = colorScaleCanvas.getContext('2d');
      ctx.clearRect(0, 0, colorScaleCanvas.width, colorScaleCanvas.height);
      for (let i = 0; i < colors.length; i++) {
        ctx.fillStyle = `rgb(${colors[i][0]},${colors[i][1]},${colors[i][2]})`;
        const y = colorScaleCanvas.height - (i + 1) * (colorScaleCanvas.height / colors.length);
        const h = colorScaleCanvas.height / colors.length + 1;
        ctx.fillRect(0, y, colorScaleCanvas.width, h);
      }
    }

    function showStatus(msg, timeout = 2500) {
      const el = document.getElementById('status-message');
      el.innerText = msg;
      el.style.display = 'block';
      clearTimeout(el._timeout);
      el._timeout = setTimeout(() => { el.style.display = 'none'; }, timeout);
    }

    hillshadeToggleButton.onclick = function() {
      hillshadeVisible = !hillshadeVisible;
      if (map.getLayer('hillshade')) {
        map.setLayoutProperty('hillshade', 'visibility', hillshadeVisible ? 'visible' : 'none');
      }
      hillshadeToggleButton.innerText = hillshadeVisible ? 'Ẩn bóng địa hình' : 'Hiện bóng địa hình';
    };
  </script>
</body>
</html>