/**
 * Offline Scene Manager for Weather App
 */
class OfflineSceneManager {
  constructor() {
    this.db = null;
    this.isProcessing = false;
    this.tempTiles = new Map();
    
    // Constants
    this.MAX_SCENES = 5;
    this.SHORT_SAVE_TTL = 3 * 60 * 60 * 1000; // 3 hours
    this.TILE_SIZE_ESTIMATE = 50 * 1024; // 50KB
    
    this.init();
  }

  async init() {
    try {
      await this.initIndexedDB();
      await this.registerServiceWorker();
      await this.cleanupExpiredShortSaves();
      console.log('✅ Offline Scene Manager ready');
    } catch (error) {
      console.error('❌ Init failed:', error);
    }
  }

  async initIndexedDB() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('WeatherAppOffline', 1);
      
      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };
      
      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        
        // Scenes table
        if (!db.objectStoreNames.contains('scenes')) {
          const scenesStore = db.createObjectStore('scenes', { keyPath: 'id' });
          scenesStore.createIndex('type', 'type', { unique: false });
        }
        
        // Global tile pool
        if (!db.objectStoreNames.contains('global_tile_pool')) {
          const tilesStore = db.createObjectStore('global_tile_pool', { keyPath: 'url' });
          tilesStore.createIndex('refCount', 'refCount', { unique: false });
        }
      };
    });
  }

  async registerServiceWorker() {
    if (!('serviceWorker' in navigator)) return;
    
    try {
      await navigator.serviceWorker.register('/demo/tile-cache-worker.js');
      console.log('✅ Service Worker registered');
    } catch (error) {
      console.warn('Service Worker failed:', error);
    }
  }

  async cleanupExpiredShortSaves() {
    if (!this.db) return;
    
    try {
      const transaction = this.db.transaction(['scenes'], 'readwrite');
      const store = transaction.objectStore('scenes');
      const index = store.index('type');
      const request = index.getAll('short');
      
      request.onsuccess = () => {
        const shortScenes = request.result;
        const now = Date.now();
        
        shortScenes.forEach(scene => {
          if (now - scene.timestamp > this.SHORT_SAVE_TTL) {
            console.log(`🗑️ Cleaning expired: ${scene.id}`);
            store.delete(scene.id);
            this.updateSceneButtonUI(scene.id, 'empty');
          }
        });
      };
    } catch (error) {
      console.error('Cleanup failed:', error);
    }
  }

  // Save scene with short save (metadata only)
  async saveShortScene(sceneId, sceneName, mapState) {
    if (!this.db) return false;
    
    try {
      const sceneData = {
        id: sceneId,
        type: 'short',
        name: sceneName,
        timestamp: Date.now(),
        mapState: mapState
      };

      const transaction = this.db.transaction(['scenes'], 'readwrite');
      const store = transaction.objectStore('scenes');
      await this.putInStore(store, sceneData);

      this.updateSceneButtonUI(sceneId, 'short', sceneName);
      
      // Add timeline marker
      if (window.timelineManager) {
        window.timelineManager.addSceneMarker(sceneId, 'short', mapState.time, sceneName);
      }
      
      this.showToast('Short save completed', 'success');
      return true;
    } catch (error) {
      console.error('Short save failed:', error);
      this.showToast('Save failed', 'error');
      return false;
    }
  }

  // Save scene with long save (metadata + tiles)
  async saveLongScene(sceneId, sceneName, mapState, map, activeLayers) {
    if (this.isProcessing || !this.db) return false;
    
    this.isProcessing = true;
    let cancelled = false;

    try {
      // Get viewport tiles
      const tileUrls = this.getViewportTiles(map, activeLayers);
      console.log(`📦 Processing ${tileUrls.length} tiles`);

      // Check storage quota
      const hasQuota = await this.checkStorageQuota(tileUrls.length);
      if (!hasQuota) {
        this.showToast('Storage full - clear a scene', 'error');
        return false;
      }

      // Show modal
      const modal = this.showSaveModal(tileUrls.length, () => { cancelled = true; });
      
      // Process tiles
      let processed = 0;
      for (const tileUrl of tileUrls) {
        if (cancelled) break;
        
        await this.processTileForLongSave(tileUrl, sceneId);
        processed++;
        this.updateProgressModal(modal, processed, tileUrls.length);
      }

      if (cancelled) {
        await this.revertTempTiles(sceneId);
        this.hideSaveModal(modal);
        this.showToast('Save cancelled', 'info');
        return false;
      }

      // Finalize
      await this.finalizeLongSave(sceneId, sceneName, mapState, tileUrls);
      this.hideSaveModal(modal);
      this.updateSceneButtonUI(sceneId, 'long', sceneName);
      
      // Add timeline marker
      if (window.timelineManager) {
        window.timelineManager.addSceneMarker(sceneId, 'long', mapState.time, sceneName);
      }
      
      this.showToast('Long save completed', 'success');
      return true;

    } catch (error) {
      console.error('Long save failed:', error);
      this.showToast('Save failed', 'error');
      return false;
    } finally {
      this.tempTiles.clear();
      this.isProcessing = false;
    }
  }

  getViewportTiles(map, activeLayers) {
    const bounds = map.getBounds();
    const zoom = Math.floor(map.getZoom());
    const tiles = [];

    const ne = bounds.getNorthEast();
    const sw = bounds.getSouthWest();
    const scale = Math.pow(2, zoom);
    
    const minX = Math.floor((sw.lng + 180) / 360 * scale);
    const maxX = Math.floor((ne.lng + 180) / 360 * scale);
    const minY = Math.floor((1 - Math.log(Math.tan(ne.lat * Math.PI / 180) + 1 / Math.cos(ne.lat * Math.PI / 180)) / Math.PI) / 2 * scale);
    const maxY = Math.floor((1 - Math.log(Math.tan(sw.lat * Math.PI / 180) + 1 / Math.cos(sw.lat * Math.PI / 180)) / Math.PI) / 2 * scale);

    activeLayers.forEach(layer => {
      for (let x = minX; x <= maxX; x++) {
        for (let y = minY; y <= maxY; y++) {
          tiles.push(`${layer}/${zoom}/${x}/${y}`);
        }
      }
    });

    return tiles;
  }

  async checkStorageQuota(estimatedTiles) {
    try {
      if ('storage' in navigator && 'estimate' in navigator.storage) {
        const estimate = await navigator.storage.estimate();
        const available = (estimate.quota || 0) - (estimate.usage || 0);
        const needed = estimatedTiles * this.TILE_SIZE_ESTIMATE;
        return available > needed * 1.2;
      }
      return true;
    } catch (error) {
      return true;
    }
  }

  async processTileForLongSave(tileUrl, sceneId) {
    try {
      const transaction = this.db.transaction(['global_tile_pool'], 'readwrite');
      const store = transaction.objectStore('global_tile_pool');
      const existing = await this.getFromStore(store, tileUrl);

      if (existing) {
        existing.refCount++;
        if (!existing.usedByScenes.includes(sceneId)) {
          existing.usedByScenes.push(sceneId);
        }
        this.tempTiles.set(tileUrl, existing);
      } else {
        const tileData = await this.fetchTileData(tileUrl);
        const tileRecord = {
          url: tileUrl,
          data: tileData,
          refCount: 1,
          usedByScenes: [sceneId]
        };
        this.tempTiles.set(tileUrl, tileRecord);
      }
    } catch (error) {
      console.error(`Tile processing failed: ${tileUrl}`, error);
    }
  }

  async fetchTileData(tileUrl) {
    // Simulate tile fetching - replace with actual implementation
    return new ArrayBuffer(this.TILE_SIZE_ESTIMATE);
  }

  async finalizeLongSave(sceneId, sceneName, mapState, tileUrls) {
    const transaction = this.db.transaction(['scenes', 'global_tile_pool'], 'readwrite');
    const scenesStore = transaction.objectStore('scenes');
    const tilesStore = transaction.objectStore('global_tile_pool');

    // Save tiles
    for (const [url, tileData] of this.tempTiles) {
      await this.putInStore(tilesStore, tileData);
    }

    // Save scene
    const sceneData = {
      id: sceneId,
      type: 'long',
      name: sceneName,
      timestamp: Date.now(),
      mapState: { ...mapState, tileUrls }
    };
    await this.putInStore(scenesStore, sceneData);
  }

  async revertTempTiles(sceneId) {
    console.log('Reverting temp tiles...');
  }

  async deleteScene(sceneId) {
    if (!this.db) return;
    
    try {
      const transaction = this.db.transaction(['scenes', 'global_tile_pool'], 'readwrite');
      const scenesStore = transaction.objectStore('scenes');
      
      const scene = await this.getFromStore(scenesStore, sceneId);
      if (scene) {
        await scenesStore.delete(sceneId);
        this.updateSceneButtonUI(sceneId, 'empty');
        
        // Remove timeline marker
        if (window.timelineManager) {
          window.timelineManager.removeSceneMarker(sceneId);
        }
        
        this.showToast('Scene deleted', 'info');
      }
    } catch (error) {
      console.error('Delete failed:', error);
    }
  }

  async loadScene(sceneId) {
    if (!this.db) return null;
    
    try {
      const transaction = this.db.transaction(['scenes'], 'readonly');
      const store = transaction.objectStore('scenes');
      const scene = await this.getFromStore(store, sceneId);
      
      if (scene) {
        console.log(`📦 Loading scene from IndexedDB: ${scene.name} (${scene.type})`);
        return scene.mapState; // Return the mapState for compatibility with existing loadScene function
      }
      
      return null;
    } catch (error) {
      console.error('Load scene failed:', error);
      return null;
    }
  }

  async loadSavedScenesUI() {
    if (!this.db) return;
    
    try {
      const transaction = this.db.transaction(['scenes'], 'readonly');
      const store = transaction.objectStore('scenes');
      const allScenes = await this.getAllFromStore(store);
      
      console.log(`📦 Found ${allScenes.length} scenes in IndexedDB:`, allScenes);
      
      // Update UI for each scene
      allScenes.forEach(scene => {
        this.updateSceneButtonUI(scene.id, scene.type, scene.name);
        console.log(`📦 Updated UI for ${scene.id}: ${scene.name} (${scene.type})`);
      });
      
    } catch (error) {
      console.error('Load saved scenes UI failed:', error);
    }
  }

  async loadTimelineMarkers(timelineManager) {
    if (!this.db || !timelineManager) return;
    
    try {
      const transaction = this.db.transaction(['scenes'], 'readonly');
      const store = transaction.objectStore('scenes');
      const allScenes = await this.getAllFromStore(store);
      
      console.log(`📍 Loading ${allScenes.length} IndexedDB scenes to timeline...`);
      
      // Add timeline markers for each scene
      allScenes.forEach(scene => {
        if (scene.mapState && scene.mapState.time) {
          timelineManager.addSceneMarker(
            scene.id,
            scene.type, // 'short' or 'long'
            scene.mapState.time,
            scene.name
          );
          
          console.log(`📍 Added timeline marker: ${scene.name} (${scene.type}) at ${new Date(scene.mapState.time)}`);
        }
      });
      
    } catch (error) {
      console.error('Load timeline markers failed:', error);
    }
  }

  async clearAllScenes() {
    if (!this.db) return;
    
    try {
      console.log('🗑️ Clearing all offline scenes...');
      
      const transaction = this.db.transaction(['scenes', 'global_tile_pool'], 'readwrite');
      const scenesStore = transaction.objectStore('scenes');
      const tilesStore = transaction.objectStore('global_tile_pool');
      
      // Get all scenes first
      const allScenes = await this.getAllFromStore(scenesStore);
      
      // Delete all scenes
      for (const scene of allScenes) {
        await scenesStore.delete(scene.id);
        console.log(`🗑️ Deleted scene: ${scene.id}`);
      }
      
      // Clear all tiles (since no scenes reference them anymore)
      await tilesStore.clear();
      console.log('🗑️ Cleared all tiles from global pool');
      
      // Update UI for all scene buttons
      for (let i = 1; i <= this.MAX_SCENES; i++) {
        this.updateSceneButtonUI(`scene-${i}`, 'empty');
      }
      
      // Remove any remaining delete buttons in body
      const allDeleteBtns = document.querySelectorAll('.delete-btn[data-scene-id]');
      allDeleteBtns.forEach(btn => btn.remove());
      
      // Clear timeline markers
      if (window.timelineManager) {
        window.timelineManager.clearAllMarkers();
      }
      
      this.showToast('All offline scenes cleared', 'success');
      console.log('✅ All offline scenes cleared successfully');
      
    } catch (error) {
      console.error('Clear all failed:', error);
      this.showToast('Failed to clear scenes', 'error');
    }
  }

  // UI Methods
  showSaveModal(totalTiles, onCancel) {
    const modal = document.createElement('div');
    modal.className = 'offline-save-modal';
    modal.innerHTML = `
      <div class="modal-overlay" style="position:fixed;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,0.7);z-index:10000;display:flex;align-items:center;justify-content:center;">
        <div class="modal-content" style="background:white;padding:24px;border-radius:12px;max-width:400px;width:90%;">
          <h3>💾 Saving Scene Offline</h3>
          <p>Downloading ${totalTiles} tiles...</p>
          <div class="progress-container" style="margin:16px 0;">
            <div class="progress-bar" style="background:#eee;height:8px;border-radius:4px;overflow:hidden;">
              <div class="progress-fill" style="background:#4CAF50;height:100%;width:0%;transition:width 0.3s;"></div>
            </div>
            <div class="progress-text" style="margin-top:8px;">0 / ${totalTiles}</div>
          </div>
          <button class="cancel-btn" style="background:#f44336;color:white;border:none;padding:8px 16px;border-radius:4px;cursor:pointer;">Cancel</button>
        </div>
      </div>
    `;
    
    modal.querySelector('.cancel-btn').onclick = onCancel;
    document.body.appendChild(modal);
    return modal;
  }

  updateProgressModal(modal, completed, total) {
    const fill = modal.querySelector('.progress-fill');
    const text = modal.querySelector('.progress-text');
    fill.style.width = (completed / total * 100) + '%';
    text.textContent = `${completed} / ${total}`;
  }

  hideSaveModal(modal) {
    if (modal && modal.parentElement) {
      modal.parentElement.removeChild(modal);
    }
  }

  updateSceneButtonUI(sceneId, type, name = '') {
    const button = document.getElementById(sceneId);
    if (!button) return;

    button.classList.remove('short-save', 'long-save', 'empty-scene');
    
    // Remove existing delete button (now in document body, not in button)
    this.removeDeleteButton(sceneId);

    // Add active indicator if not exists
    if (!button.querySelector('.active-indicator')) {
      const indicator = document.createElement('div');
      indicator.className = 'active-indicator';
      button.appendChild(indicator);
    }

    switch (type) {
      case 'short':
        button.classList.add('short-save');
        button.textContent = name;
        this.addDeleteButton(button, sceneId);
        break;
      case 'long':
        button.classList.add('long-save');
        button.textContent = name;
        this.addDeleteButton(button, sceneId);
        break;
      default:
        button.classList.add('empty-scene');
        button.textContent = sceneId.replace('scene-', 'Scene ');
    }
  }

  removeDeleteButton(sceneId) {
    // Find and remove delete button by data attribute or class
    const existingDeleteBtn = document.querySelector(`.delete-btn[data-scene-id="${sceneId}"]`);
    if (existingDeleteBtn) {
      existingDeleteBtn.remove();
    }
  }

  addDeleteButton(button, sceneId) {
    const deleteBtn = document.createElement('span');
    deleteBtn.className = 'delete-btn';
    deleteBtn.setAttribute('data-scene-id', sceneId);
    deleteBtn.innerHTML = '×';
    
    // Calculate position relative to button
    const buttonRect = button.getBoundingClientRect();
    const deleteX = buttonRect.right - 9; // 18px width / 2 = 9px from right edge
    const deleteY = buttonRect.top - 9;   // 18px height / 2 = 9px from top edge
    
    deleteBtn.style.cssText = `position:fixed;top:${deleteY}px;left:${deleteX}px;background:#f44336;color:white;border-radius:50%;width:18px;height:18px;font-size:12px;display:flex;align-items:center;justify-content:center;cursor:pointer;border:2px solid white;font-weight:bold;z-index:1001;`;
    deleteBtn.onclick = (e) => {
      e.stopPropagation();
      this.deleteScene(sceneId);
    };
    
    // Append to body instead of button
    document.body.appendChild(deleteBtn);
  }

  showToast(message, type = 'info') {
    const colors = {
      success: '#4CAF50',
      error: '#f44336',
      info: '#2196F3',
      warning: '#FF9800'
    };
    
    const toast = document.createElement('div');
    toast.style.cssText = `
      position:fixed;top:20px;right:20px;background:${colors[type]};color:white;
      padding:12px 20px;border-radius:4px;z-index:10001;transform:translateX(100%);
      transition:transform 0.3s;max-width:300px;
    `;
    toast.textContent = message;
    
    document.body.appendChild(toast);
    setTimeout(() => toast.style.transform = 'translateX(0)', 100);
    setTimeout(() => {
      toast.style.transform = 'translateX(100%)';
      setTimeout(() => document.body.removeChild(toast), 300);
    }, 3000);
  }

  // Helper methods
  async getFromStore(store, key) {
    return new Promise((resolve, reject) => {
      const request = store.get(key);
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  async getAllFromStore(store) {
    return new Promise((resolve, reject) => {
      const request = store.getAll();
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  async putInStore(store, data) {
    return new Promise((resolve, reject) => {
      const request = store.put(data);
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }
}

// Initialize global instance
window.offlineSceneManager = new OfflineSceneManager();
