<!DOCTYPE html>
<html>
<head>
  <title>MapTiler Wind + Temperature (Final Version)</title>
  <meta charset="utf-8" />
  <meta name="viewport" content="initial-scale=1,maximum-scale=1,user-scalable=no" />
  <script src="https://cdn.maptiler.com/maptiler-sdk-js/v3.2.0/maptiler-sdk.umd.min.js"></script>
  <link href="https://cdn.maptiler.com/maptiler-sdk-js/v3.2.0/maptiler-sdk.css" rel="stylesheet" />
  <script src="https://cdn.maptiler.com/maptiler-weather/v3.0.1/maptiler-weather.umd.min.js"></script>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: sans-serif;
    }

    #map {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 100%;
      background-color: #444952;
    }

    #time-info {
      position: fixed;
      width: 60vw;
      bottom: 0;
      z-index: 1;
      margin: 10px;
      text-shadow: 0px 0px 5px black;
      color: white;
      font-size: 20px;
      font-weight: 500;
      text-align: center;
      left: 0;
      right: 0;
      margin: auto;
      padding: 20px;
    }

    #time-slider {
      width: 100%;
      height: fit-content;
      left: 0;
      right: 0;
      z-index: 1;
      filter: drop-shadow(0 0 7px #000a);
      margin-top: 10px;
    }

    #pointer-data {
      z-index: 1;
      position: fixed;
      font-size: 50px;
      font-weight: 900;
      margin: 27px 0px 0px 10px;
      color: #fff;
      text-shadow: 0px 0px 10px #0007;
      white-space: pre; /* Để hiển thị ký tự xuống dòng \n */
    }

    #variable-name {
      z-index: 1;
      position: fixed;
      font-size: 20px;
      font-weight: 500;
      margin: 5px 0px 0px 10px;
      color: #fff;
      text-shadow: 0px 0px 10px #0007;
    }

    .button {
      cursor: pointer;
      width: auto;
      padding: 8px;
      border-radius: 3px;
      margin: 10px 0 0 0;
      font-size: 12px;
      text-align: center;
      color: #fff;
      background: #3174ff;
      font-family: sans-serif;
      font-weight: bold;
    }

    #color-scale-container {
      position: fixed;
      right: 30px;
      top: 50%;
      transform: translateY(-50%);
      z-index: 10;
      display: flex;
      flex-direction: column;
      align-items: center;
      background: rgba(0,0,0,0.15);
      border-radius: 10px;
      padding: 12px 8px;
      box-shadow: 0 2px 8px #0003;
    }
    #color-scale-canvas {
      display: block;
      width: 32px;
      height: 256px;
      border-radius: 8px;
      margin-bottom: 8px;
      margin-top: 8px;
    }
    .scale-label {
      color: #fff;
      font-size: 15px;
      font-weight: bold;
      text-shadow: 0 1px 4px #000a;
      margin: 0 0 2px 0;
      user-select: none;
    }
    .scale-label.min {
      margin-bottom: 4px;
    }
    .scale-label.max {
      margin-top: 4px;
    }
  </style>
</head>

<body>
  <div id="time-info">
    <span id="time-text"></span>
    <button id="play-pause-bt" class="button">Play 3600x</button>
    <button id="toggle-temp-bt" class="button">Ẩn nhiệt độ</button>
    <button id="auto-scale-bt" class="button">Cập nhật Auto-scale</button>
    <input type="range" id="time-slider" min="0" max="11" step="1">
  </div>

  <div id="variable-name">Temperature + Wind</div>
  <div id="pointer-data"></div>
  <div id="map"></div>

  <!-- Color scale UI -->
  <div id="color-scale-container">
    <canvas id="color-scale-canvas" width="32" height="256"></canvas>
    <div class="scale-label max" id="scale-max-label">40°C</div>
    <div class="scale-label min" id="scale-min-label">0°C</div>
  </div>

  <!-- Hidden palette image for color sampling -->
  <img id="palette-img" src="Color Hunt Palette 03a6a1ffe3bbffa673ff4f0f.png" style="display:none" crossorigin="anonymous" />

  <!-- Status message -->
  <div id="status-message" style="position:fixed;right:30px;bottom:30px;z-index:100;color:#fff;background:rgba(0,0,0,0.7);padding:8px 16px;border-radius:8px;font-size:16px;display:none"></div>
  
  <script>
    // --- DOM Elements ---
    const timeInfoContainer = document.getElementById("time-info");
    const timeTextDiv = document.getElementById("time-text");
    const timeSlider = document.getElementById("time-slider");
    const playPauseButton = document.getElementById("play-pause-bt");
    const toggleTempButton = document.getElementById("toggle-temp-bt");
    const pointerDataDiv = document.getElementById("pointer-data");
    const paletteImg = document.getElementById('palette-img');
    const colorScaleCanvas = document.getElementById('color-scale-canvas');
    const scaleMinLabel = document.getElementById('scale-min-label');
    const scaleMaxLabel = document.getElementById('scale-max-label');
    const autoScaleButton = document.getElementById('auto-scale-bt');

    // --- State Variables ---
    let pointerLngLat = null;
    let tempLayerVisible = true;
    let isPlaying = false;
    let mapLoaded = false;
    let paletteReady = false;
    let paletteColors = [];
    let tempMin = 0;
    let tempMax = 40;
    const NUM_COLORS = 64;
    const TEMP_LAYER_ID = 'temperature-layer';

    // --- MapTiler Initialization ---
    maptilersdk.config.apiKey = 'oinoatcrNmdCL1524DOl';

    const map = new maptilersdk.Map({
      container: document.getElementById('map'),
      hash: true,
      zoom: 2,
      center: [0, 40],
      style: maptilersdk.MapStyle.BACKDROP,
      projectionControl: true,
      projection: 'globe'
    });

    // --- Layer Initialization ---
    let layerBg; // Khai báo layer nhiệt độ, sẽ được khởi tạo sau
    
    const layer = new maptilerweather.WindLayer({
      id: "Wind Particles",
      colorramp: maptilerweather.ColorRamp.builtin.NULL,
      speed: 0.002,
      fadeFactor: 0.03,
      maxAmount: 256,
      density: 400,
      color: [255, 0, 0, 30],
      fastColor: [0, 255, 0, 100],
    });

    // --- Map and Layer Events ---
    map.on('load', function () {
      map.setPaintProperty("Water", 'fill-color', "rgba(0, 0, 0, 0.6)");
      map.addLayer(layer);

      // Thêm DEM source và hillshade layer
      map.addSource('dem', {
        type: 'raster-dem',
        url: `https://api.maptiler.com/tiles/terrain-rgb/tiles.json?key=${maptilersdk.config.apiKey}`,
        tileSize: 256,
        maxzoom: 12
      });
      try {
        map.addLayer({
          id: 'hillshade',
          type: 'hillshade',
          source: 'dem',
          layout: {},
          paint: {
            'hillshade-exaggeration': 0.5,
            'hillshade-shadow-color': '#444',
            'hillshade-highlight-color': '#fff',
            'hillshade-accent-color': '#aaa',
            'hillshade-illumination-direction': 335,
            'hillshade-illumination-anchor': 'viewport',
            'hillshade-opacity': 0.3
          }
        });
        console.log('Đã thêm hillshade layer thành công!');
      } catch (e) {
        console.error('Lỗi khi thêm hillshade layer:', e);
      }

      mapLoaded = true;
      if (paletteReady) {
        updateTemperatureColorRamp(tempMin, tempMax);
      }
    });

    // Events của WindLayer
    layer.on("sourceReady", () => {
      refreshTimeUI();
      const startDate = layer.getAnimationStartDate();
      const endDate = layer.getAnimationEndDate();
      timeSlider.min = +startDate;
      timeSlider.max = +endDate;
    });

    // --- UI Event Listeners ---
    timeSlider.addEventListener("input", () => {
      const time = parseInt(timeSlider.value);
      layer.setAnimationTime(time / 1000);
      if (layerBg) layerBg.setAnimationTime(time / 1000);
    });

    playPauseButton.addEventListener("click", () => {
      isPlaying = !isPlaying;
      if (isPlaying) {
        layer.animateByFactor(3600);
        if (layerBg) layerBg.animateByFactor(3600);
        playPauseButton.innerText = "Pause";
      } else {
        layer.animateByFactor(0);
        if (layerBg) layerBg.animateByFactor(0);
        playPauseButton.innerText = "Play 3600x";
      }
    });

    toggleTempButton.addEventListener("click", () => {
      tempLayerVisible = !tempLayerVisible;
      if (layerBg) {
        layerBg.setOpacity(tempLayerVisible ? 0.8 : 0);
        toggleTempButton.innerText = tempLayerVisible ? "Ẩn nhiệt độ" : "Hiện nhiệt độ";
      }
    });

    // Đổi text nút auto-scale
    autoScaleButton.innerText = "Cập nhật Auto-scale";
    autoScaleButton.onclick = function() {
      if (!layerBg) {
        showStatus("Chưa có dữ liệu nhiệt độ!");
        return;
      }
      // Các điểm đặc trưng: cực Bắc, xích đạo, và 3 điểm random
      const points = [
        {lng: 0, lat: 85, label: "Cực Bắc"},
        {lng: 0, lat: 0, label: "Xích đạo (0,0)"},
        {lng: 90, lat: 0, label: "Xích đạo (90,0)"},
        {lng: -90, lat: 0, label: "Xích đạo (-90,0)"},
        {lng: 180, lat: 0, label: "Xích đạo (180,0)"}
      ];
      for (let i = 0; i < 3; i++) {
        points.push({
          lng: Math.random() * 360 - 180,
          lat: Math.random() * 170 - 85,
          label: `Random${i+1}`
        });
      }
      // Lấy giá trị nhiệt độ tại các điểm
      const values = points.map(pt => {
        const v = layerBg.pickAt(pt.lng, pt.lat);
        const value = v && typeof v.value === 'number' && !isNaN(v.value) ? v.value : null;
        console.log(`Điểm ${pt.label || ""} (${pt.lng.toFixed(2)}, ${pt.lat.toFixed(2)}):`, value);
        return { ...pt, value };
      }).filter(obj => obj.value !== null);

      if (values.length >= 2) {
        // Tìm min/max và log
        let minObj = values[0], maxObj = values[0];
        for (const obj of values) {
          if (obj.value < minObj.value) minObj = obj;
          if (obj.value > maxObj.value) maxObj = obj;
        }
        console.log(`Min: ${minObj.value}°C tại điểm ${minObj.label || ""} (${minObj.lng.toFixed(2)}, ${minObj.lat.toFixed(2)})`);
        console.log(`Max: ${maxObj.value}°C tại điểm ${maxObj.label || ""} (${maxObj.lng.toFixed(2)}, ${maxObj.lat.toFixed(2)})`);
        tempMin = minObj.value;
        tempMax = maxObj.value;
        console.log(`Cập nhật colorramp với range: ${tempMin} - ${tempMax}`);
        updateTemperatureColorRamp(tempMin, tempMax);
        showStatus(`Auto-scale: Nhiệt độ từ ${Math.round(tempMin)}°C đến ${Math.round(tempMax)}°C`);
      } else {
        showStatus("Không lấy được dữ liệu nhiệt độ, thử lại sau!");
      }
    };

    map.on('mousemove', (e) => updatePointerValue(e.lngLat));
    timeInfoContainer.addEventListener("mouseenter", () => pointerDataDiv.innerText = "");
    
    // --- Palette Loading ---
    paletteImg.onload = function() {
      paletteColors = extractPaletteColors(paletteImg, NUM_COLORS);
      paletteReady = true;
      if (mapLoaded) updateTemperatureColorRamp(tempMin, tempMax);
    };
    if (paletteImg.complete) paletteImg.onload();
    
    // --- Helper Functions ---
    function refreshTimeUI() {
      const d = layer.getAnimationTimeDate();
      if (d) {
        timeTextDiv.innerText = d.toString();
        timeSlider.value = +d;
      }
      updatePointerValue(pointerLngLat);
    }

    function updatePointerValue(lngLat) {
      if (!lngLat || !layerBg) return;
      pointerLngLat = lngLat;
      const valueWind = layer.pickAt(lngLat.lng, lngLat.lat);
      const valueTemp = layerBg.pickAt(lngLat.lng, lngLat.lat);

      if (!valueWind || !valueTemp || valueTemp.value == null) {
        pointerDataDiv.innerText = "";
        return;
      }
      pointerDataDiv.innerText = `${valueTemp.value.toFixed(1)}°C\n${valueWind.speedKilometersPerHour.toFixed(1)} km/h`;
    }

    function extractPaletteColors(img, numColors) {
      //... (không đổi)
      const c = document.createElement('canvas');
      c.width = 1; c.height = img.height;
      const ctx = c.getContext('2d', { willReadFrequently: true });
      ctx.drawImage(img, 0, 0, 1, img.height);
      const colors = [];
      for (let i = 0; i < numColors; i++) {
        const y = Math.floor(img.height - 1 - (i * (img.height - 1) / (numColors - 1)));
        const data = ctx.getImageData(0, y, 1, 1).data;
        colors.push([data[0], data[1], data[2]]);
      }
      return colors;
    }

    function makeColorRamp(min, max, colors) {
      //... (không đổi)
      return colors.map((color, i) => ({
        value: min + (max - min) * (i / (colors.length - 1)),
        color
      }));
    }

    function drawColorScale(colors) {
      //... (không đổi)
      const ctx = colorScaleCanvas.getContext('2d');
      ctx.clearRect(0, 0, colorScaleCanvas.width, colorScaleCanvas.height);
      for (let i = 0; i < colors.length; i++) {
        ctx.fillStyle = `rgb(${colors[i][0]},${colors[i][1]},${colors[i][2]})`;
        const y = colorScaleCanvas.height - (i + 1) * (colorScaleCanvas.height / colors.length);
        const h = colorScaleCanvas.height / colors.length + 1;
        ctx.fillRect(0, y, colorScaleCanvas.width, h);
      }
    }
    
    function updateTemperatureColorRamp(min, max) {
      //... (logic tạo lại layer giữ nguyên như phiên bản đúng trước đó)
      if (!paletteColors.length || !mapLoaded) return;
      
      const ramp = makeColorRamp(min, max, paletteColors);
      const currentTime = layer.getAnimationTimeDate();

      if (map.getLayer(TEMP_LAYER_ID)) {
        map.removeLayer(TEMP_LAYER_ID);
      }

      layerBg = new maptilerweather.TemperatureLayer({
        id: TEMP_LAYER_ID,
        opacity: tempLayerVisible ? 0.8 : 0,
        colorramp: ramp,
      });

      map.addLayer(layerBg, "Water");

      if (currentTime) {
        layerBg.setAnimationTime(+currentTime / 1000);
      }
      if (isPlaying) {
        layerBg.animateByFactor(3600);
      }

      drawColorScale(paletteColors);
      scaleMinLabel.innerText = `${Math.round(min)}°C`;
      scaleMaxLabel.innerText = `${Math.round(max)}°C`;
    }

    function showStatus(msg, timeout = 2500) {
      const el = document.getElementById('status-message');
      el.innerText = msg;
      el.style.display = 'block';
      clearTimeout(el._timeout);
      el._timeout = setTimeout(() => { el.style.display = 'none'; }, timeout);
    }
  </script>
</body>
</html>