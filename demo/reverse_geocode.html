<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Reverse Geocoding</title>
  <script src="https://cdn.maptiler.com/maptiler-sdk-js/v3.2.0/maptiler-sdk.umd.min.js"></script>
  <link href="https://cdn.maptiler.com/maptiler-sdk-js/v3.2.0/maptiler-sdk.css" rel="stylesheet" />
  <style>
    body {
      margin: 0;
      padding: 0;
    }
    #map {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 100%;
    }
    #results {
      position: absolute;
      top: 0;
      left: 0;
      width: 30%;
      overflow: auto;
      background: rgba(255, 255, 255, 0.8);
    }
    #results ul {
      list-style-type: none;
      padding: 0;
      margin: 0;
    }
    #results ul li {
      padding: 10px;
      cursor: pointer;
      border-bottom: 1px solid gray;
    }
    #results ul li:hover {
      color: gray;
    }
    #results ul li:last-child{
      border-bottom-width: 0;
    }
  </style>
</head>
<body>
  <div id="map"></div>
  <div id="results">
    <ul id="search-results">
    </ul>
  </div>
  <script>
      maptilersdk.config.apiKey = 'YOUR_MAPTILER_API_KEY_HERE';
      const map = new maptilersdk.Map({
        container: 'map', // container's id or the HTML element to render the map
        style: maptilersdk.MapStyle.STREETS,
        center: [13.3975, 52.5196], // starting position [lng, lat]
        zoom: 11, // starting zoom
      });
      map.on('load', () => {
        map.addSource('search-results', {
            type: 'geojson',
            data: {
                "type": "FeatureCollection",
                "features": []
            }
        });
        map.addLayer({
            'id': 'point-result',
            'type': 'circle',
            'source': 'search-results',
            'paint': {
                'circle-radius': 8,
                'circle-color': '#B42222',
                'circle-opacity': 0.5
            },
            'filter': ['==', '$type', 'Point']
        });
      });
      map.on('click', async (e) => {
        const {lng, lat} = e.lngLat;
        const results = await maptilersdk.geocoding.reverse([lng, lat]);
        const map = e.target;
        map.getSource('search-results').setData(results);
        const bounds = results.features.reduce(function (bounds, feature) {
            return bounds.extend(feature.bbox);
        }, new maptilersdk.LngLatBounds(results.features[0].bbox));
        map.fitBounds(bounds);
        const resultList = results.features.map((feature) => {
          return `<li onClick="map.fitBounds([${feature.bbox}], {maxZoom: 19})">${feature.place_name}</li>`
        });
        document.getElementById('search-results').innerHTML = resultList.join('');
      });
  </script>
</body>
</html>