{"legend": "", "type": "baselayer", "version": "3.0", "format": "png", "format_arguments": "-f png24 -bg 1 134 160 ", "minzoom": 0, "maxzoom": 12, "scale": "2.000000", "profile": "mercator", "color": "#0186A0", "generator": "MapTiler Desktop Pro 10.3-cce17ef4a4 Merge MBTiles", "description": "The tiles contain elevation data encoded into RGB color model. Elevation in meters can be calculated as follows:\r\n\r\nheight = -10000 + ((R * 256 * 256 + G * 256 + B) * 0.1)\r\n\r\nThe viewer above shows a \"hillshading\" view of the encoded DEM. ", "attribution": "<a href=\"https://www.maptiler.com/copyright/\" target=\"_blank\">&copy; MapTiler</a> <a href=\"https://www.openstreetmap.org/copyright\" target=\"_blank\">&copy; OpenStreetMap contributors</a>", "bounds": [-180, -84.999983, 180, 84.99999999999999], "center": [8.5456, 47.37389999999999, 5], "logo": "https://api.maptiler.com/resources/logo.svg", "tilejson": "2.0.0", "name": "Terrain RGB", "crs": "EPSG:3857", "crs_wkt": "PROJCS[\"WGS 84 / Pseudo-Mercator\",GEOGCS[\"WGS 84\",<PERSON><PERSON><PERSON>[\"WGS_1984\",SPHEROID[\"WGS 84\",6378137,298.257223563,AUTHORITY[\"EPSG\",\"7030\"]],AUTH<PERSON><PERSON><PERSON>[\"EPSG\",\"6326\"]],<PERSON><PERSON><PERSON>[\"Greenwich\",0,AUTHORIT<PERSON>[\"EPSG\",\"8901\"]],UNIT[\"degree\",0.0174532925199433,AUTHORITY[\"EPSG\",\"9122\"]],AUTHORITY[\"EPSG\",\"4326\"]],PROJECTION[\"Mercator_1SP\"],PARAMETER[\"central_meridian\",0],PARAMETER[\"scale_factor\",1],PARAMETER[\"false_easting\",0],PARAMETER[\"false_northing\",0],UNIT[\"metre\",1,AUTHORITY[\"EPSG\",\"9001\"]],AXIS[\"Easting\",EAST],AXIS[\"Northing\",NORTH],<PERSON><PERSON><PERSON><PERSON><PERSON>[\"PROJ4\",\"+proj=merc +a=6378137 +b=6378137 +lat_ts=0 +lon_0=0 +x_0=0 +y_0=0 +k=1 +units=m +nadgrids=@null +wktext +no_defs\"],AUTHORITY[\"EPSG\",\"3857\"]]", "extent": [-20037508.342789244, -19971847.16723081, 20037508.342789244, 19971868.88040853], "tiles": ["https://api.maptiler.com/tiles/terrain-rgb/{z}/{x}/{y}.png?key=oinoatcrNmdCL1524DOl"]}