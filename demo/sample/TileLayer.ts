import type { CustomLayerInterface, CustomRenderMethodInput, MapProjectionEvent, Map as MapSDK } from "@maptiler/sdk";
import * as three from "three";
import { type Extent, getArea, getIntersection, getIntersectionArea, scaleFromCenter } from "ol/extent.js";
import { createXYZ } from "ol/tilegrid.js";
import type TileGrid from "ol/tilegrid/TileGrid.js";

import { name, version } from "../../../package.json";

import FRAGMENT_SHADER from "../../shaders/tilelayer/tile.fs.glsl";
import VERTEX_SHADER from "../../shaders/tilelayer/tile.vs.glsl";
import MIX_FRAGMENT_SHADER from "../../shaders/singlepassnode/mix.fs.glsl";
import BLUR_FRAGMENT_SHADER from "../../shaders/singlepassnode/blur.fs.glsl";

import { InstancePool } from "../../core/InstancePool";
import SinglePassNode from "../../core/SinglePassNode";
import type { ColoringFragmentBase, MultiChannelGradientColoringFragment } from "../../core/ColoringFragment";
import { type Tile, TileTextureSource } from "../../core/TileTextureSource";
import { type TimeFrame, TimeFrameAnimation } from "../../core/TimeFrameAnimation";
import { WeatherDataHandler } from "../../core/WeatherDataHandler";

import { constant } from "../../constant";
import { getTileCoverage, sampleImage, wgs84ToMerc, wgs84ToUnit } from "../../utils";

import { GlobeTilesService } from "../../tiles/GlobeTilesService";
import { USE_DEBUG_MANUAL_REFRESH, USE_DEBUG_OBJECTS_3D } from "../../debug";

import type { TileLayerOptions, TilePlacement, TileList, TileCoordinates } from "./types";

/**
 * Special layer that can be added to MapTiler SDK's Map.
 * Consists of multiple timeframes, every frame is a tile pyramid in a different moment in time.
 * The individual frames are smoothly animated.
 * Decoding of the data and their visual representation is configurable.
 */
class TileLayer extends TimeFrameAnimation<TileTextureSource> implements CustomLayerInterface {
  id: string;
  type: "custom" = "custom" as const;
  renderingMode = "3d" as const; // In 2d mode certain layer types can't be interlaced

  /**
   * Instance of MapTiler SDK map
   */
  protected map: MapSDK | null = null;

  protected dataMinZoom = 0;
  protected dataMaxZoom = 4;

  /**
   * Array of array of Tiles (plane geometry meshes)
   */
  protected readonly slippyTiles: Array<Array<three.Mesh>> = [];

  /**
   * Renderer to render the tiles
   */
  protected renderer: three.WebGLRenderer | null = null;

  /**
   * Camera used to render the tiles
   */
  protected readonly camera: three.Camera = new three.Camera();

  /**
   * Scene to add the tiles to
   */
  protected readonly scene: three.Scene = new three.Scene();

  /**
   * The shader material of the tiles (with uniforms such as textures, time, etc.)
   * TODO: Fix the initialisation to get rid of the `!` operator
   */
  private material!: three.RawShaderMaterial;

  /**
   * The plane geometry used for all the tiles
   */
  private geometry: three.PlaneGeometry = new three.PlaneGeometry(1, 1);

  /**
   * A TileGrid (from OL) that contains some tile logic such as mercator to index, etc.
   */
  private tilegrid!: TileGrid;

  /**
   * Extent as in OL
   */
  private extent: Extent | null = null;

  /**
   * Indicated whether the the function `map.triggerRepaint()` should be called
   * when the animation is paused.
   */
  private repaintOnPausedAnimation = true;

  /**
   * Manages a pool of rawshadermaterial to reuse, rather cloning endlessly
   */
  private materialPool: InstancePool<three.RawShaderMaterial> = new InstancePool();

  private bluringNodePasses: Array<SinglePassNode> = []; // will be instanciated when we know the tile size

  /**
   * ratio to wich the extent is enlarged when using the method `.getVisibleExtent()`
   */
  protected extentScale = 1;

  /**
   * The event `"extentChanged"` is emitted when the intersection-over-union ration from the former extent to the new
   * is below this.extentChangedThreshold
   */
  private extentChangedThreshold = 0.75;

  private lastExtent: Extent = [0, 0, 0, 0];
  private timeInterpolation = true;
  protected isReady = false;
  private defaultTexture: three.DataTexture = new three.DataTexture(
    new Uint8Array([128, 128, 128, 255]),
    1,
    1,
    three.RGBAFormat,
  );

  private onMoveEndListener: () => void;
  private onResizeListener: () => void;
  private onMoveListener: () => void;
  private onProjectionChangedListener: (event: MapProjectionEvent) => void;

  private coloringFragments: ColoringFragmentBase[] | null = null;
  private multiChannelColoringFragment!: MultiChannelGradientColoringFragment | null;
  private loadLowerZoomLevels!: boolean;
  private interpolateTileEdge = false;

  private flusher: three.Mesh;

  /**
   * Globe related changes
   */
  protected globeTilesService: GlobeTilesService;
  protected threeWorldGroup = new three.Group();
  // TODO: Create `mercatorTilesService` and change `slippyTilesGroup` to `mercatorTilesService.object3D`
  protected slippyTilesGroup = new three.Group();
  /* *** */

  /**
   * @param id Unique identifier of the layer in the map.
   * @param options
   * @param coloringFragments
   * @param multiChannelColoringFragment
   */
  constructor(
    id: string,
    options: TileLayerOptions | null,
    coloringFragments: ColoringFragmentBase[] | null = null,
    multiChannelColoringFragment: MultiChannelGradientColoringFragment | null = null,
  ) {
    super();

    this.defaultTexture.needsUpdate = true;
    this.id = id;

    if (options) {
      // TODO: Rework it to avoid the `!` operator
      // What is the point of creating material only when options are passed?
      this.init(options, coloringFragments, multiChannelColoringFragment);
    }

    this.onMoveEndListener = this.onMoveEnd.bind(this);
    this.onResizeListener = this.onResize.bind(this);
    this.onMoveListener = this.onMove.bind(this);
    this.onProjectionChangedListener = this.onProjectionChanged.bind(this);

    /**
     * HACK: MapLibre changes some WebGL states and
     * we need to "reset" those before we can do actual rendering correctly.
     */
    this.flusher = new three.Mesh(
      new three.PlaneGeometry(1, 1),
      new three.MeshBasicMaterial({
        colorWrite: false,
        depthWrite: false,
      }),
    );
    this.flusher.frustumCulled = false;
    this.scene.add(this.flusher);
    /* *** */

    this.threeWorldGroup.add(this.slippyTilesGroup);

    /**
     * Globe related changes
     */
    this.globeTilesService = new GlobeTilesService();
    this.threeWorldGroup.add(this.globeTilesService.object3D);
    /* *** */
  }

  /**
   * TODO: Add description
   */
  protected init(
    options: TileLayerOptions,
    coloringFragments: ColoringFragmentBase[] | null = null,
    multiChannelColoringFragment: MultiChannelGradientColoringFragment | null = null,
  ) {
    this.interpolateTileEdge = options.interpolateTileEdge ?? false;
    this.loadLowerZoomLevels = options.loadLowerZoomLevels ?? true;
    this.coloringFragments = coloringFragments;
    this.multiChannelColoringFragment = multiChannelColoringFragment;

    this.repaintOnPausedAnimation = options.repaintOnPausedAnimation ?? true;

    this.dataMinZoom = options.minZoom ?? this.dataMinZoom;
    this.dataMaxZoom = options.maxZoom ?? this.dataMaxZoom;

    this.tilegrid = createXYZ({ minZoom: this.dataMinZoom, maxZoom: this.dataMaxZoom });

    if (options.bounds) {
      this.extent = <Extent>(
        wgs84ToMerc(options.bounds[0], options.bounds[1]).concat(wgs84ToMerc(options.bounds[2], options.bounds[3]))
      );
    }

    let fragmentShader = "";

    if (this.coloringFragments) {
      const fragments = this.coloringFragments.map((cf, i) => cf.getBlendCode(`_cf_${i}`));
      let fragmentChain = "fragColor";
      for (let i = 0; i < this.coloringFragments.length; i++) {
        fragmentChain = `_cf_${i}(${fragmentChain},inter)`;
      }
      fragmentChain = `fragColor=${fragmentChain};`;

      fragmentShader = FRAGMENT_SHADER.replace("COLORING_FRAGMENT_DEFS", fragments.join("\n")).replace(
        "COLORING_FRAGMENT_CALLS",
        fragmentChain,
      );
    } else if (multiChannelColoringFragment) {
      fragmentShader = multiChannelColoringFragment.getCode();
    }

    const nbSmoothingBins = (options.nbSmoothingBins ?? 16).toFixed(0);
    this.timeInterpolation = options.timeInterpolation ?? true;

    this.material = new three.RawShaderMaterial({
      glslVersion: three.GLSL3,
      uniforms: {
        opacity: { value: 1 },
        time: { value: 0 },
        zoom: { value: 0 },
        tex0xy: { value: [0, 0] },
        tex1xy: { value: [0, 0] },
        tex0size: { value: 1 },
        tex1size: { value: 1 },
        tilePixelSize: { value: 1 },
        categorySmoothTransition: {
          value: options.categorySmoothTransition ?? false,
        },
        timeInterpolation: { value: this.timeInterpolation },
        tex0: {
          value: null,
        },
        tex1: {
          value: null,
        },
        zoomDelta: { value: 0 },
        localSmoothing: { value: options.localSmoothing ?? false },
        maxSmoothingDistance: { value: options.maxSmoothingDistance ?? 12 },
        smoothingDistanceDecayFactor: {
          value: options.smoothingDistanceDecayFactor ?? 10,
        },

        // Tile texture around the current one.
        // Used when smoothing is enabled
        texN0: { value: this.defaultTexture },
        texN1: { value: this.defaultTexture },
        availableN: { value: false },
        texE0: { value: this.defaultTexture },
        texE1: { value: this.defaultTexture },
        availableE: { value: false },
        texS0: { value: this.defaultTexture },
        texS1: { value: this.defaultTexture },
        availableS: { value: false },
        texW0: { value: this.defaultTexture },
        texW1: { value: this.defaultTexture },
        availableW: { value: false },
        interpolateTileEdge: { value: this.interpolateTileEdge },
      },
      vertexShader: VERTEX_SHADER,
      fragmentShader: fragmentShader,
      depthTest: false,
      depthWrite: false,
      transparent: true,
      side: three.FrontSide,
      defines: {
        RENDER_TRANSPARENT: options.renderTransparentArea ? "true" : "false",
        NB_SMOOTHING_BINS: nbSmoothingBins,
      },
    });

    this.scene.add(this.threeWorldGroup);
    this.updateSlippyTileGrid(8, 8);

    /**
     * Restart the rendering animation.
     * Will have no effect if this.repaintOnPausedAnimation is true.
     */
    this.on("playAnimation", () => {
      this.forceRepaint();
    });

    this.on("animationTimeSet", () => {
      this.forceRepaint();
    });

    this.isReady = true;
  }

  /**
   * @description Event handler - callback when the window is resized.
   */
  protected onResize() {
    if (!this.isReady) {
      return;
    }

    const { width, height } = this.getMapOrThrow().getCanvas();

    this.getRendererOrThrow().setViewport(0, 0, width, height);
    this.updateSlippyTileGrid(Math.ceil(width / 512) + 2, Math.ceil(height / 512) + 2);
  }

  /**
   * @description Event handler - callback when the map is moved.
   */
  protected onMove() {
    if (!this.isReady) {
      return;
    }

    const newExtent = this.getVisibleExtent(this.extentScale);

    if (!newExtent) throw new Error("New extent is null");

    const unionExtent = <Extent>[
      Math.min(newExtent[0], this.lastExtent[0]),
      Math.min(newExtent[1], this.lastExtent[1]),
      Math.max(newExtent[2], this.lastExtent[2]),
      Math.max(newExtent[3], this.lastExtent[3]),
    ];

    const unionExtentArea = getArea(unionExtent);
    const intersectionArea = getIntersectionArea(this.lastExtent, newExtent);
    const intersectionOverUnion = intersectionArea / unionExtentArea;

    if (intersectionOverUnion <= this.extentChangedThreshold) {
      this.lastExtent = newExtent;
      this.emit("extentChanged");
    }
  }

  /**
   * @description Event handler - called when the map movement ends.
   */
  protected onMoveEnd() {
    this.refresh();
  }

  /**
   * @description Event handler - called when projection is changed
   */
  public onProjectionChanged(event: MapProjectionEvent) {
    this.globeTilesService.object3D.visible = false;
    this.slippyTilesGroup.visible = false;

    if (USE_DEBUG_MANUAL_REFRESH === false) {
      this.onResize();
      this.refresh();
    }

    if (event.newProjection === "globe") {
      this.globeTilesService.object3D.visible = true;
    } else {
      this.slippyTilesGroup.visible = true;
    }
  }
  /* *** */

  /**
   * Remove the current tiles of the tile grid from the scene and reinstanciate them all
   * (three.Mesh) based onthe arguments, then add them to the scene again.
   * Note: The tils positions are not updated yet, this is performed by `.updateSlippyTile()`
   * @param width
   * @param height
   */
  private updateSlippyTileGrid(width: number, height: number) {
    this.slippyTilesGroup.clear();
    this.slippyTiles.length = 0;

    for (let x = 0; x < width; x++) {
      this.slippyTiles[x] = [];
      for (let y = 0; y < height; y++) {
        const tileMesh = new three.Mesh(this.geometry);
        tileMesh.frustumCulled = false;

        this.slippyTiles[x][y] = tileMesh;
        this.slippyTilesGroup.add(tileMesh);
      }
    }
  }

  /**
   * TODO: Add description
   */
  private validateSource(sourceUrl: string) {
    let urlObj: URL;

    // if the URL fails to build then it means it was provided as a local URL
    try {
      urlObj = new URL(sourceUrl);
    } catch (e) {
      urlObj = new URL(sourceUrl, document.baseURI);
    }

    if (urlObj.host !== constant.maptilerApiHost) {
      throw new Error(
        "\n\nMapTiler Weather library can only source data from MapTiler Cloud.\nPlease use our MapTiler Plus library for self hosting: https://www.maptiler.com/weather/\nor host your data with our generous free plan at MapTiler Cloud: https://cloud.maptiler.com\n",
      );
    }
  }

  /**
   * Adds another frame to the animation.
   * @param time Time of the data in this frame. Should be unique.
   * @param url URL to the tiles. Expected to have `{zxy}` placeholder to be dynamically replaced with `z/x/y` coordinates.
   */
  public addSource(
    time: number,
    url: string,
    loadedCallback: ((tile: Tile, url?: string, error?: ErrorEvent | null) => void) | null = null,
  ) {
    // If the source added is not on api.maptiler.com then an error is thrown.
    // Note: this limitation is lifted in Weather Plus.
    this.validateSource(url);

    const tts = new TileTextureSource(url, (tile: Tile, url?: string, error?: ErrorEvent | null) => {
      if (loadedCallback) {
        loadedCallback(tile, url, error);
      }
      this.forceRepaint();
    });

    // Only if the layer was already attached to the map (otherwise done later on onAdd)
    if (this.map) {
      const apiKey = this.map.getSdkConfig().apiKey;
      const sessionId = this.map.getMaptilerSessionId();
      tts.setMaptilerParams(apiKey, sessionId);
    }

    super.addFrame(time, tts);
  }

  /**
   * Removes frame
   */
  public removeSource(time: number) {
    const removed = super.removeFrame(time);
    for (const frame of removed) {
      frame.data.dispose();
    }

    this.forceRepaint();
  }

  /**
   * Get the Extent as defined in OL
   * @param scale
   * @returns
   */
  public getVisibleExtent(scale?: number): Extent | null {
    if (!this.isReady) {
      return null;
    }

    const bounds = this.getMapOrThrow().getBounds();

    let extent = <Extent>(
      wgs84ToMerc(bounds.getWest(), bounds.getSouth()).concat(wgs84ToMerc(bounds.getEast(), bounds.getNorth()))
    );

    if (this.extent) {
      extent = getIntersection(this.extent, extent);
    }

    if (scale) {
      scaleFromCenter(extent, scale);
    }

    return extent;
  }

  /**
   * Get the list of all the tiles wanted for this extent and from the min zoom to the max zoom.
   * This is used to prevent updating tiles that are not in this list in `.updateSlippyTile()`
   * @param currentZ
   * @returns {TileList}
   */
  public getWantedTiles(currentZ: number, minZoom: number, maxZoom: number): TileList {
    const ceiledCurrentZ = Math.ceil(currentZ);
    const wantedTiles: TileList = {};
    const bounds = this.getMapOrThrow().getBounds();
    const lowerZ = Math.min(maxZoom, Math.max(minZoom, ceiledCurrentZ - 1));
    const upperZ = Math.min(maxZoom, Math.max(minZoom, ceiledCurrentZ));

    for (let z = lowerZ; z <= upperZ; ++z) {
      const tilesForZ = getTileCoverage(bounds, z);

      for (const tileID of tilesForZ) {
        wantedTiles[tileID] = true;
      }
    }

    return wantedTiles;
  }

  /**
   * Changes the global opacity of the layer.
   * Default is `1`
   *
   * @param opacity Opacity 0-1.
   */
  public setOpacity(opacity: number) {
    if (!this.isReady) {
      return;
    }

    this.material.uniforms.opacity.value = opacity;
    this.forceRepaint();
  }

  /**
   * Method from CustomLayerInterface, called when the layer is added to the map
   * @param map
   * @param gl
   */
  public onAdd(map: MapSDK, gl: WebGLRenderingContext | WebGL2RenderingContext) {
    this.registerTelemetry(map);

    this.map = map as MapSDK;
    this.map.on("resize", this.onResizeListener);
    this.map.on("projectiontransition", this.onProjectionChangedListener);

    if (USE_DEBUG_MANUAL_REFRESH === true) {
      window.addEventListener("keyup", () => {
        this.onResize();
        this.onMove();
        this.onMoveEnd();

        setTimeout(() => {
          this.updateTilesMaterials();
        }, 1000);
      });
    } else {
      this.map.on("move", this.onMoveListener);
      this.map.on("moveend", this.onMoveEndListener);
    }

    const apiKey = this.map.getSdkConfig().apiKey;
    const sessionId = this.map.getMaptilerSessionId();
    this.forEachFrame((frame) => {
      frame.data.setMaptilerParams(apiKey, sessionId);
    });

    if (!WeatherDataHandler.hasData()) {
      // This is to ping the Weather/latest.json endpoint in order to start a session.
      // For billing reasons, this must be done even if the layer is using custom non-weather tileset
      WeatherDataHandler.fetchLatest(apiKey, sessionId);
    }

    this.renderer = new three.WebGLRenderer({
      canvas: map.getCanvas(),
      context: gl,
      depth: false,
      stencil: false,
      // alpha: true,
      antialias: false,
      powerPreference: "high-performance",
    });
    this.renderer.autoClear = false;
    this.renderer.compile(this.scene, this.camera);

    /**
     * Globe related changes
     */
    this.globeTilesService.init({ map: this.map, minZoom: 1, maxZoom: this.map.getMaxZoom() });
    /* *** */

    // init that is necessary for the emitting the 'extentChanged' event
    const lastExtent = this.getVisibleExtent(this.extentScale);

    if (lastExtent === null) {
      throw new Error("The last extent is null");
    }

    this.lastExtent = lastExtent;
    this.forceRepaint();

    if (USE_DEBUG_OBJECTS_3D === true) {
      this.getMapOrThrow().showTileBoundaries = true;
      this.threeWorldGroup.add(this.globeTilesService.helpersObject3D);
    }

    this.onProjectionChanged({
      type: "projectiontransition",
      newProjection: map.isGlobeProjection() ? "globe" : "mercator",
    });
  }

  /**
   * Method which should be override by the child class
   * for additional logic which has to be done:
   * - on adding the layer to the map
   * - on mouseEnd event
   */
  protected refresh() {
    if (this.getMapOrThrow().isGlobeProjection()) {
      this.globeTilesService.update();
      this.globeTilesService.recreateGlobeMeshes();
    }
  }

  /**
   * The method is called when the layer is removed from the map
   * `CustomLayerInterface` method
   */
  public onRemove(_map: MapSDK, _gl: WebGLRenderingContext | WebGL2RenderingContext) {
    if (!this.isReady) {
      return;
    }

    if (this.map != null) {
      this.map.off("resize", this.onResizeListener);
      this.map.off("projectiontransition", this.onProjectionChangedListener);
      this.map.off("move", this.onMoveListener);
      this.map.off("moveend", this.onMoveEndListener);
      this.map = null;
    }

    if (this.renderer != null) {
      this.renderer.dispose();
      this.renderer = null;
    }

    this.forEachFrame((frame) => {
      frame.data.dispose();
    });

    this.disposeObjects();
  }

  /**
   * Remove some data allocated (not of the tiles)
   */
  protected disposeObjects() {
    if (!this.isReady) {
      return;
    }

    this.geometry.dispose();
    this.material.dispose();

    for (const bnp of this.bluringNodePasses) {
      bnp.dispose();
    }
  }

  /**
   * Get the TilePlacement from a zxy
   * @param source
   * @param load
   * @param z
   * @param x
   * @param y
   * @param originalTileCoordinates
   * @returns
   */
  private getTilePlacement(
    source: TileTextureSource,
    load: boolean,
    z: number,
    x: number,
    y: number,
    originalTileCoordinates?: TileCoordinates,
  ): TilePlacement | null {
    if (!this.isReady) {
      return null;
    }

    const tileID = `${z}/${x}/${y}`;
    const tile = z !== 0 && z >= this.dataMaxZoom ? null : source.getTile(tileID, load);

    if (!tile || !tile.ready) {
      if (this.loadLowerZoomLevels && z > this.tilegrid.getMinZoom()) {
        return this.getTilePlacement(
          source,
          load,
          z - 1,
          Math.floor(x / 2),
          Math.floor(y / 2),
          originalTileCoordinates || { z, x, y },
        );
      }

      return null;
    }

    const placement: TilePlacement = {
      tile: tile,
      xy: [0, 0],
      size: 1,
    };

    if (originalTileCoordinates !== undefined) {
      const sizeDiff = 2 ** (originalTileCoordinates.z - z);
      placement.size /= sizeDiff;
      placement.xy[0] = (originalTileCoordinates.x % sizeDiff) / sizeDiff;
      placement.xy[1] = ((2 ** originalTileCoordinates.z - originalTileCoordinates.y - 1) % sizeDiff) / sizeDiff;
    }

    return placement;
  }

  /**
   * WARNING: Globe projection only
   *
   * Update the tiles textures
   * TODO: Make mercator mode also use this method instead of `updateSlippyTiles`
   */
  private updateTilesMaterials() {
    const map = this.getMapOrThrow();

    if (map.isGlobeProjection() === false) {
      console.warn("For mercator projection use legacy waether tiles flow");
      return;
    }

    const zoom = map.getZoom();
    const minZoom = this.tilegrid.getMinZoom();
    const maxZoom = this.tilegrid.getMaxZoom();
    const z = Math.min(maxZoom, Math.max(minZoom, Math.floor(zoom)));
    const zoomDelta = zoom - z;

    const { frameA, frameB, mix } = this.getCurrentFrames();

    if (!frameA || !frameB) {
      this.scene.visible = false;
      return;
    }

    this.scene.visible = true;

    // TODO: This has to be understood - removing this makes the app crash after a while
    this.materialPool.init();

    for (const [tileId, tileMesh] of this.globeTilesService.currentTilesMeshes) {
      const tilesPair = this.getTilesPair(frameA, frameB, tileId);

      if (tilesPair === null) {
        tileMesh.visible = false;
        continue;
      }

      tileMesh.visible = true;

      const { tileA, tileB } = tilesPair;

      const material = <three.RawShaderMaterial>(
        (this.materialPool.isEmpty() ? this.materialPool.add(this.material.clone()) : this.materialPool.pop())
      );

      tileMesh.material = material;

      material.uniforms.localSmoothing.value = this.material.uniforms.localSmoothing.value;
      material.uniforms.maxSmoothingDistance.value = this.material.uniforms.maxSmoothingDistance.value;
      material.uniforms.smoothingDistanceDecayFactor.value = this.material.uniforms.smoothingDistanceDecayFactor.value;

      material.uniforms.timeInterpolation.value = this.material.uniforms.timeInterpolation.value;
      material.uniforms.categorySmoothTransition.value = this.material.uniforms.categorySmoothTransition.value;
      material.uniforms.opacity.value = this.material.uniforms.opacity.value;
      material.uniforms.tex0.value = tileA.tile.texture;
      material.uniforms.tex0xy.value = tileA.xy;
      material.uniforms.tex0size.value = tileA.size;
      material.uniforms.tex1.value = tileB.tile.texture;
      material.uniforms.tex1xy.value = tileB.xy;
      material.uniforms.tex1size.value = tileB.size;
      material.uniforms.tilePixelSize.value = tileA.tile.texture?.image.width;
      material.uniforms.time.value = mix;
      material.uniforms.zoomDelta.value = zoomDelta;
      material.uniforms.zoom.value = zoom;
    }
  }

  /**
   * WARNING: Mercator projection only
   *
   * Update the tile's mesh position and size, as well each tile material uniforms (time, texture, etc.)
   *
   * @returns {void}
   */
  protected updateSlippyTiles() {
    if (!this.isReady) {
      return;
    }

    const { frameA, frameB, mix } = this.getCurrentFrames();
    if (!frameA || !frameB) {
      this.scene.visible = false;
      return;
    }

    this.scene.visible = true;

    const sourceA = frameA.data;
    const sourceB = frameB.data;

    const zoom = this.getMapOrThrow().getZoom();
    const minZoom = this.tilegrid.getMinZoom();
    const maxZoom = this.tilegrid.getMaxZoom();
    const wantedTiles = this.getWantedTiles(zoom, minZoom, maxZoom);

    const usedTiles: TileList = {};
    const z = Math.min(maxZoom, Math.max(minZoom, Math.floor(zoom)));
    const zSize = 2 ** z;
    const scale = 1 / zSize;

    const center = this.getMapOrThrow().getCenter();
    const centerCoord = this.tilegrid.getTileCoordForCoordAndZ(wgs84ToMerc(center.lng, center.lat), z);

    // The zoomDelta is the difference between the actual zoom level of the map (eg. 5.43)
    // and the zoom level of the tile being used (eg. 5).
    // Note that the tile zoom is always inferior to the view zoom so zoomDelta is always positive
    const zoomDelta = zoom - z;
    const gridWidth = this.slippyTiles.length; // depends on the screen size
    this.materialPool.init();

    /**
     * Simple type definition to capture the object representing an active tile
     */
    type ActiveTileRecord = {
      material: three.RawShaderMaterial;
      tileA: TilePlacement;
      tileB: TilePlacement;
      x: number;
      y: number;
      z: number;
    };

    // This is a Map in the sens of a key-value container (not a MapLibre Map)
    const activeTiles = new Map<string, ActiveTileRecord>();

    // Grid size does not depend on the zoom level
    for (let _x = 0; _x < gridWidth; _x++) {
      const gridHeight = this.slippyTiles[0].length;
      for (let _y = 0; _y < gridHeight; _y++) {
        const tile = this.slippyTiles[_x][_y];

        const x = centerCoord[1] + _x - Math.floor(gridWidth / 2);
        const y = centerCoord[2] + _y - Math.floor(gridHeight / 2);

        const tileID = `${z}/${x}/${y}`;

        if (y < 0 || y >= zSize || !wantedTiles[tileID]) {
          tile.visible = false;
          continue;
        }

        tile.visible = true;

        const wrappedX = ((x % zSize) + zSize) % zSize;
        const wrappedTileID = `${z}/${wrappedX}/${y}`;

        usedTiles[wrappedTileID] = true;

        const y_ = zSize - y - 1;
        tile.scale.x = scale;
        tile.scale.y = scale;
        tile.position.x = scale * (x + 0.5);
        tile.position.y = scale * (y_ + 0.5) - 1;

        let tileA = this.getTilePlacement(sourceA, true, z, wrappedX, y);
        let tileB = this.getTilePlacement(sourceB, true, z, wrappedX, y);

        // Faking it is better than nothing I guess:
        if (tileA && !tileB) {
          tileB = tileA;
        }

        if (!tileA && tileB) {
          tileA = tileB;
        }

        if (!tileA || !tileB) {
          let prevFrame: TimeFrame<TileTextureSource> | null = frameA;
          while (!tileA) {
            prevFrame = this.getNextFrame(prevFrame, -1);
            if (!prevFrame) break;
            // last resort -- load tile from prev frame
            tileA = tileB = this.getTilePlacement(prevFrame.data, false, z, wrappedX, y);
          }

          if (!tileA || !tileB) {
            tile.visible = false;
            continue;
          }
        }

        const material = <three.RawShaderMaterial>(
          (this.materialPool.isEmpty() ? this.materialPool.add(this.material.clone()) : this.materialPool.pop())
        );

        material.uniforms.localSmoothing.value = this.material.uniforms.localSmoothing.value;
        material.uniforms.maxSmoothingDistance.value = this.material.uniforms.maxSmoothingDistance.value;
        material.uniforms.smoothingDistanceDecayFactor.value =
          this.material.uniforms.smoothingDistanceDecayFactor.value;

        material.uniforms.timeInterpolation.value = this.material.uniforms.timeInterpolation.value;
        material.uniforms.categorySmoothTransition.value = this.material.uniforms.categorySmoothTransition.value;
        material.uniforms.opacity.value = this.material.uniforms.opacity.value;
        material.uniforms.tex0.value = tileA.tile.texture;
        material.uniforms.tex0xy.value = tileA.xy;
        material.uniforms.tex0size.value = tileA.size;
        material.uniforms.tex1.value = tileB.tile.texture;
        material.uniforms.tex1xy.value = tileB.xy;
        material.uniforms.tex1size.value = tileB.size;
        material.uniforms.tilePixelSize.value = tileA.tile.texture?.image.width;
        material.uniforms.time.value = mix;
        material.uniforms.zoomDelta.value = zoomDelta;
        material.uniforms.zoom.value = zoom;

        tile.material = material;

        activeTiles.set(`${_x} ${_y}`, {
          material,
          tileA,
          tileB,
          x: _x,
          y: _y,
          z,
        });
      }
    }

    if (this.material.uniforms.localSmoothing.value || this.interpolateTileEdge) {
      for (const activeTile of activeTiles) {
        const value = activeTile[1];
        // Current coordinates in the map
        const x = value.x;
        const y = value.y;
        const material = value.material;

        // coordinates (keys) of neighbors in the map
        const N = { x: x, y: y - 1 };
        const E = { x: x + 1, y: y };
        const S = { x: x, y: y + 1 };
        const W = { x: x - 1, y: y };

        const keyN = `${N.x} ${N.y}`;
        const keyE = `${E.x} ${E.y}`;
        const keyS = `${S.x} ${S.y}`;
        const keyW = `${W.x} ${W.y}`;

        // setting up the neighborhood textures
        let tmpTileData = activeTiles.get(keyN);
        if (tmpTileData?.tileA?.tile?.ready && tmpTileData?.tileB?.tile?.ready) {
          material.uniforms.texN0.value = tmpTileData.tileA.tile.texture;
          material.uniforms.texN1.value = tmpTileData.tileB.tile.texture;
          material.uniforms.availableN.value = true;
        } else {
          material.uniforms.texN0.value = null;
          material.uniforms.texN1.value = null;
          material.uniforms.availableN.value = false;
        }

        tmpTileData = activeTiles.get(keyE);
        if (tmpTileData?.tileA?.tile?.ready && tmpTileData?.tileB?.tile?.ready) {
          material.uniforms.texE0.value = tmpTileData.tileA.tile.texture;
          material.uniforms.texE1.value = tmpTileData.tileB.tile.texture;
          material.uniforms.availableE.value = true;
        } else {
          material.uniforms.texE0.value = null;
          material.uniforms.texE1.value = null;
          material.uniforms.availableE.value = false;
        }

        tmpTileData = activeTiles.get(keyS);
        if (tmpTileData?.tileA?.tile?.ready && tmpTileData?.tileB?.tile?.ready) {
          material.uniforms.texS0.value = tmpTileData.tileA.tile.texture;
          material.uniforms.texS1.value = tmpTileData.tileB.tile.texture;
          material.uniforms.availableS.value = true;
        } else {
          material.uniforms.texS0.value = null;
          material.uniforms.texS1.value = null;
          material.uniforms.availableS.value = false;
        }

        tmpTileData = activeTiles.get(keyW);
        if (tmpTileData?.tileA?.tile?.ready && tmpTileData?.tileB?.tile?.ready) {
          material.uniforms.texW0.value = tmpTileData.tileA.tile.texture;
          material.uniforms.texW1.value = tmpTileData.tileB.tile.texture;
          material.uniforms.availableW.value = true;
        } else {
          material.uniforms.texW0.value = null;
          material.uniforms.texW1.value = null;
          material.uniforms.availableW.value = false;
        }
      }
    }

    setTimeout(() => {
      sourceA.expireCache(usedTiles);
      sourceB.expireCache(usedTiles);

      const sourceC = this.getNextFrame(frameB, 1);
      if (sourceC?.data) {
        const useTilesZXY = Object.keys(usedTiles);
        for (const zxy of useTilesZXY) {
          sourceC.data.getTile(zxy, true);
        }
      }
    }, 0);
  }

  /**
   * TODO: Add description
   */
  protected getTilesPair(frameA: TimeFrame<TileTextureSource>, frameB: TimeFrame<TileTextureSource>, tileId: string) {
    const sourceA = frameA.data;
    const sourceB = frameB.data;

    const zxy = tileId.split("/").map((s) => Number.parseInt(s));
    const z = zxy[0];
    const x = zxy[1];
    const y = zxy[2];

    let tileA = this.getTilePlacement(sourceA, true, z, x, y);
    let tileB = this.getTilePlacement(sourceB, true, z, x, y);

    if (tileA === null) {
      let prevFrame: TimeFrame<TileTextureSource> | null = frameA;

      while (tileA === null) {
        prevFrame = this.getNextFrame(prevFrame, -1);

        if (prevFrame === null) {
          break;
        }

        tileA = this.getTilePlacement(prevFrame.data, false, z, x, y);
      }
    }

    if (tileB === null) {
      tileB = tileA;
    }

    if (tileA === null || tileB === null) {
      return null;
    }

    return {
      tileA,
      tileB,
    };
  }

  /**
   * `CustomLayerInterface` method
   * This is used to apply the map matrix to the local camera
   */
  public prerender(_gl: WebGLRenderingContext | WebGL2RenderingContext, options: CustomRenderMethodInput) {
    if (!this.isReady) {
      return;
    }

    this.animationTick();

    const m = new three.Matrix4().fromArray(options.defaultProjectionData.mainMatrix);

    if (this.getMapOrThrow().isGlobeProjection()) {
      this.camera.projectionMatrix = m;

      if (USE_DEBUG_MANUAL_REFRESH === false) {
        this.updateTilesMaterials();
      }
    } else {
      const l = new three.Matrix4().scale(new three.Vector3(1, -1, 1));
      this.camera.projectionMatrix = m.multiply(l);
      this.updateSlippyTiles();
    }

    this.prerenderInternal();
  }

  /**
   * The reason for this method is to allow child classes to do some work during the prerender phase
   */
  protected prerenderInternal() {}

  /**
   * `CustomLayerInterface` method
   * It is used to render the local tiles into the MapTiler SDK context
   */
  public render(_gl: WebGLRenderingContext | WebGL2RenderingContext, _options: CustomRenderMethodInput) {
    if (!this.isReady) {
      return;
    }

    const renderer = this.getRendererOrThrow();
    renderer.state.reset();
    this.renderInternal();
    renderer.render(this.scene, this.camera);

    if (this.getAnimationSpeed() > 0 || this.repaintOnPausedAnimation) {
      this.forceRepaint();
    }
  }

  /**
   * The reason for this method is to allow child classes to do some work during the render phase
   * (after renderer state reset and before the actual rendering)
   */
  protected renderInternal() {}

  /**
   * Get layer image decoded value (decoded means on the real world interval such as provided [decode.min, decode.max])
   * @param lng
   * @param lat
   * @param source
   * @returns
   */
  private pickFrame(
    lng: number,
    lat: number,
    source: TileTextureSource,
    options: { load?: boolean; highestRes?: boolean } = {},
  ): number[] | null {
    if (!this.isReady) {
      return null;
    }

    const coordinate = wgs84ToUnit(lng, lat);
    if (!this.map) return null;

    const load = options.load ?? false;
    const highestRes = options.highestRes ?? false;

    const zoom = highestRes ? this.tilegrid.getMaxZoom() : Math.min(~~this.map?.getZoom(), this.tilegrid.getMaxZoom());
    const zSize = 2 ** zoom;
    const tileCoordX = ((0.5 + coordinate[0]) % 1) * zSize;
    const tileCoordY = (1 - (coordinate[1] + 0.5)) * zSize;

    const placement = this.getTilePlacement(source, load, zoom, Math.floor(tileCoordX), Math.floor(tileCoordY));

    if (placement == null || placement.tile.texture == null) {
      return null;
    }

    if (this.coloringFragments) {
      // When using a standard coloring fragment, we use a canvas for picking because it's easier to
      // cache contexts
      const pixel = sampleImage(
        placement.tile.texture.image,
        placement.xy[0] + placement.size * (tileCoordX % 1),
        1 - placement.xy[1] - placement.size + placement.size * (tileCoordY % 1),
      );

      return this.coloringFragments.flatMap((fragment) => {
        return fragment.decodeChannel(pixel);
      });
    }

    if (this.multiChannelColoringFragment) {
      // When using a multi channel fragment, we use webgl to pick the texture value
      // because alpha channel could be leveraged and webgl does not mess with alpha premultiplication
      const pixel = sampleImage(
        placement.tile.texture.image,
        placement.xy[0] + placement.size * (tileCoordX % 1),
        1 - placement.xy[1] - placement.size + placement.size * (tileCoordY % 1),
      );

      return this.multiChannelColoringFragment.decodeChannel(pixel);
    }

    return null;
  }

  /**
   * TODO: Add description
   */
  private pickFrameBilinear(
    lng: number,
    lat: number,
    source: TileTextureSource,
    options: { load?: boolean; highestRes?: boolean } = {},
  ): number[] | null {
    const load = options.load ?? false;
    const highestRes = options.highestRes ?? false;

    //  Note for maintainer:
    //  In the context of bilinear sampling, we need to considere pixel to be points without area.
    //  The point to measure the value of (noted "X") will most likely not be located at the exact location
    //  of a pixel and will instead be somewhere in between.
    //  The bilinear interpolation consists in:
    //  - finding the location of surrounding grid-aligned actual pixel (NW, NE, SE and SW)
    //  - Retrieve their value with independant calls because they could lay on a maximum of 4 different tiles
    //  - compute the value of the color at location W with a 1D linear interpolation between NW and SW
    //  - compute the value of the color at location E with a 1D linear interpolation between NE and SE
    //  - compute the value of the color at location X with a 1D linear interpolation between W and E
    //
    //  NW---------------NE
    //  |                 |
    //  |                 |
    //  |                 |
    //  |                 |
    //  W- - - - - -X- - -E
    //  |                 |
    //  SW---------------SE

    const coordinate = wgs84ToUnit(lng, lat);

    if (!this.map) return null;

    const zoom = highestRes ? this.tilegrid.getMaxZoom() : Math.min(~~this.map?.getZoom(), this.tilegrid.getMaxZoom());

    const zSize = 2 ** zoom;
    let tileCoordX = ((0.5 + coordinate[0]) % 1) * zSize;
    let tileCoordY = (1 - (coordinate[1] + 0.5)) * zSize;

    const placement = this.getTilePlacement(source, load, zoom, Math.floor(tileCoordX), Math.floor(tileCoordY));

    if (placement == null || placement.tile.texture == null) {
      return null;
    }

    const tileWidth = placement.tile.texture.image.width;
    const unitTileStep = 1 / tileWidth;

    // Shift to center pixel because we want to emulate the bahavior of the shader
    tileCoordX -= unitTileStep / 2;
    tileCoordY -= unitTileStep / 2;

    // Computing the tile coordinates that coordinates to the surrounding pixels (center pixels)
    const tileCoordNW = {
      x: ~~(tileCoordX / unitTileStep) * unitTileStep,
      y: ~~(tileCoordY / unitTileStep) * unitTileStep,
    };

    const tileCoordNE = {
      x: tileCoordNW.x + unitTileStep,
      y: tileCoordNW.y,
    };

    const tileCoordSE = {
      x: tileCoordNW.x + unitTileStep,
      y: tileCoordNW.y + unitTileStep,
    };

    const tileCoordSW = {
      x: tileCoordNW.x,
      y: tileCoordNW.y + unitTileStep,
    };

    const placementNW = this.getTilePlacement(source, load, zoom, Math.floor(tileCoordNW.x), Math.floor(tileCoordNW.y));

    const placementNE = this.getTilePlacement(source, load, zoom, Math.floor(tileCoordNE.x), Math.floor(tileCoordNE.y));

    const placementSE = this.getTilePlacement(source, load, zoom, Math.floor(tileCoordSE.x), Math.floor(tileCoordSE.y));

    const placementSW = this.getTilePlacement(source, load, zoom, Math.floor(tileCoordSW.x), Math.floor(tileCoordSW.y));

    if (
      placementNW == null ||
      placementNW.tile.texture == null ||
      placementNE == null ||
      placementNE.tile.texture == null ||
      placementSE == null ||
      placementSE.tile.texture == null ||
      placementSW == null ||
      placementSW.tile.texture == null
    ) {
      return null;
    }

    const pixelTest = sampleImage(
      placement.tile.texture.image,
      placement.xy[0] + placement.size * (tileCoordX % 1), // x position in [0, 1]
      1 - placement.xy[1] - placement.size + placement.size * (tileCoordY % 1), // y position in [0, 1]
    );

    const pixelNW = sampleImage(
      placementNW.tile.texture.image,
      placementNW.xy[0] + placementNW.size * (tileCoordNW.x % 1), // x position in [0, 1]
      1 - placementNW.xy[1] - placementNW.size + placementNW.size * (tileCoordNW.y % 1), // y position in [0, 1]
    );

    const pixelNE = sampleImage(
      placementNE.tile.texture.image,
      placementNE.xy[0] + placementNE.size * (tileCoordNE.x % 1), // x position in [0, 1]
      1 - placementNE.xy[1] - placementNE.size + placementNE.size * (tileCoordNE.y % 1), // y position in [0, 1]
    );

    const pixelSE = sampleImage(
      placementSE.tile.texture.image,
      placementSE.xy[0] + placementSE.size * (tileCoordSE.x % 1), // x position in [0, 1]
      1 - placementSE.xy[1] - placementSE.size + placementSE.size * (tileCoordSE.y % 1), // y position in [0, 1]
    );

    const pixelSW = sampleImage(
      placementSW.tile.texture.image,
      placementSW.xy[0] + placementSW.size * (tileCoordSW.x % 1), // x position in [0, 1]
      1 - placementSW.xy[1] - placementSW.size + placementSW.size * (tileCoordSW.y % 1), // y position in [0, 1]
    );

    const distanceToN = tileCoordY - tileCoordNW.y;
    const ratioS = distanceToN / (tileCoordSW.y - tileCoordNW.y);
    const ratioN = 1 - ratioS;
    const distanceToW = tileCoordX - tileCoordNW.x;
    const ratioE = distanceToW / (tileCoordNE.x - tileCoordNW.x);
    const ratioW = 1 - ratioE;

    if (this.coloringFragments) {
      // for a standard (non multi-channel) coloring fragment,
      // it's ok to interpolate the pixel channel and then to compute the real world value
      const pixelW = {
        r: pixelNW.r * ratioN + pixelSW.r * ratioS,
        g: pixelNW.g * ratioN + pixelSW.g * ratioS,
        b: pixelNW.b * ratioN + pixelSW.b * ratioS,
        a: pixelNW.a * ratioN + pixelSW.a * ratioS,
      };

      const pixelE = {
        r: pixelNE.r * ratioN + pixelSE.r * ratioS,
        g: pixelNE.g * ratioN + pixelSE.g * ratioS,
        b: pixelNE.b * ratioN + pixelSE.b * ratioS,
        a: pixelNE.a * ratioN + pixelSE.a * ratioS,
      };

      const pixel = {
        r: pixelW.r * ratioW + pixelE.r * ratioE,
        g: pixelW.g * ratioW + pixelE.g * ratioE,
        b: pixelW.b * ratioW + pixelE.b * ratioE,
        a: pixelW.a * ratioW + pixelE.a * ratioE,
      };

      return this.coloringFragments.flatMap((fragment) => {
        return fragment.decodeChannel(pixel);
      });
    }

    if (this.multiChannelColoringFragment) {
      // For multi-channel fragment, we cannot interpolate the pixel channel to
      // later convert into real-world value because it would mean possibly averaging
      // higher order factors (from channels R anf G), an also possible averaging
      // the category (channel A), which is not supposed to be averaged.
      // As a result:
      // - if the 4 surrounding points have a different value on the A channel,
      //   the value is not interpolated (nearest neighbor)
      // - else, all the values necessary to compute the interpoalted are first
      //   converted into the real-world value, and only then is interpolated

      if (pixelNW.a !== pixelNE.a || pixelNW.a !== pixelSE.a || pixelNW.a !== pixelSW.a) {
        return this.multiChannelColoringFragment.decodeChannel(pixelTest);
      }

      const decodedPixelNW = this.multiChannelColoringFragment.decodeChannel(pixelNW);
      const decodedPixelNE = this.multiChannelColoringFragment.decodeChannel(pixelNE);
      const decodedPixelSE = this.multiChannelColoringFragment.decodeChannel(pixelSE);
      const decodedPixelSW = this.multiChannelColoringFragment.decodeChannel(pixelSW);

      const decodedPixelW = decodedPixelNW[0] * ratioN + decodedPixelSW[0] * ratioS;
      const decodedPixelE = decodedPixelNE[0] * ratioN + decodedPixelSE[0] * ratioS;
      const decodedPixel = [decodedPixelW * ratioW + decodedPixelE * ratioE, decodedPixelNW[1]];

      return decodedPixel;
    }

    return null;
  }

  /**
   * Picks the best currently available values at the position.
   *
   * The values are read from the already loaded tiles at the current time.
   *
   * Return the interpolated array of decoded values
   * of the same length as the number of specified coloring fragments.
   *
   * If the coloring fragments uses more channels (e.g. "rg"),
   * the corresponding value is an array of `[r value, g value, sqrt(r^2 + g^2)]`.
   *
   * @param lng
   * @param lat
   * @returns Array of decoded interpolated values. In case of using a multi-channel coloring fragment, the returned value is an array where the first value is the value and the second is the category
   */
  public pick(
    lng: number,
    lat: number,
    options: {
      /**
       * Enable bilinear interpolation if `true`, otherwise, the picking will be based on the nearest neighbor (NN) pixel.
       * NN has less impact on performance because it requires less texture reading, so it is generaly faster. Bilinear is more precise
       * and will retrieve values that are closer to what is being displayed (since tiles are rendered using a native-GPU bilinear method)
       * Default: `false`
       */
      bilinear?: boolean;

      /**
       * If `true`, the picking will be perfomed on the highest resolution tile available. If `false`, the picking is done on the tile currently showing on the map.
       */
      highestRes?: boolean;

      /**
       * Will force the loading of the tile if it was not already cached.
       * Usually unnecesary when the picking is done from hovering the pointer on the map, unless the option `.highRes` is `true`.
       * Default: `false`.
       */
      load?: boolean;
    } = {},
  ): number[] | null {
    if (!this.isReady) {
      return null;
    }

    const { frameA, frameB, mix } = this.getCurrentFrames();
    if (!frameA || !frameB) return null;

    const useBilinear = !!options.bilinear;
    const load = options.load ?? false;
    const highestRes = options.highestRes ?? false;

    const valuesA = useBilinear
      ? this.pickFrameBilinear(lng, lat, frameA.data, { load, highestRes })
      : this.pickFrame(lng, lat, frameA.data, { load, highestRes });

    // In some cases, there won't be two frames to interpolate in between, but only one,
    // then we don't attempt to read the same a second time.
    let valuesB = valuesA;
    if (frameA.data !== frameB.data) {
      valuesB = useBilinear
        ? this.pickFrameBilinear(lng, lat, frameB.data, { load, highestRes })
        : this.pickFrame(lng, lat, frameB.data, { load, highestRes });
    }

    if (!valuesA) return valuesB;
    if (!valuesB) return valuesA;

    if (this.coloringFragments) {
      return valuesA.flatMap((a, i) => {
        const b = (valuesB as number[])[i];
        if (Array.isArray(a) && Array.isArray(b)) {
          return a.map((a_, i_) => {
            const b_ = b[i_];
            return a_ * (1 - mix) + b_ * mix;
          });
        }

        return <number>a * (1 - mix) + <number>b * mix;
      });
    }

    if (this.multiChannelColoringFragment) {
      if (this.timeInterpolation) {
        return [
          <number>valuesA[0] * (1 - mix) + <number>valuesB[0] * mix, // value
          mix < 0.5 ? valuesA[1] : valuesB[1], // category
        ];
      }

      return [
        mix < 0.5 ? valuesA[0] : valuesB[0], // value
        mix < 0.5 ? valuesA[1] : valuesB[1], // category
      ];
    }

    return null;
  }

  /**
   * The map is available only after the layer is attached to the map
   * The method simplifies the access to the map object
   */
  protected getMapOrThrow = () => {
    if (this.map == null) {
      throw new Error("Accessing map on detached layer");
    }

    return this.map;
  };

  /**
   * The renderer is available only after the layer is attached to the map
   * The method simplifies the access to the renderer object
   */
  protected getRendererOrThrow = () => {
    if (this.renderer == null) {
      throw new Error("Accessing renderer on detached layer");
    }

    return this.renderer;
  };

  /**
   * The method simplifies the access to the triggerRepaint method
   */
  public forceRepaint() {
    try {
      this.getMapOrThrow().triggerRepaint();
    } catch (e) {
      // empty
    }
  }

  /**
   * Get the current mixed image as a ImageData, meaning with pixel data, width, height and number of channels
   * Used by: Weather Plus
   * @param options.zxy The tile ID
   * @param options.blurKernel Size of the bluring kernel
   * @param options.outputSize Size of the outpout image (-1 means same as input). Note that the bluring is applied on an image of this size.
   * @returns ImageData | null
   */
  public computeCurrentMixedImage({
    zxy = "0/0/0",
    blurKernel = 0,
    outputSize = -1,
    channel = "r",
  }: {
    zxy?: string;
    blurKernel?: number;
    outputSize?: number;
    channel?: string;
  }): ImageData | null {
    if (!this.isReady) {
      return null;
    }

    const currentFrames = this.getCurrentFrames();
    const mix = currentFrames.mix;
    const textureA = <three.Texture>currentFrames.frameA?.data.getTile(zxy, false)?.texture;
    const textureB = <three.Texture>currentFrames.frameB?.data.getTile(zxy, false)?.texture;

    const w = outputSize < 0 ? Number.parseInt(textureA?.image.width) : outputSize;
    const h = outputSize < 0 ? Number.parseInt(textureA?.image.height) : outputSize;

    // we instanciate once and then reuse because they are a bit pricy to create
    if (!this.bluringNodePasses.length) {
      this.bluringNodePasses.push(new SinglePassNode(w, h), new SinglePassNode(w, h), new SinglePassNode(w, h));
    }

    const mixerNode = this.bluringNodePasses[0];
    const horizontalBlur = this.bluringNodePasses[1];
    const verticalBlur = this.bluringNodePasses[2];

    mixerNode.setSize(w, h);
    horizontalBlur.setSize(w, h);
    horizontalBlur.setSize(w, h);

    // mixing the image A and B

    mixerNode.setFragmentShader(MIX_FRAGMENT_SHADER);
    mixerNode.setDefine("CHANNEL", channel);
    mixerNode.setUniform("imageA", textureA);
    mixerNode.setUniform("imageB", textureB);
    mixerNode.setUniform("mixValue", mix);
    mixerNode.process();

    // no bluring, we stop here
    if (blurKernel === 0) {
      const pixelData = mixerNode.getPixelData();
      return {
        data: pixelData,
        channels: pixelData.length / (w * h),
        width: w,
        height: h,
      };
    }

    // we need some blurring
    if (![5, 9, 13, 19].includes(blurKernel)) {
      throw new Error(`The kernel bluring kernel size ${blurKernel} is not available.`);
    }

    horizontalBlur.setUniform("direction", new three.Vector2(1, 0));
    horizontalBlur.setUniform("kernelSize", blurKernel);
    horizontalBlur.setUniform("imgToBlur", <three.Texture>mixerNode.getOutputTexture());
    horizontalBlur.setFragmentShader(BLUR_FRAGMENT_SHADER);
    horizontalBlur.process();

    verticalBlur.setUniform("direction", new three.Vector2(0, 1));
    verticalBlur.setUniform("kernelSize", blurKernel);
    verticalBlur.setUniform("imgToBlur", <three.Texture>horizontalBlur.getOutputTexture());
    verticalBlur.setFragmentShader(BLUR_FRAGMENT_SHADER);
    verticalBlur.process();
    const pixelData = verticalBlur.getPixelData();

    for (const spn of this.bluringNodePasses) {
      spn.dispose();
    }

    return {
      data: pixelData,
      channels: pixelData.length / (w * h),
      width: w,
      height: h,
    };
  }

  /**
   * Enables data interpolation between keyframes when true. Only shows keyframe data when false.
   * @param ti
   */
  public setTimeInterpolation(ti: boolean) {
    if (!this.isReady) {
      return;
    }

    this.material.uniforms.timeInterpolation.value = ti;
  }

  /**
   * Enable smoothing category color when true. Hard edge between categories when false.
   * This seeting applies only to TileLayers using MultiChannelGradientColoringFragment
   * as the other types of oloring fragment do not use categories.
   * @param cst
   */
  public setCategorySmoothTransition(cst: boolean) {
    if (!this.isReady) {
      return;
    }

    this.material.uniforms.categorySmoothTransition.value = cst;
    this.forceRepaint();
  }

  /**
   * If `true`, enables the local smoothing
   * @param s
   */
  public setLocalSmoothing(s: boolean) {
    this.material.uniforms.localSmoothing.value = s;
    this.forceRepaint();
  }

  /**
   * Defines the size of the smoothing kernel
   * @param d
   */
  public setMaxSmoothingDistance(d: number) {
    this.material.uniforms.maxSmoothingDistance.value = d;
    this.forceRepaint();
  }

  /**
   * Defines by what factor the smoothing kernel size is reduced with increasing zoom level
   * @param f
   */
  public setSmoothingDistanceDecayFactor(f: number) {
    this.material.uniforms.smoothingDistanceDecayFactor.value = f;
    this.forceRepaint();
  }

  /**
   * Get whether or not the frames continues to rendered on a paused animation
   * @returns
   */
  public getRepaintOnPausedAnimation(): boolean {
    return this.repaintOnPausedAnimation;
  }

  /**
   * If `true`, even the paused animation is rendered up to 60 times
   * per seconds. If `false`, the rendering is paused when the animation is paused.
   * Pausing the animation has side effects:
   * - it lowers energy consumtion
   * - it prevents overheating
   * - it pauses time-independant annimation (arrows, particles)
   * @param r
   */
  public setRepaintOnPausedAnimation(r: boolean) {
    this.repaintOnPausedAnimation = r;
    this.forceRepaint();
  }

  /**
   * Wrapping telemetry registration with protected method to allow registration of modules which extends this class
   */
  protected registerTelemetry(map: MapSDK) {
    map.telemetry.registerModule(name, version);
  }
}

export { TileLayer };

/**
 * TODO:
 * - Add explanation
 * - Export to separate file, somehow
 */
interface ImageData {
  /**
   * The pixel data
   */
  data: Uint8Array;
  /**
   * The number of channels of the image (example: if image is RGBA then channels is 4)
   */
  channels: number;
  /**
   * Width of the image
   */
  width: number;
  /**
   * Height of the image
   */
  height: number;
}
