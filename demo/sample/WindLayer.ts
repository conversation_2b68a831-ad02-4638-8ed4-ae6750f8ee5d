import type { Map as MapSDK } from "@maptiler/sdk";
import { GradientColoringFragment } from "../../core/ColoringFragment";
import { ColorRamp } from "../../core/ColorRamp";
import { ParticleLayer } from "../ParticleLayer/ParticleLayer";
import { getCardinalDirection, type UsedRgbChannel } from "../../utils";
import { WeatherDataHandler } from "../../core/WeatherDataHandler";
import type { WindLayerOptions, WindPickAt } from "./types";

/**
 * Default builtin options that can be overloaded by user-provided options
 */
const defaultWindLayerOptions: WindLayerOptions = {
  maxAmount: 128,
  color: [255, 255, 255, 0],
  density: 200,
  size: 1,
  speed: 0.0015,
  refreshInterval: 200,
  fadeFactor: 0.04,
  opacity: 1,
  colorramp: ColorRamp.builtin.VIRIDIS,
  smooth: true,
  fastColor: [255, 255, 255, 255],
  fastSpeed: 3,
  fastIsLarger: true,
};

/**
 * The WindLayer shows the atmospheric wind in millibar (or hectopascal)
 */
class WindLayer extends ParticleLayer {
  private constructorOptions: WindLayerOptions;
  private isSourceReady = false;
  private colorRamp!: ColorRamp;

  constructor(options: WindLayerOptions = {}) {
    super(options.id || "MapTiler Wind", null, null, null);
    this.constructorOptions = options;
  }

  async onAdd(map: MapSDK, gl: WebGLRenderingContext | WebGL2RenderingContext) {
    const apiKey = (map as MapSDK).getSdkConfig().apiKey;
    const maptilerSessionId = (map as MapSDK).getMaptilerSessionId();

    // Check if some data has been fetched already and fetch if necessary
    if (!WeatherDataHandler.hasData()) {
      await WeatherDataHandler.fetchLatest(apiKey, maptilerSessionId);
    }

    // Get the latest wind data
    const latestDataset = WeatherDataHandler.getWindData();

    if (!latestDataset) throw new Error("The latest weather data is not avaialble");

    this.colorRamp = this.constructorOptions.colorramp ?? (defaultWindLayerOptions.colorramp as ColorRamp).scale(0, 40);

    // Finish the initialization of the underlaying TileLayer
    this.initParticle(
      {
        minZoom: latestDataset.metadata.minzoom,
        maxZoom: latestDataset.metadata.maxzoom,
      },
      {
        decodeChannels: latestDataset.metadata.weather_variable.decoding.channels.toLowerCase() as UsedRgbChannel,
        decodeMin: latestDataset.metadata.weather_variable.decoding.min,
        decodeMax: latestDataset.metadata.weather_variable.decoding.max,
        decodeAsWaves: false,
        maxAmount: this.constructorOptions.maxAmount ?? defaultWindLayerOptions.maxAmount,
        color: this.constructorOptions.color ?? defaultWindLayerOptions.color,
        fastColor: this.constructorOptions.fastColor ?? defaultWindLayerOptions.fastColor,
        fastSpeed: this.constructorOptions.fastSpeed ?? defaultWindLayerOptions.fastSpeed,
        density: this.constructorOptions.density ?? defaultWindLayerOptions.density,
        size: this.constructorOptions.size ?? defaultWindLayerOptions.size,
        speed: this.constructorOptions.speed ?? defaultWindLayerOptions.speed,
        refreshInterval: this.constructorOptions.refreshInterval ?? defaultWindLayerOptions.refreshInterval,
        fadeFactor: this.constructorOptions.fadeFactor ?? defaultWindLayerOptions.fadeFactor,

        angleDirectionShiftSkip: 30,
        uniformSpeed: true,
        fastIsLarger: this.constructorOptions.fastIsLarger ?? defaultWindLayerOptions.fastIsLarger,
      },
      [
        new GradientColoringFragment({
          decode: {
            channel: latestDataset.metadata.weather_variable.decoding.channels.toLowerCase() as UsedRgbChannel,
            min: latestDataset.metadata.weather_variable.decoding.min,
            max: latestDataset.metadata.weather_variable.decoding.max,
          },
          stops: this.colorRamp,
          smooth: this.constructorOptions.smooth ?? defaultWindLayerOptions.smooth,
          opacity: this.constructorOptions.opacity ?? defaultWindLayerOptions.opacity,
        }),
      ],
    );

    // Properly finish to add the layer to the map
    super.onAdd(map, gl);

    // Add all the keyframes
    const kfs = WeatherDataHandler.getSourcesAndTimestamps(latestDataset, apiKey, maptilerSessionId);

    for (const kf of kfs) {
      this.addSource(kf.timestamp, kf.source);
    }

    // Set the animation time to current time if possible
    const now = +new Date() / 1000; // in seconds
    if (now >= this.getAnimationStart() && now <= this.getAnimationEnd()) {
      this.setAnimationTime(now);
    }

    this.isSourceReady = true;
    this.emit("sourceReady", { map, layer: this });
  }

  /**
   * Check if the data source that fuels the layer has been fully fetched and processed
   * @returns
   */
  getIsSourceReady(): boolean {
    return this.isSourceReady;
  }

  /**
   * Get the wind speed and direction in multiple measurement units at a given location
   * @param lng
   * @param lat
   * @param options: bilinear interpolation (boolean) is disabled by default for better performance
   * @returns
   */
  pickAt(lng: number, lat: number, options: { bilinear?: boolean } = {}): WindPickAt | null {
    const originalValue = super.pick(lng, lat, options);

    if (!originalValue) return null;

    const toWest = originalValue[0];
    const toNorth = originalValue[1];
    const windSpeed = Math.sqrt(toWest ** 2 + toNorth ** 2);

    // Calculate the angle in radians.
    // This angle is the direction towards which the wind is blowing
    const angleRad = Math.atan2(toWest, toNorth);

    // Convert the angle to degrees and adjust to match the desired convention
    const angleDeg = (angleRad * 180) / Math.PI;

    return {
      speedMetersPerSecond: windSpeed,
      speedKilometersPerHour: windSpeed * 3.6,
      speedMilesPerHour: windSpeed * 2.23694,
      speedFeetPerSecond: windSpeed * 3.28084,
      speedKnots: windSpeed * 1.94384,
      directionAngle: angleDeg,
      compassDirection: getCardinalDirection(angleDeg + 180),
    };
  }

  /**
   * Get the color ramp in use by the layer
   * @returns
   */
  getColorRamp(): Readonly<ColorRamp> {
    return this.colorRamp;
  }

  /**
   * Async function corresponding to the "sourceReady" event.
   * Resolves directly if source is already ready, or awaits the "sourceReady"
   * event if not.
   */
  onSourceReadyAsync(): Promise<void> {
    return new Promise<void>((resolve) => {
      if (this.isSourceReady) {
        return resolve();
      }

      this.once("sourceReady", () => {
        resolve();
      });
    });
  }
}

export { WindLayer };
