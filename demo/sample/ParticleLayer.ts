import * as three from "three";

import ACCUMULATOR_FRAGMENT from "../../shaders/particlelayer/accumulator.fs.glsl";
import ACCUMULATOR_VERTEX from "../../shaders/particlelayer/accumulator.vs.glsl";
import PARTICLE_FRAGMENT from "../../shaders/particlelayer/particle/fs.glsl";
import PARTICLE_LINE_VERTEX from "../../shaders/particlelayer/particle/line.vs.glsl";
import PARTICLE_POINT_VERTEX from "../../shaders/particlelayer/particle/point.vs.glsl";
import RTT_FRAGMENT from "../../shaders/particlelayer/rtt.fs.glsl";
import RTT_VERTEX from "../../shaders/particlelayer/rtt.vs.glsl";

import { TexturePreview, USE_DEBUG_PARTICLES_TEXTURE, USE_DEBUG_PARTICLES_TEXTURE_PREVIEW } from "../../debug";
import type { ColoringFragmentBase } from "../../core/ColoringFragment";
import { GLOBE_MAX_ZOOM } from "../../tiles/constants";
import { EARTH_PERIMETER_M, floatifyColor } from "../../utils";

import { TileLayer, type TileLayerOptions } from "../TileLayer";
import type { ParticleLayerOptions } from "./type";
import { getGridSizeForPitchAndMapDimensions } from "../../tiles/utils";

/**
 * Extended version of {@link TileLayer}, which does particle-based animation on top the data.
 *
 * The standard raster visualization of {@link TileLayer} can be used simultanously over the same data.
 */
class ParticleLayer extends TileLayer {
  // RTT - Render To Texture
  private rttScene: three.Scene = new three.Scene();
  private rttCamera!: three.Camera;
  private rttMaterial!: three.RawShaderMaterial;
  private rttMesh!: three.Mesh;

  private particleTexturePrev!: three.Texture;
  private particleTexture0!: three.WebGLRenderTarget;
  private particleTexture1!: three.WebGLRenderTarget;
  private particleMaterial!: three.RawShaderMaterial;
  private particles!: three.Points | three.LineSegments;
  private particleBackground!: three.Mesh;
  private accumulator!: three.WebGLRenderTarget;
  private accumulatorDrawMesh!: three.Mesh;

  private flipFlop = false;

  private numParticles!: number;
  private particleDensity!: number;
  private refreshInterval!: number;
  private fadeFactor!: number;
  private particleColor!: string;
  private particleFastColor!: string;
  private particleFastSpeed!: number;
  private particleSize!: number;
  private drawAsLines!: boolean;
  private particleSpeed!: number;
  private pixelRatio!: number;

  private prevRenderTime = 0;
  private lastRenderTime = 0;
  private forceRender = true;

  private rttTimestep = 200;
  private tileGridPass = 0;

  // private textureScale = 1;
  private particlesTexturePreview: TexturePreview | undefined;

  /**
   * @param id Unique identifier of the layer in the map.
   * @param options
   * @param particleOptions
   * @param coloringFragments If not null, the same data will also be displayed as raster.
   */
  constructor(
    id: string,
    options: TileLayerOptions | null,
    particleOptions: ParticleLayerOptions | null,
    coloringFragments?: ColoringFragmentBase[] | null,
  ) {
    super(id, null, null);

    if (options && particleOptions) {
      this.initParticle(options, particleOptions, coloringFragments);
    }

    if (USE_DEBUG_PARTICLES_TEXTURE_PREVIEW === true) {
      this.particlesTexturePreview = new TexturePreview();
    }
  }

  /**
   * TODO: Add description
   */
  public initParticle(
    options: TileLayerOptions,
    particleOptions: ParticleLayerOptions,
    coloringFragments?: ColoringFragmentBase[] | null,
  ) {
    this.init(options, coloringFragments, null);

    this.extentScale = 1.2;

    this.numParticles = 128;

    if (particleOptions.maxAmount != null) {
      if (
        particleOptions.maxAmount >= 4 &&
        particleOptions.maxAmount &&
        (particleOptions.maxAmount & (particleOptions.maxAmount - 1)) === 0
      ) {
        this.numParticles = particleOptions.maxAmount;
      } else {
        throw new Error("The number of particles needs to be power of two and at least 4");
      }
    }

    const dpr = window.devicePixelRatio;

    this.particleDensity = particleOptions.density ?? 2;
    this.particleDensity /= dpr * dpr;

    this.refreshInterval = particleOptions.refreshInterval ?? 800;
    this.fadeFactor = particleOptions.fadeFactor ?? 0.1;
    this.particleColor = floatifyColor(particleOptions.color ?? [255, 255, 255, 192]);
    this.particleFastColor = particleOptions.fastColor ? floatifyColor(particleOptions.fastColor) : this.particleColor;
    this.particleFastSpeed = (particleOptions.fastSpeed ?? 2) * dpr;
    this.particleSize = particleOptions.size ?? 1.5;
    this.drawAsLines = particleOptions.drawAsLines ?? false;
    this.particleSpeed = (particleOptions.speed ?? 0.001) / 1000;
    this.pixelRatio = particleOptions.pixelRatio ?? (dpr > 1 ? 1 : 2);

    this.particleTexturePrev = this.generateRandomizeParticleTexture(this.numParticles, this.numParticles);
    this.particleTexture0 = new three.WebGLRenderTarget(this.numParticles, this.numParticles, {
      stencilBuffer: false,
      depthBuffer: false,
    });
    this.particleTexture0.texture = this.particleTexturePrev.clone();
    this.particleTexture1 = this.particleTexture0.clone();

    this.rttMaterial = new three.RawShaderMaterial({
      glslVersion: three.GLSL3,
      uniforms: {
        tileGridPass: { value: this.tileGridPass },
        uniformSpeed: { value: particleOptions.uniformSpeed ?? false },
        canvasDiagonal: { value: 0 },
        angleDirectionShiftSkip: {
          value: particleOptions.angleDirectionShiftSkip ?? 90,
        },
        rttXY: { value: [0, 0] },
        rttScale: { value: 1 },
        rttTexture: {
          value: this.particleTexture0.texture,
        },
        tileScale: { value: 0 },
        useAlphaAsMask: { value: particleOptions.useAlphaAsMask ?? false },
        tilePosition: { value: [0, 0] },
        time: { value: 0 },
        timestep: { value: 0 },
        tex0xy: { value: [0, 0] },
        tex1xy: { value: [0, 0] },
        tex0size: { value: 1 },
        tex1size: { value: 1 },
        tex0: {
          value: null,
        },
        tex1: {
          value: null,
        },
      },
      vertexShader: RTT_VERTEX,
      fragmentShader: RTT_FRAGMENT,
      defines: {
        D_MIN: (particleOptions.decodeMin ?? 0).toFixed(2),
        D_MAX: (particleOptions.decodeMax ?? 1).toFixed(2),
        D_CHANNELS: particleOptions.decodeChannels ?? "rg",
        D_WAVES: particleOptions.decodeAsWaves ? "true" : "false",
      },
      depthTest: false,
      depthWrite: false,
    });

    this.rttCamera = new three.OrthographicCamera(
      -this.numParticles / 2,
      this.numParticles / 2,
      this.numParticles / 2,
      -this.numParticles / 2,
      -100,
      100,
    );

    this.rttMesh = new three.Mesh(new three.PlaneGeometry(this.numParticles, this.numParticles), this.rttMaterial);
    this.rttScene.add(this.rttMesh);

    // ----------------------------

    this.accumulator = new three.WebGLRenderTarget(16, 16, {
      minFilter: three.LinearFilter,
      magFilter: three.LinearFilter,
      stencilBuffer: false,
      format: three.RGBAFormat,
      depthBuffer: false,
    });

    this.particleBackground = new three.Mesh(
      new three.PlaneGeometry(2, 2),
      new three.RawShaderMaterial({
        glslVersion: three.GLSL3,
        transparent: true,
        blending: three.CustomBlending,
        blendEquationAlpha: three.ReverseSubtractEquation,
        vertexShader: "precision highp float;in vec3 position;void main(){gl_Position=vec4(position,1.);}",
        fragmentShader: `precision highp float;out vec4 fragColor;void main(){fragColor=vec4(.0,.0,.0,${this.fadeFactor.toFixed(
          2,
        )});}`,
      }),
    );

    // ----------------------------

    const pointSize = dpr * this.pixelRatio * this.particleSize;
    const lineLength = dpr * this.particleSize;

    this.particleMaterial = new three.RawShaderMaterial({
      glslVersion: three.GLSL3,
      uniforms: {
        fastIsLarger: { value: particleOptions.fastIsLarger ?? false },
        rttSize: { value: this.numParticles },
        rttTexture: {
          value: this.particleTexture1.texture,
        },
        rttTexturePrev: {
          value: this.particleTexturePrev,
        },
        screenSize: { value: 1 },
        extrapolationFactor: { value: 0 },
        renderStepSize: { value: 0 },
      },
      vertexShader: this.drawAsLines ? PARTICLE_LINE_VERTEX : PARTICLE_POINT_VERTEX,
      fragmentShader: PARTICLE_FRAGMENT,
      defines: {
        COLOR_A: this.particleColor,
        COLOR_B: this.particleFastColor,
        COLOR_SPEED: this.particleFastSpeed.toFixed(2),
        DENSITY: this.particleDensity.toFixed(2),
        SIZE: (this.drawAsLines ? lineLength : pointSize).toFixed(1),
      },
      transparent: true,
      depthTest: false,
      depthWrite: false,
      linewidth: this.pixelRatio, // not guaranteed to work, but try anyway
    });

    if (this.drawAsLines) {
      const lineIndices = new Float32Array(Array(2 * this.numParticles * this.numParticles).keys());
      const lineGeometry = new three.BufferGeometry();
      lineGeometry.setAttribute("position", new three.Float32BufferAttribute(lineIndices, 1));
      this.particles = new three.LineSegments(lineGeometry, this.particleMaterial);
    } else {
      const pointIndices = new Float32Array(Array(this.numParticles * this.numParticles).keys());
      const pointGeometry = new three.BufferGeometry();
      pointGeometry.setAttribute("position", new three.Float32BufferAttribute(pointIndices, 1));
      this.particles = new three.Points(pointGeometry, this.particleMaterial);
    }

    this.particles.frustumCulled = false;

    // ----------------------------
    const particlesResultMaterial = new three.RawShaderMaterial({
      glslVersion: three.GLSL3,
      premultipliedAlpha: true,
      transparent: true,
      depthTest: false,
      depthWrite: false,
      uniforms: {
        opacity: { value: 1 },
        tex0: {
          value: this.accumulator.texture,
        },
      },
      vertexShader: ACCUMULATOR_VERTEX,
      fragmentShader: ACCUMULATOR_FRAGMENT,
    });
    this.accumulatorDrawMesh = new three.Mesh(new three.PlaneGeometry(1, 1), particlesResultMaterial);
    this.accumulatorDrawMesh.renderOrder = 2;
    this.slippyTilesGroup.add(this.accumulatorDrawMesh);

    if (USE_DEBUG_PARTICLES_TEXTURE === false) {
      this.globeTilesService.visibleTilesAreaMesh.visible = true;
      this.globeTilesService.visibleTilesAreaMesh.material = particlesResultMaterial;
    }

    setInterval(() => {
      if (this.renderer) {
        this.randomizeParticles(false);
      }
    }, this.refreshInterval);

    this.on("extentChanged", () => {
      this.refresh();
    });
  }

  /**
   * TODO: Add description
   */
  private generateRandomizeParticleTexture(w: number, h: number) {
    const data = new Uint8Array(4 * w * h);
    for (let i = 0; i < data.length; i++) {
      data[i] = Math.floor(256 * Math.random());
    }

    return new three.DataTexture(data, w, h);
  }

  /**
   * TODO: Add description
   */
  private randomizeParticles(all: boolean) {
    if (!this.renderer) {
      return;
    }
    let w = this.numParticles;
    let h = this.numParticles;
    const xy = new three.Vector2(0, 0);

    if (!all) {
      const PART = this.numParticles / 4;
      w = PART;
      h = PART;
      xy.x = Math.floor((Math.random() * this.numParticles) / PART) * PART;
      xy.y = Math.floor((Math.random() * this.numParticles) / PART) * PART;
    }

    const newData = this.generateRandomizeParticleTexture(w, h);
    this.renderer.copyTextureToTexture(xy, newData, this.particleTexturePrev);
    this.renderer.copyTextureToTexture(xy, newData, this.particleTexture0.texture);
    this.renderer.copyTextureToTexture(xy, newData, this.particleTexture1.texture);
    newData.dispose();

    this.forceRender = all;
  }

  /**
   * Returns the ratio between the number of actually visible particle
   * (to statisfy the specified density) and the maximum amount (maxAmount).
   *
   * Value >1 mean that more particles would be utilized if availabe.
   *
   * Useful for debugging and fine-tuning client application.
   */
  public getParticleUtilization(): number {
    const size = this.particleMaterial.uniforms.screenSize.value;
    return ((this.particleDensity / 1000) * size * size) / (this.numParticles * this.numParticles);
  }

  /**
   * TODO: Add description
   */
  public setOpacity(opacity: number) {
    super.setOpacity(opacity);
    (<three.RawShaderMaterial>this.accumulatorDrawMesh.material).uniforms.opacity.value = opacity;
  }

  /**
   * TODO: Add description
   */
  protected override disposeObjects() {
    super.disposeObjects();

    this.particleTexturePrev.dispose();
    this.particleTexture0.texture.dispose();
    this.particleTexture0.dispose();
    this.particleTexture1.texture.dispose();
    this.particleTexture1.dispose();
    this.accumulator.texture.dispose();
    this.accumulator.dispose();

    this.rttMesh.geometry.dispose();
    (<three.Material>this.rttMesh.material).dispose();
    this.particleBackground.geometry.dispose();
    (<three.Material>this.particleBackground.material).dispose();
    this.particles.geometry.dispose();
    (<three.Material>this.particles.material).dispose();
    this.accumulatorDrawMesh.geometry.dispose();
    (<three.Material>this.accumulatorDrawMesh.material).dispose();
  }

  /**
   * Called in refresh method
   * It is important for globe projection, especially for low zoom levels
   */
  private updateRenderingSize() {
    const mapInstance = this.getMapOrThrow();
    const zoom = mapInstance.getZoom();
    const pitch = mapInstance.getPitch();
    const canvas = mapInstance.getCanvas();
    const width = canvas.width;
    const height = canvas.height;

    const { resolutionScaleFactor } = getGridSizeForPitchAndMapDimensions(pitch, mapInstance);
    const baseTextureSize = 1024 * 2 * this.pixelRatio * resolutionScaleFactor;

    let size = baseTextureSize;

    if (this.getMapOrThrow().isGlobeProjection() === true) {
      if (zoom > GLOBE_MAX_ZOOM + 1) {
        size = baseTextureSize;
      } else {
        size = Math.max(baseTextureSize * (zoom * 0.75), 1024);
      }
    } else {
      size = Math.round(this.pixelRatio * this.extentScale * Math.max(width, height));
    }

    size = Math.min(size, this.getRendererOrThrow().capabilities.maxTextureSize);
    /** Speed related */
    const canvasDiagonal = Math.sqrt(width * width + height * height) * (devicePixelRatio === 1 ? 2 : 1);
    this.rttMaterial.uniforms.canvasDiagonal.value = canvasDiagonal;
    /* *** */

    this.particleMaterial.uniforms.screenSize.value = size;
    this.accumulator.setSize(size, size);

    this.slippyTilesGroup.remove(this.accumulatorDrawMesh);
    this.slippyTilesGroup.add(this.accumulatorDrawMesh);
  }

  /**
   * Called by onMoveEnd in parent class.
   */
  protected override refresh() {
    super.refresh();

    if (this.getMapOrThrow().isGlobeProjection() === false) {
      this.refreshMercator();
    } else {
      this.refreshGlobe();
    }

    this.updateRenderingSize();

    const renderer = this.getRendererOrThrow();
    renderer.setRenderTarget(this.accumulator);
    renderer.setClearAlpha(0);
    renderer.clearColor();
    renderer.setRenderTarget(null);
    this.randomizeParticles(true);
  }

  /**
   * Called by prerender in parent class.
   */
  protected override prerenderInternal() {
    const now = performance.now();
    const timeSinceLastRender = now - this.lastRenderTime;
    const doRtt = this.forceRender || timeSinceLastRender > this.rttTimestep || !this.prevRenderTime;

    if (doRtt) {
      this.forceRender = false;
      this.rttMaterial.uniforms.timestep.value = this.particleSpeed * timeSinceLastRender;

      this.getRendererOrThrow().setRenderTarget(!this.flipFlop ? this.particleTexture0 : this.particleTexture1);
      this.getRendererOrThrow().copyFramebufferToTexture(new three.Vector2(0, 0), this.particleTexturePrev);

      this.prevRenderTime = this.lastRenderTime;
      this.lastRenderTime = now;

      if (this.getMapOrThrow().isGlobeProjection() === false) {
        this.prerenderInternalMercator();
      } else {
        this.prerenderInternalGlobe();
      }

      this.particleMaterial.uniforms.rttTexture.value = (
        !this.flipFlop ? this.particleTexture0 : this.particleTexture1
      ).texture;

      this.tileGridPass = this.tileGridPass === 0 ? 1 : 0;
    }

    const renderStepSize = this.lastRenderTime - this.prevRenderTime;
    const extrapolationFactor = doRtt ? 0 : timeSinceLastRender / renderStepSize;
    this.particleMaterial.uniforms.extrapolationFactor.value = extrapolationFactor;
    this.particleMaterial.uniforms.renderStepSize.value = renderStepSize / 1000;

    const renderer = this.getRendererOrThrow();
    renderer.setRenderTarget(this.accumulator);
    renderer.render(this.particleBackground, this.camera);
    renderer.render(this.particles, this.camera);
    renderer.setRenderTarget(null);

    if (this.particlesTexturePreview !== undefined) {
      const width = this.accumulator.width;
      const height = this.accumulator.height;
      const buffer = new Uint8Array(width * height * 4);

      renderer.readRenderTargetPixels(this.accumulator, 0, 0, width, height, buffer);

      this.particlesTexturePreview.update(buffer, width, height);
    }
  }

  /**
   * TODO: Add description
   */
  private refreshMercator() {
    const extent = this.getVisibleExtent(this.extentScale);

    if (!extent) throw new Error("The extent is null");

    const scale = Math.max(extent[2] - extent[0], extent[3] - extent[1]) / EARTH_PERIMETER_M;

    const XY = [
      0.5 + (extent[2] + extent[0]) / 2 / EARTH_PERIMETER_M - scale / 2,
      0.5 - (extent[3] + extent[1]) / 2 / EARTH_PERIMETER_M - scale / 2,
    ];

    this.rttMaterial.uniforms.rttScale.value = scale;
    this.rttMaterial.uniforms.rttXY.value = XY;
  }

  /**
   * TODO: Add description
   */
  private prerenderInternalMercator() {
    const gridWidth = this.slippyTiles.length;
    for (let _x = 0; _x < gridWidth; _x++) {
      const gridHeight = this.slippyTiles[0].length;
      for (let _y = 0; _y < gridHeight; _y++) {
        const tile = this.slippyTiles[_x][_y];

        if (!tile.visible) {
          continue;
        }

        const tileMaterial = <three.RawShaderMaterial>tile.material;
        const uniforms = tileMaterial.uniforms;

        if (!uniforms) continue;

        const scale = tile.scale.x;
        this.rttMaterial.uniforms.tileScale.value = scale;
        this.rttMaterial.uniforms.tilePosition.value = [tile.position.x / scale - 0.5, -tile.position.y / scale - 0.5];

        this.rttMaterial.uniforms.tileGridPass.value = this.tileGridPass;
        this.rttMaterial.uniforms.time.value = uniforms.time.value;
        this.rttMaterial.uniforms.tex0.value = uniforms.tex0.value;
        this.rttMaterial.uniforms.tex0xy.value = uniforms.tex0xy.value;
        this.rttMaterial.uniforms.tex0size.value = uniforms.tex0size.value;
        this.rttMaterial.uniforms.tex1.value = uniforms.tex1.value;
        this.rttMaterial.uniforms.tex1xy.value = uniforms.tex1xy.value;
        this.rttMaterial.uniforms.tex1size.value = uniforms.tex1size.value;

        this.rttMaterial.uniforms.rttTexture.value = (
          this.flipFlop ? this.particleTexture1 : this.particleTexture0
        ).texture;

        const renderer = this.getRendererOrThrow();
        renderer.setRenderTarget(this.flipFlop ? this.particleTexture0 : this.particleTexture1);
        renderer.render(this.rttScene, this.rttCamera);

        this.flipFlop = !this.flipFlop;
      }
    }
  }

  /**
   * Called by `refresh` method.
   * Refresh logic is different for globe and mercator projection.
   */
  private refreshGlobe() {
    // For the globe projection this is different then for the mercator projection
    // With each refresh the spher mesh fragment is updated to fit the extent of the visible tiles
    // Look also at prerenderInternalMercator vs prerenderInternalGlobe
    this.rttMaterial.uniforms.rttScale.value = 1;
    this.rttMaterial.uniforms.rttXY.value = [0, 0];
  }

  /**
   * Called by `prerenderInternal` method.
   * Prerender logic is different for globe and mercator projection.
   */
  private prerenderInternalGlobe() {
    const tilesIds = [...this.globeTilesService.currentTilesMeshes.entries()].map(([tileId]) => tileId);

    const center = this.getMapOrThrow().getCenter();
    const zoom = this.getMapOrThrow().getZoom();

    const amountOfTiles = tilesIds.length;
    const gridDimension = Math.floor(Math.sqrt(amountOfTiles));
    /**
     * For zoom level <= GLOBE_MAX_ZOOM the "seam" of the globe particles texture is visible,
     * so we have to care about that and make the stiching be on the back side.
     *
     * To do that we calculate the `globeSideFactor`.
     * lng -90;90 -> 0 (the "seam" is in the back as default)
     * lng 90;180 -> 1
     * lng -90;-180 -> -1
     */
    const globeSideFactor = center.lng >= 0 ? Math.floor(center.lng / 90) : -1 * Math.floor(Math.abs(center.lng) / 90);

    let textureTileOffset = 0;

    // Reset the rotation
    this.globeTilesService.visibleTilesAreaMesh.rotation.y = 0;
    if (zoom <= GLOBE_MAX_ZOOM) {
      /**
       * Now we have to rotate the globe to make the "seam" be on the back side.
       */
      const backSideRotationY = globeSideFactor * three.MathUtils.degToRad(180);
      this.globeTilesService.visibleTilesAreaMesh.rotation.y = backSideRotationY;
      /**
       * Since we rotate the globe 180 degrees, we have to move the texture tiles.
       * We have to move them by the half of the gridDimension.
       */
      textureTileOffset = globeSideFactor * (gridDimension / 2);
    }

    const { frameA, frameB, mix } = this.getCurrentFrames();

    if (!frameA || !frameB) {
      return;
    }

    for (const tileId of tilesIds) {
      const index = tilesIds.indexOf(tileId);
      const x = (Math.floor(index / gridDimension) + textureTileOffset + gridDimension) % gridDimension;
      const y = index % gridDimension;
      this.rttMaterial.uniforms.tileScale.value = 1 / gridDimension;
      this.rttMaterial.uniforms.tilePosition.value = [x, y];

      const tilesPair = this.getTilesPair(frameA, frameB, tileId);

      if (tilesPair === null) {
        continue;
      }

      const { tileA, tileB } = tilesPair;

      this.rttMaterial.uniforms.tileGridPass.value = this.tileGridPass;
      this.rttMaterial.uniforms.time.value = mix;
      this.rttMaterial.uniforms.tex0.value = tileA.tile.texture;
      this.rttMaterial.uniforms.tex0xy.value = tileA.xy;
      this.rttMaterial.uniforms.tex0size.value = tileA.size;
      this.rttMaterial.uniforms.tex1.value = tileB.tile.texture;
      this.rttMaterial.uniforms.tex1xy.value = tileB.xy;
      this.rttMaterial.uniforms.tex1size.value = tileB.size;
      this.rttMaterial.uniforms.rttTexture.value = (
        this.flipFlop ? this.particleTexture1 : this.particleTexture0
      ).texture;

      const renderer = this.getRendererOrThrow();
      renderer.setRenderTarget(this.flipFlop ? this.particleTexture0 : this.particleTexture1);
      renderer.render(this.rttScene, this.rttCamera);

      this.flipFlop = !this.flipFlop;
    }
  }

  /**
   * Called by render in parent class.
   */
  protected override renderInternal() {
    if (this.getMapOrThrow().isGlobeProjection() === false) {
      const scale = this.rttMaterial.uniforms.rttScale.value;

      this.accumulatorDrawMesh.position.x = 0.5 * scale + this.rttMaterial.uniforms.rttXY.value[0];
      this.accumulatorDrawMesh.position.y = -0.5 * scale - this.rttMaterial.uniforms.rttXY.value[1];
      this.accumulatorDrawMesh.scale.x = this.accumulatorDrawMesh.scale.y = scale;
    }
  }
}

export { ParticleLayer };
