{"format": "webp", "maxzoom": 14, "minzoom": 0, "profile": "mercator", "scale": "2.000000", "description": "The dataset contains the digital elevation model (DEM) - global coverage encoded into RGB. You can use this data to enrich your map styles with hill shade or colored slopes but you can also use it in applications - such as elevation profiles, microwave link planning, etc.\r\n\r\nDataset ID: terrain-rgb-v2", "attribution": "<a href=\"https://www.maptiler.com/copyright/\" target=\"_blank\">&copy; MapTiler</a> <a href=\"https://www.openstreetmap.org/copyright\" target=\"_blank\">&copy; OpenStreetMap contributors</a>", "bounds": [-180, -85.0511287798066, 180, 85.0511287798066], "center": [0, 0, 1], "logo": "https://api.maptiler.com/resources/logo.svg", "tilejson": "2.0.0", "name": "Terrain RGB", "crs": "EPSG:3857", "crs_wkt": "PROJCS[\"WGS 84 / Pseudo-Mercator\",GEOGCS[\"WGS 84\",<PERSON><PERSON><PERSON>[\"WGS_1984\",SPHEROID[\"WGS 84\",6378137,298.257223563,AUTHORITY[\"EPSG\",\"7030\"]],AUTH<PERSON><PERSON><PERSON>[\"EPSG\",\"6326\"]],<PERSON><PERSON><PERSON>[\"Greenwich\",0,AUTHORIT<PERSON>[\"EPSG\",\"8901\"]],UNIT[\"degree\",0.0174532925199433,AUTHORITY[\"EPSG\",\"9122\"]],AUTHORITY[\"EPSG\",\"4326\"]],PROJECTION[\"Mercator_1SP\"],PARAMETER[\"central_meridian\",0],PARAMETER[\"scale_factor\",1],PARAMETER[\"false_easting\",0],PARAMETER[\"false_northing\",0],UNIT[\"metre\",1,AUTHORITY[\"EPSG\",\"9001\"]],AXIS[\"Easting\",EAST],AXIS[\"Northing\",NORTH],<PERSON><PERSON><PERSON><PERSON><PERSON>[\"PROJ4\",\"+proj=merc +a=6378137 +b=6378137 +lat_ts=0 +lon_0=0 +x_0=0 +y_0=0 +k=1 +units=m +nadgrids=@null +wktext +no_defs\"],AUTHORITY[\"EPSG\",\"3857\"]]", "extent": [-20037508.3427892, -20037508.3427892, 20037508.3427892, 20037508.3427892], "tiles": ["https://api.maptiler.com/tiles/terrain-rgb-v2/{z}/{x}/{y}.webp?key=oinoatcrNmdCL1524DOl"]}