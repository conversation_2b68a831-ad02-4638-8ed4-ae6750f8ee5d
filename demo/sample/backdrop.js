{"version": 8, "id": "backdrop", "name": "Backdrop", "sources": {"land": {"url": "https://api.maptiler.com/tiles/land/tiles.json?key=oinoatcrNmdCL1524DOl", "type": "vector"}, "maptiler_attribution": {"attribution": "<a href=\"https://www.maptiler.com/copyright/\" target=\"_blank\">&copy; MapTiler</a> <a href=\"https://www.openstreetmap.org/copyright\" target=\"_blank\">&copy; OpenStreetMap contributors</a>", "type": "vector"}, "maptiler_planet": {"url": "https://api.maptiler.com/tiles/v3/tiles.json?key=oinoatcrNmdCL1524DOl", "type": "vector"}, "terrain_rgb": {"url": "https://api.maptiler.com/tiles/terrain-rgb-v2/tiles.json?key=oinoatcrNmdCL1524DOl", "type": "raster-dem"}}, "layers": [{"id": "Background", "type": "background", "layout": {"visibility": "visible"}, "paint": {"background-color": "hsl(0, 0%, 100%)", "background-opacity": 1}}, {"id": "Residential", "type": "fill", "source": "maptiler_planet", "source-layer": "landuse", "minzoom": 6, "maxzoom": 14, "layout": {"visibility": "visible"}, "paint": {"fill-antialias": true, "fill-color": ["interpolate", ["linear"], ["zoom"], 6, "hsl(190, 0%, 73%)", 13, "hsl(190, 0%, 90%)"], "fill-opacity": 1}, "filter": ["==", "class", "residential"]}, {"id": "Landcover", "type": "fill", "source": "maptiler_planet", "source-layer": "landcover", "layout": {"visibility": "visible"}, "paint": {"fill-color": "hsl(0, 0%, 100%)", "fill-opacity": {"stops": [[8, 0.2], [9, 0.25], [11, 0.35]]}}, "filter": ["in", "class", "grass", "wood"]}, {"id": "Hillshade", "type": "hillshade", "source": "terrain_rgb", "layout": {"visibility": "visible"}, "paint": {"hillshade-highlight-color": "hsl(0, 0%, 15%)", "hillshade-shadow-color": "hsl(0, 1%, 60%)"}}, {"id": "River", "type": "line", "source": "maptiler_planet", "source-layer": "waterway", "layout": {"visibility": "visible"}, "paint": {"line-color": "hsl(220, 1%, 86%)", "line-width": ["interpolate", ["linear"], ["zoom"], 8, 0.5, 9, 1, 15, 1.5, 16, 2.5]}}, {"id": "Water shadow", "type": "fill", "source": "maptiler_planet", "source-layer": "water", "layout": {"visibility": "visible"}, "paint": {"fill-antialias": true, "fill-color": "hsl(229, 1%, 73%)", "fill-opacity": 1}, "filter": ["all", ["==", "$type", "Polygon"], ["!=", "brunnel", "tunnel"]]}, {"id": "Water", "type": "fill", "source": "maptiler_planet", "source-layer": "water", "minzoom": 0, "layout": {"visibility": "visible"}, "paint": {"fill-antialias": true, "fill-color": "hsl(220, 1%, 86%)", "fill-opacity": 1, "fill-translate": {"stops": [[0, [0, 2]], [6, [0, 3]], [12, [0, 2]], [14, [0, 0]]]}, "fill-translate-anchor": "map"}, "filter": ["all", ["==", "$type", "Polygon"], ["!=", "brunnel", "tunnel"]]}, {"id": "Aeroway", "type": "line", "source": "maptiler_planet", "source-layer": "aeroway", "minzoom": 12, "layout": {"line-cap": "round", "line-join": "round", "visibility": "visible"}, "paint": {"line-color": "hsl(220, 0%, 80%)", "line-width": ["interpolate", ["linear", 1], ["zoom"], 11, ["match", ["get", "class"], ["runway"], 3, 0.5], 15, ["match", ["get", "class"], ["runway"], 15, 6], 16, ["match", ["get", "class"], ["runway"], 20, 6]]}, "metadata": {}, "filter": ["all", ["==", "$type", "LineString"]]}, {"id": "Tunnel path", "type": "line", "source": "maptiler_planet", "source-layer": "transportation", "minzoom": 15, "layout": {"line-cap": "round", "line-join": "round", "visibility": "visible"}, "paint": {"line-color": "hsl(220, 0%, 25%)", "line-dasharray": [0.5, 2], "line-opacity": 1, "line-width": ["interpolate", ["linear"], ["zoom"], 14, 0.7, 16, 0.8, 18, 1, 22, 2]}, "filter": ["all", ["==", "class", "path"], ["==", "brunnel", "tunnel"]]}, {"id": "Tunnel", "type": "line", "source": "maptiler_planet", "source-layer": "transportation", "minzoom": 11, "layout": {"line-cap": "square", "line-join": "miter", "visibility": "visible"}, "paint": {"line-color": "hsl(220, 0%, 90%)", "line-opacity": ["step", ["zoom"], 1, 10, ["match", ["get", "class"], "motorway", 1, 0], 11, ["match", ["get", "class"], ["motorway", "trunk", "primary"], 1, 0], 13, ["match", ["get", "class"], ["motorway", "trunk", "primary", "secondary", "tertiary"], 1, 0], 15, 1], "line-width": ["interpolate", ["linear", 2], ["zoom"], 5, 0.5, 6, ["match", ["get", "class"], ["motorway"], ["match", ["get", "brunnel"], ["bridge"], 0, 1], ["trunk", "primary"], 0, 0], 10, ["match", ["get", "class"], ["motorway"], ["match", ["get", "ramp"], 1, 0, 2.5], ["trunk", "primary"], 1.5, 1], 12, ["match", ["get", "class"], ["motorway"], ["match", ["get", "ramp"], 1, 1, 4], ["trunk"], 2.5, ["primary"], 2.5, ["secondary", "tertiary"], 1.5, ["minor", "service", "track"], 1, 1], 14, ["match", ["get", "class"], ["motorway"], ["match", ["get", "ramp"], 1, 5, 6], ["trunk"], 3, ["primary"], 5, ["secondary"], 4, ["tertiary"], 3, ["minor", "service", "track"], 2, 2], 16, ["match", ["get", "class"], ["motorway", "trunk", "primary"], 8, ["secondary"], 7, ["tertiary"], 6, ["minor", "service", "track"], 4, 4], 20, ["match", ["get", "class"], ["motorway", "trunk", "primary"], 24, ["secondary"], 24, ["tertiary"], 24, ["minor", "service", "track"], 16, 16]]}, "filter": ["all", ["in", "class", "motorway", "trunk", "primary", "secondary", "tertiary", "minor", "service"], ["==", "brunnel", "tunnel"]]}, {"id": "Railway tunnel", "type": "line", "source": "maptiler_planet", "source-layer": "transportation", "minzoom": 13, "layout": {"line-join": "round", "visibility": "visible"}, "paint": {"line-color": "hsl(220, 0%, 80%)", "line-opacity": 0.5, "line-width": {"base": 1.3, "stops": [[13, 0.5], [14, 1], [15, 1], [16, 3], [21, 7]]}}, "filter": ["all", ["==", "class", "rail"], ["==", "brunnel", "tunnel"]]}, {"id": "Railway tunnel dash", "type": "line", "source": "maptiler_planet", "source-layer": "transportation", "minzoom": 15, "layout": {"line-join": "round", "visibility": "visible"}, "paint": {"line-color": "hsl(220, 0%, 95%)", "line-dasharray": [2, 2], "line-width": {"base": 1.3, "stops": [[15, 0.5], [16, 1], [20, 5]]}}, "filter": ["all", ["==", "class", "rail"], ["==", "brunnel", "tunnel"]]}, {"id": "Land outline", "type": "line", "source": "land", "source-layer": "land", "minzoom": 0, "maxzoom": 16, "layout": {"visibility": "visible"}, "paint": {"line-color": "hsl(220, 1%, 0%)", "line-opacity": ["interpolate", ["linear"], ["zoom"], 0, 1, 13, 0.9, 14, 0.6, 15, 0.3], "line-translate": {"stops": [[0, [0, 2]], [6, [0, 3]], [12, [0, 2]], [14, [0, 0]]]}, "line-translate-anchor": "map", "line-width": ["interpolate", ["linear"], ["zoom"], 0, 0.3, 5, 0.5, 8, 0.75, 12, 1, 16, 1.5]}}, {"id": "Pier", "type": "fill", "source": "maptiler_planet", "source-layer": "transportation", "layout": {"visibility": "visible"}, "paint": {"fill-antialias": true, "fill-color": "hsl(220, 0%,90%)", "fill-opacity": {"stops": [[6, 0], [14, 0.5]]}}, "metadata": {}, "filter": ["all", ["==", "$type", "Polygon"], ["==", "class", "pier"]]}, {"id": "Pier road", "type": "line", "source": "maptiler_planet", "source-layer": "transportation", "layout": {"line-cap": "round", "line-join": "round", "visibility": "visible"}, "paint": {"line-color": "hsl(220, 0%,90%)", "line-opacity": {"stops": [[6, 0], [14, 0.5]]}, "line-width": {"base": 1.2, "stops": [[15, 1], [17, 4]]}}, "metadata": {}, "filter": ["all", ["==", "$type", "LineString"], ["in", "class", "pier"]]}, {"id": "Bridge", "type": "fill", "source": "maptiler_planet", "source-layer": "transportation", "layout": {"visibility": "visible"}, "paint": {"fill-antialias": true, "fill-color": "hsl(220, 0%,90%)", "fill-opacity": {"stops": [[6, 0], [14, 0.5]]}}, "metadata": {}, "filter": ["all", ["==", "$type", "Polygon"], ["in", "brunnel", "bridge"]]}, {"id": "Road network", "type": "line", "source": "maptiler_planet", "source-layer": "transportation", "minzoom": 6, "layout": {"line-cap": "butt", "line-join": "round", "visibility": "visible"}, "paint": {"line-color": "hsl(220, 0%,90%)", "line-opacity": {"stops": [[6, 0], [14, 1]]}, "line-width": ["interpolate", ["linear", 2], ["zoom"], 5, 0.5, 6, ["match", ["get", "class"], ["motorway"], ["match", ["get", "brunnel"], ["bridge"], 0, 1], ["trunk", "primary"], 0, 0], 10, ["match", ["get", "class"], ["motorway"], ["match", ["get", "ramp"], 1, 0, 2.5], ["trunk", "primary"], 1.5, 1], 12, ["match", ["get", "class"], ["motorway"], ["match", ["get", "ramp"], 1, 1, 4], ["trunk"], 2.5, ["primary"], 2.5, ["secondary", "tertiary"], 1.5, ["minor", "service", "track"], 0, 1], 14, ["match", ["get", "class"], ["motorway"], ["match", ["get", "ramp"], 1, 5, 6], ["trunk"], 3, ["primary"], 5, ["secondary"], 4, ["tertiary"], 3, ["minor", "service", "track"], 2, 2], 16, ["match", ["get", "class"], ["motorway", "trunk", "primary"], 8, ["secondary"], 7, ["tertiary"], 6, ["minor", "service", "track"], 4, 4], 20, ["match", ["get", "class"], ["motorway", "trunk", "primary"], 24, ["secondary"], 24, ["tertiary"], 24, ["minor", "service", "track"], 16, 16]]}, "metadata": {}, "filter": ["all", ["!=", "brunnel", "tunnel"], ["!in", "class", "ferry", "rail", "transit", "pier", "bridge", "path", "aerialway", "motorway_construction", "trunk_construction", "primary_construction", "secondary_construction", "tertiary_construction", "minor_construction", "service_construction", "track_construction"]]}, {"id": "Path minor", "type": "line", "source": "maptiler_planet", "source-layer": "transportation", "minzoom": 14, "layout": {"line-cap": "round", "line-join": "round", "visibility": "visible"}, "paint": {"line-color": "hsl(220, 0%, 25%)", "line-dasharray": [0.5, 2], "line-opacity": 1, "line-width": ["interpolate", ["linear"], ["zoom"], 14, 0.7, 16, 0.8, 18, 1, 22, 2]}, "filter": ["all", ["in", "class", "path_pedestrian"], ["!has", "brunnel"]]}, {"id": "Path", "type": "line", "source": "maptiler_planet", "source-layer": "transportation", "minzoom": 14, "layout": {"line-cap": "round", "line-join": "round", "visibility": "visible"}, "paint": {"line-color": "hsl(220, 0%, 25%)", "line-dasharray": [0.5, 2], "line-opacity": 1, "line-width": ["interpolate", ["linear"], ["zoom"], 14, 0.7, 16, 0.8, 18, 1, 22, 2]}, "filter": ["all", ["in", "class", "path", "track"], ["!has", "brunnel"]]}, {"id": "Railway", "type": "line", "source": "maptiler_planet", "source-layer": "transportation", "minzoom": 10, "layout": {"line-join": "round", "visibility": "visible"}, "paint": {"line-color": "hsl(220, 0%, 80%)", "line-width": {"base": 1.3, "stops": [[13, 0.5], [14, 1], [15, 1], [16, 3], [21, 7]]}}, "filter": ["all", ["==", "class", "rail"], ["!=", "brunnel", "tunnel"]]}, {"id": "Railway dash", "type": "line", "source": "maptiler_planet", "source-layer": "transportation", "minzoom": 15, "layout": {"line-join": "round", "visibility": "visible"}, "paint": {"line-color": "hsl(220, 0%, 100%)", "line-dasharray": [2, 2], "line-width": {"base": 1.3, "stops": [[15, 0.5], [16, 1], [20, 5]]}}, "filter": ["all", ["==", "class", "rail"], ["!=", "brunnel", "tunnel"]]}, {"id": "Building", "type": "fill", "source": "maptiler_planet", "source-layer": "building", "layout": {"visibility": "visible"}, "paint": {"fill-color": ["interpolate", ["linear"], ["zoom"], 13, "hsl(0, 0%,70%)", 17, "hsl(0, 0%, 0%)"]}}, {"id": "Building top", "type": "fill", "source": "maptiler_planet", "source-layer": "building", "layout": {"visibility": "visible"}, "paint": {"fill-color": ["interpolate", ["linear"], ["zoom"], 13, "hsl(220, 0%, 70%)", 17, "hsl(220, 0%, 0%)"], "fill-opacity": {"base": 1, "stops": [[13, 0], [16, 1]]}, "fill-outline-color": "hsl(220, 0%,30%)", "fill-translate": {"base": 1, "stops": [[14, [0, 0]], [16, [-2, -2]]]}}}, {"id": "Other border", "type": "line", "source": "maptiler_planet", "source-layer": "boundary", "minzoom": 5, "maxzoom": 8, "layout": {"visibility": "visible"}, "paint": {"line-color": "hsl(220, 0%, 10%)", "line-opacity": 1, "line-width": ["interpolate", ["linear", 1], ["zoom"], 5, ["case", ["<=", ["get", "admin_level"], 6], 0.4, 0.2], 8, ["case", ["<=", ["get", "admin_level"], 6], 0.6, 0.4], 12, ["case", ["<=", ["get", "admin_level"], 6], 1.5, 1], 16, ["case", ["<=", ["get", "admin_level"], 6], 2, 1.5], 22, ["case", ["<=", ["get", "admin_level"], 6], 5, 3]]}, "filter": ["all", [">=", "admin_level", 3], ["==", "maritime", 0]]}, {"id": "Other border dash", "type": "line", "source": "maptiler_planet", "source-layer": "boundary", "minzoom": 8, "layout": {"visibility": "visible"}, "paint": {"line-color": "hsl(220, 0%, 10%)", "line-dasharray": {"stops": [[8, [3, 1]], [12, [2, 2, 6, 2]]]}, "line-opacity": 1, "line-width": ["interpolate", ["linear", 1], ["zoom"], 8, ["case", ["<=", ["get", "admin_level"], 6], 0.75, 0], 12, ["case", ["<=", ["get", "admin_level"], 6], 1.5, 1], 16, ["case", ["<=", ["get", "admin_level"], 6], 2, 1.5], 22, ["case", ["<=", ["get", "admin_level"], 6], 5, 3]]}, "filter": ["all", [">=", "admin_level", 4], ["==", "maritime", 0]]}, {"id": "Disputed border", "type": "line", "source": "maptiler_planet", "source-layer": "boundary", "minzoom": 0, "layout": {"line-cap": "round", "line-join": "round", "visibility": "visible"}, "paint": {"line-color": "hsl(220, 0%, 0%)", "line-dasharray": [2, 3], "line-opacity": 1, "line-width": ["interpolate", ["linear"], ["zoom"], 1, 0.2, 3, 0.5, 6, 0.75, 12, 3, 22, 4]}, "filter": ["all", ["==", "admin_level", 2], ["==", "maritime", 0], ["==", "disputed", 1]]}, {"id": "Country border", "type": "line", "source": "maptiler_planet", "source-layer": "boundary", "minzoom": 0, "layout": {"line-cap": "round", "line-join": "round", "visibility": "visible"}, "paint": {"line-color": "hsl(220, 0%, 0%)", "line-opacity": 1, "line-width": ["interpolate", ["linear"], ["zoom"], 1, 0.2, 3, 0.5, 6, 0.75, 12, 3, 22, 4]}, "filter": ["all", ["==", "admin_level", 2], ["==", "maritime", 0], ["==", "disputed", 0]]}, {"id": "Ocean labels", "type": "symbol", "source": "maptiler_planet", "source-layer": "water_name", "minzoom": 0, "maxzoom": 4, "layout": {"symbol-placement": "point", "text-allow-overlap": false, "text-field": "{name:en}", "text-font": ["Metropolis Semi Bold Italic", "Noto Sans Bold"], "text-ignore-placement": false, "text-letter-spacing": 0.1, "text-line-height": 1.2, "text-max-width": 6, "text-padding": 2, "text-pitch-alignment": "auto", "text-rotation-alignment": "auto", "text-size": ["interpolate", ["linear", 1], ["zoom"], 1, 11, 4, 14], "text-transform": "uppercase", "visibility": "visible"}, "paint": {"text-color": "hsl(344, 0%, 69%)", "text-opacity": 1}, "filter": ["all", ["==", "class", "ocean"], ["has", "name"]]}, {"id": "Sea labels", "type": "symbol", "source": "maptiler_planet", "source-layer": "water_name", "minzoom": 3, "layout": {"symbol-placement": "point", "text-allow-overlap": false, "text-field": ["coalesce", ["get", "name:en"], ["get", "name"]], "text-font": ["Metropolis Semi Bold Italic", "Noto Sans Bold"], "text-ignore-placement": false, "text-letter-spacing": 0.1, "text-line-height": 1.2, "text-max-width": 6, "text-padding": 2, "text-pitch-alignment": "auto", "text-rotation-alignment": "auto", "text-size": ["interpolate", ["linear", 1], ["zoom"], 3, 9, 9, 16, 14, 20], "text-transform": "uppercase", "visibility": "visible"}, "paint": {"text-color": "hsl(344, 0%, 69%)", "text-opacity": 1}, "filter": ["all", ["==", "class", "sea"], ["has", "name"]]}, {"id": "Lakeline labels", "type": "symbol", "source": "maptiler_planet", "source-layer": "water_name", "layout": {"symbol-placement": "line", "symbol-spacing": 350, "text-field": ["coalesce", ["get", "name:en"], ["get", "name"]], "text-font": ["Metropolis Semi Bold Italic", "Noto Sans Bold"], "text-line-height": 1.2, "text-pitch-alignment": "auto", "text-rotation-alignment": "auto", "text-size": ["interpolate", ["linear"], ["zoom"], 9, 12, 14, 16, 18, 20], "visibility": "visible"}, "paint": {"text-color": "hsl(344, 0%, 69%)"}, "filter": ["all", ["has", "name"], ["==", "$type", "LineString"]]}, {"id": "Road labels", "type": "symbol", "source": "maptiler_planet", "source-layer": "transportation_name", "minzoom": 14, "layout": {"symbol-avoid-edges": false, "symbol-placement": "line", "symbol-spacing": {"stops": [[6, 200], [16, 250], [20, 250], [22, 600]]}, "text-field": ["coalesce", ["get", "name:en"], ["get", "name"]], "text-font": ["Metropolis Semi Bold", "Noto Sans Bold"], "text-justify": "center", "text-letter-spacing": ["interpolate", ["linear", 1], ["zoom"], 13, 0, 16, ["match", ["get", "class"], "primary", 0.2, 0.1]], "text-pitch-alignment": "auto", "text-rotation-alignment": "auto", "text-size": ["interpolate", ["linear"], ["zoom"], 14, ["match", ["get", "class"], ["primary"], 10, 7], 15, ["match", ["get", "class"], ["primary"], 10, ["secondary", "tertiary"], 9, 7], 16, ["match", ["get", "class"], ["primary", "secondary", "tertiary"], 11, 10], 18, ["match", ["get", "class"], ["primary", "secondary", "tertiary"], 13, 12]], "visibility": "visible"}, "paint": {"text-color": "hsl(220, 0%, 10%)", "text-halo-blur": 0, "text-halo-color": "hsl(220, 0%, 80%)", "text-halo-width": 0.5, "text-opacity": ["step", ["zoom"], 1, 14, ["match", ["get", "class"], ["primary"], 1, 0], 15, ["match", ["get", "class"], ["primary", "secondary", "tertiary"], 1, 0], 16, 1]}, "filter": ["in", "class", "primary", "secondary", "tertiary", "minor", "service"]}, {"id": "Place labels", "type": "symbol", "source": "maptiler_planet", "source-layer": "place", "minzoom": 13, "layout": {"symbol-sort-key": ["to-number", ["get", "rank"]], "text-anchor": "center", "text-field": "{name}", "text-font": ["Metropolis Semi Bold", "Noto Sans Bold"], "text-keep-upright": true, "text-max-width": 10, "text-offset": [0.2, 0.2], "text-size": ["interpolate", ["linear", 1], ["zoom"], 12, 10, 13, ["match", ["get", "class"], ["suburb", "neighborhood", "neighbourhood"], 12, 11], 14, ["match", ["get", "class"], ["suburb", "neighborhood", "neighbourhood"], 12, 11], 16, ["match", ["get", "class"], ["suburb", "neighborhood", "neighbourhood"], 14, 13]], "text-transform": "none", "visibility": "visible"}, "paint": {"text-color": "hsl(154, 0%, 100%)", "text-halo-blur": 0, "text-halo-color": "hsl(0, 0%, 0%)", "text-halo-width": 0.5, "text-opacity": ["step", ["zoom"], 0, 13, ["match", ["get", "class"], ["suburb", "neighborhood", "neighbourhood"], 1, 0], 14, 1]}, "filter": ["in", "class", "hamlet", "island", "isolated_dwelling", "neighbourhood", "quarter", "suburb", "place"]}, {"id": "Village labels", "type": "symbol", "source": "maptiler_planet", "source-layer": "place", "minzoom": 12, "layout": {"symbol-sort-key": ["to-number", ["get", "rank"]], "text-anchor": "center", "text-field": "{name}", "text-font": ["Metropolis Semi Bold", "Noto Sans Bold"], "text-keep-upright": true, "text-max-width": 10, "text-offset": [0.2, 0.2], "text-size": ["interpolate", ["linear", 1], ["zoom"], 12, 12, 13, 12, 14, 14, 16, 14], "text-transform": "none", "visibility": "visible"}, "paint": {"text-color": "hsl(154, 0%, 100%)", "text-halo-blur": 0, "text-halo-color": "hsl(0, 0%, 0%)", "text-halo-width": 0.5, "text-opacity": 1}, "filter": ["==", "class", "village"]}, {"id": "Town labels", "type": "symbol", "source": "maptiler_planet", "source-layer": "place", "minzoom": 11, "maxzoom": 16, "layout": {"symbol-sort-key": ["to-number", ["get", "rank"]], "text-anchor": "center", "text-field": ["coalesce", ["get", "name:en"], ["get", "name"]], "text-font": ["Metropolis Semi Bold", "Noto Sans Bold"], "text-keep-upright": true, "text-max-width": 10, "text-offset": [0.2, 0.2], "text-size": ["interpolate", ["linear"], ["zoom"], 10, 11, 13, 14, 14, 15], "text-transform": "none", "visibility": "visible"}, "paint": {"text-color": "hsl(154, 0%, 100%)", "text-halo-blur": 0, "text-halo-color": "hsl(0, 0%, 0%)", "text-halo-width": 0.5}, "filter": ["all", ["==", "class", "town"]]}, {"id": "State labels", "type": "symbol", "source": "maptiler_planet", "source-layer": "place", "minzoom": 5, "maxzoom": 8, "layout": {"symbol-sort-key": ["to-number", ["get", "rank"]], "text-allow-overlap": false, "text-field": ["coalesce", ["get", "name:en"], ["get", "name"]], "text-font": ["Metropolis Semi Bold", "Noto Sans Bold"], "text-max-width": 9, "text-size": ["interpolate", ["linear", 1], ["zoom"], 5, ["match", ["get", "rank"], 1, 10, 10], 11, ["match", ["get", "rank"], 1, 16, 16]], "text-transform": "uppercase", "visibility": "visible"}, "paint": {"text-color": "hsl(154, 0%, 100%)", "text-halo-blur": 0, "text-halo-color": "hsl(0, 0%, 0%)", "text-halo-width": 0.5, "text-opacity": ["step", ["zoom"], 0, 3, ["match", ["get", "rank"], 1, 0.85, 0], 4, ["case", ["<=", ["get", "rank"], 1], 0.75, 0], 5, ["case", ["<=", ["get", "rank"], 3], 0.75, 0], 8, ["match", ["get", "rank"], 4, 0.75, 0], 11, 0]}, "filter": ["all", ["==", "class", "state"], ["==", "rank", 1]]}, {"id": "City labels", "type": "symbol", "source": "maptiler_planet", "source-layer": "place", "minzoom": 5, "maxzoom": 14, "layout": {"symbol-sort-key": ["to-number", ["get", "rank"]], "text-anchor": "center", "text-field": "{name:en}", "text-font": ["Metropolis Semi Bold", "Noto Sans Bold"], "text-keep-upright": false, "text-max-width": 10, "text-offset": [0.2, 0.2], "text-size": ["interpolate", ["linear", 1], ["zoom"], 5, ["case", [">=", ["get", "rank"], 6], 11, 12], 9, ["case", [">=", ["get", "rank"], 6], 12, 14], 13, ["case", [">=", ["get", "rank"], 6], 16, 18], 14, ["case", [">=", ["get", "rank"], 6], 20, 22]], "text-transform": "none", "visibility": "visible"}, "paint": {"text-color": "hsl(154, 0%, 100%)", "text-halo-blur": 0, "text-halo-color": "hsl(0, 0%, 0%)", "text-halo-width": 0.5}, "filter": ["all", ["==", "class", "city"], ["has", "rank"]]}, {"id": "Country labels", "type": "symbol", "source": "maptiler_planet", "source-layer": "place", "minzoom": 2, "maxzoom": 10, "layout": {"symbol-sort-key": ["to-number", ["get", "rank"]], "text-field": "{name:en}", "text-font": ["Metropolis Bold", "Noto Sans Bold"], "text-size": ["interpolate", ["linear", 1], ["zoom"], 2, ["case", ["<=", ["get", "rank"], 2], 11, 0], 3, ["case", ["<=", ["get", "rank"], 2], 11, 9], 4, ["case", ["<=", ["get", "rank"], 2], 12, 10], 5, ["case", ["<=", ["get", "rank"], 2], 13, 11], 6, ["case", ["<=", ["get", "rank"], 2], 14, 12], 7, ["case", ["<=", ["get", "rank"], 2], 14, 13]], "text-transform": "uppercase", "visibility": "visible"}, "paint": {"text-color": "hsl(154, 0%, 100%)", "text-halo-blur": 0, "text-halo-color": "hsl(0, 0%, 0%)", "text-halo-width": 0.75, "text-opacity": ["step", ["zoom"], 1, 2, ["case", ["<=", ["get", "rank"], 2], 1, 0], 3, 1]}, "filter": ["all", ["==", "class", "country"], ["has", "iso_a2"], ["!=", "iso_a2", "VA"]]}, {"id": "Continent labels", "type": "symbol", "source": "maptiler_planet", "source-layer": "place", "minzoom": 0, "maxzoom": 2, "layout": {"text-field": "{name:en}", "text-font": ["Metropolis Bold", "Noto Sans Bold"], "text-justify": "center", "text-keep-upright": false, "text-letter-spacing": 0.1, "text-max-width": 9, "text-size": 13, "text-transform": "uppercase", "visibility": "visible"}, "paint": {"text-color": "hsl(154, 0%, 100%)", "text-halo-blur": 0, "text-halo-color": "hsl(0, 0%, 0%)", "text-halo-width": 0.5}, "filter": ["all", ["==", "class", "continent"]]}], "metadata": {"maptiler:copyright": "You are licensed to use the style or its derivate for serving map tiles exclusively with MapTiler Server or MapTiler Cloud and in accordance with their licenses and terms. If you plan to use the style in a different way, contact <NAME_EMAIL>."}, "glyphs": "https://api.maptiler.com/fonts/{fontstack}/{range}.pbf?key=oinoatcrNmdCL1524DOl", "sprite": "https://api.maptiler.com/maps/backdrop/sprite", "bearing": 0, "pitch": 0, "center": [0, 0], "zoom": 1}