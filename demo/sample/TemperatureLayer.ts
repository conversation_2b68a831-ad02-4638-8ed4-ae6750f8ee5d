import type { Map as MapSDK } from "@maptiler/sdk";
import { GradientColoringFragment } from "../../core/ColoringFragment";
import { ColorRamp } from "../../core/ColorRamp";
import { TileLayer } from "../TileLayer/TileLayer";
import type { UsedRgbChannel } from "../../utils";
import { WeatherDataHandler } from "../../core/WeatherDataHandler";
import type { TemperatureLayerOptions, TemperaturePickAt } from "./types";

/**
 * The TemperatureLayer shows the atmospheric temperature in centigrade
 */
export class TemperatureLayer extends TileLayer {
  private constructorOptions: TemperatureLayerOptions;
  private isSourceReady = false;
  private colorRamp!: ColorRamp;

  constructor(options: TemperatureLayerOptions = {}) {
    super(options.id || "MapTiler Temperature", null, null, null);
    this.constructorOptions = options;
  }

  async onAdd(map: MapSDK, gl: WebGLRenderingContext | WebGL2RenderingContext) {
    const apiKey = (map as MapSDK).getSdkConfig().apiKey;
    const maptilerSessionId = (map as MapSDK).getMaptilerSessionId();

    // Check if some data has been fetched already
    // and fetch if necessary
    if (!WeatherDataHandler.hasData()) {
      await WeatherDataHandler.fetchLatest(apiKey, maptilerSessionId);
    }

    // Get the latest temperature data
    const latestDataset = WeatherDataHandler.getTemperatureData();

    if (!latestDataset) throw new Error("The latest weather data is not avaialble");

    this.colorRamp = this.constructorOptions.colorramp ?? ColorRamp.builtin.TEMPERATURE_2;

    // Finish the initialization of the underlaying TileLayer
    this.init(
      {
        minZoom: latestDataset.metadata.minzoom,
        maxZoom: latestDataset.metadata.maxzoom,
        repaintOnPausedAnimation: false,
      },
      [
        new GradientColoringFragment({
          decode: {
            channel: latestDataset.metadata.weather_variable.decoding.channels.toLowerCase() as UsedRgbChannel,
            min: latestDataset.metadata.weather_variable.decoding.min,
            max: latestDataset.metadata.weather_variable.decoding.max,
          },
          stops: this.colorRamp,
          smooth: this.constructorOptions.smooth ?? true,
          opacity: this.constructorOptions.opacity ?? 1,
        }),
      ],
    );

    // Properly finish to add the layer to the map
    super.onAdd(map, gl);

    // Add all the keyframes
    const kfs = WeatherDataHandler.getSourcesAndTimestamps(latestDataset, apiKey, maptilerSessionId);

    for (const kf of kfs) {
      this.addSource(kf.timestamp, kf.source);
    }

    // Set the animation time to current time if possible
    const now = +new Date() / 1000; // in seconds
    if (now >= this.getAnimationStart() && now <= this.getAnimationEnd()) {
      this.setAnimationTime(now);
    }

    this.isSourceReady = true;
    this.emit("sourceReady", { map, layer: this });
  }

  /**
   * Check if the data source that fuels the layer has been fully fetched and processed
   * @returns
   */
  getIsSourceReady(): boolean {
    return this.isSourceReady;
  }

  /**
   * Get the temperature in multiple measurement units at a given location
   * @param lng
   * @param lat
   * @param options: bilinear interpolation (boolean) is disabled by default for better performance
   * @returns
   */
  pickAt(lng: number, lat: number, options: { bilinear?: boolean } = {}): TemperaturePickAt {
    const originalValue = super.pick(lng, lat, options);

    if (!originalValue) return null;

    const value = originalValue[0];

    return {
      value,
      valueImperial: 32 + (value * 9) / 5,
    };
  }

  /**
   * Get the color ramp in use by the layer
   * @returns
   */
  getColorRamp(): Readonly<ColorRamp> {
    return this.colorRamp;
  }

  /**
   * Async function corresponding to the "sourceReady" event.
   * Resolves directly if source is already ready, or awaits the "sourceReady"
   * event if not.
   */
  onSourceReadyAsync(): Promise<void> {
    return new Promise<void>((resolve) => {
      if (this.isSourceReady) {
        return resolve();
      }

      this.once("sourceReady", () => {
        resolve();
      });
    });
  }
}
