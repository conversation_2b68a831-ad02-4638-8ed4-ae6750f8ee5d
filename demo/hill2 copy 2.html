<!DOCTYPE html>
<html>
<head>
  <title>MapTiler Wind + Temperature (Fixed Boundaries)</title>
  <meta charset="utf-8" />
  <meta name="viewport" content="initial-scale=1,maximum-scale=1,user-scalable=no" />
  <script src="https://cdn.maptiler.com/maptiler-sdk-js/v3.2.0/maptiler-sdk.umd.min.js"></script>
  <link href="https://cdn.maptiler.com/maptiler-sdk-js/v3.2.0/maptiler-sdk.css" rel="stylesheet" />
  <script src="https://cdn.maptiler.com/maptiler-weather/v3.0.1/maptiler-weather.umd.min.js"></script>
  <script src="https://cdn.maptiler.com/maptiler-geocoding-control/v2.1.4/maptilersdk.umd.js"></script>
  <link href="https://cdn.maptiler.com/maptiler-geocoding-control/v2.1.4/style.css" rel="stylesheet">
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: sans-serif;
    }

    #map {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 100%;
      background-color: #444952;
      background-image: url('night-stars.webp');
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
    }

    #time-info {
      position: fixed;
      width: 60vw;
      bottom: 0;
      z-index: 1;
      margin: 10px;
      text-shadow: 0px 0px 5px black;
      color: white;
      font-size: 20px;
      font-weight: 500;
      text-align: center;
      left: 0;
      right: 0;
      margin: auto;
      padding: 20px;
    }

    #time-slider {
      width: 100%;
      height: fit-content;
      left: 0;
      right: 0;
      z-index: 1;
      filter: drop-shadow(0 0 7px #000a);
      margin-top: 10px;
    }

    #pointer-data {
      z-index: 1000;
      position: absolute;
      font-size: 14px;
      font-weight: 500;
      color: #fff;
      background: rgba(0, 0, 0, 0.85);
      padding: 6px 10px;
      border-radius: 4px;
      white-space: pre-line;
      pointer-events: none;
      display: none;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
      line-height: 1.4;
      text-align: left;
    }

    #pointer-data::after {
      content: '';
      position: absolute;
      bottom: 15px;
      left: -6px;
      width: 0;
      height: 0;
      border-top: 6px solid transparent;
      border-bottom: 6px solid transparent;
      border-right: 6px solid rgba(0, 0, 0, 0.85);
    }

    #variable-name {
      z-index: 1;
      position: fixed;
      font-size: 20px;
      font-weight: 500;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      color: #fff;
      text-shadow: 0px 0px 10px #0007;
    }

    .button {
      cursor: pointer;
      width: auto;
      padding: 8px;
      border-radius: 3px;
      margin: 10px 0 0 0;
      font-size: 12px;
      text-align: center;
      color: #fff;
      background: #3174ff;
      font-family: sans-serif;
      font-weight: bold;
    }

    #color-scale-container {
      position: fixed;
      right: 30px;
      top: 50%;
      transform: translateY(-50%);
      z-index: 10;
      display: flex;
      flex-direction: column;
      align-items: center;
      background: rgba(0,0,0,0.15);
      border-radius: 10px;
      padding: 12px 8px;
      box-shadow: 0 2px 8px #0003;
    }
    #color-scale-canvas {
      display: block;
      width: 32px;
      height: 256px;
      border-radius: 8px;
      margin-bottom: 8px;
      margin-top: 8px;
    }
    .scale-label {
      color: #fff;
      font-size: 15px;
      font-weight: bold;
      text-shadow: 0 1px 4px #000a;
      margin: 0 0 2px 0;
      user-select: none;
    }
    .scale-label.min {
      margin-bottom: 4px;
    }
    .scale-label.max {
      margin-top: 4px;
    }

    /* Tùy chỉnh chiều ngang của search box */
    .maplibregl-ctrl-geocoder {
      width: 310px !important; /* Thay đổi chiều ngang ở đây */
      min-width: 200px !important;
    }
    
    .maplibregl-ctrl-geocoder input {
      width: 100% !important;
    }

    /* Scene Management UI */
    #scene-controls {
      position: fixed;
      top: 20px;
      left: 20px;
      z-index: 1000;
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .scene-button {
      background: rgba(49, 116, 255, 0.9);
      color: white;
      border: none;
      padding: 8px 12px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 12px;
      font-weight: bold;
      min-width: 80px;
      transition: all 0.2s;
      box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .scene-button:hover {
      background: rgba(49, 116, 255, 1);
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }

    .scene-button.saved {
      background: rgba(76, 175, 80, 0.9);
    }

    .scene-button.saved:hover {
      background: rgba(76, 175, 80, 1);
    }

    .scene-button.active {
      background: rgba(255, 152, 0, 0.9);
      animation: pulse 1.5s infinite;
    }

    @keyframes pulse {
      0%, 100% { opacity: 0.9; }
      50% { opacity: 1; }
    }
  </style>
</head>

<body>
  <div id="time-info">
    <span id="time-text"></span>
    <button id="play-pause-bt" class="button">Play 3600x</button>
    <button id="toggle-temp-bt" class="button">Ẩn nhiệt độ</button>
    <button id="hillshade-toggle-bt" class="button">Ẩn bóng địa hình</button>
    <input type="range" id="time-slider" min="0" max="11" step="1">
  </div>

  <div id="variable-name">Temperature + Wind</div>
  <div id="pointer-data"></div>
  <div id="map"></div>

  <!-- Scene Management Controls -->
  <div id="scene-controls">
    <button class="scene-button" id="scene-1" onclick="loadScene(1)">Scene 1</button>
    <button class="scene-button" id="scene-2" onclick="loadScene(2)">Scene 2</button>
    <button class="scene-button" id="scene-3" onclick="loadScene(3)">Scene 3</button>
    <button class="scene-button" id="scene-4" onclick="loadScene(4)">Scene 4</button>
    <button class="scene-button" id="scene-5" onclick="loadScene(5)">Scene 5</button>
    <button class="scene-button" id="save-scene" onclick="saveCurrentScene()" style="background:rgba(255,193,7,0.9);">💾 Save</button>
    <button class="scene-button" id="clear-scenes" onclick="clearAllScenes()" style="background:rgba(244,67,54,0.9);margin-top:8px;">🗑️ Clear All</button>
  </div>

  <!-- Color scale UI -->
  <div id="color-scale-container">
    <canvas id="color-scale-canvas" width="32" height="256"></canvas>
    <div class="scale-label max" id="scale-max-label">40°C</div>
    <div class="scale-label min" id="scale-min-label">0°C</div>
  </div>

  <!-- Projection notification -->
  <div id="projection-notification" style="position:fixed;top:80px;right:15px;z-index:1000;background:rgba(255,107,107,0.95);color:#fff;padding:12px 16px;border-radius:8px;font-size:14px;display:none;max-width:250px;box-shadow:0 4px 12px rgba(0,0,0,0.3);border:1px solid #ff6b6b;">
    ⚠️ Globe 3D tốn hiệu năng GPU đáng kể!
  </div>

  <!-- Location panel -->
  <div id="location-panel" style="position:fixed;top:328px;right:110px;z-index:900;background:rgba(255,255,255,0.95);color:#333;padding:12px;border-radius:10px;font-size:14px;min-width:180px;max-width:220px;box-shadow:0 4px 16px rgba(0,0,0,0.2);border:1px solid #ddd;display:none;">
    <div style="font-weight:bold;margin-bottom:8px;color:#666;font-size:12px;">📍 Khu vực lân cận tâm viewport</div>
    <div id="location-list"></div>
  </div>

  <!-- Location marker popup -->
  <div id="location-popup" style="position:absolute;background:rgba(255,255,255,0.95);color:#333;padding:8px 12px;border-radius:8px;font-size:14px;box-shadow:0 2px 8px rgba(0,0,0,0.3);border:1px solid #ddd;display:none;z-index:1000;max-width:200px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;">
    <div id="location-popup-text"></div>
  </div>
  
  <script>
    // --- DOM Elements ---
    const timeInfoContainer = document.getElementById("time-info");
    const timeTextDiv = document.getElementById("time-text");
    const timeSlider = document.getElementById("time-slider");
    const playPauseButton = document.getElementById("play-pause-bt");
    const toggleTempButton = document.getElementById("toggle-temp-bt");
    const pointerDataDiv = document.getElementById("pointer-data");
    const colorScaleCanvas = document.getElementById('color-scale-canvas');
    const scaleMinLabel = document.getElementById('scale-min-label');
    const scaleMaxLabel = document.getElementById('scale-max-label');
    const hillshadeToggleButton = document.getElementById('hillshade-toggle-bt');
    const projectionNotification = document.getElementById('projection-notification');
    const locationPanel = document.getElementById('location-panel');
    const locationList = document.getElementById('location-list');
    const locationPopup = document.getElementById('location-popup');
    const locationPopupText = document.getElementById('location-popup-text');

    // --- State Variables ---
    let pointerLngLat = null;
    let tempLayerVisible = true;
    let isPlaying = false;
    let mapLoaded = false;
    let colorScale = [];
    const TEMP_LAYER_ID = 'temperature-layer';
    let hillshadeVisible = true;
    let weatherLayersAdded = false;
    let locationUpdateTimeout = null;
    let currentLocationMarker = null;
    
    // Scene Management System
    let scenes = {};
    let currentSceneIndex = null;
    let selectedSceneToSave = null;

    // --- Tạo color scale mặc định ---
    colorScale = [
      // Dải màu cực lạnh: Từ trắng đến xanh nhạt
      { min: -73.3, max: -51.1, color: '#E5F0FF' },
      { min: -51.1, max: -48.3, color: '#DBE9FB' },
      { min: -48.3, max: -45.6, color: '#D3E2F7' },
      { min: -45.6, max: -42.8, color: '#CCDBF4' },
      { min: -42.8, max: -40.0, color: '#C0D4ED' },
      { min: -40.0, max: -37.2, color: '#B8CDEA' },
      { min: -37.2, max: -34.4, color: '#AFC6E7' },
      { min: -34.4, max: -31.7, color: '#A7BFE3' },
      { min: -31.7, max: -28.9, color: '#9CB8DE' },
      { min: -28.9, max: -26.1, color: '#93B1D7' },
      { min: -26.1, max: -23.3, color: '#89A5CD' },
      
      // Dải màu lạnh: Bắt đầu từ Xanh da trời nhạt, đậm dần thành Xanh dương
      { min: -23.3, max: -20.6, color:  '#04062E'},
      { min: -20.6, max: -17.8, color:  '#060A3F'},
      { min: -17.8, max: -15.0, color:  '#091057'},
      { min: -15.0, max: -12.2, color:  '#0D1871'}, // Xanh da trời
      { min: -12.2, max: -9.4, color:  '#11228B'},
      { min: -9.4, max: -6.7, color:  '#162DA6'},
      { min: -6.7, max: -3.9, color:  '#1C3AC2'},
      { min: -3.9, max: -1.1, color:  '#2348DE'}, 
      { min: -1.1, max: 1.7, color:  '#2F5BF0'},
      { min: 1.7, max: 4.4, color:  '#3B6EFF'},
      { min: 4.4, max: 7.2, color:  '#4981FF'},
      { min: 7.2, max: 10.0, color:  '#5793FF'},

      { min: 10.0, max: 12.8, color: '#66A6FF' }, // Xanh ngọc (Tile/Teal)
      { min: 12.8, max: 15.6, color:  '#A2D9D4'}, // Xanh lá cây pastel

      { min: 15.6, max: 18.3, color: '#B3E6B5' }, // Vàng chanh nhạt
      { min: 18.3, max: 21.1, color: '#FFE67F' },
      { min: 21.1, max: 22.5, color: '#FFE066' }, // Vàng tươi sáng nhất
      { min: 22.5, max: 23.9, color: '#FFD957' }, // Vàng tươi
      { min: 23.9, max: 25.2, color: '#FFD148' }, // Vàng hơi đậm
      { min: 25.2, max: 26.6, color: '#FFC83C' }, // Vàng cam nhạt
      { min: 26.6, max: 28.0, color: '#FFB82F' }, // Vàng cam
      { min: 28.0, max: 29.4, color: '#FFB52A' }, // Vàng cam đậm
      { min: 29.4, max: 30.8, color: '#FF9F1C' }, // Cam sáng
      { min: 30.8, max: 32.2, color: '#FF931A' }, // Cam
      { min: 32.2, max: 33.6, color: '#F98611' }, // Cam đậm
      { min: 33.6, max: 35.0, color: '#F67F0C' }, // Cam đỏ nhạt
      { min: 35.0, max: 36.4, color: '#F26A0A' }, // Cam đỏ
      { min: 36.4, max: 37.8, color: '#EE5808' }, // Cam đỏ đậm
      { min: 37.8, max: 40.6, color: '#E94E06' }, // Cam đỏ
      { min: 40.6, max: 43.3, color: '#DE3103' },
      { min: 43.3, max: 46.1, color: '#D21502' }, // Đỏ tươi
      { min: 46.1, max: 48.9, color: '#B70E02' },
      { min: 48.9, max: 65.6, color: '#8B0000' }  // Đỏ thẫm (Maroon)
    ];

    // --- MapTiler Initialization ---
    maptilersdk.config.apiKey = 'oinoatcrNmdCL1524DOl';

    const map = new maptilersdk.Map({
      container: document.getElementById('map'),
      hash: true,
      zoom: 2,
      center: [0, 40],
      style: maptilersdk.MapStyle.BACKDROP,
      projectionControl: true,
      projection: 'mercator'
    });

    // Thêm Geocoding Control - di chuyển vào sau khi map load
    let gc;
    
    // Hàm tạo location marker
    function createLocationMarker(lngLat, locationName) {
        // Xóa marker cũ nếu có
        if (currentLocationMarker) {
            map.removeLayer(currentLocationMarker.id);
            map.removeSource(currentLocationMarker.id);
        }
        
        // Tạo marker mới
        const markerId = 'location-marker-' + Date.now();
        
        // Thêm source cho marker
        map.addSource(markerId, {
            type: 'geojson',
            data: {
                type: 'Feature',
                geometry: {
                    type: 'Point',
                    coordinates: [lngLat.lng, lngLat.lat]
                },
                properties: {
                    name: locationName
                }
            }
        });
        
        // Thêm layer cho marker
        map.addLayer({
            id: markerId,
            type: 'circle',
            source: markerId,
            paint: {
                'circle-radius': 8,
                'circle-color': '#FF4444',
                'circle-stroke-color': '#FFFFFF',
                'circle-stroke-width': 2
            }
        });
        
        // Thêm layer cho icon
        map.addLayer({
            id: markerId + '-icon',
            type: 'symbol',
            source: markerId,
            layout: {
                'text-field': '📍',
                'text-size': 16,
                'text-offset': [0, -0.5]
            }
        });
        
        currentLocationMarker = { id: markerId, lngLat: lngLat, name: locationName };
        
        // Hiển thị popup
        showLocationPopup(lngLat, locationName);
        
        console.log(`📍 Location marker created at: ${locationName} (${lngLat.lng.toFixed(4)}, ${lngLat.lat.toFixed(4)})`);
    }
    
    // Hàm hiển thị popup
    function showLocationPopup(lngLat, locationName) {
        const point = map.project(lngLat);
        locationPopupText.textContent = locationName;
        locationPopup.style.left = point.x + 'px';
        locationPopup.style.top = (point.y - 40) + 'px';
        locationPopup.style.display = 'block';
        
        // Tự động ẩn sau 3 giây
        setTimeout(() => {
            locationPopup.style.display = 'none';
        }, 3000);
    }

    // --- Layer Initialization ---
    let layerBg;
    
    const layer = new maptilerweather.WindLayer({
      id: "Wind Particles",
      colorramp: maptilerweather.ColorRamp.builtin.NULL,
      speed: 0.002, fadeFactor: 0.03, maxAmount: 256, density: 400,
      color: [255, 0, 0, 30], fastColor: [0, 255, 0, 100],
    });

    // ===== HÀM CHÍNH ĐỂ ĐẢMBẢO RANH GIỚI LUÔN HIỂN THỊ =====
    function ensureBoundariesOnTop() {
        if (!map.isStyleLoaded()) return;

        const layers = map.getStyle().layers;
        // console.log('All layers:', layers.map(l => ({ id: l.id, type: l.type })));
        
        // Tìm TẤT CẢ layer có thể là ranh giới hoặc nhãn
        const boundaryAndLabelLayers = layers.filter(layer => {
            const id = layer.id.toLowerCase();
            const type = layer.type;
            
            // Điều kiện mở rộng để bắt tất cả các loại ranh giới và nhãn
            return (
                // Các layer ranh giới
                id.includes('boundary') || 
                id.includes('admin') || 
                id.includes('border') || 
                id.includes('country') || 
                id.includes('state') || 
                id.includes('province') ||
                id.includes('outline') ||
                id.includes('stroke') ||
                // Các layer nhãn
                type === 'symbol' ||
                id.includes('label') || 
                id.includes('text') || 
                id.includes('place') ||
                id.includes('city') ||
                id.includes('town') ||
                // Các layer line có thể là ranh giới
                (type === 'line' && (
                    id.includes('line') || 
                    id.includes('admin') || 
                    id.includes('boundary')
                ))
            );
        });

        // console.log('Boundary and label layers found:', boundaryAndLabelLayers.map(l => l.id));

        // Di chuyển tất cả lên trên cùng, giữ nguyên thứ tự tương đối
        boundaryAndLabelLayers.forEach(layer => {
            try {
                if (map.getLayer(layer.id)) {
                    // Xóa fill color cho các layer fill/polygon
                    if (layer.type === 'fill') {
                        map.setPaintProperty(layer.id, 'fill-opacity', 0);
                        map.setPaintProperty(layer.id, 'fill-color', 'transparent');
                        // Đảm bảo stroke hiển thị
                        if (map.getPaintProperty(layer.id, 'fill-outline-color')) {
                            map.setPaintProperty(layer.id, 'fill-outline-color', '#666666');
                        }
                        // console.log(`Removed fill from layer ${layer.id}`);
                    }
                    
                    map.moveLayer(layer.id);
                    // console.log(`Moved layer ${layer.id} to top`);
                }
            } catch (e) {
                console.log(`Failed to move layer ${layer.id}:`, e.message);
            }
        });
    }

    // Tạo Hàm riêng để đảm bảo borders luôn ở trên
    function ensureAllBordersOnTop() {
        if (!mapLoaded || !map.isStyleLoaded()) return;
        
        setTimeout(() => {
            ensureBoundariesOnTop();
            addCustomBorderLayers();
            console.log('All borders moved to top');
        }, 100);
    }

    // ===== HÀM THÊM BORDER LAYER RIÊNG =====
    function addCustomBorderLayers() {
        if (!map.isStyleLoaded()) return;
        
        try {
            // Thêm source cho ranh giới từ MapTiler Countries
            if (!map.getSource('maptiler-countries')) {
                map.addSource('maptiler-countries', {
                    type: 'vector',
                    url: `https://api.maptiler.com/tiles/countries/tiles.json?key=${maptilersdk.config.apiKey}`
                });
                console.log('Added MapTiler Countries source');
            }

            // 1. Thêm ranh giới quốc gia (admin_level=2)
            if (!map.getLayer('country-boundaries')) {
                map.addLayer({
                    id: 'country-boundaries',
                    type: 'line',
                    source: 'maptiler-countries',
                    'source-layer': 'boundary',
                    filter: ['==', 'admin_level', 2],
                    paint: {
                        'line-color': '#444444',
                        'line-width': [
                            'interpolate',
                            ['linear'],
                            ['zoom'],
                            0, 0.5,
                            4, 1,
                            8, 1.5,
                            12, 2
                        ],
                        'line-opacity': 0.8
                    }
                });
                console.log('Added country boundaries layer');
            }

            // 2. Thêm ranh giới cấp 1 (admin_level=4 - states/provinces)
            if (!map.getLayer('state-boundaries')) {
                map.addLayer({
                    id: 'state-boundaries',
                    type: 'line',
                    source: 'maptiler-countries',
                    'source-layer': 'boundary',
                    filter: ['==', 'admin_level', 4],
                    paint: {
                        'line-color': '#666666',
                        'line-width': [
                            'interpolate',
                            ['linear'],
                            ['zoom'],
                            4, 0.3,
                            8, 0.8,
                            12, 1.2
                        ],
                        'line-opacity': 0.6,
                        'line-dasharray': [2, 2]
                    }
                });
                console.log('Added state boundaries layer');
            }

            // 3. Đảm bảo các border layer luôn ở trên cùng
            setTimeout(() => {
                if (map.getLayer('state-boundaries')) {
                    map.moveLayer('state-boundaries');
                }
                if (map.getLayer('country-boundaries')) {
                    map.moveLayer('country-boundaries');
                }
                // console.log('Moved custom border layers to top');
            }, 100);

        } catch (error) {
            console.error('Error adding custom border layers:', error);
        }
    }



    // Hàm thêm custom layers
    function addCustomLayers() {
        try {
            // 1. Kiểm tra và thêm DEM source nếu chưa có
            if (!map.getSource('dem')) {
                map.addSource('dem', {
                    type: 'raster-dem',
                    url: `https://api.maptiler.com/tiles/terrain-rgb/tiles.json?key=${maptilersdk.config.apiKey}`,
                    tileSize: 256, 
                    maxzoom: 12
                });
            }

            // 2. Kiểm tra và thêm hillshade layers nếu chưa có
            if (!map.getLayer('hillshade-shadows')) {
                map.addLayer({
                    id: 'hillshade-shadows', 
                    type: 'hillshade', 
                    source: 'dem',
                    layout: { visibility: hillshadeVisible ? 'visible' : 'none' },
                    paint: { 
                        'hillshade-exaggeration': 0.65, 
                        'hillshade-illumination-direction': 315, 
                        'hillshade-highlight-color': 'rgba(0, 0, 0, 0)', 
                        'hillshade-shadow-color': '#252525' 
                    }
                });
            }
            
            if (!map.getLayer('hillshade-highlights')) {
                map.addLayer({
                    id: 'hillshade-highlights', 
                    type: 'hillshade', 
                    source: 'dem',
                    layout: { visibility: hillshadeVisible ? 'visible' : 'none' },
                    paint: { 
                        'hillshade-exaggeration': 0.65, 
                        'hillshade-illumination-direction': 315, 
                        'hillshade-shadow-color': 'rgba(0, 0, 0, 0)', 
                        'hillshade-highlight-color': '#FFFFFF', 
                        'hillshade-opacity': 0.2 
                    }
                });
            }

            // 3. Thêm temperature layer
            updateTemperatureColorRamp();

            // 4. Thêm wind layer nếu chưa có - QUAN TRỌNG: Đảm bảo luôn thêm
            if (!map.getLayer(layer.id)) {
                map.addLayer(layer);
                console.log('Wind layer added to map');
            } else {
                console.log('Wind layer already exists');
            }

            setTimeout(() => {
              console.log('Auto-clicking hillshade button...');
              
              // Click 2 lần để simulate user behavior
              hillshadeToggleButton.click(); // Ẩn
              setTimeout(() => {
                  hillshadeToggleButton.click(); // Hiện lại
                  mapLoaded = true;
                  console.log('Auto-click completed');
              }, 200);
              
          }, 500);
              

            // // 5. QUAN TRỌNG: Đảm bảo ranh giới lên trên
            // ensureBoundariesOnTop();

            // console.log('All custom layers added successfully');

            // // Cuối cùng đảm bảo borders lên trên
            // setTimeout(() => {
            //     ensureAllBordersOnTop();
            //     mapLoaded = true;
            // }, 300);
            
        } catch (error) {
            console.error('Error adding custom layers:', error);
        }
    }

    // --- Map Events ---
    map.on('load', function () {
        console.log('Map loaded');
        try {
            // Xử lý Water layer giống wind_temp.html
            map.setPaintProperty("Water", 'fill-color', "rgba(0, 0, 0, 0.6)");
        } catch (error) { 
            console.log('Water layer styling error:', error); 
        }
        
        // Thêm Geocoding Control sau khi map load
        gc = new maptilersdkMaptilerGeocoder.GeocodingControl({});
        map.addControl(gc, 'top-right');
        
        // Lắng nghe sự kiện khi user chọn địa điểm từ search box
        gc.on('result', function(e) {
            const result = e.result;
            const lngLat = result.center;
            const locationName = result.place_name || result.text;
            
            console.log('🔍 Search result selected:', locationName, lngLat);
            
            // Tạo marker cho địa điểm được chọn
            createLocationMarker(lngLat, locationName);
        });
        
        // Khởi tạo temperature layer ngay lập tức
        mapLoaded = true;
        if (colorScale.length) {
            updateTemperatureColorRamp();
        }
    });

    // Sử dụng event 'idle' để đảm bảo map đã sẵn sàng hoàn toàn
    map.on('idle', function() {
        if (!weatherLayersAdded && map.isStyleLoaded()) {
            weatherLayersAdded = true;
            console.log('Map is idle and style loaded, adding custom layers...');
            
            // Delay nhỏ để đảm bảo style đã ổn định hoàn toàn
            setTimeout(() => {
                addCustomLayers();
            }, 100);
        }
    });

    // Backup: Thêm wind layer nếu idle không trigger
    setTimeout(() => {
        if (!weatherLayersAdded && map.isStyleLoaded()) {
            weatherLayersAdded = true;
            console.log('Backup: Adding custom layers after timeout...');
            addCustomLayers();
        }
    }, 2000); // 2 giây backup

    // Event để đảm bảo ranh giới luôn hiển thị khi style thay đổi
    map.on('styledata', function() {
        if (mapLoaded) {
            setTimeout(() => {
                ensureBoundariesOnTop();
                addCustomBorderLayers();
            }, 50);
        }
    });

    layer.on("sourceReady", () => {
        console.log('Wind layer source ready');
        
        // Cập nhật timeline ngay lập tức
        const startDate = layer.getAnimationStartDate();
        const endDate = layer.getAnimationEndDate();
        timeSlider.min = +startDate;
        timeSlider.max = +endDate;
        
        // Đặt về thời gian hiện tại
        const currentTime = Date.now();
        if (currentTime >= startDate && currentTime <= endDate) {
            layer.setAnimationTime(currentTime / 1000);
            if (layerBg) layerBg.setAnimationTime(currentTime / 1000);
            timeSlider.value = currentTime;
        } else {
            // Nếu thời gian hiện tại không trong range, đặt về đầu
            layer.setAnimationTime(startDate / 1000);
            if (layerBg) layerBg.setAnimationTime(startDate / 1000);
            timeSlider.value = startDate;
        }
        
        refreshTimeUI();
        console.log('Timeline initialized with current time');
    });

    // --- UI Event Listeners ---
    let timeSliderTimeout = null;
    
    timeSlider.addEventListener("input", () => {
      const time = parseInt(timeSlider.value);
      
      // Hủy timeout cũ nếu user vẫn đang kéo
      if (timeSliderTimeout) {
        clearTimeout(timeSliderTimeout);
      }
      
      // Chỉ update UI text ngay lập tức, không load data
      const d = new Date(time);
      if (d) {
        timeTextDiv.innerText = d.toString();
      }
      
      // Đặt timeout mới - chỉ load data khi dừng kéo > 1 giây
      timeSliderTimeout = setTimeout(() => {
        console.log('Loading data for time:', new Date(time).toString());
        layer.setAnimationTime(time / 1000);
        if (layerBg) layerBg.setAnimationTime(time / 1000);
        
        // Update pointer data với thời gian mới
        updatePointerValue(pointerLngLat);
        
        timeSliderTimeout = null;
      }, 1000); // Đợi 1 giây sau khi dừng kéo (timeline loading)
    });

    playPauseButton.addEventListener("click", () => {
      isPlaying = !isPlaying;
      if (isPlaying) {
        layer.animateByFactor(3600); 
        if (layerBg) layerBg.animateByFactor(3600);
        playPauseButton.innerText = "Pause";
      } else {
        layer.animateByFactor(0); 
        if (layerBg) layerBg.animateByFactor(0);
        playPauseButton.innerText = "Play 3600x";
      }
    });

    toggleTempButton.addEventListener("click", () => {
      tempLayerVisible = !tempLayerVisible;
      if (layerBg) {
        const visibility = tempLayerVisible ? 'visible' : 'none';
        map.setLayoutProperty(TEMP_LAYER_ID, 'visibility', visibility);
        toggleTempButton.innerText = tempLayerVisible ? "Ẩn nhiệt độ" : "Hiện nhiệt độ";
      }
    });

    hillshadeToggleButton.addEventListener('click', function() {
      hillshadeVisible = !hillshadeVisible;
      const visibility = hillshadeVisible ? 'visible' : 'none';
      if (map.getLayer('hillshade-shadows')) { 
        map.setLayoutProperty('hillshade-shadows', 'visibility', visibility); 
      }
      if (map.getLayer('hillshade-highlights')) { 
        map.setLayoutProperty('hillshade-highlights', 'visibility', visibility); 
      }
      hillshadeToggleButton.innerText = hillshadeVisible ? 'Ẩn bóng địa hình' : 'Hiện bóng địa hình';
    });

    // Lắng nghe sự kiện thay đổi projection từ icon có sẵn
    let currentProjection = 'mercator';
    
    // Theo dõi thay đổi projection
    setInterval(() => {
      const newProjection = map.getProjection();
      if (newProjection !== currentProjection) {
        currentProjection = newProjection;
        if (newProjection && newProjection !== 'mercator') {
          // Hiện notification khi chuyển sang projection khác mercator (như globe)
          projectionNotification.style.display = 'block';
          setTimeout(() => {
            projectionNotification.style.display = 'none';
          }, 5000); // Tự tắt sau 5 giây
        }
      }
    }, 100); // Kiểm tra mỗi 100ms

    // Mouse tracking for tooltip
    map.on('mousemove', (e) => {
      updatePointerValue(e.lngLat);
      
      // Update tooltip position
      const point = map.project(e.lngLat);
      pointerDataDiv.style.left = (point.x + 15) + 'px'; // Offset 15px from cursor
      pointerDataDiv.style.top = (point.y - 35) + 'px';  // Offset 35px above cursor
      pointerDataDiv.style.display = 'block';
    });
    
    // Hide tooltip when mouse leaves map
    map.on('mouseleave', () => {
      pointerDataDiv.style.display = 'none';
    });
    
    // Hide tooltip when hovering over UI elements
    const uiElements = [
      timeInfoContainer,
      document.getElementById('scene-controls'),
      document.getElementById('color-scale-container'),
      document.getElementById('location-panel'),
      document.querySelector('.maplibregl-ctrl-top-right'), // Geocoding control
      document.querySelector('.maplibregl-ctrl-top-left'),  // Other controls
      document.querySelector('.maplibregl-ctrl-bottom-right'), // Attribution
      document.getElementById('projection-notification'),
      document.getElementById('location-popup')
    ];
    
    uiElements.forEach(element => {
      if (element) {
        element.addEventListener("mouseenter", () => {
          pointerDataDiv.style.display = 'none';
        });
        
        element.addEventListener("mouseleave", () => {
          if (pointerLngLat) {
            pointerDataDiv.style.display = 'block';
          }
        });
      }
    });

    // Location panel functionality - simplified hierarchy display
    let moveTimer = null;
    let isMoving = false;

    async function updateLocationPanel() {
      const center = map.getCenter();
      
      console.log('🎯 Getting location hierarchy for center:', center.lng.toFixed(4), center.lat.toFixed(4));
      
      try {
        // Reverse geocoding để lấy toàn bộ hierarchy như demo
        const response = await fetch(
          `https://api.maptiler.com/geocoding/${center.lng},${center.lat}.json?key=${maptilersdk.config.apiKey}&language=en`
        );
        
        if (!response.ok) {
          console.log('❌ Reverse geocoding failed:', response.status, response.statusText);
          locationList.innerHTML = '<div style="padding:8px;color:#999;">Unable to detect location</div>';
          return;
        }
        
        const data = await response.json();
        console.log('🏠 Reverse geocoding result:', data);
        
        if (!data.features || data.features.length === 0) {
          locationList.innerHTML = '<div style="padding:8px;color:#999;">No location data found</div>';
          return;
        }
        
        // Hiển thị toàn bộ hierarchy (như demo) - từ chi tiết nhất đến tổng quát nhất
        locationList.innerHTML = data.features.map(feature => {
          const placeType = feature.place_type && feature.place_type[0] ? feature.place_type[0] : 'place';
          
          return `
            <div style="padding:4px 8px;margin:2px 0;cursor:pointer;border-radius:6px;transition:all 0.2s;border:1px solid transparent;" 
                 class="location-item" 
                 data-bbox='${JSON.stringify(feature.bbox)}'
                 data-name="${feature.place_name.replace(/"/g, '&quot;')}"
                 onmouseover="this.style.background='#e3f2fd';this.style.borderColor='#90caf9';" 
                 onmouseout="this.style.background='transparent';this.style.borderColor='transparent';">
              <div style="font-weight:500;color:#333;">${feature.text}</div>
              <div style="font-size:11px;color:#666;text-transform:capitalize;">${placeType}</div>
            </div>
          `;
        }).join('');
        
        // Add click events để zoom tới từng level
        document.querySelectorAll('.location-item').forEach(item => {
          item.addEventListener('click', () => {
            const bbox = JSON.parse(item.dataset.bbox);
            const name = item.dataset.name;
            
            console.log(`📍 Jumping to: ${name}, bbox:`, bbox);
            
            // Fit bounds với maxZoom để không zoom quá gần
            map.fitBounds(bbox, {
              maxZoom: 19,
              padding: 50,
              duration: 1500
            });
            
            // Tạo marker cho địa điểm được chọn
            const center = map.getCenter();
            createLocationMarker(center, name);
          });
        });
        
        locationPanel.style.display = 'block';
        console.log(`✅ Location panel updated with ${data.features.length} hierarchy levels`);
        
      } catch (error) {
        console.log('💥 Error updating location panel:', error);
        locationList.innerHTML = '<div style="padding:8px;color:#999;">Error loading locations</div>';
      }
    }

    // Lắng nghe sự kiện move với debounce 2 giây
    map.on('move', () => {
      isMoving = true;
      
      // Clear timer cũ
      if (moveTimer) {
        clearTimeout(moveTimer);
      }
      
      // Set timer mới - chỉ update khi dừng di chuyển > 4 giây
      moveTimer = setTimeout(() => {
        if (isMoving) {
          console.log('🕐 User stopped moving for 4s, updating location panel...');
          updateLocationPanel();
          isMoving = false;
        }
      }, 4000); // 4 giây
    });
    
    // Lắng nghe sự kiện zoom để cập nhật popup vị trí
    map.on('zoom', () => {
      if (currentLocationMarker && locationPopup.style.display === 'block') {
        const point = map.project(currentLocationMarker.lngLat);
        locationPopup.style.left = point.x + 'px';
        locationPopup.style.top = (point.y - 40) + 'px';
      }
    });
    
    // Update lần đầu khi map load xong
    map.on('load', () => {
      setTimeout(() => {
        updateLocationPanel();
        
        // Load saved scenes from localStorage
        loadSavedScenes();
      }, 1000);
    });
    
    // --- Helper Functions ---
    function updateTemperatureColorRamp() {
        if (!colorScale.length || !mapLoaded) return;
        if (map.getLayer(TEMP_LAYER_ID)) {
            map.removeLayer(TEMP_LAYER_ID);
        }
        const ramp = colorScale.map(entry => ({
            value: entry.min,
            color: hexToRgb(entry.color)
        }));
        layerBg = new maptilerweather.TemperatureLayer({
            id: TEMP_LAYER_ID,
            opacity: 1, // Giữ opacity = 1 như yêu cầu
            colorramp: ramp,
        });
        
        // Thêm temperature layer trước hillshade như hill2 copy.html
        map.addLayer(layerBg, 'hillshade-shadows');
        
        // Thêm layer border riêng lên trên temp layer
        addBorderLayer();

        // Đảm bảo custom border layers luôn ở trên cùng
        setTimeout(() => {
            if (map.getLayer('state-boundaries')) {
                map.moveLayer('state-boundaries');
            }
            if (map.getLayer('country-boundaries')) {
                map.moveLayer('country-boundaries');
            }
            if (map.getLayer('simple-country-borders')) {
                map.moveLayer('simple-country-borders');
            }
            console.log('Moved border layers to top after temperature layer');
        }, 50);
        
        drawColorScale(ramp.map(r => r.color));
        scaleMinLabel.innerText = `${colorScale[0].min}°C`;
        scaleMaxLabel.innerText = `${colorScale[colorScale.length-1].max}°C`;
    }

    // Hàm thêm layer border riêng
    function addBorderLayer() {
        // Kiểm tra và thêm các layer border có sẵn của MapTiler
        const borderLayers = ['boundary', 'admin', 'country-border'];
        
        borderLayers.forEach(layerName => {
            try {
                if (map.getLayer(layerName)) {
                    // Di chuyển layer border lên trên temp layer
                    map.moveLayer(layerName, TEMP_LAYER_ID);
                    console.log(`Moved border layer ${layerName} above temp layer`);
                }
            } catch (e) {
                console.log(`Could not move border layer ${layerName}:`, e.message);
            }
        });
    }

    function refreshTimeUI() {
      const d = layer.getAnimationTimeDate();
      if (d) {
        timeTextDiv.innerText = d.toString();
        timeSlider.value = +d;
      }
      updatePointerValue(pointerLngLat);
    }

    function updatePointerValue(lngLat) {
      if (!lngLat || !mapLoaded) return;
      if (!layerBg && tempLayerVisible) return;
      
      pointerLngLat = lngLat;
      const valueWind = layer.pickAt(lngLat.lng, lngLat.lat);
      let valueTemp = null;
      if(layerBg) {
        valueTemp = layerBg.pickAt(lngLat.lng, lngLat.lat);
      }

      // Build tooltip content like the image
      let lines = [];
      
      // Temperature line
      if (tempLayerVisible && valueTemp && valueTemp.value != null) {
        lines.push(`${valueTemp.value.toFixed(0)}°C`);
      }
      
      // Wind speed and direction line
      if (valueWind) {
        let windLine = `${valueWind.speedKilometersPerHour.toFixed(0)} km/h`;
        
        // Get wind direction from available fields
        let directionAngle = null;
        if (valueWind.directionAngle !== undefined && valueWind.directionAngle !== null) {
          directionAngle = valueWind.directionAngle;
        } else if (valueWind.compassDirection) {
          // Convert compass direction back to angle for rotation
          directionAngle = compassToAngle(valueWind.compassDirection);
        }
        
        if (directionAngle !== null) {
          const compassText = valueWind.compassDirection || getCompassFromAngle(directionAngle);
          // Use +270° rotation to match wind animation direction
          const flowAngle = (directionAngle + 270) % 360;
          windLine += ` <span style="display:inline-block;transform:rotate(${flowAngle}deg)">➤</span> ${compassText}`;
        }
        
        lines.push(windLine);
      }
      
      if (lines.length === 0) {
        lines.push("No data");
      }
      
      // Use innerHTML to support HTML styling for rotated arrow
      pointerDataDiv.innerHTML = lines.join('<br>');
    }
    
    // Convert wind direction degrees to compass direction
    function getCompassFromAngle(degrees) {
      const directions = ['N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE', 
                         'S', 'SSW', 'SW', 'WSW', 'W', 'WNW', 'NW', 'NNW'];
      const index = Math.round(degrees / 22.5) % 16;
      return directions[index];
    }
    
    // Convert compass direction to angle for rotation
    function compassToAngle(compass) {
      const compassMap = {
        'N': 0, 'NNE': 22.5, 'NE': 45, 'ENE': 67.5,
        'E': 90, 'ESE': 112.5, 'SE': 135, 'SSE': 157.5,
        'S': 180, 'SSW': 202.5, 'SW': 225, 'WSW': 247.5,
        'W': 270, 'WNW': 292.5, 'NW': 315, 'NNW': 337.5
      };
      return compassMap[compass] || 0;
    }

    function hexToRgb(hex) {
      const v = hex.replace('#', '');
      return [parseInt(v.substring(0,2),16), parseInt(v.substring(2,4),16), parseInt(v.substring(4,6),16)];
    }

    function drawColorScale(colors) {
      const ctx = colorScaleCanvas.getContext('2d');
      ctx.clearRect(0, 0, colorScaleCanvas.width, colorScaleCanvas.height);
      for (let i = 0; i < colors.length; i++) {
        ctx.fillStyle = `rgb(${colors[i][0]},${colors[i][1]},${colors[i][2]})`;
        const y = colorScaleCanvas.height - (i + 1) * (colorScaleCanvas.height / colors.length);
        const h = colorScaleCanvas.height / colors.length + 1;
        ctx.fillRect(0, y, colorScaleCanvas.width, h);
      }
    }

    // === SCENE MANAGEMENT SYSTEM ===
    
    // Capture current scene state
    function getCurrentSceneState() {
      // Get projection object and extract name
      let currentProjection;
      let projectionName;
      try {
        currentProjection = map.getProjection?.() || map.projection;
        if (currentProjection) {
          projectionName = currentProjection.name || currentProjection.type || JSON.stringify(currentProjection);
          console.log(`💾 Full projection object:`, currentProjection);
          console.log(`💾 Projection properties:`, Object.keys(currentProjection));
        } else {
          projectionName = 'mercator';
        }
      } catch (e) {
        projectionName = 'mercator'; // fallback
      }
      
      console.log(`💾 Saving scene with projection name: ${projectionName}`);
      
      return {
        // Timeline
        time: parseInt(timeSlider.value),
        
        // Viewport
        center: map.getCenter(),
        zoom: map.getZoom(),
        bearing: map.getBearing(),
        pitch: map.getPitch(),
        
        // Layers
        tempLayerVisible: tempLayerVisible,
        hillshadeVisible: hillshadeVisible,
        isPlaying: isPlaying,
        
        // Additional state
        projection: projectionName,
        projectionObject: currentProjection, // Store full object too
        
        // Timestamp
        savedAt: new Date().toISOString()
      };
    }
    
    // Save scene to specific slot
    function saveScene(sceneIndex, sceneState) {
      scenes[sceneIndex] = sceneState;
      
      // Update button appearance
      const button = document.getElementById(`scene-${sceneIndex}`);
      if (button) {
        button.classList.add('saved');
        button.title = `Saved: ${new Date(sceneState.savedAt).toLocaleString()}`;
      }
      
      // Save to localStorage for persistence
      localStorage.setItem('weather-app-scenes', JSON.stringify(scenes));
      
      console.log(`💾 Scene ${sceneIndex} saved:`, sceneState);
    }
    
    // Scene loading debounce protection
    let sceneLoadingTimeout = null;
    let isLoadingScene = false;
    
    // Load scene from specific slot
    function loadScene(sceneIndex) {
      // Prevent multiple rapid clicks
      if (isLoadingScene) {
        console.log(`⏳ Scene loading in progress, ignoring click`);
        return;
      }
      
      // Check if already at this scene
      if (currentSceneIndex === sceneIndex) {
        console.log(`📍 Already at Scene ${sceneIndex}, ignoring click`);
        return;
      }
      
      const sceneState = scenes[sceneIndex];
      
      if (!sceneState) {
        console.log(`❌ Scene ${sceneIndex} is empty`);
        return;
      }
      
      console.log(`🎬 Loading Scene ${sceneIndex}:`, sceneState);
      
      // Set loading flag
      isLoadingScene = true;
      
      // Clear any existing timeout
      if (sceneLoadingTimeout) {
        clearTimeout(sceneLoadingTimeout);
      }
      
      // Update current scene indicator
      updateActiveSceneButton(sceneIndex);
      
      // Apply timeline
      if (sceneState.time) {
        timeSlider.value = sceneState.time;
        layer.setAnimationTime(sceneState.time / 1000);
        if (layerBg) layerBg.setAnimationTime(sceneState.time / 1000);
        refreshTimeUI();
      }
      
      // Apply viewport
      map.easeTo({
        center: sceneState.center,
        zoom: sceneState.zoom,
        bearing: sceneState.bearing || 0,
        pitch: sceneState.pitch || 0,
        duration: 1500
      });
      
      // Apply layer visibility
      if (sceneState.tempLayerVisible !== tempLayerVisible) {
        toggleTempButton.click();
      }
      
      if (sceneState.hillshadeVisible !== hillshadeVisible) {
        hillshadeToggleButton.click();
      }
      
      // Apply playing state
      if (sceneState.isPlaying !== isPlaying) {
        playPauseButton.click();
      }
      
      // Apply projection (simulate user click on projection control)
      if (sceneState.projection) {
        setTimeout(() => {
          let currentProjectionObj = map.getProjection?.() || map.projection;
          let currentProjectionName = currentProjectionObj?.name || currentProjectionObj?.type || 'mercator';
          
          console.log(`🗺️ Current projection: ${currentProjectionName}, Target: ${sceneState.projection}`);
          
          if (sceneState.projection !== currentProjectionName) {
            console.log(`🔄 Simulating user click to change projection to: ${sceneState.projection}`);
            
            try {
              // Find projection control button and simulate click
              const projectionButton = document.querySelector('.maplibregl-ctrl-projection button, .maptiler-ctrl-projection button, button[data-projection], .projection-control button');
              
              if (projectionButton) {
                console.log(`📍 Found projection button:`, projectionButton);
                projectionButton.click();
                console.log(`✅ Projection button clicked`);
              } else {
                // Try to find any button containing projection-related text
                const allButtons = document.querySelectorAll('button');
                let foundButton = null;
                
                allButtons.forEach(btn => {
                  if (btn.textContent.toLowerCase().includes('globe') || 
                      btn.textContent.toLowerCase().includes('projection') ||
                      btn.title.toLowerCase().includes('globe') ||
                      btn.title.toLowerCase().includes('projection')) {
                    foundButton = btn;
                  }
                });
                
                if (foundButton) {
                  console.log(`📍 Found projection-related button:`, foundButton);
                  foundButton.click();
                  console.log(`✅ Projection-related button clicked`);
                } else {
                  console.log(`❌ Could not find projection control button`);
                  console.log(`📋 Available buttons:`, Array.from(allButtons).map(btn => ({
                    text: btn.textContent,
                    title: btn.title,
                    className: btn.className
                  })));
                }
              }
            } catch (e) {
              console.error('❌ Projection click simulation failed:', e);
            }
          } else {
            console.log('📍 Projection already matches, no change needed');
          }
        }, 100);
      }
      
      currentSceneIndex = sceneIndex;
      
      // Reset loading flag after animation completes
      sceneLoadingTimeout = setTimeout(() => {
        isLoadingScene = false;
        console.log(`✅ Scene ${sceneIndex} loading completed`);
      }, 2000); // Wait for map animation (1500ms) + buffer
    }
    
    // Save current scene (with selection UI)
    function saveCurrentScene() {
      if (selectedSceneToSave) {
        // Save to selected slot
        const sceneState = getCurrentSceneState();
        saveScene(selectedSceneToSave, sceneState);
        selectedSceneToSave = null;
        updateSaveButtonState();
      } else {
        // Show selection mode
        showSceneSelection();
      }
    }
    
    // Show scene selection for saving
    function showSceneSelection() {
      selectedSceneToSave = null;
      
      // Highlight save button
      const saveButton = document.getElementById('save-scene');
      saveButton.textContent = '📍 Select Slot';
      saveButton.style.background = 'rgba(255,87,34,0.9)';
      
      // Add click listeners to scene buttons
      for (let i = 1; i <= 5; i++) {
        const button = document.getElementById(`scene-${i}`);
        button.style.border = '2px dashed #FFC107';
        button.onclick = function() {
          selectedSceneToSave = i;
          updateSaveButtonState();
          
          // Reset other buttons
          for (let j = 1; j <= 5; j++) {
            const btn = document.getElementById(`scene-${j}`);
            btn.style.border = 'none';
            btn.onclick = function() { loadScene(j); };
          }
        };
      }
    }
    
    // Update save button state
    function updateSaveButtonState() {
      const saveButton = document.getElementById('save-scene');
      
      if (selectedSceneToSave) {
        saveButton.textContent = `💾 Save → ${selectedSceneToSave}`;
        saveButton.style.background = 'rgba(76,175,80,0.9)';
      } else {
        saveButton.textContent = '💾 Save';
        saveButton.style.background = 'rgba(255,193,7,0.9)';
      }
    }
    
    // Update active scene button appearance
    function updateActiveSceneButton(sceneIndex) {
      // Remove active from all
      for (let i = 1; i <= 5; i++) {
        const button = document.getElementById(`scene-${i}`);
        if (button) {
          button.classList.remove('active');
        }
      }
      
      // Add active to current
      const activeButton = document.getElementById(`scene-${sceneIndex}`);
      if (activeButton) {
        activeButton.classList.add('active');
      }
    }
    
    // Load scenes from localStorage on startup
    function loadSavedScenes() {
      try {
        const savedScenes = localStorage.getItem('weather-app-scenes');
        if (savedScenes) {
          scenes = JSON.parse(savedScenes);
          
          // Update button appearances for saved scenes
          Object.keys(scenes).forEach(sceneIndex => {
            const button = document.getElementById(`scene-${sceneIndex}`);
            if (button && scenes[sceneIndex]) {
              button.classList.add('saved');
              button.title = `Saved: ${new Date(scenes[sceneIndex].savedAt).toLocaleString()}`;
            }
          });
          
          console.log('🎬 Loaded saved scenes:', Object.keys(scenes));
        }
      } catch (e) {
        console.log('Error loading saved scenes:', e);
      }
    }
    
    // Clear all saved scenes
    function clearAllScenes() {
      // Confirmation dialog
      const confirmClear = confirm('🗑️ Xóa tất cả scenes đã lưu?\n\nHành động này không thể hoàn tác!');
      
      if (confirmClear) {
        // Clear localStorage
        localStorage.removeItem('weather-app-scenes');
        
        // Clear memory
        scenes = {};
        currentSceneIndex = null;
        
        // Reset all button appearances
        for (let i = 1; i <= 5; i++) {
          const button = document.getElementById(`scene-${i}`);
          if (button) {
            button.classList.remove('saved', 'active');
            button.title = '';
            button.onclick = function() { loadScene(i); };
            button.style.border = 'none';
          }
        }
        
        // Reset save button
        selectedSceneToSave = null;
        updateSaveButtonState();
        
        console.log('🗑️ All scenes cleared');
        
        // Show success message
        const clearButton = document.getElementById('clear-scenes');
        const originalText = clearButton.textContent;
        clearButton.textContent = '✅ Cleared!';
        clearButton.style.background = 'rgba(76,175,80,0.9)';
        
        setTimeout(() => {
          clearButton.textContent = originalText;
          clearButton.style.background = 'rgba(244,67,54,0.9)';
        }, 2000);
      }
    }
  </script>
</body>
</html>