try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="3defc1ac-f978-4ac7-9241-0a56356ae620",e._sentryDebugIdIdentifier="sentry-dbid-3defc1ac-f978-4ac7-9241-0a56356ae620")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3644],{10986:function(e,t,i){let r,n,s,o,l,a;i.d(t,{cA:function(){return iu}});var u=i(14760),c=Object.defineProperty,d=e=>{throw TypeError(e)},h=(e,t,i)=>t in e?c(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i,f=(e,t,i)=>h(e,"symbol"!=typeof t?t+"":t,i),g=(e,t,i)=>t.has(e)||d("Cannot "+i),p=(e,t,i)=>(g(e,t,"read from private field"),i?i.call(e):t.get(e)),m=(e,t,i)=>t.has(e)?d("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,i),y=(e,t,i,r)=>(g(e,t,"write to private field"),r?r.call(e,i):t.set(e,i),i);function v(){}function b(e){return e()}function x(){return Object.create(null)}function L(e){e.forEach(b)}function w(e){return"function"==typeof e}function E(e,t){return e!=e?t==t:e!==t||e&&"object"==typeof e||"function"==typeof e}function S(e,t){return e===t||(r||(r=document.createElement("a")),r.href=t,e===r.href)}function $(e,t,i,r){return e[1]&&r?function(e,t){for(let i in t)e[i]=t[i];return e}(i.ctx.slice(),e[1](r(t))):i.ctx}function T(e,t){e.appendChild(t)}function k(e,t,i){e.insertBefore(t,i||null)}function C(e){e.parentNode&&e.parentNode.removeChild(e)}function N(e){return document.createElement(e)}function M(e){return document.createElementNS("http://www.w3.org/2000/svg",e)}function R(e){return document.createTextNode(e)}function _(){return R(" ")}function O(e,t,i,r){return e.addEventListener(t,i,r),()=>e.removeEventListener(t,i,r)}function P(e,t,i){null==i?e.removeAttribute(t):e.getAttribute(t)!==i&&e.setAttribute(t,i)}function I(e,t){t=""+t,e.data!==t&&(e.data=t)}function A(e,t,i){e.classList.toggle(t,!!i)}function W(){if(!n)throw Error("Function called outside component initialization");return n}function B(){let e=W();return(t,i,{cancelable:r=!1}={})=>{let n=e.$$.callbacks[t];if(n){let s=function(e,t,{bubbles:i=!1,cancelable:r=!1}={}){return new CustomEvent(e,{detail:t,bubbles:i,cancelable:r})}(t,i,{cancelable:r});return n.slice().forEach(t=>{t.call(e,s)}),!s.defaultPrevented}return!0}}function G(e,t){let i=e.$$.callbacks[t.type];i&&i.slice().forEach(e=>e.call(this,t))}let z=[],D=[],U=[],F=[],q=Promise.resolve(),j=!1;function H(e){U.push(e)}let Z=new Set,V=0;function K(){if(0!==V)return;let e=n;do{try{for(;V<z.length;){let e=z[V];V++,n=e,function(e){if(null!==e.fragment){e.update(),L(e.before_update);let t=e.dirty;e.dirty=[-1],e.fragment&&e.fragment.p(e.ctx,t),e.after_update.forEach(H)}}(e.$$)}}catch(e){throw z.length=0,V=0,e}for(n=null,z.length=0,V=0;D.length;)D.pop()();for(let e=0;e<U.length;e+=1){let t=U[e];Z.has(t)||(Z.add(t),t())}U.length=0}while(z.length);for(;F.length;)F.pop()();j=!1,Z.clear(),n=e}let Q=new Set;function Y(){s={r:0,c:[],p:s}}function X(){s.r||L(s.c),s=s.p}function J(e,t){e&&e.i&&(Q.delete(e),e.i(t))}function ee(e,t,i,r){e&&e.o?Q.has(e)||(Q.add(e),s.c.push(()=>{Q.delete(e),r&&(i&&e.d(1),r())}),e.o(t)):r&&r()}function et(e){return(null==e?void 0:e.length)!==void 0?e:Array.from(e)}function ei(e,t){ee(e,1,1,()=>{t.delete(e.key)})}function er(e){e&&e.c()}function en(e,t,i){let{fragment:r,after_update:n}=e.$$;r&&r.m(t,i),H(()=>{let t=e.$$.on_mount.map(b).filter(w);e.$$.on_destroy?e.$$.on_destroy.push(...t):L(t),e.$$.on_mount=[]}),n.forEach(H)}function es(e,t){let i=e.$$;null!==i.fragment&&(function(e){let t=[],i=[];U.forEach(r=>-1===e.indexOf(r)?t.push(r):i.push(r)),i.forEach(e=>e()),U=t}(i.after_update),L(i.on_destroy),i.fragment&&i.fragment.d(t),i.on_destroy=i.fragment=null,i.ctx=[])}function eo(e,t,i,r,s,o,l=null,a=[-1]){let u=n;n=e;let c=e.$$={fragment:null,ctx:[],props:o,update:v,not_equal:s,bound:x(),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(t.context||(u?u.$$.context:[])),callbacks:x(),dirty:a,skip_bound:!1,root:t.target||u.$$.root};l&&l(c.root);let d=!1;if(c.ctx=i?i(e,t.props||{},(t,i,...r)=>{let n=r.length?r[0]:i;return c.ctx&&s(c.ctx[t],c.ctx[t]=n)&&(!c.skip_bound&&c.bound[t]&&c.bound[t](n),d&&(-1===e.$$.dirty[0]&&(z.push(e),j||(j=!0,q.then(K)),e.$$.dirty.fill(0)),e.$$.dirty[t/31|0]|=1<<t%31)),i}):[],c.update(),d=!0,L(c.before_update),c.fragment=!!r&&r(c.ctx),t.target){if(t.hydrate){let e=Array.from(t.target.childNodes);c.fragment&&c.fragment.l(e),e.forEach(C)}else c.fragment&&c.fragment.c();t.intro&&J(e.$$.fragment),en(e,t.target,t.anchor),K()}n=u}class el{constructor(){f(this,"$$"),f(this,"$$set")}$destroy(){es(this,1),this.$destroy=v}$on(e,t){if(!w(t))return v;let i=this.$$.callbacks[e]||(this.$$.callbacks[e]=[]);return i.push(t),()=>{let e=i.indexOf(t);-1!==e&&i.splice(e,1)}}$set(e){this.$$set&&0!==Object.keys(e).length&&(this.$$.skip_bound=!0,this.$$set(e),this.$$.skip_bound=!1)}}function ea(e){let t,i;return{c(){t=M("svg"),P(i=M("path"),"d","M13.12.706a.982.982 0 0 0-1.391 0L6.907 5.517 2.087.696a.982.982 0 1 0-1.391 1.39l4.821 4.821L.696 11.73a.982.982 0 1 0 1.39 1.39l4.821-4.821 4.822 4.821a.982.982 0 1 0 1.39-1.39L8.298 6.908l4.821-4.822a.988.988 0 0 0 0-1.38Z"),P(t,"viewBox","0 0 14 14"),P(t,"width","13"),P(t,"height","13"),P(t,"class","svelte-en2qvf")},m(e,r){k(e,t,r),T(t,i)},p:v,i:v,o:v,d(e){e&&C(t)}}}"u">typeof window&&(window.__svelte||(window.__svelte={v:new Set})).v.add("4");class eu extends el{constructor(e){super(),eo(this,e,null,ea,E,{})}}function ec(e){let t,i;return{c(){t=M("svg"),P(i=M("path"),"d","M15 0C6.705 0 0 6.705 0 15C0 23.295 6.705 30 15 30C23.295 30 30 23.295 30 15C30 6.705 23.295 0 15 0ZM22.5 20.385L20.385 22.5L15 17.115L9.615 22.5L7.5 20.385L12.885 15L7.5 9.615L9.615 7.5L15 12.885L20.385 7.5L22.5 9.615L17.115 15L22.5 20.385Z"),P(t,"viewBox","0 0 30 30"),P(t,"fill","none"),P(t,"xmlns","http://www.w3.org/2000/svg"),P(t,"class","svelte-d2loi5")},m(e,r){k(e,t,r),T(t,i)},p:v,i:v,o:v,d(e){e&&C(t)}}}class ed extends el{constructor(e){super(),eo(this,e,null,ec,E,{})}}function eh(e){let t,i;return{c(){S((t=N("img")).src,i=e[3]+"area.svg")||P(t,"src",i),P(t,"alt",e[7]),P(t,"title",e[7]),P(t,"class","svelte-w9y5n9")},m(e,i){k(e,t,i)},p(e,r){8&r&&!S(t.src,i=e[3]+"area.svg")&&P(t,"src",i),128&r&&P(t,"alt",e[7]),128&r&&P(t,"title",e[7])},d(e){e&&C(t)}}}function ef(e){let t,i;return{c(){S((t=N("img")).src,i=e[3]+"reverse.svg")||P(t,"src",i),P(t,"alt",e[7]),P(t,"title",e[7]),P(t,"class","svelte-w9y5n9")},m(e,i){k(e,t,i)},p(e,r){8&r&&!S(t.src,i=e[3]+"reverse.svg")&&P(t,"src",i),128&r&&P(t,"alt",e[7]),128&r&&P(t,"title",e[7])},d(e){e&&C(t)}}}function eg(e){let t,i;return{c(){S((t=N("img")).src,i=e[3]+"poi.svg")||P(t,"src",i),P(t,"alt",e[7]),P(t,"title",e[7]),P(t,"class","svelte-w9y5n9")},m(e,i){k(e,t,i)},p(e,r){8&r&&!S(t.src,i=e[3]+"poi.svg")&&P(t,"src",i),128&r&&P(t,"alt",e[7]),128&r&&P(t,"title",e[7])},d(e){e&&C(t)}}}function ep(e){let t,i;return{c(){S((t=N("img")).src,i=e[3]+"postal_code.svg")||P(t,"src",i),P(t,"alt",e[7]),P(t,"title",e[7]),P(t,"class","svelte-w9y5n9")},m(e,i){k(e,t,i)},p(e,r){8&r&&!S(t.src,i=e[3]+"postal_code.svg")&&P(t,"src",i),128&r&&P(t,"alt",e[7]),128&r&&P(t,"title",e[7])},d(e){e&&C(t)}}}function em(e){let t,i;return{c(){S((t=N("img")).src,i=e[3]+"street.svg")||P(t,"src",i),P(t,"alt",e[7]),P(t,"title",e[7]),P(t,"class","svelte-w9y5n9")},m(e,i){k(e,t,i)},p(e,r){8&r&&!S(t.src,i=e[3]+"street.svg")&&P(t,"src",i),128&r&&P(t,"alt",e[7]),128&r&&P(t,"title",e[7])},d(e){e&&C(t)}}}function ey(e){let t,i;return{c(){S((t=N("img")).src,i=e[3]+"road.svg")||P(t,"src",i),P(t,"alt",e[7]),P(t,"title",e[7]),P(t,"class","svelte-w9y5n9")},m(e,i){k(e,t,i)},p(e,r){8&r&&!S(t.src,i=e[3]+"road.svg")&&P(t,"src",i),128&r&&P(t,"alt",e[7]),128&r&&P(t,"title",e[7])},d(e){e&&C(t)}}}function ev(e){let t,i;return{c(){S((t=N("img")).src,i=e[3]+"housenumber.svg")||P(t,"src",i),P(t,"alt",e[7]),P(t,"title",e[7]),P(t,"class","svelte-w9y5n9")},m(e,i){k(e,t,i)},p(e,r){8&r&&!S(t.src,i=e[3]+"housenumber.svg")&&P(t,"src",i),128&r&&P(t,"alt",e[7]),128&r&&P(t,"title",e[7])},d(e){e&&C(t)}}}function eb(e){let t,i,r,n;return{c(){S((t=N("img")).src,i=e[5])||P(t,"src",i),P(t,"alt",e[4]),P(t,"title",e[7]),P(t,"class","svelte-w9y5n9")},m(i,s){k(i,t,s),r||(n=O(t,"error",e[14]),r=!0)},p(e,r){32&r&&!S(t.src,i=e[5])&&P(t,"src",i),16&r&&P(t,"alt",e[4]),128&r&&P(t,"title",e[7])},d(e){e&&C(t),r=!1,n()}}}function ex(e){let t,i;return{c(){P(t=N("div"),"class","sprite-icon svelte-w9y5n9"),P(t,"style",i=`
        width: ${e[6].width/e$}px;
        height: ${e[6].height/e$}px;
        background-image: url(${e[3]}sprite${eS}.png);
        background-position: -${e[6].x/e$}px -${e[6].y/e$}px;
        background-size: ${o.width/e$}px ${o.height/e$}px;
      `),P(t,"title",e[7])},m(e,i){k(e,t,i)},p(e,r){72&r&&i!==(i=`
        width: ${e[6].width/e$}px;
        height: ${e[6].height/e$}px;
        background-image: url(${e[3]}sprite${eS}.png);
        background-position: -${e[6].x/e$}px -${e[6].y/e$}px;
        background-size: ${o.width/e$}px ${o.height/e$}px;
      `)&&P(t,"style",i),128&r&&P(t,"title",e[7])},d(e){e&&C(t)}}}function eL(e){let t,i;return{c(){t=N("span"),i=R(e[7]),P(t,"class","secondary svelte-w9y5n9")},m(e,r){k(e,t,r),T(t,i)},p(e,t){128&t&&I(i,e[7])},d(e){e&&C(t)}}}function ew(e){let t,i,r,n,s,l,a,u,c,d=(e[8]?e[0].place_name:e[0].place_name.replace(/,.*/,""))+"",h,f,g="always"===e[2]||"never"!==e[2]&&!e[0].address&&!e[0].id.startsWith("road.")&&!e[0].id.startsWith("address.")&&!e[0].id.startsWith("postal_code.")&&(!e[0].id.startsWith("poi.")||!e[5])&&!e[8],p,m,y=(e[8]?"":e[0].place_name.replace(/[^,]*,?\s*/,""))+"",b,x,w,E,S,$;function M(e,t){return 1&t&&(i=null),1&t&&(r=null),1&t&&(n=null),1&t&&(s=null),o&&e[6]?ex:e[5]?eb:e[0].address?ev:(null==i&&(i=!!e[0].id.startsWith("road.")),i?ey:(null==r&&(r=!!e[0].id.startsWith("address.")),r?em:(null==n&&(n=!!e[0].id.startsWith("postal_code.")),n?ep:(null==s&&(s=!!e[0].id.startsWith("poi.")),s?eg:e[8]?ef:eh))))}let A=M(e,-1),W=A(e),B=g&&eL(e);return{c(){t=N("li"),W.c(),l=_(),a=N("span"),u=N("span"),c=N("span"),h=R(d),f=_(),B&&B.c(),p=_(),m=N("span"),b=R(y),P(c,"class","primary svelte-w9y5n9"),P(u,"class","svelte-w9y5n9"),P(m,"class","line2 svelte-w9y5n9"),P(a,"class","texts svelte-w9y5n9"),P(t,"tabindex","-1"),P(t,"role","option"),P(t,"aria-selected",x="selected"===e[1]),P(t,"aria-checked",w="picked"===e[1]),P(t,"class",E=(e[1]??"")+" svelte-w9y5n9")},m(i,r){k(i,t,r),W.m(t,null),T(t,l),T(t,a),T(a,u),T(u,c),T(c,h),T(u,f),B&&B.m(u,null),T(a,p),T(a,m),T(m,b),S||($=[O(t,"mouseenter",e[13]),O(t,"focus",e[15]),O(t,"click",e[16])],S=!0)},p(e,[i]){A===(A=M(e,i))&&W?W.p(e,i):(W.d(1),(W=A(e))&&(W.c(),W.m(t,l))),257&i&&d!==(d=(e[8]?e[0].place_name:e[0].place_name.replace(/,.*/,""))+"")&&I(h,d),293&i&&(g="always"===e[2]||"never"!==e[2]&&!e[0].address&&!e[0].id.startsWith("road.")&&!e[0].id.startsWith("address.")&&!e[0].id.startsWith("postal_code.")&&(!e[0].id.startsWith("poi.")||!e[5])&&!e[8]),g?B?B.p(e,i):((B=eL(e)).c(),B.m(u,null)):B&&(B.d(1),B=null),257&i&&y!==(y=(e[8]?"":e[0].place_name.replace(/[^,]*,?\s*/,""))+"")&&I(b,y),2&i&&x!==(x="selected"===e[1])&&P(t,"aria-selected",x),2&i&&w!==(w="picked"===e[1])&&P(t,"aria-checked",w),2&i&&E!==(E=(e[1]??"")+" svelte-w9y5n9")&&P(t,"class",E)},i:v,o:v,d(e){e&&C(t),W.d(),B&&B.d(),S=!1,L($)}}}let eE=typeof devicePixelRatio>"u"?1:devicePixelRatio>1.25,eS=eE?"@2x":"",e$=eE?2:1;function eT(e,t,i){let r,n,s,a,u,c,d,{feature:h}=t,{style:f="default"}=t,{showPlaceType:g}=t,{missingIconsCache:p}=t,{iconsBaseUrl:m}=t,y=B();function v(){n&&p.add(n),b()}function b(){void 0!==o?x():(l??(l=fetch(`${m}sprite${eS}.json`).then(e=>e.json()).then(e=>{o=e}).catch(()=>{o=null})),null==l||l.then(x))}function x(){do{if(a--,i(4,r=null==u?void 0:u[a]),i(6,s=r?null==o?void 0:o.icons[r]:void 0),s)break;i(5,n=r?m+r.replace(/ /g,"_")+".svg":void 0)}while(a>-1&&(!n||p.has(n)))}return e.$$set=e=>{"feature"in e&&i(0,h=e.feature),"style"in e&&i(1,f=e.style),"showPlaceType"in e&&i(2,g=e.showPlaceType),"missingIconsCache"in e&&i(11,p=e.missingIconsCache),"iconsBaseUrl"in e&&i(3,m=e.iconsBaseUrl)},e.$$.update=()=>{var t,r,n,s,o;1&e.$$.dirty&&i(12,u=null==(t=h.properties)?void 0:t.categories),1&e.$$.dirty&&i(8,c="reverse"===h.place_type[0]),1&e.$$.dirty&&i(7,d=(null==(n=null==(r=h.properties)?void 0:r.categories)?void 0:n.join(", "))??(null==(o=null==(s=h.properties)?void 0:s.place_type_name)?void 0:o[0])??h.place_type[0]),4096&e.$$.dirty&&(a=(null==u?void 0:u.length)??0,b())},[h,f,g,m,r,n,s,d,c,y,v,p,u,function(t){G.call(this,e,t)},()=>v(),()=>y("select",void 0),e=>{document.activeElement!==e.target&&y("select",void 0)}]}class ek extends el{constructor(e){super(),eo(this,e,eT,ew,E,{feature:0,style:1,showPlaceType:2,missingIconsCache:11,iconsBaseUrl:3})}}function eC(e){let t;return{c(){(t=N("div")).innerHTML='<svg viewBox="0 0 18 18" width="24" height="24" class="svelte-7cmwmc"><path fill="#333" d="M4.4 4.4l.8.8c2.1-2.1 5.5-2.1 7.6 0l.8-.8c-2.5-2.5-6.7-2.5-9.2 0z"></path><path opacity=".1" d="M12.8 12.9c-2.1 2.1-5.5 2.1-7.6 0-2.1-2.1-2.1-5.5 0-7.7l-.8-.8c-2.5 2.5-2.5 6.7 0 9.2s6.6 2.5 9.2 0 2.5-6.6 0-9.2l-.8.8c2.2 2.1 2.2 5.6 0 7.7z"></path></svg>',P(t,"class","svelte-7cmwmc")},m(e,i){k(e,t,i)},p:v,i:v,o:v,d(e){e&&C(t)}}}class eN extends el{constructor(e){super(),eo(this,e,null,eC,E,{})}}function eM(e){let t,i,r;return{c(){t=M("svg"),P(i=M("path"),"stroke-width","4"),P(i,"d","M 5,33.103579 C 5,17.607779 18.457,5 35,5 C 51.543,5 65,17.607779 65,33.103579 C 65,56.388679 40.4668,76.048179 36.6112,79.137779 C 36.3714,79.329879 36.2116,79.457979 36.1427,79.518879 C 35.8203,79.800879 35.4102,79.942779 35,79.942779 C 34.5899,79.942779 34.1797,79.800879 33.8575,79.518879 C 33.7886,79.457979 33.6289,79.330079 33.3893,79.138079 C 29.5346,76.049279 5,56.389379 5,33.103579 Z M 35.0001,49.386379 C 43.1917,49.386379 49.8323,42.646079 49.8323,34.331379 C 49.8323,26.016779 43.1917,19.276479 35.0001,19.276479 C 26.8085,19.276479 20.1679,26.016779 20.1679,34.331379 C 20.1679,42.646079 26.8085,49.386379 35.0001,49.386379 Z"),P(i,"class","svelte-gzo3ar"),P(t,"width",r="list"===e[0]?20:void 0),P(t,"viewBox","0 0 70 85"),P(t,"fill","none"),P(t,"class","svelte-gzo3ar"),A(t,"in-map","list"!==e[0]),A(t,"list-icon","list"===e[0])},m(e,r){k(e,t,r),T(t,i)},p(e,[i]){1&i&&r!==(r="list"===e[0]?20:void 0)&&P(t,"width",r),1&i&&A(t,"in-map","list"!==e[0]),1&i&&A(t,"list-icon","list"===e[0])},i:v,o:v,d(e){e&&C(t)}}}function eR(e,t,i){let{displayIn:r}=t;return e.$$set=e=>{"displayIn"in e&&i(0,r=e.displayIn)},[r]}class e_ extends el{constructor(e){super(),eo(this,e,eR,eM,E,{displayIn:0})}}function eO(e){let t,i;return{c(){t=M("svg"),P(i=M("path"),"d","M30.003-26.765C13.46-26.765 0-14.158 0 1.337c0 23.286 24.535 42.952 28.39 46.04.24.192.402.316.471.376.323.282.732.424 1.142.424.41 0 .82-.142 1.142-.424.068-.06.231-.183.471-.376 3.856-3.09 28.39-22.754 28.39-46.04 0-15.495-13.46-28.102-30.003-28.102Zm1.757 12.469c4.38 0 7.858 1.052 10.431 3.158 2.595 2.105 3.89 4.913 3.89 8.422 0 2.34-.53 4.362-1.593 6.063-1.063 1.702-3.086 3.616-6.063 5.742-2.042 1.51-3.337 2.659-3.89 3.446-.532.787-.8 1.82-.8 3.096v1.914h-8.449V15.18c0-2.041.434-3.815 1.306-5.325.872-1.51 2.467-3.118 4.785-4.82 2.233-1.594 3.7-2.89 4.402-3.889a5.582 5.582 0 0 0 1.087-3.35c0-1.382-.51-2.435-1.531-3.158-1.02-.723-2.45-1.087-4.28-1.087-3.19 0-6.826 1.047-10.91 3.131l-3.472-6.986c4.742-2.659 9.77-3.992 15.087-3.992Zm-1.88 37.324c1.765 0 3.124.472 4.08 1.408.98.936 1.47 2.276 1.47 4.02 0 1.68-.49 3.007-1.47 3.985-.977.957-2.336 1.435-4.08 1.435-1.787 0-3.171-.465-4.15-1.4-.978-.958-1.47-2.298-1.47-4.02 0-1.787.48-3.14 1.436-4.054.957-.915 2.355-1.374 4.184-1.374Z"),P(t,"viewBox","0 0 60.006 21.412"),P(t,"width","14"),P(t,"height","20"),P(t,"class","svelte-en2qvf")},m(e,r){k(e,t,r),T(t,i)},p:v,i:v,o:v,d(e){e&&C(t)}}}class eP extends el{constructor(e){super(),eo(this,e,null,eO,E,{})}}function eI(e){let t,i,r;return{c(){t=M("svg"),i=M("circle"),r=M("path"),P(i,"cx","4.789"),P(i,"cy","4.787"),P(i,"r","3.85"),P(i,"class","svelte-1aq105l"),P(r,"d","M12.063 12.063 7.635 7.635"),P(r,"class","svelte-1aq105l"),P(t,"xmlns","http://www.w3.org/2000/svg"),P(t,"width","13"),P(t,"height","13"),P(t,"viewBox","0 0 13 13"),P(t,"class","svelte-1aq105l")},m(e,n){k(e,t,n),T(t,i),T(t,r)},p:v,i:v,o:v,d(e){e&&C(t)}}}class eA extends el{constructor(e){super(),eo(this,e,null,eI,E,{})}}function eW(e){let t=[...e];return t[2]<t[0]&&(Math.abs((t[0]+t[2]+360)/2)>Math.abs((t[0]-360+t[2])/2)?t[0]-=360:t[2]+=360),t}async function eB(e,t,i){let r=null==e?void 0:e.getCenterAndZoom();for(let e of t??[])if(!(r&&(null!=e.minZoom&&e.minZoom>r[0]||null!=e.maxZoom&&e.maxZoom<r[0]))){if("fixed"===e.type)return e.coordinates.join(",");e:if("client-geolocation"===e.type){let t;if(a&&e.cachedLocationExpiry&&a.time+e.cachedLocationExpiry>Date.now()){if(!a.coords)break e;return a.coords}try{return t=await new Promise((t,r)=>{i.signal.addEventListener("abort",()=>{r(Error("aborted"))}),navigator.geolocation.getCurrentPosition(e=>{t([e.coords.longitude,e.coords.latitude].map(e=>e.toFixed(6)).join(","))},e=>{r(e)},e)})}catch{}finally{e.cachedLocationExpiry&&(a={time:Date.now(),coords:t})}if(i.signal.aborted)return}if("server-geolocation"===e.type)return"ip";if(r&&"map-center"===e.type)return r[1].toFixed(6)+","+r[2].toFixed(6)}}let eG=/^(NORTH|SOUTH|[NS])?\s*([+-]?[0-8]?[0-9])\s*([•º°\.:]|D(?:EG)?(?:REES)?)?\s*,?([6-9][0-9])\s*(['′´’\.:]|M(?:IN)?(?:UTES)?)?\s*(NORTH|SOUTH|[NS])?(?:\s*[,/;]\s*|\s*)(EAST|WEST|[EW])?\s*([+-]?[0-1]?[0-9]?[0-9])\s*([•º°\.:]|D(?:EG)?(?:REES)?)?\s*,?([6-9][0-9])\s*(['′´’\.:]|M(?:IN)?(?:UTES)?)?\s*(EAST|WEST|[EW])?$/i,ez=/^([+-]?[0-8]?[0-9])\s+([0-5]?[0-9]\.\d{3,})[\s,]{1,}([+-]?[0-1]?[0-9]?[0-9])\s+([0-5]?[0-9]\.\d{3,})$/,eD=/^(NORTH|SOUTH|[NS])?[\s]*([+-]?[0-8]?[0-9](?:[\.,]\d{3,}))[\s]*([•º°]?)[\s]*(NORTH|SOUTH|[NS])?[\s]*[,/;]?[\s]*(EAST|WEST|[EW])?[\s]*([+-]?[0-1]?[0-9]?[0-9](?:[\.,]\d{3,}))[\s]*([•º°]?)[\s]*(EAST|WEST|[EW])?$/i,eU=/^(NORTH|SOUTH|[NS])?\s*([+-]?[0-8]?[0-9])\s*(\.)\s*([0-5]?[0-9])\s*(\.)\s*((?:[0-5]?[0-9])(?:[\.,]\d{1,3})?)?\s*(NORTH|SOUTH|[NS])?(?:\s*[,/;]\s*|\s*)(EAST|WEST|[EW])?\s*([+-]?[0-1]?[0-9]?[0-9])\s*(\.)\s*([0-5]?[0-9])\s*(\.)\s*((?:[0-5]?[0-9])(?:[\.,]\d{1,3})?)?\s*(EAST|WEST|[EW])?$/i,eF=/^(NORTH|SOUTH|[NS])?\s*([+-]?[0-8]?[0-9])\s*(D(?:EG)?(?:REES)?)\s*([0-5]?[0-9])\s*(M(?:IN)?(?:UTES)?)\s*((?:[0-5]?[0-9])(?:[\.,]\d{1,3})?)?\s*(S(?:EC)?(?:ONDS)?)?\s*(NORTH|SOUTH|[NS])?(?:\s*[,/;]\s*|\s*)(EAST|WEST|[EW])?\s*([+-]?[0-1]?[0-9]?[0-9])\s*(D(?:EG)?(?:REES)?)\s*([0-5]?[0-9])\s*(M(?:IN)?(?:UTES)?)\s*((?:[0-5]?[0-9])(?:[\.,]\d{1,3})?)?\s*(S(?:EC)?(?:ONDS)?)\s*(EAST|WEST|[EW])?$/i,eq=/^(NORTH|SOUTH|[NS])?\s*([+-]?[0-8]?[0-9])\s*([•º°\.:]|D(?:EG)?(?:REES)?)?\s*,?([0-5]?[0-9](?:[\.,]\d{1,})?)?\s*(['′´’\.:]|M(?:IN)?(?:UTES)?)?\s*,?((?:[0-5]?[0-9])(?:[\.,]\d{1,3})?)?\s*(''|′′|’’|´´|["″”\.])?\s*(NORTH|SOUTH|[NS])?(?:\s*[,/;]\s*|\s*)(EAST|WEST|[EW])?\s*([+-]?[0-1]?[0-9]?[0-9])\s*([•º°\.:]|D(?:EG)?(?:REES)?)?\s*,?([0-5]?[0-9](?:[\.,]\d{1,})?)?\s*(['′´’\.:]|M(?:IN)?(?:UTES)?)?\s*,?((?:[0-5]?[0-9])(?:[\.,]\d{1,3})?)?\s*(''|′′|´´|’’|["″”\.])?\s*(EAST|WEST|[EW])?$/i;function ej(e){if(!["DMS","DM","DD"].includes(e))throw Error("invalid format specified");if(this.decimalCoordinates&&this.decimalCoordinates.trim()){let t;let i=this.decimalCoordinates.split(",").map(e=>Number(e.trim())),r=Number(i[0]),n=Number(i[1]),s=Math.abs(r),o=Math.abs(n),l=r>0?"N":"S",a=n>0?"E":"W";"DD"==e&&(t=`${s}\xb0 ${l}, ${o}\xb0 ${a}`);let u=Math.floor(s),c=Math.floor(o),d=(s-u)*60,h=(o-c)*60;if("DM"==e){let e=eH(d,3).toFixed(3).padStart(6,"0"),i=eH(h,3).toFixed(3).padStart(6,"0");e.endsWith(".000")&&i.endsWith(".000")&&(e=e.replace(/\.000$/,""),i=i.replace(/\.000$/,"")),t=`${u}\xb0 ${e}' ${l}, ${c}\xb0 ${i}' ${a}`}if("DMS"==e){let e=Math.floor(d),i=Math.floor(h),r=((d-e)*60).toFixed(1).padStart(4,"0"),n=((h-i)*60).toFixed(1).padStart(4,"0"),s=e.toString().padStart(2,"0"),o=i.toString().padStart(2,"0");r.endsWith(".0")&&n.endsWith(".0")&&(r=r.replace(/\.0$/,""),n=n.replace(/\.0$/,"")),t=`${u}\xb0 ${s}' ${r}" ${l}, ${c}\xb0 ${o}' ${n}" ${a}`}return t}throw Error("no decimal coordinates to convert")}function eH(e,t){let i=Math.pow(10,t);return Math.round((e+Number.EPSILON)*i)/i}function eZ(e,t){t||(t=5),e=e.replace(/\s+/g," ").trim();let i=null,r=null,n="",s="",o=null,l=[],a=!1;if(eG.test(e))throw Error("invalid coordinate value");if(ez.test(e)){if(a=eV(l=ez.exec(e)))i=Math.abs(l[1])+l[2]/60,0>Number(l[1])&&(i*=-1),r=Math.abs(l[3])+l[4]/60,0>Number(l[3])&&(r*=-1),o="DM";else throw Error("invalid coordinate format")}else if(eD.test(e)){if(a=eV(l=eD.exec(e))){if(i=l[2],r=l[6],i.includes(",")&&(i=i.replace(",",".")),r.includes(",")&&(r=r.replace(",",".")),o="DD",Number(Math.round(i))==Number(i)||Number(Math.round(r))==Number(r))throw Error("integer only coordinate provided");l[1]?(n=l[1],s=l[5]):l[4]&&(n=l[4],s=l[8])}else throw Error("invalid decimal coordinate format")}else if(eU.test(e)){if(a=eV(l=eU.exec(e)))i=Math.abs(parseInt(l[2])),l[4]&&(i+=l[4]/60,o="DM"),l[6]&&(i+=l[6].replace(",",".")/3600,o="DMS"),0>parseInt(l[2])&&(i*=-1),r=Math.abs(parseInt(l[9])),l[11]&&(r+=l[11]/60),l[13]&&(r+=l[13].replace(",",".")/3600),0>parseInt(l[9])&&(r*=-1),l[1]?(n=l[1],s=l[8]):l[7]&&(n=l[7],s=l[14]);else throw Error("invalid DMS coordinates format")}else if(eF.test(e)){if(a=eV(l=eF.exec(e)))i=Math.abs(parseInt(l[2])),l[4]&&(i+=l[4]/60,o="DM"),l[6]&&(i+=l[6]/3600,o="DMS"),0>parseInt(l[2])&&(i*=-1),r=Math.abs(parseInt(l[10])),l[12]&&(r+=l[12]/60),l[14]&&(r+=l[14]/3600),0>parseInt(l[10])&&(r*=-1),l[1]?(n=l[1],s=l[9]):l[8]&&(n=l[8],s=l[16]);else throw Error("invalid DMS coordinates format")}else if(eq.test(e)){if(a=eV(l=eq.exec(e)),l.filter(e=>e).length<=5)throw Error("invalid coordinates format");if(a)i=Math.abs(parseInt(l[2])),l[4]&&(i+=l[4].replace(",",".")/60,o="DM"),l[6]&&(i+=l[6].replace(",",".")/3600,o="DMS"),0>parseInt(l[2])&&(i*=-1),r=Math.abs(parseInt(l[10])),l[12]&&(r+=l[12].replace(",",".")/60),l[14]&&(r+=l[14].replace(",",".")/3600),0>parseInt(l[10])&&(r*=-1),l[1]?(n=l[1],s=l[9]):l[8]&&(n=l[8],s=l[16]);else throw Error("invalid coordinates format")}if(a){let a,u;if(Math.abs(r)>=180)throw Error("invalid longitude value");if(Math.abs(i)>=90)throw Error("invalid latitude value");if(n&&!s||!n&&s)throw Error("invalid coordinates value");if(n&&n==s)throw Error("invalid coordinates format");i.toString().includes(",")&&(i=i.replace(",",".")),r.toString().includes(",")&&(r=r.replace(",","."));let c=/S|SOUTH/i;c.test(n)&&i>0&&(i*=-1),(c=/W|WEST/i).test(s)&&r>0&&(r*=-1);let d=l[0].trim(),h=d.match(/[,/;\u0020]/g);if(null==h){let t=Math.floor(e.length/2);a=d.substring(0,t).trim(),u=d.substring(t).trim()}else{let e;e=h.length%2==1?Math.floor(h.length/2):h.length/2-1;let t=0;if(0==e)t=d.indexOf(h[0]),a=d.substring(0,t).trim(),u=d.substring(t+1).trim();else{let i=0,r=0;for(;i<=e;)r=(t=d.indexOf(h[i],r))+1,i++;a=d.substring(0,t).trim(),u=d.substring(t+1).trim()}}let f=a.split(".");if(2==f.length&&0==f[1]&&2!=f[1].length)throw Error("invalid coordinates format");let g=u.split(".");if(2==g.length&&0==g[1]&&2!=g[1].length)throw Error("invalid coordinates format");if(/^\d+$/.test(a)||/^\d+$/.test(u))throw Error("degree only coordinate/s provided");return Object.freeze({verbatimCoordinates:d,verbatimLatitude:a,verbatimLongitude:u,decimalLatitude:i=Number(Number(i).toFixed(t)),decimalLongitude:r=Number(Number(r).toFixed(t)),decimalCoordinates:`${i},${r}`,originalFormat:o,closeEnough:eQ,toCoordinateFormat:ej})}throw Error("coordinates pattern match failed")}function eV(e){if(!isNaN(e[0]))return!1;let t=[...e];if(t.shift(),t.length%2>0)return!1;let i=/^[-+]?\d+([\.,]\d+)?$/,r=/[eastsouthnorthwest]+/i,n=t.length/2;for(let e=0;e<n;e++){let s=t[e],o=t[e+n],l=i.test(s)&&i.test(o),a=r.test(s)&&r.test(o),u=s==o;if(!(null==s&&null==o)){if(null==s||null==o)return!1;if(l||a||u)continue;return!1}}return!0}function eK(e,t){return 1e-5>=Number(Math.abs(e-t).toFixed(6))}function eQ(e){if(!e)throw Error("coords must be provided");if(e.includes(",")){let t=e.split(",");if(Number(t[0])==NaN||Number(t[1])==NaN)throw Error("coords are not valid decimals");return eK(this.decimalLatitude,Number(t[0]))&&eK(this.decimalLongitude,t[1])}throw Error("coords being tested must be separated by a comma")}let eY=Object.freeze({DMS:"DMS",DM:"DM",DD:"DD"});eZ.to=eY;let eX=[{verbatimCoordinates:"40.123, -74.123",verbatimLatitude:"40.123",verbatimLongitude:"-74.123"},{verbatimCoordinates:"40.123\xb0 N 74.123\xb0 W",verbatimLatitude:"40.123\xb0 N",verbatimLongitude:"74.123\xb0 W"},{verbatimCoordinates:"40.123\xb0 N 74.123\xb0 W",verbatimLatitude:"40.123\xb0 N",verbatimLongitude:"74.123\xb0 W"},{verbatimCoordinates:'40\xb0 7\xb4 22.8" N 74\xb0 7\xb4 22.8" W',verbatimLatitude:'40\xb0 7\xb4 22.8" N',verbatimLongitude:'74\xb0 7\xb4 22.8" W'},{verbatimCoordinates:"40\xb0 7.38’ , -74\xb0 7.38’",verbatimLatitude:"40\xb0 7.38’",verbatimLongitude:"-74\xb0 7.38’"},{verbatimCoordinates:"N40\xb07’22.8’’, W74\xb07’22.8’’",verbatimLatitude:"N40\xb07’22.8’’",verbatimLongitude:"W74\xb07’22.8’’"},{verbatimCoordinates:'40\xb07’22.8"N, 74\xb07’22.8"W',verbatimLatitude:'40\xb07’22.8"N',verbatimLongitude:'74\xb07’22.8"W'},{verbatimCoordinates:`40\xb07'22.8"N, 74\xb07'22.8"W`,verbatimLatitude:`40\xb07'22.8"N`,verbatimLongitude:`74\xb07'22.8"W`},{verbatimCoordinates:"40 7 22.8, -74 7 22.8",verbatimLatitude:"40 7 22.8",verbatimLongitude:"-74 7 22.8"},{verbatimCoordinates:"40.123 -74.123",verbatimLatitude:"40.123",verbatimLongitude:"-74.123"},{verbatimCoordinates:"40.123\xb0,-74.123\xb0",verbatimLatitude:"40.123\xb0",verbatimLongitude:"-74.123\xb0"},{verbatimCoordinates:"40.123N74.123W",verbatimLatitude:"40.123N",verbatimLongitude:"74.123W"},{verbatimCoordinates:"4007.38N7407.38W",verbatimLatitude:"4007.38N",verbatimLongitude:"7407.38W"},{verbatimCoordinates:'40\xb07’22.8"N, 74\xb07’22.8"W',verbatimLatitude:'40\xb07’22.8"N',verbatimLongitude:'74\xb07’22.8"W'},{verbatimCoordinates:"400722.8N740722.8W",verbatimLatitude:"400722.8N",verbatimLongitude:"740722.8W"},{verbatimCoordinates:"N 40 7.38 W 74 7.38",verbatimLatitude:"N 40 7.38",verbatimLongitude:"W 74 7.38"},{verbatimCoordinates:"40:7:22.8N 74:7:22.8W",verbatimLatitude:"40:7:22.8N",verbatimLongitude:"74:7:22.8W"},{verbatimCoordinates:"40:7:23N,74:7:23W",verbatimLatitude:"40:7:23N",verbatimLongitude:"74:7:23W",decimalLatitude:40.1230555555,decimalLongitude:-74.1230555555},{verbatimCoordinates:'40\xb07’23"N 74\xb07’23"W',verbatimLatitude:'40\xb07’23"N',verbatimLongitude:'74\xb07’23"W',decimalLatitude:40.1230555555,decimalLongitude:-74.12305555555555},{verbatimCoordinates:'40\xb07’23"S 74\xb07’23"E',verbatimLatitude:'40\xb07’23"S',verbatimLongitude:'74\xb07’23"E',decimalLatitude:-40.1230555555,decimalLongitude:74.12305555555555},{verbatimCoordinates:'40\xb07’23" -74\xb07’23"',verbatimLatitude:'40\xb07’23"',verbatimLongitude:'-74\xb07’23"',decimalLatitude:40.1230555555,decimalLongitude:-74.123055555},{verbatimCoordinates:'40d 7’ 23" N 74d 7’ 23" W',verbatimLatitude:'40d 7’ 23" N',verbatimLongitude:'74d 7’ 23" W',decimalLatitude:40.1230555555,decimalLongitude:-74.123055555},{verbatimCoordinates:"40.123N 74.123W",verbatimLatitude:"40.123N",verbatimLongitude:"74.123W"},{verbatimCoordinates:"40\xb0 7.38, -74\xb0 7.38",verbatimLatitude:"40\xb0 7.38",verbatimLongitude:"-74\xb0 7.38"},{verbatimCoordinates:"40\xb0 7.38, -74\xb0 7.38",verbatimLatitude:"40\xb0 7.38",verbatimLongitude:"-74\xb0 7.38"},{verbatimCoordinates:"40 7 22.8; -74 7 22.8",verbatimLatitude:"40 7 22.8",verbatimLongitude:"-74 7 22.8"}],eJ={decimalLatitude:40.123,decimalLongitude:-74.123},e0=[{verbatimCoordinates:`50\xb04'17.698"south, 14\xb024'2.826"east`,verbatimLatitude:`50\xb04'17.698"south`,verbatimLongitude:`14\xb024'2.826"east`,decimalLatitude:-50.07158277777778,decimalLongitude:14.400785},{verbatimCoordinates:"50d4m17.698S 14d24m2.826E",verbatimLatitude:"50d4m17.698S",verbatimLongitude:"14d24m2.826E",decimalLatitude:-50.07158277777778,decimalLongitude:14.400785},{verbatimCoordinates:"40:26:46N,79:56:55W",verbatimLatitude:"40:26:46N",verbatimLongitude:"79:56:55W",decimalLatitude:40.44611111111111,decimalLongitude:-79.9486111111111},{verbatimCoordinates:"40:26:46.302N 79:56:55.903W",verbatimLatitude:"40:26:46.302N",verbatimLongitude:"79:56:55.903W",decimalLatitude:40.446195,decimalLongitude:-79.94886194444445},{verbatimCoordinates:"40\xb026′47″N 79\xb058′36″W",verbatimLatitude:"40\xb026′47″N",verbatimLongitude:"79\xb058′36″W",decimalLatitude:40.44638888888889,decimalLongitude:-79.97666666666667},{verbatimCoordinates:"40d 26′ 47″ N 79d 58′ 36″ W",verbatimLatitude:"40d 26′ 47″ N",verbatimLongitude:"79d 58′ 36″ W",decimalLatitude:40.44638888888889,decimalLongitude:-79.97666666666667},{verbatimCoordinates:"40.446195N 79.948862W",verbatimLatitude:"40.446195N",verbatimLongitude:"79.948862W",decimalLatitude:40.446195,decimalLongitude:-79.948862},{verbatimCoordinates:"40,446195\xb0 79,948862\xb0",verbatimLatitude:"40,446195\xb0",verbatimLongitude:"79,948862\xb0",decimalLatitude:40.446195,decimalLongitude:79.948862},{verbatimCoordinates:"40\xb0 26.7717, -79\xb0 56.93172",verbatimLatitude:"40\xb0 26.7717",verbatimLongitude:"-79\xb0 56.93172",decimalLatitude:40.446195,decimalLongitude:-79.948862},{verbatimCoordinates:"40.446195, -79.948862",verbatimLatitude:"40.446195",verbatimLongitude:"-79.948862",decimalLatitude:40.446195,decimalLongitude:-79.948862},{verbatimCoordinates:"40.123256; -74.123256",verbatimLatitude:"40.123256",verbatimLongitude:"-74.123256",decimalLatitude:40.123256,decimalLongitude:-74.123256},{verbatimCoordinates:"18\xb024S 22\xb045E",verbatimLatitude:"18\xb024S",verbatimLongitude:"22\xb045E",decimalLatitude:-18.4,decimalLongitude:22.75}],e1=[{verbatimCoordinates:"10.432342S 10.6345345E",verbatimLatitude:"10.432342S",verbatimLongitude:"10.6345345E",decimalLatitude:-10.432342,decimalLongitude:10.6345345},{verbatimCoordinates:"10.00S 10.00E",verbatimLatitude:"10.00S",verbatimLongitude:"10.00E",decimalLatitude:-10,decimalLongitude:10},{verbatimCoordinates:"00.00S 01.00E",verbatimLatitude:"00.00S",verbatimLongitude:"01.00E",decimalLatitude:0,decimalLongitude:1},{verbatimCoordinates:"18.24S 22.45E",verbatimLatitude:"18.24S",verbatimLongitude:"22.45E",decimalLatitude:-18.4,decimalLongitude:22.75},{verbatimCoordinates:"27deg 15min 45.2sec S 18deg 32min 53.7sec E",verbatimLatitude:"27deg 15min 45.2sec S",verbatimLongitude:"18deg 32min 53.7sec E",decimalLatitude:-27.262555555555554,decimalLongitude:18.54825},{verbatimCoordinates:"-23.3245\xb0 S / 28.2344\xb0 E",verbatimLatitude:"-23.3245\xb0 S",verbatimLongitude:"28.2344\xb0 E",decimalLatitude:-23.3245,decimalLongitude:28.2344},{verbatimCoordinates:"40\xb0 26.7717 -79\xb0 56.93172",verbatimLatitude:"40\xb0 26.7717",verbatimLongitude:"-79\xb0 56.93172",decimalLatitude:40.446195,decimalLongitude:-79.948862},{verbatimCoordinates:"27.15.45S 18.32.53E",verbatimLatitude:"27.15.45S",verbatimLongitude:"18.32.53E",decimalLatitude:-27.2625,decimalLongitude:18.548055},{verbatimCoordinates:"-27.15.45 18.32.53",verbatimLatitude:"-27.15.45",verbatimLongitude:"18.32.53",decimalLatitude:-27.2625,decimalLongitude:18.548055},{verbatimCoordinates:"27.15.45.2S 18.32.53.4E",verbatimLatitude:"27.15.45.2S",verbatimLongitude:"18.32.53.4E",decimalLatitude:-27.262556,decimalLongitude:18.548167},{verbatimCoordinates:"27.15.45,2S 18.32.53,4E",verbatimLatitude:"27.15.45,2S",verbatimLongitude:"18.32.53,4E",decimalLatitude:-27.262556,decimalLongitude:18.548167},{verbatimCoordinates:"S23.43563 \xb0  E22.45634 \xb0",verbatimLatitude:"S23.43563 \xb0",verbatimLongitude:"E22.45634 \xb0",decimalLatitude:-23.43563,decimalLongitude:22.45634},{verbatimCoordinates:"27,71372\xb0 S 23,07771\xb0 E",verbatimLatitude:"27,71372\xb0 S",verbatimLongitude:"23,07771\xb0 E",decimalLatitude:-27.71372,decimalLongitude:23.07771},{verbatimCoordinates:"27.45.34 S 23.23.23 E",verbatimLatitude:"27.45.34 S",verbatimLongitude:"23.23.23 E",decimalLatitude:-27.759444,decimalLongitude:23.38972222},{verbatimCoordinates:"S 27.45.34 E 23.23.23",verbatimLatitude:"S 27.45.34",verbatimLongitude:"E 23.23.23",decimalLatitude:-27.759444,decimalLongitude:23.38972222},{verbatimCoordinates:"53 16.3863,4 52.8171",verbatimLatitude:"53 16.3863",verbatimLongitude:"4 52.8171",decimalLatitude:53.273105,decimalLongitude:4.88029},{verbatimCoordinates:"50 8.2914,-5 2.4447",verbatimLatitude:"50 8.2914",verbatimLongitude:"-5 2.4447",decimalLatitude:50.13819,decimalLongitude:-5.040745},{verbatimCoordinates:"N 48\xb0 30,6410', E 18\xb0 57,4583'",verbatimLatitude:"N 48\xb0 30,6410'",verbatimLongitude:"E 18\xb0 57,4583'",decimalLatitude:48.51068,decimalLongitude:18.95764},{verbatimCoordinates:"1.23456, 18.33453",verbatimLatitude:"1.23456",verbatimLongitude:"18.33453",decimalLatitude:1.23456,decimalLongitude:18.33453}],e2=function(){let e=[];return eX.forEach(t=>{t.decimalLatitude?e.push(t):e.push({...t,...eJ})}),[...e,...e0,...e1]}();function e4(e,t,i){let r=e.slice();return r[97]=t[i],r[99]=i,r}function e7(e){let t,i;return t=new eN({}),{c(){er(t.$$.fragment)},m(e,r){en(t,e,r),i=!0},i(e){i||(J(t.$$.fragment,e),i=!0)},o(e){ee(t.$$.fragment,e),i=!1},d(e){es(t,e)}}}function e3(e){let t,i,r,n,s;return i=new eP({}),{c(){t=N("button"),er(i.$$.fragment),P(t,"type","button"),P(t,"title",e[10]),P(t,"class","svelte-bz0zu3"),A(t,"active",e[0])},m(o,l){k(o,t,l),en(i,t,null),r=!0,n||(s=O(t,"click",e[79]),n=!0)},p(e,i){(!r||1024&i[0])&&P(t,"title",e[10]),(!r||1&i[0])&&A(t,"active",e[0])},i(e){r||(J(i.$$.fragment,e),r=!0)},o(e){ee(i.$$.fragment,e),r=!1},d(e){e&&C(t),es(i),n=!1,s()}}}function e5(e){let t,i=[],r=new Map,n,s,o,l=et(e[13]),a=e=>e[97].id+(e[97].address?","+e[97].address:"");for(let t=0;t<l.length;t+=1){let n=e4(e,l,t),s=a(n);r.set(s,i[t]=te(s,n))}return{c(){t=N("ul");for(let e=0;e<i.length;e+=1)i[e].c();P(t,"class","options svelte-bz0zu3"),P(t,"role","listbox")},m(r,l){k(r,t,l);for(let e=0;e<i.length;e+=1)i[e]&&i[e].m(t,null);n=!0,s||(o=[O(t,"mouseleave",e[27]),O(t,"blur",e[83]),O(t,"keydown",e[23])],s=!0)},p(e,n){102823936&n[0]&&(l=et(e[13]),Y(),i=function(e,t,i,r,n,s,o,l,a,u,c,d){let h=e.length,f=s.length,g=h,p={};for(;g--;)p[e[g].key]=g;let m=[],y=new Map,v=new Map,b=[];for(g=f;g--;){let e=d(n,s,g),r=i(e),l=o.get(r);l?b.push(()=>l.p(e,t)):(l=u(r,e)).c(),y.set(r,m[g]=l),r in p&&v.set(r,Math.abs(g-p[r]))}let x=new Set,w=new Set;function E(e){J(e,1),e.m(l,c),o.set(e.key,e),c=e.first,f--}for(;h&&f;){let t=m[f-1],i=e[h-1],r=t.key,n=i.key;t===i?(c=t.first,h--,f--):y.has(n)?!o.has(r)||x.has(r)?E(t):w.has(n)?h--:v.get(r)>v.get(n)?(w.add(r),E(t)):(x.add(n),h--):(a(i,o),h--)}for(;h--;){let t=e[h];y.has(t.key)||a(t,o)}for(;f;)E(m[f-1]);return L(b),m}(i,n,a,0,e,l,r,t,ei,te,null,e4),X())},i(e){if(!n){for(let e=0;e<l.length;e+=1)J(i[e]);n=!0}},o(e){for(let e=0;e<i.length;e+=1)ee(i[e]);n=!1},d(e){e&&C(t);for(let e=0;e<i.length;e+=1)i[e].d();s=!1,L(o)}}}function e8(e){let t,i,r,n,s,o;return i=new ed({}),{c(){t=N("div"),er(i.$$.fragment),r=_(),n=N("div"),s=R(e[8]),P(n,"class","svelte-bz0zu3"),P(t,"class","no-results svelte-bz0zu3")},m(e,l){k(e,t,l),en(i,t,null),T(t,r),T(t,n),T(n,s),o=!0},p(e,t){(!o||256&t[0])&&I(s,e[8])},i(e){o||(J(i.$$.fragment,e),o=!0)},o(e){ee(i.$$.fragment,e),o=!1},d(e){e&&C(t),es(i)}}}function e9(e){let t;return{c(){t=R("")},m(e,i){k(e,t,i)},p:v,i:v,o:v,d(e){e&&C(t)}}}function e6(e){let t,i,r,n,s,o,l,a,u,c,d;return i=new ed({}),a=new eu({}),{c(){t=N("div"),er(i.$$.fragment),r=_(),n=N("div"),s=R(e[7]),o=_(),l=N("button"),er(a.$$.fragment),P(n,"class","svelte-bz0zu3"),P(l,"class","svelte-bz0zu3"),P(t,"class","error svelte-bz0zu3")},m(h,f){k(h,t,f),en(i,t,null),T(t,r),T(t,n),T(n,s),T(t,o),T(t,l),en(a,l,null),u=!0,c||(d=O(l,"click",e[80]),c=!0)},p(e,t){(!u||128&t[0])&&I(s,e[7])},i(e){u||(J(i.$$.fragment,e),J(a.$$.fragment,e),u=!0)},o(e){ee(i.$$.fragment,e),ee(a.$$.fragment,e),u=!1},d(e){e&&C(t),es(i),es(a),c=!1,d()}}}function te(e,t){var i;let r,n,s;return(n=new ek({props:{feature:t[97],showPlaceType:t[11],style:t[15]===t[99]?"selected":(null==(i=t[14])?void 0:i.id)===t[97].id?"picked":"default",missingIconsCache:t[21],iconsBaseUrl:t[12]}})).$on("mouseenter",function(){return t[81](t[99])}),n.$on("select",function(){return t[82](t[97])}),{key:e,first:null,c(){r=R(""),er(n.$$.fragment),this.first=r},m(e,t){k(e,r,t),en(n,e,t),s=!0},p(e,i){var r;t=e;let s={};8192&i[0]&&(s.feature=t[97]),2048&i[0]&&(s.showPlaceType=t[11]),57344&i[0]&&(s.style=t[15]===t[99]?"selected":(null==(r=t[14])?void 0:r.id)===t[97].id?"picked":"default"),4096&i[0]&&(s.iconsBaseUrl=t[12]),n.$set(s)},i(e){s||(J(n.$$.fragment,e),s=!0)},o(e){ee(n.$$.fragment,e),s=!1},d(e){e&&C(r),es(n,e)}}}function tt(e){let t,i,r,n,s,o,l,a,u,c,d,h,f,g,p,m,y,v,b,x,w;s=new eA({}),d=new eu({});let E=e[20]&&e7(),S="button"===e[6]&&e3(e),M=e[70].default,R=function(e,t,i,r){if(e){let r=$(e,t,i,null);return e[0](r)}}(M,e,e[69],0),I=[e6,e9,e8,e5],W=[];function B(e,t){var i,r;return e[19]?0:e[16]||e[4]?(null==(i=e[13])?void 0:i.length)===0?2:null!=(r=e[13])&&r.length&&(e[16]||e[4])?3:-1:1}return~(m=B(e))&&(y=W[m]=I[m](e)),{c(){t=_(),i=N("form"),r=N("div"),n=N("button"),er(s.$$.fragment),o=_(),l=N("input"),a=_(),u=N("div"),c=N("button"),er(d.$$.fragment),h=_(),E&&E.c(),f=_(),S&&S.c(),g=_(),R&&R.c(),p=_(),y&&y.c(),P(n,"class","search-button svelte-bz0zu3"),P(n,"type","button"),P(l,"placeholder",e[9]),P(l,"aria-label",e[9]),P(l,"class","svelte-bz0zu3"),P(c,"type","button"),P(c,"title",e[3]),P(c,"class","svelte-bz0zu3"),P(u,"class","clear-button-container svelte-bz0zu3"),A(u,"displayable",""!==e[1]),P(r,"class","input-group svelte-bz0zu3"),P(i,"class",v=(e[2]??"")+" svelte-bz0zu3"),A(i,"can-collapse",e[5]&&""===e[1])},m(y,v){var L,$,C;k(y,t,v),k(y,i,v),T(i,r),T(r,n),en(s,n,null),T(r,o),T(r,l),e[72](l),L=l,$=e[1],L.value=$??"",T(r,a),T(r,u),T(u,c),en(d,c,null),T(u,h),E&&E.m(u,null),T(r,f),S&&S.m(r,null),T(r,g),R&&R.m(r,null),T(i,p),~m&&W[m].m(i,null),b=!0,x||(w=[O(n,"click",e[71]),O(l,"input",e[73]),O(l,"focus",e[74]),O(l,"blur",e[75]),O(l,"click",e[76]),O(l,"keydown",e[23]),O(l,"input",e[24]),O(l,"change",e[77]),O(c,"click",e[78]),O(i,"submit",(C=e[22],function(e){return e.preventDefault(),C.call(this,e)}))],x=!0)},p(e,t){var n,s,o;(!b||512&t[0])&&P(l,"placeholder",e[9]),(!b||512&t[0])&&P(l,"aria-label",e[9]),2&t[0]&&l.value!==e[1]&&(n=l,s=e[1],n.value=s??""),(!b||8&t[0])&&P(c,"title",e[3]),e[20]?E?1048576&t[0]&&J(E,1):((E=e7()).c(),J(E,1),E.m(u,null)):E&&(Y(),ee(E,1,1,()=>{E=null}),X()),(!b||2&t[0])&&A(u,"displayable",""!==e[1]),"button"===e[6]?S?(S.p(e,t),64&t[0]&&J(S,1)):((S=e3(e)).c(),J(S,1),S.m(r,g)):S&&(Y(),ee(S,1,1,()=>{S=null}),X()),R&&R.p&&(!b||128&t[2])&&function(e,t,i,r,n,s){if(n){let s=$(t,i,r,null);e.p(s,n)}}(R,M,e,e[69],b?(o=e[69],M[2],o.dirty):function(e){if(e.ctx.length>32){let t=[],i=e.ctx.length/32;for(let e=0;e<i;e++)t[e]=-1;return t}return -1}(e[69]),0);let a=m;(m=B(e))===a?~m&&W[m].p(e,t):(y&&(Y(),ee(W[a],1,1,()=>{W[a]=null}),X()),~m?((y=W[m])?y.p(e,t):(y=W[m]=I[m](e)).c(),J(y,1),y.m(i,null)):y=null),(!b||4&t[0]&&v!==(v=(e[2]??"")+" svelte-bz0zu3"))&&P(i,"class",v),(!b||38&t[0])&&A(i,"can-collapse",e[5]&&""===e[1])},i(e){b||(J(!1),J(s.$$.fragment,e),J(d.$$.fragment,e),J(E),J(S),J(R,e),J(y),b=!0)},o(e){ee(!1),ee(s.$$.fragment,e),ee(d.$$.fragment,e),ee(E),ee(S),ee(R,e),ee(y),b=!1},d(r){r&&(C(t),C(i)),es(s),e[72](null),es(d),E&&E.d(),S&&S.d(),R&&R.d(r),~m&&W[m].d(),x=!1,L(w)}}}function ti(e,t,i){var r;let n,s,o,{$$slots:l={},$$scope:a}=t,u={continental_marine:4,country:4,major_landform:8,region:5,subregion:6,county:7,joint_municipality:8,joint_submunicipality:9,municipality:10,municipal_district:11,locality:12,neighbourhood:13,place:14,postal_code:14,road:16,poi:17,address:18,"poi.peak":15,"poi.shop":18,"poi.cafe":18,"poi.restaurant":18,"poi.aerodrome":13},{class:c}=t,{apiKey:d}=t,{bbox:h}=t,{clearButtonTitle:f="clear"}=t,{clearOnBlur:g=!1}=t,{clearListOnPick:p=!1}=t,{keepListOpen:m=!1}=t,{collapsed:y=!1}=t,{country:v}=t,{debounceSearch:b=200}=t,{enableReverse:x="never"}=t,{errorMessage:L="Something went wrong…"}=t,{filter:w=()=>!0}=t,{flyTo:E=!0}=t,{fuzzyMatch:S=!0}=t,{language:$}=t,{limit:T}=t,{reverseGeocodingLimit:k=41415112612}=t,{mapController:C}=t,{minLength:N=2}=t,{noResultsMessage:M="Oops! Looks like you're trying to predict something that's not quite right. We can't seem to find what you're looking for. Maybe try double-checking your spelling or try a different search term. Keep on typing - we'll do our best to get you where you need to go!"}=t,{placeholder:R="Search"}=t,{proximity:_=[{type:"server-geolocation"}]}=t,{reverseActive:O="always"===x}=t,{reverseButtonTitle:P="toggle reverse geocoding"}=t,{searchValue:I=""}=t,{pickedResultStyle:A="full-geometry"}=t,{showPlaceType:G="if-needed"}=t,{showResultsWhileTyping:z=!0}=t,{selectFirst:U=!0}=t,{flyToSelected:F=!1}=t,{markerOnSelected:q=!0}=t,{types:j}=t,H=[],{reverseGeocodingTypes:Z=H}=t,{exhaustiveReverseGeocoding:V=!1}=t,{excludeTypes:K=!1}=t,Q=void 0,{reverseGeocodingExcludeTypes:Y=Q}=t,{zoom:X=u}=t,{apiUrl:J="https://api.maptiler.com/geocoding"}=t,{fetchParameters:ee={}}=t,{iconsBaseUrl:et="https://cdn.maptiler.com/maptiler-geocoding-control/v2.1.6/icons/"}=t,{adjustUrlQuery:ei=()=>{}}=t,{adjustUrl:er=()=>{}}=t;function en(e,t=!0,r=!1){i(1,I=e),t?(i(15,ec=-1),ex()):(e$(void 0,!r,r),setTimeout(()=>{eu.focus(),eu.select()}))}let es,eo,el,ea="",eu,ec=-1,ed,eh=[],ef,eg,ep,em,ey=!1,ev=new Set,eb=B();function ex(e){if(i(17,ey=!1),eg&&(clearTimeout(eg),eg=void 0),ec>-1&&es)i(14,el=es[ec]),i(1,I="reverse"===el.place_type[0]?el.place_name:el.place_name.replace(/,.*/,"")),i(19,ed=void 0),i(64,eo=void 0),i(15,ec=-1);else if(I){let t=e||!eL(I);ew(I,{exact:!0}).then(()=>{i(64,eo=es),i(14,el=void 0),t&&function(){var e;let t;if(!(null!=eo&&eo.length)||!E)return;let i=[180,90,-180,-90],r=!eo.some(e=>!e.matching_text);for(let n of eo){let s=eS(n);if(t=void 0===t?s:void 0===s?t:Math.max(t,s),r||!n.matching_text)for(let t of[0,1,2,3])i[t]=Math[t<2?"min":"max"](i[t],(null==(e=n.bbox)?void 0:e[t])??n.center[t%2])}C&&eo.length>0&&(el&&i[0]===i[2]&&i[1]===i[3]?C.flyTo(el.center,eS(el)):C.fitBounds(eW(i),50,t))}()}).catch(e=>i(19,ed=e))}}function eL(e){try{return eZ(e,6)}catch{return!1}}async function ew(e,{byId:t=!1,exact:r=!1}={}){var n,o,l;i(19,ed=void 0),null==ef||ef.abort();let a=new AbortController;i(20,ef=a);try{let u;let c=eL(e),f=new URL(J+"/"+encodeURIComponent(c?c.decimalLongitude+","+c.decimalLatitude:e)+".json"),g=f.searchParams;void 0!==$&&g.set("language",Array.isArray($)?$.join(","):$??"");let[m]=(null==C?void 0:C.getCenterAndZoom())??[],y=null==(n=c&&Z!==H?Z:j)?void 0:n.map(e=>"string"==typeof e?e:void 0===m||(e[0]??0)<=m&&m<(e[1]??1/0)?e[2]:void 0).filter(e=>void 0!==e);y&&(y=[...new Set(y)],g.set("types",y.join(",")));let b=c&&Y!==Q?Y:K;if(b&&g.set("excludeTypes",String(b)),h&&g.set("bbox",h.map(e=>e.toFixed(6)).join(",")),v&&g.set("country",Array.isArray(v)?v.join(","):v),!t&&!c){let e=await eB(C,_,a);e&&g.set("proximity",e),(r||!z)&&g.set("autocomplete","false"),g.set("fuzzyMatch",String(S))}let x=41415112612===k?T:k;void 0!==x&&x>1&&(null==y?void 0:y.length)!==1&&console.warn("For reverse geocoding when limit > 1 then types must contain single value."),c?(1===x||void 0!==x&&(V||(null==y?void 0:y.length)===1))&&g.set("limit",String(x)):void 0!==T&&g.set("limit",String(T)),d&&g.set("key",d),ei(g),er(f);let L=""===f.searchParams.get("types")&&"true"!==f.searchParams.get("excludeTypes"),E=f.toString();if(E===ea){t?(p&&i(13,es=void 0),i(14,el=eh[0])):(i(13,es=eh),(null==(o=es[ec])?void 0:o.id)!==(null==s?void 0:s.id)&&i(15,ec=-1));return}if(ea=E,L)u={type:"FeatureCollection",features:[]};else{let e=await fetch(E,{signal:a.signal,...ee});if(!e.ok)throw Error(await e.text());u=await e.json()}eb("response",{url:E,featureCollection:u}),t?(p&&i(13,es=void 0),i(14,el=u.features[0]),eh=[el]):(i(13,es=u.features.filter(w)),c&&es.unshift({type:"Feature",properties:{},id:"reverse_"+c.decimalLongitude+"_"+c.decimalLatitude,text:c.decimalLatitude+", "+c.decimalLongitude,place_name:c.decimalLatitude+", "+c.decimalLongitude,place_type:["reverse"],center:[c.decimalLongitude,c.decimalLatitude],bbox:[c.decimalLongitude,c.decimalLatitude,c.decimalLongitude,c.decimalLatitude],geometry:{type:"Point",coordinates:[c.decimalLongitude,c.decimalLatitude]}}),eh=es,(null==(l=es[ec])?void 0:l.id)!==(null==s?void 0:s.id)&&i(15,ec=-1),c&&eu.focus())}catch(e){if(e&&"object"==typeof e&&"name"in e&&"AbortError"===e.name)return;throw e}finally{a===ef&&i(20,ef=void 0)}}function eE(){el&&C&&(el.bbox&&(el.bbox[0]!==el.bbox[2]||el.bbox[1]!==el.bbox[3])?C.fitBounds(eW(el.bbox),50,eS(el)):C.flyTo(el.center,eS(el)))}function eS(e){var t;if(!e.bbox||e.bbox[0]!==e.bbox[2]&&e.bbox[1]!==e.bbox[3])return;let i=e.id.replace(/\..*/,"");return(Array.isArray(null==(t=e.properties)?void 0:t.categories)?e.properties.categories.reduce((e,t)=>{let r=X[i+"."+t];return void 0===e?r:void 0===r?e:Math.max(e,r)},void 0):void 0)??X[i]}function e$(e,t=!0,r=!1){if(i(19,ed=void 0),i(14,el=void 0),i(17,ey=!0),z||r){if(eg&&clearTimeout(eg),I.length<N)return;let e=I;eg=window.setTimeout(()=>{ew(e).catch(e=>i(19,ed=e))},t?b:0)}else i(13,es=void 0),i(19,ed=void 0)}function eT(e){el&&(null==el?void 0:el.id)===(null==e?void 0:e.id)?eE():(i(14,el=e),i(1,I=e.place_name))}function ek(e){i(15,ec=e)}return r=()=>{C&&(C.setEventHandler(void 0),C.indicateReverse(!1),C.setSelectedMarker(-1),C.setFeatures(void 0,void 0,!1))},W().$$.on_destroy.push(r),e.$$set=e=>{"class"in e&&i(2,c=e.class),"apiKey"in e&&i(29,d=e.apiKey),"bbox"in e&&i(30,h=e.bbox),"clearButtonTitle"in e&&i(3,f=e.clearButtonTitle),"clearOnBlur"in e&&i(31,g=e.clearOnBlur),"clearListOnPick"in e&&i(32,p=e.clearListOnPick),"keepListOpen"in e&&i(4,m=e.keepListOpen),"collapsed"in e&&i(5,y=e.collapsed),"country"in e&&i(33,v=e.country),"debounceSearch"in e&&i(34,b=e.debounceSearch),"enableReverse"in e&&i(6,x=e.enableReverse),"errorMessage"in e&&i(7,L=e.errorMessage),"filter"in e&&i(35,w=e.filter),"flyTo"in e&&i(36,E=e.flyTo),"fuzzyMatch"in e&&i(37,S=e.fuzzyMatch),"language"in e&&i(38,$=e.language),"limit"in e&&i(39,T=e.limit),"reverseGeocodingLimit"in e&&i(40,k=e.reverseGeocodingLimit),"mapController"in e&&i(41,C=e.mapController),"minLength"in e&&i(42,N=e.minLength),"noResultsMessage"in e&&i(8,M=e.noResultsMessage),"placeholder"in e&&i(9,R=e.placeholder),"proximity"in e&&i(43,_=e.proximity),"reverseActive"in e&&i(0,O=e.reverseActive),"reverseButtonTitle"in e&&i(10,P=e.reverseButtonTitle),"searchValue"in e&&i(1,I=e.searchValue),"pickedResultStyle"in e&&i(44,A=e.pickedResultStyle),"showPlaceType"in e&&i(11,G=e.showPlaceType),"showResultsWhileTyping"in e&&i(45,z=e.showResultsWhileTyping),"selectFirst"in e&&i(46,U=e.selectFirst),"flyToSelected"in e&&i(47,F=e.flyToSelected),"markerOnSelected"in e&&i(48,q=e.markerOnSelected),"types"in e&&i(49,j=e.types),"reverseGeocodingTypes"in e&&i(50,Z=e.reverseGeocodingTypes),"exhaustiveReverseGeocoding"in e&&i(51,V=e.exhaustiveReverseGeocoding),"excludeTypes"in e&&i(52,K=e.excludeTypes),"reverseGeocodingExcludeTypes"in e&&i(53,Y=e.reverseGeocodingExcludeTypes),"zoom"in e&&i(54,X=e.zoom),"apiUrl"in e&&i(55,J=e.apiUrl),"fetchParameters"in e&&i(56,ee=e.fetchParameters),"iconsBaseUrl"in e&&i(12,et=e.iconsBaseUrl),"adjustUrlQuery"in e&&i(57,ei=e.adjustUrlQuery),"adjustUrl"in e&&i(58,er=e.adjustUrl),"$$scope"in e&&i(69,a=e.$$scope)},e.$$.update=()=>{if(64&e.$$.dirty[0]&&i(0,O="always"===x),16384&e.$$.dirty[0]|8192&e.$$.dirty[1]&&"marker-only"!==A&&el&&!el.address&&"Point"===el.geometry.type&&"reverse"!==el.place_type[0]&&ew(el.id,{byId:!0}).catch(e=>i(19,ed=e)),16384&e.$$.dirty[0]|1058&e.$$.dirty[1]|8&e.$$.dirty[2]&&(C&&el&&el.id!==em&&E&&(eE(),p&&i(13,es=void 0),i(64,eo=void 0),i(15,ec=-1)),i(65,em=null==el?void 0:el.id)),196608&e.$$.dirty[0]|1&e.$$.dirty[1]&&setTimeout(()=>{i(16,ep=ey),g&&!ep&&i(1,I="")}),8194&e.$$.dirty[0]|2048&e.$$.dirty[1]&&I.length<N&&(i(13,es=void 0),i(19,ed=void 0),i(64,eo=es)),57344&e.$$.dirty[0]|32768&e.$$.dirty[1]&&U&&null!=es&&es.length&&-1==ec&&!el&&i(15,ec=0),8192&e.$$.dirty[0]|4&e.$$.dirty[2]&&eo!==es&&i(64,eo=void 0),73729&e.$$.dirty[0]|1024&e.$$.dirty[1]|4&e.$$.dirty[2]&&C&&C.setEventHandler(e=>{switch(e.type){case"mapClick":var t;O&&(t=e.coordinates,i(0,O="always"===x),i(13,es=void 0),i(14,el=void 0),i(15,ec=-1),en(t[1].toFixed(6)+", "+(function(e,t,i){let r=t[1],n=t[0],s=r-n;return e===r&&i?e:((e-n)%s+s)%s+n})(t[0],[-180,180],!0).toFixed(6),!1,!0));break;case"markerClick":{let t=null==es?void 0:es.find(t=>t.id===e.id);t&&eT(t)}break;case"markerMouseEnter":eo&&i(15,ec=ep?(null==es?void 0:es.findIndex(t=>t.id===e.id))??-1:-1);break;case"markerMouseLeave":eo&&i(15,ec=-1)}}),40960&e.$$.dirty[0]&&i(66,s=null==es?void 0:es[ec]),66592&e.$$.dirty[1]|16&e.$$.dirty[2]&&C&&s&&E&&F&&C.flyTo(s.center,eS(s)),8192&e.$$.dirty[1]&&i(68,n="full-geometry-including-polygon-center-marker"===A),132096&e.$$.dirty[1]|64&e.$$.dirty[2]&&(q||null==C||C.setFeatures(void 0,void 0,n)),16384&e.$$.dirty[0]|132096&e.$$.dirty[1]|84&e.$$.dirty[2]&&C&&q&&!eo&&(C.setFeatures(s?[s]:void 0,el,n),C.setSelectedMarker(s?0:-1)),16384&e.$$.dirty[0]|1024&e.$$.dirty[1]|68&e.$$.dirty[2]&&C&&C.setFeatures(eo,el,n),32768&e.$$.dirty[0]|1024&e.$$.dirty[1]|4&e.$$.dirty[2]&&eo&&C&&C.setSelectedMarker(ec),2&e.$$.dirty[0]|1024&e.$$.dirty[1]&&C){let e=eL(I);C.setReverseMarker(e?[e.decimalLongitude,e.decimalLatitude]:void 0)}16&e.$$.dirty[2]&&eb("select",{feature:s}),16384&e.$$.dirty[0]&&eb("pick",{feature:el}),73744&e.$$.dirty[0]&&i(67,o=!!(null!=es&&es.length)&&(ep||m)),32&e.$$.dirty[2]&&eb("optionsvisibilitychange",{optionsVisible:o}),8192&e.$$.dirty[0]&&eb("featureslisted",{features:es}),4&e.$$.dirty[2]&&eb("featuresmarked",{features:eo}),1&e.$$.dirty[0]&&eb("reversetoggle",{reverse:O}),2&e.$$.dirty[0]&&eb("querychange",{query:I}),1&e.$$.dirty[0]|1024&e.$$.dirty[1]&&C&&C.indicateReverse(O)},[O,I,c,f,m,y,x,L,M,R,P,G,et,es,el,ec,ep,ey,eu,ed,ef,ev,ex,function(e){if(!es)return;let t="ArrowDown"===e.key?1:"ArrowUp"===e.key?-1:0;t&&(eu.focus(),i(17,ey=!0),e.preventDefault(),el&&-1===ec&&i(15,ec=es.findIndex(e=>e.id===(null==el?void 0:el.id))),ec===(el||U?0:-1)&&-1===t&&i(15,ec=es.length),i(15,ec+=t),ec>=es.length&&i(15,ec=-1),ec<0&&(el||U)&&i(15,ec=0))},e$,eT,ek,function(){(!U||el)&&i(15,ec=-1),F&&eE()},u,d,h,g,p,v,b,w,E,S,$,T,k,C,N,_,A,z,U,F,q,j,Z,V,K,Y,X,J,ee,ei,er,function(e){eu.focus(e)},function(){eu.blur()},en,function(){i(13,es=void 0),i(14,el=void 0),i(15,ec=-1)},function(){i(64,eo=[]),i(14,el=void 0)},eo,em,s,o,n,a,l,()=>eu.focus(),function(e){D[e?"unshift":"push"](()=>{i(18,eu=e)})},function(){i(1,I=this.value),i(17,ey),i(31,g),i(16,ep)},()=>i(17,ey=!0),()=>i(17,ey=!1),()=>i(17,ey=!0),()=>i(14,el=void 0),()=>{i(1,I=""),i(14,el=void 0),eu.focus()},()=>i(0,O=!O),()=>i(19,ed=void 0),e=>ek(e),e=>eT(e),()=>{}]}eZ.formats=e2.map(e=>e.verbatimCoordinates);let tr=class extends el{constructor(e){super(),eo(this,e,ti,tt,E,{ZOOM_DEFAULTS:28,class:2,apiKey:29,bbox:30,clearButtonTitle:3,clearOnBlur:31,clearListOnPick:32,keepListOpen:4,collapsed:5,country:33,debounceSearch:34,enableReverse:6,errorMessage:7,filter:35,flyTo:36,fuzzyMatch:37,language:38,limit:39,reverseGeocodingLimit:40,mapController:41,minLength:42,noResultsMessage:8,placeholder:9,proximity:43,reverseActive:0,reverseButtonTitle:10,searchValue:1,pickedResultStyle:44,showPlaceType:11,showResultsWhileTyping:45,selectFirst:46,flyToSelected:47,markerOnSelected:48,types:49,reverseGeocodingTypes:50,exhaustiveReverseGeocoding:51,excludeTypes:52,reverseGeocodingExcludeTypes:53,zoom:54,apiUrl:55,fetchParameters:56,iconsBaseUrl:12,adjustUrlQuery:57,adjustUrl:58,focus:59,blur:60,setQuery:61,clearList:62,clearMap:63},null,[-1,-1,-1,-1])}get ZOOM_DEFAULTS(){return this.$$.ctx[28]}get focus(){return this.$$.ctx[59]}get blur(){return this.$$.ctx[60]}get setQuery(){return this.$$.ctx[61]}get clearList(){return this.$$.ctx[62]}get clearMap(){return this.$$.ctx[63]}};function tn(e,t,i={}){let r={type:"Feature"};return(0===i.id||i.id)&&(r.id=i.id),i.bbox&&(r.bbox=i.bbox),r.properties=t||{},r.geometry=e,r}function ts(e,t,i={}){for(let t of e){if(t.length<4)throw Error("Each LinearRing of a Polygon must have 4 or more Positions.");if(t[t.length-1].length!==t[0].length)throw Error("First and last Position are not equivalent.");for(let e=0;e<t[t.length-1].length;e++)if(t[t.length-1][e]!==t[0][e])throw Error("First and last Position are not equivalent.")}return tn({type:"Polygon",coordinates:e},t,i)}function to(e,t={}){let i={type:"FeatureCollection"};return t.id&&(i.id=t.id),t.bbox&&(i.bbox=t.bbox),i.features=e,i}function tl(e,t,i={}){return tn({type:"MultiPolygon",coordinates:e},t,i)}var ta=/^-?(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?$/i,tu=Math.ceil,tc=Math.floor,td="[BigNumber Error] ",th=td+"Number primitive has more than 15 significant digits: ",tf=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8,1e9,1e10,1e11,1e12,1e13];function tg(e){var t=0|e;return e>0||e===t?t:t-1}function tp(e){for(var t,i,r=1,n=e.length,s=e[0]+"";r<n;){for(i=14-(t=e[r++]+"").length;i--;t="0"+t);s+=t}for(n=s.length;48===s.charCodeAt(--n););return s.slice(0,n+1||1)}function tm(e,t){var i,r,n=e.c,s=t.c,o=e.s,l=t.s,a=e.e,u=t.e;if(!o||!l)return null;if(i=n&&!n[0],r=s&&!s[0],i||r)return i?r?0:-l:o;if(o!=l)return o;if(i=o<0,r=a==u,!n||!s)return r?0:!n^i?1:-1;if(!r)return a>u^i?1:-1;for(l=(a=n.length)<(u=s.length)?a:u,o=0;o<l;o++)if(n[o]!=s[o])return n[o]>s[o]^i?1:-1;return a==u?0:a>u^i?1:-1}function ty(e,t,i,r){if(e<t||e>i||e!==tc(e))throw Error(td+(r||"Argument")+("number"==typeof e?e<t||e>i?" out of range: ":" not an integer: ":" not a primitive number: ")+String(e))}function tv(e){var t=e.c.length-1;return tg(e.e/14)==t&&e.c[t]%2!=0}function tb(e,t){return(e.length>1?e.charAt(0)+"."+e.slice(1):e)+(t<0?"e":"e+")+t}function tx(e,t,i){var r,n;if(t<0){for(n=i+".";++t;n+=i);e=n+e}else if(r=e.length,++t>r){for(n=i,t-=r;--t;n+=i);e+=n}else t<r&&(e=e.slice(0,t)+"."+e.slice(t));return e}var tL,tw,tE=function e(t){var i,r,n,s,o,l,a,u,c,d=$.prototype={constructor:$,toString:null,valueOf:null},h=new $(1),f=20,g=4,p=-7,m=21,y=-1e7,v=1e7,b=!1,x=1,L=0,w={prefix:"",groupSize:3,secondaryGroupSize:0,groupSeparator:",",decimalSeparator:".",fractionGroupSize:0,fractionGroupSeparator:"\xa0",suffix:""},E="0123456789abcdefghijklmnopqrstuvwxyz",S=!0;function $(e,t){var i,r,n,s,o,l,a,d,h=this;if(!(h instanceof $))return new $(e,t);if(null==t){if(e&&!0===e._isBigNumber){h.s=e.s,!e.c||e.e>v?h.c=h.e=null:e.e<y?h.c=[h.e=0]:(h.e=e.e,h.c=e.c.slice());return}if((l="number"==typeof e)&&0*e==0){if(h.s=1/e<0?(e=-e,-1):1,e===~~e){for(s=0,o=e;o>=10;o/=10,s++);s>v?h.c=h.e=null:(h.e=s,h.c=[e]);return}d=String(e)}else{if(!ta.test(d=String(e)))return c(h,d,l);h.s=45==d.charCodeAt(0)?(d=d.slice(1),-1):1}(s=d.indexOf("."))>-1&&(d=d.replace(".","")),(o=d.search(/e/i))>0?(s<0&&(s=o),s+=+d.slice(o+1),d=d.substring(0,o)):s<0&&(s=d.length)}else{if(ty(t,2,E.length,"Base"),10==t&&S)return N(h=new $(e),f+h.e+1,g);if(d=String(e),l="number"==typeof e){if(0*e!=0)return c(h,d,l,t);if(h.s=1/e<0?(d=d.slice(1),-1):1,$.DEBUG&&d.replace(/^0\.0*|\./,"").length>15)throw Error(th+e)}else h.s=45===d.charCodeAt(0)?(d=d.slice(1),-1):1;for(i=E.slice(0,t),s=o=0,a=d.length;o<a;o++)if(0>i.indexOf(r=d.charAt(o))){if("."==r){if(o>s){s=a;continue}}else if(!n&&(d==d.toUpperCase()&&(d=d.toLowerCase())||d==d.toLowerCase()&&(d=d.toUpperCase()))){n=!0,o=-1,s=0;continue}return c(h,String(e),l,t)}l=!1,(s=(d=u(d,t,10,h.s)).indexOf("."))>-1?d=d.replace(".",""):s=d.length}for(o=0;48===d.charCodeAt(o);o++);for(a=d.length;48===d.charCodeAt(--a););if(d=d.slice(o,++a)){if(a-=o,l&&$.DEBUG&&a>15&&(e>9007199254740991||e!==tc(e)))throw Error(th+h.s*e);if((s=s-o-1)>v)h.c=h.e=null;else if(s<y)h.c=[h.e=0];else{if(h.e=s,h.c=[],o=(s+1)%14,s<0&&(o+=14),o<a){for(o&&h.c.push(+d.slice(0,o)),a-=14;o<a;)h.c.push(+d.slice(o,o+=14));o=14-(d=d.slice(o)).length}else o-=a;for(;o--;d+="0");h.c.push(+d)}}else h.c=[h.e=0]}function T(e,t,i,r){var n,s,o,l,a;if(null==i?i=g:ty(i,0,8),!e.c)return e.toString();if(n=e.c[0],o=e.e,null==t)a=tp(e.c),a=1==r||2==r&&(o<=p||o>=m)?tb(a,o):tx(a,o,"0");else if(s=(e=N(new $(e),t,i)).e,l=(a=tp(e.c)).length,1==r||2==r&&(t<=s||s<=p)){for(;l<t;a+="0",l++);a=tb(a,s)}else if(t-=o,a=tx(a,s,"0"),s+1>l){if(--t>0)for(a+=".";t--;a+="0");}else if((t+=s-l)>0)for(s+1==l&&(a+=".");t--;a+="0");return e.s<0&&n?"-"+a:a}function k(e,t){for(var i,r,n=1,s=new $(e[0]);n<e.length;n++)(r=new $(e[n])).s&&(i=tm(s,r))!==t&&(0!==i||s.s!==t)||(s=r);return s}function C(e,t,i){for(var r=1,n=t.length;!t[--n];t.pop());for(n=t[0];n>=10;n/=10,r++);return(i=r+14*i-1)>v?e.c=e.e=null:i<y?e.c=[e.e=0]:(e.e=i,e.c=t),e}function N(e,t,i,r){var n,s,o,l,a,u,c,d=e.c;if(d){e:{for(n=1,l=d[0];l>=10;l/=10,n++);if((s=t-n)<0)s+=14,o=t,c=tc((a=d[u=0])/tf[n-o-1]%10);else if((u=tu((s+1)/14))>=d.length){if(r){for(;d.length<=u;d.push(0));a=c=0,n=1,s%=14,o=s-14+1}else break e}else{for(a=l=d[u],n=1;l>=10;l/=10,n++);s%=14,c=(o=s-14+n)<0?0:tc(a/tf[n-o-1]%10)}if(r=r||t<0||null!=d[u+1]||(o<0?a:a%tf[n-o-1]),r=i<4?(c||r)&&(0==i||i==(e.s<0?3:2)):c>5||5==c&&(4==i||r||6==i&&(s>0?o>0?a/tf[n-o]:0:d[u-1])%10&1||i==(e.s<0?8:7)),t<1||!d[0])return d.length=0,r?(t-=e.e+1,d[0]=tf[(14-t%14)%14],e.e=-t||0):d[0]=e.e=0,e;if(0==s?(d.length=u,l=1,u--):(d.length=u+1,l=tf[14-s],d[u]=o>0?tc(a/tf[n-o]%tf[o])*l:0),r)for(;;)if(0==u){for(s=1,o=d[0];o>=10;o/=10,s++);for(o=d[0]+=l,l=1;o>=10;o/=10,l++);s!=l&&(e.e++,1e14==d[0]&&(d[0]=1));break}else{if(d[u]+=l,1e14!=d[u])break;d[u--]=0,l=1}for(s=d.length;0===d[--s];d.pop());}e.e>v?e.c=e.e=null:e.e<y&&(e.c=[e.e=0])}return e}function M(e){var t,i=e.e;return null===i?e.toString():(t=tp(e.c),t=i<=p||i>=m?tb(t,i):tx(t,i,"0"),e.s<0?"-"+t:t)}return $.clone=e,$.ROUND_UP=0,$.ROUND_DOWN=1,$.ROUND_CEIL=2,$.ROUND_FLOOR=3,$.ROUND_HALF_UP=4,$.ROUND_HALF_DOWN=5,$.ROUND_HALF_EVEN=6,$.ROUND_HALF_CEIL=7,$.ROUND_HALF_FLOOR=8,$.EUCLID=9,$.config=$.set=function(e){var t,i;if(null!=e){if("object"==typeof e){if(e.hasOwnProperty(t="DECIMAL_PLACES")&&(ty(i=e[t],0,1e9,t),f=i),e.hasOwnProperty(t="ROUNDING_MODE")&&(ty(i=e[t],0,8,t),g=i),e.hasOwnProperty(t="EXPONENTIAL_AT")&&((i=e[t])&&i.pop?(ty(i[0],-1e9,0,t),ty(i[1],0,1e9,t),p=i[0],m=i[1]):(ty(i,-1e9,1e9,t),p=-(m=i<0?-i:i))),e.hasOwnProperty(t="RANGE")){if((i=e[t])&&i.pop)ty(i[0],-1e9,-1,t),ty(i[1],1,1e9,t),y=i[0],v=i[1];else if(ty(i,-1e9,1e9,t),i)y=-(v=i<0?-i:i);else throw Error(td+t+" cannot be zero: "+i)}if(e.hasOwnProperty(t="CRYPTO")){if(!!(i=e[t])===i){if(i){if("u">typeof crypto&&crypto&&(crypto.getRandomValues||crypto.randomBytes))b=i;else throw b=!i,Error(td+"crypto unavailable")}else b=i}else throw Error(td+t+" not true or false: "+i)}if(e.hasOwnProperty(t="MODULO_MODE")&&(ty(i=e[t],0,9,t),x=i),e.hasOwnProperty(t="POW_PRECISION")&&(ty(i=e[t],0,1e9,t),L=i),e.hasOwnProperty(t="FORMAT")){if("object"==typeof(i=e[t]))w=i;else throw Error(td+t+" not an object: "+i)}if(e.hasOwnProperty(t="ALPHABET")){if("string"!=typeof(i=e[t])||/^.?$|[+\-.\s]|(.).*\1/.test(i))throw Error(td+t+" invalid: "+i);S="0123456789"==i.slice(0,10),E=i}}else throw Error(td+"Object expected: "+e)}return{DECIMAL_PLACES:f,ROUNDING_MODE:g,EXPONENTIAL_AT:[p,m],RANGE:[y,v],CRYPTO:b,MODULO_MODE:x,POW_PRECISION:L,FORMAT:w,ALPHABET:E}},$.isBigNumber=function(e){if(!e||!0!==e._isBigNumber)return!1;if(!$.DEBUG)return!0;var t,i,r=e.c,n=e.e,s=e.s;e:if("[object Array]"==({}).toString.call(r)){if((1===s||-1===s)&&n>=-1e9&&n<=1e9&&n===tc(n)){if(0===r[0]){if(0===n&&1===r.length)return!0;break e}if((t=(n+1)%14)<1&&(t+=14),String(r[0]).length==t){for(t=0;t<r.length;t++)if((i=r[t])<0||i>=1e14||i!==tc(i))break e;if(0!==i)return!0}}}else if(null===r&&null===n&&(null===s||1===s||-1===s))return!0;throw Error(td+"Invalid BigNumber: "+e)},$.maximum=$.max=function(){return k(arguments,-1)},$.minimum=$.min=function(){return k(arguments,1)},$.random=(i=9007199254740992*Math.random()&2097151?function(){return tc(9007199254740992*Math.random())}:function(){return(1073741824*Math.random()|0)*8388608+(8388608*Math.random()|0)},function(e){var t,r,n,s,o,l=0,a=[],u=new $(h);if(null==e?e=f:ty(e,0,1e9),s=tu(e/14),b){if(crypto.getRandomValues){for(t=crypto.getRandomValues(new Uint32Array(s*=2));l<s;)(o=131072*t[l]+(t[l+1]>>>11))>=9e15?(r=crypto.getRandomValues(new Uint32Array(2)),t[l]=r[0],t[l+1]=r[1]):(a.push(o%1e14),l+=2);l=s/2}else if(crypto.randomBytes){for(t=crypto.randomBytes(s*=7);l<s;)(o=(31&t[l])*281474976710656+1099511627776*t[l+1]+4294967296*t[l+2]+16777216*t[l+3]+(t[l+4]<<16)+(t[l+5]<<8)+t[l+6])>=9e15?crypto.randomBytes(7).copy(t,l):(a.push(o%1e14),l+=7);l=s/7}else throw b=!1,Error(td+"crypto unavailable")}if(!b)for(;l<s;)(o=i())<9e15&&(a[l++]=o%1e14);for(s=a[--l],e%=14,s&&e&&(o=tf[14-e],a[l]=tc(s/o)*o);0===a[l];a.pop(),l--);if(l<0)a=[n=0];else{for(n=-1;0===a[0];a.splice(0,1),n-=14);for(l=1,o=a[0];o>=10;o/=10,l++);l<14&&(n-=14-l)}return u.e=n,u.c=a,u}),$.sum=function(){for(var e=1,t=arguments,i=new $(t[0]);e<t.length;)i=i.plus(t[e++]);return i},u=function(){var e="0123456789";function t(e,t,i,r){for(var n,s,o=[0],l=0,a=e.length;l<a;){for(s=o.length;s--;o[s]*=t);for(o[0]+=r.indexOf(e.charAt(l++)),n=0;n<o.length;n++)o[n]>i-1&&(null==o[n+1]&&(o[n+1]=0),o[n+1]+=o[n]/i|0,o[n]%=i)}return o.reverse()}return function(i,r,n,s,o){var l,u,c,d,h,p,m,y,v=i.indexOf("."),b=f,x=g;for(v>=0&&(d=L,L=0,i=i.replace(".",""),p=(y=new $(r)).pow(i.length-v),L=d,y.c=t(tx(tp(p.c),p.e,"0"),10,n,e),y.e=y.c.length),c=d=(m=t(i,r,n,o?(l=E,e):(l=e,E))).length;0==m[--d];m.pop());if(!m[0])return l.charAt(0);if(v<0?--c:(p.c=m,p.e=c,p.s=s,m=(p=a(p,y,b,x,n)).c,h=p.r,c=p.e),v=m[u=c+b+1],d=n/2,h=h||u<0||null!=m[u+1],h=x<4?(null!=v||h)&&(0==x||x==(p.s<0?3:2)):v>d||v==d&&(4==x||h||6==x&&1&m[u-1]||x==(p.s<0?8:7)),u<1||!m[0])i=h?tx(l.charAt(1),-b,l.charAt(0)):l.charAt(0);else{if(m.length=u,h)for(--n;++m[--u]>n;)m[u]=0,u||(++c,m=[1].concat(m));for(d=m.length;!m[--d];);for(v=0,i="";v<=d;i+=l.charAt(m[v++]));i=tx(i,c,l.charAt(0))}return i}}(),a=function(){function e(e,t,i){var r,n,s,o,l=0,a=e.length,u=t%1e7,c=t/1e7|0;for(e=e.slice();a--;)r=c*(s=e[a]%1e7)+(o=e[a]/1e7|0)*u,l=((n=u*s+r%1e7*1e7+l)/i|0)+(r/1e7|0)+c*o,e[a]=n%i;return l&&(e=[l].concat(e)),e}function t(e,t,i,r){var n,s;if(i!=r)s=i>r?1:-1;else for(n=s=0;n<i;n++)if(e[n]!=t[n]){s=e[n]>t[n]?1:-1;break}return s}function i(e,t,i,r){for(var n=0;i--;)e[i]-=n,n=e[i]<t[i]?1:0,e[i]=n*r+e[i]-t[i];for(;!e[0]&&e.length>1;e.splice(0,1));}return function(r,n,s,o,l){var a,u,c,d,h,f,g,p,m,y,v,b,x,L,w,E,S,T=r.s==n.s?1:-1,k=r.c,C=n.c;if(!k||!k[0]||!C||!C[0])return new $(r.s&&n.s&&(k?!C||k[0]!=C[0]:C)?k&&0==k[0]||!C?0*T:T/0:NaN);for(m=(p=new $(T)).c=[],T=s+(u=r.e-n.e)+1,l||(l=1e14,u=tg(r.e/14)-tg(n.e/14),T=T/14|0),c=0;C[c]==(k[c]||0);c++);if(C[c]>(k[c]||0)&&u--,T<0)m.push(1),d=!0;else{for(L=k.length,E=C.length,c=0,T+=2,(h=tc(l/(C[0]+1)))>1&&(C=e(C,h,l),k=e(k,h,l),E=C.length,L=k.length),x=E,v=(y=k.slice(0,E)).length;v<E;y[v++]=0);S=[0].concat(S=C.slice()),w=C[0],C[1]>=l/2&&w++;do{if(h=0,(a=t(C,y,E,v))<0){if(b=y[0],E!=v&&(b=b*l+(y[1]||0)),(h=tc(b/w))>1)for(h>=l&&(h=l-1),g=(f=e(C,h,l)).length,v=y.length;1==t(f,y,g,v);)h--,i(f,E<g?S:C,g,l),g=f.length,a=1;else 0==h&&(a=h=1),g=(f=C.slice()).length;if(g<v&&(f=[0].concat(f)),i(y,f,v,l),v=y.length,-1==a)for(;1>t(C,y,E,v);)h++,i(y,E<v?S:C,v,l),v=y.length}else 0===a&&(h++,y=[0]);m[c++]=h,y[0]?y[v++]=k[x]||0:(y=[k[x]],v=1)}while((x++<L||null!=y[0])&&T--);d=null!=y[0],m[0]||m.splice(0,1)}if(1e14==l){for(c=1,T=m[0];T>=10;T/=10,c++);N(p,s+(p.e=c+14*u-1)+1,o,d)}else p.e=u,p.r=+d;return p}}(),r=/^(-?)0([xbo])(?=\w[\w.]*$)/i,n=/^([^.]+)\.$/,s=/^\.([^.]+)$/,o=/^-?(Infinity|NaN)$/,l=/^\s*\+(?=[\w.])|^\s+|\s+$/g,c=function(e,t,i,a){var u,c=i?t:t.replace(l,"");if(o.test(c))e.s=isNaN(c)?null:c<0?-1:1;else{if(!i&&(c=c.replace(r,function(e,t,i){return u="x"==(i=i.toLowerCase())?16:"b"==i?2:8,a&&a!=u?e:t}),a&&(u=a,c=c.replace(n,"$1").replace(s,"0.$1")),t!=c))return new $(c,u);if($.DEBUG)throw Error(td+"Not a"+(a?" base "+a:"")+" number: "+t);e.s=null}e.c=e.e=null},d.absoluteValue=d.abs=function(){var e=new $(this);return e.s<0&&(e.s=1),e},d.comparedTo=function(e,t){return tm(this,new $(e,t))},d.decimalPlaces=d.dp=function(e,t){var i,r,n;if(null!=e)return ty(e,0,1e9),null==t?t=g:ty(t,0,8),N(new $(this),e+this.e+1,t);if(!(i=this.c))return null;if(r=((n=i.length-1)-tg(this.e/14))*14,n=i[n])for(;n%10==0;n/=10,r--);return r<0&&(r=0),r},d.dividedBy=d.div=function(e,t){return a(this,new $(e,t),f,g)},d.dividedToIntegerBy=d.idiv=function(e,t){return a(this,new $(e,t),0,1)},d.exponentiatedBy=d.pow=function(e,t){var i,r,n,s,o,l,a,u,c,d=this;if((e=new $(e)).c&&!e.isInteger())throw Error(td+"Exponent not an integer: "+M(e));if(null!=t&&(t=new $(t)),l=e.e>14,!d.c||!d.c[0]||1==d.c[0]&&!d.e&&1==d.c.length||!e.c||!e.c[0])return c=new $(Math.pow(+M(d),l?e.s*(2-tv(e)):+M(e))),t?c.mod(t):c;if(a=e.s<0,t){if(t.c?!t.c[0]:!t.s)return new $(NaN);(r=!a&&d.isInteger()&&t.isInteger())&&(d=d.mod(t))}else{if(e.e>9&&(d.e>0||d.e<-1||(0==d.e?d.c[0]>1||l&&d.c[1]>=24e7:d.c[0]<8e13||l&&d.c[0]<=9999975e7)))return s=d.s<0&&tv(e)?-0:0,d.e>-1&&(s=1/s),new $(a?1/s:s);L&&(s=tu(L/14+2))}for(l?(i=new $(.5),a&&(e.s=1),u=tv(e)):u=(n=Math.abs(+M(e)))%2,c=new $(h);;){if(u){if(!(c=c.times(d)).c)break;s?c.c.length>s&&(c.c.length=s):r&&(c=c.mod(t))}if(n){if(0===(n=tc(n/2)))break;u=n%2}else if(N(e=e.times(i),e.e+1,1),e.e>14)u=tv(e);else{if(0==(n=+M(e)))break;u=n%2}d=d.times(d),s?d.c&&d.c.length>s&&(d.c.length=s):r&&(d=d.mod(t))}return r?c:(a&&(c=h.div(c)),t?c.mod(t):s?N(c,L,g,o):c)},d.integerValue=function(e){var t=new $(this);return null==e?e=g:ty(e,0,8),N(t,t.e+1,e)},d.isEqualTo=d.eq=function(e,t){return 0===tm(this,new $(e,t))},d.isFinite=function(){return!!this.c},d.isGreaterThan=d.gt=function(e,t){return tm(this,new $(e,t))>0},d.isGreaterThanOrEqualTo=d.gte=function(e,t){return 1===(t=tm(this,new $(e,t)))||0===t},d.isInteger=function(){return!!this.c&&tg(this.e/14)>this.c.length-2},d.isLessThan=d.lt=function(e,t){return 0>tm(this,new $(e,t))},d.isLessThanOrEqualTo=d.lte=function(e,t){return -1===(t=tm(this,new $(e,t)))||0===t},d.isNaN=function(){return!this.s},d.isNegative=function(){return this.s<0},d.isPositive=function(){return this.s>0},d.isZero=function(){return!!this.c&&0==this.c[0]},d.minus=function(e,t){var i,r,n,s,o=this.s;if(t=(e=new $(e,t)).s,!o||!t)return new $(NaN);if(o!=t)return e.s=-t,this.plus(e);var l=this.e/14,a=e.e/14,u=this.c,c=e.c;if(!l||!a){if(!u||!c)return u?(e.s=-t,e):new $(c?this:NaN);if(!u[0]||!c[0])return c[0]?(e.s=-t,e):new $(u[0]?this:3==g?-0:0)}if(l=tg(l),a=tg(a),u=u.slice(),o=l-a){for((s=o<0)?(o=-o,n=u):(a=l,n=c),n.reverse(),t=o;t--;n.push(0));n.reverse()}else for(r=(s=(o=u.length)<(t=c.length))?o:t,o=t=0;t<r;t++)if(u[t]!=c[t]){s=u[t]<c[t];break}if(s&&(n=u,u=c,c=n,e.s=-e.s),(t=(r=c.length)-(i=u.length))>0)for(;t--;u[i++]=0);for(t=1e14-1;r>o;){if(u[--r]<c[r]){for(i=r;i&&!u[--i];u[i]=t);--u[i],u[r]+=1e14}u[r]-=c[r]}for(;0==u[0];u.splice(0,1),--a);return u[0]?C(e,u,a):(e.s=3==g?-1:1,e.c=[e.e=0],e)},d.modulo=d.mod=function(e,t){var i,r;return e=new $(e,t),this.c&&e.s&&(!e.c||e.c[0])?e.c&&(!this.c||this.c[0])?(9==x?(r=e.s,e.s=1,i=a(this,e,0,3),e.s=r,i.s*=r):i=a(this,e,0,x),(e=this.minus(i.times(e))).c[0]||1!=x||(e.s=this.s),e):new $(this):new $(NaN)},d.multipliedBy=d.times=function(e,t){var i,r,n,s,o,l,a,u,c,d,h,f,g,p=this.c,m=(e=new $(e,t)).c;if(!p||!m||!p[0]||!m[0])return this.s&&e.s&&(!p||p[0]||m)&&(!m||m[0]||p)?(e.s*=this.s,p&&m?(e.c=[0],e.e=0):e.c=e.e=null):e.c=e.e=e.s=null,e;for(r=tg(this.e/14)+tg(e.e/14),e.s*=this.s,(a=p.length)<(d=m.length)&&(g=p,p=m,m=g,n=a,a=d,d=n),n=a+d,g=[];n--;g.push(0));for(n=d;--n>=0;){for(i=0,h=m[n]%1e7,f=m[n]/1e7|0,s=n+(o=a);s>n;)l=f*(u=p[--o]%1e7)+(c=p[o]/1e7|0)*h,i=((u=h*u+l%1e7*1e7+g[s]+i)/1e14|0)+(l/1e7|0)+f*c,g[s--]=u%1e14;g[s]=i}return i?++r:g.splice(0,1),C(e,g,r)},d.negated=function(){var e=new $(this);return e.s=-e.s||null,e},d.plus=function(e,t){var i,r=this.s;if(t=(e=new $(e,t)).s,!r||!t)return new $(NaN);if(r!=t)return e.s=-t,this.minus(e);var n=this.e/14,s=e.e/14,o=this.c,l=e.c;if(!n||!s){if(!o||!l)return new $(r/0);if(!o[0]||!l[0])return l[0]?e:new $(o[0]?this:0*r)}if(n=tg(n),s=tg(s),o=o.slice(),r=n-s){for(r>0?(s=n,i=l):(r=-r,i=o),i.reverse();r--;i.push(0));i.reverse()}for((r=o.length)-(t=l.length)<0&&(i=l,l=o,o=i,t=r),r=0;t;)r=(o[--t]=o[t]+l[t]+r)/1e14|0,o[t]=1e14===o[t]?0:o[t]%1e14;return r&&(o=[r].concat(o),++s),C(e,o,s)},d.precision=d.sd=function(e,t){var i,r,n;if(null!=e&&!!e!==e)return ty(e,1,1e9),null==t?t=g:ty(t,0,8),N(new $(this),e,t);if(!(i=this.c))return null;if(r=14*(n=i.length-1)+1,n=i[n]){for(;n%10==0;n/=10,r--);for(n=i[0];n>=10;n/=10,r++);}return e&&this.e+1>r&&(r=this.e+1),r},d.shiftedBy=function(e){return ty(e,-9007199254740991,9007199254740991),this.times("1e"+e)},d.squareRoot=d.sqrt=function(){var e,t,i,r,n,s=this.c,o=this.s,l=this.e,u=f+4,c=new $("0.5");if(1!==o||!s||!s[0])return new $(!o||o<0&&(!s||s[0])?NaN:s?this:1/0);if(0==(o=Math.sqrt(+M(this)))||o==1/0?(((t=tp(s)).length+l)%2==0&&(t+="0"),o=Math.sqrt(+t),l=tg((l+1)/2)-(l<0||l%2),i=new $(t=o==1/0?"5e"+l:(t=o.toExponential()).slice(0,t.indexOf("e")+1)+l)):i=new $(o+""),i.c[0]){for((o=(l=i.e)+u)<3&&(o=0);;)if(n=i,i=c.times(n.plus(a(this,n,u,1))),tp(n.c).slice(0,o)===(t=tp(i.c)).slice(0,o)){if(i.e<l&&--o,"9999"!=(t=t.slice(o-3,o+1))&&(r||"4999"!=t)){+t&&(+t.slice(1)||"5"!=t.charAt(0))||(N(i,i.e+f+2,1),e=!i.times(i).eq(this));break}if(!r&&(N(n,n.e+f+2,0),n.times(n).eq(this))){i=n;break}u+=4,o+=4,r=1}}return N(i,i.e+f+1,g,e)},d.toExponential=function(e,t){return null!=e&&(ty(e,0,1e9),e++),T(this,e,t,1)},d.toFixed=function(e,t){return null!=e&&(ty(e,0,1e9),e=e+this.e+1),T(this,e,t)},d.toFormat=function(e,t,i){var r;if(null==i)null!=e&&t&&"object"==typeof t?(i=t,t=null):e&&"object"==typeof e?(i=e,e=t=null):i=w;else if("object"!=typeof i)throw Error(td+"Argument not an object: "+i);if(r=this.toFixed(e,t),this.c){var n,s=r.split("."),o=+i.groupSize,l=+i.secondaryGroupSize,a=i.groupSeparator||"",u=s[0],c=s[1],d=this.s<0,h=d?u.slice(1):u,f=h.length;if(l&&(n=o,o=l,l=n,f-=n),o>0&&f>0){for(n=f%o||o,u=h.substr(0,n);n<f;n+=o)u+=a+h.substr(n,o);l>0&&(u+=a+h.slice(n)),d&&(u="-"+u)}r=c?u+(i.decimalSeparator||"")+((l=+i.fractionGroupSize)?c.replace(RegExp("\\d{"+l+"}\\B","g"),"$&"+(i.fractionGroupSeparator||"")):c):u}return(i.prefix||"")+r+(i.suffix||"")},d.toFraction=function(e){var t,i,r,n,s,o,l,u,c,d,f,p,m=this.c;if(null!=e&&(!(l=new $(e)).isInteger()&&(l.c||1!==l.s)||l.lt(h)))throw Error(td+"Argument "+(l.isInteger()?"out of range: ":"not an integer: ")+M(l));if(!m)return new $(this);for(t=new $(h),c=i=new $(h),r=u=new $(h),p=tp(m),s=t.e=p.length-this.e-1,t.c[0]=tf[(o=s%14)<0?14+o:o],e=!e||l.comparedTo(t)>0?s>0?t:c:l,o=v,v=1/0,l=new $(p),u.c[0]=0;d=a(l,t,0,1),1!=(n=i.plus(d.times(r))).comparedTo(e);)i=r,r=n,c=u.plus(d.times(n=c)),u=n,t=l.minus(d.times(n=t)),l=n;return n=a(e.minus(i),r,0,1),u=u.plus(n.times(c)),i=i.plus(n.times(r)),u.s=c.s=this.s,s*=2,f=1>a(c,r,s,g).minus(this).abs().comparedTo(a(u,i,s,g).minus(this).abs())?[c,r]:[u,i],v=o,f},d.toNumber=function(){return+M(this)},d.toPrecision=function(e,t){return null!=e&&ty(e,1,1e9),T(this,e,t,2)},d.toString=function(e){var t,i=this,r=i.s,n=i.e;return null===n?r?(t="Infinity",r<0&&(t="-"+t)):t="NaN":(null==e?t=n<=p||n>=m?tb(tp(i.c),n):tx(tp(i.c),n,"0"):10===e&&S?t=tx(tp((i=N(new $(i),f+n+1,g)).c),i.e,"0"):(ty(e,2,E.length,"Base"),t=u(tx(tp(i.c),n,"0"),10,e,r,!0)),r<0&&i.c[0]&&(t="-"+t)),t},d.valueOf=d.toJSON=function(){return M(this)},d._isBigNumber=!0,d[Symbol.toStringTag]="BigNumber",d[Symbol.for("nodejs.util.inspect.custom")]=d.valueOf,null!=t&&$.set(t),$}(),tS=class{constructor(e){f(this,"key"),f(this,"left",null),f(this,"right",null),this.key=e}},t$=class extends tS{constructor(e){super(e)}},tT=class{constructor(){f(this,"size",0),f(this,"modificationCount",0),f(this,"splayCount",0)}splay(e){let t;let i=this.root;if(null==i)return this.compare(e,e),-1;let r=null,n=null,s=null,o=null,l=i,a=this.compare;for(;;)if((t=a(l.key,e))>0){let i=l.left;if(null==i||(t=a(i.key,e))>0&&(l.left=i.right,i.right=l,null==(i=(l=i).left)))break;null==r?n=l:r.left=l,r=l,l=i}else if(t<0){let i=l.right;if(null==i||(t=a(i.key,e))<0&&(l.right=i.left,i.left=l,null==(i=(l=i).right)))break;null==s?o=l:s.right=l,s=l,l=i}else break;return null!=s&&(s.right=l.left,l.left=o),null!=r&&(r.left=l.right,l.right=n),this.root!==l&&(this.root=l,this.splayCount++),t}splayMin(e){let t=e,i=t.left;for(;null!=i;){let e=i;t.left=e.right,e.right=t,i=(t=e).left}return t}splayMax(e){let t=e,i=t.right;for(;null!=i;){let e=i;t.right=e.left,e.left=t,i=(t=e).right}return t}_delete(e){if(null==this.root||0!=this.splay(e))return null;let t=this.root,i=t,r=t.left;if(this.size--,null==r)this.root=t.right;else{let e=t.right;(t=this.splayMax(r)).right=e,this.root=t}return this.modificationCount++,i}addNewRoot(e,t){this.size++,this.modificationCount++;let i=this.root;if(null==i){this.root=e;return}t<0?(e.left=i,e.right=i.right,i.right=null):(e.right=i,e.left=i.left,i.left=null),this.root=e}_first(){let e=this.root;return null==e?null:(this.root=this.splayMin(e),this.root)}_last(){let e=this.root;return null==e?null:(this.root=this.splayMax(e),this.root)}clear(){this.root=null,this.size=0,this.modificationCount++}has(e){return this.validKey(e)&&0==this.splay(e)}defaultCompare(){return(e,t)=>e<t?-1:e>t?1:0}wrap(){return{getRoot:()=>this.root,setRoot:e=>{this.root=e},getSize:()=>this.size,getModificationCount:()=>this.modificationCount,getSplayCount:()=>this.splayCount,setSplayCount:e=>{this.splayCount=e},splay:e=>this.splay(e),has:e=>this.has(e)}}},tk=class e extends tT{constructor(e,t){super(),f(this,"root",null),f(this,"compare"),f(this,"validKey"),f(this,tL,"[object Set]"),this.compare=e??this.defaultCompare(),this.validKey=t??(e=>null!=e&&null!=e)}delete(e){return!!this.validKey(e)&&null!=this._delete(e)}deleteAll(e){for(let t of e)this.delete(t)}forEach(e){let t;let i=this[Symbol.iterator]();for(;!(t=i.next()).done;)e(t.value,t.value,this)}add(e){let t=this.splay(e);return 0!=t&&this.addNewRoot(new t$(e),t),this}addAndReturn(e){let t=this.splay(e);return 0!=t&&this.addNewRoot(new t$(e),t),this.root.key}addAll(e){for(let t of e)this.add(t)}isEmpty(){return null==this.root}isNotEmpty(){return null!=this.root}single(){if(0==this.size)throw"Bad state: No element";if(this.size>1)throw"Bad state: Too many element";return this.root.key}first(){if(0==this.size)throw"Bad state: No element";return this._first().key}last(){if(0==this.size)throw"Bad state: No element";return this._last().key}lastBefore(e){if(null==e)throw"Invalid arguments(s)";if(null==this.root)return null;if(0>this.splay(e))return this.root.key;let t=this.root.left;if(null==t)return null;let i=t.right;for(;null!=i;)i=(t=i).right;return t.key}firstAfter(e){if(null==e)throw"Invalid arguments(s)";if(null==this.root)return null;if(this.splay(e)>0)return this.root.key;let t=this.root.right;if(null==t)return null;let i=t.left;for(;null!=i;)i=(t=i).left;return t.key}retainAll(t){let i=new e(this.compare,this.validKey),r=this.modificationCount;for(let e of t){if(r!=this.modificationCount)throw"Concurrent modification during iteration.";this.validKey(e)&&0==this.splay(e)&&i.add(this.root.key)}i.size!=this.size&&(this.root=i.root,this.size=i.size,this.modificationCount++)}lookup(e){return this.validKey(e)&&0==this.splay(e)?this.root.key:null}intersection(t){let i=new e(this.compare,this.validKey);for(let e of this)t.has(e)&&i.add(e);return i}difference(t){let i=new e(this.compare,this.validKey);for(let e of this)t.has(e)||i.add(e);return i}union(e){let t=this.clone();return t.addAll(e),t}clone(){let t=new e(this.compare,this.validKey);return t.size=this.size,t.root=this.copyNode(this.root),t}copyNode(e){if(null==e)return null;let t=new t$(e.key);return function e(t,i){let r,n;do{if(r=t.left,n=t.right,null!=r){let t=new t$(r.key);i.left=t,e(r,t)}if(null!=n){let e=new t$(n.key);i.right=e,t=n,i=e}}while(null!=n)}(e,t),t}toSet(){return this.clone()}entries(){return new tM(this.wrap())}keys(){return this[Symbol.iterator]()}values(){return this[Symbol.iterator]()}[(tw=Symbol.iterator,tL=Symbol.toStringTag,tw)](){return new tN(this.wrap())}},tC=class{constructor(e){f(this,"tree"),f(this,"path",[]),f(this,"modificationCount",null),f(this,"splayCount"),this.tree=e,this.splayCount=e.getSplayCount()}[Symbol.iterator](){return this}next(){return this.moveNext()?{done:!1,value:this.current()}:{done:!0,value:null}}current(){if(!this.path.length)return null;let e=this.path[this.path.length-1];return this.getValue(e)}rebuildPath(e){this.path.splice(0,this.path.length),this.tree.splay(e),this.path.push(this.tree.getRoot()),this.splayCount=this.tree.getSplayCount()}findLeftMostDescendent(e){for(;null!=e;)this.path.push(e),e=e.left}moveNext(){if(this.modificationCount!=this.tree.getModificationCount()){if(null==this.modificationCount){this.modificationCount=this.tree.getModificationCount();let e=this.tree.getRoot();for(;null!=e;)this.path.push(e),e=e.left;return this.path.length>0}throw"Concurrent modification during iteration."}if(!this.path.length)return!1;this.splayCount!=this.tree.getSplayCount()&&this.rebuildPath(this.path[this.path.length-1].key);let e=this.path[this.path.length-1],t=e.right;if(null!=t){for(;null!=t;)this.path.push(t),t=t.left;return!0}for(this.path.pop();this.path.length&&this.path[this.path.length-1].right===e;)e=this.path.pop();return this.path.length>0}},tN=class extends tC{getValue(e){return e.key}},tM=class extends tC{getValue(e){return[e.key,e.key]}},tR=e=>()=>e,t_=e=>{let t=e?(t,i)=>i.minus(t).abs().isLessThanOrEqualTo(e):tR(!1);return(e,i)=>t(e,i)?0:e.comparedTo(i)},tO=e=>e,tP=e=>{if(e){let t=new tk(t_(e)),i=new tk(t_(e)),r=(e,t)=>t.addAndReturn(e),n=e=>({x:r(e.x,t),y:r(e.y,i)});return n({x:new tE(0),y:new tE(0)}),n}return tO},tI=e=>({set:e=>{tA=tI(e)},reset:()=>tI(e),compare:t_(e),snap:tP(e),orient:function(e){let t=e?(t,i,r,n,s)=>t.exponentiatedBy(2).isLessThanOrEqualTo(n.minus(i).exponentiatedBy(2).plus(s.minus(r).exponentiatedBy(2)).times(e)):tR(!1);return(e,i,r)=>{let n=e.x,s=e.y,o=r.x,l=r.y,a=s.minus(l).times(i.x.minus(o)).minus(n.minus(o).times(i.y.minus(l)));return t(a,n,s,o,l)?0:a.comparedTo(0)}}(e)}),tA=tI(),tW=(e,t)=>e.ll.x.isLessThanOrEqualTo(t.x)&&t.x.isLessThanOrEqualTo(e.ur.x)&&e.ll.y.isLessThanOrEqualTo(t.y)&&t.y.isLessThanOrEqualTo(e.ur.y),tB=(e,t)=>{if(t.ur.x.isLessThan(e.ll.x)||e.ur.x.isLessThan(t.ll.x)||t.ur.y.isLessThan(e.ll.y)||e.ur.y.isLessThan(t.ll.y))return null;let i=e.ll.x.isLessThan(t.ll.x)?t.ll.x:e.ll.x,r=e.ur.x.isLessThan(t.ur.x)?e.ur.x:t.ur.x;return{ll:{x:i,y:e.ll.y.isLessThan(t.ll.y)?t.ll.y:e.ll.y},ur:{x:r,y:e.ur.y.isLessThan(t.ur.y)?e.ur.y:t.ur.y}}},tG=(e,t)=>e.x.times(t.y).minus(e.y.times(t.x)),tz=(e,t)=>e.x.times(t.x).plus(e.y.times(t.y)),tD=e=>tz(e,e).sqrt(),tU=(e,t,i)=>{let r={x:t.x.minus(e.x),y:t.y.minus(e.y)},n={x:i.x.minus(e.x),y:i.y.minus(e.y)};return tG(n,r).div(tD(n)).div(tD(r))},tF=(e,t,i)=>{let r={x:t.x.minus(e.x),y:t.y.minus(e.y)},n={x:i.x.minus(e.x),y:i.y.minus(e.y)};return tz(n,r).div(tD(n)).div(tD(r))},tq=(e,t,i)=>t.y.isZero()?null:{x:e.x.plus(t.x.div(t.y).times(i.minus(e.y))),y:i},tj=(e,t,i)=>t.x.isZero()?null:{x:i,y:e.y.plus(t.y.div(t.x).times(i.minus(e.x)))},tH=(e,t,i,r)=>{if(t.x.isZero())return tj(i,r,e.x);if(r.x.isZero())return tj(e,t,i.x);if(t.y.isZero())return tq(i,r,e.y);if(r.y.isZero())return tq(e,t,i.y);let n=tG(t,r);if(n.isZero())return null;let s={x:i.x.minus(e.x),y:i.y.minus(e.y)},o=tG(s,t).div(n),l=tG(s,r).div(n),a=e.x.plus(l.times(t.x)),u=i.x.plus(o.times(r.x)),c=e.y.plus(l.times(t.y)),d=i.y.plus(o.times(r.y));return{x:a.plus(u).div(2),y:c.plus(d).div(2)}},tZ=class e{constructor(e,t){f(this,"point"),f(this,"isLeft"),f(this,"segment"),f(this,"otherSE"),f(this,"consumedBy"),void 0===e.events?e.events=[this]:e.events.push(this),this.point=e,this.isLeft=t}static compare(t,i){let r=e.comparePoints(t.point,i.point);return 0!==r?r:(t.point!==i.point&&t.link(i),t.isLeft!==i.isLeft?t.isLeft?1:-1:t0.compare(t.segment,i.segment))}static comparePoints(e,t){return e.x.isLessThan(t.x)?-1:e.x.isGreaterThan(t.x)?1:e.y.isLessThan(t.y)?-1:e.y.isGreaterThan(t.y)?1:0}link(e){if(e.point===this.point)throw Error("Tried to link already linked events");let t=e.point.events;for(let e=0,i=t.length;e<i;e++){let i=t[e];this.point.events.push(i),i.point=this.point}this.checkForConsuming()}checkForConsuming(){let e=this.point.events.length;for(let t=0;t<e;t++){let i=this.point.events[t];if(void 0===i.segment.consumedBy)for(let r=t+1;r<e;r++){let e=this.point.events[r];void 0===e.consumedBy&&i.otherSE.point.events===e.otherSE.point.events&&i.segment.consume(e.segment)}}}getAvailableLinkedEvents(){let e=[];for(let t=0,i=this.point.events.length;t<i;t++){let i=this.point.events[t];i!==this&&!i.segment.ringOut&&i.segment.isInResult()&&e.push(i)}return e}getLeftmostComparator(e){let t=new Map,i=i=>{let r=i.otherSE;t.set(i,{sine:tU(this.point,e.point,r.point),cosine:tF(this.point,e.point,r.point)})};return(e,r)=>{t.has(e)||i(e),t.has(r)||i(r);let{sine:n,cosine:s}=t.get(e),{sine:o,cosine:l}=t.get(r);return n.isGreaterThanOrEqualTo(0)&&o.isGreaterThanOrEqualTo(0)?s.isLessThan(l)?1:s.isGreaterThan(l)?-1:0:n.isLessThan(0)&&o.isLessThan(0)?s.isLessThan(l)?-1:s.isGreaterThan(l)?1:0:o.isLessThan(n)?-1:o.isGreaterThan(n)?1:0}}},tV=class e{constructor(e){f(this,"events"),f(this,"poly"),f(this,"_isExteriorRing"),f(this,"_enclosingRing"),this.events=e;for(let t=0,i=e.length;t<i;t++)e[t].segment.ringOut=this;this.poly=null}static factory(t){let i=[];for(let r=0,n=t.length;r<n;r++){let n=t[r];if(!n.isInResult()||n.ringOut)continue;let s=null,o=n.leftSE,l=n.rightSE,a=[o],u=o.point,c=[];for(;s=o,o=l,a.push(o),o.point!==u;)for(;;){let t=o.getAvailableLinkedEvents();if(0===t.length){let e=a[0].point,t=a[a.length-1].point;throw Error(`Unable to complete output ring starting at [${e.x}, ${e.y}]. Last matching segment found ends at [${t.x}, ${t.y}].`)}if(1===t.length){l=t[0].otherSE;break}let r=null;for(let e=0,t=c.length;e<t;e++)if(c[e].point===o.point){r=e;break}if(null!==r){let t=c.splice(r)[0],n=a.splice(t.index);n.unshift(n[0].otherSE),i.push(new e(n.reverse()));continue}c.push({index:a.length,point:o.point});let n=o.getLeftmostComparator(s);l=t.sort(n)[0].otherSE;break}i.push(new e(a))}return i}getGeom(){let e=this.events[0].point,t=[e];for(let i=1,r=this.events.length-1;i<r;i++){let r=this.events[i].point,n=this.events[i+1].point;0!==tA.orient(r,e,n)&&(t.push(r),e=r)}if(1===t.length)return null;let i=t[0],r=t[1];0===tA.orient(i,e,r)&&t.shift(),t.push(t[0]);let n=this.isExteriorRing()?1:-1,s=this.isExteriorRing()?0:t.length-1,o=this.isExteriorRing()?t.length:-1,l=[];for(let e=s;e!=o;e+=n)l.push([t[e].x.toNumber(),t[e].y.toNumber()]);return l}isExteriorRing(){if(void 0===this._isExteriorRing){let e=this.enclosingRing();this._isExteriorRing=!e||!e.isExteriorRing()}return this._isExteriorRing}enclosingRing(){return void 0===this._enclosingRing&&(this._enclosingRing=this._calcEnclosingRing()),this._enclosingRing}_calcEnclosingRing(){var e,t;let i=this.events[0];for(let e=1,t=this.events.length;e<t;e++){let t=this.events[e];tZ.compare(i,t)>0&&(i=t)}let r=i.segment.prevInResult(),n=r?r.prevInResult():null;for(;;){if(!r)return null;if(!n)return r.ringOut;if(n.ringOut!==r.ringOut)return(null==(e=n.ringOut)?void 0:e.enclosingRing())!==r.ringOut?r.ringOut:null==(t=r.ringOut)?void 0:t.enclosingRing();n=(r=n.prevInResult())?r.prevInResult():null}}},tK=class{constructor(e){f(this,"exteriorRing"),f(this,"interiorRings"),this.exteriorRing=e,e.poly=this,this.interiorRings=[]}addInterior(e){this.interiorRings.push(e),e.poly=this}getGeom(){let e=this.exteriorRing.getGeom();if(null===e)return null;let t=[e];for(let e=0,i=this.interiorRings.length;e<i;e++){let i=this.interiorRings[e].getGeom();null!==i&&t.push(i)}return t}},tQ=class{constructor(e){f(this,"rings"),f(this,"polys"),this.rings=e,this.polys=this._composePolys(e)}getGeom(){let e=[];for(let t=0,i=this.polys.length;t<i;t++){let i=this.polys[t].getGeom();null!==i&&e.push(i)}return e}_composePolys(e){var t;let i=[];for(let r=0,n=e.length;r<n;r++){let n=e[r];if(!n.poly){if(n.isExteriorRing())i.push(new tK(n));else{let e=n.enclosingRing();null!=e&&e.poly||i.push(new tK(e)),null==(t=null==e?void 0:e.poly)||t.addInterior(n)}}}return i}},tY=class{constructor(e,t=t0.compare){f(this,"queue"),f(this,"tree"),f(this,"segments"),this.queue=e,this.tree=new tk(t),this.segments=[]}process(e){let t=e.segment,i=[];if(e.consumedBy)return e.isLeft?this.queue.delete(e.otherSE):this.tree.delete(t),i;e.isLeft&&this.tree.add(t);let r=t,n=t;do r=this.tree.lastBefore(r);while(null!=r&&null!=r.consumedBy);do n=this.tree.firstAfter(n);while(null!=n&&null!=n.consumedBy);if(e.isLeft){let s=null;if(r){let e=r.getIntersection(t);if(null!==e&&(t.isAnEndpoint(e)||(s=e),!r.isAnEndpoint(e))){let t=this._splitSafely(r,e);for(let e=0,r=t.length;e<r;e++)i.push(t[e])}}let o=null;if(n){let e=n.getIntersection(t);if(null!==e&&(t.isAnEndpoint(e)||(o=e),!n.isAnEndpoint(e))){let t=this._splitSafely(n,e);for(let e=0,r=t.length;e<r;e++)i.push(t[e])}}if(null!==s||null!==o){let e=null;e=null===s?o:null===o?s:0>=tZ.comparePoints(s,o)?s:o,this.queue.delete(t.rightSE),i.push(t.rightSE);let r=t.split(e);for(let e=0,t=r.length;e<t;e++)i.push(r[e])}i.length>0?(this.tree.delete(t),i.push(e)):(this.segments.push(t),t.prev=r)}else{if(r&&n){let e=r.getIntersection(n);if(null!==e){if(!r.isAnEndpoint(e)){let t=this._splitSafely(r,e);for(let e=0,r=t.length;e<r;e++)i.push(t[e])}if(!n.isAnEndpoint(e)){let t=this._splitSafely(n,e);for(let e=0,r=t.length;e<r;e++)i.push(t[e])}}}this.tree.delete(t)}return i}_splitSafely(e,t){this.tree.delete(e);let i=e.rightSE;this.queue.delete(i);let r=e.split(t);return r.push(i),void 0===e.consumedBy&&this.tree.add(e),r}},tX=new class{constructor(){f(this,"type"),f(this,"numMultiPolys")}run(e,t,i){tX.type=e;let r=[new t4(t,!0)];for(let e=0,t=i.length;e<t;e++)r.push(new t4(i[e],!1));if(tX.numMultiPolys=r.length,"difference"===tX.type){let e=r[0],t=1;for(;t<r.length;)null!==tB(r[t].bbox,e.bbox)?t++:r.splice(t,1)}if("intersection"===tX.type)for(let e=0,t=r.length;e<t;e++){let t=r[e];for(let i=e+1,n=r.length;i<n;i++)if(null===tB(t.bbox,r[i].bbox))return[]}let n=new tk(tZ.compare);for(let e=0,t=r.length;e<t;e++){let t=r[e].getSweepEvents();for(let e=0,i=t.length;e<i;e++)n.add(t[e])}let s=new tY(n),o=null;for(0!=n.size&&(o=n.first(),n.delete(o));o;){let e=s.process(o);for(let t=0,i=e.length;t<i;t++){let i=e[t];void 0===i.consumedBy&&n.add(i)}0!=n.size?(o=n.first(),n.delete(o)):o=null}return tA.reset(),new tQ(tV.factory(s.segments)).getGeom()}},tJ=0,t0=class e{constructor(e,t,i,r){f(this,"id"),f(this,"leftSE"),f(this,"rightSE"),f(this,"rings"),f(this,"windings"),f(this,"ringOut"),f(this,"consumedBy"),f(this,"prev"),f(this,"_prevInResult"),f(this,"_beforeState"),f(this,"_afterState"),f(this,"_isInResult"),this.id=++tJ,this.leftSE=e,e.segment=this,e.otherSE=t,this.rightSE=t,t.segment=this,t.otherSE=e,this.rings=i,this.windings=r}static compare(e,t){let i=e.leftSE.point.x,r=t.leftSE.point.x,n=e.rightSE.point.x,s=t.rightSE.point.x;if(s.isLessThan(i))return 1;if(n.isLessThan(r))return -1;let o=e.leftSE.point.y,l=t.leftSE.point.y,a=e.rightSE.point.y,u=t.rightSE.point.y;if(i.isLessThan(r)){if(l.isLessThan(o)&&l.isLessThan(a))return 1;if(l.isGreaterThan(o)&&l.isGreaterThan(a))return -1;let i=e.comparePoint(t.leftSE.point);if(i<0)return 1;if(i>0)return -1;let r=t.comparePoint(e.rightSE.point);return 0!==r?r:-1}if(i.isGreaterThan(r)){if(o.isLessThan(l)&&o.isLessThan(u))return -1;if(o.isGreaterThan(l)&&o.isGreaterThan(u))return 1;let i=t.comparePoint(e.leftSE.point);if(0!==i)return i;let r=e.comparePoint(t.rightSE.point);return r<0?1:r>0?-1:1}if(o.isLessThan(l))return -1;if(o.isGreaterThan(l))return 1;if(n.isLessThan(s)){let i=t.comparePoint(e.rightSE.point);if(0!==i)return i}if(n.isGreaterThan(s)){let i=e.comparePoint(t.rightSE.point);if(i<0)return 1;if(i>0)return -1}if(!n.eq(s)){let e=a.minus(o),t=n.minus(i),c=u.minus(l),d=s.minus(r);if(e.isGreaterThan(t)&&c.isLessThan(d))return 1;if(e.isLessThan(t)&&c.isGreaterThan(d))return -1}return n.isGreaterThan(s)?1:n.isLessThan(s)||a.isLessThan(u)?-1:a.isGreaterThan(u)?1:e.id<t.id?-1:e.id>t.id?1:0}static fromRing(t,i,r){let n,s,o;let l=tZ.comparePoints(t,i);if(l<0)n=t,s=i,o=1;else if(l>0)n=i,s=t,o=-1;else throw Error(`Tried to create degenerate segment at [${t.x}, ${t.y}]`);return new e(new tZ(n,!0),new tZ(s,!1),[r],[o])}replaceRightSE(e){this.rightSE=e,this.rightSE.segment=this,this.rightSE.otherSE=this.leftSE,this.leftSE.otherSE=this.rightSE}bbox(){let e=this.leftSE.point.y,t=this.rightSE.point.y;return{ll:{x:this.leftSE.point.x,y:e.isLessThan(t)?e:t},ur:{x:this.rightSE.point.x,y:e.isGreaterThan(t)?e:t}}}vector(){return{x:this.rightSE.point.x.minus(this.leftSE.point.x),y:this.rightSE.point.y.minus(this.leftSE.point.y)}}isAnEndpoint(e){return e.x.eq(this.leftSE.point.x)&&e.y.eq(this.leftSE.point.y)||e.x.eq(this.rightSE.point.x)&&e.y.eq(this.rightSE.point.y)}comparePoint(e){return tA.orient(this.leftSE.point,e,this.rightSE.point)}getIntersection(e){let t=this.bbox(),i=e.bbox(),r=tB(t,i);if(null===r)return null;let n=this.leftSE.point,s=this.rightSE.point,o=e.leftSE.point,l=e.rightSE.point,a=tW(t,o)&&0===this.comparePoint(o),u=tW(i,n)&&0===e.comparePoint(n),c=tW(t,l)&&0===this.comparePoint(l),d=tW(i,s)&&0===e.comparePoint(s);if(u&&a)return d&&!c?s:!d&&c?l:null;if(u)return c&&n.x.eq(l.x)&&n.y.eq(l.y)?null:n;if(a)return d&&s.x.eq(o.x)&&s.y.eq(o.y)?null:o;if(d&&c)return null;if(d)return s;if(c)return l;let h=tH(n,this.vector(),o,e.vector());return null!==h&&tW(r,h)?tA.snap(h):null}split(t){let i=[],r=void 0!==t.events,n=new tZ(t,!0),s=new tZ(t,!1),o=this.rightSE;this.replaceRightSE(s),i.push(s),i.push(n);let l=new e(n,o,this.rings.slice(),this.windings.slice());return tZ.comparePoints(l.leftSE.point,l.rightSE.point)>0&&l.swapEvents(),tZ.comparePoints(this.leftSE.point,this.rightSE.point)>0&&this.swapEvents(),r&&(n.checkForConsuming(),s.checkForConsuming()),i}swapEvents(){let e=this.rightSE;this.rightSE=this.leftSE,this.leftSE=e,this.leftSE.isLeft=!0,this.rightSE.isLeft=!1;for(let e=0,t=this.windings.length;e<t;e++)this.windings[e]*=-1}consume(t){let i=this,r=t;for(;i.consumedBy;)i=i.consumedBy;for(;r.consumedBy;)r=r.consumedBy;let n=e.compare(i,r);if(0!==n){if(n>0){let e=i;i=r,r=e}if(i.prev===r){let e=i;i=r,r=e}for(let e=0,t=r.rings.length;e<t;e++){let t=r.rings[e],n=r.windings[e],s=i.rings.indexOf(t);-1===s?(i.rings.push(t),i.windings.push(n)):i.windings[s]+=n}r.rings=null,r.windings=null,r.consumedBy=i,r.leftSE.consumedBy=i.leftSE,r.rightSE.consumedBy=i.rightSE}}prevInResult(){return void 0!==this._prevInResult||(this.prev?this.prev.isInResult()?this._prevInResult=this.prev:this._prevInResult=this.prev.prevInResult():this._prevInResult=null),this._prevInResult}beforeState(){if(void 0!==this._beforeState)return this._beforeState;if(this.prev){let e=this.prev.consumedBy||this.prev;this._beforeState=e.afterState()}else this._beforeState={rings:[],windings:[],multiPolys:[]};return this._beforeState}afterState(){if(void 0!==this._afterState)return this._afterState;let e=this.beforeState();this._afterState={rings:e.rings.slice(0),windings:e.windings.slice(0),multiPolys:[]};let t=this._afterState.rings,i=this._afterState.windings,r=this._afterState.multiPolys;for(let e=0,r=this.rings.length;e<r;e++){let r=this.rings[e],n=this.windings[e],s=t.indexOf(r);-1===s?(t.push(r),i.push(n)):i[s]+=n}let n=[],s=[];for(let e=0,r=t.length;e<r;e++){if(0===i[e])continue;let r=t[e],o=r.poly;if(-1===s.indexOf(o)){if(r.isExterior)n.push(o);else{-1===s.indexOf(o)&&s.push(o);let e=n.indexOf(r.poly);-1!==e&&n.splice(e,1)}}}for(let e=0,t=n.length;e<t;e++){let t=n[e].multiPoly;-1===r.indexOf(t)&&r.push(t)}return this._afterState}isInResult(){if(this.consumedBy)return!1;if(void 0!==this._isInResult)return this._isInResult;let e=this.beforeState().multiPolys,t=this.afterState().multiPolys;switch(tX.type){case"union":{let i=0===e.length,r=0===t.length;this._isInResult=i!==r;break}case"intersection":{let i,r;e.length<t.length?(i=e.length,r=t.length):(i=t.length,r=e.length),this._isInResult=r===tX.numMultiPolys&&i<r;break}case"xor":{let i=Math.abs(e.length-t.length);this._isInResult=i%2==1;break}case"difference":this._isInResult=(1===e.length&&e[0].isSubject)!==(1===t.length&&t[0].isSubject)}return this._isInResult}},t1=class{constructor(e,t,i){if(f(this,"poly"),f(this,"isExterior"),f(this,"segments"),f(this,"bbox"),!Array.isArray(e)||0===e.length||(this.poly=t,this.isExterior=i,this.segments=[],"number"!=typeof e[0][0]||"number"!=typeof e[0][1]))throw Error("Input geometry is not a valid Polygon or MultiPolygon");let r=tA.snap({x:new tE(e[0][0]),y:new tE(e[0][1])});this.bbox={ll:{x:r.x,y:r.y},ur:{x:r.x,y:r.y}};let n=r;for(let t=1,i=e.length;t<i;t++){if("number"!=typeof e[t][0]||"number"!=typeof e[t][1])throw Error("Input geometry is not a valid Polygon or MultiPolygon");let i=tA.snap({x:new tE(e[t][0]),y:new tE(e[t][1])});i.x.eq(n.x)&&i.y.eq(n.y)||(this.segments.push(t0.fromRing(n,i,this)),i.x.isLessThan(this.bbox.ll.x)&&(this.bbox.ll.x=i.x),i.y.isLessThan(this.bbox.ll.y)&&(this.bbox.ll.y=i.y),i.x.isGreaterThan(this.bbox.ur.x)&&(this.bbox.ur.x=i.x),i.y.isGreaterThan(this.bbox.ur.y)&&(this.bbox.ur.y=i.y),n=i)}r.x.eq(n.x)&&r.y.eq(n.y)||this.segments.push(t0.fromRing(n,r,this))}getSweepEvents(){let e=[];for(let t=0,i=this.segments.length;t<i;t++){let i=this.segments[t];e.push(i.leftSE),e.push(i.rightSE)}return e}},t2=class{constructor(e,t){if(f(this,"multiPoly"),f(this,"exteriorRing"),f(this,"interiorRings"),f(this,"bbox"),!Array.isArray(e))throw Error("Input geometry is not a valid Polygon or MultiPolygon");this.exteriorRing=new t1(e[0],this,!0),this.bbox={ll:{x:this.exteriorRing.bbox.ll.x,y:this.exteriorRing.bbox.ll.y},ur:{x:this.exteriorRing.bbox.ur.x,y:this.exteriorRing.bbox.ur.y}},this.interiorRings=[];for(let t=1,i=e.length;t<i;t++){let i=new t1(e[t],this,!1);i.bbox.ll.x.isLessThan(this.bbox.ll.x)&&(this.bbox.ll.x=i.bbox.ll.x),i.bbox.ll.y.isLessThan(this.bbox.ll.y)&&(this.bbox.ll.y=i.bbox.ll.y),i.bbox.ur.x.isGreaterThan(this.bbox.ur.x)&&(this.bbox.ur.x=i.bbox.ur.x),i.bbox.ur.y.isGreaterThan(this.bbox.ur.y)&&(this.bbox.ur.y=i.bbox.ur.y),this.interiorRings.push(i)}this.multiPoly=t}getSweepEvents(){let e=this.exteriorRing.getSweepEvents();for(let t=0,i=this.interiorRings.length;t<i;t++){let i=this.interiorRings[t].getSweepEvents();for(let t=0,r=i.length;t<r;t++)e.push(i[t])}return e}},t4=class{constructor(e,t){if(f(this,"isSubject"),f(this,"polys"),f(this,"bbox"),!Array.isArray(e))throw Error("Input geometry is not a valid Polygon or MultiPolygon");try{"number"==typeof e[0][0][0]&&(e=[e])}catch{}this.polys=[],this.bbox={ll:{x:new tE(Number.POSITIVE_INFINITY),y:new tE(Number.POSITIVE_INFINITY)},ur:{x:new tE(Number.NEGATIVE_INFINITY),y:new tE(Number.NEGATIVE_INFINITY)}};for(let t=0,i=e.length;t<i;t++){let i=new t2(e[t],this);i.bbox.ll.x.isLessThan(this.bbox.ll.x)&&(this.bbox.ll.x=i.bbox.ll.x),i.bbox.ll.y.isLessThan(this.bbox.ll.y)&&(this.bbox.ll.y=i.bbox.ll.y),i.bbox.ur.x.isGreaterThan(this.bbox.ur.x)&&(this.bbox.ur.x=i.bbox.ur.x),i.bbox.ur.y.isGreaterThan(this.bbox.ur.y)&&(this.bbox.ur.y=i.bbox.ur.y),this.polys.push(i)}this.isSubject=t}getSweepEvents(){let e=[];for(let t=0,i=this.polys.length;t<i;t++){let i=this.polys[t].getSweepEvents();for(let t=0,r=i.length;t<r;t++)e.push(i[t])}return e}},t7=(e,...t)=>tX.run("union",e,t),t3=(e,...t)=>tX.run("difference",e,t);function t5(e,t){var i,r,n,s,o,l,a,u,c,d,h=0,f="FeatureCollection"===e.type,g="Feature"===e.type,p=f?e.features.length:1;for(i=0;i<p;i++){for(l=f?e.features[i].geometry:g?e.geometry:e,u=f?e.features[i].properties:g?e.properties:{},c=f?e.features[i].bbox:g?e.bbox:void 0,d=f?e.features[i].id:g?e.id:void 0,o=(a=!!l&&"GeometryCollection"===l.type)?l.geometries.length:1,n=0;n<o;n++){if(null===(s=a?l.geometries[n]:l)){if(!1===t(null,h,u,c,d))return!1;continue}switch(s.type){case"Point":case"LineString":case"MultiPoint":case"Polygon":case"MultiLineString":case"MultiPolygon":if(!1===t(s,h,u,c,d))return!1;break;case"GeometryCollection":for(r=0;r<s.geometries.length;r++)if(!1===t(s.geometries[r],h,u,c,d))return!1;break;default:throw Error("Unknown Geometry Type")}}h++}}tA.set;var t8=function(e,t={}){let i=[];if(t5(e,e=>{i.push(e.coordinates)}),i.length<2)throw Error("Must have at least 2 geometries");let r=t7(i[0],...i.slice(1));return 0===r.length?null:1===r.length?ts(r[0],t.properties):tl(r,t.properties)},t9=function(e,t={}){if(null!=e.bbox&&!0!==t.recompute)return e.bbox;let i=[1/0,1/0,-1/0,-1/0];return function e(t,i,r){if(null!==t)for(var n,s,o,l,a,u,c,d,h=0,f=0,g=t.type,p="FeatureCollection"===g,m="Feature"===g,y=p?t.features.length:1,v=0;v<y;v++){a=(d=!!(c=p?t.features[v].geometry:m?t.geometry:t)&&"GeometryCollection"===c.type)?c.geometries.length:1;for(var b=0;b<a;b++){var x=0,L=0;if(null!==(l=d?c.geometries[b]:c)){u=l.coordinates;var w=l.type;switch(h=0,w){case null:break;case"Point":if(!1===i(u,f,v,x,L))return!1;f++,x++;break;case"LineString":case"MultiPoint":for(n=0;n<u.length;n++){if(!1===i(u[n],f,v,x,L))return!1;f++,"MultiPoint"===w&&x++}"LineString"===w&&x++;break;case"Polygon":case"MultiLineString":for(n=0;n<u.length;n++){for(s=0;s<u[n].length-h;s++){if(!1===i(u[n][s],f,v,x,L))return!1;f++}"MultiLineString"===w&&x++,"Polygon"===w&&L++}"Polygon"===w&&x++;break;case"MultiPolygon":for(n=0;n<u.length;n++){for(L=0,s=0;s<u[n].length;s++){for(o=0;o<u[n][s].length-h;o++){if(!1===i(u[n][s][o],f,v,x,L))return!1;f++}L++}x++}break;case"GeometryCollection":for(n=0;n<l.geometries.length;n++)if(!1===e(l.geometries[n],i))return!1;break;default:throw Error("Unknown Geometry Type")}}}}}(e,e=>{i[0]>e[0]&&(i[0]=e[0]),i[1]>e[1]&&(i[1]=e[1]),i[2]<e[0]&&(i[2]=e[0]),i[3]<e[1]&&(i[3]=e[1])}),i},t6=function(e){let t=[];if(t5(e,e=>{t.push(e.coordinates)}),t.length<2)throw Error("Must have at least two features");let i=e.features[0].properties||{},r=t3(t[0],...t.slice(1));return 0===r.length?null:1===r.length?ts(r[0],i):tl(r,i)},ie=function(e){if(!e)throw Error("geojson is required");var t,i=[];return t=function(e){i.push(e)},t5(e,function(e,i,r,n,s){var o,l=null===e?null:e.type;switch(l){case null:case"Point":case"LineString":case"Polygon":return!1!==t(tn(e,r,{bbox:n,id:s}),i,0)&&void 0}switch(l){case"MultiPoint":o="Point";break;case"MultiLineString":o="LineString";break;case"MultiPolygon":o="Polygon"}for(var a=0;a<e.coordinates.length;a++)if(!1===t(tn({type:o,coordinates:e.coordinates[a]},r),i,a))return!1}),to(i)};function it(e,t){let i=t6(to([ts([[[180,90],[-180,90],[-180,-90],[180,-90],[180,90]]]),e]));if(!i)return;i.properties={isMask:!0};let r=eW(t9(e)),n=(r[2]-r[0])/360/1e3,s=r[0]<-180,o=r[2]>180,l=ie(e);if(l.features.length>1&&(s||o))for(let e of l.features){let t=eW(t9(e));if(o&&t[0]<-180+n)for(let t of e.geometry.coordinates)for(let e of t)e[0]+=360-n;if(s&&t[2]>180-n)for(let t of e.geometry.coordinates)for(let e of t)e[0]-=360-n}t(to([l.features.length<2?e:t8(l)??e,i]))}let ii={fill:{paint:{"fill-color":"#000","fill-opacity":.1},filter:["all",["==",["geometry-type"],"Polygon"],["has","isMask"]]},line:{layout:{"line-cap":"square"},paint:{"line-width":["case",["==",["geometry-type"],"Polygon"],2,3],"line-dasharray":[1,1],"line-color":"#3170fe"},filter:["!",["has","isMask"]]}},ir="mtlr-gc-full-geom",is="mtlr-gc-full-geom-fill",io="mtlr-gc-full-geom-line",{MapLibreBasedGeocodingControl:il,events:ia}=function(e,t,i){var r,n,s;class o{constructor(e,t){f(this,"type"),f(this,"target"),this.type=t,this.target=e}}class l extends o{constructor(e,t){super(e,"select"),f(this,"feature"),Object.assign(this,t)}}class a extends o{constructor(e,t){super(e,"featureslisted"),f(this,"features"),this.features=t}}class u extends o{constructor(e,t){super(e,"featuresmarked"),f(this,"features"),this.features=t}}class c extends o{constructor(e,t){super(e,"optionsvisibilitychange"),f(this,"optionsVisible"),this.optionsVisible=t}}class d extends o{constructor(e,t){super(e,"pick"),f(this,"feature"),this.feature=t}}class h extends o{constructor(e,t){super(e,"querychange"),f(this,"query"),this.query=t}}class g extends o{constructor(e,t,i){super(e,"response"),f(this,"url"),f(this,"featureCollection"),this.url=t,this.featureCollection=i}}class v extends o{constructor(e,t){super(e,"reversetoggle"),f(this,"reverse"),this.reverse=t}}class b extends e{constructor(e={}){super(),m(this,r),m(this,n),m(this,s),y(this,n,e)}onAddInt(e){let o=document.createElement("div");o.className="mapboxgl-ctrl-geocoder mapboxgl-ctrl maplibregl-ctrl-geocoder maplibregl-ctrl mapboxgl-ctrl-group";let{marker:f,showResultMarkers:m,flyTo:b,fullGeometryStyle:x,...L}=p(this,n),w="boolean"==typeof b?{}:b,E={mapController:function(e,t,i=!0,r=!0,n={},s={},o=ii){let l,a,u,c;let d=[];function h(){if(!e.loaded){e.once("load",h);return}let t=o?!0===o?ii:o:void 0;if(!(null!=t&&t.fill)&&!(null!=t&&t.line))return;let i=e.getSource(ir);if(i)i.setData(c??to([]));else{if(!c)return;e.addSource(ir,{type:"geojson",data:c})}!e.getLayer(is)&&null!=t&&t.fill&&e.addLayer({...null==t?void 0:t.fill,id:is,type:"fill",source:ir}),!e.getLayer(io)&&null!=t&&t.line&&e.addLayer({...null==t?void 0:t.line,id:io,type:"line",source:ir})}function f(e){c=e,h()}e.on("styledata",()=>{setTimeout(()=>{c&&h()})});let g=e=>{null==l||l({type:"mapClick",coordinates:[e.lngLat.lng,e.lngLat.lat]})};function p(e=!1){if(!t)throw Error();let i=document.createElement("div");return e&&i.classList.add("marker-interactive"),new e_({props:{displayIn:"maplibre"},target:i}),new t.Marker({element:i,offset:[1,-13]})}return{setEventHandler(t){t?(l=t,e.on("click",g)):(l=void 0,e.off("click",g))},flyTo(t,i){e.flyTo({center:t,...i?{zoom:i}:{},...n})},fitBounds(t,i,r){e.fitBounds([[t[0],t[1]],[t[2],t[3]]],{padding:i,...r?{maxZoom:r}:{},...s})},indicateReverse(t){e.getCanvasContainer().style.cursor=t?"crosshair":""},setReverseMarker(r){t&&i&&(u?r?u.setLngLat(r):(u.remove(),u=void 0):r&&(i instanceof Function?u=i(e)??void 0:(u=("object"==typeof i?new t.Marker(i):p()).setLngLat(r).addTo(e)).getElement().classList.add("marker-reverse")))},setFeatures(n,s,o){for(let e of d)e.remove();if(d.length=0,f(void 0),t){e:if(s){let r=!1;if("GeometryCollection"===s.geometry.type){let e=s.geometry.geometries.filter(e=>"Polygon"===e.type||"MultiPolygon"===e.type);t:if(e.length>0){let t=t8(to(e.map(e=>tn(e))));if(!t)break t;it({...s,geometry:t.geometry},f),r=!0}else{let e=s.geometry.geometries.filter(e=>"LineString"===e.type||"MultiLineString"===e.type);e.length>0&&(f({...s,geometry:{type:"GeometryCollection",geometries:e}}),r=!0)}}if(!r){if("Polygon"===s.geometry.type||"MultiPolygon"===s.geometry.type)it(s,f);else if("LineString"===s.geometry.type||"MultiLineString"===s.geometry.type){f(s);break e}}if(!o&&!s.geometry.type.endsWith("Point"))break e;if(i instanceof Function){let t=i(e,s);t&&d.push(t)}else i&&d.push("object"==typeof i?new t.Marker(i):p().setLngLat(s.center).addTo(e))}if(r)for(let i of n??[]){let n;if(i===s)continue;if(r instanceof Function){if(!(n=r(e,i)))continue}else n=("object"==typeof r?new t.Marker(r):p(!0)).setLngLat(i.center).setPopup(new t.Popup({offset:[1,-27],closeButton:!1,closeOnMove:!0,className:"maptiler-gc-popup"}).setText("reverse"===i.place_type[0]?i.place_name:i.place_name.replace(/,.*/,""))).addTo(e);let o=n.getElement();o.addEventListener("click",e=>{e.stopPropagation(),null==l||l({type:"markerClick",id:i.id})}),o.addEventListener("mouseenter",()=>{null==l||l({type:"markerMouseEnter",id:i.id}),n.togglePopup()}),o.addEventListener("mouseleave",()=>{null==l||l({type:"markerMouseLeave",id:i.id}),n.togglePopup()}),d.push(n)}}},setSelectedMarker(e){a&&a.getElement().classList.toggle("marker-selected",!1),null==(a=e>-1?d[e]:void 0)||a.getElement().classList.toggle("marker-selected",!0)},getCenterAndZoom(){let t=e.getCenter();return[e.getZoom(),t.lng,t.lat]}}}(e,t,f,m,w,w,x),flyTo:void 0===b||!!b,apiKey:"",...null==i?void 0:i(e,o),...L};if(!E.apiKey)throw Error("no apiKey provided");return y(this,r,new tr({target:o,props:E})),p(this,r).$on("select",e=>{this.fire(new l(this,e.detail))}),p(this,r).$on("pick",e=>{this.fire(new d(this,e.detail.feature))}),p(this,r).$on("featureslisted",e=>{this.fire(new a(this,e.detail.features))}),p(this,r).$on("featuresmarked",e=>{this.fire(new u(this,e.detail.features))}),p(this,r).$on("response",e=>{this.fire(new g(this,e.detail.url,e.detail.featureCollection))}),p(this,r).$on("optionsvisibilitychange",e=>{this.fire(new c(this,e.detail.optionsVisible))}),p(this,r).$on("reversetoggle",e=>{this.fire(new v(this,e.detail.reverse))}),p(this,r).$on("querychange",e=>{this.fire(new h(this,e.detail.query))}),y(this,s,o),o}on(e,t){return super.on(e,t)}once(e,t){return super.once(e,t)}off(e,t){return super.off(e,t)}listens(e){return super.listens(e)}setOptions(e){var t;Object.assign(p(this,n),e);let{marker:i,showResultMarkers:s,flyTo:o,fullGeometryStyle:l,...a}=p(this,n);null==(t=p(this,r))||t.$set(a)}setQuery(e,t=!0){var i;null==(i=p(this,r))||i.setQuery(e,t)}clearMap(){var e;null==(e=p(this,r))||e.clearMap()}clearList(){var e;null==(e=p(this,r))||e.clearList()}setReverseMode(e){var t;null==(t=p(this,r))||t.$set({reverseActive:e})}focus(e){var t;null==(t=p(this,r))||t.focus(e)}blur(){var e;null==(e=p(this,r))||e.blur()}onRemove(){var e,t,i;null==(e=p(this,r))||e.$destroy(),y(this,r,void 0),null==(i=null==(t=p(this,s))?void 0:t.parentNode)||i.removeChild(p(this,s))}}return r=new WeakMap,n=new WeakMap,s=new WeakMap,{MapLibreBasedGeocodingControl:b,events:{SelectEvent:l,FeaturesListedEvent:a,FeaturesMarkedEvent:u,OptionsVisibilityChangeEvent:c,PickEvent:d,QueryChangeEvent:h,ResponseEvent:g,ReverseToggleEvent:v}}}(u.Evented,u,(e,t)=>{let i={};if(!("getSdkConfig"in e&&"function"==typeof e.getSdkConfig))throw Error("MapTiler SDK not detected");let{primaryLanguage:r,apiKey:n}=e.getSdkConfig();i.apiKey=n;let s=/^([a-z]{2})($|_|-)/.exec(r);return s&&(i.language=s[1]),t.className+=" maptiler-ctrl",i});class iu extends il{onAdd(e){var t;return null==(t=e.telemetry)||t.registerModule("@maptiler/geocoding-control","2.1.6"),super.onAddInt(e)}}ia.SelectEvent,ia.FeaturesListedEvent,ia.FeaturesMarkedEvent,ia.OptionsVisibilityChangeEvent,ia.PickEvent,ia.QueryChangeEvent,ia.ResponseEvent,ia.ReverseToggleEvent}}]);