try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="29ebdd96-8b07-479c-a025-1a8422346d05",e._sentryDebugIdIdentifier="sentry-dbid-29ebdd96-8b07-479c-a025-1a8422346d05")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8532],{80816:function(e,t,o){let r;o.d(t,{$:function(){return eY},DH:function(){return tG},Eo:function(){return eU},Im:function(){return tD},Oq:function(){return eR},Qo:function(){return tL},WT:function(){return eP},_x:function(){return ez},hE:function(){return ej},jH:function(){return eN},m$:function(){return eC},pb:function(){return tU},uW:function(){return eF},vo:function(){return tB},yy:function(){return eL},zf:function(){return tO}});var l,a,s,n,u,h,c,m,d,v,f,p,g,x,w,T,S,y,M,_,R,b,E,A,P,C,I,z,F,D,O,N,k,W,L,G,U,$,B,Z,j,Y,X,V,H,K,q,J,Q=o(93396),ee=o(17187),et=o(83454),ei=Object.defineProperty,eo=e=>{throw TypeError(e)},er=(e,t,o)=>t in e?ei(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,el=(e,t,o)=>er(e,"symbol"!=typeof t?t+"":t,o),ea=(e,t,o)=>t.has(e)||eo("Cannot "+o),es=(e,t,o)=>(ea(e,t,"read from private field"),o?o.call(e):t.get(e)),en=(e,t,o)=>t.has(e)?eo("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,o),eu=(e,t,o,r)=>(ea(e,t,"write to private field"),r?r.call(e,o):t.set(e,o),o),eh=(e,t,o)=>(ea(e,t,"access private method"),o),ec=(e,t,o,r)=>({set _(i){eu(e,t,i,o)},get _(){return es(e,t,r)}});function em(e,t,o){let r=Math.acos(Math.min(Math.max(e[0]*t[0]+e[1]*t[1]+e[2]*t[2],-1),1));if(r<1e-6)return e;let l=Math.sin(r),a=Math.sin((1-o)*r)/l,s=Math.sin(o*r)/l;return[e[0]*a+t[0]*s,e[1]*a+t[1]*s,e[2]*a+t[2]*s]}let ed="object"==typeof performance&&performance&&"function"==typeof performance.now?performance:Date,ev=new Set,ef="object"==typeof et&&et?et:{},ep=(e,t,o,r)=>{"function"==typeof ef.emitWarning?ef.emitWarning(e,t,o,r):console.error(`[${o}] ${t}: ${e}`)},eg=globalThis.AbortController,ex=globalThis.AbortSignal;if(typeof eg>"u"){ex=class{constructor(){el(this,"onabort"),el(this,"_onabort",[]),el(this,"reason"),el(this,"aborted",!1)}addEventListener(e,t){this._onabort.push(t)}},eg=class{constructor(){el(this,"signal",new ex),t()}abort(e){var t,o;if(!this.signal.aborted){for(let t of(this.signal.reason=e,this.signal.aborted=!0,this.signal._onabort))t(e);null==(o=(t=this.signal).onabort)||o.call(t,e)}}};let e=(null==(l=ef.env)?void 0:l.LRU_CACHE_IGNORE_AC_WARNING)!=="1",t=()=>{e&&(e=!1,ep("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",t))}}let ew=e=>!ev.has(e),eT=e=>e&&e===Math.floor(e)&&e>0&&isFinite(e),eS=e=>eT(e)?e<=256?Uint8Array:e<=65536?Uint16Array:e<=4294967296?Uint32Array:e<=Number.MAX_SAFE_INTEGER?ey:null:null;class ey extends Array{constructor(e){super(e),this.fill(0)}}let eM=class e{constructor(t,o){if(el(this,"heap"),el(this,"length"),!es(e,a))throw TypeError("instantiate Stack using Stack.create(n)");this.heap=new o(t),this.length=0}static create(t){let o=eS(t);if(!o)return[];eu(e,a,!0);let r=new e(t,o);return eu(e,a,!1),r}push(e){this.heap[this.length++]=e}pop(){return this.heap[--this.length]}};en(eM,a=new WeakMap,!1);let e_=(J=class{constructor(e){en(this,z),en(this,u),en(this,h),en(this,c),en(this,m),en(this,d),en(this,v),el(this,"ttl"),el(this,"ttlResolution"),el(this,"ttlAutopurge"),el(this,"updateAgeOnGet"),el(this,"updateAgeOnHas"),el(this,"allowStale"),el(this,"noDisposeOnSet"),el(this,"noUpdateTTL"),el(this,"maxEntrySize"),el(this,"sizeCalculation"),el(this,"noDeleteOnFetchRejection"),el(this,"noDeleteOnStaleGet"),el(this,"allowStaleOnFetchAbort"),el(this,"allowStaleOnFetchRejection"),el(this,"ignoreFetchAbort"),en(this,f),en(this,p),en(this,g),en(this,x),en(this,w),en(this,T),en(this,S),en(this,y),en(this,M),en(this,_),en(this,R),en(this,b),en(this,E),en(this,A),en(this,P),en(this,C),en(this,I),en(this,D,()=>{}),en(this,O,()=>{}),en(this,N,()=>{}),en(this,k,()=>!1),en(this,L,e=>{}),en(this,G,(e,t,o)=>{}),en(this,U,(e,t,o,r)=>{if(o||r)throw TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0}),el(this,s,"LRUCache");let{max:t=0,ttl:o,ttlResolution:r=1,ttlAutopurge:l,updateAgeOnGet:a,updateAgeOnHas:n,allowStale:$,dispose:B,disposeAfter:Z,noDisposeOnSet:j,noUpdateTTL:Y,maxSize:X=0,maxEntrySize:V=0,sizeCalculation:H,fetchMethod:K,memoMethod:q,noDeleteOnFetchRejection:Q,noDeleteOnStaleGet:ee,allowStaleOnFetchRejection:et,allowStaleOnFetchAbort:ei,ignoreFetchAbort:eo}=e;if(0!==t&&!eT(t))throw TypeError("max option must be a nonnegative integer");let er=t?eS(t):Array;if(!er)throw Error("invalid max value: "+t);if(eu(this,u,t),eu(this,h,X),this.maxEntrySize=V||es(this,h),this.sizeCalculation=H,this.sizeCalculation){if(!es(this,h)&&!this.maxEntrySize)throw TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if("function"!=typeof this.sizeCalculation)throw TypeError("sizeCalculation set to non-function")}if(void 0!==q&&"function"!=typeof q)throw TypeError("memoMethod must be a function if defined");if(eu(this,v,q),void 0!==K&&"function"!=typeof K)throw TypeError("fetchMethod must be a function if specified");if(eu(this,d,K),eu(this,C,!!K),eu(this,g,new Map),eu(this,x,Array(t).fill(void 0)),eu(this,w,Array(t).fill(void 0)),eu(this,T,new er(t)),eu(this,S,new er(t)),eu(this,y,0),eu(this,M,0),eu(this,_,eM.create(t)),eu(this,f,0),eu(this,p,0),"function"==typeof B&&eu(this,c,B),"function"==typeof Z?(eu(this,m,Z),eu(this,R,[])):(eu(this,m,void 0),eu(this,R,void 0)),eu(this,P,!!es(this,c)),eu(this,I,!!es(this,m)),this.noDisposeOnSet=!!j,this.noUpdateTTL=!!Y,this.noDeleteOnFetchRejection=!!Q,this.allowStaleOnFetchRejection=!!et,this.allowStaleOnFetchAbort=!!ei,this.ignoreFetchAbort=!!eo,0!==this.maxEntrySize){if(0!==es(this,h)&&!eT(es(this,h)))throw TypeError("maxSize must be a positive integer if specified");if(!eT(this.maxEntrySize))throw TypeError("maxEntrySize must be a positive integer if specified");eh(this,z,W).call(this)}if(this.allowStale=!!$,this.noDeleteOnStaleGet=!!ee,this.updateAgeOnGet=!!a,this.updateAgeOnHas=!!n,this.ttlResolution=eT(r)||0===r?r:1,this.ttlAutopurge=!!l,this.ttl=o||0,this.ttl){if(!eT(this.ttl))throw TypeError("ttl must be a positive integer if specified");eh(this,z,F).call(this)}if(0===es(this,u)&&0===this.ttl&&0===es(this,h))throw TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!es(this,u)&&!es(this,h)){let e="LRU_CACHE_UNBOUNDED";ew(e)&&(ev.add(e),ep("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",e,J))}}static unsafeExposeInternals(e){return{starts:es(e,E),ttls:es(e,A),sizes:es(e,b),keyMap:es(e,g),keyList:es(e,x),valList:es(e,w),next:es(e,T),prev:es(e,S),get head(){return es(e,y)},get tail(){return es(e,M)},free:es(e,_),isBackgroundFetch:t=>eh(e,z,X).call(e,t),backgroundFetch:(t,o,r,l)=>eh(e,z,Y).call(e,t,o,r,l),moveToTail:t=>eh(e,z,H).call(e,t),indexes:t=>eh(e,z,$).call(e,t),rindexes:t=>eh(e,z,B).call(e,t),isStale:t=>es(e,k).call(e,t)}}get max(){return es(this,u)}get maxSize(){return es(this,h)}get calculatedSize(){return es(this,p)}get size(){return es(this,f)}get fetchMethod(){return es(this,d)}get memoMethod(){return es(this,v)}get dispose(){return es(this,c)}get disposeAfter(){return es(this,m)}getRemainingTTL(e){return es(this,g).has(e)?1/0:0}*entries(){for(let e of eh(this,z,$).call(this))void 0===es(this,w)[e]||void 0===es(this,x)[e]||eh(this,z,X).call(this,es(this,w)[e])||(yield[es(this,x)[e],es(this,w)[e]])}*rentries(){for(let e of eh(this,z,B).call(this))void 0===es(this,w)[e]||void 0===es(this,x)[e]||eh(this,z,X).call(this,es(this,w)[e])||(yield[es(this,x)[e],es(this,w)[e]])}*keys(){for(let e of eh(this,z,$).call(this)){let t=es(this,x)[e];void 0===t||eh(this,z,X).call(this,es(this,w)[e])||(yield t)}}*rkeys(){for(let e of eh(this,z,B).call(this)){let t=es(this,x)[e];void 0===t||eh(this,z,X).call(this,es(this,w)[e])||(yield t)}}*values(){for(let e of eh(this,z,$).call(this))void 0===es(this,w)[e]||eh(this,z,X).call(this,es(this,w)[e])||(yield es(this,w)[e])}*rvalues(){for(let e of eh(this,z,B).call(this))void 0===es(this,w)[e]||eh(this,z,X).call(this,es(this,w)[e])||(yield es(this,w)[e])}[(n=Symbol.iterator,s=Symbol.toStringTag,n)](){return this.entries()}find(e,t={}){for(let o of eh(this,z,$).call(this)){let r=es(this,w)[o],l=eh(this,z,X).call(this,r)?r.__staleWhileFetching:r;if(void 0!==l&&e(l,es(this,x)[o],this))return this.get(es(this,x)[o],t)}}forEach(e,t=this){for(let o of eh(this,z,$).call(this)){let r=es(this,w)[o],l=eh(this,z,X).call(this,r)?r.__staleWhileFetching:r;void 0!==l&&e.call(t,l,es(this,x)[o],this)}}rforEach(e,t=this){for(let o of eh(this,z,B).call(this)){let r=es(this,w)[o],l=eh(this,z,X).call(this,r)?r.__staleWhileFetching:r;void 0!==l&&e.call(t,l,es(this,x)[o],this)}}purgeStale(){let e=!1;for(let t of eh(this,z,B).call(this,{allowStale:!0}))es(this,k).call(this,t)&&(eh(this,z,K).call(this,es(this,x)[t],"expire"),e=!0);return e}info(e){let t=es(this,g).get(e);if(void 0===t)return;let o=es(this,w)[t],r=eh(this,z,X).call(this,o)?o.__staleWhileFetching:o;if(void 0===r)return;let l={value:r};if(es(this,A)&&es(this,E)){let e=es(this,A)[t],o=es(this,E)[t];if(e&&o){let t=e-(ed.now()-o);l.ttl=t,l.start=Date.now()}}return es(this,b)&&(l.size=es(this,b)[t]),l}dump(){let e=[];for(let t of eh(this,z,$).call(this,{allowStale:!0})){let o=es(this,x)[t],r=es(this,w)[t],l=eh(this,z,X).call(this,r)?r.__staleWhileFetching:r;if(void 0===l||void 0===o)continue;let a={value:l};if(es(this,A)&&es(this,E)){a.ttl=es(this,A)[t];let e=ed.now()-es(this,E)[t];a.start=Math.floor(Date.now()-e)}es(this,b)&&(a.size=es(this,b)[t]),e.unshift([o,a])}return e}load(e){for(let[t,o]of(this.clear(),e)){if(o.start){let e=Date.now()-o.start;o.start=ed.now()-e}this.set(t,o.value,o)}}set(e,t,o={}){var r,l,a,s,n;if(void 0===t)return this.delete(e),this;let{ttl:h=this.ttl,start:d,noDisposeOnSet:v=this.noDisposeOnSet,sizeCalculation:p=this.sizeCalculation,status:y}=o,{noUpdateTTL:b=this.noUpdateTTL}=o,E=es(this,U).call(this,e,t,o.size||0,p);if(this.maxEntrySize&&E>this.maxEntrySize)return y&&(y.set="miss",y.maxEntrySizeExceeded=!0),eh(this,z,K).call(this,e,"set"),this;let D=0===es(this,f)?void 0:es(this,g).get(e);if(void 0===D)D=0===es(this,f)?es(this,M):0!==es(this,_).length?es(this,_).pop():es(this,f)===es(this,u)?eh(this,z,j).call(this,!1):es(this,f),es(this,x)[D]=e,es(this,w)[D]=t,es(this,g).set(e,D),es(this,T)[es(this,M)]=D,es(this,S)[D]=es(this,M),eu(this,M,D),ec(this,f)._++,es(this,G).call(this,D,E,y),y&&(y.set="add"),b=!1;else{eh(this,z,H).call(this,D);let o=es(this,w)[D];if(t!==o){if(es(this,C)&&eh(this,z,X).call(this,o)){o.__abortController.abort(Error("replaced"));let{__staleWhileFetching:t}=o;void 0!==t&&!v&&(es(this,P)&&(null==(r=es(this,c))||r.call(this,t,e,"set")),es(this,I)&&(null==(l=es(this,R))||l.push([t,e,"set"])))}else v||(es(this,P)&&(null==(a=es(this,c))||a.call(this,o,e,"set")),es(this,I)&&(null==(s=es(this,R))||s.push([o,e,"set"])));if(es(this,L).call(this,D),es(this,G).call(this,D,E,y),es(this,w)[D]=t,y){y.set="replace";let e=o&&eh(this,z,X).call(this,o)?o.__staleWhileFetching:o;void 0!==e&&(y.oldValue=e)}}else y&&(y.set="update")}if(0===h||es(this,A)||eh(this,z,F).call(this),es(this,A)&&(b||es(this,N).call(this,D,h,d),y&&es(this,O).call(this,y,D)),!v&&es(this,I)&&es(this,R)){let e;let t=es(this,R);for(;e=null==t?void 0:t.shift();)null==(n=es(this,m))||n.call(this,...e)}return this}pop(){var e;try{for(;es(this,f);){let e=es(this,w)[es(this,y)];if(eh(this,z,j).call(this,!0),eh(this,z,X).call(this,e)){if(e.__staleWhileFetching)return e.__staleWhileFetching}else if(void 0!==e)return e}}finally{if(es(this,I)&&es(this,R)){let t;let o=es(this,R);for(;t=null==o?void 0:o.shift();)null==(e=es(this,m))||e.call(this,...t)}}}has(e,t={}){let{updateAgeOnHas:o=this.updateAgeOnHas,status:r}=t,l=es(this,g).get(e);if(void 0!==l){let e=es(this,w)[l];if(eh(this,z,X).call(this,e)&&void 0===e.__staleWhileFetching)return!1;if(!es(this,k).call(this,l))return o&&es(this,D).call(this,l),r&&(r.has="hit",es(this,O).call(this,r,l)),!0;r&&(r.has="stale",es(this,O).call(this,r,l))}else r&&(r.has="miss");return!1}peek(e,t={}){let{allowStale:o=this.allowStale}=t,r=es(this,g).get(e);if(void 0===r||!o&&es(this,k).call(this,r))return;let l=es(this,w)[r];return eh(this,z,X).call(this,l)?l.__staleWhileFetching:l}async fetch(e,t={}){let{allowStale:o=this.allowStale,updateAgeOnGet:r=this.updateAgeOnGet,noDeleteOnStaleGet:l=this.noDeleteOnStaleGet,ttl:a=this.ttl,noDisposeOnSet:s=this.noDisposeOnSet,size:n=0,sizeCalculation:u=this.sizeCalculation,noUpdateTTL:h=this.noUpdateTTL,noDeleteOnFetchRejection:c=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:m=this.allowStaleOnFetchRejection,ignoreFetchAbort:d=this.ignoreFetchAbort,allowStaleOnFetchAbort:v=this.allowStaleOnFetchAbort,context:f,forceRefresh:p=!1,status:x,signal:T}=t;if(!es(this,C))return x&&(x.fetch="get"),this.get(e,{allowStale:o,updateAgeOnGet:r,noDeleteOnStaleGet:l,status:x});let S={allowStale:o,updateAgeOnGet:r,noDeleteOnStaleGet:l,ttl:a,noDisposeOnSet:s,size:n,sizeCalculation:u,noUpdateTTL:h,noDeleteOnFetchRejection:c,allowStaleOnFetchRejection:m,allowStaleOnFetchAbort:v,ignoreFetchAbort:d,status:x,signal:T},y=es(this,g).get(e);if(void 0===y){x&&(x.fetch="miss");let t=eh(this,z,Y).call(this,e,y,S,f);return t.__returned=t}{let t=es(this,w)[y];if(eh(this,z,X).call(this,t)){let e=o&&void 0!==t.__staleWhileFetching;return x&&(x.fetch="inflight",e&&(x.returnedStale=!0)),e?t.__staleWhileFetching:t.__returned=t}let l=es(this,k).call(this,y);if(!p&&!l)return x&&(x.fetch="hit"),eh(this,z,H).call(this,y),r&&es(this,D).call(this,y),x&&es(this,O).call(this,x,y),t;let a=eh(this,z,Y).call(this,e,y,S,f),s=void 0!==a.__staleWhileFetching&&o;return x&&(x.fetch=l?"stale":"refresh",s&&l&&(x.returnedStale=!0)),s?a.__staleWhileFetching:a.__returned=a}}async forceFetch(e,t={}){let o=await this.fetch(e,t);if(void 0===o)throw Error("fetch() returned undefined");return o}memo(e,t={}){let o=es(this,v);if(!o)throw Error("no memoMethod provided to constructor");let{context:r,forceRefresh:l,...a}=t,s=this.get(e,a);if(!l&&void 0!==s)return s;let n=o(e,s,{options:a,context:r});return this.set(e,n,a),n}get(e,t={}){let{allowStale:o=this.allowStale,updateAgeOnGet:r=this.updateAgeOnGet,noDeleteOnStaleGet:l=this.noDeleteOnStaleGet,status:a}=t,s=es(this,g).get(e);if(void 0!==s){let t=es(this,w)[s],n=eh(this,z,X).call(this,t);return a&&es(this,O).call(this,a,s),es(this,k).call(this,s)?(a&&(a.get="stale"),n?(a&&o&&void 0!==t.__staleWhileFetching&&(a.returnedStale=!0),o?t.__staleWhileFetching:void 0):(l||eh(this,z,K).call(this,e,"expire"),a&&o&&(a.returnedStale=!0),o?t:void 0)):(a&&(a.get="hit"),n?t.__staleWhileFetching:(eh(this,z,H).call(this,s),r&&es(this,D).call(this,s),t))}a&&(a.get="miss")}delete(e){return eh(this,z,K).call(this,e,"delete")}clear(){return eh(this,z,q).call(this,"delete")}},u=new WeakMap,h=new WeakMap,c=new WeakMap,m=new WeakMap,d=new WeakMap,v=new WeakMap,f=new WeakMap,p=new WeakMap,g=new WeakMap,x=new WeakMap,w=new WeakMap,T=new WeakMap,S=new WeakMap,y=new WeakMap,M=new WeakMap,_=new WeakMap,R=new WeakMap,b=new WeakMap,E=new WeakMap,A=new WeakMap,P=new WeakMap,C=new WeakMap,I=new WeakMap,z=new WeakSet,F=function(){let e=new ey(es(this,u)),t=new ey(es(this,u));eu(this,A,e),eu(this,E,t),eu(this,N,(o,r,l=ed.now())=>{if(t[o]=0!==r?l:0,e[o]=r,0!==r&&this.ttlAutopurge){let e=setTimeout(()=>{es(this,k).call(this,o)&&eh(this,z,K).call(this,es(this,x)[o],"expire")},r+1);e.unref&&e.unref()}}),eu(this,D,o=>{t[o]=0!==e[o]?ed.now():0}),eu(this,O,(l,a)=>{if(e[a]){let s=e[a],n=t[a];if(!s||!n)return;l.ttl=s,l.start=n,l.now=o||r();let u=l.now-n;l.remainingTTL=s-u}});let o=0,r=()=>{let e=ed.now();if(this.ttlResolution>0){o=e;let t=setTimeout(()=>o=0,this.ttlResolution);t.unref&&t.unref()}return e};this.getRemainingTTL=l=>{let a=es(this,g).get(l);if(void 0===a)return 0;let s=e[a],n=t[a];return s&&n?s-((o||r())-n):1/0},eu(this,k,l=>{let a=t[l],s=e[l];return!!s&&!!a&&(o||r())-a>s})},D=new WeakMap,O=new WeakMap,N=new WeakMap,k=new WeakMap,W=function(){let e=new ey(es(this,u));eu(this,p,0),eu(this,b,e),eu(this,L,t=>{eu(this,p,es(this,p)-e[t]),e[t]=0}),eu(this,U,(e,t,o,r)=>{if(eh(this,z,X).call(this,t))return 0;if(!eT(o)){if(r){if("function"!=typeof r)throw TypeError("sizeCalculation must be a function");if(!eT(o=r(t,e)))throw TypeError("sizeCalculation return invalid (expect positive integer)")}else throw TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.")}return o}),eu(this,G,(t,o,r)=>{if(e[t]=o,es(this,h)){let o=es(this,h)-e[t];for(;es(this,p)>o;)eh(this,z,j).call(this,!0)}eu(this,p,es(this,p)+e[t]),r&&(r.entrySize=o,r.totalCalculatedSize=es(this,p))})},L=new WeakMap,G=new WeakMap,U=new WeakMap,$=function*({allowStale:e=this.allowStale}={}){if(es(this,f))for(let t=es(this,M);!(!eh(this,z,Z).call(this,t)||((e||!es(this,k).call(this,t))&&(yield t),t===es(this,y)));)t=es(this,S)[t]},B=function*({allowStale:e=this.allowStale}={}){if(es(this,f))for(let t=es(this,y);!(!eh(this,z,Z).call(this,t)||((e||!es(this,k).call(this,t))&&(yield t),t===es(this,M)));)t=es(this,T)[t]},Z=function(e){return void 0!==e&&es(this,g).get(es(this,x)[e])===e},j=function(e){var t,o;let r=es(this,y),l=es(this,x)[r],a=es(this,w)[r];return es(this,C)&&eh(this,z,X).call(this,a)?a.__abortController.abort(Error("evicted")):(es(this,P)||es(this,I))&&(es(this,P)&&(null==(t=es(this,c))||t.call(this,a,l,"evict")),es(this,I)&&(null==(o=es(this,R))||o.push([a,l,"evict"]))),es(this,L).call(this,r),e&&(es(this,x)[r]=void 0,es(this,w)[r]=void 0,es(this,_).push(r)),1===es(this,f)?(eu(this,y,eu(this,M,0)),es(this,_).length=0):eu(this,y,es(this,T)[r]),es(this,g).delete(l),ec(this,f)._--,r},Y=function(e,t,o,r){let l=void 0===t?void 0:es(this,w)[t];if(eh(this,z,X).call(this,l))return l;let a=new eg,{signal:s}=o;null==s||s.addEventListener("abort",()=>a.abort(s.reason),{signal:a.signal});let n={signal:a.signal,options:o,context:r},u=(r,l=!1)=>{let{aborted:s}=a.signal,u=o.ignoreFetchAbort&&void 0!==r;return(o.status&&(s&&!l?(o.status.fetchAborted=!0,o.status.fetchError=a.signal.reason,u&&(o.status.fetchAbortIgnored=!0)):o.status.fetchResolved=!0),!s||u||l)?(es(this,w)[t]===c&&(void 0===r?c.__staleWhileFetching?es(this,w)[t]=c.__staleWhileFetching:eh(this,z,K).call(this,e,"fetch"):(o.status&&(o.status.fetchUpdated=!0),this.set(e,r,n.options))),r):h(a.signal.reason)},h=r=>{let{aborted:l}=a.signal,s=l&&o.allowStaleOnFetchAbort,n=s||o.allowStaleOnFetchRejection,u=n||o.noDeleteOnFetchRejection;if(es(this,w)[t]===c&&(u&&void 0!==c.__staleWhileFetching?s||(es(this,w)[t]=c.__staleWhileFetching):eh(this,z,K).call(this,e,"fetch")),n)return o.status&&void 0!==c.__staleWhileFetching&&(o.status.returnedStale=!0),c.__staleWhileFetching;if(c.__returned===c)throw r};o.status&&(o.status.fetchDispatched=!0);let c=new Promise((t,r)=>{var s;let h=null==(s=es(this,d))?void 0:s.call(this,e,l,n);h&&h instanceof Promise&&h.then(e=>t(void 0===e?void 0:e),r),a.signal.addEventListener("abort",()=>{(!o.ignoreFetchAbort||o.allowStaleOnFetchAbort)&&(t(void 0),o.allowStaleOnFetchAbort&&(t=e=>u(e,!0)))})}).then(u,e=>(o.status&&(o.status.fetchRejected=!0,o.status.fetchError=e),h(e))),m=Object.assign(c,{__abortController:a,__staleWhileFetching:l,__returned:void 0});return void 0===t?(this.set(e,m,{...n.options,status:void 0}),t=es(this,g).get(e)):es(this,w)[t]=m,m},X=function(e){return!!es(this,C)&&!!e&&e instanceof Promise&&e.hasOwnProperty("__staleWhileFetching")&&e.__abortController instanceof eg},V=function(e,t){es(this,S)[t]=e,es(this,T)[e]=t},H=function(e){e!==es(this,M)&&(e===es(this,y)?eu(this,y,es(this,T)[e]):eh(this,z,V).call(this,es(this,S)[e],es(this,T)[e]),eh(this,z,V).call(this,es(this,M),e),eu(this,M,e))},K=function(e,t){var o,r,l,a;let s=!1;if(0!==es(this,f)){let l=es(this,g).get(e);if(void 0!==l){if(s=!0,1===es(this,f))eh(this,z,q).call(this,t);else{es(this,L).call(this,l);let a=es(this,w)[l];if(eh(this,z,X).call(this,a)?a.__abortController.abort(Error("deleted")):(es(this,P)||es(this,I))&&(es(this,P)&&(null==(o=es(this,c))||o.call(this,a,e,t)),es(this,I)&&(null==(r=es(this,R))||r.push([a,e,t]))),es(this,g).delete(e),es(this,x)[l]=void 0,es(this,w)[l]=void 0,l===es(this,M))eu(this,M,es(this,S)[l]);else if(l===es(this,y))eu(this,y,es(this,T)[l]);else{let e=es(this,S)[l];es(this,T)[e]=es(this,T)[l];let t=es(this,T)[l];es(this,S)[t]=es(this,S)[l]}ec(this,f)._--,es(this,_).push(l)}}}if(es(this,I)&&null!=(l=es(this,R))&&l.length){let e;let t=es(this,R);for(;e=null==t?void 0:t.shift();)null==(a=es(this,m))||a.call(this,...e)}return s},q=function(e){var t,o,r;for(let r of eh(this,z,B).call(this,{allowStale:!0})){let l=es(this,w)[r];if(eh(this,z,X).call(this,l))l.__abortController.abort(Error("deleted"));else{let a=es(this,x)[r];es(this,P)&&(null==(t=es(this,c))||t.call(this,l,a,e)),es(this,I)&&(null==(o=es(this,R))||o.push([l,a,e]))}}if(es(this,g).clear(),es(this,w).fill(void 0),es(this,x).fill(void 0),es(this,A)&&es(this,E)&&(es(this,A).fill(0),es(this,E).fill(0)),es(this,b)&&es(this,b).fill(0),eu(this,y,0),eu(this,M,0),es(this,_).length=0,eu(this,p,0),eu(this,f,0),es(this,I)&&es(this,R)){let e;let t=es(this,R);for(;e=null==t?void 0:t.shift();)null==(r=es(this,m))||r.call(this,...e)}},J);var eR=((r=eR||{})[r.r=0]="r",r[r.g=1]="g",r[r.b=2]="b",r[r.a=3]="a",r);let eb=null,eE=null,eA=null;function eP(e){return`vec4(${(e[0]/255).toFixed(3)},${(e[1]/255).toFixed(3)},${(e[2]/255).toFixed(3)},${((e[3]??255)/255).toFixed(3)})`}function eC(e){return[e[0]/255,e[1]/255,e[2]/255,(e[3]??255)/255]}function eI(e,t){return[e/360,180/Math.PI*Math.log(Math.tan(Math.PI/4+t*Math.PI/360))/360]}function ez(e,t){return function(e){let t=Math.PI;return((0,Math.atan)((0,Math.exp)(2*e*t))-t/4)*360/t}(-.5*(e/t*2-1))}let eF=2*Math.PI*6378137;function eD(e,t){return eI(e,t).map(e=>e*eF)}function eO(e,t,o){let r;let l=Math.floor(t*e.width),a=Math.floor(o*e.height),s=function e(){return eE&&eA||(eb=new e_({max:null==(eA=(eE=document.createElement("canvas")).getContext("webgl2"))?void 0:eA.getParameter(eA.MAX_TEXTURE_IMAGE_UNITS),dispose:(t,o)=>{e().deleteTexture(t)}})),eA}();if(null==s)throw Error("Failed to obtain WebGL2 context");if(!eb)throw Error("Could not initialize texture cache");s.activeTexture(s.TEXTURE0),eb.has(e)?r=eb.get(e):(r=s.createTexture(),s.bindTexture(s.TEXTURE_2D,r),s.texImage2D(s.TEXTURE_2D,0,s.RGBA,s.RGBA,s.UNSIGNED_BYTE,e),eb.set(e,r));let n=s.createFramebuffer();s.bindFramebuffer(s.FRAMEBUFFER,n),s.framebufferTexture2D(s.FRAMEBUFFER,s.COLOR_ATTACHMENT0,s.TEXTURE_2D,r,0),s.drawBuffers([s.COLOR_ATTACHMENT0]);let u=new Uint8Array(4);return s.readPixels(l,a,1,1,s.RGBA,s.UNSIGNED_BYTE,u),s.deleteFramebuffer(n),{r:u[0]/255,g:u[1]/255,b:u[2]/255,a:u[3]/255}}function eN(e){return["N","NNE","NE","ENE","E","ESE","SE","SSE","S","SSW","SW","WSW","W","WNW","NW","NNW"][Math.round((e+360)%360/22.5)%16]}function ek(e,t){let o=function(e,t){let o=eI(e,t);return[o[0]+.5,-1*o[1]+.5]}(e.lng,e.lat),r=2**t;return{z:t,x:o[0]*r,y:o[1]*r}}class eW{constructor(e,t){this.code=e,this.decode=t,this.decode.min=this.decode.min??0,this.decode.max=this.decode.max??1}codeValue(e){return(e-this.decode.min)/(this.decode.max-this.decode.min)}decodeValue(e){return this.decode.min+e*(this.decode.max-this.decode.min)}decodeChannel(e){if(this.decode.channel.length>1){let t=this.decodeValue(e[this.decode.channel[0]]),o=this.decodeValue(e[this.decode.channel[1]]);return[t,o,Math.sqrt(t*t+o*o)]}return this.decodeValue(e[this.decode.channel])}getBlendCode(e){let t=`${this.code} d.rgb=(d.rgb*d.a*(1.-c.a)+c.rgb*c.a)/(d.a*(1.-c.a)+c.a); d.a=d.a+c.a*(1.-d.a);`;return`vec4 ${e}(vec4 d,vec4 data){${t}return d;}`}getCode(e){return`vec4 ${e}(vec4 data){${this.code}return c;}`}}class eL extends eW{constructor(e){let t=e.decode,o=e.stops||[{value:t.min,color:[0,0,0,0]},{value:t.max,color:[255,255,255,255]}],r=e.smooth??!0,l=e.opacity??1,a=function({channel:e="r",min:t=0,max:o=1}){let r=`${t.toFixed(7)}+data.${e}*${(o-t).toFixed(7)}`;return e.length>1&&(r=`length(${r})`),`float v=${r};`}(t);super(a+=function(e,t,o={inputName:"v",outputName:"c"}){let r=function e(t,o=50){return t.length<=o?t:e(t.filter((e,o)=>0===o||o%2||o===t.length-1),o)}(e),l="";for(let e=0;e<r.length;e++){let a=r[e];0===e&&(l+=`vec4 ${o.outputName}=${eP(a.color)};
`);let s=a.value,n=eP(a.color);if(t&&e+1<r.length){let t=r[e+1].value,a=eP(r[e+1].color),u=0===Number.parseFloat((s-t).toFixed(7))?"0.":`(${o.inputName} - ${s.toFixed(7)}) / (${(t-s).toFixed(7)})`;l+=`${e>0?" else ":""} if(${o.inputName}>=${s.toFixed(7)}&&${o.inputName}<${t.toFixed(7)}) { ${o.outputName}=mix(${n},${a}, ${u} ); }
`}else l+=`if(${o.inputName}>=${s.toFixed(7)})${o.outputName}=${n};
`}return l}(o,r)+`c.a*=${l.toFixed(3)};`,t)}}let eG=class e extends Array{constructor(e={}){super(),el(this,"min",0),el(this,"max",1),"min"in e&&(this.min=e.min),"max"in e&&(this.max=e.max),"stops"in e&&this.setStops(e.stops,{clone:!1})}static fromArrayDefinition(t){return new e({stops:t.map(e=>({value:e[0],color:e[1]}))})}setStops(e,t={clone:!0}){let o=t.clone?this.clone():this;o.length=0;let r=Number.POSITIVE_INFINITY,l=Number.NEGATIVE_INFINITY;for(let t=0;t<e.length;t+=1)r=Math.min(r,e[t].value),l=Math.max(l,e[t].value),o.push({value:e[t].value,color:e[t].color.slice()});return o.sort((e,t)=>e.value<t.value?-1:1),this.min=r,this.max=l,o}scale(t,o,r={clone:!0}){let l=r.clone,a=this[0].value,s=this.at(-1).value-a,n=o-t,u=[];for(let e=0;e<this.length;e+=1){let o=(this[e].value-a)/s*n+t;l?u.push({value:o,color:this[e].color.slice()}):this[e].value=o}return l?new e({stops:u}):this}at(e){return e<0?this[this.length+e]:this[e]}clone(){return new e({stops:this.getRawColorStops()})}getRawColorStops(){let e=[];for(let t=0;t<this.length;t+=1)e.push(this[t]);return e}reverse(e={clone:!0}){let t=e.clone?this.clone():this;for(let e=0;e<~~(t.length/2);e+=1){let o=t[e].color;t[e].color=t.at(-(e+1)).color,t.at(-(e+1)).color=o}return t}getBounds(){return{min:this.min,max:this.max}}getColor(e,t={smooth:!0}){if(e<=this[0].value)return this[0].color;if(e>=this.at(-1).value)return this.at(-1).color;for(let o=0;o<this.length-1;o+=1){if(e>this[o+1].value)continue;let r=this[o].color;if(!t.smooth)return r.slice();let l=this[o].value,a=this[o+1].value,s=this[o+1].color,n=(a-e)/(a-l);return r.map((e,t)=>Math.round(e*n+s[t]*(1-n)))}return[0,0,0]}getCanvasStrip(e={horizontal:!0,size:512,smooth:!0}){let t=document.createElement("canvas");t.width=e.horizontal?e.size:1,t.height=e.horizontal?1:e.size;let o=t.getContext("2d");if(!o)throw Error("Canvs context is missing");let r=o.getImageData(0,0,t.width,t.height),l=r.data,a=e.size,s=this[0].value,n=(this.at(-1).value-s)/a;for(let t=0;t<a;t+=1){let o=this.getColor(s+t*n,{smooth:e.smooth});l[4*t]=o[0],l[4*t+1]=o[1],l[4*t+2]=o[2],l[4*t+3]=o.length>3?o[3]:255}return o.putImageData(r,0,0),t}};el(eG,"builtin",{NULL:new eG({stops:[{value:0,color:[0,0,0,0]},{value:1,color:[0,0,0,0]}]}),JET:new eG({stops:[{value:0,color:[0,0,131]},{value:.125,color:[0,60,170]},{value:.375,color:[5,255,255]},{value:.625,color:[255,255,0]},{value:.875,color:[250,0,0]},{value:1,color:[128,0,0]}]}),HSV:new eG({stops:[{value:0,color:[255,0,0]},{value:.169,color:[253,255,2]},{value:.173,color:[247,255,2]},{value:.337,color:[0,252,4]},{value:.341,color:[0,252,10]},{value:.506,color:[1,249,255]},{value:.671,color:[2,0,253]},{value:.675,color:[8,0,253]},{value:.839,color:[255,0,251]},{value:.843,color:[255,0,245]},{value:1,color:[255,0,6]}]}),HOT:new eG({stops:[{value:0,color:[0,0,0]},{value:.3,color:[230,0,0]},{value:.6,color:[255,210,0]},{value:1,color:[255,255,255]}]}),SPRING:new eG({stops:[{value:0,color:[255,0,255]},{value:1,color:[255,255,0]}]}),SUMMER:new eG({stops:[{value:0,color:[0,128,102]},{value:1,color:[255,255,102]}]}),AUTOMN:new eG({stops:[{value:0,color:[255,0,0]},{value:1,color:[255,255,0]}]}),WINTER:new eG({stops:[{value:0,color:[0,0,255]},{value:1,color:[0,255,128]}]}),BONE:new eG({stops:[{value:0,color:[0,0,0]},{value:.376,color:[84,84,116]},{value:.753,color:[169,200,200]},{value:1,color:[255,255,255]}]}),COPPER:new eG({stops:[{value:0,color:[0,0,0]},{value:.804,color:[255,160,102]},{value:1,color:[255,199,127]}]}),GREYS:new eG({stops:[{value:0,color:[0,0,0]},{value:1,color:[255,255,255]}]}),YIGNBU:new eG({stops:[{value:0,color:[8,29,88]},{value:.125,color:[37,52,148]},{value:.25,color:[34,94,168]},{value:.375,color:[29,145,192]},{value:.5,color:[65,182,196]},{value:.625,color:[127,205,187]},{value:.75,color:[199,233,180]},{value:.875,color:[237,248,217]},{value:1,color:[255,255,217]}]}),GREENS:new eG({stops:[{value:0,color:[0,68,27]},{value:.125,color:[0,109,44]},{value:.25,color:[35,139,69]},{value:.375,color:[65,171,93]},{value:.5,color:[116,196,118]},{value:.625,color:[161,217,155]},{value:.75,color:[199,233,192]},{value:.875,color:[229,245,224]},{value:1,color:[247,252,245]}]}),YIORRD:new eG({stops:[{value:0,color:[128,0,38]},{value:.125,color:[189,0,38]},{value:.25,color:[227,26,28]},{value:.375,color:[252,78,42]},{value:.5,color:[253,141,60]},{value:.625,color:[254,178,76]},{value:.75,color:[254,217,118]},{value:.875,color:[255,237,160]},{value:1,color:[255,255,204]}]}),BLUERED:new eG({stops:[{value:0,color:[0,0,255]},{value:1,color:[255,0,0]}]}),RDBU:new eG({stops:[{value:0,color:[5,10,172]},{value:.35,color:[106,137,247]},{value:.5,color:[190,190,190]},{value:.6,color:[220,170,132]},{value:.7,color:[230,145,90]},{value:1,color:[178,10,28]}]}),PICNIC:new eG({stops:[{value:0,color:[0,0,255]},{value:.1,color:[51,153,255]},{value:.2,color:[102,204,255]},{value:.3,color:[153,204,255]},{value:.4,color:[204,204,255]},{value:.5,color:[255,255,255]},{value:.6,color:[255,204,255]},{value:.7,color:[255,153,255]},{value:.8,color:[255,102,204]},{value:.9,color:[255,102,102]},{value:1,color:[255,0,0]}]}),RAINBOW:new eG({stops:[{value:0,color:[150,0,90]},{value:.125,color:[0,0,200]},{value:.25,color:[0,25,255]},{value:.375,color:[0,152,255]},{value:.5,color:[44,255,150]},{value:.625,color:[151,255,0]},{value:.75,color:[255,234,0]},{value:.875,color:[255,111,0]},{value:1,color:[255,0,0]}]}),PORTLAND:new eG({stops:[{value:0,color:[12,51,131]},{value:.25,color:[10,136,186]},{value:.5,color:[242,211,56]},{value:.75,color:[242,143,56]},{value:1,color:[217,30,30]}]}),BLACKBODY:new eG({stops:[{value:0,color:[0,0,0]},{value:.2,color:[230,0,0]},{value:.4,color:[230,210,0]},{value:.7,color:[255,255,255]},{value:1,color:[160,200,255]}]}),EARTH:new eG({stops:[{value:0,color:[0,0,130]},{value:.1,color:[0,180,180]},{value:.2,color:[40,210,40]},{value:.4,color:[230,230,50]},{value:.6,color:[120,70,20]},{value:1,color:[255,255,255]}]}),ELECTRIC:new eG({stops:[{value:0,color:[0,0,0]},{value:.15,color:[30,0,100]},{value:.4,color:[120,0,100]},{value:.6,color:[160,90,0]},{value:.8,color:[230,200,0]},{value:1,color:[255,250,220]}]}),VIRIDIS:new eG({stops:[{value:0,color:[68,1,84]},{value:.13,color:[71,44,122]},{value:.25,color:[59,81,139]},{value:.38,color:[44,113,142]},{value:.5,color:[33,144,141]},{value:.63,color:[39,173,129]},{value:.75,color:[92,200,99]},{value:.88,color:[170,220,50]},{value:1,color:[253,231,37]}]}),INFERNO:new eG({stops:[{value:0,color:[0,0,4]},{value:.13,color:[31,12,72]},{value:.25,color:[85,15,109]},{value:.38,color:[136,34,106]},{value:.5,color:[186,54,85]},{value:.63,color:[227,89,51]},{value:.75,color:[249,140,10]},{value:.88,color:[249,201,50]},{value:1,color:[252,255,164]}]}),MAGMA:new eG({stops:[{value:0,color:[0,0,4]},{value:.13,color:[28,16,68]},{value:.25,color:[79,18,123]},{value:.38,color:[129,37,129]},{value:.5,color:[181,54,122]},{value:.63,color:[229,80,100]},{value:.75,color:[251,135,97]},{value:.88,color:[254,194,135]},{value:1,color:[252,253,191]}]}),PLASMA:new eG({stops:[{value:0,color:[13,8,135]},{value:.13,color:[75,3,161]},{value:.25,color:[125,3,168]},{value:.38,color:[168,34,150]},{value:.5,color:[203,70,121]},{value:.63,color:[229,107,93]},{value:.75,color:[248,148,65]},{value:.88,color:[253,195,40]},{value:1,color:[240,249,33]}]}),WARM:new eG({stops:[{value:0,color:[125,0,179]},{value:.13,color:[172,0,187]},{value:.25,color:[219,0,170]},{value:.38,color:[255,0,130]},{value:.5,color:[255,63,74]},{value:.63,color:[255,123,0]},{value:.75,color:[234,176,0]},{value:.88,color:[190,228,0]},{value:1,color:[147,255,0]}]}),COOL:new eG({stops:[{value:0,color:[125,0,179]},{value:.13,color:[116,0,218]},{value:.25,color:[98,74,237]},{value:.38,color:[68,146,231]},{value:.5,color:[0,204,197]},{value:.63,color:[0,247,146]},{value:.75,color:[0,255,88]},{value:.88,color:[40,255,8]},{value:1,color:[147,255,0]}]}),RAINBOW_SOFT:new eG({stops:[{value:0,color:[125,0,179]},{value:.1,color:[199,0,180]},{value:.2,color:[255,0,121]},{value:.3,color:[255,108,0]},{value:.4,color:[222,194,0]},{value:.5,color:[150,255,0]},{value:.6,color:[0,255,55]},{value:.7,color:[0,246,150]},{value:.8,color:[50,167,222]},{value:.9,color:[103,51,235]},{value:1,color:[124,0,186]}]}),BATHYMETRY:new eG({stops:[{value:0,color:[40,26,44]},{value:.13,color:[59,49,90]},{value:.25,color:[64,76,139]},{value:.38,color:[63,110,151]},{value:.5,color:[72,142,158]},{value:.63,color:[85,174,163]},{value:.75,color:[120,206,163]},{value:.88,color:[187,230,172]},{value:1,color:[253,254,204]}]}),CDOM:new eG({stops:[{value:0,color:[47,15,62]},{value:.13,color:[87,23,86]},{value:.25,color:[130,28,99]},{value:.38,color:[171,41,96]},{value:.5,color:[206,67,86]},{value:.63,color:[230,106,84]},{value:.75,color:[242,149,103]},{value:.88,color:[249,193,135]},{value:1,color:[254,237,176]}]}),CHLOROPHYLL:new eG({stops:[{value:0,color:[18,36,20]},{value:.13,color:[25,63,41]},{value:.25,color:[24,91,59]},{value:.38,color:[13,119,72]},{value:.5,color:[18,148,80]},{value:.63,color:[80,173,89]},{value:.75,color:[132,196,122]},{value:.88,color:[175,221,162]},{value:1,color:[215,249,208]}]}),DENSITY:new eG({stops:[{value:0,color:[54,14,36]},{value:.13,color:[89,23,80]},{value:.25,color:[110,45,132]},{value:.38,color:[120,77,178]},{value:.5,color:[120,113,213]},{value:.63,color:[115,151,228]},{value:.75,color:[134,185,227]},{value:.88,color:[177,214,227]},{value:1,color:[230,241,241]}]}),FREESURFACE_BLUE:new eG({stops:[{value:0,color:[30,4,110]},{value:.13,color:[47,14,176]},{value:.25,color:[41,45,236]},{value:.38,color:[25,99,212]},{value:.5,color:[68,131,200]},{value:.63,color:[114,156,197]},{value:.75,color:[157,181,203]},{value:.88,color:[200,208,216]},{value:1,color:[241,237,236]}]}),FREESURFACE_RED:new eG({stops:[{value:0,color:[60,9,18]},{value:.13,color:[100,17,27]},{value:.25,color:[142,20,29]},{value:.38,color:[177,43,27]},{value:.5,color:[192,87,63]},{value:.63,color:[205,125,105]},{value:.75,color:[216,162,148]},{value:.88,color:[227,199,193]},{value:1,color:[241,237,236]}]}),OXYGEN:new eG({stops:[{value:0,color:[64,5,5]},{value:.13,color:[106,6,15]},{value:.25,color:[144,26,7]},{value:.38,color:[168,64,3]},{value:.5,color:[188,100,4]},{value:.63,color:[206,136,11]},{value:.75,color:[220,174,25]},{value:.88,color:[231,215,44]},{value:1,color:[248,254,105]}]}),PAR:new eG({stops:[{value:0,color:[51,20,24]},{value:.13,color:[90,32,35]},{value:.25,color:[129,44,34]},{value:.38,color:[159,68,25]},{value:.5,color:[182,99,19]},{value:.63,color:[199,134,22]},{value:.75,color:[212,171,35]},{value:.88,color:[221,210,54]},{value:1,color:[225,253,75]}]}),PHASE:new eG({stops:[{value:0,color:[145,105,18]},{value:.13,color:[184,71,38]},{value:.25,color:[186,58,115]},{value:.38,color:[160,71,185]},{value:.5,color:[110,97,218]},{value:.63,color:[50,123,164]},{value:.75,color:[31,131,110]},{value:.88,color:[77,129,34]},{value:1,color:[145,105,18]}]}),SALINITY:new eG({stops:[{value:0,color:[42,24,108]},{value:.13,color:[33,50,162]},{value:.25,color:[15,90,145]},{value:.38,color:[40,118,137]},{value:.5,color:[59,146,135]},{value:.63,color:[79,175,126]},{value:.75,color:[120,203,104]},{value:.88,color:[193,221,100]},{value:1,color:[253,239,154]}]}),TEMPERATURE:new eG({stops:[{value:0,color:[4,35,51]},{value:.13,color:[23,51,122]},{value:.25,color:[85,59,157]},{value:.38,color:[129,79,143]},{value:.5,color:[175,95,130]},{value:.63,color:[222,112,101]},{value:.75,color:[249,146,66]},{value:.88,color:[249,196,65]},{value:1,color:[232,250,91]}]}),TURBIDITY:new eG({stops:[{value:0,color:[34,31,27]},{value:.13,color:[65,50,41]},{value:.25,color:[98,69,52]},{value:.38,color:[131,89,57]},{value:.5,color:[161,112,59]},{value:.63,color:[185,140,66]},{value:.75,color:[202,174,88]},{value:.88,color:[216,209,126]},{value:1,color:[233,246,171]}]}),VELOCITY_BLUE:new eG({stops:[{value:0,color:[17,32,64]},{value:.13,color:[35,52,116]},{value:.25,color:[29,81,156]},{value:.38,color:[31,113,162]},{value:.5,color:[50,144,169]},{value:.63,color:[87,173,176]},{value:.75,color:[149,196,189]},{value:.88,color:[203,221,211]},{value:1,color:[254,251,230]}]}),VELOCITY_GREEN:new eG({stops:[{value:0,color:[23,35,19]},{value:.13,color:[24,64,38]},{value:.25,color:[11,95,45]},{value:.38,color:[39,123,35]},{value:.5,color:[95,146,12]},{value:.63,color:[152,165,18]},{value:.75,color:[201,186,69]},{value:.88,color:[233,216,137]},{value:1,color:[255,253,205]}]}),CUBEHELIX:new eG({stops:[{value:0,color:[0,0,0]},{value:.07,color:[22,5,59]},{value:.13,color:[60,4,105]},{value:.2,color:[109,1,135]},{value:.27,color:[161,0,147]},{value:.33,color:[210,2,142]},{value:.4,color:[251,11,123]},{value:.47,color:[255,29,97]},{value:.53,color:[255,54,69]},{value:.6,color:[255,85,46]},{value:.67,color:[255,120,34]},{value:.73,color:[255,157,37]},{value:.8,color:[241,191,57]},{value:.87,color:[224,220,93]},{value:.93,color:[218,241,142]},{value:1,color:[227,253,198]}]}),CIVIDIS:new eG({stops:[{value:0,color:[0,32,77,255]},{value:.125,color:[5,54,110,255]},{value:.25,color:[65,77,108,255]},{value:.375,color:[97,100,111,255]},{value:.5,color:[125,124,121,255]},{value:.625,color:[156,149,120,255]},{value:.75,color:[190,175,111,255]},{value:.875,color:[225,204,94,255]},{value:1,color:[255,235,70,255]}]}),TURBO:new eG({stops:[{value:0,color:[48,18,59,255]},{value:.125,color:[70,107,227,255]},{value:.25,color:[40,187,236,255]},{value:.375,color:[49,242,153,255]},{value:.5,color:[162,252,60,255]},{value:.625,color:[237,208,58,255]},{value:.75,color:[251,128,34,255]},{value:.875,color:[210,49,5,255]},{value:1,color:[122,4,3,255]}]}),ROCKET:new eG({stops:[{value:0,color:[250,235,221,0]},{value:.133,color:[250,235,221,255]},{value:.266,color:[246,170,130,255]},{value:.4,color:[240,96,67,255]},{value:.533,color:[203,27,79,255]},{value:.666,color:[132,30,90,255]},{value:.8,color:[63,27,68,255]},{value:1,color:[3,5,26,255]}]}),MAKO:new eG({stops:[{value:0,color:[11,4,5,255]},{value:.125,color:[43,28,53,255]},{value:.25,color:[62,53,107,255]},{value:.375,color:[59,86,152,255]},{value:.5,color:[53,123,162,255]},{value:.625,color:[53,158,170,255]},{value:.75,color:[73,193,173,255]},{value:.875,color:[150,221,181,255]},{value:1,color:[222,245,229,255]}]}),TERRAIN:new eG({stops:[{value:-10001,color:[0,20,60]},{value:-5e3,color:[0,10,30]},{value:-1e3,color:[0,30,80]},{value:-100,color:[0,38,115]},{value:0,color:[122,200,255]},{value:10,color:[51,102,0]},{value:500,color:[129,195,31]},{value:800,color:[255,255,204]},{value:1200,color:[244,189,69]},{value:2e3,color:[132,75,0]},{value:3e3,color:[102,51,12]},{value:8e3,color:[255,255,255]}]}),PRESSURE:new eG({stops:[{value:900,color:[0,0,100,250]},{value:950,color:[0,0,255,250]},{value:980,color:[0,0,255,120]},{value:1e3,color:[255,255,255,0]},{value:1020,color:[255,0,0,120]},{value:1080,color:[255,0,0,250]}]}),PRESSURE_2:new eG({stops:[{value:900,color:[8,16,48,255]},{value:950,color:[0,32,96,255]},{value:976,color:[0,52,146,255]},{value:986,color:[0,90,148,255]},{value:995,color:[0,117,146,255]},{value:1002,color:[26,140,147,255]},{value:1007,color:[103,162,155,255]},{value:1011,color:[155,183,172,255]},{value:1013,color:[182,182,182,255]},{value:1015,color:[176,174,152,255]},{value:1019,color:[167,147,107,255]},{value:1024,color:[163,116,67,255]},{value:1030,color:[159,81,44,255]},{value:1038,color:[142,47,57,255]},{value:1046,color:[111,24,64,255]},{value:1080,color:[48,8,24,255]}]}),PRESSURE_3:new eG({stops:[{value:900,color:[40,46,117,255]},{value:950,color:[40,51,121,255]},{value:976,color:[42,63,125,255]},{value:986,color:[55,97,141,255]},{value:995,color:[69,115,150,255]},{value:1002,color:[87,144,168,255]},{value:1007,color:[125,184,194,255]},{value:1011,color:[195,226,226,255]},{value:1013,color:[245,220,196,255]},{value:1015,color:[232,191,160,255]},{value:1019,color:[219,154,125,255]},{value:1024,color:[210,115,98,255]},{value:1030,color:[194,72,67,255]},{value:1038,color:[199,61,60,255]},{value:1046,color:[177,50,54,255]},{value:1080,color:[159,42,48,255]}]}),PRESSURE_4:new eG({stops:[{value:900,color:[40,46,117,255]},{value:950,color:[40,51,121,255]},{value:976,color:[42,63,125,255]},{value:986,color:[55,97,141,255]},{value:995,color:[69,115,150,255]},{value:1002,color:[87,144,168,255]},{value:1007,color:[125,184,194,255]},{value:1012.5,color:[226,226,226,255]},{value:1013.5,color:[226,226,226,255]},{value:1015,color:[232,191,160,255]},{value:1019,color:[219,154,125,255]},{value:1024,color:[210,115,98,255]},{value:1030,color:[194,72,67,255]},{value:1038,color:[199,61,60,255]},{value:1046,color:[177,50,54,255]},{value:1080,color:[159,42,48,255]}]}),TEMPERATURE_2:new eG({stops:[{value:-70.15,color:[115,70,105,255]},{value:-55.15,color:[202,172,195,255]},{value:-40.15,color:[162,70,145,255]},{value:-25.15,color:[143,89,169,255]},{value:-15.15,color:[157,219,217,255]},{value:-8.15,color:[106,191,181,255]},{value:-4.15,color:[100,166,189,255]},{value:0,color:[93,133,198,255]},{value:.85,color:[68,125,99,255]},{value:9.85,color:[128,147,24,255]},{value:20.85,color:[243,183,4,255]},{value:29.85,color:[232,83,25,255]},{value:46.85,color:[71,14,0,255]}]}),TEMPERATURE_3:new eG({stops:[{value:-65,color:[3,78,77,255]},{value:-55,color:[4,98,96,255]},{value:-40,color:[5,122,120,255]},{value:-30,color:[6,152,149,255]},{value:-20,color:[8,201,198,255]},{value:-15,color:[20,245,241,255]},{value:-10,color:[108,237,249,255]},{value:-5,color:[133,205,250,255]},{value:0,color:[186,227,252,255]},{value:5,color:[238,221,145,255]},{value:10,color:[232,183,105,255]},{value:15,color:[232,137,69,255]},{value:20,color:[231,107,24,255]},{value:25,color:[236,84,19,255]},{value:30,color:[236,44,19,255]},{value:40,color:[123,23,10,255]},{value:55,color:[91,11,0,255]}]}),PRECIPITATION:new eG({stops:[{value:0,color:[111,111,111,0]},{value:.6,color:[60,116,160,180]},{value:6,color:[59,161,161,255]},{value:8,color:[59,161,61,255]},{value:10,color:[130,161,59,255]},{value:15,color:[161,161,59,255]},{value:20,color:[161,59,59,255]},{value:31,color:[161,59,161,255]},{value:50,color:[168,168,168,255]}]}),PRECIPITATION_2:new eG({stops:[{value:0,color:[255,255,255,0]},{value:.1,color:[171,218,252,255]},{value:1,color:[98,186,249,255]},{value:2,color:[87,160,240,255]},{value:4,color:[112,128,250,255]},{value:6,color:[128,102,245,255]},{value:8,color:[152,102,245,255]},{value:10,color:[154,87,172,255]},{value:15,color:[228,88,126,255]},{value:20,color:[247,135,95,255]},{value:30,color:[249,206,64,255]},{value:50,color:[250,248,168,52]}]}),RADAR:new eG({stops:[{value:0,color:[7,235,236,0]},{value:4,color:[7,235,236,80]},{value:5,color:[7,235,236,255]},{value:10,color:[0,159,246,255]},{value:15,color:[0,0,247,255]},{value:20,color:[3,255,0,255]},{value:25,color:[0,200,2,255]},{value:30,color:[1,144,0,255]},{value:35,color:[255,255,0,255]},{value:40,color:[231,192,0,255]},{value:45,color:[255,145,3,255]},{value:50,color:[255,0,0,255]},{value:55,color:[215,0,0,255]},{value:60,color:[192,0,0,255]},{value:65,color:[255,0,255,255]},{value:70,color:[155,85,200,255]},{value:75,color:[235,235,235,255]}]}),RADAR_CLOUD:new eG({stops:[{value:4,color:[134,134,176,0]},{value:7,color:[134,134,176,30]},{value:10,color:[134,134,176,60]},{value:20,color:[110,110,145,80]},{value:35,color:[77,77,105,140]},{value:45,color:[58,58,87,180]},{value:60,color:[26,26,51,220]}]}),PRESSURE_CIVIDIS:new eG({stops:[{value:900,color:[0,32,77,255]},{value:950,color:[0,37,82,255]},{value:976,color:[15,56,110,255]},{value:986,color:[49,68,107,255]},{value:995,color:[70,80,107,255]},{value:1002,color:[87,92,109,255]},{value:1007,color:[102,105,112,255]},{value:1011,color:[117,117,117,255]},{value:1013,color:[132,130,121,255]},{value:1015,color:[149,143,120,255]},{value:1019,color:[166,157,117,255]},{value:1024,color:[184,171,112,255]},{value:1030,color:[203,186,105,255]},{value:1038,color:[221,201,95,255]},{value:1046,color:[250,229,65,255]},{value:1080,color:[255,234,70,255]}]}),RADAR_ROCKET:new eG({stops:[{value:0,color:[250,235,221,0]},{value:.1,color:[250,235,221,255]},{value:10,color:[246,180,142,255]},{value:20,color:[243,118,81,255]},{value:30,color:[225,51,66,255]},{value:40,color:[174,23,89,255]},{value:50,color:[112,31,87,255]},{value:60,color:[54,25,62,255]},{value:75,color:[3,5,26,255]}]}),WIND_ROCKET:new eG({stops:[{value:0,color:[250,235,221,255]},{value:1,color:[246,187,151,255]},{value:3,color:[244,135,94,255]},{value:5,color:[236,75,62,255]},{value:10,color:[203,27,79,255]},{value:15,color:[150,28,91,255]},{value:20,color:[97,31,83,255]},{value:25,color:[48,23,58,255]},{value:30,color:[3,5,26,255]}]}),MAKO_PRECIPITATION:new eG({stops:[{value:0,color:[255,255,255,0]},{value:.1,color:[222,245,229,255]},{value:1,color:[168,225,188,255]},{value:2,color:[96,206,172,255]},{value:4,color:[61,180,173,255]},{value:6,color:[52,151,169,255]},{value:8,color:[53,123,162,255]},{value:10,color:[57,93,156,255]},{value:15,color:[65,64,129,255]},{value:20,color:[56,42,84,255]},{value:30,color:[38,23,42,255]},{value:50,color:[11,4,5,255]}]}),TEMPERATURE_TURBO:new eG({stops:[{value:-65,color:[48,18,59,255]},{value:-55,color:[64,64,162,255]},{value:-40,color:[70,107,227,255]},{value:-30,color:[66,147,255,255]},{value:-20,color:[40,187,236,255]},{value:-15,color:[24,220,195,255]},{value:-10,color:[49,242,153,255]},{value:-5,color:[107,254,100,255]},{value:0,color:[162,252,60,255]},{value:5,color:[204,237,52,255]},{value:10,color:[237,208,58,255]},{value:15,color:[253,173,53,255]},{value:20,color:[231,107,24,255]},{value:25,color:[236,82,15,255]},{value:30,color:[210,49,5,255]},{value:40,color:[172,23,1,255]},{value:55,color:[122,4,3,255]}]})});let eU=eG,e$="api.maptiler.com",eB={maptilerApiHost:e$,endpoint:`https://${e$}/weather/latest.json`,tileSourceUrlSchema:`https://${e$}/tiles/{tileset_id}/{zxy}.{format}`};Object.freeze(eB);let eZ={latest:null};class ej{static async fetchLatest(e,t){let o=`${eB.endpoint}?key=${e}&mtsid=${t}`,r=await fetch(o,{cache:"no-store"});eZ.latest=await r.json()}static getDataForWeatherVariableID(e){if(!eZ.latest)return null;let t=eZ.latest.variables.filter(t=>t.metadata.weather_variable.variable_id===e);return t.length?t[0]:null}static hasData(){return!!eZ.latest}static getTemperatureData(){return ej.getDataForWeatherVariableID("temperature-2m:gfs")}static getPressureData(){return ej.getDataForWeatherVariableID("pressure-msl:gfs")}static getPrecipitationData(){return ej.getDataForWeatherVariableID("precipitation-1h:gfs")}static getCloudCoverData(){return ej.getDataForWeatherVariableID("cloud_cover-total:gfs")}static getFrozenPrecipitationData(){return ej.getDataForWeatherVariableID("frozen_precipitation-1h:gfs")}static getRadarData(){return ej.getDataForWeatherVariableID("radar-composite:gfs")}static getWindData(){return ej.getDataForWeatherVariableID("wind-10m:gfs")}static getSourcesAndTimestamps(e,t,o){return e.keyframes.map(r=>({source:`${eB.tileSourceUrlSchema.replace("{tileset_id}",r.id).replace("{format}",e.tile_format)}?key=${t}&mtsid=${o}`,timestamp:+new Date(r.timestamp)/1e3}))}}class eY{constructor(){el(this,"allInstances",[]),el(this,"availableInstances",[])}add(e,t=!1){return this.allInstances.push(e),t&&this.availableInstances.push(e),e}isEmpty(){return 0===this.availableInstances.length}pop(){if(this.isEmpty())throw Error("No more instances available ini the pool.");return this.availableInstances.pop()}init(){this.availableInstances=this.allInstances.slice()}reset(){this.availableInstances=[],this.allInstances=[]}}let eX={UNKNOWN:0,INTERSECTING:1,ABOVE:2,RIGHT:4,BELOW:8,LEFT:16};function eV(e,t){let o=e[0],r=e[1],l=e[2],a=e[3],s=t[0],n=t[1],u=eX.UNKNOWN;return s<o?u|=eX.LEFT:s>l&&(u|=eX.RIGHT),n<r?u|=eX.BELOW:n>a&&(u|=eX.ABOVE),u===eX.UNKNOWN&&(u=eX.INTERSECTING),u}function eH(){return[1/0,1/0,-1/0,-1/0]}function eK(e,t,o,r,l){return l?(l[0]=e,l[1]=t,l[2]=o,l[3]=r,l):[e,t,o,r]}function eq(e){let t=0;return e[2]<e[0]||e[3]<e[1]||(t=(e[2]-e[0])*(e[3]-e[1])),t}function eJ(e,t,o){let r=o||eH();return eQ(e,t)?(e[0]>t[0]?r[0]=e[0]:r[0]=t[0],e[1]>t[1]?r[1]=e[1]:r[1]=t[1],e[2]<t[2]?r[2]=e[2]:r[2]=t[2],e[3]<t[3]?r[3]=e[3]:r[3]=t[3]):eK(1/0,1/0,-1/0,-1/0,r),r}function eQ(e,t){return e[0]<=t[2]&&e[2]>=t[0]&&e[1]<=t[3]&&e[3]>=t[1]}class e1{constructor(e,t,o,r){this.minX=e,this.maxX=t,this.minY=o,this.maxY=r}contains(e){return this.containsXY(e[1],e[2])}containsTileRange(e){return this.minX<=e.minX&&e.maxX<=this.maxX&&this.minY<=e.minY&&e.maxY<=this.maxY}containsXY(e,t){return this.minX<=e&&e<=this.maxX&&this.minY<=t&&t<=this.maxY}equals(e){return this.minX==e.minX&&this.minY==e.minY&&this.maxX==e.maxX&&this.maxY==e.maxY}extend(e){e.minX<this.minX&&(this.minX=e.minX),e.maxX>this.maxX&&(this.maxX=e.maxX),e.minY<this.minY&&(this.minY=e.minY),e.maxY>this.maxY&&(this.maxY=e.maxY)}getHeight(){return this.maxY-this.minY+1}getSize(){return[this.getWidth(),this.getHeight()]}getWidth(){return this.maxX-this.minX+1}intersects(e){return this.minX<=e.maxX&&this.maxX>=e.minX&&this.minY<=e.maxY&&this.maxY>=e.minY}}function e0(e,t,o,r,l){return void 0!==l?(l.minX=e,l.maxX=t,l.minY=o,l.maxY=r,l):new e1(e,t,o,r)}function e2(e,t){if(!e)throw Error(t)}function e5(e,t){let o=Math.pow(10,t);return Math.round(e*o)/o}function e3(e,t){return Math.floor(e5(e,t))}function e4(e,t){return Math.ceil(e5(e,t))}function e6(e,t,o,r){return void 0!==r?(r[0]=e,r[1]=t,r[2]=o,r):[e,t,o]}function e8(e,t,o,r,l,a){let s=0,n=e[o-r],u=e[o-r+1];for(;t<o;t+=r){let o=e[t],r=e[t+1];u<=a?r>a&&(o-n)*(a-u)-(l-n)*(r-u)>0&&s++:r<=a&&(o-n)*(a-u)-(l-n)*(r-u)<0&&s--,n=o,u=r}return 0!==s}function e7(e,t){return e>t?1:e<t?-1:0}function e9(e,t){return Array.isArray(e)?e:(void 0===t?t=[e,e]:(t[0]=e,t[1]=e),t)}let te=[0,0,0];class tt{constructor(e){let t;if(this.minZoom=void 0!==e.minZoom?e.minZoom:0,this.resolutions_=e.resolutions,e2(function(e,t,o){let r=t||e7;return e.every(function(t,o){if(0===o)return!0;let l=r(e[o-1],t);return!(l>0||0===l)})}(this.resolutions_,(e,t)=>t-e),"`resolutions` must be sorted in descending order"),!e.origins)for(let e=0,o=this.resolutions_.length-1;e<o;++e)if(t){if(this.resolutions_[e]/this.resolutions_[e+1]!==t){t=void 0;break}}else t=this.resolutions_[e]/this.resolutions_[e+1];this.zoomFactor_=t,this.maxZoom=this.resolutions_.length-1,this.origin_=void 0!==e.origin?e.origin:null,this.origins_=null,void 0!==e.origins&&(this.origins_=e.origins,e2(this.origins_.length==this.resolutions_.length,"Number of `origins` and `resolutions` must be equal"));let o=e.extent;void 0===o||this.origin_||this.origins_||(this.origin_=[o[0],o[3]]),e2(!this.origin_&&this.origins_||this.origin_&&!this.origins_,"Either `origin` or `origins` must be configured, never both"),this.tileSizes_=null,void 0!==e.tileSizes&&(this.tileSizes_=e.tileSizes,e2(this.tileSizes_.length==this.resolutions_.length,"Number of `tileSizes` and `resolutions` must be equal")),this.tileSize_=void 0!==e.tileSize?e.tileSize:this.tileSizes_?null:256,e2(!this.tileSize_&&this.tileSizes_||this.tileSize_&&!this.tileSizes_,"Either `tileSize` or `tileSizes` must be configured, never both"),this.extent_=void 0!==o?o:null,this.fullTileRanges_=null,this.tmpSize_=[0,0],this.tmpExtent_=[0,0,0,0],void 0!==e.sizes?this.fullTileRanges_=e.sizes.map((e,t)=>{let r=new e1(Math.min(0,e[0]),Math.max(e[0]-1,-1),Math.min(0,e[1]),Math.max(e[1]-1,-1));if(o){let e=this.getTileRangeForExtentAndZ(o,t);r.minX=Math.max(e.minX,r.minX),r.maxX=Math.min(e.maxX,r.maxX),r.minY=Math.max(e.minY,r.minY),r.maxY=Math.min(e.maxY,r.maxY)}return r}):o&&this.calculateTileRanges_(o)}forEachTileCoord(e,t,o){let r=this.getTileRangeForExtentAndZ(e,t);for(let e=r.minX,l=r.maxX;e<=l;++e)for(let l=r.minY,a=r.maxY;l<=a;++l)o([t,e,l])}forEachTileCoordParentTileRange(e,t,o,r){let l,a,s,n=null,u=e[0]-1;for(2===this.zoomFactor_?(a=e[1],s=e[2]):n=this.getTileCoordExtent(e,r);u>=this.minZoom;){if(l=void 0!==a&&void 0!==s?e0(a=Math.floor(a/2),a,s=Math.floor(s/2),s,o):this.getTileRangeForExtentAndZ(n,u,o),t(u,l))return!0;--u}return!1}getExtent(){return this.extent_}getMaxZoom(){return this.maxZoom}getMinZoom(){return this.minZoom}getOrigin(e){return this.origin_?this.origin_:this.origins_[e]}getResolution(e){return this.resolutions_[e]}getResolutions(){return this.resolutions_}getTileCoordChildTileRange(e,t,o){if(e[0]<this.maxZoom){if(2===this.zoomFactor_){let o=2*e[1],r=2*e[2];return e0(o,o+1,r,r+1,t)}let r=this.getTileCoordExtent(e,o||this.tmpExtent_);return this.getTileRangeForExtentAndZ(r,e[0]+1,t)}return null}getTileRangeForTileCoordAndZ(e,t,o){if(t>this.maxZoom||t<this.minZoom)return null;let r=e[0],l=e[1],a=e[2];if(t===r)return e0(l,a,l,a,o);if(this.zoomFactor_){let e=Math.pow(this.zoomFactor_,t-r),s=Math.floor(l*e),n=Math.floor(a*e);return t<r?e0(s,s,n,n,o):e0(s,Math.floor(e*(l+1))-1,n,Math.floor(e*(a+1))-1,o)}let s=this.getTileCoordExtent(e,this.tmpExtent_);return this.getTileRangeForExtentAndZ(s,t,o)}getTileRangeForExtentAndZ(e,t,o){this.getTileCoordForXYAndZ_(e[0],e[3],t,!1,te);let r=te[1],l=te[2];return this.getTileCoordForXYAndZ_(e[2],e[1],t,!0,te),e0(r,te[1],l,te[2],o)}getTileCoordCenter(e){let t=this.getOrigin(e[0]),o=this.getResolution(e[0]),r=e9(this.getTileSize(e[0]),this.tmpSize_);return[t[0]+(e[1]+.5)*r[0]*o,t[1]-(e[2]+.5)*r[1]*o]}getTileCoordExtent(e,t){let o=this.getOrigin(e[0]),r=this.getResolution(e[0]),l=e9(this.getTileSize(e[0]),this.tmpSize_),a=o[0]+e[1]*l[0]*r,s=o[1]-(e[2]+1)*l[1]*r,n=a+l[0]*r,u=s+l[1]*r;return eK(a,s,n,u,t)}getTileCoordForCoordAndResolution(e,t,o){return this.getTileCoordForXYAndResolution_(e[0],e[1],t,!1,o)}getTileCoordForXYAndResolution_(e,t,o,r,l){let a=this.getZForResolution(o),s=o/this.getResolution(a),n=this.getOrigin(a),u=e9(this.getTileSize(a),this.tmpSize_),h=s*(e-n[0])/o/u[0],c=s*(n[1]-t)/o/u[1];return r?(h=e4(h,5)-1,c=e4(c,5)-1):(h=e3(h,5),c=e3(c,5)),e6(a,h,c,l)}getTileCoordForXYAndZ_(e,t,o,r,l){let a=this.getOrigin(o),s=this.getResolution(o),n=e9(this.getTileSize(o),this.tmpSize_),u=(e-a[0])/s/n[0],h=(a[1]-t)/s/n[1];return r?(u=e4(u,5)-1,h=e4(h,5)-1):(u=e3(u,5),h=e3(h,5)),e6(o,u,h,l)}getTileCoordForCoordAndZ(e,t,o){return this.getTileCoordForXYAndZ_(e[0],e[1],t,!1,o)}getTileCoordResolution(e){return this.resolutions_[e[0]]}getTileSize(e){return this.tileSize_?this.tileSize_:this.tileSizes_[e]}getFullTileRange(e){return this.fullTileRanges_?this.fullTileRanges_[e]:this.extent_?this.getTileRangeForExtentAndZ(this.extent_,e):null}getZForResolution(e,t){return Math.min(Math.max(function(e,t,o){if(e[0]<=t)return 0;let r=e.length;if(t<=e[r-1])return r-1;if("function"==typeof o){for(let l=1;l<r;++l){let r=e[l];if(r===t)return l;if(r<t)return o(t,e[l-1],r)>0?l-1:l}return r-1}if(o>0){for(let o=1;o<r;++o)if(e[o]<t)return o-1;return r-1}if(o<0){for(let o=1;o<r;++o)if(e[o]<=t)return o;return r-1}for(let o=1;o<r;++o){if(e[o]==t)return o;if(e[o]<t)return e[o-1]-t<t-e[o]?o-1:o}return r-1}(this.resolutions_,e,t||0),this.minZoom),this.maxZoom)}tileCoordIntersectsViewport(e,t){var o,r;return o=t.length,!!(function(e,t,o,r,l){let a=function(e,t,o,r,l){for(;o<r;o+=2){var a,s;a=t[o],s=t[o+1],e[0]=Math.min(e[0],a),e[1]=Math.min(e[1],s),e[2]=Math.max(e[2],a),e[3]=Math.max(e[3],s)}return e}(eH(),e,0,o,2);return!!eQ(l,a)&&(l[0]<=a[0]&&a[2]<=l[2]&&l[1]<=a[1]&&a[3]<=l[3]||a[0]>=l[0]&&a[2]<=l[2]||a[1]>=l[1]&&a[3]<=l[3]||function(e,t,o,r,l){let a;for(t+=r;t<o;t+=r)if(a=l(e.slice(t-r,t),e.slice(t,t+r)))return a;return!1}(e,0,o,2,function(e,t){return function(e,t,o){let r=!1,l=eV(e,t),a=eV(e,o);if(l===eX.INTERSECTING||a===eX.INTERSECTING)r=!0;else{let s,n;let u=e[0],h=e[1],c=e[2],m=e[3],d=t[0],v=t[1],f=o[0],p=o[1],g=(p-v)/(f-d);a&eX.ABOVE&&!(l&eX.ABOVE)&&(r=(s=f-(p-m)/g)>=u&&s<=c),r||!(a&eX.RIGHT)||l&eX.RIGHT||(r=(n=p-(f-c)*g)>=h&&n<=m),r||!(a&eX.BELOW)||l&eX.BELOW||(r=(s=f-(p-h)/g)>=u&&s<=c),r||!(a&eX.LEFT)||l&eX.LEFT||(r=(n=p-(f-u)*g)>=h&&n<=m)}return r}(l,e,t)}))}(t,0,o,2,r=this.getTileCoordExtent(e))||e8(t,0,o,2,r[0],r[1])||e8(t,0,o,2,r[0],r[3])||e8(t,0,o,2,r[2],r[1])||e8(t,0,o,2,r[2],r[3]))}calculateTileRanges_(e){let t=this.resolutions_.length,o=Array(t);for(let r=this.minZoom;r<t;++r)o[r]=this.getTileRangeForExtentAndZ(e,r);this.fullTileRanges_=o}}let ti={radians:6370997/(2*Math.PI),degrees:2*Math.PI*6370997/360,ft:.3048,m:1,"us-ft":1200/3937};class to{constructor(e){this.code_=e.code,this.units_=e.units,this.extent_=void 0!==e.extent?e.extent:null,this.worldExtent_=void 0!==e.worldExtent?e.worldExtent:null,this.axisOrientation_=void 0!==e.axisOrientation?e.axisOrientation:"enu",this.global_=void 0!==e.global&&e.global,this.canWrapX_=!!(this.global_&&this.extent_),this.getPointResolutionFunc_=e.getPointResolution,this.defaultTileGrid_=null,this.metersPerUnit_=e.metersPerUnit}canWrapX(){return this.canWrapX_}getCode(){return this.code_}getExtent(){return this.extent_}getUnits(){return this.units_}getMetersPerUnit(){return this.metersPerUnit_||ti[this.units_]}getWorldExtent(){return this.worldExtent_}getAxisOrientation(){return this.axisOrientation_}isGlobal(){return this.global_}setGlobal(e){this.global_=e,this.canWrapX_=!!(e&&this.extent_)}getDefaultTileGrid(){return this.defaultTileGrid_}setDefaultTileGrid(e){this.defaultTileGrid_=e}setExtent(e){this.extent_=e,this.canWrapX_=!!(this.global_&&e)}setWorldExtent(e){this.worldExtent_=e}setGetPointResolution(e){this.getPointResolutionFunc_=e}getPointResolutionFunc(){return this.getPointResolutionFunc_}}let tr=6378137*Math.PI,tl=[-tr,-tr,tr,tr],ta=[-180,-85,180,85],ts=6378137*Math.log(Math.tan(Math.PI/2));class tn extends to{constructor(e){super({code:e,units:"m",extent:tl,global:!0,worldExtent:ta,getPointResolution:function(e,t){return e/Math.cosh(t[1]/6378137)}})}}let tu=[new tn("EPSG:3857"),new tn("EPSG:102100"),new tn("EPSG:102113"),new tn("EPSG:900913"),new tn("http://www.opengis.net/def/crs/EPSG/0/3857"),new tn("http://www.opengis.net/gml/srs/epsg.xml#3857")],th=[-180,-90,180,90],tc=6378137*Math.PI/180;class tm extends to{constructor(e,t){super({code:e,units:"degrees",extent:th,axisOrientation:t,global:!0,metersPerUnit:tc,worldExtent:th})}}let td=[new tm("CRS:84"),new tm("EPSG:4326","neu"),new tm("urn:ogc:def:crs:OGC:1.3:CRS84"),new tm("urn:ogc:def:crs:OGC:2:84"),new tm("http://www.opengis.net/def/crs/OGC/1.3/CRS84"),new tm("http://www.opengis.net/gml/srs/epsg.xml#4326","neu"),new tm("http://www.opengis.net/def/crs/EPSG/0/4326","neu")],tv={},tf={};function tp(e,t,o){let r=e.getCode(),l=t.getCode();r in tf||(tf[r]={}),tf[r][l]=o}let tg=[/^EPSG:(\d+)$/,/^urn:ogc:def:crs:EPSG::(\d+)$/,/^http:\/\/www\.opengis\.net\/def\/crs\/EPSG\/0\/(\d+)$/],tx=[function(e){return!function(e){let t=0;for(let o of tg){let r=e.match(o);if(r){t=parseInt(r[1]);break}}if(!t)return null;let o=0,r=!1;return t>32700&&t<32761?o=t-32700:t>32600&&t<32661&&(r=!0,o=t-32600),o?{number:o,north:r}:null}(e)?null:new to({code:e,units:"m"})}];function tw(e,t){if(void 0!==t)for(let o=0,r=e.length;o<r;++o)t[o]=e[o];else t=e.slice();return t}function tT(e){tv[e.getCode()]=e,tp(e,e,tw)}function tS(e){(function(e){e.forEach(tT)})(e),e.forEach(function(t){e.forEach(function(e){t!==e&&tp(t,e,tw)})})}tS(tu),tS(td),function(e,t,o,r){e.forEach(function(e){t.forEach(function(t){tp(e,t,o),tp(t,e,r)})})}(td,tu,function(e,t,o,r){let l=e.length;o=o>1?o:2,r=r??o,void 0===t&&(t=o>2?e.slice():Array(l));for(let o=0;o<l;o+=r){t[o]=tr*e[o]/180;let r=6378137*Math.log(Math.tan(Math.PI*(+e[o+1]+90)/360));r>ts?r=ts:r<-ts&&(r=-ts),t[o+1]=r}return t},function(e,t,o,r){let l=e.length;o=o>1?o:2,r=r??o,void 0===t&&(t=o>2?e.slice():Array(l));for(let o=0;o<l;o+=r)t[o]=180*e[o]/tr,t[o+1]=360*Math.atan(Math.exp(e[o+1]/6378137))/Math.PI-90;return t});var ty="precision highp float;precision highp int;precision highp sampler2D;uniform vec2 direction;uniform int kernelSize;uniform sampler2D imgToBlur;in vec2 vUv;out vec4 fragColor;vec4 blur5(sampler2D image,vec2 uv,vec2 resolution,vec2 direction){vec4 color=vec4(0.0);vec2 off1=vec2(1.3333333333333333)*direction;color+=texture(image,uv)*0.29411764705882354;color+=texture(image,uv+(off1/resolution))*0.35294117647058826;color+=texture(image,uv-(off1/resolution))*0.35294117647058826;return color;}vec4 blur9(sampler2D image,vec2 uv,vec2 resolution,vec2 direction){vec4 color=vec4(0.0);vec2 off1=vec2(1.3846153846)*direction;vec2 off2=vec2(3.2307692308)*direction;color+=texture(image,uv)*0.2270270270;color+=texture(image,uv+(off1/resolution))*0.3162162162;color+=texture(image,uv-(off1/resolution))*0.3162162162;color+=texture(image,uv+(off2/resolution))*0.0702702703;color+=texture(image,uv-(off2/resolution))*0.0702702703;return color;}vec4 blur13(sampler2D image,vec2 uv,vec2 resolution,vec2 direction){vec4 color=vec4(0.0);vec2 off1=vec2(1.411764705882353)*direction;vec2 off2=vec2(3.2941176470588234)*direction;vec2 off3=vec2(5.176470588235294)*direction;color+=texture(image,uv)*0.1964825501511404;color+=texture(image,uv+(off1/resolution))*0.2969069646728344;color+=texture(image,uv-(off1/resolution))*0.2969069646728344;color+=texture(image,uv+(off2/resolution))*0.09447039785044732;color+=texture(image,uv-(off2/resolution))*0.09447039785044732;color+=texture(image,uv+(off3/resolution))*0.010381362401148057;color+=texture(image,uv-(off3/resolution))*0.010381362401148057;return color;}vec4 blur19(sampler2D image,vec2 uv,vec2 resolution,vec2 direction){vec4 color=vec4(0.0);vec2 off1=vec2(1.434782608695652)*direction;vec2 off2=vec2(3.347826086956522)*direction;vec2 off3=vec2(5.260869565217392)*direction;vec2 off4=vec2(7.173913043478261)*direction;color+=texture(image,uv)*0.16818993967466953;color+=texture(image,uv+(off1/resolution))*0.2727695816518679;color+=texture(image,uv-(off1/resolution))*0.2727695816518679;color+=texture(image,uv+(off2/resolution))*0.11690124927937194;color+=texture(image,uv-(off2/resolution))*0.11690124927937194;color+=texture(image,uv+(off3/resolution))*0.024067904263400105;color+=texture(image,uv-(off3/resolution))*0.024067904263400105;color+=texture(image,uv+(off4/resolution))*0.0021112196722280793;color+=texture(image,uv-(off4/resolution))*0.0021112196722280793;return color;}void main(){vec4 color=vec4(1.,0,0,1.);vec2 textureResolution=vec2(textureSize(imgToBlur,0));if(kernelSize==5){color=blur5(imgToBlur,vUv,textureResolution,direction);}else if(kernelSize==9){color=blur9(imgToBlur,vUv,textureResolution,direction);}else if(kernelSize==13){color=blur13(imgToBlur,vUv,textureResolution,direction);}else if(kernelSize==19){color=blur19(imgToBlur,vUv,textureResolution,direction);}fragColor=color;}";let tM=null;class t_{constructor(e,t,o=(tM||(tM=new Q.CP7({alpha:!0,premultipliedAlpha:!1})),tM)){el(this,"renderer"),el(this,"camera",new Q.iKG(-1,1,1,-1,0,1)),el(this,"scene",new Q.xsS),el(this,"renderTarget"),el(this,"material",new Q.FIo({glslVersion:Q.LSk,vertexShader:"precision highp float;precision highp int;uniform mat4 modelViewMatrix;uniform mat4 projectionMatrix;in vec3 position;in vec2 uv;out vec2 vUv;void main(){vUv=uv;gl_Position=projectionMatrix*modelViewMatrix*vec4(position,1.0);}",fragmentShader:"precision highp float;precision highp int;precision highp sampler2D;in vec2 vUv;out vec4 fragColor;void main(){fragColor=vec4(1.,0.,0.,1.);}",depthWrite:!1,transparent:!0})),el(this,"size"),this.size=new Q.FM8(e,t),this.renderTarget=new Q.dd2(e,t,{minFilter:Q.wem,magFilter:Q.wem,format:Q.wk1}),this.camera.matrixAutoUpdate=!1,this.renderer=o,this.renderer.setPixelRatio(1),this.renderer.autoClear=!1;let r=new Q.Kj0(new Q._12(2,2),this.material);this.scene.add(r)}getOutputTexture(e=!1){return e?new Q.IEO(this.getPixelData(),this.size.x,this.size.y,Q.wk1):this.renderTarget.texture}setSize(e,t){e===this.size.x&&t===this.size.y||(this.size.x=e,this.size.y=t,this.renderTarget=new Q.dd2(e,t,{minFilter:Q.wem,magFilter:Q.wem,format:Q.wk1}),this.renderer.setRenderTarget(this.renderTarget))}setVertexShader(e){this.material.vertexShader=e.trim(),this.material.needsUpdate=!0}setFragmentShader(e){this.material.fragmentShader=e.trim(),this.material.needsUpdate=!0}setUniform(e,t){this.material.uniforms[e]={value:t}}setDefine(e,t){this.material.fragmentShader=this.material.fragmentShader.replace(RegExp(e,"g"),t.toString()),this.material.vertexShader=this.material.vertexShader.replace(RegExp(e,"g"),t.toString()),this.material.needsUpdate=!0}getPixelData(){let e=this.size,t=new Uint8Array(e.x*e.y*4);return this.renderer.readRenderTargetPixels(this.renderTarget,0,0,e.x,e.y,t),t}getSize(){return this.size.clone()}process(){return this.material.needsUpdate=!0,this.renderer.setRenderTarget(this.renderTarget),this.renderer.clear(),this.renderer.render(this.scene,this.camera),this.renderTarget.texture}debugAsPNG(){let e=this.getSize(),t=this.getPixelData(),o=document.createElement("canvas");o.width=e.x,o.height=e.y;let r=o.getContext("2d");if(!r)return;let l=r.getImageData(0,0,o.width,o.height);l.data.set(t),r.putImageData(l,0,0);let a=window.open();a&&a.document.write(`<iframe src="${o.toDataURL("image/png")}" frameborder="0" style="border:0; top:0px; left:0px; bottom:0px; right:0px; width:100%; height:100%;" allowfullscreen></iframe>`)}dispose(){this.renderTarget.texture.dispose(),this.renderTarget.dispose()}}class tR{constructor(){this.disposed=!1}dispose(){this.disposed||(this.disposed=!0,this.disposeInternal())}disposeInternal(){}}class tb{constructor(e){this.highWaterMark=void 0!==e?e:2048,this.count_=0,this.entries_={},this.oldest_=null,this.newest_=null}deleteOldest(){let e=this.pop();e instanceof tR&&e.dispose()}canExpireCache(){return this.highWaterMark>0&&this.getCount()>this.highWaterMark}expireCache(e){for(;this.canExpireCache();)this.deleteOldest()}clear(){for(;this.oldest_;)this.deleteOldest()}containsKey(e){return this.entries_.hasOwnProperty(e)}forEach(e){let t=this.oldest_;for(;t;)e(t.value_,t.key_,this),t=t.newer}get(e,t){let o=this.entries_[e];return e2(void 0!==o,"Tried to get a value for a key that does not exist in the cache"),o===this.newest_||(o===this.oldest_?(this.oldest_=this.oldest_.newer,this.oldest_.older=null):(o.newer.older=o.older,o.older.newer=o.newer),o.newer=null,o.older=this.newest_,this.newest_.newer=o,this.newest_=o),o.value_}remove(e){let t=this.entries_[e];return e2(void 0!==t,"Tried to get a value for a key that does not exist in the cache"),t===this.newest_?(this.newest_=t.older,this.newest_&&(this.newest_.newer=null)):t===this.oldest_?(this.oldest_=t.newer,this.oldest_&&(this.oldest_.older=null)):(t.newer.older=t.older,t.older.newer=t.newer),delete this.entries_[e],--this.count_,t.value_}getCount(){return this.count_}getKeys(){let e=Array(this.count_),t=0,o;for(o=this.newest_;o;o=o.older)e[t++]=o.key_;return e}getValues(){let e=Array(this.count_),t=0,o;for(o=this.newest_;o;o=o.older)e[t++]=o.value_;return e}peekLast(){return this.oldest_.value_}peekLastKey(){return this.oldest_.key_}peekFirstKey(){return this.newest_.key_}peek(e){var t;return null==(t=this.entries_[e])?void 0:t.value_}pop(){let e=this.oldest_;return delete this.entries_[e.key_],e.newer&&(e.newer.older=null),this.oldest_=e.newer,this.oldest_||(this.newest_=null),--this.count_,e.value_}replace(e,t){this.get(e),this.entries_[e].value_=t}set(e,t){e2(!(e in this.entries_),"Tried to set a value for a key that is used already");let o={key_:e,newer:null,older:this.newest_,value_:t};this.newest_?this.newest_.newer=o:this.oldest_=o,this.newest_=o,this.entries_[e]=o,++this.count_}setSize(e){this.highWaterMark=e}}class tE{constructor(e,t){el(this,"loader",new Q.dpR),el(this,"lru",new tb(32)),el(this,"apiKey",""),el(this,"sessionId",""),this.url=e,this.onTileLoad=t}setMaptilerParams(e,t){this.apiKey=e,this.sessionId=t}urlPatternToUrl(e){let t;try{t=new URL(this.url.replace("{zxy}",e))}catch{t=new URL(this.url.replace("{zxy}",e),document.baseURI)}return t.host===eB.maptilerApiHost&&(this.apiKey&&!t.searchParams.has("key")&&t.searchParams.append("key",this.apiKey),this.sessionId&&!t.searchParams.has("mtsid")&&t.searchParams.append("mtsid",this.sessionId)),t.href}getTile(e,t){if(e.split("/").map(e=>Number.parseInt(e)).some(e=>e<0))return null;if(this.lru.containsKey(e))return this.lru.get(e);if(t){let t=this.createTile(e);return this.lru.set(e,t),t}return null}createTile(e){let t=this.urlPatternToUrl(e),o={ready:!1,texture:null,zxy:e};return o.texture=this.loader.load(t,()=>{o.ready=!0,null!=this.onTileLoad&&this.onTileLoad(o,t,null)},void 0,e=>{null!=this.onTileLoad&&this.onTileLoad(o,t,e)}),o.texture.minFilter=Q.wem,o.texture.needsUpdate=!0,o}disposeTile(e){e.texture&&e.texture.dispose()}expireCache(e){for(;this.lru.canExpireCache()&&!(this.lru.peekLast().zxy in e);)this.disposeTile(this.lru.pop())}dispose(){for(let e of this.lru.getValues())this.disposeTile(e);this.lru.clear()}}class tA extends ee.EventEmitter{constructor(){super(...arguments),el(this,"frames",[]),el(this,"time",0),el(this,"animationSpeed",0),el(this,"lastTickTime",0)}addFrame(e,t){let o=this.findSmallerFrameIndex(e);if(o<0)this.frames.unshift({time:e,data:t});else{if(this.frames[o].time===e)throw Error("Frame with this time already exists");this.frames.splice(o+1,0,{time:e,data:t})}this.clampAnimation()}removeFrame(e){let t=this.frames.filter(t=>t.time===e);return this.frames=this.frames.filter(t=>t.time!==e),this.clampAnimation(),t}forEachFrame(e){for(let t of this.frames)e(t)}getAnimationStart(){return this.frames.length?this.frames[0].time:Number.POSITIVE_INFINITY}getAnimationStartDate(){return new Date(1e3*this.getAnimationStart())}getAnimationEnd(){return this.frames.length?this.frames[this.frames.length-1].time:Number.NEGATIVE_INFINITY}getAnimationEndDate(){return new Date(1e3*this.getAnimationEnd())}getAnimationTime(){return this.time}getAnimationTimeDate(){return new Date(1e3*this.getAnimationTime())}setAnimationTime(e){this.time=e,this.clampAnimation(),this.emit("animationTimeSet",{time:this.time})}clampAnimation(){this.time=Math.max(this.getAnimationStart(),Math.min(this.getAnimationEnd(),this.time))}animateByFactor(e){this.animate(e)}animate(e){e>0&&0===this.animationSpeed?this.emit("playAnimation",{time:this.time}):0===e&&this.animationSpeed>0&&this.emit("pauseAnimation",{time:this.time}),this.animationSpeed=e,0!==this.animationSpeed&&(this.lastTickTime=performance.now())}getAnimationSpeed(){return this.animationSpeed}isPlaying(){return this.animationSpeed>0}animationTick(){let e=performance.now();if(this.animationSpeed>0&&this.lastTickTime){let t=e-this.lastTickTime;this.time+=this.animationSpeed*(t/1e3);let o=this.getAnimationStart(),r=this.getAnimationEnd();this.time=o+(this.time-o)%(r-o),Number.isNaN(this.time)&&(this.time=o),this.emit("tick",{time:this.time})}this.lastTickTime=e}findSmallerFrameIndex(e){for(let t=this.frames.length-1;t>=0;--t)if(this.frames[t].time<=e)return t;return -1}getCurrentFrames(){if(0===this.frames.length)return{frameA:null,frameB:null,mix:0};let e=this.findSmallerFrameIndex(this.time);e<0&&(e=0);let t=this.frames[e],o=this.frames[Math.min(this.frames.length-1,e+1)],r=o.time-t.time,l=0===r?0:(this.time-t.time)/r;return{frameA:t,frameB:o,mix:l}}getNextFrame(e,t){let o=this.frames.findIndex(t=>t===e);return o<0?null:t<0?o>0?this.frames[o-1]:null:o<this.frames.length-1?this.frames[o+1]:null}}let tP=new Q.vBJ({transparent:!0,opacity:1,side:Q.Wl3,depthTest:!1,depthWrite:!1}),tC=(e={})=>{let{color:t=16777215*Math.random(),wireframe:o=!1,opacity:r=1}=e,l=tP.clone();return l.color=new Q.Ilk(t),l.wireframe=o,l.opacity=r,l};function tI({color:e}){let t=function({color:e}){let t=new Q.xo$(1,32,32),o=new Q.vBJ({color:e});return new Q.Kj0(t,o)}({color:e});for(let e of Array.isArray(t.material)?t.material:[t.material])e.transparent=!0,e.depthTest=!1,e.depthWrite=!1;return t.renderOrder=9999,t}function tz(e,t){var o;let r=t.getZoom(),{width:l,height:a}=t.getCanvas().getBoundingClientRect(),s=(o={width:l,height:a},r<=3||o.width<918&&o.height>1020||o.width<918?1:2);return{gridSize:e>30?9:3*s,resolutionScaleFactor:s}}class tF{constructor(){el(this,"object3D",new Q.ZAu),el(this,"helpersObject3D",new Q.ZAu),el(this,"map"),el(this,"currentTiles",[]),el(this,"currentTilesMeshes",new Map),el(this,"visibleTilesAreaMesh",new Q.Kj0),el(this,"areaGeometryVerticesRows",new Map),el(this,"minZoom",0),el(this,"maxZoom",24),el(this,"isGeometryNeedRefresh",!1);let e=function(){let e=new Q.ZAu,t=tI({color:16711680});t.scale.setScalar(.01),t.position.set(0,1.01,0);let o=tI({color:255});o.scale.setScalar(.01),o.position.set(0,-1.01,0);let r=tI({color:65280});return r.scale.setScalar(.01),r.position.set(0,0,1.01),e.add(t),e.add(o),e.add(r),e}();this.helpersObject3D.add(e),this.object3D.add(this.visibleTilesAreaMesh),this.visibleTilesAreaMesh.material=tC({color:16777215,wireframe:!1}),this.visibleTilesAreaMesh.renderOrder=2,this.visibleTilesAreaMesh.visible=!1}init({map:e,minZoom:t,maxZoom:o}){this.map=e,this.minZoom=t,this.maxZoom=o}update(){var e,t;if(void 0===this.map)return;let o=Math.floor(this.map.getZoom());o=Math.min(Math.max(o,this.minZoom),this.maxZoom);let r=this.map.getCenter(),{gridSize:l}=tz(this.map.getPitch(),this.map),a=function(e,t){let o=2**t,{lat:r,lng:l}=e,a=r*Math.PI/180,s=(l+180)/360*o,n=(1-Math.log(Math.tan(a)+1/Math.cos(a))/Math.PI)/2*o;return s=Math.floor((s%o+o)%o),n=Math.floor(Math.max(0,Math.min(n,o-1))),{id:`${t}/${s}/${n}`,x:s,y:n,z:t}}(r,o),s=o<=3?function(e){let t=2**e,o=[];for(let r=0;r<t;r++)for(let l=0;l<t;l++)o.push({id:`${e}/${r}/${l}`,x:r,y:l,z:e});return o}(o):function(e,t){let{x:o,y:r,z:l}=e,a=2**l,s=Math.floor(Math.min(t,a)/2),n=[],u=new Set;for(let e=-s;e<=s;e++)for(let t=-s;t<=s;t++){let s=(o+e+a)%a,h=(r+t+a)%a,c=`${l}/${s}/${h}`;u.has(c)||(u.add(c),n.push({id:c,x:s,y:h,z:l}))}return n}(a,l);e=this.currentTiles.map(({id:e})=>e),t=s.map(({id:e})=>e),e.length===t.length&&e.every((e,o)=>e===t[o])||(this.currentTiles=s,this.isGeometryNeedRefresh=!0,this.object3D.remove(this.visibleTilesAreaMesh))}recreateGlobeMeshes(){!1!==this.isGeometryNeedRefresh&&(this.isGeometryNeedRefresh=!1,this.object3D.clear(),this.helpersObject3D.clear(),this.currentTilesMeshes.clear(),this.areaGeometryVerticesRows.clear(),this.recreateGlobeTiles(),this.recreateAreaMesh())}recreateGlobeTiles(){let e=this.currentTiles.reduce((e,t)=>Math.min(e,t.x),Number.POSITIVE_INFINITY),t=this.currentTiles.reduce((e,t)=>Math.min(e,t.y),Number.POSITIVE_INFINITY);for(let o of this.currentTiles){let{z:r,x:l,y:a}=o,s=2**r-1,n=new Q.u9r,u=[],h=[],c=[];for(let o=0;o<=16;o++){let n=o;for(let m=0;m<=16;m++){let d=m,v=m/16,f=o/16,p=1-f,g=function(e,t,o,r,l){var a;let s=1/(1<<l),n=(((e/8192*s+o*s)*Math.PI*2+Math.PI)%(a=2*Math.PI)+a)%a,u=2*Math.atan(Math.exp(Math.PI-(t/8192*s+r*s)*Math.PI*2))-.5*Math.PI,h=Math.cos(u),c=new Float64Array(3);return c[0]=Math.sin(n)*h,c[1]=Math.sin(u),c[2]=Math.cos(n)*h,c}(8192*v,8192*f,l,a,r);0===a&&n<=4&&(g=em(g,[0,1,0],1-n/4)),a===s&&n>=12&&(g=em(g,[0,-1,0],1-(16-n)/4)),u.push(g[0],g[1],g[2]),c.push(v,p);let x=0===n,w=0===d,T=a===t,S=l===e;if(!1===x&&!1===w||!0===x&&!1===w&&!0===T||!0===w&&!1===x&&!0===S||!0===T&&!0===S){let e=`tileY:${a}__vertexRow:${n}`;!1===this.areaGeometryVerticesRows.has(e)&&this.areaGeometryVerticesRows.set(e,[]),(function(e,t){let o=e.get(t);if(void 0===o)throw Error(`Element with key ${t} not found in map`);return o})(this.areaGeometryVerticesRows,e).push(g)}if(16!==o&&16!==m){let e=17*o+m,t=(o+1)*17+m,r=(o+1)*17+(m+1),l=17*o+(m+1);h.push(e,t,l),h.push(t,r,l)}}}n.setAttribute("position",new Q.a$l(u,3)),n.setAttribute("uv",new Q.a$l(c,2)),n.setIndex(h);let m=new Q.Kj0;m.geometry=n,m.renderOrder=1,this.object3D.add(m),this.currentTilesMeshes.set(o.id,m)}}recreateAreaMesh(){let e=Array.from(this.areaGeometryVerticesRows.keys()).sort((e,t)=>{let[o,r]=e.split("__").map(e=>Number(e.split(":")[1])),[l,a]=t.split("__").map(e=>Number(e.split(":")[1]));return o===l?r-a:o-l}).map(e=>this.areaGeometryVerticesRows.get(e)),t=e.length,o=e[0].length;if(t<2){console.error("Not enough rows to create sphere fragment:",{rowsAmount:t});return}if(t!==o){console.warn("Rows amount is not equal to columns amount:",{rowsAmount:t,columnsAmount:o});return}let r=[],l=[],a=[],s=new Map;for(let a=0;a<t;a++)for(let n=0;n<o;n++){let u=e[a][n],h=`${u[0]},${u[1]},${u[2]}`;s.has(h)&&(u[0]+=1e-4*Math.random(),u[1]+=1e-4*Math.random(),u[2]+=1e-4*Math.random(),h=`${u[0]},${u[1]},${u[2]}`),s.set(h,s.size),r.push(u[0],u[1],u[2]);let c=n/(o-1),m=1-a/(t-1);l.push(c,m)}for(let r=0;r<t-1;r++){let t=e[r],l=e[r+1];for(let e=0;e<o-1;e++){let o=`${t[e][0]},${t[e][1]},${t[e][2]}`,r=`${t[e+1][0]},${t[e+1][1]},${t[e+1][2]}`,n=`${l[e][0]},${l[e][1]},${l[e][2]}`,u=`${l[e+1][0]},${l[e+1][1]},${l[e+1][2]}`,h=s.get(o),c=s.get(r),m=s.get(n),d=s.get(u);if(void 0===h||void 0===c||void 0===m||void 0===d){console.error("Undefined index:",{topLeftIndex:h,topRightIndex:c,bottomLeftIndex:m,bottomRightIndex:d});continue}a.push(h,m,c),a.push(m,d,c)}}let n=new Q.u9r;n.setAttribute("position",new Q.a$l(r,3)),n.setAttribute("uv",new Q.a$l(l,2)),n.setIndex(a),this.visibleTilesAreaMesh.geometry=n,this.object3D.add(this.visibleTilesAreaMesh)}}class tD extends tA{constructor(e,t,o=null,r=null){super(),el(this,"id"),el(this,"type","custom"),el(this,"renderingMode","3d"),el(this,"map",null),el(this,"dataMinZoom",0),el(this,"dataMaxZoom",4),el(this,"slippyTiles",[]),el(this,"renderer",null),el(this,"camera",new Q.V1s),el(this,"scene",new Q.xsS),el(this,"material"),el(this,"geometry",new Q._12(1,1)),el(this,"tilegrid"),el(this,"extent",null),el(this,"repaintOnPausedAnimation",!0),el(this,"materialPool",new eY),el(this,"bluringNodePasses",[]),el(this,"extentScale",1),el(this,"extentChangedThreshold",.75),el(this,"lastExtent",[0,0,0,0]),el(this,"timeInterpolation",!0),el(this,"isReady",!1),el(this,"defaultTexture",new Q.IEO(new Uint8Array([128,128,128,255]),1,1,Q.wk1)),el(this,"onMoveEndListener"),el(this,"onResizeListener"),el(this,"onMoveListener"),el(this,"onProjectionChangedListener"),el(this,"coloringFragments",null),el(this,"multiChannelColoringFragment"),el(this,"loadLowerZoomLevels"),el(this,"interpolateTileEdge",!1),el(this,"flusher"),el(this,"globeTilesService"),el(this,"threeWorldGroup",new Q.ZAu),el(this,"slippyTilesGroup",new Q.ZAu),el(this,"getMapOrThrow",()=>{if(null==this.map)throw Error("Accessing map on detached layer");return this.map}),el(this,"getRendererOrThrow",()=>{if(null==this.renderer)throw Error("Accessing renderer on detached layer");return this.renderer}),this.defaultTexture.needsUpdate=!0,this.id=e,t&&this.init(t,o,r),this.onMoveEndListener=this.onMoveEnd.bind(this),this.onResizeListener=this.onResize.bind(this),this.onMoveListener=this.onMove.bind(this),this.onProjectionChangedListener=this.onProjectionChanged.bind(this),this.flusher=new Q.Kj0(new Q._12(1,1),new Q.vBJ({colorWrite:!1,depthWrite:!1})),this.flusher.frustumCulled=!1,this.scene.add(this.flusher),this.threeWorldGroup.add(this.slippyTilesGroup),this.globeTilesService=new tF,this.threeWorldGroup.add(this.globeTilesService.object3D)}init(e,t=null,o=null){this.interpolateTileEdge=e.interpolateTileEdge??!1,this.loadLowerZoomLevels=e.loadLowerZoomLevels??!0,this.coloringFragments=t,this.multiChannelColoringFragment=o,this.repaintOnPausedAnimation=e.repaintOnPausedAnimation??!0,this.dataMinZoom=e.minZoom??this.dataMinZoom,this.dataMaxZoom=e.maxZoom??this.dataMaxZoom,this.tilegrid=function(e){let t=e||{},o=t.extent||(function(e){let t=tv[e]||tv[e.replace(/urn:(x-)?ogc:def:crs:EPSG:(.*:)?(\w+)$/,"EPSG:$3")]||null;if(t)return t;for(let t of tx){let o=t(e);if(o)return o}return null})("EPSG:3857").getExtent();return new tt({extent:o,minZoom:t.minZoom,tileSize:t.tileSize,resolutions:function(e,t,o,r){t=void 0!==t?t:42,o=e9(void 0!==o?o:256);let l=e[3]-e[1],a=e[2]-e[0];r=r>0?r:Math.max(a/o[0],l/o[1]);let s=t+1,n=Array(s);for(let e=0;e<s;++e)n[e]=r/Math.pow(2,e);return n}(o,t.maxZoom,t.tileSize,t.maxResolution)})}({minZoom:this.dataMinZoom,maxZoom:this.dataMaxZoom}),e.bounds&&(this.extent=eD(e.bounds[0],e.bounds[1]).concat(eD(e.bounds[2],e.bounds[3])));let r="";if(this.coloringFragments){let e=this.coloringFragments.map((e,t)=>e.getBlendCode(`_cf_${t}`)),t="fragColor";for(let e=0;e<this.coloringFragments.length;e++)t=`_cf_${e}(${t},inter)`;t=`fragColor=${t};`,r="precision highp float;precision highp sampler2D;COLORING_FRAGMENT_DEFSout vec4 fragColor;uniform float opacity;uniform float zoom;uniform float time;uniform sampler2D tex0;uniform sampler2D tex1;uniform float tilePixelSize;uniform sampler2D texN0;uniform sampler2D texN1;uniform bool availableN;uniform sampler2D texE0;uniform sampler2D texE1;uniform bool availableE;uniform sampler2D texS0;uniform sampler2D texS1;uniform bool availableS;uniform sampler2D texW0;uniform sampler2D texW1;uniform bool availableW;uniform float maxSmoothingDistance;uniform float smoothingDistanceDecayFactor;uniform bool localSmoothing;uniform float zoomDelta;in vec2 tex0coord;in vec2 tex1coord;in vec2 mercCoord;float PI=3.141592;const int halfNbSmoothingBins=int(ceil(float(NB_SMOOTHING_BINS))/2.);void textPositionToInternalTexPosition(vec2 uv,out vec2 newUv,out int texturePositionIndex){if(uv.x>=0.&&uv.x<=1.){if(uv.y>=0.&&uv.y<=1.){newUv=uv;texturePositionIndex=0;}else if(uv.y<0.&&availableS){newUv=vec2(uv.x,1.+uv.y);texturePositionIndex=5;}else if(uv.y>1.&&availableN){newUv=vec2(uv.x,1.-uv.y);texturePositionIndex=1;}else{newUv=uv;texturePositionIndex=0;}}else if(uv.x<0.){if(uv.y>=0.&&uv.y<=1.&&availableW){newUv=vec2(1.+uv.x,uv.y);texturePositionIndex=7;}else{newUv=uv;texturePositionIndex=0;}}else if(uv.x>1.){if(uv.y>=0.&&uv.y<=1.&&availableE){newUv=vec2(uv.x-1.,uv.y);texturePositionIndex=3;}else{newUv=uv;texturePositionIndex=0;}}}vec4 getTextureColorByIndex(int textureTimeIndex,int texturePositionIndex,vec2 uv){if(textureTimeIndex==0){if(texturePositionIndex==0){return texture(tex0,uv);}else if(texturePositionIndex==1){return texture(texN0,uv);}else if(texturePositionIndex==3){return texture(texE0,uv);}else if(texturePositionIndex==5){return texture(texS0,uv);}else if(texturePositionIndex==7){return texture(texW0,uv);}}else if(textureTimeIndex==1){if(texturePositionIndex==0){return texture(tex1,uv);}else if(texturePositionIndex==1){return texture(texN1,uv);}else if(texturePositionIndex==3){return texture(texE1,uv);}else if(texturePositionIndex==5){return texture(texS1,uv);}else if(texturePositionIndex==7){return texture(texW1,uv);}}else{return vec4(1.,0.,0.,1.);}}vec4 getColor(int textureTimeIndex,vec2 uv){int texturePositionIndex=0;vec2 localUV=vec2(0.,0.);textPositionToInternalTexPosition(uv,localUV,texturePositionIndex);return getTextureColorByIndex(textureTimeIndex,texturePositionIndex,localUV);}vec4 circularSampleSmoothing(int textureTimeIndex,vec2 uv,float resolution,float distance,out bool isTransparant){vec4 colorRGBA=getColor(textureTimeIndex,uv);int weightCenter=3;vec4 color=colorRGBA*float(weightCenter);isTransparant=colorRGBA.a<1.;float angularStep=2.*PI/float(halfNbSmoothingBins);for(int i=0;i<halfNbSmoothingBins;i++){float angle=angularStep*float(i);vec2 offset=vec2(cos(angle),sin(angle))*distance*0.5;vec2 pos=uv+offset/resolution;vec4 c=getColor(textureTimeIndex,pos);color+=c;isTransparant=isTransparant||c.a<1.;angle=angularStep/2.+angularStep*float(i);offset=vec2(cos(angle),sin(angle))*distance;pos=uv+offset/resolution;c=getColor(textureTimeIndex,pos);color+=c;isTransparant=isTransparant||c.a<1.;}return color/float(2*halfNbSmoothingBins+weightCenter);}void main(){float scaledSamplingDistance=(maxSmoothingDistance*smoothingDistanceDecayFactor/(smoothingDistanceDecayFactor+zoom))*max(1.,2.-zoomDelta);bool isTransparant0=false;bool isTransparant1=false;vec4 tex0Color;vec4 tex1Color;if(localSmoothing&&halfNbSmoothingBins!=0){tex0Color=circularSampleSmoothing(0,tex0coord,tilePixelSize,scaledSamplingDistance,isTransparant0);tex1Color=circularSampleSmoothing(1,tex1coord,tilePixelSize,scaledSamplingDistance,isTransparant1);}else{tex0Color=texture(tex0,tex0coord);tex1Color=texture(tex1,tex1coord);}vec4 inter=mix(tex0Color,tex1Color,time);if(inter.a<1.0&&!RENDER_TRANSPARENT)discard;COLORING_FRAGMENT_CALLSfragColor.a*=opacity;}".replace("COLORING_FRAGMENT_DEFS",e.join(`
`)).replace("COLORING_FRAGMENT_CALLS",t)}else o&&(r=o.getCode());let l=(e.nbSmoothingBins??16).toFixed(0);this.timeInterpolation=e.timeInterpolation??!0,this.material=new Q.FIo({glslVersion:Q.LSk,uniforms:{opacity:{value:1},time:{value:0},zoom:{value:0},tex0xy:{value:[0,0]},tex1xy:{value:[0,0]},tex0size:{value:1},tex1size:{value:1},tilePixelSize:{value:1},categorySmoothTransition:{value:e.categorySmoothTransition??!1},timeInterpolation:{value:this.timeInterpolation},tex0:{value:null},tex1:{value:null},zoomDelta:{value:0},localSmoothing:{value:e.localSmoothing??!1},maxSmoothingDistance:{value:e.maxSmoothingDistance??12},smoothingDistanceDecayFactor:{value:e.smoothingDistanceDecayFactor??10},texN0:{value:this.defaultTexture},texN1:{value:this.defaultTexture},availableN:{value:!1},texE0:{value:this.defaultTexture},texE1:{value:this.defaultTexture},availableE:{value:!1},texS0:{value:this.defaultTexture},texS1:{value:this.defaultTexture},availableS:{value:!1},texW0:{value:this.defaultTexture},texW1:{value:this.defaultTexture},availableW:{value:!1},interpolateTileEdge:{value:this.interpolateTileEdge}},vertexShader:"precision highp float;precision highp sampler2D;uniform mat4 modelViewMatrix;uniform mat4 projectionMatrix;in vec3 position;in vec2 uv;uniform vec2 tex0xy;uniform vec2 tex1xy;uniform float tex0size;uniform float tex1size;out vec2 tex0coord;out vec2 tex1coord;out vec2 mercCoord;void main(){tex0coord=tex0xy+uv*tex0size;tex1coord=tex1xy+uv*tex1size;mercCoord=(modelViewMatrix*vec4(position,1.0)).xy*vec2(1.0,-1.0);gl_Position=projectionMatrix*modelViewMatrix*vec4(position,1.);}",fragmentShader:r,depthTest:!1,depthWrite:!1,transparent:!0,side:Q.Wl3,defines:{RENDER_TRANSPARENT:e.renderTransparentArea?"true":"false",NB_SMOOTHING_BINS:l}}),this.scene.add(this.threeWorldGroup),this.updateSlippyTileGrid(8,8),this.on("playAnimation",()=>{this.forceRepaint()}),this.on("animationTimeSet",()=>{this.forceRepaint()}),this.isReady=!0}onResize(){if(!this.isReady)return;let{width:e,height:t}=this.getMapOrThrow().getCanvas();this.getRendererOrThrow().setViewport(0,0,e,t),this.updateSlippyTileGrid(Math.ceil(e/512)+2,Math.ceil(t/512)+2)}onMove(){if(!this.isReady)return;let e=this.getVisibleExtent(this.extentScale);if(!e)throw Error("New extent is null");let t=eq([Math.min(e[0],this.lastExtent[0]),Math.min(e[1],this.lastExtent[1]),Math.max(e[2],this.lastExtent[2]),Math.max(e[3],this.lastExtent[3])]);eq(eJ(this.lastExtent,e))/t<=this.extentChangedThreshold&&(this.lastExtent=e,this.emit("extentChanged"))}onMoveEnd(){this.refresh()}onProjectionChanged(e){this.globeTilesService.object3D.visible=!1,this.slippyTilesGroup.visible=!1,this.onResize(),this.refresh(),"globe"===e.newProjection?this.globeTilesService.object3D.visible=!0:this.slippyTilesGroup.visible=!0}updateSlippyTileGrid(e,t){this.slippyTilesGroup.clear(),this.slippyTiles.length=0;for(let o=0;o<e;o++){this.slippyTiles[o]=[];for(let e=0;e<t;e++){let t=new Q.Kj0(this.geometry);t.frustumCulled=!1,this.slippyTiles[o][e]=t,this.slippyTilesGroup.add(t)}}}validateSource(e){let t;try{t=new URL(e)}catch{t=new URL(e,document.baseURI)}if(t.host!==eB.maptilerApiHost)throw Error(`

MapTiler Weather library can only source data from MapTiler Cloud.
Please use our MapTiler Plus library for self hosting: https://www.maptiler.com/weather/
or host your data with our generous free plan at MapTiler Cloud: https://cloud.maptiler.com
`)}addSource(e,t,o=null){this.validateSource(t);let r=new tE(t,(e,t,r)=>{o&&o(e,t,r),this.forceRepaint()});if(this.map){let e=this.map.getSdkConfig().apiKey,t=this.map.getMaptilerSessionId();r.setMaptilerParams(e,t)}super.addFrame(e,r)}removeSource(e){for(let t of super.removeFrame(e))t.data.dispose();this.forceRepaint()}getVisibleExtent(e){if(!this.isReady)return null;let t=this.getMapOrThrow().getBounds(),o=eD(t.getWest(),t.getSouth()).concat(eD(t.getEast(),t.getNorth()));return this.extent&&(o=eJ(this.extent,o)),e&&function(e,t){let o=(e[2]-e[0])/2*(t-1),r=(e[3]-e[1])/2*(t-1);e[0]-=o,e[2]+=o,e[1]-=r,e[3]+=r}(o,e),o}getWantedTiles(e,t,o){let r=Math.ceil(e),l={},a=this.getMapOrThrow().getBounds(),s=Math.min(o,Math.max(t,r-1)),n=Math.min(o,Math.max(t,r));for(let e=s;e<=n;++e)for(let t of function(e,t){let o=~~t,r=ek(e.getNorthWest(),o),l=ek(e.getSouthEast(),o),a=[];for(let e=Math.floor(r.x);e<=l.x;e+=1)for(let o=Math.floor(r.y);o<=Math.floor(l.y);o+=1)a.push(`${t}/${e}/${o}`);return a}(a,e))l[t]=!0;return l}setOpacity(e){this.isReady&&(this.material.uniforms.opacity.value=e,this.forceRepaint())}onAdd(e,t){this.registerTelemetry(e),this.map=e,this.map.on("resize",this.onResizeListener),this.map.on("projectiontransition",this.onProjectionChangedListener),this.map.on("move",this.onMoveListener),this.map.on("moveend",this.onMoveEndListener);let o=this.map.getSdkConfig().apiKey,r=this.map.getMaptilerSessionId();this.forEachFrame(e=>{e.data.setMaptilerParams(o,r)}),ej.hasData()||ej.fetchLatest(o,r),this.renderer=new Q.CP7({canvas:e.getCanvas(),context:t,depth:!1,stencil:!1,antialias:!1,powerPreference:"high-performance"}),this.renderer.autoClear=!1,this.renderer.compile(this.scene,this.camera),this.globeTilesService.init({map:this.map,minZoom:1,maxZoom:this.map.getMaxZoom()});let l=this.getVisibleExtent(this.extentScale);if(null===l)throw Error("The last extent is null");this.lastExtent=l,this.forceRepaint(),this.onProjectionChanged({type:"projectiontransition",newProjection:e.isGlobeProjection()?"globe":"mercator"})}refresh(){this.getMapOrThrow().isGlobeProjection()&&(this.globeTilesService.update(),this.globeTilesService.recreateGlobeMeshes())}onRemove(e,t){this.isReady&&(null!=this.map&&(this.map.off("resize",this.onResizeListener),this.map.off("projectiontransition",this.onProjectionChangedListener),this.map.off("move",this.onMoveListener),this.map.off("moveend",this.onMoveEndListener),this.map=null),null!=this.renderer&&(this.renderer.dispose(),this.renderer=null),this.forEachFrame(e=>{e.data.dispose()}),this.disposeObjects())}disposeObjects(){if(this.isReady)for(let e of(this.geometry.dispose(),this.material.dispose(),this.bluringNodePasses))e.dispose()}getTilePlacement(e,t,o,r,l,a){if(!this.isReady)return null;let s=`${o}/${r}/${l}`,n=0!==o&&o>=this.dataMaxZoom?null:e.getTile(s,t);if(!n||!n.ready)return this.loadLowerZoomLevels&&o>this.tilegrid.getMinZoom()?this.getTilePlacement(e,t,o-1,Math.floor(r/2),Math.floor(l/2),a||{z:o,x:r,y:l}):null;let u={tile:n,xy:[0,0],size:1};if(void 0!==a){let e=2**(a.z-o);u.size/=e,u.xy[0]=a.x%e/e,u.xy[1]=(2**a.z-a.y-1)%e/e}return u}updateTilesMaterials(){var e;let t=this.getMapOrThrow();if(!1===t.isGlobeProjection()){console.warn("For mercator projection use legacy waether tiles flow");return}let o=t.getZoom(),r=this.tilegrid.getMinZoom(),l=o-Math.min(this.tilegrid.getMaxZoom(),Math.max(r,Math.floor(o))),{frameA:a,frameB:s,mix:n}=this.getCurrentFrames();if(!a||!s){this.scene.visible=!1;return}for(let[t,r]of(this.scene.visible=!0,this.materialPool.init(),this.globeTilesService.currentTilesMeshes)){let u=this.getTilesPair(a,s,t);if(null===u){r.visible=!1;continue}r.visible=!0;let{tileA:h,tileB:c}=u,m=this.materialPool.isEmpty()?this.materialPool.add(this.material.clone()):this.materialPool.pop();r.material=m,m.uniforms.localSmoothing.value=this.material.uniforms.localSmoothing.value,m.uniforms.maxSmoothingDistance.value=this.material.uniforms.maxSmoothingDistance.value,m.uniforms.smoothingDistanceDecayFactor.value=this.material.uniforms.smoothingDistanceDecayFactor.value,m.uniforms.timeInterpolation.value=this.material.uniforms.timeInterpolation.value,m.uniforms.categorySmoothTransition.value=this.material.uniforms.categorySmoothTransition.value,m.uniforms.opacity.value=this.material.uniforms.opacity.value,m.uniforms.tex0.value=h.tile.texture,m.uniforms.tex0xy.value=h.xy,m.uniforms.tex0size.value=h.size,m.uniforms.tex1.value=c.tile.texture,m.uniforms.tex1xy.value=c.xy,m.uniforms.tex1size.value=c.size,m.uniforms.tilePixelSize.value=null==(e=h.tile.texture)?void 0:e.image.width,m.uniforms.time.value=n,m.uniforms.zoomDelta.value=l,m.uniforms.zoom.value=o}}updateSlippyTiles(){var e,t,o,r,l,a,s,n,u,h,c,m,d,v,f,p,g;if(!this.isReady)return;let{frameA:x,frameB:w,mix:T}=this.getCurrentFrames();if(!x||!w){this.scene.visible=!1;return}this.scene.visible=!0;let S=x.data,y=w.data,M=this.getMapOrThrow().getZoom(),_=this.tilegrid.getMinZoom(),R=this.tilegrid.getMaxZoom(),b=this.getWantedTiles(M,_,R),E={},A=Math.min(R,Math.max(_,Math.floor(M))),P=2**A,C=1/P,I=this.getMapOrThrow().getCenter(),z=this.tilegrid.getTileCoordForCoordAndZ(eD(I.lng,I.lat),A),F=M-A,D=this.slippyTiles.length;this.materialPool.init();let O=new Map;for(let t=0;t<D;t++){let o=this.slippyTiles[0].length;for(let r=0;r<o;r++){let l=this.slippyTiles[t][r],a=z[1]+t-Math.floor(D/2),s=z[2]+r-Math.floor(o/2),n=`${A}/${a}/${s}`;if(s<0||s>=P||!b[n]){l.visible=!1;continue}l.visible=!0;let u=(a%P+P)%P;E[`${A}/${u}/${s}`]=!0;let h=P-s-1;l.scale.x=C,l.scale.y=C,l.position.x=C*(a+.5),l.position.y=C*(h+.5)-1;let c=this.getTilePlacement(S,!0,A,u,s),m=this.getTilePlacement(y,!0,A,u,s);if(c&&!m&&(m=c),!c&&m&&(c=m),!c||!m){let e=x;for(;!c&&(e=this.getNextFrame(e,-1));)c=m=this.getTilePlacement(e.data,!1,A,u,s);if(!c||!m){l.visible=!1;continue}}let d=this.materialPool.isEmpty()?this.materialPool.add(this.material.clone()):this.materialPool.pop();d.uniforms.localSmoothing.value=this.material.uniforms.localSmoothing.value,d.uniforms.maxSmoothingDistance.value=this.material.uniforms.maxSmoothingDistance.value,d.uniforms.smoothingDistanceDecayFactor.value=this.material.uniforms.smoothingDistanceDecayFactor.value,d.uniforms.timeInterpolation.value=this.material.uniforms.timeInterpolation.value,d.uniforms.categorySmoothTransition.value=this.material.uniforms.categorySmoothTransition.value,d.uniforms.opacity.value=this.material.uniforms.opacity.value,d.uniforms.tex0.value=c.tile.texture,d.uniforms.tex0xy.value=c.xy,d.uniforms.tex0size.value=c.size,d.uniforms.tex1.value=m.tile.texture,d.uniforms.tex1xy.value=m.xy,d.uniforms.tex1size.value=m.size,d.uniforms.tilePixelSize.value=null==(e=c.tile.texture)?void 0:e.image.width,d.uniforms.time.value=T,d.uniforms.zoomDelta.value=F,d.uniforms.zoom.value=M,l.material=d,O.set(`${t} ${r}`,{material:d,tileA:c,tileB:m,x:t,y:r,z:A})}}if(this.material.uniforms.localSmoothing.value||this.interpolateTileEdge)for(let e of O){let x=e[1],w=x.x,T=x.y,S=x.material,y={x:w,y:T-1},M={x:w+1,y:T},_={x:w,y:T+1},R={x:w-1,y:T},b=`${y.x} ${y.y}`,E=`${M.x} ${M.y}`,A=`${_.x} ${_.y}`,P=`${R.x} ${R.y}`,C=O.get(b);null!=(o=null==(t=null==C?void 0:C.tileA)?void 0:t.tile)&&o.ready&&null!=(l=null==(r=null==C?void 0:C.tileB)?void 0:r.tile)&&l.ready?(S.uniforms.texN0.value=C.tileA.tile.texture,S.uniforms.texN1.value=C.tileB.tile.texture,S.uniforms.availableN.value=!0):(S.uniforms.texN0.value=null,S.uniforms.texN1.value=null,S.uniforms.availableN.value=!1),null!=(s=null==(a=null==(C=O.get(E))?void 0:C.tileA)?void 0:a.tile)&&s.ready&&null!=(u=null==(n=null==C?void 0:C.tileB)?void 0:n.tile)&&u.ready?(S.uniforms.texE0.value=C.tileA.tile.texture,S.uniforms.texE1.value=C.tileB.tile.texture,S.uniforms.availableE.value=!0):(S.uniforms.texE0.value=null,S.uniforms.texE1.value=null,S.uniforms.availableE.value=!1),null!=(c=null==(h=null==(C=O.get(A))?void 0:C.tileA)?void 0:h.tile)&&c.ready&&null!=(d=null==(m=null==C?void 0:C.tileB)?void 0:m.tile)&&d.ready?(S.uniforms.texS0.value=C.tileA.tile.texture,S.uniforms.texS1.value=C.tileB.tile.texture,S.uniforms.availableS.value=!0):(S.uniforms.texS0.value=null,S.uniforms.texS1.value=null,S.uniforms.availableS.value=!1),null!=(f=null==(v=null==(C=O.get(P))?void 0:C.tileA)?void 0:v.tile)&&f.ready&&null!=(g=null==(p=null==C?void 0:C.tileB)?void 0:p.tile)&&g.ready?(S.uniforms.texW0.value=C.tileA.tile.texture,S.uniforms.texW1.value=C.tileB.tile.texture,S.uniforms.availableW.value=!0):(S.uniforms.texW0.value=null,S.uniforms.texW1.value=null,S.uniforms.availableW.value=!1)}setTimeout(()=>{S.expireCache(E),y.expireCache(E);let e=this.getNextFrame(w,1);if(null!=e&&e.data)for(let t of Object.keys(E))e.data.getTile(t,!0)},0)}getTilesPair(e,t,o){let r=e.data,l=t.data,a=o.split("/").map(e=>Number.parseInt(e)),s=a[0],n=a[1],u=a[2],h=this.getTilePlacement(r,!0,s,n,u),c=this.getTilePlacement(l,!0,s,n,u);if(null===h){let t=e;for(;null===h&&null!==(t=this.getNextFrame(t,-1));)h=this.getTilePlacement(t.data,!1,s,n,u)}return null===c&&(c=h),null===h||null===c?null:{tileA:h,tileB:c}}prerender(e,t){if(!this.isReady)return;this.animationTick();let o=new Q.yGw().fromArray(t.defaultProjectionData.mainMatrix);if(this.getMapOrThrow().isGlobeProjection())this.camera.projectionMatrix=o,this.updateTilesMaterials();else{let e=new Q.yGw().scale(new Q.Pa4(1,-1,1));this.camera.projectionMatrix=o.multiply(e),this.updateSlippyTiles()}this.prerenderInternal()}prerenderInternal(){}render(e,t){if(!this.isReady)return;let o=this.getRendererOrThrow();o.state.reset(),this.renderInternal(),o.render(this.scene,this.camera),(this.getAnimationSpeed()>0||this.repaintOnPausedAnimation)&&this.forceRepaint()}renderInternal(){}pickFrame(e,t,o,r={}){var l;if(!this.isReady)return null;let a=eI(e,t);if(!this.map)return null;let s=r.load??!1,n=r.highestRes?this.tilegrid.getMaxZoom():Math.min(~~(null==(l=this.map)?void 0:l.getZoom()),this.tilegrid.getMaxZoom()),u=2**n,h=(.5+a[0])%1*u,c=(1-(a[1]+.5))*u,m=this.getTilePlacement(o,s,n,Math.floor(h),Math.floor(c));if(null==m||null==m.tile.texture)return null;if(this.coloringFragments){let e=eO(m.tile.texture.image,m.xy[0]+m.size*(h%1),1-m.xy[1]-m.size+m.size*(c%1));return this.coloringFragments.flatMap(t=>t.decodeChannel(e))}if(this.multiChannelColoringFragment){let e=eO(m.tile.texture.image,m.xy[0]+m.size*(h%1),1-m.xy[1]-m.size+m.size*(c%1));return this.multiChannelColoringFragment.decodeChannel(e)}return null}pickFrameBilinear(e,t,o,r={}){var l;let a=r.load??!1,s=r.highestRes??!1,n=eI(e,t);if(!this.map)return null;let u=s?this.tilegrid.getMaxZoom():Math.min(~~(null==(l=this.map)?void 0:l.getZoom()),this.tilegrid.getMaxZoom()),h=2**u,c=(.5+n[0])%1*h,m=(1-(n[1]+.5))*h,d=this.getTilePlacement(o,a,u,Math.floor(c),Math.floor(m));if(null==d||null==d.tile.texture)return null;let v=1/d.tile.texture.image.width;c-=v/2,m-=v/2;let f={x:~~(c/v)*v,y:~~(m/v)*v},p={x:f.x+v,y:f.y},g={x:f.x+v,y:f.y+v},x={x:f.x,y:f.y+v},w=this.getTilePlacement(o,a,u,Math.floor(f.x),Math.floor(f.y)),T=this.getTilePlacement(o,a,u,Math.floor(p.x),Math.floor(p.y)),S=this.getTilePlacement(o,a,u,Math.floor(g.x),Math.floor(g.y)),y=this.getTilePlacement(o,a,u,Math.floor(x.x),Math.floor(x.y));if(null==w||null==w.tile.texture||null==T||null==T.tile.texture||null==S||null==S.tile.texture||null==y||null==y.tile.texture)return null;let M=eO(d.tile.texture.image,d.xy[0]+d.size*(c%1),1-d.xy[1]-d.size+d.size*(m%1)),_=eO(w.tile.texture.image,w.xy[0]+w.size*(f.x%1),1-w.xy[1]-w.size+w.size*(f.y%1)),R=eO(T.tile.texture.image,T.xy[0]+T.size*(p.x%1),1-T.xy[1]-T.size+T.size*(p.y%1)),b=eO(S.tile.texture.image,S.xy[0]+S.size*(g.x%1),1-S.xy[1]-S.size+S.size*(g.y%1)),E=eO(y.tile.texture.image,y.xy[0]+y.size*(x.x%1),1-y.xy[1]-y.size+y.size*(x.y%1)),A=(m-f.y)/(x.y-f.y),P=1-A,C=(c-f.x)/(p.x-f.x),I=1-C;if(this.coloringFragments){let e={r:_.r*P+E.r*A,g:_.g*P+E.g*A,b:_.b*P+E.b*A,a:_.a*P+E.a*A},t={r:R.r*P+b.r*A,g:R.g*P+b.g*A,b:R.b*P+b.b*A,a:R.a*P+b.a*A},o={r:e.r*I+t.r*C,g:e.g*I+t.g*C,b:e.b*I+t.b*C,a:e.a*I+t.a*C};return this.coloringFragments.flatMap(e=>e.decodeChannel(o))}if(this.multiChannelColoringFragment){if(_.a!==R.a||_.a!==b.a||_.a!==E.a)return this.multiChannelColoringFragment.decodeChannel(M);let e=this.multiChannelColoringFragment.decodeChannel(_),t=this.multiChannelColoringFragment.decodeChannel(R),o=this.multiChannelColoringFragment.decodeChannel(b),r=this.multiChannelColoringFragment.decodeChannel(E);return[(e[0]*P+r[0]*A)*I+(t[0]*P+o[0]*A)*C,e[1]]}return null}pick(e,t,o={}){if(!this.isReady)return null;let{frameA:r,frameB:l,mix:a}=this.getCurrentFrames();if(!r||!l)return null;let s=!!o.bilinear,n=o.load??!1,u=o.highestRes??!1,h=s?this.pickFrameBilinear(e,t,r.data,{load:n,highestRes:u}):this.pickFrame(e,t,r.data,{load:n,highestRes:u}),c=h;return r.data!==l.data&&(c=s?this.pickFrameBilinear(e,t,l.data,{load:n,highestRes:u}):this.pickFrame(e,t,l.data,{load:n,highestRes:u})),h?c?this.coloringFragments?h.flatMap((e,t)=>{let o=c[t];return Array.isArray(e)&&Array.isArray(o)?e.map((e,t)=>e*(1-a)+o[t]*a):e*(1-a)+o*a}):this.multiChannelColoringFragment?this.timeInterpolation?[h[0]*(1-a)+c[0]*a,a<.5?h[1]:c[1]]:[a<.5?h[0]:c[0],a<.5?h[1]:c[1]]:null:h:c}forceRepaint(){try{this.getMapOrThrow().triggerRepaint()}catch{}}computeCurrentMixedImage({zxy:e="0/0/0",blurKernel:t=0,outputSize:o=-1,channel:r="r"}){var l,a,s,n;if(!this.isReady)return null;let u=this.getCurrentFrames(),h=u.mix,c=null==(a=null==(l=u.frameA)?void 0:l.data.getTile(e,!1))?void 0:a.texture,m=null==(n=null==(s=u.frameB)?void 0:s.data.getTile(e,!1))?void 0:n.texture,d=o<0?Number.parseInt(null==c?void 0:c.image.width):o,v=o<0?Number.parseInt(null==c?void 0:c.image.height):o;this.bluringNodePasses.length||this.bluringNodePasses.push(new t_(d,v),new t_(d,v),new t_(d,v));let f=this.bluringNodePasses[0],p=this.bluringNodePasses[1],g=this.bluringNodePasses[2];if(f.setSize(d,v),p.setSize(d,v),p.setSize(d,v),f.setFragmentShader("precision highp float;precision highp int;precision highp sampler2D;uniform sampler2D imageA;uniform sampler2D imageB;uniform float mixValue;in vec2 vUv;out vec4 fragColor;void main(){fragColor=mix(texture(imageA,vUv),texture(imageB,vUv),mixValue);}"),f.setDefine("CHANNEL",r),f.setUniform("imageA",c),f.setUniform("imageB",m),f.setUniform("mixValue",h),f.process(),0===t){let e=f.getPixelData();return{data:e,channels:e.length/(d*v),width:d,height:v}}if(![5,9,13,19].includes(t))throw Error(`The kernel bluring kernel size ${t} is not available.`);p.setUniform("direction",new Q.FM8(1,0)),p.setUniform("kernelSize",t),p.setUniform("imgToBlur",f.getOutputTexture()),p.setFragmentShader(ty),p.process(),g.setUniform("direction",new Q.FM8(0,1)),g.setUniform("kernelSize",t),g.setUniform("imgToBlur",p.getOutputTexture()),g.setFragmentShader(ty),g.process();let x=g.getPixelData();for(let e of this.bluringNodePasses)e.dispose();return{data:x,channels:x.length/(d*v),width:d,height:v}}setTimeInterpolation(e){this.isReady&&(this.material.uniforms.timeInterpolation.value=e)}setCategorySmoothTransition(e){this.isReady&&(this.material.uniforms.categorySmoothTransition.value=e,this.forceRepaint())}setLocalSmoothing(e){this.material.uniforms.localSmoothing.value=e,this.forceRepaint()}setMaxSmoothingDistance(e){this.material.uniforms.maxSmoothingDistance.value=e,this.forceRepaint()}setSmoothingDistanceDecayFactor(e){this.material.uniforms.smoothingDistanceDecayFactor.value=e,this.forceRepaint()}getRepaintOnPausedAnimation(){return this.repaintOnPausedAnimation}setRepaintOnPausedAnimation(e){this.repaintOnPausedAnimation=e,this.forceRepaint()}registerTelemetry(e){e.telemetry.registerModule("@maptiler/weather","3.0.1")}}class tO extends tD{constructor(e={}){super(e.id||"MapTiler Pressure",null,null,null),el(this,"constructorOptions"),el(this,"isSourceReady",!1),el(this,"colorRamp"),this.constructorOptions=e}async onAdd(e,t){let o=e.getSdkConfig().apiKey,r=e.getMaptilerSessionId();ej.hasData()||await ej.fetchLatest(o,r);let l=ej.getPressureData();if(!l)throw Error("The latest weather data is not avaialble");for(let a of(this.colorRamp=this.constructorOptions.colorramp??eU.builtin.PRESSURE_2,this.init({minZoom:l.metadata.minzoom,maxZoom:l.metadata.maxzoom,repaintOnPausedAnimation:!1},[new eL({decode:{channel:l.metadata.weather_variable.decoding.channels.toLowerCase(),min:l.metadata.weather_variable.decoding.min,max:l.metadata.weather_variable.decoding.max},stops:this.colorRamp,smooth:this.constructorOptions.smooth??!0,opacity:this.constructorOptions.opacity??1})]),super.onAdd(e,t),ej.getSourcesAndTimestamps(l,o,r)))this.addSource(a.timestamp,a.source);let a=+new Date/1e3;a>=this.getAnimationStart()&&a<=this.getAnimationEnd()&&this.setAnimationTime(a),this.isSourceReady=!0,this.emit("sourceReady",{map:e,layer:this})}getIsSourceReady(){return this.isSourceReady}pickAt(e,t,o={}){let r=super.pick(e,t,o);return r?{value:r[0]}:null}getColorRamp(){return this.colorRamp}onSourceReadyAsync(){return new Promise(e=>{if(this.isSourceReady)return e();this.once("sourceReady",()=>{e()})})}}var tN=`#version 300 es
precision highp float;uniform sampler2D rttTexture;uniform sampler2D rttTexturePrev;uniform float rttSize;uniform float screenSize;uniform float extrapolationFactor;uniform float renderStepSize;out float speed;vec4 advance(float position_){if(position_>(DENSITY/1000.0)*screenSize*screenSize){speed=0.0;return vec4(0.0);}float x=fract(position_/rttSize);float y=floor(position_/rttSize)/rttSize;vec4 color=texture(rttTexture,vec2(x,y));vec2 pos=color.ba+color.rg/255.0;vec4 colorPrev=texture(rttTexturePrev,vec2(x,y));vec2 posPrev=colorPrev.ba+colorPrev.rg/255.0;vec2 diff=pos-posPrev;pos+=diff*extrapolationFactor;speed=renderStepSize*step(0.0001,dot(posPrev,posPrev))*length(diff*screenSize);return vec4(pos,diff);}void main(){float position_=floor(position/2.0);float vertex=2.0*fract(position/2.0);vec4 posdiff=advance(position_);vec2 pos=posdiff.xy;vec2 dir=normalize(posdiff.zw);vec2 uv=(float(SIZE)/screenSize)*vec2(vertex,-vertex)*dir.yx;pos=2.0*(pos-uv)-1.0;gl_Position=vec4(pos.x,-pos.y,0.,1.);}`,tk=`precision highp float;precision highp sampler2D;
#define M_PI 3.14159265358
uniform float tileScale;uniform vec2 tilePosition;uniform vec2 tex0xy;uniform vec2 tex1xy;uniform float tex0size;uniform float tex1size;uniform bool useAlphaAsMask;uniform sampler2D rttTexture;uniform vec2 rttXY;uniform float rttScale;uniform float time;uniform sampler2D tex0;uniform sampler2D tex1;uniform float timestep;uniform float canvasDiagonal;uniform float angleDirectionShiftSkip;uniform bool uniformSpeed;uniform float tileGridPass;in vec2 rttCoord;out vec4 fragColor;float modI(float a,float b){float m=a-floor((a+0.5)/b)*b;return floor(m+0.5);}void main(){vec4 color=texture(rttTexture,rttCoord);if(tileGridPass==0.&&modI(floor(color.r*255.),2.)==0.){fragColor=color;return;}if(tileGridPass==1.&&modI(floor(color.r*255.),2.)!=0.){fragColor=color;return;}vec2 pos=color.ba+color.rg/255.0;vec2 posInTile=(rttXY+rttScale*pos)/tileScale-tilePosition;float uniformSpeedFactor=uniformSpeed ?(4000./canvasDiagonal): 1.;if(posInTile.x>=0.&&posInTile.x<=1.&&posInTile.y>=0.&&posInTile.y<=1.){posInTile.y=1.0-posInTile.y;vec2 tex0coord=tex0xy+posInTile*tex0size;vec2 tex1coord=tex1xy+posInTile*tex1size;vec4 tex0Color=texture(tex0,tex0coord);vec4 tex1Color=texture(tex1,tex1coord);if(useAlphaAsMask&&(tex0Color.a<1.||tex1Color.a<1.)){fragColor=color;return;}vec2 uv;bool skipRendering=false;if(D_WAVES){vec2 value0=tex0Color.D_CHANNELS;vec2 value1=tex1Color.D_CHANNELS;float dir0=2.0*M_PI*(-value0.x-0.25);float dir1=2.0*M_PI*(-value1.x-0.25);float speed=sqrt(1.0/mix(value0.y,value1.y,time));uv=vec2(mix(cos(dir0),cos(dir1),time),mix(sin(dir0),sin(dir1),time))*(D_MIN+(D_MAX-D_MIN)*speed);}else{vec4 lookup=mix(tex0Color,tex1Color,time);vec2 value=lookup.D_CHANNELS;uv=D_MIN+(D_MAX-D_MIN)*value;if(angleDirectionShiftSkip<89.999){vec2 offset=vec2(uv.x,-uv.y)*timestep*uniformSpeedFactor;vec2 nextPos=pos+offset;vec2 posNextInTile=(rttXY+rttScale*nextPos)/tileScale-tilePosition;posNextInTile.y=1.0-posNextInTile.y;vec2 tex0coordNext=tex0xy+posNextInTile*tex0size;vec2 tex1coordNext=tex1xy+posNextInTile*tex1size;vec4 tex0ColorNext=texture(tex0,tex0coordNext);vec4 tex1ColorNext=texture(tex1,tex1coordNext);vec4 lookupNext=mix(tex0ColorNext,tex1ColorNext,time);vec2 valueNext=lookupNext.D_CHANNELS;vec2 uvNext=D_MIN+(D_MAX-D_MIN)*valueNext;skipRendering=dot(normalize(uv),normalize(uvNext))<cos(angleDirectionShiftSkip*M_PI/180.);}}vec2 offset=vec2(uv.x,-uv.y)*timestep*uniformSpeedFactor;if(skipRendering){offset=vec2(0.,0.);}vec2 newPos=pos+offset;fragColor=vec4(fract(newPos*255.0),floor(newPos*255.0)/255.0);float red255=floor(fragColor.r*255.);if(tileGridPass==0.){if(modI(red255,2.)!=0.){red255-=1.;}}else{if(modI(red255,2.)==0.){red255+=1.;}}fragColor.r=red255/255.;}else{fragColor=color;}}`;class tW extends tD{constructor(e,t,o,r){super(e,null,null),el(this,"rttScene",new Q.xsS),el(this,"rttCamera"),el(this,"rttMaterial"),el(this,"rttMesh"),el(this,"particleTexturePrev"),el(this,"particleTexture0"),el(this,"particleTexture1"),el(this,"particleMaterial"),el(this,"particles"),el(this,"particleBackground"),el(this,"accumulator"),el(this,"accumulatorDrawMesh"),el(this,"flipFlop",!1),el(this,"numParticles"),el(this,"particleDensity"),el(this,"refreshInterval"),el(this,"fadeFactor"),el(this,"particleColor"),el(this,"particleFastColor"),el(this,"particleFastSpeed"),el(this,"particleSize"),el(this,"drawAsLines"),el(this,"particleSpeed"),el(this,"pixelRatio"),el(this,"prevRenderTime",0),el(this,"lastRenderTime",0),el(this,"forceRender",!0),el(this,"rttTimestep",200),el(this,"tileGridPass",0),el(this,"particlesTexturePreview"),t&&o&&this.initParticle(t,o,r)}initParticle(e,t,o){if(this.init(e,o,null),this.extentScale=1.2,this.numParticles=128,null!=t.maxAmount){if(t.maxAmount>=4&&t.maxAmount&&!(t.maxAmount&t.maxAmount-1))this.numParticles=t.maxAmount;else throw Error("The number of particles needs to be power of two and at least 4")}let r=window.devicePixelRatio;this.particleDensity=t.density??2,this.particleDensity/=r*r,this.refreshInterval=t.refreshInterval??800,this.fadeFactor=t.fadeFactor??.1,this.particleColor=eP(t.color??[255,255,255,192]),this.particleFastColor=t.fastColor?eP(t.fastColor):this.particleColor,this.particleFastSpeed=(t.fastSpeed??2)*r,this.particleSize=t.size??1.5,this.drawAsLines=t.drawAsLines??!1,this.particleSpeed=(t.speed??.001)/1e3,this.pixelRatio=t.pixelRatio??(r>1?1:2),this.particleTexturePrev=this.generateRandomizeParticleTexture(this.numParticles,this.numParticles),this.particleTexture0=new Q.dd2(this.numParticles,this.numParticles,{stencilBuffer:!1,depthBuffer:!1}),this.particleTexture0.texture=this.particleTexturePrev.clone(),this.particleTexture1=this.particleTexture0.clone(),this.rttMaterial=new Q.FIo({glslVersion:Q.LSk,uniforms:{tileGridPass:{value:this.tileGridPass},uniformSpeed:{value:t.uniformSpeed??!1},canvasDiagonal:{value:0},angleDirectionShiftSkip:{value:t.angleDirectionShiftSkip??90},rttXY:{value:[0,0]},rttScale:{value:1},rttTexture:{value:this.particleTexture0.texture},tileScale:{value:0},useAlphaAsMask:{value:t.useAlphaAsMask??!1},tilePosition:{value:[0,0]},time:{value:0},timestep:{value:0},tex0xy:{value:[0,0]},tex1xy:{value:[0,0]},tex0size:{value:1},tex1size:{value:1},tex0:{value:null},tex1:{value:null}},vertexShader:"precision highp float;uniform mat4 modelViewMatrix;uniform mat4 projectionMatrix;in vec3 position;in vec2 uv;out vec2 rttCoord;void main(){rttCoord=uv;gl_Position=projectionMatrix*vec4(position,1.);}",fragmentShader:tk,defines:{D_MIN:(t.decodeMin??0).toFixed(2),D_MAX:(t.decodeMax??1).toFixed(2),D_CHANNELS:t.decodeChannels??"rg",D_WAVES:t.decodeAsWaves?"true":"false"},depthTest:!1,depthWrite:!1}),this.rttCamera=new Q.iKG(-this.numParticles/2,this.numParticles/2,this.numParticles/2,-this.numParticles/2,-100,100),this.rttMesh=new Q.Kj0(new Q._12(this.numParticles,this.numParticles),this.rttMaterial),this.rttScene.add(this.rttMesh),this.accumulator=new Q.dd2(16,16,{minFilter:Q.wem,magFilter:Q.wem,stencilBuffer:!1,format:Q.wk1,depthBuffer:!1}),this.particleBackground=new Q.Kj0(new Q._12(2,2),new Q.FIo({glslVersion:Q.LSk,transparent:!0,blending:Q.Xaj,blendEquationAlpha:Q.rOj,vertexShader:"precision highp float;in vec3 position;void main(){gl_Position=vec4(position,1.);}",fragmentShader:`precision highp float;out vec4 fragColor;void main(){fragColor=vec4(.0,.0,.0,${this.fadeFactor.toFixed(2)});}`}));let l=r*this.pixelRatio*this.particleSize,a=r*this.particleSize;if(this.particleMaterial=new Q.FIo({glslVersion:Q.LSk,uniforms:{fastIsLarger:{value:t.fastIsLarger??!1},rttSize:{value:this.numParticles},rttTexture:{value:this.particleTexture1.texture},rttTexturePrev:{value:this.particleTexturePrev},screenSize:{value:1},extrapolationFactor:{value:0},renderStepSize:{value:0}},vertexShader:this.drawAsLines?tN:"precision highp float;precision highp sampler2D;in float position;uniform sampler2D rttTexture;uniform sampler2D rttTexturePrev;uniform float rttSize;uniform float screenSize;uniform float extrapolationFactor;uniform float renderStepSize;uniform bool fastIsLarger;out float speed;float factor=1.;vec4 advance(float position_){if(position_>(DENSITY/1000.0)*screenSize*screenSize){speed=0.0;return vec4(0.0);}float x=fract(position_/rttSize);float y=floor(position_/rttSize)/rttSize;vec4 color=texture(rttTexture,vec2(x,y));vec2 pos=color.ba+color.rg/255.0;vec4 colorPrev=texture(rttTexturePrev,vec2(x,y));vec2 posPrev=colorPrev.ba+colorPrev.rg/255.0;vec2 diff=pos-posPrev;pos+=diff*extrapolationFactor;speed=renderStepSize*step(0.0001,dot(posPrev,posPrev))*length(diff*screenSize);return vec4(pos,diff);}void main(){vec4 posdiff=advance(position);gl_PointSize=fastIsLarger ?(SIZE+SIZE*log(speed+1.))/2. : SIZE;gl_Position=vec4(2.0*posdiff.x-1.0,-(2.0*posdiff.y-1.0),0.,1.);}",fragmentShader:"precision highp float;precision highp sampler2D;out vec4 fragColor;in float speed;void main(){vec2 uv=vec2(gl_PointCoord.x-0.5,1.0-gl_PointCoord.y-0.5);float dFromCenter=sqrt(uv.x*uv.x+uv.y*uv.y);if(dFromCenter>0.5){discard;return;}if(speed==0.){discard;return;}float colorFactor=min(1.0,speed/COLOR_SPEED);fragColor=mix(COLOR_A,COLOR_B,colorFactor);float opacityFactor=smoothstep(0.05,0.1,speed);fragColor.a*=opacityFactor;}",defines:{COLOR_A:this.particleColor,COLOR_B:this.particleFastColor,COLOR_SPEED:this.particleFastSpeed.toFixed(2),DENSITY:this.particleDensity.toFixed(2),SIZE:(this.drawAsLines?a:l).toFixed(1)},transparent:!0,depthTest:!1,depthWrite:!1,linewidth:this.pixelRatio}),this.drawAsLines){let e=new Float32Array(Array(2*this.numParticles*this.numParticles).keys()),t=new Q.u9r;t.setAttribute("position",new Q.a$l(e,1)),this.particles=new Q.ejS(t,this.particleMaterial)}else{let e=new Float32Array(Array(this.numParticles*this.numParticles).keys()),t=new Q.u9r;t.setAttribute("position",new Q.a$l(e,1)),this.particles=new Q.woe(t,this.particleMaterial)}this.particles.frustumCulled=!1;let s=new Q.FIo({glslVersion:Q.LSk,premultipliedAlpha:!0,transparent:!0,depthTest:!1,depthWrite:!1,uniforms:{opacity:{value:1},tex0:{value:this.accumulator.texture}},vertexShader:"precision highp float;uniform mat4 modelViewMatrix;uniform mat4 projectionMatrix;in vec3 position;in vec2 uv;out vec2 coord;void main(){coord=uv;gl_Position=projectionMatrix*modelViewMatrix*vec4(position,1.);}",fragmentShader:"precision highp float;precision highp sampler2D;uniform float opacity;uniform sampler2D tex0;in vec2 coord;out vec4 fragColor;void main(){vec4 c=texture(tex0,coord);if(c.a<1.0/16.0){discard;}if(opacity<0.0001){discard;}fragColor=c;fragColor.a*=opacity;}"});this.accumulatorDrawMesh=new Q.Kj0(new Q._12(1,1),s),this.accumulatorDrawMesh.renderOrder=2,this.slippyTilesGroup.add(this.accumulatorDrawMesh),this.globeTilesService.visibleTilesAreaMesh.visible=!0,this.globeTilesService.visibleTilesAreaMesh.material=s,setInterval(()=>{this.renderer&&this.randomizeParticles(!1)},this.refreshInterval),this.on("extentChanged",()=>{this.refresh()})}generateRandomizeParticleTexture(e,t){let o=new Uint8Array(4*e*t);for(let e=0;e<o.length;e++)o[e]=Math.floor(256*Math.random());return new Q.IEO(o,e,t)}randomizeParticles(e){if(!this.renderer)return;let t=this.numParticles,o=this.numParticles,r=new Q.FM8(0,0);if(!e){let e=this.numParticles/4;t=e,o=e,r.x=Math.floor(Math.random()*this.numParticles/e)*e,r.y=Math.floor(Math.random()*this.numParticles/e)*e}let l=this.generateRandomizeParticleTexture(t,o);this.renderer.copyTextureToTexture(r,l,this.particleTexturePrev),this.renderer.copyTextureToTexture(r,l,this.particleTexture0.texture),this.renderer.copyTextureToTexture(r,l,this.particleTexture1.texture),l.dispose(),this.forceRender=e}getParticleUtilization(){let e=this.particleMaterial.uniforms.screenSize.value;return this.particleDensity/1e3*e*e/(this.numParticles*this.numParticles)}setOpacity(e){super.setOpacity(e),this.accumulatorDrawMesh.material.uniforms.opacity.value=e}disposeObjects(){super.disposeObjects(),this.particleTexturePrev.dispose(),this.particleTexture0.texture.dispose(),this.particleTexture0.dispose(),this.particleTexture1.texture.dispose(),this.particleTexture1.dispose(),this.accumulator.texture.dispose(),this.accumulator.dispose(),this.rttMesh.geometry.dispose(),this.rttMesh.material.dispose(),this.particleBackground.geometry.dispose(),this.particleBackground.material.dispose(),this.particles.geometry.dispose(),this.particles.material.dispose(),this.accumulatorDrawMesh.geometry.dispose(),this.accumulatorDrawMesh.material.dispose()}updateRenderingSize(){let e=this.getMapOrThrow(),t=e.getZoom(),o=e.getPitch(),r=e.getCanvas(),l=r.width,a=r.height,{resolutionScaleFactor:s}=tz(o,e),n=2048*this.pixelRatio*s,u=n;u=Math.min(u=!0===this.getMapOrThrow().isGlobeProjection()?t>5?n:Math.max(.75*t*n,1024):Math.round(this.pixelRatio*this.extentScale*Math.max(l,a)),this.getRendererOrThrow().capabilities.maxTextureSize);let h=Math.sqrt(l*l+a*a)*(1===devicePixelRatio?2:1);this.rttMaterial.uniforms.canvasDiagonal.value=h,this.particleMaterial.uniforms.screenSize.value=u,this.accumulator.setSize(u,u),this.slippyTilesGroup.remove(this.accumulatorDrawMesh),this.slippyTilesGroup.add(this.accumulatorDrawMesh)}refresh(){super.refresh(),!1===this.getMapOrThrow().isGlobeProjection()?this.refreshMercator():this.refreshGlobe(),this.updateRenderingSize();let e=this.getRendererOrThrow();e.setRenderTarget(this.accumulator),e.setClearAlpha(0),e.clearColor(),e.setRenderTarget(null),this.randomizeParticles(!0)}prerenderInternal(){let e=performance.now(),t=e-this.lastRenderTime,o=this.forceRender||t>this.rttTimestep||!this.prevRenderTime;o&&(this.forceRender=!1,this.rttMaterial.uniforms.timestep.value=this.particleSpeed*t,this.getRendererOrThrow().setRenderTarget(this.flipFlop?this.particleTexture1:this.particleTexture0),this.getRendererOrThrow().copyFramebufferToTexture(new Q.FM8(0,0),this.particleTexturePrev),this.prevRenderTime=this.lastRenderTime,this.lastRenderTime=e,!1===this.getMapOrThrow().isGlobeProjection()?this.prerenderInternalMercator():this.prerenderInternalGlobe(),this.particleMaterial.uniforms.rttTexture.value=(this.flipFlop?this.particleTexture1:this.particleTexture0).texture,this.tileGridPass=0===this.tileGridPass?1:0);let r=this.lastRenderTime-this.prevRenderTime;this.particleMaterial.uniforms.extrapolationFactor.value=o?0:t/r,this.particleMaterial.uniforms.renderStepSize.value=r/1e3;let l=this.getRendererOrThrow();if(l.setRenderTarget(this.accumulator),l.render(this.particleBackground,this.camera),l.render(this.particles,this.camera),l.setRenderTarget(null),void 0!==this.particlesTexturePreview){let e=this.accumulator.width,t=this.accumulator.height,o=new Uint8Array(e*t*4);l.readRenderTargetPixels(this.accumulator,0,0,e,t,o),this.particlesTexturePreview.update(o,e,t)}}refreshMercator(){let e=this.getVisibleExtent(this.extentScale);if(!e)throw Error("The extent is null");let t=Math.max(e[2]-e[0],e[3]-e[1])/eF,o=[.5+(e[2]+e[0])/2/eF-t/2,.5-(e[3]+e[1])/2/eF-t/2];this.rttMaterial.uniforms.rttScale.value=t,this.rttMaterial.uniforms.rttXY.value=o}prerenderInternalMercator(){let e=this.slippyTiles.length;for(let t=0;t<e;t++){let e=this.slippyTiles[0].length;for(let o=0;o<e;o++){let e=this.slippyTiles[t][o];if(!e.visible)continue;let r=e.material.uniforms;if(!r)continue;let l=e.scale.x;this.rttMaterial.uniforms.tileScale.value=l,this.rttMaterial.uniforms.tilePosition.value=[e.position.x/l-.5,-e.position.y/l-.5],this.rttMaterial.uniforms.tileGridPass.value=this.tileGridPass,this.rttMaterial.uniforms.time.value=r.time.value,this.rttMaterial.uniforms.tex0.value=r.tex0.value,this.rttMaterial.uniforms.tex0xy.value=r.tex0xy.value,this.rttMaterial.uniforms.tex0size.value=r.tex0size.value,this.rttMaterial.uniforms.tex1.value=r.tex1.value,this.rttMaterial.uniforms.tex1xy.value=r.tex1xy.value,this.rttMaterial.uniforms.tex1size.value=r.tex1size.value,this.rttMaterial.uniforms.rttTexture.value=(this.flipFlop?this.particleTexture1:this.particleTexture0).texture;let a=this.getRendererOrThrow();a.setRenderTarget(this.flipFlop?this.particleTexture0:this.particleTexture1),a.render(this.rttScene,this.rttCamera),this.flipFlop=!this.flipFlop}}}refreshGlobe(){this.rttMaterial.uniforms.rttScale.value=1,this.rttMaterial.uniforms.rttXY.value=[0,0]}prerenderInternalGlobe(){let e=[...this.globeTilesService.currentTilesMeshes.entries()].map(([e])=>e),t=this.getMapOrThrow().getCenter(),o=this.getMapOrThrow().getZoom(),r=Math.floor(Math.sqrt(e.length)),l=t.lng>=0?Math.floor(t.lng/90):-1*Math.floor(Math.abs(t.lng)/90),a=0;if(this.globeTilesService.visibleTilesAreaMesh.rotation.y=0,o<=4){let e=l*Q.M8C.degToRad(180);this.globeTilesService.visibleTilesAreaMesh.rotation.y=e,a=r/2*l}let{frameA:s,frameB:n,mix:u}=this.getCurrentFrames();if(!(!s||!n))for(let t of e){let o=e.indexOf(t),l=(Math.floor(o/r)+a+r)%r,h=o%r;this.rttMaterial.uniforms.tileScale.value=1/r,this.rttMaterial.uniforms.tilePosition.value=[l,h];let c=this.getTilesPair(s,n,t);if(null===c)continue;let{tileA:m,tileB:d}=c;this.rttMaterial.uniforms.tileGridPass.value=this.tileGridPass,this.rttMaterial.uniforms.time.value=u,this.rttMaterial.uniforms.tex0.value=m.tile.texture,this.rttMaterial.uniforms.tex0xy.value=m.xy,this.rttMaterial.uniforms.tex0size.value=m.size,this.rttMaterial.uniforms.tex1.value=d.tile.texture,this.rttMaterial.uniforms.tex1xy.value=d.xy,this.rttMaterial.uniforms.tex1size.value=d.size,this.rttMaterial.uniforms.rttTexture.value=(this.flipFlop?this.particleTexture1:this.particleTexture0).texture;let v=this.getRendererOrThrow();v.setRenderTarget(this.flipFlop?this.particleTexture0:this.particleTexture1),v.render(this.rttScene,this.rttCamera),this.flipFlop=!this.flipFlop}}renderInternal(){if(!1===this.getMapOrThrow().isGlobeProjection()){let e=this.rttMaterial.uniforms.rttScale.value;this.accumulatorDrawMesh.position.x=.5*e+this.rttMaterial.uniforms.rttXY.value[0],this.accumulatorDrawMesh.position.y=-.5*e-this.rttMaterial.uniforms.rttXY.value[1],this.accumulatorDrawMesh.scale.x=this.accumulatorDrawMesh.scale.y=e}}}class tL extends tD{constructor(e={}){super(e.id||"MapTiler Precipitation",null,null,null),el(this,"constructorOptions"),el(this,"isSourceReady",!1),el(this,"colorRamp"),this.constructorOptions=e}async onAdd(e,t){let o=e.getSdkConfig().apiKey,r=e.getMaptilerSessionId();ej.hasData()||await ej.fetchLatest(o,r);let l=ej.getPrecipitationData();if(!l)throw Error("The latest weather data is not avaialble");for(let a of(this.colorRamp=this.constructorOptions.colorramp??eU.builtin.PRECIPITATION,this.init({minZoom:l.metadata.minzoom,maxZoom:l.metadata.maxzoom,repaintOnPausedAnimation:!1},[new eL({decode:{channel:l.metadata.weather_variable.decoding.channels.toLowerCase(),min:l.metadata.weather_variable.decoding.min,max:l.metadata.weather_variable.decoding.max},stops:this.colorRamp,smooth:this.constructorOptions.smooth??!0,opacity:this.constructorOptions.opacity??1})]),super.onAdd(e,t),ej.getSourcesAndTimestamps(l,o,r)))this.addSource(a.timestamp,a.source);let a=+new Date/1e3;a>=this.getAnimationStart()&&a<=this.getAnimationEnd()&&this.setAnimationTime(a),this.isSourceReady=!0,this.emit("sourceReady",{map:e,layer:this})}getIsSourceReady(){return this.isSourceReady}pickAt(e,t,o={}){let r=super.pick(e,t,o);return r?{value:r[0],valueImperial:r[0]/2.54}:null}getColorRamp(){return this.colorRamp}onSourceReadyAsync(){return new Promise(e=>{if(this.isSourceReady)return e();this.once("sourceReady",()=>{e()})})}}class tG extends tD{constructor(e={}){super(e.id||"MapTiler Temperature",null,null,null),el(this,"constructorOptions"),el(this,"isSourceReady",!1),el(this,"colorRamp"),this.constructorOptions=e}async onAdd(e,t){let o=e.getSdkConfig().apiKey,r=e.getMaptilerSessionId();ej.hasData()||await ej.fetchLatest(o,r);let l=ej.getTemperatureData();if(!l)throw Error("The latest weather data is not avaialble");for(let a of(this.colorRamp=this.constructorOptions.colorramp??eU.builtin.TEMPERATURE_2,this.init({minZoom:l.metadata.minzoom,maxZoom:l.metadata.maxzoom,repaintOnPausedAnimation:!1},[new eL({decode:{channel:l.metadata.weather_variable.decoding.channels.toLowerCase(),min:l.metadata.weather_variable.decoding.min,max:l.metadata.weather_variable.decoding.max},stops:this.colorRamp,smooth:this.constructorOptions.smooth??!0,opacity:this.constructorOptions.opacity??1})]),super.onAdd(e,t),ej.getSourcesAndTimestamps(l,o,r)))this.addSource(a.timestamp,a.source);let a=+new Date/1e3;a>=this.getAnimationStart()&&a<=this.getAnimationEnd()&&this.setAnimationTime(a),this.isSourceReady=!0,this.emit("sourceReady",{map:e,layer:this})}getIsSourceReady(){return this.isSourceReady}pickAt(e,t,o={}){let r=super.pick(e,t,o);if(!r)return null;let l=r[0];return{value:l,valueImperial:32+9*l/5}}getColorRamp(){return this.colorRamp}onSourceReadyAsync(){return new Promise(e=>{if(this.isSourceReady)return e();this.once("sourceReady",()=>{e()})})}}class tU extends tD{constructor(e={}){super(e.id||"MapTiler Radar",null,null,null),el(this,"constructorOptions"),el(this,"isSourceReady",!1),el(this,"colorRamp"),this.constructorOptions=e}async onAdd(e,t){let o=e.getSdkConfig().apiKey,r=e.getMaptilerSessionId();ej.hasData()||await ej.fetchLatest(o,r);let l=ej.getRadarData();if(!l)throw Error("The latest weather data is not avaialble");for(let a of(this.colorRamp=this.constructorOptions.colorramp??eU.builtin.RADAR,this.init({minZoom:l.metadata.minzoom,maxZoom:l.metadata.maxzoom,repaintOnPausedAnimation:!1},[new eL({decode:{channel:l.metadata.weather_variable.decoding.channels.toLowerCase(),min:l.metadata.weather_variable.decoding.min,max:l.metadata.weather_variable.decoding.max},stops:this.colorRamp,smooth:this.constructorOptions.smooth??!0,opacity:this.constructorOptions.opacity??1})]),super.onAdd(e,t),ej.getSourcesAndTimestamps(l,o,r)))this.addSource(a.timestamp,a.source);let a=+new Date/1e3;a>=this.getAnimationStart()&&a<=this.getAnimationEnd()&&this.setAnimationTime(a),this.isSourceReady=!0,this.emit("sourceReady",{map:e,layer:this})}getIsSourceReady(){return this.isSourceReady}pickAt(e,t,o={}){let r=super.pick(e,t,o);return r?{value:r[0]}:null}getColorRamp(){return this.colorRamp}onSourceReadyAsync(){return new Promise(e=>{if(this.isSourceReady)return e();this.once("sourceReady",()=>{e()})})}}let t$={maxAmount:128,color:[255,255,255,0],density:200,size:1,speed:.0015,refreshInterval:200,fadeFactor:.04,opacity:1,colorramp:eU.builtin.VIRIDIS,smooth:!0,fastColor:[255,255,255,255],fastSpeed:3,fastIsLarger:!0};class tB extends tW{constructor(e={}){super(e.id||"MapTiler Wind",null,null,null),el(this,"constructorOptions"),el(this,"isSourceReady",!1),el(this,"colorRamp"),this.constructorOptions=e}async onAdd(e,t){let o=e.getSdkConfig().apiKey,r=e.getMaptilerSessionId();ej.hasData()||await ej.fetchLatest(o,r);let l=ej.getWindData();if(!l)throw Error("The latest weather data is not avaialble");for(let a of(this.colorRamp=this.constructorOptions.colorramp??t$.colorramp.scale(0,40),this.initParticle({minZoom:l.metadata.minzoom,maxZoom:l.metadata.maxzoom},{decodeChannels:l.metadata.weather_variable.decoding.channels.toLowerCase(),decodeMin:l.metadata.weather_variable.decoding.min,decodeMax:l.metadata.weather_variable.decoding.max,decodeAsWaves:!1,maxAmount:this.constructorOptions.maxAmount??t$.maxAmount,color:this.constructorOptions.color??t$.color,fastColor:this.constructorOptions.fastColor??t$.fastColor,fastSpeed:this.constructorOptions.fastSpeed??t$.fastSpeed,density:this.constructorOptions.density??t$.density,size:this.constructorOptions.size??t$.size,speed:this.constructorOptions.speed??t$.speed,refreshInterval:this.constructorOptions.refreshInterval??t$.refreshInterval,fadeFactor:this.constructorOptions.fadeFactor??t$.fadeFactor,angleDirectionShiftSkip:30,uniformSpeed:!0,fastIsLarger:this.constructorOptions.fastIsLarger??t$.fastIsLarger},[new eL({decode:{channel:l.metadata.weather_variable.decoding.channels.toLowerCase(),min:l.metadata.weather_variable.decoding.min,max:l.metadata.weather_variable.decoding.max},stops:this.colorRamp,smooth:this.constructorOptions.smooth??t$.smooth,opacity:this.constructorOptions.opacity??t$.opacity})]),super.onAdd(e,t),ej.getSourcesAndTimestamps(l,o,r)))this.addSource(a.timestamp,a.source);let a=+new Date/1e3;a>=this.getAnimationStart()&&a<=this.getAnimationEnd()&&this.setAnimationTime(a),this.isSourceReady=!0,this.emit("sourceReady",{map:e,layer:this})}getIsSourceReady(){return this.isSourceReady}pickAt(e,t,o={}){let r=super.pick(e,t,o);if(!r)return null;let l=r[0],a=r[1],s=Math.sqrt(l**2+a**2),n=180*Math.atan2(l,a)/Math.PI;return{speedMetersPerSecond:s,speedKilometersPerHour:3.6*s,speedMilesPerHour:2.23694*s,speedFeetPerSecond:3.28084*s,speedKnots:1.94384*s,directionAngle:n,compassDirection:eN(n+180)}}getColorRamp(){return this.colorRamp}onSourceReadyAsync(){return new Promise(e=>{if(this.isSourceReady)return e();this.once("sourceReady",()=>{e()})})}}}}]);