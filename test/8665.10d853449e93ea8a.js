try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="5351df02-a73d-4b29-8d93-10ac0adf759f",e._sentryDebugIdIdentifier="sentry-dbid-5351df02-a73d-4b29-8d93-10ac0adf759f")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8665],{8665:function(e,t,i){i.r(t),i.d(t,{Alert:function(){return eb},Button:function(){return ey},Carousel:function(){return eG},Collapse:function(){return ti},Dropdown:function(){return tk},Modal:function(){return t7},Offcanvas:function(){return ip},Popover:function(){return iH},ScrollSpy:function(){return iY},Tab:function(){return sl},Toast:function(){return sA},Tooltip:function(){return ix}});var s={};i.r(s),i.d(s,{afterMain:function(){return n.wX},afterRead:function(){return n.r5},afterWrite:function(){return n.MS},applyStyles:function(){return l},arrow:function(){return a.Z},auto:function(){return n.d7},basePlacements:function(){return n.mv},beforeMain:function(){return n.XM},beforeRead:function(){return n.N7},beforeWrite:function(){return n.iv},bottom:function(){return n.I},clippingParents:function(){return n.zV},computeStyles:function(){return c.Z},createPopper:function(){return v},createPopperBase:function(){return m.fi},createPopperLite:function(){return w},detectOverflow:function(){return p.Z},end:function(){return n.ut},eventListeners:function(){return h.Z},flip:function(){return u.Z},hide:function(){return d.Z},left:function(){return n.t$},main:function(){return n.DH},modifierPhases:function(){return n.xs},offset:function(){return f.Z},placements:function(){return n.Ct},popper:function(){return n.k5},popperGenerator:function(){return m.kZ},popperOffsets:function(){return _.Z},preventOverflow:function(){return g.Z},read:function(){return n.ij},reference:function(){return n.YP},right:function(){return n.F2},start:function(){return n.BL},top:function(){return n.we},variationPlacements:function(){return n.bw},viewport:function(){return n.Pj},write:function(){return n.cW}});var n=i(87701),r=i(96333),o=i(62556),l={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach(function(e){var i=t.styles[e]||{},s=t.attributes[e]||{},n=t.elements[e];(0,o.Re)(n)&&(0,r.Z)(n)&&(Object.assign(n.style,i),Object.keys(s).forEach(function(e){var t=s[e];!1===t?n.removeAttribute(e):n.setAttribute(e,!0===t?"":t)}))})},effect:function(e){var t=e.state,i={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,i.popper),t.styles=i,t.elements.arrow&&Object.assign(t.elements.arrow.style,i.arrow),function(){Object.keys(t.elements).forEach(function(e){var s=t.elements[e],n=t.attributes[e]||{},l=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:i[e]).reduce(function(e,t){return e[t]="",e},{});(0,o.Re)(s)&&(0,r.Z)(s)&&(Object.assign(s.style,l),Object.keys(n).forEach(function(e){s.removeAttribute(e)}))})}},requires:["computeStyles"]},a=i(66896),c=i(36531),h=i(82372),u=i(68855),d=i(19892),f=i(82122),_=i(77421),g=i(394),m=i(45704),p=i(6486),b=[h.Z,_.Z,c.Z,l,f.Z,u.Z,g.Z,a.Z,d.Z],v=(0,m.kZ)({defaultModifiers:b}),y=[h.Z,_.Z,c.Z,l],w=(0,m.kZ)({defaultModifiers:y});let A=new Map,E={set(e,t,i){A.has(e)||A.set(e,new Map);let s=A.get(e);if(!s.has(t)&&0!==s.size){console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(s.keys())[0]}.`);return}s.set(t,i)},get:(e,t)=>A.has(e)&&A.get(e).get(t)||null,remove(e,t){if(!A.has(e))return;let i=A.get(e);i.delete(t),0===i.size&&A.delete(e)}},C="transitionend",T=e=>(e&&window.CSS&&window.CSS.escape&&(e=e.replace(/#([^\s"#']+)/g,(e,t)=>`#${CSS.escape(t)}`)),e),k=e=>null==e?`${e}`:Object.prototype.toString.call(e).match(/\s([a-z]+)/i)[1].toLowerCase(),$=e=>{do e+=Math.floor(1e6*Math.random());while(document.getElementById(e));return e},S=e=>{if(!e)return 0;let{transitionDuration:t,transitionDelay:i}=window.getComputedStyle(e),s=Number.parseFloat(t),n=Number.parseFloat(i);return s||n?(t=t.split(",")[0],i=i.split(",")[0],(Number.parseFloat(t)+Number.parseFloat(i))*1e3):0},L=e=>{e.dispatchEvent(new Event(C))},O=e=>!!e&&"object"==typeof e&&(void 0!==e.jquery&&(e=e[0]),void 0!==e.nodeType),I=e=>O(e)?e.jquery?e[0]:e:"string"==typeof e&&e.length>0?document.querySelector(T(e)):null,N=e=>{if(!O(e)||0===e.getClientRects().length)return!1;let t="visible"===getComputedStyle(e).getPropertyValue("visibility"),i=e.closest("details:not([open])");if(!i)return t;if(i!==e){let t=e.closest("summary");if(t&&t.parentNode!==i||null===t)return!1}return t},D=e=>!!(!e||e.nodeType!==Node.ELEMENT_NODE||e.classList.contains("disabled"))||(void 0!==e.disabled?e.disabled:e.hasAttribute("disabled")&&"false"!==e.getAttribute("disabled")),P=e=>{if(!document.documentElement.attachShadow)return null;if("function"==typeof e.getRootNode){let t=e.getRootNode();return t instanceof ShadowRoot?t:null}return e instanceof ShadowRoot?e:e.parentNode?P(e.parentNode):null},M=()=>{},x=e=>{e.offsetHeight},j=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,F=[],H=e=>{"loading"===document.readyState?(F.length||document.addEventListener("DOMContentLoaded",()=>{for(let e of F)e()}),F.push(e)):e()},z=()=>"rtl"===document.documentElement.dir,W=e=>{H(()=>{let t=j();if(t){let i=e.NAME,s=t.fn[i];t.fn[i]=e.jQueryInterface,t.fn[i].Constructor=e,t.fn[i].noConflict=()=>(t.fn[i]=s,e.jQueryInterface)}})},q=(e,t=[],i=e)=>"function"==typeof e?e(...t):i,R=(e,t,i=!0)=>{if(!i){q(e);return}let s=S(t)+5,n=!1,r=({target:i})=>{i===t&&(n=!0,t.removeEventListener(C,r),q(e))};t.addEventListener(C,r),setTimeout(()=>{n||L(t)},s)},Z=(e,t,i,s)=>{let n=e.length,r=e.indexOf(t);return -1===r?!i&&s?e[n-1]:e[0]:(r+=i?1:-1,s&&(r=(r+n)%n),e[Math.max(0,Math.min(r,n-1))])},B=/[^.]*(?=\..*)\.|.*/,K=/\..*/,V=/::\d+$/,Q={},X=1,Y={mouseenter:"mouseover",mouseleave:"mouseout"},U=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function G(e,t){return t&&`${t}::${X++}`||e.uidEvent||X++}function J(e){let t=G(e);return e.uidEvent=t,Q[t]=Q[t]||{},Q[t]}function ee(e,t,i=null){return Object.values(e).find(e=>e.callable===t&&e.delegationSelector===i)}function et(e,t,i){let s="string"==typeof t,n=en(e);return U.has(n)||(n=e),[s,s?i:t||i,n]}function ei(e,t,i,s,n){var r,o;if("string"!=typeof t||!e)return;let[l,a,c]=et(t,i,s);if(t in Y){let e;e=a,a=function(t){if(!t.relatedTarget||t.relatedTarget!==t.delegateTarget&&!t.delegateTarget.contains(t.relatedTarget))return e.call(this,t)}}let h=J(e),u=h[c]||(h[c]={}),d=ee(u,a,l?i:null);if(d){d.oneOff=d.oneOff&&n;return}let f=G(a,t.replace(B,"")),_=l?(r=a,function t(s){let n=e.querySelectorAll(i);for(let{target:o}=s;o&&o!==this;o=o.parentNode)for(let l of n)if(l===o)return eo(s,{delegateTarget:o}),t.oneOff&&er.off(e,s.type,i,r),r.apply(o,[s])}):(o=a,function t(i){return eo(i,{delegateTarget:e}),t.oneOff&&er.off(e,i.type,o),o.apply(e,[i])});_.delegationSelector=l?i:null,_.callable=a,_.oneOff=n,_.uidEvent=f,u[f]=_,e.addEventListener(c,_,l)}function es(e,t,i,s,n){let r=ee(t[i],s,n);r&&(e.removeEventListener(i,r,!!n),delete t[i][r.uidEvent])}function en(e){return Y[e=e.replace(K,"")]||e}let er={on(e,t,i,s){ei(e,t,i,s,!1)},one(e,t,i,s){ei(e,t,i,s,!0)},off(e,t,i,s){if("string"!=typeof t||!e)return;let[n,r,o]=et(t,i,s),l=o!==t,a=J(e),c=a[o]||{},h=t.startsWith(".");if(void 0!==r){if(!Object.keys(c).length)return;es(e,a,o,r,n?i:null);return}if(h)for(let i of Object.keys(a))!function(e,t,i,s){for(let[n,r]of Object.entries(t[i]||{}))n.includes(s)&&es(e,t,i,r.callable,r.delegationSelector)}(e,a,i,t.slice(1));for(let[i,s]of Object.entries(c)){let n=i.replace(V,"");(!l||t.includes(n))&&es(e,a,o,s.callable,s.delegationSelector)}},trigger(e,t,i){if("string"!=typeof t||!e)return null;let s=j(),n=en(t),r=null,o=!0,l=!0,a=!1;t!==n&&s&&(r=s.Event(t,i),s(e).trigger(r),o=!r.isPropagationStopped(),l=!r.isImmediatePropagationStopped(),a=r.isDefaultPrevented());let c=eo(new Event(t,{bubbles:o,cancelable:!0}),i);return a&&c.preventDefault(),l&&e.dispatchEvent(c),c.defaultPrevented&&r&&r.preventDefault(),c}};function eo(e,t={}){for(let[i,s]of Object.entries(t))try{e[i]=s}catch(t){Object.defineProperty(e,i,{configurable:!0,get:()=>s})}return e}function el(e){if("true"===e)return!0;if("false"===e)return!1;if(e===Number(e).toString())return Number(e);if(""===e||"null"===e)return null;if("string"!=typeof e)return e;try{return JSON.parse(decodeURIComponent(e))}catch(t){return e}}function ea(e){return e.replace(/[A-Z]/g,e=>`-${e.toLowerCase()}`)}let ec={setDataAttribute(e,t,i){e.setAttribute(`data-bs-${ea(t)}`,i)},removeDataAttribute(e,t){e.removeAttribute(`data-bs-${ea(t)}`)},getDataAttributes(e){if(!e)return{};let t={};for(let i of Object.keys(e.dataset).filter(e=>e.startsWith("bs")&&!e.startsWith("bsConfig"))){let s=i.replace(/^bs/,"");t[s=s.charAt(0).toLowerCase()+s.slice(1,s.length)]=el(e.dataset[i])}return t},getDataAttribute:(e,t)=>el(e.getAttribute(`data-bs-${ea(t)}`))};class eh{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw Error('You have to implement the static method "NAME", for each component!')}_getConfig(e){return e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e}_mergeConfigObj(e,t){let i=O(t)?ec.getDataAttribute(t,"config"):{};return{...this.constructor.Default,..."object"==typeof i?i:{},...O(t)?ec.getDataAttributes(t):{},..."object"==typeof e?e:{}}}_typeCheckConfig(e,t=this.constructor.DefaultType){for(let[i,s]of Object.entries(t)){let t=e[i],n=O(t)?"element":k(t);if(!new RegExp(s).test(n))throw TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${i}" provided type "${n}" but expected type "${s}".`)}}}class eu extends eh{constructor(e,t){if(super(),!(e=I(e)))return;this._element=e,this._config=this._getConfig(t),E.set(this._element,this.constructor.DATA_KEY,this)}dispose(){for(let e of(E.remove(this._element,this.constructor.DATA_KEY),er.off(this._element,this.constructor.EVENT_KEY),Object.getOwnPropertyNames(this)))this[e]=null}_queueCallback(e,t,i=!0){R(e,t,i)}_getConfig(e){return e=this._mergeConfigObj(e,this._element),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}static getInstance(e){return E.get(I(e),this.DATA_KEY)}static getOrCreateInstance(e,t={}){return this.getInstance(e)||new this(e,"object"==typeof t?t:null)}static get VERSION(){return"5.3.3"}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(e){return`${e}${this.EVENT_KEY}`}}let ed=e=>{let t=e.getAttribute("data-bs-target");if(!t||"#"===t){let i=e.getAttribute("href");if(!i||!i.includes("#")&&!i.startsWith("."))return null;i.includes("#")&&!i.startsWith("#")&&(i=`#${i.split("#")[1]}`),t=i&&"#"!==i?i.trim():null}return t?t.split(",").map(e=>T(e)).join(","):null},ef={find:(e,t=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(t,e)),findOne:(e,t=document.documentElement)=>Element.prototype.querySelector.call(t,e),children:(e,t)=>[].concat(...e.children).filter(e=>e.matches(t)),parents(e,t){let i=[],s=e.parentNode.closest(t);for(;s;)i.push(s),s=s.parentNode.closest(t);return i},prev(e,t){let i=e.previousElementSibling;for(;i;){if(i.matches(t))return[i];i=i.previousElementSibling}return[]},next(e,t){let i=e.nextElementSibling;for(;i;){if(i.matches(t))return[i];i=i.nextElementSibling}return[]},focusableChildren(e){let t=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map(e=>`${e}:not([tabindex^="-"])`).join(",");return this.find(t,e).filter(e=>!D(e)&&N(e))},getSelectorFromElement(e){let t=ed(e);return t&&ef.findOne(t)?t:null},getElementFromSelector(e){let t=ed(e);return t?ef.findOne(t):null},getMultipleElementsFromSelector(e){let t=ed(e);return t?ef.find(t):[]}},e_=(e,t="hide")=>{let i=`click.dismiss${e.EVENT_KEY}`,s=e.NAME;er.on(document,i,`[data-bs-dismiss="${s}"]`,function(i){if(["A","AREA"].includes(this.tagName)&&i.preventDefault(),D(this))return;let n=ef.getElementFromSelector(this)||this.closest(`.${s}`);e.getOrCreateInstance(n)[t]()})},eg=".bs.alert",em=`close${eg}`,ep=`closed${eg}`;class eb extends eu{static get NAME(){return"alert"}close(){if(er.trigger(this._element,em).defaultPrevented)return;this._element.classList.remove("show");let e=this._element.classList.contains("fade");this._queueCallback(()=>this._destroyElement(),this._element,e)}_destroyElement(){this._element.remove(),er.trigger(this._element,ep),this.dispose()}static jQueryInterface(e){return this.each(function(){let t=eb.getOrCreateInstance(this);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw TypeError(`No method named "${e}"`);t[e](this)}})}}e_(eb,"close"),W(eb);let ev='[data-bs-toggle="button"]';class ey extends eu{static get NAME(){return"button"}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))}static jQueryInterface(e){return this.each(function(){let t=ey.getOrCreateInstance(this);"toggle"===e&&t[e]()})}}er.on(document,"click.bs.button.data-api",ev,e=>{e.preventDefault();let t=e.target.closest(ev);ey.getOrCreateInstance(t).toggle()}),W(ey);let ew=".bs.swipe",eA=`touchstart${ew}`,eE=`touchmove${ew}`,eC=`touchend${ew}`,eT=`pointerdown${ew}`,ek=`pointerup${ew}`,e$={endCallback:null,leftCallback:null,rightCallback:null},eS={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class eL extends eh{constructor(e,t){if(super(),this._element=e,!e||!eL.isSupported())return;this._config=this._getConfig(t),this._deltaX=0,this._supportPointerEvents=!!window.PointerEvent,this._initEvents()}static get Default(){return e$}static get DefaultType(){return eS}static get NAME(){return"swipe"}dispose(){er.off(this._element,ew)}_start(e){if(!this._supportPointerEvents){this._deltaX=e.touches[0].clientX;return}this._eventIsPointerPenTouch(e)&&(this._deltaX=e.clientX)}_end(e){this._eventIsPointerPenTouch(e)&&(this._deltaX=e.clientX-this._deltaX),this._handleSwipe(),q(this._config.endCallback)}_move(e){this._deltaX=e.touches&&e.touches.length>1?0:e.touches[0].clientX-this._deltaX}_handleSwipe(){let e=Math.abs(this._deltaX);if(e<=40)return;let t=e/this._deltaX;this._deltaX=0,t&&q(t>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(er.on(this._element,eT,e=>this._start(e)),er.on(this._element,ek,e=>this._end(e)),this._element.classList.add("pointer-event")):(er.on(this._element,eA,e=>this._start(e)),er.on(this._element,eE,e=>this._move(e)),er.on(this._element,eC,e=>this._end(e)))}_eventIsPointerPenTouch(e){return this._supportPointerEvents&&("pen"===e.pointerType||"touch"===e.pointerType)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}let eO=".bs.carousel",eI=".data-api",eN="next",eD="prev",eP="left",eM="right",ex=`slide${eO}`,ej=`slid${eO}`,eF=`keydown${eO}`,eH=`mouseenter${eO}`,ez=`mouseleave${eO}`,eW=`dragstart${eO}`,eq=`load${eO}${eI}`,eR=`click${eO}${eI}`,eZ="carousel",eB="active",eK=".active",eV=".carousel-item",eQ=eK+eV,eX={ArrowLeft:eM,ArrowRight:eP},eY={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},eU={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class eG extends eu{constructor(e,t){super(e,t),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=ef.findOne(".carousel-indicators",this._element),this._addEventListeners(),this._config.ride===eZ&&this.cycle()}static get Default(){return eY}static get DefaultType(){return eU}static get NAME(){return"carousel"}next(){this._slide(eN)}nextWhenVisible(){!document.hidden&&N(this._element)&&this.next()}prev(){this._slide(eD)}pause(){this._isSliding&&L(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval(()=>this.nextWhenVisible(),this._config.interval)}_maybeEnableCycle(){if(this._config.ride){if(this._isSliding){er.one(this._element,ej,()=>this.cycle());return}this.cycle()}}to(e){let t=this._getItems();if(e>t.length-1||e<0)return;if(this._isSliding){er.one(this._element,ej,()=>this.to(e));return}let i=this._getItemIndex(this._getActive());i!==e&&this._slide(e>i?eN:eD,t[e])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(e){return e.defaultInterval=e.interval,e}_addEventListeners(){this._config.keyboard&&er.on(this._element,eF,e=>this._keydown(e)),"hover"===this._config.pause&&(er.on(this._element,eH,()=>this.pause()),er.on(this._element,ez,()=>this._maybeEnableCycle())),this._config.touch&&eL.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(let e of ef.find(".carousel-item img",this._element))er.on(e,eW,e=>e.preventDefault());this._swipeHelper=new eL(this._element,{leftCallback:()=>this._slide(this._directionToOrder(eP)),rightCallback:()=>this._slide(this._directionToOrder(eM)),endCallback:()=>{"hover"===this._config.pause&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout(()=>this._maybeEnableCycle(),500+this._config.interval))}})}_keydown(e){if(/input|textarea/i.test(e.target.tagName))return;let t=eX[e.key];t&&(e.preventDefault(),this._slide(this._directionToOrder(t)))}_getItemIndex(e){return this._getItems().indexOf(e)}_setActiveIndicatorElement(e){if(!this._indicatorsElement)return;let t=ef.findOne(eK,this._indicatorsElement);t.classList.remove(eB),t.removeAttribute("aria-current");let i=ef.findOne(`[data-bs-slide-to="${e}"]`,this._indicatorsElement);i&&(i.classList.add(eB),i.setAttribute("aria-current","true"))}_updateInterval(){let e=this._activeElement||this._getActive();if(!e)return;let t=Number.parseInt(e.getAttribute("data-bs-interval"),10);this._config.interval=t||this._config.defaultInterval}_slide(e,t=null){if(this._isSliding)return;let i=this._getActive(),s=e===eN,n=t||Z(this._getItems(),i,s,this._config.wrap);if(n===i)return;let r=this._getItemIndex(n),o=t=>er.trigger(this._element,t,{relatedTarget:n,direction:this._orderToDirection(e),from:this._getItemIndex(i),to:r});if(o(ex).defaultPrevented||!i||!n)return;let l=!!this._interval;this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(r),this._activeElement=n;let a=s?"carousel-item-start":"carousel-item-end",c=s?"carousel-item-next":"carousel-item-prev";n.classList.add(c),x(n),i.classList.add(a),n.classList.add(a),this._queueCallback(()=>{n.classList.remove(a,c),n.classList.add(eB),i.classList.remove(eB,c,a),this._isSliding=!1,o(ej)},i,this._isAnimated()),l&&this.cycle()}_isAnimated(){return this._element.classList.contains("slide")}_getActive(){return ef.findOne(eQ,this._element)}_getItems(){return ef.find(eV,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(e){return z()?e===eP?eD:eN:e===eP?eN:eD}_orderToDirection(e){return z()?e===eD?eP:eM:e===eD?eM:eP}static jQueryInterface(e){return this.each(function(){let t=eG.getOrCreateInstance(this,e);if("number"==typeof e){t.to(e);return}if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw TypeError(`No method named "${e}"`);t[e]()}})}}er.on(document,eR,"[data-bs-slide], [data-bs-slide-to]",function(e){let t=ef.getElementFromSelector(this);if(!t||!t.classList.contains(eZ))return;e.preventDefault();let i=eG.getOrCreateInstance(t),s=this.getAttribute("data-bs-slide-to");if(s){i.to(s),i._maybeEnableCycle();return}if("next"===ec.getDataAttribute(this,"slide")){i.next(),i._maybeEnableCycle();return}i.prev(),i._maybeEnableCycle()}),er.on(window,eq,()=>{for(let e of ef.find('[data-bs-ride="carousel"]'))eG.getOrCreateInstance(e)}),W(eG);let eJ=".bs.collapse",e0=`show${eJ}`,e1=`shown${eJ}`,e5=`hide${eJ}`,e3=`hidden${eJ}`,e2=`click${eJ}.data-api`,e6="show",e8="collapse",e7="collapsing",e9=`:scope .${e8} .${e8}`,e4='[data-bs-toggle="collapse"]',te={parent:null,toggle:!0},tt={parent:"(null|element)",toggle:"boolean"};class ti extends eu{constructor(e,t){for(let i of(super(e,t),this._isTransitioning=!1,this._triggerArray=[],ef.find(e4))){let e=ef.getSelectorFromElement(i),t=ef.find(e).filter(e=>e===this._element);null!==e&&t.length&&this._triggerArray.push(i)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return te}static get DefaultType(){return tt}static get NAME(){return"collapse"}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let e=[];if(this._config.parent&&(e=this._getFirstLevelChildren(".collapse.show, .collapse.collapsing").filter(e=>e!==this._element).map(e=>ti.getOrCreateInstance(e,{toggle:!1}))),e.length&&e[0]._isTransitioning||er.trigger(this._element,e0).defaultPrevented)return;for(let t of e)t.hide();let t=this._getDimension();this._element.classList.remove(e8),this._element.classList.add(e7),this._element.style[t]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;let i=t[0].toUpperCase()+t.slice(1),s=`scroll${i}`;this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(e7),this._element.classList.add(e8,e6),this._element.style[t]="",er.trigger(this._element,e1)},this._element,!0),this._element.style[t]=`${this._element[s]}px`}hide(){if(this._isTransitioning||!this._isShown()||er.trigger(this._element,e5).defaultPrevented)return;let e=this._getDimension();for(let t of(this._element.style[e]=`${this._element.getBoundingClientRect()[e]}px`,x(this._element),this._element.classList.add(e7),this._element.classList.remove(e8,e6),this._triggerArray)){let e=ef.getElementFromSelector(t);e&&!this._isShown(e)&&this._addAriaAndCollapsedClass([t],!1)}this._isTransitioning=!0,this._element.style[e]="",this._queueCallback(()=>{this._isTransitioning=!1,this._element.classList.remove(e7),this._element.classList.add(e8),er.trigger(this._element,e3)},this._element,!0)}_isShown(e=this._element){return e.classList.contains(e6)}_configAfterMerge(e){return e.toggle=!!e.toggle,e.parent=I(e.parent),e}_getDimension(){return this._element.classList.contains("collapse-horizontal")?"width":"height"}_initializeChildren(){if(this._config.parent)for(let e of this._getFirstLevelChildren(e4)){let t=ef.getElementFromSelector(e);t&&this._addAriaAndCollapsedClass([e],this._isShown(t))}}_getFirstLevelChildren(e){let t=ef.find(e9,this._config.parent);return ef.find(e,this._config.parent).filter(e=>!t.includes(e))}_addAriaAndCollapsedClass(e,t){if(e.length)for(let i of e)i.classList.toggle("collapsed",!t),i.setAttribute("aria-expanded",t)}static jQueryInterface(e){let t={};return"string"==typeof e&&/show|hide/.test(e)&&(t.toggle=!1),this.each(function(){let i=ti.getOrCreateInstance(this,t);if("string"==typeof e){if(void 0===i[e])throw TypeError(`No method named "${e}"`);i[e]()}})}}er.on(document,e2,e4,function(e){for(let t of(("A"===e.target.tagName||e.delegateTarget&&"A"===e.delegateTarget.tagName)&&e.preventDefault(),ef.getMultipleElementsFromSelector(this)))ti.getOrCreateInstance(t,{toggle:!1}).toggle()}),W(ti);let ts="dropdown",tn=".bs.dropdown",tr=".data-api",to="ArrowDown",tl=`hide${tn}`,ta=`hidden${tn}`,tc=`show${tn}`,th=`shown${tn}`,tu=`click${tn}${tr}`,td=`keydown${tn}${tr}`,tf=`keyup${tn}${tr}`,t_="show",tg='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',tm=`${tg}.${t_}`,tp=".dropdown-menu",tb=z()?"top-end":"top-start",tv=z()?"top-start":"top-end",ty=z()?"bottom-end":"bottom-start",tw=z()?"bottom-start":"bottom-end",tA=z()?"left-start":"right-start",tE=z()?"right-start":"left-start",tC={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},tT={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class tk extends eu{constructor(e,t){super(e,t),this._popper=null,this._parent=this._element.parentNode,this._menu=ef.next(this._element,tp)[0]||ef.prev(this._element,tp)[0]||ef.findOne(tp,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return tC}static get DefaultType(){return tT}static get NAME(){return ts}toggle(){return this._isShown()?this.hide():this.show()}show(){if(D(this._element)||this._isShown())return;let e={relatedTarget:this._element};if(!er.trigger(this._element,tc,e).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(".navbar-nav"))for(let e of[].concat(...document.body.children))er.on(e,"mouseover",M);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(t_),this._element.classList.add(t_),er.trigger(this._element,th,e)}}hide(){if(D(this._element)||!this._isShown())return;let e={relatedTarget:this._element};this._completeHide(e)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(e){if(!er.trigger(this._element,tl,e).defaultPrevented){if("ontouchstart"in document.documentElement)for(let e of[].concat(...document.body.children))er.off(e,"mouseover",M);this._popper&&this._popper.destroy(),this._menu.classList.remove(t_),this._element.classList.remove(t_),this._element.setAttribute("aria-expanded","false"),ec.removeDataAttribute(this._menu,"popper"),er.trigger(this._element,ta,e)}}_getConfig(e){if("object"==typeof(e=super._getConfig(e)).reference&&!O(e.reference)&&"function"!=typeof e.reference.getBoundingClientRect)throw TypeError(`${ts.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return e}_createPopper(){if(void 0===s)throw TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)");let e=this._element;"parent"===this._config.reference?e=this._parent:O(this._config.reference)?e=I(this._config.reference):"object"==typeof this._config.reference&&(e=this._config.reference);let t=this._getPopperConfig();this._popper=v(e,this._menu,t)}_isShown(){return this._menu.classList.contains(t_)}_getPlacement(){let e=this._parent;if(e.classList.contains("dropend"))return tA;if(e.classList.contains("dropstart"))return tE;if(e.classList.contains("dropup-center"))return"top";if(e.classList.contains("dropdown-center"))return"bottom";let t="end"===getComputedStyle(this._menu).getPropertyValue("--bs-position").trim();return e.classList.contains("dropup")?t?tv:tb:t?tw:ty}_detectNavbar(){return null!==this._element.closest(".navbar")}_getOffset(){let{offset:e}=this._config;return"string"==typeof e?e.split(",").map(e=>Number.parseInt(e,10)):"function"==typeof e?t=>e(t,this._element):e}_getPopperConfig(){let e={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||"static"===this._config.display)&&(ec.setDataAttribute(this._menu,"popper","static"),e.modifiers=[{name:"applyStyles",enabled:!1}]),{...e,...q(this._config.popperConfig,[e])}}_selectMenuItem({key:e,target:t}){let i=ef.find(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",this._menu).filter(e=>N(e));i.length&&Z(i,t,e===to,!i.includes(t)).focus()}static jQueryInterface(e){return this.each(function(){let t=tk.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw TypeError(`No method named "${e}"`);t[e]()}})}static clearMenus(e){if(2!==e.button&&("keyup"!==e.type||"Tab"===e.key))for(let t of ef.find(tm)){let i=tk.getInstance(t);if(!i||!1===i._config.autoClose)continue;let s=e.composedPath(),n=s.includes(i._menu);if(s.includes(i._element)||"inside"===i._config.autoClose&&!n||"outside"===i._config.autoClose&&n||i._menu.contains(e.target)&&("keyup"===e.type&&"Tab"===e.key||/input|select|option|textarea|form/i.test(e.target.tagName)))continue;let r={relatedTarget:i._element};"click"===e.type&&(r.clickEvent=e),i._completeHide(r)}}static dataApiKeydownHandler(e){let t=/input|textarea/i.test(e.target.tagName),i="Escape"===e.key,s=["ArrowUp",to].includes(e.key);if(!s&&!i||t&&!i)return;e.preventDefault();let n=this.matches(tg)?this:ef.prev(this,tg)[0]||ef.next(this,tg)[0]||ef.findOne(tg,e.delegateTarget.parentNode),r=tk.getOrCreateInstance(n);if(s){e.stopPropagation(),r.show(),r._selectMenuItem(e);return}r._isShown()&&(e.stopPropagation(),r.hide(),n.focus())}}er.on(document,td,tg,tk.dataApiKeydownHandler),er.on(document,td,tp,tk.dataApiKeydownHandler),er.on(document,tu,tk.clearMenus),er.on(document,tf,tk.clearMenus),er.on(document,tu,tg,function(e){e.preventDefault(),tk.getOrCreateInstance(this).toggle()}),W(tk);let t$="backdrop",tS="show",tL=`mousedown.bs.${t$}`,tO={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},tI={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class tN extends eh{constructor(e){super(),this._config=this._getConfig(e),this._isAppended=!1,this._element=null}static get Default(){return tO}static get DefaultType(){return tI}static get NAME(){return t$}show(e){if(!this._config.isVisible){q(e);return}this._append();let t=this._getElement();this._config.isAnimated&&x(t),t.classList.add(tS),this._emulateAnimation(()=>{q(e)})}hide(e){if(!this._config.isVisible){q(e);return}this._getElement().classList.remove(tS),this._emulateAnimation(()=>{this.dispose(),q(e)})}dispose(){this._isAppended&&(er.off(this._element,tL),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){let e=document.createElement("div");e.className=this._config.className,this._config.isAnimated&&e.classList.add("fade"),this._element=e}return this._element}_configAfterMerge(e){return e.rootElement=I(e.rootElement),e}_append(){if(this._isAppended)return;let e=this._getElement();this._config.rootElement.append(e),er.on(e,tL,()=>{q(this._config.clickCallback)}),this._isAppended=!0}_emulateAnimation(e){R(e,this._getElement(),this._config.isAnimated)}}let tD=".bs.focustrap",tP=`focusin${tD}`,tM=`keydown.tab${tD}`,tx="backward",tj={autofocus:!0,trapElement:null},tF={autofocus:"boolean",trapElement:"element"};class tH extends eh{constructor(e){super(),this._config=this._getConfig(e),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return tj}static get DefaultType(){return tF}static get NAME(){return"focustrap"}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),er.off(document,tD),er.on(document,tP,e=>this._handleFocusin(e)),er.on(document,tM,e=>this._handleKeydown(e)),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,er.off(document,tD))}_handleFocusin(e){let{trapElement:t}=this._config;if(e.target===document||e.target===t||t.contains(e.target))return;let i=ef.focusableChildren(t);0===i.length?t.focus():this._lastTabNavDirection===tx?i[i.length-1].focus():i[0].focus()}_handleKeydown(e){"Tab"===e.key&&(this._lastTabNavDirection=e.shiftKey?tx:"forward")}}let tz=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",tW=".sticky-top",tq="padding-right",tR="margin-right";class tZ{constructor(){this._element=document.body}getWidth(){let e=document.documentElement.clientWidth;return Math.abs(window.innerWidth-e)}hide(){let e=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,tq,t=>t+e),this._setElementAttributes(tz,tq,t=>t+e),this._setElementAttributes(tW,tR,t=>t-e)}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,tq),this._resetElementAttributes(tz,tq),this._resetElementAttributes(tW,tR)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(e,t,i){let s=this.getWidth();this._applyManipulationCallback(e,e=>{if(e!==this._element&&window.innerWidth>e.clientWidth+s)return;this._saveInitialAttribute(e,t);let n=window.getComputedStyle(e).getPropertyValue(t);e.style.setProperty(t,`${i(Number.parseFloat(n))}px`)})}_saveInitialAttribute(e,t){let i=e.style.getPropertyValue(t);i&&ec.setDataAttribute(e,t,i)}_resetElementAttributes(e,t){this._applyManipulationCallback(e,e=>{let i=ec.getDataAttribute(e,t);if(null===i){e.style.removeProperty(t);return}ec.removeDataAttribute(e,t),e.style.setProperty(t,i)})}_applyManipulationCallback(e,t){if(O(e)){t(e);return}for(let i of ef.find(e,this._element))t(i)}}let tB=".bs.modal",tK=`hide${tB}`,tV=`hidePrevented${tB}`,tQ=`hidden${tB}`,tX=`show${tB}`,tY=`shown${tB}`,tU=`resize${tB}`,tG=`click.dismiss${tB}`,tJ=`mousedown.dismiss${tB}`,t0=`keydown.dismiss${tB}`,t1=`click${tB}.data-api`,t5="modal-open",t3="show",t2="modal-static",t6={backdrop:!0,focus:!0,keyboard:!0},t8={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class t7 extends eu{constructor(e,t){super(e,t),this._dialog=ef.findOne(".modal-dialog",this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new tZ,this._addEventListeners()}static get Default(){return t6}static get DefaultType(){return t8}static get NAME(){return"modal"}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){this._isShown||this._isTransitioning||er.trigger(this._element,tX,{relatedTarget:e}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(t5),this._adjustDialog(),this._backdrop.show(()=>this._showElement(e)))}hide(){!this._isShown||this._isTransitioning||er.trigger(this._element,tK).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(t3),this._queueCallback(()=>this._hideModal(),this._element,this._isAnimated()))}dispose(){er.off(window,tB),er.off(this._dialog,tB),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new tN({isVisible:!!this._config.backdrop,isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new tH({trapElement:this._element})}_showElement(e){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;let t=ef.findOne(".modal-body",this._dialog);t&&(t.scrollTop=0),x(this._element),this._element.classList.add(t3),this._queueCallback(()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,er.trigger(this._element,tY,{relatedTarget:e})},this._dialog,this._isAnimated())}_addEventListeners(){er.on(this._element,t0,e=>{if("Escape"===e.key){if(this._config.keyboard){this.hide();return}this._triggerBackdropTransition()}}),er.on(window,tU,()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()}),er.on(this._element,tJ,e=>{er.one(this._element,tG,t=>{if(this._element===e.target&&this._element===t.target){if("static"===this._config.backdrop){this._triggerBackdropTransition();return}this._config.backdrop&&this.hide()}})})}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide(()=>{document.body.classList.remove(t5),this._resetAdjustments(),this._scrollBar.reset(),er.trigger(this._element,tQ)})}_isAnimated(){return this._element.classList.contains("fade")}_triggerBackdropTransition(){if(er.trigger(this._element,tV).defaultPrevented)return;let e=this._element.scrollHeight>document.documentElement.clientHeight,t=this._element.style.overflowY;"hidden"===t||this._element.classList.contains(t2)||(e||(this._element.style.overflowY="hidden"),this._element.classList.add(t2),this._queueCallback(()=>{this._element.classList.remove(t2),this._queueCallback(()=>{this._element.style.overflowY=t},this._dialog)},this._dialog),this._element.focus())}_adjustDialog(){let e=this._element.scrollHeight>document.documentElement.clientHeight,t=this._scrollBar.getWidth(),i=t>0;if(i&&!e){let e=z()?"paddingLeft":"paddingRight";this._element.style[e]=`${t}px`}if(!i&&e){let e=z()?"paddingRight":"paddingLeft";this._element.style[e]=`${t}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(e,t){return this.each(function(){let i=t7.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===i[e])throw TypeError(`No method named "${e}"`);i[e](t)}})}}er.on(document,t1,'[data-bs-toggle="modal"]',function(e){let t=ef.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&e.preventDefault(),er.one(t,tX,e=>{e.defaultPrevented||er.one(t,tQ,()=>{N(this)&&this.focus()})});let i=ef.findOne(".modal.show");i&&t7.getInstance(i).hide(),t7.getOrCreateInstance(t).toggle(this)}),e_(t7),W(t7);let t9=".bs.offcanvas",t4=".data-api",ie=`load${t9}${t4}`,it="show",ii="showing",is="hiding",ir=".offcanvas.show",io=`show${t9}`,il=`shown${t9}`,ia=`hide${t9}`,ic=`hidePrevented${t9}`,ih=`hidden${t9}`,iu=`resize${t9}`,id=`click${t9}${t4}`,i_=`keydown.dismiss${t9}`,ig={backdrop:!0,keyboard:!0,scroll:!1},im={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class ip extends eu{constructor(e,t){super(e,t),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return ig}static get DefaultType(){return im}static get NAME(){return"offcanvas"}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){this._isShown||er.trigger(this._element,io,{relatedTarget:e}).defaultPrevented||(this._isShown=!0,this._backdrop.show(),this._config.scroll||new tZ().hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(ii),this._queueCallback(()=>{(!this._config.scroll||this._config.backdrop)&&this._focustrap.activate(),this._element.classList.add(it),this._element.classList.remove(ii),er.trigger(this._element,il,{relatedTarget:e})},this._element,!0))}hide(){this._isShown&&!er.trigger(this._element,ia).defaultPrevented&&(this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(is),this._backdrop.hide(),this._queueCallback(()=>{this._element.classList.remove(it,is),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||new tZ().reset(),er.trigger(this._element,ih)},this._element,!0))}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){let e=!!this._config.backdrop;return new tN({className:"offcanvas-backdrop",isVisible:e,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:e?()=>{if("static"===this._config.backdrop){er.trigger(this._element,ic);return}this.hide()}:null})}_initializeFocusTrap(){return new tH({trapElement:this._element})}_addEventListeners(){er.on(this._element,i_,e=>{if("Escape"===e.key){if(this._config.keyboard){this.hide();return}er.trigger(this._element,ic)}})}static jQueryInterface(e){return this.each(function(){let t=ip.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw TypeError(`No method named "${e}"`);t[e](this)}})}}er.on(document,id,'[data-bs-toggle="offcanvas"]',function(e){let t=ef.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&e.preventDefault(),D(this))return;er.one(t,ih,()=>{N(this)&&this.focus()});let i=ef.findOne(ir);i&&i!==t&&ip.getInstance(i).hide(),ip.getOrCreateInstance(t).toggle(this)}),er.on(window,ie,()=>{for(let e of ef.find(ir))ip.getOrCreateInstance(e).show()}),er.on(window,iu,()=>{for(let e of ef.find("[aria-modal][class*=show][class*=offcanvas-]"))"fixed"!==getComputedStyle(e).position&&ip.getOrCreateInstance(e).hide()}),e_(ip),W(ip);let ib={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},iv=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),iy=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,iw=(e,t)=>{let i=e.nodeName.toLowerCase();return t.includes(i)?!iv.has(i)||!!iy.test(e.nodeValue):t.filter(e=>e instanceof RegExp).some(e=>e.test(i))},iA={allowList:ib,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},iE={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},iC={entry:"(string|element|function|null)",selector:"(string|element)"};class iT extends eh{constructor(e){super(),this._config=this._getConfig(e)}static get Default(){return iA}static get DefaultType(){return iE}static get NAME(){return"TemplateFactory"}getContent(){return Object.values(this._config.content).map(e=>this._resolvePossibleFunction(e)).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(e){return this._checkContent(e),this._config.content={...this._config.content,...e},this}toHtml(){let e=document.createElement("div");for(let[t,i]of(e.innerHTML=this._maybeSanitize(this._config.template),Object.entries(this._config.content)))this._setContent(e,i,t);let t=e.children[0],i=this._resolvePossibleFunction(this._config.extraClass);return i&&t.classList.add(...i.split(" ")),t}_typeCheckConfig(e){super._typeCheckConfig(e),this._checkContent(e.content)}_checkContent(e){for(let[t,i]of Object.entries(e))super._typeCheckConfig({selector:t,entry:i},iC)}_setContent(e,t,i){let s=ef.findOne(i,e);if(s){if(!(t=this._resolvePossibleFunction(t))){s.remove();return}if(O(t)){this._putElementInTemplate(I(t),s);return}if(this._config.html){s.innerHTML=this._maybeSanitize(t);return}s.textContent=t}}_maybeSanitize(e){return this._config.sanitize?function(e,t,i){if(!e.length)return e;if(i&&"function"==typeof i)return i(e);let s=new window.DOMParser().parseFromString(e,"text/html");for(let e of[].concat(...s.body.querySelectorAll("*"))){let i=e.nodeName.toLowerCase();if(!Object.keys(t).includes(i)){e.remove();continue}let s=[].concat(...e.attributes),n=[].concat(t["*"]||[],t[i]||[]);for(let t of s)iw(t,n)||e.removeAttribute(t.nodeName)}return s.body.innerHTML}(e,this._config.allowList,this._config.sanitizeFn):e}_resolvePossibleFunction(e){return q(e,[this])}_putElementInTemplate(e,t){if(this._config.html){t.innerHTML="",t.append(e);return}t.textContent=e.textContent}}let ik=new Set(["sanitize","allowList","sanitizeFn"]),i$="fade",iS="show",iL=".modal",iO="hide.bs.modal",iI="hover",iN="focus",iD={AUTO:"auto",TOP:"top",RIGHT:z()?"left":"right",BOTTOM:"bottom",LEFT:z()?"right":"left"},iP={allowList:ib,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},iM={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class ix extends eu{constructor(e,t){if(void 0===s)throw TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)");super(e,t),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return iP}static get DefaultType(){return iM}static get NAME(){return"tooltip"}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){if(this._isEnabled){if(this._activeTrigger.click=!this._activeTrigger.click,this._isShown()){this._leave();return}this._enter()}}dispose(){clearTimeout(this._timeout),er.off(this._element.closest(iL),iO,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if("none"===this._element.style.display)throw Error("Please use show on visible elements");if(!(this._isWithContent()&&this._isEnabled))return;let e=er.trigger(this._element,this.constructor.eventName("show")),t=(P(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(e.defaultPrevented||!t)return;this._disposePopper();let i=this._getTipElement();this._element.setAttribute("aria-describedby",i.getAttribute("id"));let{container:s}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(s.append(i),er.trigger(this._element,this.constructor.eventName("inserted"))),this._popper=this._createPopper(i),i.classList.add(iS),"ontouchstart"in document.documentElement)for(let e of[].concat(...document.body.children))er.on(e,"mouseover",M);this._queueCallback(()=>{er.trigger(this._element,this.constructor.eventName("shown")),!1===this._isHovered&&this._leave(),this._isHovered=!1},this.tip,this._isAnimated())}hide(){if(this._isShown()&&!er.trigger(this._element,this.constructor.eventName("hide")).defaultPrevented){if(this._getTipElement().classList.remove(iS),"ontouchstart"in document.documentElement)for(let e of[].concat(...document.body.children))er.off(e,"mouseover",M);this._activeTrigger.click=!1,this._activeTrigger[iN]=!1,this._activeTrigger[iI]=!1,this._isHovered=null,this._queueCallback(()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),er.trigger(this._element,this.constructor.eventName("hidden")))},this.tip,this._isAnimated())}}update(){this._popper&&this._popper.update()}_isWithContent(){return!!this._getTitle()}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(e){let t=this._getTemplateFactory(e).toHtml();if(!t)return null;t.classList.remove(i$,iS),t.classList.add(`bs-${this.constructor.NAME}-auto`);let i=$(this.constructor.NAME).toString();return t.setAttribute("id",i),this._isAnimated()&&t.classList.add(i$),t}setContent(e){this._newContent=e,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(e){return this._templateFactory?this._templateFactory.changeContent(e):this._templateFactory=new iT({...this._config,content:e,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{".tooltip-inner":this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(e){return this.constructor.getOrCreateInstance(e.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(i$)}_isShown(){return this.tip&&this.tip.classList.contains(iS)}_createPopper(e){let t=iD[q(this._config.placement,[this,e,this._element]).toUpperCase()];return v(this._element,e,this._getPopperConfig(t))}_getOffset(){let{offset:e}=this._config;return"string"==typeof e?e.split(",").map(e=>Number.parseInt(e,10)):"function"==typeof e?t=>e(t,this._element):e}_resolvePossibleFunction(e){return q(e,[this._element])}_getPopperConfig(e){let t={placement:e,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:e=>{this._getTipElement().setAttribute("data-popper-placement",e.state.placement)}}]};return{...t,...q(this._config.popperConfig,[t])}}_setListeners(){for(let e of this._config.trigger.split(" "))if("click"===e)er.on(this._element,this.constructor.eventName("click"),this._config.selector,e=>{this._initializeOnDelegatedTarget(e).toggle()});else if("manual"!==e){let t=e===iI?this.constructor.eventName("mouseenter"):this.constructor.eventName("focusin"),i=e===iI?this.constructor.eventName("mouseleave"):this.constructor.eventName("focusout");er.on(this._element,t,this._config.selector,e=>{let t=this._initializeOnDelegatedTarget(e);t._activeTrigger["focusin"===e.type?iN:iI]=!0,t._enter()}),er.on(this._element,i,this._config.selector,e=>{let t=this._initializeOnDelegatedTarget(e);t._activeTrigger["focusout"===e.type?iN:iI]=t._element.contains(e.relatedTarget),t._leave()})}this._hideModalHandler=()=>{this._element&&this.hide()},er.on(this._element.closest(iL),iO,this._hideModalHandler)}_fixTitle(){let e=this._element.getAttribute("title");e&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",e),this._element.setAttribute("data-bs-original-title",e),this._element.removeAttribute("title"))}_enter(){if(this._isShown()||this._isHovered){this._isHovered=!0;return}this._isHovered=!0,this._setTimeout(()=>{this._isHovered&&this.show()},this._config.delay.show)}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout(()=>{this._isHovered||this.hide()},this._config.delay.hide))}_setTimeout(e,t){clearTimeout(this._timeout),this._timeout=setTimeout(e,t)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(e){let t=ec.getDataAttributes(this._element);for(let e of Object.keys(t))ik.has(e)&&delete t[e];return e={...t,..."object"==typeof e&&e?e:{}},e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e.container=!1===e.container?document.body:I(e.container),"number"==typeof e.delay&&(e.delay={show:e.delay,hide:e.delay}),"number"==typeof e.title&&(e.title=e.title.toString()),"number"==typeof e.content&&(e.content=e.content.toString()),e}_getDelegateConfig(){let e={};for(let[t,i]of Object.entries(this._config))this.constructor.Default[t]!==i&&(e[t]=i);return e.selector=!1,e.trigger="manual",e}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(e){return this.each(function(){let t=ix.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw TypeError(`No method named "${e}"`);t[e]()}})}}W(ix);let ij={...ix.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},iF={...ix.DefaultType,content:"(null|string|element|function)"};class iH extends ix{static get Default(){return ij}static get DefaultType(){return iF}static get NAME(){return"popover"}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{".popover-header":this._getTitle(),".popover-body":this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(e){return this.each(function(){let t=iH.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw TypeError(`No method named "${e}"`);t[e]()}})}}W(iH);let iz=".bs.scrollspy",iW=`activate${iz}`,iq=`click${iz}`,iR=`load${iz}.data-api`,iZ="active",iB="[href]",iK=".nav-link",iV=`${iK}, .nav-item > ${iK}, .list-group-item`,iQ={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},iX={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class iY extends eu{constructor(e,t){super(e,t),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement="visible"===getComputedStyle(this._element).overflowY?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return iQ}static get DefaultType(){return iX}static get NAME(){return"scrollspy"}refresh(){for(let e of(this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver(),this._observableSections.values()))this._observer.observe(e)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(e){return e.target=I(e.target)||document.body,e.rootMargin=e.offset?`${e.offset}px 0px -30%`:e.rootMargin,"string"==typeof e.threshold&&(e.threshold=e.threshold.split(",").map(e=>Number.parseFloat(e))),e}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(er.off(this._config.target,iq),er.on(this._config.target,iq,iB,e=>{let t=this._observableSections.get(e.target.hash);if(t){e.preventDefault();let i=this._rootElement||window,s=t.offsetTop-this._element.offsetTop;if(i.scrollTo){i.scrollTo({top:s,behavior:"smooth"});return}i.scrollTop=s}}))}_getNewObserver(){return new IntersectionObserver(e=>this._observerCallback(e),{root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin})}_observerCallback(e){let t=e=>this._targetLinks.get(`#${e.target.id}`),i=e=>{this._previousScrollData.visibleEntryTop=e.target.offsetTop,this._process(t(e))},s=(this._rootElement||document.documentElement).scrollTop,n=s>=this._previousScrollData.parentScrollTop;for(let r of(this._previousScrollData.parentScrollTop=s,e)){if(!r.isIntersecting){this._activeTarget=null,this._clearActiveClass(t(r));continue}let e=r.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(n&&e){if(i(r),!s)return;continue}n||e||i(r)}}_initializeTargetsAndObservables(){for(let e of(this._targetLinks=new Map,this._observableSections=new Map,ef.find(iB,this._config.target))){if(!e.hash||D(e))continue;let t=ef.findOne(decodeURI(e.hash),this._element);N(t)&&(this._targetLinks.set(decodeURI(e.hash),e),this._observableSections.set(e.hash,t))}}_process(e){this._activeTarget!==e&&(this._clearActiveClass(this._config.target),this._activeTarget=e,e.classList.add(iZ),this._activateParents(e),er.trigger(this._element,iW,{relatedTarget:e}))}_activateParents(e){if(e.classList.contains("dropdown-item")){ef.findOne(".dropdown-toggle",e.closest(".dropdown")).classList.add(iZ);return}for(let t of ef.parents(e,".nav, .list-group"))for(let e of ef.prev(t,iV))e.classList.add(iZ)}_clearActiveClass(e){for(let t of(e.classList.remove(iZ),ef.find(`${iB}.${iZ}`,e)))t.classList.remove(iZ)}static jQueryInterface(e){return this.each(function(){let t=iY.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw TypeError(`No method named "${e}"`);t[e]()}})}}er.on(window,iR,()=>{for(let e of ef.find('[data-bs-spy="scroll"]'))iY.getOrCreateInstance(e)}),W(iY);let iU=".bs.tab",iG=`hide${iU}`,iJ=`hidden${iU}`,i0=`show${iU}`,i1=`shown${iU}`,i5=`click${iU}`,i3=`keydown${iU}`,i2=`load${iU}`,i6="ArrowRight",i8="ArrowDown",i7="Home",i9="active",i4="fade",se="show",st=".dropdown-toggle",si=`:not(${st})`,ss=`.nav-link${si}, .list-group-item${si}, [role="tab"]${si}`,sn='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',sr=`${ss}, ${sn}`,so=`.${i9}[data-bs-toggle="tab"], .${i9}[data-bs-toggle="pill"], .${i9}[data-bs-toggle="list"]`;class sl extends eu{constructor(e){if(super(e),this._parent=this._element.closest('.list-group, .nav, [role="tablist"]'),!this._parent)return;this._setInitialAttributes(this._parent,this._getChildren()),er.on(this._element,i3,e=>this._keydown(e))}static get NAME(){return"tab"}show(){let e=this._element;if(this._elemIsActive(e))return;let t=this._getActiveElem(),i=t?er.trigger(t,iG,{relatedTarget:e}):null;er.trigger(e,i0,{relatedTarget:t}).defaultPrevented||i&&i.defaultPrevented||(this._deactivate(t,e),this._activate(e,t))}_activate(e,t){e&&(e.classList.add(i9),this._activate(ef.getElementFromSelector(e)),this._queueCallback(()=>{if("tab"!==e.getAttribute("role")){e.classList.add(se);return}e.removeAttribute("tabindex"),e.setAttribute("aria-selected",!0),this._toggleDropDown(e,!0),er.trigger(e,i1,{relatedTarget:t})},e,e.classList.contains(i4)))}_deactivate(e,t){e&&(e.classList.remove(i9),e.blur(),this._deactivate(ef.getElementFromSelector(e)),this._queueCallback(()=>{if("tab"!==e.getAttribute("role")){e.classList.remove(se);return}e.setAttribute("aria-selected",!1),e.setAttribute("tabindex","-1"),this._toggleDropDown(e,!1),er.trigger(e,iJ,{relatedTarget:t})},e,e.classList.contains(i4)))}_keydown(e){let t;if(!["ArrowLeft",i6,"ArrowUp",i8,i7,"End"].includes(e.key))return;e.stopPropagation(),e.preventDefault();let i=this._getChildren().filter(e=>!D(e));if([i7,"End"].includes(e.key))t=i[e.key===i7?0:i.length-1];else{let s=[i6,i8].includes(e.key);t=Z(i,e.target,s,!0)}t&&(t.focus({preventScroll:!0}),sl.getOrCreateInstance(t).show())}_getChildren(){return ef.find(sr,this._parent)}_getActiveElem(){return this._getChildren().find(e=>this._elemIsActive(e))||null}_setInitialAttributes(e,t){for(let i of(this._setAttributeIfNotExists(e,"role","tablist"),t))this._setInitialAttributesOnChild(i)}_setInitialAttributesOnChild(e){e=this._getInnerElement(e);let t=this._elemIsActive(e),i=this._getOuterElement(e);e.setAttribute("aria-selected",t),i!==e&&this._setAttributeIfNotExists(i,"role","presentation"),t||e.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(e,"role","tab"),this._setInitialAttributesOnTargetPanel(e)}_setInitialAttributesOnTargetPanel(e){let t=ef.getElementFromSelector(e);t&&(this._setAttributeIfNotExists(t,"role","tabpanel"),e.id&&this._setAttributeIfNotExists(t,"aria-labelledby",`${e.id}`))}_toggleDropDown(e,t){let i=this._getOuterElement(e);if(!i.classList.contains("dropdown"))return;let s=(e,s)=>{let n=ef.findOne(e,i);n&&n.classList.toggle(s,t)};s(st,i9),s(".dropdown-menu",se),i.setAttribute("aria-expanded",t)}_setAttributeIfNotExists(e,t,i){e.hasAttribute(t)||e.setAttribute(t,i)}_elemIsActive(e){return e.classList.contains(i9)}_getInnerElement(e){return e.matches(sr)?e:ef.findOne(sr,e)}_getOuterElement(e){return e.closest(".nav-item, .list-group-item")||e}static jQueryInterface(e){return this.each(function(){let t=sl.getOrCreateInstance(this);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw TypeError(`No method named "${e}"`);t[e]()}})}}er.on(document,i5,sn,function(e){["A","AREA"].includes(this.tagName)&&e.preventDefault(),D(this)||sl.getOrCreateInstance(this).show()}),er.on(window,i2,()=>{for(let e of ef.find(so))sl.getOrCreateInstance(e)}),W(sl);let sa=".bs.toast",sc=`mouseover${sa}`,sh=`mouseout${sa}`,su=`focusin${sa}`,sd=`focusout${sa}`,sf=`hide${sa}`,s_=`hidden${sa}`,sg=`show${sa}`,sm=`shown${sa}`,sp="hide",sb="show",sv="showing",sy={animation:"boolean",autohide:"boolean",delay:"number"},sw={animation:!0,autohide:!0,delay:5e3};class sA extends eu{constructor(e,t){super(e,t),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return sw}static get DefaultType(){return sy}static get NAME(){return"toast"}show(){er.trigger(this._element,sg).defaultPrevented||(this._clearTimeout(),this._config.animation&&this._element.classList.add("fade"),this._element.classList.remove(sp),x(this._element),this._element.classList.add(sb,sv),this._queueCallback(()=>{this._element.classList.remove(sv),er.trigger(this._element,sm),this._maybeScheduleHide()},this._element,this._config.animation))}hide(){this.isShown()&&!er.trigger(this._element,sf).defaultPrevented&&(this._element.classList.add(sv),this._queueCallback(()=>{this._element.classList.add(sp),this._element.classList.remove(sv,sb),er.trigger(this._element,s_)},this._element,this._config.animation))}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(sb),super.dispose()}isShown(){return this._element.classList.contains(sb)}_maybeScheduleHide(){!this._config.autohide||this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout(()=>{this.hide()},this._config.delay))}_onInteraction(e,t){switch(e.type){case"mouseover":case"mouseout":this._hasMouseInteraction=t;break;case"focusin":case"focusout":this._hasKeyboardInteraction=t}if(t){this._clearTimeout();return}let i=e.relatedTarget;this._element===i||this._element.contains(i)||this._maybeScheduleHide()}_setListeners(){er.on(this._element,sc,e=>this._onInteraction(e,!0)),er.on(this._element,sh,e=>this._onInteraction(e,!1)),er.on(this._element,su,e=>this._onInteraction(e,!0)),er.on(this._element,sd,e=>this._onInteraction(e,!1))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(e){return this.each(function(){let t=sA.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw TypeError(`No method named "${e}"`);t[e](this)}})}}e_(sA),W(sA)}}]);