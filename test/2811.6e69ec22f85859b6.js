try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="10ce847e-fe3e-4b98-835c-115d47356758",e._sentryDebugIdIdentifier="sentry-dbid-10ce847e-fe3e-4b98-835c-115d47356758")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2811],{62811:function(e,t,n){n.r(t),n.d(t,{default:function(){return y}});var i=n(85893),r=n(40645),o=n.n(r),s=n(4298),a=n.n(s),l=n(67294),d=n(58676),p=n(10986);n(49952);var u=n(14760);n(21006);let m=new String("body{margin:0;padding:0;overflow:hidden}.map-wrap.fullscreen{width:100%;position:absolute!important;height:-webkit-calc(100vh - var(--header-height));height:-moz-calc(100vh - var(--header-height));height:calc(100vh - var(--header-height))}.map-wrap.fullscreen.embedded{top:0;height:100vh!important}.map-wrap.fullscreen.embedded .maplibregl-ctrl-geocoder{top:unset}.maplibregl-ctrl-geocoder{top:74px}.about{position:absolute;left:16px;top:-webkit-calc(var(--header-height) + 16px);top:-moz-calc(var(--header-height) + 16px);top:calc(var(--header-height) + 16px);z-index:1000}");m.__hash="98fd7ad65eb089e";let h=new String(":root{--header-height:73px}.layers{position:absolute;left:16px;bottom:150px;z-index:1000}.pointer{cursor:pointer}.legend{position:absolute;right:8px;bottom:150px;z-index:1000;padding:10px;background-color:var(--bs-lighter);display:none}.legend .units,.legend #unitsList{font-size:12px;background-color:var(--bs-white);padding:8px 4px;font-weight:600;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px;color:var(--bs-secondary)}.legend .units{width:40px;height:40px}.legend #unitsList{position:absolute;right:2px;z-index:1000;width:75px}.legend #unitsList a{border-width:0}.legend #unitsList .list-group-item-action:hover{background-color:var(--bs-white);color:var(--bs-primary)}.layers button{text-align:left!important}.layers .btn{text-transform:none;padding:12.5px;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center}.layers .btn>div{margin-left:4px}.btn-white{background-color:var(--bs-white);border-width:0;color:var(--bs-secondary)!important}.btn-white:hover{color:var(--bs-primary)!important;background-color:var(--bs-white)!important;border-color:var(--bs-white)!important}.colorRamp{position:relative;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center}.colorRamp canvas{width:37px;-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px;border-color:var(--bs-white);border-width:2px;border-style:solid;-webkit-transform:rotate(180deg);-moz-transform:rotate(180deg);-ms-transform:rotate(180deg);-o-transform:rotate(180deg);transform:rotate(180deg);background-color:var(--bs-white)}.colorRamp .legend-labels{color:var(--bs-white);font-size:10px;font-weight:600;position:absolute;z-index:900;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-justify-content:space-around;-ms-flex-pack:distribute;justify-content:space-around;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;height:100%;text-shadow:1px 1px 0 var(--bs-secondary),1px -1px 0 var(--bs-secondary),-1px 1px 0 var(--bs-secondary),-1px -1px 0 var(--bs-secondary),1px 0px 0 var(--bs-secondary),0px 1px 0 var(--bs-secondary),-1px 0px 0 var(--bs-secondary),0px -1px 0 var(--bs-secondary)}.colorRamp .colorRampCanvas{background-color:var(--bs-white);display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex}.maplibregl-popup-content{width:164px}.maplibregl-popup-content .placename{font-size:13px;font-weight:600;color:var(--bs-secondary);margin-bottom:4px}.maplibregl-popup-content .layerInfo{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-pack:justify;-webkit-justify-content:space-between;-moz-box-pack:justify;-ms-flex-pack:justify;justify-content:space-between;font-size:12px}.maplibregl-popup-content .layerInfo .infoLabel{font-weight:400;color:var(--bs-gray)}.maplibregl-popup-content .layerInfo .infoValue{font-weight:600;color:var(--bs-secondary)}.maplibregl-popup-content .layerInfo .infoValue span{margin-left:2px}#time-info{position:absolute;left:16px;bottom:35px;right:8px;z-index:1000;background-color:rgba(255,255,255,.5);padding:16px}.timebar-wrapper{-webkit-box-flex:2;-webkit-flex-grow:2;-moz-box-flex:2;-ms-flex-positive:2;flex-grow:2}#playpause{width:50px;height:50px;-webkit-border-radius:25px;-moz-border-radius:25px;border-radius:25px;padding:0}.noUi-horizontal{height:5px}.noUi-value{font-size:12px;font-weight:600}.noUi-pips-horizontal{top:8px;color:var(--bs-secondary);font-size:12px}.noUi-tooltip{color:var(--bs-secondary);font-size:12px;font-weight:600;padding:8px;border:2px solid var(--bs-primary);-webkit-border-radius:4px;-moz-border-radius:4px;border-radius:4px;margin-bottom:8px}.noUi-marker-horizontal.noUi-marker{display:block;height:16px;background:var(--bs-secondary);top:4px}.noUi-marker-horizontal.noUi-marker-large{display:none}@media(max-width:575.98px){.maplibregl-ctrl-geocoder .input-group{-webkit-flex-wrap:nowrap;-ms-flex-wrap:nowrap;flex-wrap:nowrap}.maplibregl-ctrl-geocoder{position:relative!important;top:unset;right:unset;z-index:2000;top:72px}.btn-white{width:-webkit-fit-content;width:-moz-fit-content;width:fit-content}.layers{-webkit-flex-wrap:wrap-reverse;-ms-flex-wrap:wrap-reverse;flex-wrap:wrap-reverse;-webkit-box-pack:start;-webkit-justify-content:start;-moz-box-pack:start;-ms-flex-pack:start;justify-content:start}.layers .btn>div.show-label{margin-left:4px;width:80px}.layers .btn>div{width:0px;-webkit-transition:all 1s;-moz-transition:all 1s;-o-transition:all 1s;transition:all 1s;margin-left:0px;overflow:hidden}}.legend-colapsed{padding:0}#legend-colapsed{display:none}@media only screen and (max-height:589px){.legend-expand .hide-on-small-screen{display:none}.legend-expand .hide-on-big-screen{display:none}}@media only screen and (max-height:710px){.hide-label{display:none}}@media only screen and (min-height:589px){.legend{display:block}.legend-expand{padding-left:0;padding-right:0;background-color:unset;#close{display:none}}.legend-expand .hide-on-small-screen{display:none}.legend-expand #legend-colapsed{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex}}@media only screen and (min-height:760px){.legend-expand .hide-on-big-screen{display:none}.legend{padding:0;background-color:unset}.legend-expand .hide-on-small-screen{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex}.legend-expand #legend-colapsed{display:none}#weatherLayerButtons{-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column}#close{display:none}}@media only screen and (max-width:529px)and (max-height:709px){.legend{display:none}}");h.__hash="a59d62aedb05373a";var b=n(93967),g=n.n(b),x=n(67221),y=e=>{let[t,n]=(0,l.useState)(!1),[r,s]=(0,l.useState)(!1),b=(0,l.useRef)(null),y=e.data.fullscreen;return(0,l.useEffect)(()=>{let e;return t&&r&&(e=function(e,t){let n,i;let r=document.getElementById("legend");class o{onAdd(e){this._map=e;let t=document.createElement("div");return t.classList.add(["about","d-flex"]),t.innerHTML='<a href="https://www.maptiler.com/weather/" id="primary-button" class="btn btn-secondary me-1" role="button">Read about the product</a>\n    <a href="https://docs.maptiler.com/sdk-js/modules/weather/" class="btn btn-secondary d-none d-md-inline" role="button">Get the code</a>',this._container.appendChild(t),this._container}onRemove(){var e;null===(e=this._container.parentNode)||void 0===e||e.removeChild(this._container),delete this._map}constructor(e){this._options={...e},this._container=document.createElement("div"),this._container.classList.add("maplibregl-ctrl")}}let s=null,a=!1,l=null,m=document.getElementById("timeSlider"),h=document.getElementById("playpause");u.config.apiKey="O7VbOY3zrXxBupgrQtdE";let b=new URLSearchParams(window.location.search),g=new URLSearchParams(location.hash.substring(1)).get("projection"),x=b.get("layer")||"wind",y=new u.Map({container:e,style:u.MapStyle.BACKDROP,zoom:2,center:[-31.04,16.67],hash:!!t&&"position",projectionControl:!0,projection:g||"mercator"});b.get("embed")&&(document.querySelector(".navbar-sticky-top").classList.add("hidden"),document.querySelector(".map-wrap.fullscreen").classList.add("embedded"),y.resize());let w={showFullGeometry:!1,showResultMarkers:!1,marker:!1,class:"geocoder d-none d-sm-block"};window.matchMedia("(max-width: 700px)").matches&&(w.collapsed=!0),!b.get("cta")&&t&&y.addControl(new o,"top-left");let v=new p.cA(w);y.addControl(v),y.on("load",()=>{y.setPaintProperty("Water","fill-color","rgba(0, 0, 0, 0.4)"),y.setPaintProperty("Water","fill-outline-color","rgba(0, 0, 0, 0)"),Object.keys(E).map(e=>{let t=B(e);y.addLayer(t,"Water")}),T(x,!0),y.on("click",async e=>{let t=await N(e.lngLat);i=new u.Popup().setLngLat(e.lngLat).setHTML(t).addTo(y)}),L()});let j=()=>{y.isGlobeProjection()&&(y.setLight({anchor:"viewport",color:"#FFFFFF",intensity:.8,position:[1.15,90,40]}),y.setSky({"atmosphere-blend":["interpolate",["linear"],["zoom"],0,.4,8,0],"fog-color":"hsl(0, 0%, 43%)","fog-ground-blend":.7,"horizon-color":"hsl(234, 30%, 18%)","horizon-fog-blend":.5,"sky-color":"hsl(203, 100%, 79%)","sky-horizon-blend":.4}))};function L(){let e=new URLSearchParams(location.hash);return y.isGlobeProjection()?e.set("projection","globe"):e.delete("projection"),location.hash=decodeURIComponent(e.toString()),location.hash}y.on("projectiontransition",e=>{"globe"===e.newProjection&&j(),L()}),y.on("moveend",()=>{y.isGlobeProjection()&&L()});let P=document.querySelectorAll(".layers > button"),S={wind:{speedMetersPerSecond:"m/s",speedKilometersPerHour:"km/h",speedMilesPerHour:"mph",speedFeetPerSecond:"ft/s",speedKnots:"knots"},radar:"dBZ",precipitation:"mm/h",temperature:{celcius:"\xb0C",farenheit:"\xb0F"},pressure:"hPa",arrow:{speedMetersPerSecond:"m/s",speedKilometersPerHour:"km/h",speedMilesPerHour:"mph",speedFeetPerSecond:"ft/s",speedKnots:"knots"},isolines:"hPa"},E={precipitation:{layer:null,value:"value",colorramp:d.Eo.builtin.PRECIPITATION},pressure:{layer:null,value:"value",colorramp:d.Eo.builtin.PRESSURE_2},radar:{layer:null,value:"value",colorramp:d.Eo.builtin.RADAR},temperature:{layer:null,value:"value",units:"celcius",colorramp:d.Eo.builtin.TEMPERATURE_2.scale(-50,50)},wind:{layer:null,value:"speedMetersPerSecond",units:"speedMetersPerSecond",colorramp:d.Eo.builtin.VIRIDIS.scale(0,40)},isolines:{layer:null,value:"value",colorramp:d.Eo.builtin.PRESSURE_4},arrow:{layer:null,value:"speedMetersPerSecond",units:"speedMetersPerSecond",colorramp:d.Eo.builtin.VIRIDIS.scale(0,40)}},z=(()=>{let e=0,t=0,n={get fahrenheit(){return 1.8*n.celsius+32},get celsius(){return n.kelvin+-273.15},get kelvin(){return e<0?0:e},set fahrenheit(f){e=(f-32)/1.8- -273.15},set celsius(c){e=c- -273.15},set kelvin(k){e=k},get milesPerHour(){return 2.237*t},get kilometersPerHour(){return 3600*t/1e3},get metersPerSecond(){return t},get knots(){return 1.94384*t},get feetsPerSecond(){return 3.28084*t},set milesPerHour(mph){t=mph/2.237},set kilometersPerHour(kmph){t=1e3*kmph/3600},set metersPerSecond(ms){t=ms},set knots(knots){t=knots/194384},set feetsPerSecond(ftps){t=ftps/3.28084},get F(){return n.fahrenheit},get C(){return n.celsius},get K(){return n.kelvin},set F(f){n.fahrenheit=f},set C(c){n.celsius=c},set K(k){e=k},get mph(){return n.milesPerHour},get kmph(){return n.kilometersPerHour},get ms(){return t},get ftps(){return n.feetsPerSecond},set mph(mph){n.milesPerHour=mph},set kmph(kmph){n.kilometersPerHour=kmph},set ms(ms){t=ms},set ftps(ftps){n.feetsPerSecond=ftps}};return Object.freeze(n)})();function I(e){var t,n;let i=function(e){let t;let n=e.getRawColorStops();switch(s){case"temperature":t=[40,20,10,0,-10,-20,-40];break;case"precipitation":case"wind":case"arrow":t=[40,30,20,15,10,5,0];break;case"pressure":case"isolines":t=[1050,1030,1010,990,970,950,930];break;case"radar":t=[60,50,40,25,15,10,0];break;default:(t=n.reduce((e,t)=>(e.push(t.value),e),[])).reverse()}return t}(e);return("temperature"===s||"wind"===s||"arrow"===s)&&E[s].units&&(t=i,n=E[s].units,i="temperature"===s&&"farenheit"===n?t.map(e=>(z.celsius=e,_(z.fahrenheit))):("wind"===s||"arrow"===s)&&"speedMetersPerSecond"!==n&&"farenheit"!==n&&"celcius"!==n?t.map(e=>{let t;switch(z.metersPerSecond=e,n){case"speedKilometersPerHour":t=z.kilometersPerHour;break;case"speedMilesPerHour":t=z.milesPerHour;break;case"speedFeetPerSecond":t=z.feetsPerSecond;break;case"speedKnots":t=z.knots}return _(t)}):t),"<div>".concat(i[0],"</div>\n    <div>").concat(i[1],"</div>\n    <div>").concat(i[2],"</div>\n    <div>").concat(i[3],"</div>\n    <div>").concat(i[4],"</div>\n    <div>").concat(i[5],"</div>\n    <div>").concat(i[6],"</div>")}function R(e){E[e].units=n;let t=S[e];if("string"==typeof t||t instanceof String)document.getElementById("units").innerHTML=t;else{var i;let e=null!==(i=t[n])&&void 0!==i?i:t[Object.keys(t)[0]];document.getElementById("units").innerHTML=e,function(e){let t=Object.keys(e).map(t=>'<a href="#" class="list-group-item list-group-item-action" data-unit="'.concat(t,'">').concat(e[t],"</a>"));document.getElementById("unitsList").innerHTML=t.join("")}(t)}F(e)?document.getElementById("units").classList.add("pointer"):document.getElementById("units").classList.remove("pointer"),document.getElementById("unitsList").classList.add("hidden"),function(){let e=document.querySelector("#colorRamp .legend-labels");if(e){let t=E[s].colorramp;e.innerHTML=I(t)}}()}function T(e,t){if(e!==s){var n,i,o;if(y.getLayer(s)){let e=null===(n=E[s])||void 0===n?void 0:n.layer;e&&(l=e.getAnimationTime(),y.setLayoutProperty(s,"visibility","none")),"isolines"===s&&e.hidePressureLabels()}let d=E[s=e].layer||B(s);return y.getLayoutProperty(s,"visibility")||t||(d.setOpacity(K(d)),"isolines"==e&&d.setLineThickness(1)),y.getLayer(s)?y.setLayoutProperty(s,"visibility","visible"):y.addLayer(d,"Water"),d.setAnimationTime(parseInt((null==m?void 0:null===(o=m.noUiSlider)||void 0===o?void 0:o.get())/1e3)),a?M(d):A(d),i=s,P.forEach(e=>{e.id==="tool-".concat(i)?(e.classList.add("btn-primary"),e.classList.remove("btn-white")):(e.classList.remove("btn-primary"),e.classList.add("btn-white"))}),R(s),function(e){let t=E[e].layer,n=E[e].colorramp,i=n.getCanvasStrip({horizontal:!1,size:8});i.style.opacity=K(t);let o=function(e){let t=document.createElement("div");return t.classList.add("legend-labels"),t.innerHTML=I(e),t}(n),s=document.createElement("div");s.classList.add("colorRampCanvas"),s.appendChild(i),document.getElementById("colorRamp").replaceChildren(o,s);let a=document.getElementById("legend-colapsed"),l=document.getElementById("iframelegend");a.addEventListener("click",function(){r.classList.remove("legend-expand"),r.classList.add("bg-lighter"),l.classList.remove("d-none"),a.classList.add("d-none")}),document.getElementById("close").addEventListener("click",function(){r.classList.add("legend-expand"),r.classList.remove("bg-lighter"),a.classList.remove("d-none"),l.classList.add("d-none")})}(s),function(e){let t=document.getElementById("primary-button");if(t){if("isolines"===e||"arrow"===e){if("READ ABOUT THE PRODUCT"===t.innerText){t.innerText="READ ABOUT WEATHER PLUS";let e=new URL(t.getAttribute("href"));e.hash="weather-plus",t.setAttribute("href",e.toString())}}else if("READ ABOUT WEATHER PLUS"===t.innerText){t.innerText="READ ABOUT THE PRODUCT";let e=new URL(t.getAttribute("href"));e.hash="",t.setAttribute("href",e.toString())}}}(s),d}}function B(e){let t=null;switch(e){case"precipitation":t=new d.Qo({id:"precipitation",colorramp:E[e].colorramp});break;case"pressure":t=new d.zf({opacity:.8,id:"pressure",colorramp:E[e].colorramp});break;case"radar":t=new d.pb({opacity:.8,id:"radar",colorramp:E[e].colorramp});break;case"temperature":t=new d.DH({id:"temperature",colorramp:E[e].colorramp});break;case"wind":t=new d.vo({id:"wind",colorramp:E[e].colorramp});break;case"isolines":t=new d.nl({opacity:.8,id:"isolines",colorramp:E[e].colorramp});break;case"arrow":t=new d.S9({id:"arrow",colorramp:E[e].colorramp})}return t.on("tick",()=>{a&&H()}),t.on("animationTimeSet",()=>{H(),q(),"isolines"===t.id&&G(t)}),t.on("sourceReady",()=>{(function(e,t){var n;let i=e.getAnimationStartDate(),r=e.getAnimationEndDate();if(t!==x&&(e.setOpacity(0),"isolines"==t&&e.setLineThickness(0)),null==m?void 0:null===(n=m.noUiSlider)||void 0===n?void 0:n.get())e.setAnimationTime(l);else{let e=new Date;l=parseInt(U(e)/1e3),noUiSlider.create(m,{connect:"lower",range:{min:U(i),max:U(r)},start:U(e),format:wNumb({decimals:0}),tooltips:{to:D},pips:{mode:"values",values:function(e){let t=new Date(e.getTime()),n=!(t.getHours()>8),i=[];for(let e=0;e<10;e++){let r=new Date(t.getTime());n?e%2==0?r.setHours(12):(r.setDate(t.getDate()+1),t.setDate(t.getDate()+1),r.setHours(0)):e%2==0?(r.setDate(t.getDate()+1),t.setDate(t.getDate()+1),r.setHours(0)):r.setHours(12),i.push(r)}return i}(i),stepped:!0,filter:C,format:{to:e=>(function(e){let t=new Date(e);return O()?t.toLocaleString("en-GB",{weekday:"short"}):t.toLocaleString("en-GB",{weekday:"long"})})(e)}},animate:!1}),m.noUiSlider.on("slide",function(e){var t,n;null===(n=E[s])||void 0===n||null===(t=n.layer)||void 0===t||t.setAnimationTime(parseInt(e[0]/1e3))})}})(t,e)}),E[e].layer=t,t}function H(){var e,t;let n=null===(e=E[s])||void 0===e?void 0:e.layer;if(n){let e=n.getAnimationTimeDate();null==m||null===(t=m.noUiSlider)||void 0===t||t.set(U(e))}}async function N(e){var t;let n=[],i=null===(t=(await geocoding.reverse([e.lng,e.lat])).features[0])||void 0===t?void 0:t.context,r=null==i?void 0:i.find(e=>e.id.includes("municipality")||e.id.includes("region")||e.id.includes("country"));return(null==r?void 0:r.text)&&n.push('<div class="placename">'.concat(null==r?void 0:r.text,"</div>")),Object.keys(E).forEach(t=>{let i=E[t].layer.pickAt(e.lng,e.lat);i&&n.push('<div class="layerInfo">\n        <div class="infoLabel">'.concat(t,'</div>\n        <div class="infoValue">').concat(Math.round("wind"===t||"arrow"===t?i.speedMetersPerSecond:i.value),"<span>").concat(function(e){let t=S[e];return"string"==typeof t||t instanceof String?t:t[Object.keys(t)[0]]}(t),"</span></div>\n      </div>"))}),n.join("")}function _(e){return 10*Math.round(e/10)}function U(e){return new Date(e).getTime()}function D(e){let t=new Date(e);return O()?t.toLocaleString("en-GB",{hour:"numeric",minute:"numeric",hour12:!1}):t.toLocaleString("en-GB",{weekday:"short",month:"short",day:"numeric",hour:"numeric",minute:"numeric",hour12:!1})}function A(e){e.animateByFactor(0),a=!1,h.querySelector("#pause").classList.add("hidden"),h.querySelector("#play").classList.remove("hidden"),G(e)}function M(e){e.animateByFactor(3600),a=!0,h.querySelector("#play").classList.add("hidden"),h.querySelector("#pause").classList.remove("hidden"),q()}function C(e){let t=new Date(e).getHours();if(12!==t&&0!==t);else if(12===t||0===t)return 12===t?1:0;return -1}function O(){return window.matchMedia("(max-width: 575.98px)").matches}function F(e){return"temperature"===e||"wind"===e||"arrow"===e}function q(){i&&i.isOpen()&&i.remove()}function K(e){var t;return(null==e?void 0:null===(t=e.constructorOptions)||void 0===t?void 0:t.opacity)?e.constructorOptions.opacity:1}function G(e,t){if("isolines"===s)try{e.showPressureLabels()}catch(n){(!t||t<5)&&(t=t?t++:0,setTimeout(()=>{G(e,t)},3e3))}else{let e=E.isolines.layer;null==e||e.hidePressureLabels()}}return P.forEach(e=>{e.addEventListener("click",function(e){var t;T(e.currentTarget.id.replace("tool-","")),O()&&((t=e.currentTarget).querySelector("div").classList.add("show-label"),setTimeout(()=>{t.querySelector("div").classList.remove("show-label")},3e3))})}),document.getElementById("units").addEventListener("click",function(){F(s)?document.getElementById("unitsList").classList.toggle("hidden"):document.getElementById("unitsList").classList.add("hidden")}),document.getElementById("unitsList").addEventListener("click",function(e){n=e.target.dataset.unit,document.getElementById("unitsList").classList.add("hidden"),R(s)}),document.addEventListener("mouseup",function(e){let t=document.getElementById("unitsList"),n=document.getElementById("units");t.contains(e.target)||n.contains(e.target)||t.classList.add("hidden")}),h.addEventListener("click",()=>{var e;let t=null===(e=E[s])||void 0===e?void 0:e.layer;t&&(a?A(t):M(t))}),()=>{null==y||y.remove(),location.hash=""}}(b.current,y)),e},[y,t,r]),(0,i.jsxs)(i.Fragment,{children:[y?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(o(),{id:h.__hash,children:h}),(0,i.jsx)(o(),{id:m.__hash,children:m})]}):(0,i.jsx)(o(),{id:h.__hash,children:h}),(0,i.jsxs)("div",{className:g()("pad-0","map-wrap","map-loaded-bg__satellite",{fullscreen:y,rounded:!y}),children:[(0,i.jsx)("div",{className:g()("map-container","map-loaded-bg__satellite",{rounded:!y}),ref:b}),(0,i.jsxs)("div",{id:"weatherLayerButtons",className:"layers d-flex align-items-strech w-auto",children:[(0,i.jsxs)("button",{type:"button",title:"Temperature",className:"btn btn-primary mb-1 me-1",id:"tool-temperature",children:[(0,i.jsx)("svg",{width:20,height:20,viewBox:"0 0 20 20",children:(0,i.jsx)("use",{xlinkHref:"/styles/style/icon/extra/weather-tool.svg#temperature"})}),(0,i.jsx)("div",{className:"hide-label",children:"Temperature"})]}),(0,i.jsxs)("button",{type:"button",title:"Precipitation",className:"btn btn-white  mb-1 me-1",id:"tool-precipitation",children:[(0,i.jsx)("svg",{width:20,height:20,viewBox:"0 0 20 20",children:(0,i.jsx)("use",{xlinkHref:"/styles/style/icon/extra/weather-tool.svg#precipitation"})}),(0,i.jsx)("div",{className:"hide-label",children:"Precipitation"})]}),(0,i.jsxs)("button",{type:"button",title:"Wind",className:"btn btn-white  mb-1 me-1",id:"tool-wind",children:[(0,i.jsx)("svg",{width:20,height:20,viewBox:"0 0 20 20",children:(0,i.jsx)("use",{xlinkHref:"/styles/style/icon/extra/weather-tool.svg#wind"})}),(0,i.jsx)("div",{className:"hide-label",children:"Wind"})]}),(0,i.jsxs)("button",{type:"button",title:"Pressure",className:"btn btn-white  mb-1 me-1",id:"tool-pressure",children:[(0,i.jsx)("svg",{width:20,height:20,viewBox:"0 0 20 20",children:(0,i.jsx)("use",{xlinkHref:"/styles/style/icon/extra/weather-tool.svg#pressure"})}),(0,i.jsx)("div",{className:"hide-label",children:"Pressure"})]}),(0,i.jsxs)("button",{type:"button",title:"Radar",className:"btn btn-white  mb-1 me-1",id:"tool-radar",children:[(0,i.jsx)("svg",{width:20,height:20,viewBox:"0 0 20 20",children:(0,i.jsx)("use",{xlinkHref:"/styles/style/icon/extra/weather-tool.svg#radar"})}),(0,i.jsx)("div",{className:"hide-label",children:"Radar"})]}),(0,i.jsxs)("button",{type:"button",title:"Isobar (PLUS)",className:"btn btn-white  mb-1 me-1",id:"tool-isolines",children:[(0,i.jsx)("svg",{width:20,height:20,viewBox:"0 0 20 20",children:(0,i.jsx)("use",{xlinkHref:"/styles/style/icon/extra/weather-tool.svg#pressure"})}),(0,i.jsx)("div",{className:"hide-label",children:"Isobar (PLUS)"})]}),(0,i.jsxs)("button",{type:"button",title:"Arrow (PLUS)",className:"btn btn-white  mb-1 me-1",id:"tool-arrow",children:[(0,i.jsx)("svg",{width:20,height:20,viewBox:"0 0 20 20",children:(0,i.jsx)("use",{xlinkHref:"/styles/style/icon/extra/weather-tool.svg#wind"})}),(0,i.jsx)("div",{className:"hide-label",children:"Arrow (PLUS)"})]})]}),(0,i.jsxs)("div",{id:"legend",className:g()("legend","align-items-end","rounded","p-1",{"legend-colapsed":y,"legend-expand":!y}),children:[(0,i.jsxs)("div",{id:"iframelegend",className:"align-items-end justify-content-end ".concat(y?"":"d-none "),children:[(0,i.jsx)("div",{id:"close",className:g()("mt-1","justify-content-center","m-0","toggle-button","d-flex",{"hide-on-big-screen":y}),children:(0,i.jsx)("svg",{width:20,height:20,viewBox:"0 0 20 20",children:(0,i.jsx)("use",{xlinkHref:"/styles/style/icon/icon.svg#cancel"})})}),(0,i.jsx)("div",{className:"d-flex justify-content-center align-items-center pointer my-1 pointer p-0 hide-on-small-screen units  ",children:(0,i.jsx)("p",{id:"units",className:" text-center "})}),(0,i.jsx)("div",{id:"unitsList",className:"hidden list-group text-start hide-on-small-screen"}),(0,i.jsx)("div",{id:"colorRamp",className:"hide-on-small-screen colorRamp "})]}),(0,i.jsx)("button",{type:"button",title:"show legend",className:g()("btn","btn-white",{"hide-on-big-screen":y,"d-flex":!y}),id:"legend-colapsed",children:(0,i.jsx)("div",{children:"Legend"})})]}),(0,i.jsxs)("div",{id:"time-info",className:"d-flex justify-content-between align-items-center",children:[(0,i.jsx)("div",{children:(0,i.jsxs)("button",{id:"playpause",type:"button",className:"btn btn-white",children:[(0,i.jsx)(x.Z,{id:"pause",className:"hidden",name:"pause",width:24,height:24}),(0,i.jsx)(x.Z,{id:"play",name:"play_arrow",width:24,height:24})]})}),(0,i.jsx)("div",{className:"timebar-wrapper mx-2",children:(0,i.jsx)("div",{id:"timeSlider"})})]})]}),(0,i.jsx)(a(),{type:"module",src:"/js/nouislider.js",onReady:()=>n(!0)}),(0,i.jsx)(a(),{type:"module",src:"/js/wNumb.js",onReady:()=>s(!0)})]})}}}]);