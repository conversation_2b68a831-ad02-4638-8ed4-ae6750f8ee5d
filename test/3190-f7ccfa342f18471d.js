try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},a=(new e.Error).stack;a&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[a]="d39b1424-f129-4454-ac91-2d668b33d350",e._sentryDebugIdIdentifier="sentry-dbid-d39b1424-f129-4454-ac91-2d668b33d350")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3190],{80643:function(e,a,t){"use strict";var r=t(85893),l=t(41664),s=t.n(l);let n=e=>e.startsWith("/"),i=e=>{var a;if(null===(a=e.maps)||void 0===a?void 0:a.data)return"map"===e.maps.data.attributes.type?"https://cloud.maptiler.com/maps/"+e.maps.data.attributes.mapID:"https://cloud.maptiler.com/tiles/"+e.maps.data.attributes.mapID;switch(e.url){case"/#contact":return"#contact";case"cloud":return"https://cloud.maptiler.com/";case"blog":return"https://www.maptiler.com/news/";case"customize":return"https://cloud.maptiler.com/maps/editor/";case"home":return"https://www.maptiler.com";case"support":return"https://documentation.maptiler.com/hc/en-us/requests/new";default:return e.url}};a.Z=e=>{let{data:a,children:t,...l}=e,o=i(a);return o&&n(o)?(0,r.jsx)(s(),{...l,href:o,children:t}):(0,r.jsx)("a",{...l,href:o,rel:"noopener",children:t})}},67221:function(e,a,t){"use strict";var r=t(85893);a.Z=e=>{let{className:a,name:t,width:l=24,height:s=24,...n}=e;return(0,r.jsx)("svg",{className:a,version:"1.1",xmlns:"http://www.w3.org/2000/svg",width:l,height:s,viewBox:"0 0 24 24",...n,children:(0,r.jsx)("use",{href:"/styles/style/icon/icon.svg#"+t})})}},62560:function(e,a,t){"use strict";t.d(a,{Z:function(){return G}});var r=t(85893),l=t(2962),s=t(11163),n=t.n(s),i=t(67294),o=t(97375),c=t(90273),d=t(25675),m=t.n(d),p=t(11656),h=t.n(p),u=e=>{var a;let{data:t}=e;if(!t)return null;let{url:l,ext:s}=t;return(0,r.jsx)("div",{className:"container-fluid ".concat(h().headerShape," ").concat(null!==(a=h()[s.substring(1)])&&void 0!==a?a:""),children:(0,r.jsx)(m(),{priority:!0,src:l,width:"100",height:"100",alt:"Header Rectangle"})})},b=t(2626),x=t(50259),g=t(45087),v=t(80129),f=t.n(v);let j=e=>e.startsWith("/engine")?"engine":e.startsWith("/cloud")?"cloud":e.startsWith("/server")?"server":e.startsWith("/data")?"data":e.startsWith("/sdk")?"sdk":e.startsWith("/mobile")?"mobile":"",k=e=>{let{data:a,setShowContactModal:t,setNotification:l,linkUrl:n}=e,{name:o,email:c,phone:d,product:m,text:p,locale:h,other:u,send:b,infoNotification:v,successNotification:k,errorNotification:w}=a.data.attributes,y=(0,s.useRouter)(),{executeRecaptcha:N}=(0,x.xX)(),M=(0,i.useMemo)(()=>({cloud:"MapTiler Cloud",data:"MapTiler Data",engine:"MapTiler Engine",server:"MapTiler Server",other:u}),[u]),_=(0,i.useCallback)(function(e){var a;let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,r=e.split("#")[1];return M[null===(a=f().parse(r).product)||void 0===a?void 0:a.toString()]||M[j(t)]||M.cloud},[M]),[C,T]=(0,i.useState)({"mauticform[formId]":15,"mauticform[messenger]":15,"mauticform[formName]":"webcontactform","mauticform[f_name]":"","mauticform[email2]":"","mauticform[phone]":"","mauticform[lang]":h,"mauticform[product]":_(n||y.asPath,y.asPath),"mauticform[text1]":"","mauticform[pageuri]":window.location.toString()}),I=C["mauticform[f_name]"]&&(0,g.zL)(C["mauticform[email2]"])&&C["mauticform[text1]"],L=e=>{let{name:a,value:t}=e.target;T({...C,[a]:t})},Z=async e=>{if(e.preventDefault(),I){let e=await N("contact_form_submit");try{if((await fetch("/api/recaptcha",{method:"POST",body:JSON.stringify({formData:C,captcha:e}),headers:{"Content-Type":"application/json"}})).ok)(0,g.iv)(C,k,w,l),t(!1);else throw Error("invalid reCaptcha")}catch(e){l({message:"reCaptcha verification failed, are you a bot?",type:"error",show:!0})}}else l({message:v,type:"info",show:!0})};return(0,r.jsxs)("form",{onSubmit:Z,children:[(0,r.jsx)("input",{name:"mauticform[lang]",value:h,className:"mauticform-hidden",type:"hidden"}),(0,r.jsx)("label",{children:o}),(0,r.jsx)("input",{type:"text",name:"mauticform[f_name]",className:"form-control w-100",value:C["mauticform[f_name]"],onChange:L}),(0,r.jsx)("label",{children:c}),(0,r.jsx)("input",{type:"email",name:"mauticform[email2]",className:"form-control w-100",value:C["mauticform[email2]"],onChange:L}),(0,r.jsx)("label",{children:d}),(0,r.jsx)("input",{type:"tel",name:"mauticform[phone]",className:"form-control w-100",placeholder:"Optional",value:C["mauticform[phone]"],onChange:L}),(0,r.jsx)("label",{children:m}),(0,r.jsx)("select",{name:"mauticform[product]",className:"form-select w-100",value:C["mauticform[product]"],onChange:L,children:Object.values(M).map(e=>(0,r.jsx)("option",{value:e,children:e},e))}),(0,r.jsx)("label",{children:p}),(0,r.jsx)("textarea",{name:"mauticform[text1]",rows:6,className:"form-control w-100",value:C["mauticform[text1]"],onChange:L}),(0,r.jsx)("button",{disabled:!I,type:"submit",className:"btn btn-primary w-100",children:b})]})};var w=e=>{let{data:a}=e,[t,l]=(0,i.useState)(!1),[n,o]=(0,i.useState)(null),[c,d]=(0,i.useState)(""),m=(0,s.useRouter)();return(0,i.useEffect)(()=>{if(null==n?void 0:n.show){let e=setTimeout(()=>{o(e=>({...e,show:!1}))},2e3);return()=>clearTimeout(e)}},[n]),(0,i.useEffect)(()=>{let e=document.querySelectorAll('button[href*="#contact"], a[href*="#contact"]'),a=e=>{e.preventDefault(),d(e.target.href),l(!0)};return e.forEach(e=>{e.addEventListener("click",a)}),m.asPath.includes("#contact")&&l(!0),()=>{e.forEach(e=>{e.removeEventListener("click",a)})}},[m]),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(g.jf,{message:null==n?void 0:n.message,type:null==n?void 0:n.type,show:null==n?void 0:n.show}),(0,r.jsx)(b.Z,{contentClassName:"p-3 border-0 rounded",centered:!0,show:t,onHide:()=>l(!1),id:"contact-form-modal",children:(0,r.jsx)(x.pm,{reCaptchaKey:"6Lf-pZ8qAAAAAFNO6YT6TCOWk1NMRWe2l_hGnblS",language:m.locale,container:{element:"recaptcha-box",parameters:{badge:"bottomright"}},children:(0,r.jsx)(k,{data:a,setShowContactModal:l,setNotification:o,linkUrl:c})})}),(0,r.jsx)("div",{id:"recaptcha-box",className:t?"":"invisible"})]})},y=t(19101),N=t(68070),M=t(41664),_=t.n(M),C=t(17137);let T={en:"English",cs:"Čeština",ja:"日本語",fr:"Fran\xe7ais"};var I=e=>{let{data:a,altLangs:t}=e,l=(0,s.useRouter)(),[n,o]=(0,i.useState)(l.locale);return(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"mb-1 ps-o5",children:null==a?void 0:a.title}),(0,r.jsx)(C.Z.Select,{className:"w-80 ms-o5","aria-label":"Language Selector",onChange:e=>{let a=e.target.value;o(a),e.target.blur(),(null==t?void 0:t.includes(a))?l.push(l.asPath,null,{locale:a}):l.push("/",null,{locale:a})},value:n,children:Object.entries(T).map(e=>{let[a,t]=e;return(0,r.jsx)("option",{value:a,children:t},a)})})]})},L=e=>{let a,t,l,{data:s,altLangs:n}=e,c=new Date().getFullYear(),d=s.find(e=>"languageSelector"===e.id);return s.length%4==0||s.length%5==0||s.length%6==0?(a=2,t=4,l=6):(a=3,t=3,l=4),(0,r.jsx)("footer",{className:"footer border-top border-light bg-white",children:(0,r.jsxs)(o.Z,{fluid:"lg",className:"pt-5",children:[(0,r.jsxs)(y.Z,{className:"justify-content-center justify-content-sm-start",children:[(0,r.jsx)(N.Z,{md:4,lg:2,className:"mb-4",children:(0,r.jsx)(y.Z,{children:(0,r.jsxs)(N.Z,{children:[(0,r.jsx)(_(),{href:"/","aria-label":"MapTiler",className:"track-link-footer",children:(0,r.jsx)("svg",{className:"mt-1 text-secondary",width:"205",viewBox:"0 0 215 55",children:(0,r.jsx)("use",{href:"/styles/style/logo/maptiler-logo-adaptive.svg?123#maptilerLogo"})})}),(0,r.jsxs)("div",{className:"ps-gutter",children:[(0,r.jsx)("p",{className:"text-secondary mb-1 ps-o5",children:"Follow\xa0us\xa0on"}),(0,r.jsxs)("div",{className:"social-circles",children:[(0,r.jsx)("a",{target:"_blank",className:"twitter track-link-footer",href:"https://x.com/MapTiler",rel:"noopener","aria-label":"X"}),(0,r.jsx)("a",{target:"_blank",className:"bsky track-link-footer",href:"https://bsky.app/profile/maptiler.bsky.social",rel:"noopener","aria-label":"Bluesky"}),(0,r.jsx)("a",{target:"_blank",className:"linkedin track-link-footer",href:"https://www.linkedin.com/company/maptiler",rel:"noopener","aria-label":"LinkedIn"}),(0,r.jsx)("a",{target:"_blank",className:"facebook track-link-footer",href:"https://www.facebook.com/maptiler/",rel:"noopener","aria-label":"Facebook"}),(0,r.jsx)("a",{target:"_blank",className:"instagram track-link-footer",href:"https://www.instagram.com/maptiler/",rel:"noopener","aria-label":"Instagram"}),(0,r.jsx)("a",{target:"_blank",className:"youtube track-link-footer",href:"https://www.youtube.com/channel/UCubcQeWuBKvqpMu172CLEXw",rel:"noopener","aria-label":"YouTube"})]}),(0,r.jsx)(I,{data:d,altLangs:n})]})]})})}),null==s?void 0:s.map((e,s)=>{var n;return(0,r.jsx)(i.Fragment,{children:"languageSelector"!=e.id&&(0,r.jsxs)(N.Z,{xs:10,sm:l,lg:a,md:t,className:"link-list",children:[(0,r.jsx)("h6",{children:e.title}),null===(n=e.items)||void 0===n?void 0:n.map((e,a)=>"INTERNAL"===e.type?(0,r.jsx)(_(),{href:e.path,className:"track-link-footer",children:e.title},a):(0,r.jsx)("a",{id:e.path.includes("csconsentlink")?"csconsentlink":void 0,href:e.path,className:"track-link-footer",children:e.title},a))]})},s)})]}),(0,r.jsx)(y.Z,{className:"border-top border-light py-4 mt-6",children:(0,r.jsx)(N.Z,{children:(0,r.jsxs)("p",{children:["\xa9",(0,r.jsxs)("span",{id:"copyrightYear",children:[" ",c," "]}),(0,r.jsx)(_(),{className:"text-dark track-link-footer",href:"/contacts/",children:"MapTiler"}),". All rights reserved.",(0,r.jsxs)(_(),{href:"/privacy-policy/",locale:!1,className:"track-link-footer",children:[" ","Privacy"," "]}),"&"," ",(0,r.jsx)(_(),{href:"/terms/",locale:!1,className:"track-link-footer",children:"Terms"}),"."]})})})]})})},Z=t(67221);let D=()=>(0,r.jsx)("span",{className:"float-end collapse-arrow",children:(0,r.jsx)(Z.Z,{name:"keyboard_arrow_down",width:24,height:24})}),S=e=>{var a;let{data:t}=e,l=(0,s.useRouter)(),n=l.asPath,o=j(n),c=t.find(e=>{var a;return null===(a=e.id)||void 0===a?void 0:a.includes("products")}),d=t.find(e=>{var a;return null===(a=e.id)||void 0===a?void 0:a.includes("company")}),m=t.find(e=>{var a;return null===(a=e.id)||void 0===a?void 0:a.includes("pricing")}),p=t.find(e=>{var a;return null===(a=e.id)||void 0===a?void 0:a.includes("searchButton")}),h=t.find(e=>{var a;return null===(a=e.id)||void 0===a?void 0:a.includes("resources")}),u=t.filter(e=>{var a;return null===(a=e.id)||void 0===a?void 0:a.includes("singleLink")}),b="server"===o||"data"===o?"/data/pricing/":"engine"===o?"/engine/pricing/":"/cloud/pricing/",x=(null==p?void 0:p.path)&&n.startsWith("/news")?"".concat(p.path,"?index=news"):null==p?void 0:p.path;return(0,r.jsx)("div",{className:"border-bottom border-light bg-white navbar-sticky-top",children:(0,r.jsxs)("nav",{id:"navbar",className:"navbar navbar-expand-lg navbar-dark navbar-maptiler px-gutter",children:[(0,r.jsx)(_(),{className:"navbar-brand product-brand track-link-header",href:"/","aria-label":"MapTiler",children:o?(0,r.jsx)("svg",{width:"245",height:"45",children:(0,r.jsx)("use",{href:"/styles/style/logo/maptiler-logo-adaptive-".concat(o,".svg?123#").concat(o)})}):(0,r.jsx)("svg",{width:"160",height:"45",children:(0,r.jsx)("use",{href:"/styles/style/logo/maptiler-logo-adaptive.svg?123#maptilerLogo"})})}),(0,r.jsx)("div",{className:"collapse navbar-collapse navbar-mobile justify-content-end",id:"Mobilenav",children:(0,r.jsxs)("div",{className:"navbar-nav",children:[(0,r.jsxs)("div",{className:"d-none d-lg-flex",children:[c&&(0,r.jsxs)("div",{className:"dropdown",children:[(0,r.jsxs)("a",{className:"nav-item nav-link",type:"button",id:"dropdownProductsLink","data-bs-toggle":"dropdown","aria-expanded":"false","aria-controls":"dropdownProductsLink",children:[c.title," ",(0,r.jsx)(D,{})]}),(0,r.jsx)("div",{className:"dropdown-menu dropdown-menu-navbar p-3 dropdown-menu-navbar-animation slideIn","aria-labelledby":"dropdownProductsLink",children:(0,r.jsxs)("div",{className:"d-flex",children:[(0,r.jsxs)("div",{className:"dropdown-products dropdown-cloud ".concat((null===(a=c.items[0].items)||void 0===a?void 0:a.length)>4?"xlTwoColumns":""),children:[(0,r.jsx)("h6",{className:"m-0 text-uppercase small ps-o5 text-cloud pb-1",children:c.items[0].title}),c.items[0].items.map((e,a)=>(0,r.jsxs)(i.Fragment,{children:[4===a&&(0,r.jsx)("h6",{className:"m-0 text-uppercase small text-white user-select-none d-none d-xl-block pb-1",children:"MapTiler"}),(0,r.jsxs)(_(),{className:"dropdown-link me-2 track-link-header",href:e.path,children:[(0,r.jsx)("img",{alt:"",src:e.navbarIcon.url,width:e.navbarIcon.name.includes("sdk")?"50":"30",height:e.navbarIcon.name.includes("sdk")?"17":"30"}),e.title]})]},a))]}),(0,r.jsxs)("div",{className:"dropdown-products dropdown-data px-2 border-start border-light",children:[(0,r.jsx)("h6",{className:"m-0 text-uppercase small ps-o5 text-data pb-1",children:c.items[1].title}),c.items[1].items.map((e,a)=>(0,r.jsxs)(_(),{className:"dropdown-link me-2 track-link-header",href:e.path,children:[(0,r.jsx)("img",{alt:"",src:e.navbarIcon.url,width:"30",height:"30"}),e.title]},a))]}),(0,r.jsxs)("div",{className:"dropdown-products dropdown-desktop ps-2 border-start border-light",children:[(0,r.jsx)("h6",{className:"m-0 text-uppercase small ps-o5 text-desktop pb-1",children:c.items[2].title}),c.items[2].items.map((e,a)=>(0,r.jsxs)(_(),{className:"dropdown-link me-2 track-link-header",href:e.path,children:[(0,r.jsx)("img",{alt:"",src:e.navbarIcon.url,width:"30",height:"30"}),e.title]},a))]})]})})]}),d&&(0,r.jsxs)("div",{className:"dropdown",children:[(0,r.jsxs)("a",{className:"nav-item nav-link",type:"button",id:"dropdownCompanyLink","data-bs-toggle":"dropdown","aria-expanded":"false","aria-controls":"dropdownCompanyLink",children:[d.title," ",(0,r.jsx)(D,{})]}),(0,r.jsx)("div",{className:"dropdown-menu dropdown-menu-navbar navbar-links dropdown-menu-navbar-animation slideIn","aria-labelledby":"dropdownCompanyLink",children:d.items.map((e,a)=>(0,r.jsx)(_(),{className:"dropdown-link track-link-header",href:e.path,children:e.title},a))})]}),m&&(0,r.jsx)(_(),{className:"nav-item nav-link track-link-header",href:b,children:m.title}),h&&(0,r.jsxs)("div",{className:"dropdown",children:[(0,r.jsxs)("button",{className:"nav-item nav-link",type:"button",id:"dropdownResourcesLink","data-bs-toggle":"dropdown","aria-expanded":"false",children:[h.title," ",(0,r.jsx)(D,{})]}),(0,r.jsx)("div",{className:"dropdown-menu dropdown-menu-navbar navbar-links dropdown-menu-navbar-animation slideIn","aria-labelledby":"dropdownResourcesLink",children:h.items.map((e,a)=>(0,r.jsx)("a",{className:"dropdown-link track-link-header",href:e.path,children:e.title},a))})]}),null==u?void 0:u.map((e,a)=>(0,r.jsx)(_(),{className:"nav-item nav-link track-link-header",href:e.path,children:e.title},a))]}),(0,r.jsxs)("div",{className:"col-12 d-flex justify-content-between d-lg-none navbar-mobile-dropdown align-items-center px-gutter",children:[(0,r.jsx)(_(),{className:"navbar-brand product-brand text-secondary track-link-header",href:"/",children:(0,r.jsx)("svg",{width:"160",height:"45",children:(0,r.jsx)("use",{href:"/styles/style/logo/maptiler-logo-adaptive.svg?123#maptilerLogo"})})}),(0,r.jsx)("div",{children:(0,r.jsx)("button",{className:"navbar-toggler text-secondary align-self-center",type:"button","data-bs-toggle":"collapse","data-bs-target":"#Mobilenav","aria-controls":"Mobilenav","aria-expanded":"false","aria-label":"Toggle navigation",children:(0,r.jsx)(Z.Z,{name:"clear",width:40,height:40})})})]}),(0,r.jsxs)("div",{className:"col-12 d-lg-none border-top border-light",children:[" ",(0,r.jsxs)("div",{className:"accordion px-gutter py-2",id:"accordionExample",children:[p&&(0,r.jsxs)(_(),{role:"button",type:"button",className:"py-2 mx-2 h6 d-block my-0 link-secondary border-bottom border-gray track-link-header","data-bs-toggle":"collapse","data-bs-target":"#Mobilenav",href:x,onClick:()=>l.push(x),children:[(0,r.jsx)(Z.Z,{className:"me-o5 align-bottom",name:"search",width:24,height:24}),p.title]}),c&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("a",{role:"button",type:"button","data-bs-toggle":"collapse",className:"py-2 mx-2 h6 d-block my-0 link-secondary","data-bs-target":"#accordionMobileProducts","aria-expanded":"false","aria-controls":"accordionMobileProducts",children:[c.title,(0,r.jsxs)("span",{className:"d-inline-block collapse-arrow",children:[" ",(0,r.jsx)(Z.Z,{name:"keyboard_arrow_down",width:24,height:24})]})]}),(0,r.jsx)("div",{id:"accordionMobileProducts",className:"accordion-collapse collapse","aria-labelledby":"headingOne","data-bs-parent":"#accordionExample",children:(0,r.jsxs)("div",{className:"accordion-body",children:[(0,r.jsxs)("div",{className:"dropdown-products dropdown-cloud pb-2",children:[(0,r.jsx)("h6",{className:"m-0 text-uppercase small ps-o5 text-cloud pb-1",children:c.items[0].title}),c.items[0].items.map((e,a)=>(0,r.jsxs)(_(),{className:"dropdown-link me-2 track-link-header",href:e.path,onClick:()=>l.push(e.path),"data-bs-toggle":"collapse","data-bs-target":"#Mobilenav",children:[(0,r.jsx)("img",{alt:"",src:e.navbarIcon.url,width:e.navbarIcon.name.includes("sdk")?"50":"30",height:e.navbarIcon.name.includes("sdk")?"17":"30"}),e.title]},a))]}),(0,r.jsxs)("div",{className:"dropdown-products dropdown-data py-2 border-top border-light",children:[(0,r.jsx)("h6",{className:"m-0 text-uppercase small ps-o5 text-data pb-1",children:c.items[1].title}),c.items[1].items.map((e,a)=>(0,r.jsxs)(_(),{className:"dropdown-link me-2 track-link-header",href:e.path,onClick:()=>l.push(e.path),"data-bs-toggle":"collapse","data-bs-target":"#Mobilenav",children:[(0,r.jsx)("img",{alt:"",src:e.navbarIcon.url,width:"30",height:"30"}),e.title]},a))]}),(0,r.jsxs)("div",{className:"dropdown-products dropdown-desktop pt-2 border-top border-light",children:[(0,r.jsx)("h6",{className:"m-0 text-uppercase small ps-o5 text-desktop pb-1",children:c.items[2].title}),c.items[2].items.map((e,a)=>(0,r.jsxs)(_(),{className:"dropdown-link me-2 track-link-header",href:e.path,onClick:()=>l.push(e.path),"data-bs-toggle":"collapse","data-bs-target":"#Mobilenav",children:[(0,r.jsx)("img",{alt:"",src:e.navbarIcon.url,width:"30",height:"30"}),e.title]},a))]})]})})]}),d&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("a",{role:"button",type:"button","data-bs-toggle":"collapse",className:"py-2 mx-2 h6 d-block my-0 border-top border-light link-secondary","data-bs-target":"#accordionMobileCompany","aria-expanded":"false","aria-controls":"accordionMobileCompany",children:[d.title,(0,r.jsxs)("span",{className:"d-inline-block collapse-arrow",children:[" ",(0,r.jsx)(Z.Z,{name:"keyboard_arrow_down",width:24,height:24})]})]}),(0,r.jsx)("div",{id:"accordionMobileCompany",className:"accordion-collapse collapse","aria-labelledby":"headingTwo","data-bs-parent":"#accordionExample",children:(0,r.jsx)("div",{className:"accordion-body navbar-links",children:d.items.map((e,a)=>(0,r.jsx)(_(),{className:"dropdown-link track-link-header",href:e.path,onClick:()=>l.push(e.path),"data-bs-toggle":"collapse","data-bs-target":"#Mobilenav",children:e.title},a))})})]}),m&&(0,r.jsx)(_(),{role:"button",type:"button",className:"py-2 mx-2 h6 d-block my-0 border-top border-light link-secondary track-link-header",href:b,"data-bs-toggle":"collapse","data-bs-target":"#Mobilenav",onClick:()=>l.push(b),children:m.title}),h&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("a",{role:"button",type:"button","data-bs-toggle":"collapse",className:"py-2 mx-2 h6 d-block my-0 border-top border-light link-secondary","data-bs-target":"#accordionMobileResources","aria-expanded":"false","aria-controls":"accordionMobileResources",children:[h.title,(0,r.jsxs)("span",{className:"d-inline-block collapse-arrow",children:[" ",(0,r.jsx)(Z.Z,{name:"keyboard_arrow_down",width:24,height:24})]})]}),(0,r.jsx)("div",{id:"accordionMobileResources",className:"accordion-collapse collapse","aria-labelledby":"headingThree","data-bs-parent":"#accordionExample",children:(0,r.jsx)("div",{className:"accordion-body navbar-links",children:h.items.map((e,a)=>(0,r.jsx)("a",{className:"dropdown-link track-link-header",href:e.path,children:e.title},a))})})]}),null==u?void 0:u.map((e,a)=>(0,r.jsx)(_(),{role:"button",type:"button",className:"py-2 mx-2 h6 d-block my-0 border-top border-light link-secondary track-link-header",href:e.path,"data-bs-toggle":"collapse","data-bs-target":"#Mobilenav",onClick:()=>l.push(e.path),children:e.title},a)),(0,r.jsx)("div",{className:"py-2 mx-2 my-0 border-top border-light",children:"data"===o||"server"===o?(0,r.jsx)("a",{className:"btn btn-primary track-link-header",href:"https://data.maptiler.com/my-extracts/",children:"My Data"}):(0,r.jsx)("a",{className:"btn btn-primary track-link-header",href:"https://cloud.maptiler.com/",children:"My Cloud"})})]})]})]})}),(0,r.jsxs)("div",{className:"d-flex align-items-center",children:[p&&(0,r.jsx)(_(),{href:x,className:"d-none d-lg-inline-block track-link-header",children:(0,r.jsx)(Z.Z,{className:"text-secondary hover-primary",name:"search",width:35,height:24})}),(0,r.jsx)("div",{className:p?"ms-2":"ms-3",children:"data"===o||"server"===o?(0,r.jsx)("a",{className:"btn btn-primary d-none d-lg-block track-link-header",href:"https://data.maptiler.com/my-extracts/",children:"My Data"}):(0,r.jsx)("a",{className:"btn btn-primary d-none d-lg-block track-link-header",href:"https://cloud.maptiler.com/",children:"My Cloud"})}),(0,r.jsx)("button",{className:"navbar-toggler",type:"button","data-bs-toggle":"collapse","data-bs-target":"#Mobilenav","aria-controls":"Mobilenav","aria-expanded":"false","aria-label":"Toggle navigation",children:(0,r.jsx)(Z.Z,{name:"menu",width:40,height:40})})]})]})})},E=t(41686).ZP.div.withConfig({componentId:"sc-e8ab0f94-0"})(["position:relative;overflow-x:clip;"]);var P=t(80643);let R=e=>{let{name:a}=e;return(0,r.jsx)("span",{className:"ms-auto float-end collapse-arrow",children:(0,r.jsx)(Z.Z,{className:"me-0",name:a})})},F=()=>(0,r.jsx)("a",{className:"navbar-brand pl-gutter product-brand track-link-header me-auto",href:"/","aria-label":"MapTiler",children:(0,r.jsx)("svg",{width:"165",height:"45",children:(0,r.jsx)("use",{href:"/styles/style/logo/maptiler-logo-adaptive.svg?123#maptilerLogo"})})}),A=()=>{let{asPath:e}=(0,s.useRouter)(),a=j(e),t="data"===a||"server"===a?"https://data.maptiler.com/my-extracts/":"https://cloud.maptiler.com/";return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("a",{href:t,type:"button",className:"btn btn-primary btn-sm me-o5 track-link-header",children:"Create account"}),(0,r.jsx)("a",{href:t,type:"button",className:"btn btn-lighter btn-sm track-link-header",children:"Log in"})]})},O=e=>{let{icon:a}=e;return a?a.includes(".svg")?(0,r.jsx)("svg",{className:"",version:"1.1",xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",children:(0,r.jsx)("use",{href:"/img/navbar-icon/".concat(a)})}):(0,r.jsx)("img",{width:"40",height:"40",className:"rounded",src:"/img/navbar-icon/".concat(a),alt:a.split(".")[0]}):null},W=e=>{let a=document.getElementById("".concat(e,"MobileOffcanvasClose"));a&&a.click()},B=e=>{let a,{id:t,icon:l,title:s,arrow:n,active:i}=e;return(0,r.jsxs)("a",{className:"dropdown-link dropdown-products-tab track-link-header ".concat(i?"active":""),id:"".concat(t,"-tab"),"data-bs-toggle":"pill","data-bs-target":"#".concat(t),type:"button",role:"tab","aria-controls":t,onClick:e=>e.stopPropagation(),onMouseEnter:e=>{let t=e.currentTarget;a=setTimeout(()=>{t&&t.click()},150)},onMouseLeave:()=>{clearTimeout(a)},children:[(0,r.jsx)(O,{icon:l}),s,n&&(0,r.jsx)(R,{name:"keyboard_arrow_right"})]})},q=e=>{let{url:a,icon:t,title:l,arrow:s,badge:i,mobile:o=!1,categoryID:c,subcategoryID:d}=e;return(0,r.jsxs)(P.Z,{className:"dropdown-link track-link-header track-link-category-".concat(c," ").concat(d?"track-link-subcategory-".concat(d):""),data:{url:a},...o&&{"data-bs-toggle":"collapse","data-bs-target":"#Mobilenav",onClick:()=>{n().push(a),W(d)}},children:[(0,r.jsx)(O,{icon:t}),l,i&&(0,r.jsx)("span",{className:"badge bg-lighter text-secondary ms-1",children:i}),s&&(0,r.jsx)(R,{name:"keyboard_arrow_right"})]})},Y=e=>{let{url:a,icon:t,title:l,arrow:s,categoryID:i}=e;return(0,r.jsxs)(P.Z,{className:"py-2 mx-2 h6 d-block my-0 link-secondary border-bottom border-gray track-link-header track-link-category-".concat(i),data:{url:a},"data-bs-toggle":"collapse","data-bs-target":"#Mobilenav",onClick:()=>n().push(a),children:[(0,r.jsx)(O,{icon:t}),l,"link-arrow"===s&&(0,r.jsx)(R,{name:"keyboard_arrow_right"})]})},U=e=>{let{id:a,icon:t,title:l,arrow:s}=e;return(0,r.jsxs)("a",{className:"dropdown-link dropdown-products-tab track-link-header",type:"button","data-bs-toggle":"offcanvas","data-bs-target":"#".concat(a,"Mobile"),"aria-controls":"".concat(a,"Mobile"),children:[(0,r.jsx)(O,{icon:t}),l,s&&(0,r.jsx)(R,{name:"keyboard_arrow_right"})]})};var X=e=>{let{type:a}=e;switch(a){case"tab-link":return(0,r.jsx)(B,{...e});case"page-link":return(0,r.jsx)(q,{...e});case"mobile-link":return(0,r.jsx)(Y,{...e});case"mobile-link-offcanvas":return(0,r.jsx)(U,{...e});default:return null}},z=e=>{var a,t;let{type:l,id:s,title:n,data:i,categoryID:o}=e;return"mobile-accordion"===l?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("a",{role:"button",type:"button","data-bs-toggle":"collapse",className:"py-2 mx-2 h6 d-block my-0 border-bottom border-light link-secondary track-link-header","data-bs-target":"#accordionMobile".concat(s),"aria-expanded":"false","aria-controls":"accordionMobile".concat(s),children:[n," ",(0,r.jsx)(R,{name:"keyboard_arrow_down"})]}),(0,r.jsx)("div",{id:"accordionMobile".concat(s),className:"accordion-collapse collapse","data-bs-parent":"#mobileNavigationAccordion","aria-labelledby":"accordionMobile".concat(s),children:(0,r.jsx)("div",{className:"accordion-body dropdown-products",children:null===(a=i.items)||void 0===a?void 0:a.map((e,a)=>(0,r.jsx)(X,{type:"page-link",url:e.path,title:e.title,mobile:!0,categoryID:o},a))})})]}):"desktop-dropdown"===l?(0,r.jsxs)("div",{className:"dropdown",children:[(0,r.jsxs)("a",{className:"nav-item nav-link",type:"button",id:"".concat(s,"Link"),"aria-controls":s,"data-bs-toggle":"dropdown","aria-expanded":"false",children:[n," ",(0,r.jsx)(R,{name:"keyboard_arrow_down"})]}),(0,r.jsx)("div",{className:"dropdown-menu dropdown-menu-navbar navbar-links dropdown-menu-navbar-animation slideIn","aria-labelledby":"".concat(s,"Link"),children:null===(t=i.items)||void 0===t?void 0:t.map((e,a)=>(0,r.jsx)(X,{type:"page-link",url:e.path,title:e.title,categoryID:o},a))})]}):null},V=e=>{let{type:a,id:t,active:l,contentTitle:s,contentDesc:n,data:i,categoryID:o}=e;return"tab-content"===a?(0,r.jsxs)("div",{className:"tab-pane fade ".concat(l?"show active":""),id:t,role:"tabpanel","aria-labelledby":"".concat(t,"-tab"),children:[(0,r.jsx)("h5",{className:"m-0",children:s}),(0,r.jsx)("p",{className:"mt-1 small",children:n}),(0,r.jsx)("div",{className:"dropdown-products",children:i.map((e,a)=>(0,r.jsx)(X,{icon:e.id,type:"page-link",url:e.path,title:e.title,badge:e.badge,arrow:!0,categoryID:o,subcategoryID:t},a))})]}):"offcanvas-content"===a?(0,r.jsxs)("div",{className:"offcanvas offcanvas-end position-absolute w-100 h-100 dropdown-products p-gutter",tabIndex:-1,"data-bs-backdrop":"false","data-bs-scroll":"true",id:"".concat(t,"Mobile"),children:[(0,r.jsxs)("a",{role:"button",type:"button",className:"py-2 mb-2 h6 d-block my-0 link-secondary dropdown-link px-0","data-bs-dismiss":"offcanvas",id:"".concat(t,"MobileOffcanvasClose"),children:[(0,r.jsx)(Z.Z,{name:"arrow_back",width:24,height:24}),"Back"]}),(0,r.jsx)("h5",{className:"m-0",children:s}),(0,r.jsx)("p",{className:"mt-1 small",children:n}),(0,r.jsx)("div",{className:"dropdown-products",children:i.map((e,a)=>(0,r.jsx)(X,{type:"page-link",url:e.path,title:e.title,badge:e.badge,icon:e.id,arrow:!0,mobile:!0,categoryID:o,subcategoryID:t},a))})]}):null};let J=e=>{let{data:a}=e,t=(0,i.useRef)(null),{asPath:l}=(0,s.useRouter)(),n=j(l),o=a.find(e=>{var a;return null===(a=e.id)||void 0===a?void 0:a.includes("platform")}),c=a.find(e=>{var a;return null===(a=e.id)||void 0===a?void 0:a.includes("company")}),d=a.find(e=>{var a;return null===(a=e.id)||void 0===a?void 0:a.includes("pricing")}),m=a.find(e=>{var a;return null===(a=e.id)||void 0===a?void 0:a.includes("search")}),p=a.find(e=>{var a;return null===(a=e.id)||void 0===a?void 0:a.includes("resources")}),h=a.filter(e=>{var a;return null===(a=e.id)||void 0===a?void 0:a.includes("singleLink")}),u=(null==m?void 0:m.path)&&l.startsWith("/news")?"".concat(m.path,"?index=news"):null==m?void 0:m.path,b="server"===n||"data"===n?"/data/pricing/":"engine"===n?"/engine/pricing/":"/cloud/pricing/";return(0,i.useEffect)(()=>{let e=document.querySelectorAll(".offcanvas"),a=document.querySelector("#mobileDropdownContainer");e.forEach(e=>{e.addEventListener("shown.bs.offcanvas",()=>{a&&a.scrollTo({top:0,behavior:"smooth"})})})},[]),(0,i.useEffect)(()=>{let e=t.current,a=new ResizeObserver(e=>{for(let a of e){let{width:e,height:t}=a.contentRect;e>0&&t>0?document.body.classList.add("overflow-hidden"):document.body.classList.remove("overflow-hidden")}});return e&&a.observe(e),()=>{e&&a.unobserve(e)}},[]),(0,r.jsx)("nav",{className:"border-bottom border-light bg-white navbar-sticky-top",children:(0,r.jsxs)("div",{id:"navbar",className:"navbar navbar-expand-lg navbar-dark navbar-maptiler container-lg px-gutter",children:[(0,r.jsx)(F,{}),(0,r.jsxs)("div",{className:"navbar-nav d-none d-lg-flex",id:"Desktopnav",children:[o&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("a",{className:"nav-item nav-link",type:"button",id:"dropdownPlatformLink","aria-controls":"dropdownPlatform","data-bs-toggle":"dropdown","aria-expanded":"false",children:[o.title," ",(0,r.jsx)(R,{name:"keyboard_arrow_down"})]}),(0,r.jsxs)("div",{className:"dropdown-menu dropdown-menu-navbar dropdown-menu-navbar-platform dropdown-products p-3 dropdown-menu-navbar-animation slideIn","aria-labelledby":"dropdownPlatformLink",children:[(0,r.jsx)("div",{className:"nav flex-column pe-3",id:"v-pills-tab",role:"tablist","aria-orientation":"vertical",children:o.items.map((e,a)=>(0,r.jsx)(X,{icon:"".concat(e.id,".svg#").concat(e.id),type:"tab-link",id:e.id,title:e.title,active:0===a,arrow:!0},a))}),(0,r.jsx)("div",{className:"tab-content ps-3 border-start border-light",id:"platform-tabContent",children:o.items.map((e,a)=>(0,r.jsx)(V,{type:"tab-content",id:e.id,active:0===a,contentTitle:e.subnavTitle,contentDesc:e.subnavDescription,data:e.items,categoryID:o.id},a))})]})]}),c&&(0,r.jsx)(z,{type:"desktop-dropdown",id:"dropdownCompany",title:c.title,data:c,categoryID:c.id}),d&&(0,r.jsx)(_(),{className:"nav-item nav-link track-link-header",href:b,children:d.title}),p&&(0,r.jsx)(z,{type:"desktop-dropdown",id:"dropdownResources",title:p.title,data:p,categoryID:p.id}),m&&(0,r.jsx)(_(),{className:"d-none d-lg-block nav-item nav-link ps-0 pe-3 track-link-header",href:u,children:(0,r.jsx)("svg",{version:"1.1",xmlns:"http://www.w3.org/2000/svg",width:"20",height:"21",viewBox:"0 0 20 21",children:(0,r.jsx)("use",{href:"/img/navbar-icon/Search.svg#Search"})})}),h.map((e,a)=>(0,r.jsx)(P.Z,{className:"nav-item nav-link track-link-header",data:{url:e.path},children:e.title},a))]}),(0,r.jsxs)("div",{className:"collapse navbar-mobile justify-content-end",id:"Mobilenav",ref:t,children:[(0,r.jsxs)("div",{className:"col-12 d-flex justify-content-between d-lg-none navbar-mobile-dropdown align-items-center px-o5 px-gutter px-o5 px-sm-gutter",children:[(0,r.jsx)(F,{}),(0,r.jsx)("button",{className:"navbar-toggler text-secondary align-self-center",type:"button","data-bs-toggle":"collapse","data-bs-target":"#Mobilenav","aria-controls":"Mobilenav","aria-expanded":"false","aria-label":"Toggle navigation",children:(0,r.jsx)(Z.Z,{name:"clear",width:40,height:40})})]}),(0,r.jsx)("div",{id:"mobileDropdownContainer",className:"navbar-nav border-top border-light overflow-x-hidden",children:(0,r.jsxs)("div",{className:"col-12 d-lg-none position-relative accordion",id:"mobileNavigationAccordion",children:[m&&(0,r.jsx)(X,{url:u,icon:"Search.svg#Search",type:"mobile-link",title:m.title}),o&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("a",{role:"button",type:"button","data-bs-toggle":"collapse",className:"py-2 mx-2 h6 d-block my-0 border-bottom border-light link-secondary track-link-header","data-bs-target":"#accordionMobilePlatform","aria-expanded":"false","aria-controls":"accordionMobilePlatform",children:[o.title," ",(0,r.jsx)(R,{name:"keyboard_arrow_down"})]}),(0,r.jsx)("div",{id:"accordionMobilePlatform",className:"accordion-collapse collapse","data-bs-parent":"#mobileNavigationAccordion","aria-labelledby":"accordionMobilePlatform",children:(0,r.jsx)("div",{className:"accordion-body dropdown-products",children:o.items.map((e,a)=>(0,r.jsx)(X,{icon:"".concat(e.id,".svg#").concat(e.id),type:"mobile-link-offcanvas",id:e.id,title:e.title,active:0===a,arrow:!0},a))})}),o.items.map((e,a)=>(0,r.jsx)(V,{type:"offcanvas-content",id:e.id,active:0===a,contentTitle:e.subnavTitle,contentDesc:e.subnavDescription,data:e.items,categoryID:o.id},a))]}),c&&(0,r.jsx)(z,{type:"mobile-accordion",id:"dropdownCompany",title:c.title,data:c,categoryID:c.id}),d&&(0,r.jsx)(X,{type:"mobile-link",title:d.title,url:b}),p&&(0,r.jsx)(z,{type:"mobile-accordion",id:"dropdownResources",title:p.title,data:p,categoryID:c.id}),h.map((e,a)=>(0,r.jsx)(X,{type:"mobile-link",title:e.title,url:e.path},a)),(0,r.jsx)("div",{className:"px-gutter py-2 pt-5",children:(0,r.jsx)(A,{})})]})})]}),(0,r.jsx)("div",{className:"d-none d-md-flex",children:(0,r.jsx)(A,{})}),(0,r.jsx)("div",{className:"d-lg-none d-flex ms-3",children:(0,r.jsx)("button",{className:"navbar-toggler text-secondary align-self-center",type:"button","data-bs-toggle":"collapse","data-bs-target":"#Mobilenav","aria-controls":"Mobilenav","aria-expanded":"false","aria-label":"Toggle navigation",children:(0,r.jsx)(Z.Z,{name:"menu",width:40,height:40})})})]})})},K=e=>{let{data:a,altLangs:t}=e,l=new Date().getFullYear(),s=a.find(e=>"languageSelector"===e.id);return(0,r.jsxs)("footer",{className:"footer container-lg border-top border-light",children:[(0,r.jsxs)(y.Z,{className:"gap-md-3 pt-5 pb-4",children:[(0,r.jsxs)(N.Z,{xs:12,md:4,children:[(0,r.jsx)(_(),{className:"text-secondary track-link-footer","aria-label":"MapTiler",href:"/",children:(0,r.jsx)("svg",{className:"ms-ngutter",width:"165",height:"45",children:(0,r.jsx)("use",{href:"/styles/style/logo/maptiler-logo-adaptive.svg?123#maptilerLogo"})})}),(0,r.jsx)("p",{className:"text-secondary mb-1",children:"Follow\xa0us\xa0on"}),(0,r.jsxs)("div",{className:"social-circles ms-no5",children:[(0,r.jsx)("a",{target:"_blank",className:"twitter track-link-footer",href:"https://twitter.com/MapTiler","aria-label":"Twitter"}),(0,r.jsx)("a",{target:"_blank",className:"bsky track-link-footer",href:"https://bsky.app/profile/maptiler.bsky.social","aria-label":"Bluesky"}),(0,r.jsx)("a",{target:"_blank",className:"linkedin track-link-footer",href:"https://www.linkedin.com/company/maptiler","aria-label":"LinkedIn"}),(0,r.jsx)("a",{target:"_blank",className:"facebook track-link-footer",href:"https://www.facebook.com/maptiler/","aria-label":"Facebook"}),(0,r.jsx)("a",{target:"_blank",className:"instagram track-link-footer",href:"https://www.instagram.com/maptiler/","aria-label":"Instagram"}),(0,r.jsx)("a",{target:"_blank",className:"youtube track-link-footer",href:"https://www.youtube.com/channel/UCubcQeWuBKvqpMu172CLEXw","aria-label":"YouTube"})]}),(0,r.jsx)("div",{className:"ms-no5",children:(0,r.jsx)(I,{data:s,altLangs:t})})]}),null==a?void 0:a.map((e,a)=>{var t;return"languageSelector"!==e.id&&(0,r.jsxs)(N.Z,{xs:12,md:!0,className:"link-list",children:[(0,r.jsx)("h6",{children:e.title}),null===(t=e.items)||void 0===t?void 0:t.map((a,t)=>"INTERNAL"===a.type?(0,r.jsx)(_(),{href:a.path,className:"track-link-footer track-link-category-".concat(e.id),children:a.title},t):(0,r.jsx)("a",{href:a.path,className:"track-link-footer track-link-category-".concat(e.id),children:a.title},t))]},a)})]}),(0,r.jsxs)(y.Z,{className:"justify-content-center border-top border-light py-4",children:[(0,r.jsx)(N.Z,{md:6,className:"order-2 order-md-0 text-sm-center text-md-start",children:(0,r.jsxs)("p",{className:"text-secondary my-gutter",children:["\xa9\xa0",l,"\xa0MapTiler.\xa0All\xa0rights\xa0reserved."]})}),(0,r.jsxs)(N.Z,{md:6,className:"d-flex align-items-center gap-1 gap-sm-3 flex-column flex-sm-row justify-content-start justify-content-sm-center justify-content-md-end",children:[(0,r.jsx)(_(),{className:"text-secondary track-link-footer hover-primary me-auto mx-sm-0",href:"/privacy-policy/",children:"Privacy\xa0Policy"}),(0,r.jsx)(_(),{className:"text-secondary track-link-footer hover-primary me-auto mx-sm-0",href:"/terms/",children:"Terms\xa0of\xa0Use"}),(0,r.jsx)("a",{type:"button",className:"text-secondary track-link-footer hover-primary me-auto mx-sm-0",id:"csconsentlink",children:"Cookie\xa0settings"})]})]})]})};var G=e=>{var a;let{children:t,shape:n,settings:d,layoutData:m,altLangs:p}=e,h=d||{NavbarVisible:!0,FooterVisible:!0,ContainerEnabled:!0},{footerRes:b,navbarRes:x,contactFormRes:g}=m,v=(0,i.useContext)(c.k),f=(0,s.useRouter)(),j=f.asPath.split("?")[0],{seo:k,metaTitleSuffix:y}=v.attributes,{attributes:N}=k.metaImage.data,M="en"!==f.locale&&p&&!p.includes("en")?"/".concat(f.locale):"";return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.lX,{titleTemplate:"%s | ".concat(y),title:k.metaTitle,description:k.metaDescription,canonical:"https://www.maptiler.com"+M+j,twitter:{cardType:"summary_large_image"},openGraph:{title:k.metaTitle,description:k.metaDescription,...k.metaImage.data&&{images:[{url:N.url,width:N.width,height:N.height,alt:N.alternativeText}]}}}),h.NavbarVisible&&("en"===f.locale?(0,r.jsx)(J,{data:x}):(0,r.jsx)(S,{data:x})),(0,r.jsx)(u,{data:null==n?void 0:null===(a=n.data)||void 0===a?void 0:a.attributes}),(0,r.jsx)(E,{children:h.ContainerEnabled?(0,r.jsx)(o.Z,{fluid:"lg",children:t}):t}),h.FooterVisible&&("en"===f.locale?(0,r.jsx)(K,{data:b,altLangs:p}):(0,r.jsx)(L,{data:b,altLangs:p})),g&&(0,r.jsx)(w,{data:g})]})}},45087:function(e,a,t){"use strict";t.d(a,{iv:function(){return n},jf:function(){return i},zL:function(){return s}});var r=t(85893);t(67294);var l=t(29156);let s=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),n=(e,a,t,r)=>{let s=window.MauticJS;s&&s.makeCORSRequest("POST","".concat((0,l.cl)("https://www.maptiler.com/m/mtc.js"),"/form/submit"),e,()=>{r&&r({message:a||"Message was successfully sent!",type:"success",show:!0})},()=>{r&&r({message:t||"There was an error sending your message!",type:"error",show:!0})})},i=e=>{let{message:a="notification",type:t="info",show:l}=e;return(0,r.jsx)("div",{id:"flashes",className:"flashes right ".concat(l?"":"closed"),children:(0,r.jsx)("div",{id:"emailNotification",className:"message ".concat(t),children:a})})}},29156:function(e,a,t){"use strict";async function r(e){return"clipboard"in navigator?await navigator.clipboard.writeText(e):document.execCommand("copy",!0,e)}t.d(a,{JU:function(){return s},TE:function(){return r},cl:function(){return l}});let l=e=>e.substring(0,e.lastIndexOf("/")),s=()=>navigator.language.split("-")[0]||"en"},11656:function(e){e.exports={headerShape:"styles_headerShape__7sTl9",svg:"styles_svg__S2bxx"}},24654:function(){}}]);