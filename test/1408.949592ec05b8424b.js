try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="b53af45e-29bb-43f3-9d19-eb5c3fc86d0f",e._sentryDebugIdIdentifier="sentry-dbid-b53af45e-29bb-43f3-9d19-eb5c3fc86d0f")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1408],{49952:function(){},21006:function(){},4298:function(e,t,i){e.exports=i(23381)},58676:function(e,t,i){"use strict";i.d(t,{DH:function(){return r.DH},Eo:function(){return r.Eo},Qo:function(){return r.Qo},S9:function(){return D},nl:function(){return z},pb:function(){return r.pb},vo:function(){return r.vo},zf:function(){return r.zf}});var r=i(80816),a=i(47857),o=Object.defineProperty,s=(e,t,i)=>t in e?o(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i,l=(e,t,i)=>(s(e,"symbol"!=typeof t?t+"":t,i),i);let n=Math.sqrt(50),c=Math.sqrt(10),u=Math.sqrt(2);function h(e,t,i){let r,a,o;let s=(t-e)/Math.max(0,i),l=Math.floor(Math.log10(s)),p=s/Math.pow(10,l),d=p>=n?10:p>=c?5:p>=u?2:1;return l<0?(r=Math.round(e*(o=Math.pow(10,-l)/d)),a=Math.round(t*o),r/o<e&&++r,a/o>t&&--a,o=-o):(r=Math.round(e/(o=Math.pow(10,l)*d)),a=Math.round(t/o),r*o<e&&++r,a*o>t&&--a),a<r&&.5<=i&&i<2?h(e,t,2*i):[r,a,o]}function p(e){return Math.max(1,Math.ceil(Math.log(function(e,t){let i=0;for(let t of e)null!=t&&(t=+t)>=t&&++i;return i}(e))/Math.LN2)+1)}var d=Array.prototype.slice;function f(e,t){return e-t}let m=e=>()=>e;function v(){}var x=[[],[[[1,1.5],[.5,1]]],[[[1.5,1],[1,1.5]]],[[[1.5,1],[.5,1]]],[[[1,.5],[1.5,1]]],[[[1,1.5],[.5,1]],[[1,.5],[1.5,1]]],[[[1,.5],[1,1.5]]],[[[1,.5],[.5,1]]],[[[.5,1],[1,.5]]],[[[1,1.5],[1,.5]]],[[[.5,1],[1,.5]],[[1.5,1],[1,1.5]]],[[[1.5,1],[1,.5]]],[[[.5,1],[1.5,1]]],[[[1,1.5],[1.5,1]]],[[[.5,1],[1,1.5]]],[]];function g(e){return isFinite(e)?e:NaN}function S(e,t){return null!=e&&+e>=t}function T(e){return null==e||isNaN(e=+e)?-1/0:e}function b(e,t,i,r){let a=r-t,o=i-t,s=isFinite(a)||isFinite(o)?a/o:Math.sign(a)/Math.sign(o);return isNaN(s)?e:e+s-.5}class w{constructor(e){l(this,"polygon"),l(this,"bbox",{n:0,e:0,s:0,w:0}),l(this,"bboxArea",0),l(this,"isLineageSorted",!1),l(this,"ascendants",[]),l(this,"descendants",[]),this.polygon=e,this.computeBBox()}getPolygon(){return this.polygon}computeBBox(){let e=1/0,t=1/0,i=-1/0,r=-1/0;for(let a=0;a<this.polygon.coordinates[0].length;a+=1){let o=this.polygon.coordinates[0][a];e=Math.min(e,o[1]),t=Math.min(t,o[0]),i=Math.max(i,o[1]),r=Math.max(r,o[0])}this.bbox.w=t,this.bbox.s=e,this.bbox.e=r,this.bbox.n=i,this.bboxArea=(r-t)*(i-e)}getBBox(){return this.bbox}getBBoxCenter(){return[(this.bbox.w+this.bbox.e)/2,(this.bbox.s+this.bbox.n)/2]}getBBoxArea(){return this.bboxArea}isPointInBBox(e){return e[0]>=this.bbox.w&&e[0]<=this.bbox.e&&e[1]>=this.bbox.s&&e[1]<=this.bbox.n}isExternalBBoxInBBox(e){return e.n<=this.bbox.n&&e.s>=this.bbox.s&&e.e<=this.bbox.e&&e.w>=this.bbox.w}addNewAscendants(e){this.ascendants.push(e),this.isLineageSorted=!1}addNewDescendants(e){this.descendants.push(e),this.isLineageSorted=!1}establishLineage(e){e!==this&&(this.isExternalBBoxInBBox(e.getBBox())?(this.addNewDescendants(e),e.addNewAscendants(this)):e.isExternalBBoxInBBox(this.bbox)&&(e.addNewDescendants(this),this.addNewAscendants(e)))}sortLineage(){this.ascendants.sort((e,t)=>e.getBBoxArea()-t.getBBoxArea()),this.descendants.sort((e,t)=>t.getBBoxArea()-e.getBBoxArea()),this.isLineageSorted=!0}getNumberOfDescendants(){return this.descendants.length}getNumberOfAscendants(){return this.ascendants.length}isLeafNode(){return 0===this.descendants.length}getDescendants(){return this.isLineageSorted||this.sortLineage(),this.descendants.slice()}getAscendants(){return this.isLineageSorted||this.sortLineage(),this.ascendants.slice()}hasSiblings(){return this.isLineageSorted||this.sortLineage(),0!==this.ascendants.length&&this.ascendants[0].getDescendants().filter(e=>e!==this&&!this.descendants.includes(e)).length>0}hasCousins(){return this.isLineageSorted||this.sortLineage(),!(this.ascendants.length<2)&&this.ascendants[0].hasSiblings()}}let y="PRESSURE_DATASOURCE",M="PRESSURE_LAYER";class C extends r.Im{constructor(e,t,i,o){super(e,null,null),l(this,"stepForZoomLevels",[{zoom:0,step:2}]),l(this,"isolineMaterial"),l(this,"isolineTiles",[]),l(this,"isolineTiles2",new Map),l(this,"min"),l(this,"max"),l(this,"isolineTileThreeContainer",new a.Tme),l(this,"isolineTilePool",new r.$),l(this,"isolineMaterialPool",new r.$),l(this,"channel",null),l(this,"hasLabels",!1),t&&i&&this.initIsoline(t,i,o)}initIsoline(e,t,i){this.init({...e,repaintOnPausedAnimation:!1,maxZoom:0},i,null),this.channel=t.channel??null,this.min=t.min,this.max=t.max,this.stepForZoomLevels=t.stepForZoomLevels.slice().sort((e,t)=>e.zoom-t.zoom),this.isolineMaterial=new a.FIo({glslVersion:a.LSk,uniforms:{opacity:{value:1},renderTransparentArea:{value:e.renderTransparentArea},time:{value:0},zoom:{value:0},tex0size:{value:1},tex1size:{value:1},lineStep:{value:2},lineThickness:{value:t.lineThickness??1},tex0:{value:null},tex1:{value:null},tex0res:{value:[0,0]},tex1res:{value:[0,0]},tex0xy:{value:[0,0]},tex1xy:{value:[0,0]},minValue:{value:this.min},maxValue:{value:this.max},lineColor:{value:(0,r.m$)(t.lineColor)},lineColorTen:{value:(0,r.m$)(t.lineColorTen??t.lineColor)}},defines:{D_BLUR_KERNEL_SIZE:t.renderBlurRadius.toFixed(0),D_CHANNEL:t.channel},vertexShader:"precision highp float;precision highp int;uniform mat4 modelViewMatrix;uniform mat4 projectionMatrix;in vec3 position;in vec2 uv;uniform float tex0size;uniform float tex1size;out vec2 tex0coord;out vec2 tex1coord;out vec2 vUV;void main(){vUV=uv;tex0coord=uv*tex0size;tex1coord=uv*tex1size;gl_Position=projectionMatrix*modelViewMatrix*vec4(position,1.);}",fragmentShader:"precision highp float;precision highp int;precision highp sampler2D;uniform vec4 lineColor;uniform vec4 lineColorTen;uniform float time;uniform sampler2D tex0;uniform sampler2D tex1;uniform vec2 tex0res;uniform vec2 tex1res;uniform float zoom;uniform float minValue;uniform float maxValue;uniform float lineStep;uniform bool renderTransparentArea;uniform float lineThickness;in vec2 tex0coord;in vec2 tex1coord;in vec2 vUV;out vec4 fragColor;float normpdf(in float x,in float sigma){return(1./(sigma*sqrt(2.*3.1415926)))*exp(-0.5*x*x/(sigma*sigma));}int modI(float a,float b){float m=a-floor((a+0.5)/b)*b;return int(floor(m+0.5));}vec2 loopDataline(vec2 uv){if(uv.x<0.0){return vec2(1.+uv.x,uv.y);}else if(uv.x>1.){return vec2(uv.x-1.,uv.y);}return uv;}float circularSamplingBlur(sampler2D image,vec2 uv,vec2 resolution,float distance,out bool isTransparant){const int nbSamples=D_BLUR_KERNEL_SIZE+1;vec4 colorRGBA=texture(image,loopDataline(uv));float color=colorRGBA.D_CHANNEL*3.;isTransparant=colorRGBA.a<1.;float pi=3.141592653589793;float angularStep=2.*pi/float(nbSamples);for(int i=0;i<nbSamples;i++){float angle=angularStep*float(i);vec2 offset=vec2(cos(angle),sin(angle))*distance*1.5;vec2 pos=uv+offset/resolution;vec4 c=texture(image,loopDataline(pos));color+=c.D_CHANNEL;isTransparant=isTransparant||c.a<1.;angle=angularStep/2.+angularStep*float(i);offset=vec2(cos(angle),sin(angle))*distance*2.;pos=uv+offset/resolution;c=texture(image,loopDataline(pos));color+=c.D_CHANNEL;isTransparant=isTransparant||c.a<1.;}return color/float(2*nbSamples+3);}void main(){const int kNbElem=D_BLUR_KERNEL_SIZE*D_BLUR_KERNEL_SIZE;float zoomRatio=1./pow(zoom+0.5,2.);float samplingRatio=2.;float scaledSamplingDistance=(tex0res.x/256.)*samplingRatio/(samplingRatio+zoom);float epsilon=lineThickness/512./pow(max(2.,zoom),2.);float valueRange=maxValue-minValue;int nbStep=int(valueRange/lineStep);float threshStep=1./float(nbStep);vec2 coordN=vec2(tex1coord.x,tex1coord.y+epsilon);vec2 coordS=vec2(tex1coord.x,tex1coord.y-epsilon);vec2 coordE=vec2(tex1coord.x+epsilon,tex1coord.y);vec2 coordW=vec2(tex1coord.x-epsilon,tex1coord.y);float colorN=0.;float colorS=0.;float colorE=0.;float colorW=0.;const int kernelSize=D_BLUR_KERNEL_SIZE;const int kernelHalfSize=(kernelSize-1)/2;bool isTransparantN0=false;bool isTransparantN1=false;bool isTransparantS0=false;bool isTransparantS1=false;bool isTransparantE0=false;bool isTransparantE1=false;bool isTransparantW0=false;bool isTransparantW1=false;float colorN0=circularSamplingBlur(tex0,coordN,tex0res,scaledSamplingDistance,isTransparantN0);float colorN1=circularSamplingBlur(tex1,coordN,tex0res,scaledSamplingDistance,isTransparantN1);float colorS0=circularSamplingBlur(tex0,coordS,tex0res,scaledSamplingDistance,isTransparantS0);float colorS1=circularSamplingBlur(tex1,coordS,tex0res,scaledSamplingDistance,isTransparantS1);float colorE0=circularSamplingBlur(tex0,coordE,tex0res,scaledSamplingDistance,isTransparantE0);float colorE1=circularSamplingBlur(tex1,coordE,tex0res,scaledSamplingDistance,isTransparantE1);float colorW0=circularSamplingBlur(tex0,coordW,tex0res,scaledSamplingDistance,isTransparantW0);float colorW1=circularSamplingBlur(tex1,coordW,tex0res,scaledSamplingDistance,isTransparantW1);bool isTransparant=isTransparantN0||isTransparantN1||isTransparantS0||isTransparantS1||isTransparantE0||isTransparantE1||isTransparantW0||isTransparantW1;if(!renderTransparentArea&&isTransparant){fragColor=vec4(0.,0.,0.,0.);return;}colorN+=mix(colorN0,colorN1,time);colorS+=mix(colorS0,colorS1,time);colorE+=mix(colorE0,colorE1,time);colorW+=mix(colorW0,colorW1,time);int nbStepN=int(colorN/threshStep);int nbStepS=int(colorS/threshStep);int nbStepE=int(colorE/threshStep);int nbStepW=int(colorW/threshStep);bool isLine=nbStepN!=nbStepS||nbStepN!=nbStepE||nbStepN!=nbStepW;if(isLine){float avgColor=(colorN+colorS+colorE+colorW)/4.;float avgPressure=avgColor*(maxValue-minValue)+minValue;float modToTen=mod(avgPressure,10.);if(modToTen<0.5||modToTen>9.5){fragColor=lineColorTen;}else{fragColor=lineColor;}}else{fragColor=vec4(0.,0.,0.,0.);}}",depthTest:!1,depthWrite:!1,transparent:!0}),this.scene.add(this.isolineTileThreeContainer),this.on("playAnimation",()=>{try{this.hidePressureLabels()}catch{}})}onAdd(e,t){super.onAdd(e,t),this.globeTilesService.init({map:e,minZoom:0,maxZoom:0})}disposeObjects(){super.disposeObjects(),this.isolineMaterial.dispose()}getLinestepPerZoomLevel(e){let t=this.stepForZoomLevels.filter(t=>Math.max(0,e)>=t.zoom);return t[t.length-1].step}prerenderInternal(){if(!1===this.getMapOrThrow().isGlobeProjection()){let e=this.slippyTiles.length,t=this.getMapOrThrow().getZoom();this.isolineMaterialPool.init();let i=this.getLinestepPerZoomLevel(t);for(let r=0;r<e;r++){let e=this.slippyTiles[0].length;for(let a=0;a<e;a++){let e=this.slippyTiles[r][a],o=this.isolineTiles[r][a];if(o.scale.x=e.scale.x,o.scale.y=e.scale.y,o.position.x=e.position.x,o.position.y=e.position.y,o.visible=e.visible,!o.visible)continue;let s=e.material,l=this.isolineMaterialPool.isEmpty()?this.isolineMaterialPool.add(this.isolineMaterial.clone()):this.isolineMaterialPool.pop(),n=s.uniforms.tex0.value.image,c=s.uniforms.tex1.value.image;l.uniforms.lineStep.value=i,l.uniforms.tex0res.value[0]=n.width,l.uniforms.tex0res.value[1]=n.height,l.uniforms.tex1res.value[0]=c.width,l.uniforms.tex1res.value[1]=c.height,l.uniforms.zoom.value=t,l.uniforms.tex0.value=s.uniforms.tex0.value,l.uniforms.tex1.value=s.uniforms.tex1.value,l.uniforms.time.value=s.uniforms.time.value,l.uniforms.opacity.value=s.uniforms.opacity.value,l.uniforms.lineThickness.value=this.isolineMaterial.uniforms.lineThickness.value,l.uniforms.lineColor.value=this.isolineMaterial.uniforms.lineColor.value,o.material=l}}}else{let e=[...this.globeTilesService.currentTilesMeshes.entries()].map(([e])=>e),t=this.getMapOrThrow().getZoom();this.isolineMaterialPool.init();let i=this.getLinestepPerZoomLevel(t);for(let r of e){let e=this.globeTilesService.currentTilesMeshes.get(r);if(void 0===e)throw Error("backgroundTile is undefined");let a=this.isolineTiles2.get(r);if(void 0===a)throw Error("isolineTile is undefined");if(a.scale.x=e.scale.x,a.scale.y=e.scale.y,a.position.x=e.position.x,a.position.y=e.position.y,a.visible=e.visible,!a.visible)continue;let o=e.material,s=this.isolineMaterialPool.isEmpty()?this.isolineMaterialPool.add(this.isolineMaterial.clone()):this.isolineMaterialPool.pop(),l=o.uniforms.tex0.value.image,n=o.uniforms.tex1.value.image;s.uniforms.lineStep.value=i,s.uniforms.tex0res.value[0]=l.width,s.uniforms.tex0res.value[1]=l.height,s.uniforms.tex1res.value[0]=n.width,s.uniforms.tex1res.value[1]=n.height,s.uniforms.zoom.value=t,s.uniforms.tex0.value=o.uniforms.tex0.value,s.uniforms.tex1.value=o.uniforms.tex1.value,s.uniforms.time.value=o.uniforms.time.value,s.uniforms.opacity.value=o.uniforms.opacity.value,s.uniforms.lineThickness.value=this.isolineMaterial.uniforms.lineThickness.value,s.uniforms.lineColor.value=this.isolineMaterial.uniforms.lineColor.value,a.material=s}}}refresh(){if(super.refresh(),this.isolineTileThreeContainer.clear(),this.isolineTilePool.init(),!1===this.getMapOrThrow().isGlobeProjection()){this.isolineTiles=[];let e=this.slippyTiles.length;for(let t=0;t<e;t++){let e=this.slippyTiles[0].length;this.isolineTiles[t]=[];for(let i=0;i<e;i++){let e=this.slippyTiles[t][i],r=this.isolineTilePool.isEmpty()?this.isolineTilePool.add(e.clone()):this.isolineTilePool.pop();this.isolineTiles[t][i]=r,this.isolineTileThreeContainer.add(r)}}}else for(let e of(this.isolineTiles2=new Map,[...this.globeTilesService.currentTilesMeshes.entries()].map(([e])=>e))){let t=this.globeTilesService.currentTilesMeshes.get(e);if(void 0===t)continue;let i=t.clone();this.isolineTiles2.set(e,i),this.isolineTileThreeContainer.add(i),i.material=new a.vBJ({color:16711680,depthTest:!1,depthWrite:!1,transparent:!0})}}showPressureLabels(e={}){let{zxy:t,blurKernel:i,step:a,outputSize:o,labelTextSize:s,labelValueSize:l,labelTextHighValues:n,labelTextLowValues:c,highColor:u,lowColor:C,threshold:z,labelTextUp:P,labelOutlineColor:E}={zxy:"0/0/0",blurKernel:5,step:2,outputSize:-1,labelTextSize:1.5,labelValueSize:1,labelTextHighValues:"H",labelTextLowValues:"L",highColor:"#FF0000",lowColor:"#0000FF",threshold:1013,labelTextUp:!0,labelOutlineColor:"#FFFFFF",...e},R=Array.from({length:255/a},(e,t)=>t*a),D=this.computeCurrentMixedImage({zxy:t,blurKernel:i,outputSize:o,channel:this.channel});if(!D)throw Error("Impossible to compute labels");let _=Array(D.width*D.height),A=r.Oq[this.channel];for(let e=0;e<_.length;e+=1)_[e]=D.data[e*D.channels+A];let O=(function(){var e=1,t=1,i=p,r=l;function a(e){var t=i(e);if(Array.isArray(t))t=t.slice().sort(f);else{let i=function(e,t){let i,r;if(void 0===t)for(let t of e)null!=t&&(void 0===i?t>=t&&(i=r=t):(i>t&&(i=t),r<t&&(r=t)));else{let a=-1;for(let o of e)null!=(o=t(o,++a,e))&&(void 0===i?o>=o&&(i=r=o):(i>o&&(i=o),r<o&&(r=o)))}return[i,r]}(e,g);for(t=function(e,t,i){if(t=+t,e=+e,!((i=+i)>0))return[];if(e===t)return[e];let r=t<e,[a,o,s]=r?h(t,e,i):h(e,t,i);if(!(o>=a))return[];let l=o-a+1,n=Array(l);if(r){if(s<0)for(let e=0;e<l;++e)n[e]=-((o-e)/s);else for(let e=0;e<l;++e)n[e]=(o-e)*s}else if(s<0)for(let e=0;e<l;++e)n[e]=-((a+e)/s);else for(let e=0;e<l;++e)n[e]=(a+e)*s;return n}(...function(e,t,i){let r;for(;;){let a=h(+e,+t,+i)[2];if(a===r||0===a||!isFinite(a))return[e,t];a>0?(e=Math.floor(e/a)*a,t=Math.ceil(t/a)*a):a<0&&(e=Math.ceil(e*a)/a,t=Math.floor(t*a)/a),r=a}}(i[0],i[1],t),t);t[t.length-1]>=i[1];)t.pop();for(;t[1]<i[0];)t.shift()}return t.map(t=>o(e,t))}function o(i,a){let o=null==a?NaN:+a;if(isNaN(o))throw Error(`invalid value: ${a}`);var l=[],n=[];return function(i,r,a){var o,l,n,c,u,h,p=[],d=[];for(o=l=-1,x[(c=S(i[0],r))<<1].forEach(f);++o<e-1;)x[(n=c)|(c=S(i[o+1],r))<<1].forEach(f);for(x[c<<0].forEach(f);++l<t-1;){for(o=-1,x[(c=S(i[l*e+e],r))<<1|(u=S(i[l*e],r))<<2].forEach(f);++o<e-1;)n=c,c=S(i[l*e+e+o+1],r),h=u,x[n|c<<1|(u=S(i[l*e+o+1],r))<<2|h<<3].forEach(f);x[c|u<<3].forEach(f)}for(o=-1,x[(u=i[l*e]>=r)<<2].forEach(f);++o<e-1;)h=u,x[(u=S(i[l*e+o+1],r))<<2|h<<3].forEach(f);function f(e){var t,i,r=[e[0][0]+o,e[0][1]+l],n=[e[1][0]+o,e[1][1]+l],c=s(r),u=s(n);(t=d[c])?(i=p[u])?(delete d[t.end],delete p[i.start],t===i?(t.ring.push(n),a(t.ring)):p[t.start]=d[i.end]={start:t.start,end:i.end,ring:t.ring.concat(i.ring)}):(delete d[t.end],t.ring.push(n),d[t.end=u]=t):(t=p[u])?(i=d[c])?(delete p[t.start],delete d[i.end],t===i?(t.ring.push(n),a(t.ring)):p[i.start]=d[t.end]={start:i.start,end:t.end,ring:i.ring.concat(t.ring)}):(delete p[t.start],t.ring.unshift(r),p[t.start=c]=t):p[c]=d[u]={start:c,end:u,ring:[r,n]}}x[u<<3].forEach(f)}(i,o,function(e){r(e,i,o),function(e){for(var t=0,i=e.length,r=e[i-1][1]*e[0][0]-e[i-1][0]*e[0][1];++t<i;)r+=e[t-1][1]*e[t][0]-e[t-1][0]*e[t][1];return r}(e)>0?l.push([e]):n.push(e)}),n.forEach(function(e){for(var t,i=0,r=l.length;i<r;++i)if(-1!==function(e,t){for(var i,r=-1,a=t.length;++r<a;)if(i=function(e,t){for(var i=t[0],r=t[1],a=-1,o=0,s=e.length,l=s-1;o<s;l=o++){var n=e[o],c=n[0],u=n[1],h=e[l],p=h[0],d=h[1];if(function(e,t,i){var r,a,o,s;return(t[0]-e[0])*(i[1]-e[1])==(i[0]-e[0])*(t[1]-e[1])&&(a=e[r=+(e[0]===t[0])],o=i[r],s=t[r],a<=o&&o<=s||s<=o&&o<=a)}(n,h,t))return 0;u>r!=d>r&&i<(p-c)*(r-u)/(d-u)+c&&(a=-a)}return a}(e,t[r]))return i;return 0}((t=l[i])[0],e)){t.push(e);return}}),{type:"MultiPolygon",value:a,coordinates:l}}function s(t){return 2*t[0]+t[1]*(e+1)*4}function l(i,r,a){i.forEach(function(i){var o=i[0],s=i[1],l=0|o,n=0|s,c=T(r[n*e+l]);o>0&&o<e&&l===o&&(i[0]=b(o,T(r[n*e+l-1]),c,a)),s>0&&s<t&&n===s&&(i[1]=b(s,T(r[(n-1)*e+l]),c,a))})}return a.contour=o,a.size=function(i){if(!arguments.length)return[e,t];var r=Math.floor(i[0]),o=Math.floor(i[1]);if(!(r>=0&&o>=0))throw Error("invalid size");return e=r,t=o,a},a.thresholds=function(e){return arguments.length?(i="function"==typeof e?e:Array.isArray(e)?m(d.call(e)):m(e),a):i},a.smooth=function(e){return arguments.length?(r=e?l:v,a):r===l},a})().size([D.width,D.height]).thresholds(R)(_),I=this.getMapOrThrow();this.hidePressureLabels();let L=O.map(e=>(function(e,t=!1){let i=["coordinates","type"],r=Object.getOwnPropertyNames(e).filter(e=>!i.includes(e));return e.coordinates.map(i=>{let a={type:"Polygon",coordinates:i};return r.forEach(t=>{Object.defineProperty(a,t,{value:e[t],configurable:!0,writable:!0})}),t?function(e){let t=["coordinates","type"],i=Object.getOwnPropertyNames(e).filter(e=>!t.includes(e));return e.coordinates.map((t,r)=>{r>0&&t.reverse();let a={type:"Polygon",coordinates:[t]};return i.forEach(t=>{Object.defineProperty(a,t,{value:e[t],configurable:!0,writable:!0})}),a})}(a):[a]}).reduce((e,t)=>e.concat(t),[])})(e,!0)).reduce((e,t)=>e.concat(t),[]);L.forEach(e=>(function(e,t){for(let i=0;i<e.coordinates.length;i+=1){let r=e.coordinates[i];for(let e=0;e<r.length;e+=1){let i=r[e],a=t(i);i[0]=a[0],i[1]=a[1]}}})(e,e=>[e[0]/D.width*360-180,-1*(0,r._x)(e[1],D.height)]));let F=L.map(e=>new w(e));for(let e=0;e<F.length;e+=1)for(let t=e+1;t<F.length;t+=1)F[e].establishLineage(F[t]);F.forEach(e=>e.sortLineage());let N=F.filter(e=>!e.hasSiblings()&&e.isLeafNode()).map(e=>{let t=e.getPolygon().value/256*(this.max-this.min)+this.min;return{type:"Feature",properties:{labelText:t>z?n:c,labelValue:t>z?Math.ceil(t).toString():Math.floor(t).toString(),color:t>z?u:C},geometry:{type:"Point",coordinates:e.getBBoxCenter()}}});I.addSource(y,{type:"geojson",data:{type:"FeatureCollection",features:N}}),I.addLayer({id:M,type:"symbol",source:y,layout:{"text-field":["format",["get",P?"labelText":"labelValue"],{"font-scale":P?s:l},`
`,{},["get",P?"labelValue":"labelText"],{"font-scale":P?l:s}],"text-variable-anchor":["center"],"text-justify":"center"},paint:{"text-halo-blur":1,"text-halo-color":E,"text-halo-width":1.5,"text-color":["get","color"]}}),this.hasLabels=!0}setLineThickness(e){this.isolineMaterial.uniforms.lineThickness.value=e,this.isolineMaterial.needsUpdate=!0}setLineColor(e){this.isolineMaterial.uniforms.lineColor.value=(0,r.m$)(e),this.isolineMaterial.needsUpdate=!0}hidePressureLabels(){if(this.hasLabels)try{let e=this.getMapOrThrow();e.getLayer(M)&&e.removeLayer(M),e.getSource(y)&&e.removeSource(y),this.hasLabels=!1}catch{}}setAnimationTime(e){this.hidePressureLabels(),super.setAnimationTime(e)}}class z extends C{constructor(e={}){super(e.id||"MapTiler Isoline Pressure",null,null,null),l(this,"constructorOptions"),l(this,"isSourceReady",!1),l(this,"colorRamp"),this.constructorOptions=e}async onAdd(e,t){let i=e.getSdkConfig().apiKey,a=e.getMaptilerSessionId();r.hE.hasData()||await r.hE.fetchLatest(i,a);let o=r.hE.getPressureData();if(!o)throw Error("The latest weather data is not avaialble");this.colorRamp=this.constructorOptions.colorramp??r.Eo.builtin.PRESSURE_4,this.initIsoline({minZoom:o.metadata.minzoom,maxZoom:o.metadata.maxzoom,repaintOnPausedAnimation:!1},{channel:o.metadata.weather_variable.decoding.channels.toLowerCase(),min:o.metadata.weather_variable.decoding.min,max:o.metadata.weather_variable.decoding.max,lineThickness:this.constructorOptions.lineThickness??.8,lineColor:this.constructorOptions.lineColor??[0,0,0,40],lineColorTen:this.constructorOptions.lineColorTen??[0,0,0,120],renderBlurRadius:this.constructorOptions.renderBlurRadius??4,stepForZoomLevels:this.constructorOptions.stepForZoomLevels??[{zoom:0,step:4},{zoom:3,step:2},{zoom:5,step:1}]},[new r.yy({decode:{channel:o.metadata.weather_variable.decoding.channels.toLowerCase(),min:o.metadata.weather_variable.decoding.min,max:o.metadata.weather_variable.decoding.max},stops:this.colorRamp,smooth:this.constructorOptions.smooth??!0,opacity:this.constructorOptions.opacity??.8})]),super.onAdd(e,t),r.hE.getSourcesAndTimestamps(o,i,a).forEach(e=>{this.addSource(e.timestamp,e.source)});let s=+new Date/1e3;s>=this.getAnimationStart()&&s<=this.getAnimationEnd()&&this.setAnimationTime(s),this.isSourceReady=!0,this.emit("sourceReady",{map:e,layer:this})}getIsSourceReady(){return this.isSourceReady}pickAt(e,t){let i=super.pick(e,t);return i?{value:i[0]}:null}getColorRamp(){return this.colorRamp}onSourceReadyAsync(){return new Promise(e=>{if(this.isSourceReady)return e();this.once("sourceReady",()=>{e()})})}}var P=`precision highp float;
#define M_PI 3.14159265358
uniform float tileScale;uniform vec2 tilePosition;uniform vec2 tex0xy;uniform vec2 tex1xy;uniform float tex0size;uniform float tex1size;uniform sampler2D rttTexture;uniform sampler2D rttTextureTileValues;uniform vec2 rttXY;uniform float rttScale;uniform float time;uniform sampler2D tex0;uniform sampler2D tex1;uniform float zoom;uniform bool worldSpaceConstant;uniform float worldSpaceFactor;uniform float timestep;uniform bool outputTileValues;in vec2 rttCoord;out vec4 fragColor;void main(){vec4 color=texture(rttTexture,rttCoord);vec2 pos=color.ba+color.rg/255.0;vec2 posInTile=(rttXY+rttScale*pos)/tileScale-tilePosition;if(posInTile.x>=0.&&posInTile.x<=1.&&posInTile.y>=0.&&posInTile.y<=1.){posInTile.y=1.0-posInTile.y;vec2 tex0coord=tex0xy+posInTile*tex0size;vec2 tex1coord=tex1xy+posInTile*tex1size;vec4 tex0Color=texture(tex0,tex0coord);vec4 tex1Color=texture(tex1,tex1coord);if(outputTileValues){fragColor=mix(tex0Color,tex1Color,time);return;}vec2 uv;if(D_WAVES){vec2 value0=tex0Color.D_CHANNELS;vec2 value1=tex1Color.D_CHANNELS;float dir0=2.0*M_PI*(-value0.x-0.25);float dir1=2.0*M_PI*(-value1.x-0.25);float speed=worldSpaceConstant ? mix(value0.y,value1.y,time): sqrt(1.0/mix(value0.y,value1.y,time));if(tex0Color.a<1.0||tex1Color.a<1.0){speed=0.0;}uv=vec2(mix(cos(dir0),cos(dir1),time),mix(sin(dir0),sin(dir1),time))*(D_MIN+(D_MAX-D_MIN)*speed);}else{vec4 lookup=mix(tex0Color,tex1Color,time);vec2 value=lookup.D_CHANNELS;uv=(D_MIN+(D_MAX-D_MIN)*value);}vec2 offset=vec2(1.,1.);if(SPEED_WEIGHTED_BY_VECTOR_NORM==true){offset=vec2(uv.x,-uv.y)*timestep*(worldSpaceConstant ? pow(2.,zoom)*worldSpaceFactor : 1.);}else{offset=normalize(vec2(uv.x,-uv.y))*timestep*(worldSpaceConstant ? pow(2.,zoom)*worldSpaceFactor : 1.);}pos=pos+offset;fragColor=vec4(fract(pos*255.0),floor(pos*255.0)/255.0);}else{if(outputTileValues){fragColor=texture(rttTextureTileValues,rttCoord);return;}fragColor=color;}}`;class E extends r.Im{constructor(e,t,i,r){super(e,null,null),l(this,"rttScene",new a.xsS),l(this,"rttCamera"),l(this,"rttMaterial"),l(this,"rttMesh"),l(this,"particleTexturePrev"),l(this,"particleTexture0"),l(this,"particleTexture1"),l(this,"particleTexture0TileValues"),l(this,"particleTexture1TileValues"),l(this,"particleMaterial"),l(this,"particles"),l(this,"particleBackground"),l(this,"accumulator"),l(this,"accumulatorDrawMesh"),l(this,"particleDensity"),l(this,"refreshInterval"),l(this,"flipFlop",!1),l(this,"numParticles"),l(this,"particleColor"),l(this,"particleFastColor"),l(this,"particleFastSpeed"),l(this,"particleSize"),l(this,"particleSpeed"),l(this,"pixelRatio"),l(this,"prevRenderTime",0),l(this,"lastRenderTime",0),l(this,"forceRender",!0),l(this,"rttTimestep"),l(this,"intervalID"),l(this,"lastRefreshTime",0),l(this,"worldSpaceConstant",!1),l(this,"worldSpaceFactor",.005),l(this,"arrowScaledOnChannel",!1),t&&i&&this.initArrow(t,i,r)}initArrow(e,t,i){if(this.init(e,i,null),this.extentScale=1.2,this.numParticles=128,null!=t.maxAmount){if(t.maxAmount>=4&&t.maxAmount&&!(t.maxAmount&t.maxAmount-1))this.numParticles=t.maxAmount;else throw Error("The number of arrows needs to be power of two and at least 4")}if(this.rttTimestep=200,null!=t.rttTimestep){if(t.rttTimestep>=0)this.rttTimestep=t.rttTimestep;else throw Error("rttTimestep needs to be greater than 0")}let o=window.devicePixelRatio;this.worldSpaceConstant=t.worldSpaceConstant??this.worldSpaceConstant,this.worldSpaceFactor=t.worldSpaceFactor??this.worldSpaceFactor,this.particleDensity=t.density??2,this.particleDensity/=o*o,this.refreshInterval=t.refreshInterval??800,this.particleColor=(0,r.WT)(t.color??[255,255,255,128]),this.particleFastColor=t.fastColor?(0,r.WT)(t.fastColor):this.particleColor,this.particleFastSpeed=(t.fastSpeed??2)*o,this.particleSize=t.size??1.5,this.particleSpeed=(t.speed??.001)/1e3,this.pixelRatio=t.pixelRatio??(o>1?1:2),this.particleTexturePrev=this.generateRandomizeParticleTexture(this.numParticles,this.numParticles),this.particleTexture0=new a.dd2(this.numParticles,this.numParticles,{stencilBuffer:!1,depthBuffer:!1}),this.particleTexture0.texture=this.particleTexturePrev.clone(),this.particleTexture1=this.particleTexture0.clone(),this.particleTexture0TileValues=new a.dd2(this.numParticles,this.numParticles,{stencilBuffer:!1,depthBuffer:!1}),this.particleTexture0TileValues.texture=this.generateZeroValueTexture(this.numParticles,this.numParticles),this.particleTexture1TileValues=this.particleTexture0TileValues.clone(),this.rttMaterial=new a.FIo({glslVersion:a.LSk,uniforms:{rttXY:{value:[0,0]},rttScale:{value:1},rttTexture:{value:this.particleTexture0.texture},rttTextureTileValues:{value:this.particleTexture0TileValues.texture},outputTileValues:{value:!1},tileScale:{value:0},tilePosition:{value:[0,0]},time:{value:0},timestep:{value:0},tex0xy:{value:[0,0]},tex1xy:{value:[0,0]},tex0size:{value:1},tex1size:{value:1},zoom:{value:0},worldSpaceConstant:{value:this.worldSpaceConstant},worldSpaceFactor:{value:this.worldSpaceFactor},tex0:{value:null},tex1:{value:null}},vertexShader:"uniform mat4 modelViewMatrix;uniform mat4 projectionMatrix;in vec3 position;in vec2 uv;out vec2 rttCoord;void main(){rttCoord=uv;gl_Position=projectionMatrix*vec4(position,1.);}",fragmentShader:P,defines:{D_MIN:(t.decodeMin??0).toFixed(2),D_MAX:(t.decodeMax??1).toFixed(2),D_CHANNELS:t.decodeChannels??"rg",D_WAVES:t.decodeAsWaves?"true":"false",SPEED_WEIGHTED_BY_VECTOR_NORM:(t.speedWeightedByVectorNorm??!0).toString()},depthTest:!1,depthWrite:!1}),this.rttMesh=new a.Kj0(new a._12(this.numParticles,this.numParticles),this.rttMaterial),this.rttScene.add(this.rttMesh),this.rttCamera=new a.iKG(-this.numParticles/2,this.numParticles/2,this.numParticles/2,-this.numParticles/2,-100,100),this.accumulator=new a.dd2(16,16,{minFilter:a.wem,magFilter:a.wem,stencilBuffer:!1,format:a.wk1,depthBuffer:!1}),this.particleBackground=new a.Kj0(new a._12(2,2),new a.FIo({glslVersion:a.LSk,transparent:!0,blending:a.Xaj,blendEquationAlpha:a.rOj,vertexShader:"precision highp float;in vec3 position;void main(){gl_Position=vec4(position,1.);}",fragmentShader:"precision highp float;out vec4 fragColor;void main(){fragColor=vec4(.0,.0,.0,1.0);}"}));let s=o*this.pixelRatio*this.particleSize;this.arrowScaledOnChannel="object"==typeof t.scaleSize&&"decodeMin"in t.scaleSize&&"decodeMax"in t.scaleSize&&"decodeChannels"in t.scaleSize,this.lastRefreshTime=0,this.particleMaterial=new a.FIo({glslVersion:a.LSk,uniforms:{rttSize:{value:this.numParticles},rttTexture:{value:this.particleTexture1.texture},rttTexturePrev:{value:this.particleTexturePrev},rttTextureTileValues:{value:this.particleTexture1TileValues.texture},scaleSizeCase:{value:this.arrowScaledOnChannel?2:!1===t.scaleSize?0:1},arrowMinSize:{value:"object"==typeof t.scaleSize&&"arrowMinSize"in t.scaleSize?t.scaleSize.arrowMinSize:0},scaleSizeChannelDecodeMin:{value:"object"==typeof t.scaleSize&&"decodeMin"in t.scaleSize?t.scaleSize.decodeMin:0},scaleSizeChannelDecodeMax:{value:"object"==typeof t.scaleSize&&"decodeMax"in t.scaleSize?t.scaleSize.decodeMax:0},screenSize:{value:2},extrapolationFactor:{value:0},renderStepSize:{value:0},randomizedQuadrant:{value:0},timeSinceLastRefresh:{value:0},colorSpeed:{value:this.particleFastSpeed},zoom:{value:0},worldSpaceConstant:{value:this.worldSpaceConstant},worldSpaceFactor:{value:this.worldSpaceFactor},opacity:{value:1},particleSize:{value:s},particleDensity:{value:this.particleDensity},refreshInterval:{value:this.refreshInterval}},vertexShader:"precision highp float;precision highp int;in float position;uniform sampler2D rttTexture;uniform sampler2D rttTexturePrev;uniform sampler2D rttTextureTileValues;uniform float rttSize;uniform float screenSize;uniform float particleSize;uniform int scaleSizeCase;uniform bool worldSpaceConstant;uniform float worldSpaceFactor;uniform float extrapolationFactor;uniform float renderStepSize;uniform float randomizedQuadrant;uniform float zoom;uniform float particleDensity;uniform float colorSpeed;uniform float scaleSizeChannelDecodeMin;uniform float scaleSizeChannelDecodeMax;out float speed;out vec2 dir;out float visibilityPhase;out float scaleFactor;vec4 advance(float position_){if(position_>(particleDensity/1000.0)*screenSize*screenSize){speed=0.0;return vec4(0.0);}float x=fract(position_/rttSize);float y=floor(position_/rttSize)/rttSize;vec4 color=texture(rttTexture,vec2(x,y));vec2 pos=color.ba+color.rg/255.0;vec4 colorPrev=texture(rttTexturePrev,vec2(x,y));vec2 posPrev=colorPrev.ba+colorPrev.rg/255.0;vec2 diff=pos-posPrev;pos+=diff*extrapolationFactor;speed=renderStepSize*step(ZERO_SPEED,dot(posPrev,posPrev))*length(diff*screenSize);if(scaleSizeCase==0){scaleFactor=1.;}else if(scaleSizeCase==1){scaleFactor=(1./colorSpeed)*speed*(worldSpaceConstant ?(100000./screenSize)/pow(2.,zoom): 1.);}else{vec4 tileValues=texture(rttTextureTileValues,vec2(x,y));float channelWorldValue=tileValues.SCALE_SIZE_CHANNEL*(scaleSizeChannelDecodeMax-scaleSizeChannelDecodeMin)+scaleSizeChannelDecodeMin;scaleFactor=channelWorldValue/scaleSizeChannelDecodeMax;}return vec4(pos,diff);}void main(){float x=mod(position,rttSize);float y=floor(position/rttSize);float quadrantSize=rttSize/2.0;float quadrant=floor(x/quadrantSize)*2.0+floor(y/quadrantSize);visibilityPhase=distance(mod((4.0-randomizedQuadrant+quadrant),4.0),3.0);vec4 posdiff=advance(position);vec2 pos=2.0*(posdiff.xy)-1.0;dir=normalize(posdiff.zw);gl_PointSize=particleSize;gl_Position=vec4(pos.x,-pos.y,0.,1.);}",fragmentShader:"precision highp float;precision highp int;precision highp sampler2D;uniform float timeSinceLastRefresh;uniform float colorSpeed;uniform float zoom;uniform bool worldSpaceConstant;uniform float worldSpaceFactor;uniform float screenSize;uniform float opacity;uniform float particleSize;uniform float refreshInterval;uniform int scaleSizeCase;uniform float arrowMinSize;in float speed;in vec2 dir;in float visibilityPhase;in float scaleFactor;out vec4 fragColor;float line(vec2 p,vec2 p1,vec2 p2){vec2 center=(p1+p2)*0.5;float len=length(p2-p1);vec2 dir=(p2-p1)/len;vec2 rel_p=p-center;float dist1=abs(dot(rel_p,vec2(dir.y,-dir.x)));float dist2=abs(dot(rel_p,dir))-0.5*len;return max(dist1,dist2);}float arrowHead(vec2 p,vec2 v){float x=0.4;float y=0.25;vec2 a=x*v+y*vec2(-v.y,v.x);vec2 b=x*v+y*vec2(v.y,-v.x);float d=(a.y-b.y)*(v.x-b.x)+(b.x-a.x)*(v.y-b.y);float _a=((a.y-b.y)*(p.x-b.x)+(b.x-a.x)*(p.y-b.y))/d;float _b=((b.y-v.y)*(p.x-b.x)+(v.x-b.x)*(p.y-b.y))/d;float c=1.0-_a-_b;bool isInTriangle=0.0<=_a&&_a<=1.0&&0.0<=_b&&_b<=1.0&&0.0<=c&&c<=1.0;if(isInTriangle){return 0.0;}else{return 1.0;}}float arrow(vec2 p,vec2 v){p-=vec2(0.5);float mag_v=length(v);float mag_p=length(p);if(mag_v>0.0){vec2 dir_v=v/mag_v;mag_v=clamp(mag_v,0.1,0.5);v=dir_v*mag_v;float shaft=line(p,v-dir_v*0.1,-v);float head=arrowHead(p,v);return min(shaft,head);}else{return mag_p;}}void main(){if(scaleFactor<0.){discard;return;}float colorSpeedWeighted=colorSpeed*(worldSpaceConstant ? pow(2.,zoom)*worldSpaceFactor : 1.);float colorFactor=min(1.0,(speed*(1000./screenSize))/colorSpeedWeighted);vec4 arrowColor=mix(COLOR_A,COLOR_B,colorFactor);float arrowDist=arrow(gl_PointCoord.xy,dir*(scaleFactor+arrowMinSize/particleSize));float arrowShapeAlpha=clamp(arrowDist*particleSize/2.0,0.0,1.0)*-1.0+1.0;float x=visibilityPhase+timeSinceLastRefresh/refreshInterval;float up=x;float down=-1.0*x+4.0;float downQ=step(2.0,x);float upQ=mod(downQ+1.0,2.0);float phaseOpacity=upQ*up+downQ*down;float speedOpacity=step(ZERO_SPEED,speed);float totalOpacity=arrowColor.a*arrowShapeAlpha*phaseOpacity*speedOpacity*opacity;fragColor=vec4(arrowColor.rgb,totalOpacity);}",defines:{COLOR_A:this.particleColor,COLOR_B:this.particleFastColor,ZERO_SPEED:1e-4,SCALE_SIZE_CHANNEL:"object"==typeof t.scaleSize&&"decodeChannels"in t.scaleSize?t.scaleSize.decodeChannels:"r"},transparent:!0,depthTest:!1,depthWrite:!1,linewidth:this.pixelRatio});let l=new Float32Array(Array(this.numParticles*this.numParticles).keys()),n=new a.u9r;n.setAttribute("position",new a.a$l(l,1)),this.particles=new a.woe(n,this.particleMaterial),this.particles.frustumCulled=!1;let c=new a.FIo({glslVersion:a.LSk,premultipliedAlpha:!0,transparent:!0,depthTest:!1,depthWrite:!1,uniforms:{opacity:{value:1},tex0:{value:this.accumulator.texture}},vertexShader:"precision highp float;uniform mat4 modelViewMatrix;uniform mat4 projectionMatrix;in vec3 position;in vec2 uv;out vec2 coord;void main(){coord=uv;gl_Position=projectionMatrix*modelViewMatrix*vec4(position,1.);}",fragmentShader:"precision highp float;uniform float opacity;uniform sampler2D tex0;in vec2 coord;out vec4 fragColor;void main(){vec4 c=texture(tex0,coord);if(c.a<1.0/16.0){discard;}fragColor=c;fragColor.a*=opacity;}"});this.accumulatorDrawMesh=new a.Kj0(new a._12(1,1),c),this.accumulatorDrawMesh.renderOrder=3,this.slippyTilesGroup.add(this.accumulatorDrawMesh),this.globeTilesService.visibleTilesAreaMesh.visible=!0,this.globeTilesService.visibleTilesAreaMesh.material=c,this.intervalID=window.setInterval(()=>{this.renderer&&this.randomizeParticles(!1)},this.refreshInterval),this.on("extentChanged",()=>{this.refresh()})}setArrowSize(e){this.particleMaterial.uniforms.particleSize.value=e*window.devicePixelRatio*this.pixelRatio}setRefreshInterval(e){this.refreshInterval=e,this.particleMaterial.uniforms.refreshInterval.value=e;try{window.clearInterval(this.intervalID)}catch{}this.intervalID=window.setInterval(()=>{this.renderer&&this.randomizeParticles(!1)},this.refreshInterval)}onAdd(e,t){super.onAdd(e,t)}generateRandomizeParticleTexture(e,t){let i=new Uint8Array(4*e*t);for(let e=0;e<i.length;e++)i[e]=Math.floor(256*Math.random());return new a.IEO(i,e,t)}generateZeroValueTexture(e,t,i=0){let r=new Uint8Array(4*e*t);if(0!==i)for(let e=0;e<r.length;e++)r[e]=i;let o=new a.IEO(r,e,t);return o.needsUpdate=!0,o}randomizeParticles(e){if(!this.renderer)return;this.lastRefreshTime=performance.now();let t=this.numParticles,i=this.numParticles,r=new a.FM8(0,0);if(!e){let{randomizedQuadrant:e}=this.particleMaterial.uniforms,a=this.numParticles/2;t=a,i=a;let o=Math.floor(e.value/2),s=e.value%2;r.x=o*a,r.y=s*a,e.value=(e.value+1)%4}let o=this.generateRandomizeParticleTexture(t,i);this.renderer.copyTextureToTexture(r,o,this.particleTexturePrev),this.renderer.copyTextureToTexture(r,o,this.particleTexture0.texture),this.renderer.copyTextureToTexture(r,o,this.particleTexture1.texture),o.dispose(),this.forceRender=e}getParticleUtilization(){let e=this.particleMaterial.uniforms.screenSize.value;return this.particleDensity/1e3*e*e/(this.numParticles*this.numParticles)}setDensity(e){this.particleDensity=e/(window.devicePixelRatio*window.devicePixelRatio),this.particleMaterial.uniforms.particleDensity.value=this.particleDensity}setOpacity(e){super.setOpacity(e),this.particleMaterial.uniforms.opacity.value=e}disposeObjects(){super.disposeObjects(),this.particleTexturePrev.dispose(),this.particleTexture0.texture.dispose(),this.particleTexture0.dispose(),this.particleTexture1.texture.dispose(),this.particleTexture1.dispose(),this.accumulator.texture.dispose(),this.accumulator.dispose(),this.rttMesh.geometry.dispose(),this.rttMesh.material.dispose(),this.particleBackground.geometry.dispose(),this.particleBackground.material.dispose(),this.particles.geometry.dispose(),this.particles.material.dispose(),this.accumulatorDrawMesh.geometry.dispose(),this.accumulatorDrawMesh.material.dispose(),clearInterval(this.intervalID)}refresh(){super.refresh(),!1===this.getMapOrThrow().isGlobeProjection()?this.refreshMercator():this.refreshGlobe(),this.updateRenderingSize();let e=this.getRendererOrThrow();e.setRenderTarget(this.accumulator),e.setClearAlpha(0),e.clearColor(),e.setRenderTarget(null),this.randomizeParticles(!0)}refreshMercator(){let e=this.getVisibleExtent(this.extentScale);if(null===e)throw Error("The extent is null");let t=Math.max(e[2]-e[0],e[3]-e[1])/r.uW,i=[.5+(e[2]+e[0])/2/r.uW-t/2,.5-(e[3]+e[1])/2/r.uW-t/2];this.rttMaterial.uniforms.rttScale.value=t,this.rttMaterial.uniforms.rttXY.value=i}refreshGlobe(){this.rttMaterial.uniforms.rttScale.value=1,this.rttMaterial.uniforms.rttXY.value=[0,0]}updateRenderingSize(){let e=this.getMapOrThrow().getZoom(),t=this.getMapOrThrow().getCanvas(),i=t.width,r=t.height,a=2048*this.pixelRatio,o=a;o=Math.min(o=!0===this.getMapOrThrow().isGlobeProjection()?e>4?a:Math.max(.75*e*a,1024):Math.round(this.pixelRatio*this.extentScale*Math.max(i,r)),this.getRendererOrThrow().capabilities.maxTextureSize),this.particleMaterial.uniforms.screenSize.value=o/this.pixelRatio,this.accumulator.setSize(o,o),this.slippyTilesGroup.remove(this.accumulatorDrawMesh),this.slippyTilesGroup.add(this.accumulatorDrawMesh)}prerenderInternal(){let e=this.getMapOrThrow().getZoom(),t=performance.now(),i=t-this.lastRenderTime,r=this.forceRender||i>this.rttTimestep||!this.prevRenderTime;r&&(this.forceRender=!1,this.rttMaterial.uniforms.timestep.value=this.particleSpeed*i,this.rttMaterial.uniforms.zoom.value=e,this.getRendererOrThrow().setRenderTarget(this.flipFlop?this.particleTexture0:this.particleTexture1),this.getRendererOrThrow().copyFramebufferToTexture(new a.FM8(0,0),this.particleTexturePrev),this.prevRenderTime=this.lastRenderTime,this.lastRenderTime=t,!1===this.getMapOrThrow().isGlobeProjection()?this.prerenderInternalMercator():this.prerenderInternalGlobe(),this.particleMaterial.uniforms.rttTexture.value=(this.flipFlop?this.particleTexture0:this.particleTexture1).texture,this.arrowScaledOnChannel&&(this.particleMaterial.uniforms.rttTextureTileValues.value=(this.flipFlop?this.particleTexture0TileValues:this.particleTexture1TileValues).texture),this.particleMaterial.uniforms.zoom.value=e);let o=this.lastRenderTime-this.prevRenderTime;this.particleMaterial.uniforms.extrapolationFactor.value=r?0:i/o,this.particleMaterial.uniforms.renderStepSize.value=o/1e3,this.particleMaterial.uniforms.timeSinceLastRefresh.value=t-this.lastRefreshTime;let s=this.getRendererOrThrow();s.setRenderTarget(this.accumulator),s.render(this.particleBackground,this.camera),s.render(this.particles,this.camera),s.setRenderTarget(null)}prerenderInternalMercator(){let e=this.slippyTiles.length;for(let t=0;t<e;t++){let e=this.slippyTiles[0].length;for(let i=0;i<e;i++){let e=this.slippyTiles[t][i];if(!e.visible)continue;let r=e.material.uniforms;if(!r)continue;let a=e.scale.x;this.rttMaterial.uniforms.tileScale.value=a,this.rttMaterial.uniforms.tilePosition.value=[e.position.x/a-.5,-e.position.y/a-.5],this.rttMaterial.uniforms.outputTileValues.value=!1,this.rttMaterial.uniforms.time.value=r.time.value,this.rttMaterial.uniforms.tex0.value=r.tex0.value,this.rttMaterial.uniforms.tex0xy.value=r.tex0xy.value,this.rttMaterial.uniforms.tex0size.value=r.tex0size.value,this.rttMaterial.uniforms.tex1.value=r.tex1.value,this.rttMaterial.uniforms.tex1xy.value=r.tex1xy.value,this.rttMaterial.uniforms.tex1size.value=r.tex1size.value,this.rttMaterial.uniforms.rttTexture.value=(this.flipFlop?this.particleTexture1:this.particleTexture0).texture;let o=this.getRendererOrThrow();o.setRenderTarget(this.flipFlop?this.particleTexture0:this.particleTexture1),this.rttMesh.visible=!0,o.render(this.rttScene,this.rttCamera),this.arrowScaledOnChannel&&(this.rttMaterial.uniforms.rttTextureTileValues.value=(this.flipFlop?this.particleTexture1TileValues:this.particleTexture0TileValues).texture,this.rttMaterial.uniforms.outputTileValues.value=!0,o.setRenderTarget(this.flipFlop?this.particleTexture0TileValues:this.particleTexture1TileValues),this.rttMesh.visible=!0,o.render(this.rttScene,this.rttCamera)),this.flipFlop=!this.flipFlop}}}prerenderInternalGlobe(){let e=[...this.globeTilesService.currentTilesMeshes.entries()].map(([e])=>e),t=this.getMapOrThrow().getCenter(),i=Math.floor(this.getMapOrThrow().getZoom()),r=Math.ceil(Math.sqrt(e.length)),o=t.lng>=0?Math.floor(t.lng/90):-1*Math.floor(Math.abs(t.lng)/90),s=0;if(this.globeTilesService.visibleTilesAreaMesh.rotation.y=0,i<=4){let e=o*a.M8C.degToRad(180);this.globeTilesService.visibleTilesAreaMesh.rotation.y=e,s=r/2*o}let{frameA:l,frameB:n,mix:c}=this.getCurrentFrames();if(!(!l||!n))for(let t of e){let i=e.indexOf(t),a=(Math.floor(i/r)+s+r)%r,o=i%r;this.rttMaterial.uniforms.tileScale.value=1/r,this.rttMaterial.uniforms.tilePosition.value=[a,o];let u=this.getTilesPair(l,n,t);if(null===u)continue;let{tileA:h,tileB:p}=u;this.rttMaterial.uniforms.outputTileValues.value=!1,this.rttMaterial.uniforms.time.value=c,this.rttMaterial.uniforms.tex0.value=h.tile.texture,this.rttMaterial.uniforms.tex0xy.value=h.xy,this.rttMaterial.uniforms.tex0size.value=h.size,this.rttMaterial.uniforms.tex1.value=p.tile.texture,this.rttMaterial.uniforms.tex1xy.value=p.xy,this.rttMaterial.uniforms.tex1size.value=p.size,this.rttMaterial.uniforms.rttTexture.value=(this.flipFlop?this.particleTexture1:this.particleTexture0).texture;let d=this.getRendererOrThrow();d.setRenderTarget(this.flipFlop?this.particleTexture0:this.particleTexture1),this.rttMesh.visible=!0,d.render(this.rttScene,this.rttCamera),this.arrowScaledOnChannel&&(this.rttMaterial.uniforms.rttTextureTileValues.value=(this.flipFlop?this.particleTexture1TileValues:this.particleTexture0TileValues).texture,this.rttMaterial.uniforms.outputTileValues.value=!0,d.setRenderTarget(this.flipFlop?this.particleTexture0TileValues:this.particleTexture1TileValues),this.rttMesh.visible=!0,d.render(this.rttScene,this.rttCamera)),this.flipFlop=!this.flipFlop}}renderInternal(){if(!1===this.getMapOrThrow().isGlobeProjection()){let e=this.rttMaterial.uniforms.rttScale.value;this.accumulatorDrawMesh.position.x=.5*e+this.rttMaterial.uniforms.rttXY.value[0],this.accumulatorDrawMesh.position.y=-.5*e-this.rttMaterial.uniforms.rttXY.value[1],this.accumulatorDrawMesh.scale.x=this.accumulatorDrawMesh.scale.y=e}}setWorldSpaceConstant(e=!0){this.particleMaterial.uniforms.worldSpaceConstant.value=e,this.rttMaterial.uniforms.worldSpaceConstant.value=e,this.worldSpaceConstant=e}setParticleSpeed(e){this.particleSpeed=e/1e3}setParticleFastSpeed(e){this.particleFastSpeed=e*window.devicePixelRatio,this.particleMaterial.uniforms.colorSpeed.value=this.particleFastSpeed}}let R={id:"MapTiler Arrow Wind",colorramp:r.Eo.builtin.VIRIDIS.scale(0,40),smooth:!0,opacity:.8,size:18,speed:.001,maxAmount:128,density:2,refreshInterval:800,color:[255,255,255,0],fastColor:[255,255,255,255],fastSpeed:.8,rttTimestep:200,worldSpaceConstant:!1};class D extends E{constructor(e={}){super(e.id??R.id,null,null,null),l(this,"constructorOptions"),l(this,"isSourceReady",!1),l(this,"colorRamp"),this.constructorOptions=e}async onAdd(e,t){let i=e.getSdkConfig().apiKey,a=e.getMaptilerSessionId();!1===r.hE.hasData()&&await r.hE.fetchLatest(i,a);let o=r.hE.getWindData();if(null===o)throw Error("The latest weather data is not avaialble");this.colorRamp=this.constructorOptions.colorramp??R.colorramp;let s=this.constructorOptions.worldSpaceConstant??R.worldSpaceConstant;this.initArrow({minZoom:o.metadata.minzoom,maxZoom:o.metadata.maxzoom},{decodeChannels:o.metadata.weather_variable.decoding.channels.toLowerCase(),decodeMin:o.metadata.weather_variable.decoding.min,decodeMax:o.metadata.weather_variable.decoding.max,size:this.constructorOptions.size??R.size,speed:this.constructorOptions.speed?this.constructorOptions.speed:R.speed*(s?2:1),maxAmount:this.constructorOptions.maxAmount??R.maxAmount,density:this.constructorOptions.density??R.density,refreshInterval:this.constructorOptions.refreshInterval?this.constructorOptions.refreshInterval:R.refreshInterval*(s?2:1),color:this.constructorOptions.color??R.color,fastColor:this.constructorOptions.fastColor??R.fastColor,fastSpeed:this.constructorOptions.fastSpeed??R.fastSpeed,worldSpaceConstant:s,rttTimestep:this.constructorOptions.rttTimestep??R.rttTimestep,decodeAsWaves:!1},[new r.yy({decode:{channel:o.metadata.weather_variable.decoding.channels.toLowerCase(),min:o.metadata.weather_variable.decoding.min,max:o.metadata.weather_variable.decoding.max},stops:this.colorRamp,smooth:this.constructorOptions.smooth??R.smooth,opacity:this.constructorOptions.opacity??R.opacity})]),super.onAdd(e,t),r.hE.getSourcesAndTimestamps(o,i,a).forEach(e=>{this.addSource(e.timestamp,e.source)});let l=+new Date/1e3;l>=this.getAnimationStart()&&l<=this.getAnimationEnd()&&this.setAnimationTime(l),this.isSourceReady=!0,this.emit("sourceReady",{map:e,layer:this})}getIsSourceReady(){return this.isSourceReady}pickAt(e,t){let i=super.pick(e,t);if(null===i)return null;let a=i[0],o=i[1],s=Math.sqrt(a**2+o**2),l=180*Math.atan2(a,o)/Math.PI;return{speedMetersPerSecond:s,speedKilometersPerHour:3.6*s,speedMilesPerHour:2.23694*s,speedFeetPerSecond:3.28084*s,speedKnots:1.94384*s,directionAngle:l,compassDirection:(0,r.jH)(l+180)}}getColorRamp(){return this.colorRamp}onSourceReadyAsync(){return new Promise(e=>{if(this.isSourceReady)return e();this.once("sourceReady",()=>{e()})})}}r.Im.prototype.validateSource=function(){}}}]);