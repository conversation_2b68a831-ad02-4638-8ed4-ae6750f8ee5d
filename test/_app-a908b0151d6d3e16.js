try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="87d49891-1a67-49a7-99f5-947a7eea473b",e._sentryDebugIdIdentifier="sentry-dbid-87d49891-1a67-49a7-99f5-947a7eea473b")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2888],{83454:function(e,t,n){"use strict";var r,i;e.exports=(null==(r=n.g.process)?void 0:r.env)&&"object"==typeof(null==(i=n.g.process)?void 0:i.env)?n.g.process:n(77663)},6840:function(e,t,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/_app",function(){return n(55545)}])},5620:function(e,t,n){"use strict";let r,i,s,a,o,u,c,l,p,d,f,h,m;var g=n(33280),_=n(17986),y=n(49889);function v(e,t,n=[t],r="npm"){let i=e._metadata||{};i.sdk||(i.sdk={name:`sentry.javascript.${t}`,packages:n.map(e=>({name:`${r}:@sentry/${e}`,version:y.J})),version:y.J}),e._metadata=i}var b=n(39424),S=n(94223);let E=[];function x(e,t){for(let n of t)n?.afterAllSetup&&n.afterAllSetup(e)}function k(e,t,n){if(n[t.name]){S.X&&_.kg.log(`Integration skipped because it was already installed: ${t.name}`);return}if(n[t.name]=t,-1===E.indexOf(t.name)&&"function"==typeof t.setupOnce&&(t.setupOnce(),E.push(t.name)),t.setup&&"function"==typeof t.setup&&t.setup(e),"function"==typeof t.preprocessEvent){let n=t.preprocessEvent.bind(t);e.on("preprocessEvent",(t,r)=>n(t,r,e))}if("function"==typeof t.processEvent){let n=t.processEvent.bind(t),r=Object.assign((t,r)=>n(t,r,e),{id:t.name});e.addEventProcessor(r)}S.X&&_.kg.log(`Integration installed: ${t.name}`)}function T(e){let t=[];e.message&&t.push(e.message);try{let n=e.exception.values[e.exception.values.length-1];n?.value&&(t.push(n.value),n.type&&t.push(`${n.type}: ${n.value}`))}catch(e){}return t}var w=n(82305),O=n(50027);let $=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/,/^Can't find variable: gmo$/,/^undefined is not an object \(evaluating 'a\.[A-Z]'\)$/,'can\'t redefine non-configurable property "solana"',"vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)","Can't find variable: _AutofillCallbackHandler",/^Non-Error promise rejection captured with value: Object Not Found Matching Id:\d+, MethodName:simulateEvent, ParamCount:\d+$/,/^Java exception was raised during method invocation$/],A=(e={})=>{let t;return{name:"EventFilters",setup(n){t=L(e,n.getOptions())},processEvent:(n,r,i)=>(t||(t=L(e,i.getOptions())),!function(e,t){if(e.type){if("transaction"===e.type&&function(e,t){if(!t?.length)return!1;let n=e.transaction;return!!n&&(0,O.U0)(n,t)}(e,t.ignoreTransactions))return S.X&&_.kg.warn(`Event dropped due to being matched by \`ignoreTransactions\` option.
Event: ${(0,w.jH)(e)}`),!0}else{var n;if(n=t.ignoreErrors,n?.length&&T(e).some(e=>(0,O.U0)(e,n)))return S.X&&_.kg.warn(`Event dropped due to being matched by \`ignoreErrors\` option.
Event: ${(0,w.jH)(e)}`),!0;if(e.exception?.values?.length&&!e.message&&!e.exception.values.some(e=>e.stacktrace||e.type&&"Error"!==e.type||e.value))return S.X&&_.kg.warn(`Event dropped due to not having an error message, error type or stacktrace.
Event: ${(0,w.jH)(e)}`),!0;if(function(e,t){if(!t?.length)return!1;let n=R(e);return!!n&&(0,O.U0)(n,t)}(e,t.denyUrls))return S.X&&_.kg.warn(`Event dropped due to being matched by \`denyUrls\` option.
Event: ${(0,w.jH)(e)}.
Url: ${R(e)}`),!0;if(!function(e,t){if(!t?.length)return!0;let n=R(e);return!n||(0,O.U0)(n,t)}(e,t.allowUrls))return S.X&&_.kg.warn(`Event dropped due to not being matched by \`allowUrls\` option.
Event: ${(0,w.jH)(e)}.
Url: ${R(e)}`),!0}return!1}(n,t)?n:null)}},C=(e={})=>({...A(e),name:"InboundFilters"});function L(e={},t={}){return{allowUrls:[...e.allowUrls||[],...t.allowUrls||[]],denyUrls:[...e.denyUrls||[],...t.denyUrls||[]],ignoreErrors:[...e.ignoreErrors||[],...t.ignoreErrors||[],...e.disableErrorDefaults?[]:$],ignoreTransactions:[...e.ignoreTransactions||[],...t.ignoreTransactions||[]]}}function R(e){try{let t=[...e.exception?.values??[]].reverse().find(e=>e.mechanism?.parent_id===void 0&&e.stacktrace?.frames?.length),n=t?.stacktrace?.frames;return n?function(e=[]){for(let t=e.length-1;t>=0;t--){let n=e[t];if(n&&"<anonymous>"!==n.filename&&"[native code]"!==n.filename)return n.filename||null}return null}(n):null}catch(t){return S.X&&_.kg.error(`Cannot extract url for event ${(0,w.jH)(e)}`),null}}var I=n(73076),D=n(51150);let P=new WeakMap,j=()=>({name:"FunctionToString",setupOnce(){r=Function.prototype.toString;try{Function.prototype.toString=function(...e){let t=(0,D.HK)(this),n=P.has((0,I.s3)())&&void 0!==t?t:this;return r.apply(n,e)}}catch{}},setup(e){P.set(e,!0)}});var N=n(39649);let F=()=>{let e;return{name:"Dedupe",processEvent(t){if(t.type)return t;try{var n;if((n=e)&&(function(e,t){let n=e.message,r=t.message;return!!((n||r)&&(!n||r)&&(n||!r)&&n===r&&B(e,t)&&M(e,t))}(t,n)||function(e,t){let n=X(t),r=X(e);return!!(n&&r&&n.type===r.type&&n.value===r.value&&B(e,t)&&M(e,t))}(t,n)))return S.X&&_.kg.warn("Event dropped due to being a duplicate of previously captured event."),null}catch(e){}return e=t}}};function M(e,t){let n=(0,N.Fr)(e),r=(0,N.Fr)(t);if(!n&&!r)return!0;if(n&&!r||!n&&r||r.length!==n.length)return!1;for(let e=0;e<r.length;e++){let t=r[e],i=n[e];if(t.filename!==i.filename||t.lineno!==i.lineno||t.colno!==i.colno||t.function!==i.function)return!1}return!0}function B(e,t){let n=e.fingerprint,r=t.fingerprint;if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;try{return!(n.join("")!==r.join(""))}catch(e){return!1}}function X(e){return e.exception?.values&&e.exception.values[0]}var J=n(70428),H=n(23187);let U=g.GLOBAL_OBJ;function G(e){return e&&/^function\s+\w+\(\)\s+\{\s+\[native code\]\s+\}$/.test(e.toString())}var q=n(73243),W=n(72123),Z=n(81585);let z=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function Y(e,t=!1){let{host:n,path:r,pass:i,port:s,projectId:a,protocol:o,publicKey:u}=e;return`${o}://${u}${t&&i?`:${i}`:""}@${n}${s?`:${s}`:""}/${r?`${r}/`:r}${a}`}function V(e){let t=z.exec(e);if(!t){(0,_.Cf)(()=>{console.error(`Invalid Sentry Dsn: ${e}`)});return}let[n,r,i="",s="",a="",o=""]=t.slice(1),u="",c=o,l=c.split("/");if(l.length>1&&(u=l.slice(0,-1).join("/"),c=l.pop()),c){let e=c.match(/^\d+/);e&&(c=e[0])}return K({host:s,pass:i,path:u,projectId:c,port:a,protocol:n,publicKey:r})}function K(e){return{protocol:e.protocol,publicKey:e.publicKey||"",pass:e.pass||"",host:e.host,port:e.port||"",path:e.path||"",projectId:e.projectId}}var Q=n(13533),ee=n(29218);function et(e,t=[]){return[e,t]}function en(e,t){for(let n of e[1]){let e=n[0].type;if(t(n,e))return!0}return!1}function er(e){let t=(0,Q.qA)(g.GLOBAL_OBJ);return t.encodePolyfill?t.encodePolyfill(e):new TextEncoder().encode(e)}let ei={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",profile_chunk:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",raw_security:"security",log:"log_item"};function es(e){if(!e?.sdk)return;let{name:t,version:n}=e.sdk;return{name:t,version:n}}var ea=n(10042),eo=n(3613),eu=n(79769),ec=n(47515),el=n(31218),ep=n(59943),ed=n(24925),ef=n(52340);let eh="Not capturing exception because it's already been captured.",em="Discarded session because of missing or non-string release",eg=Symbol.for("SentryInternalError"),e_=Symbol.for("SentryDoNotSendEventError");function ey(e){return{message:e,[eg]:!0}}function ev(e){return{message:e,[e_]:!0}}function eb(e){return!!e&&"object"==typeof e&&eg in e}function eS(e){return!!e&&"object"==typeof e&&e_ in e}class eE{constructor(e){if(this._options=e,this._integrations={},this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],e.dsn?this._dsn=function(e){let t="string"==typeof e?V(e):K(e);if(t&&function(e){if(!H.X)return!0;let{port:t,projectId:n,protocol:r}=e;return!["protocol","publicKey","host","projectId"].find(t=>!e[t]&&(_.kg.error(`Invalid Sentry Dsn: ${t} missing`),!0))&&(n.match(/^\d+$/)?"http"===r||"https"===r?!(t&&isNaN(parseInt(t,10)))||(_.kg.error(`Invalid Sentry Dsn: Invalid port ${t}`),!1):(_.kg.error(`Invalid Sentry Dsn: Invalid protocol ${r}`),!1):(_.kg.error(`Invalid Sentry Dsn: Invalid projectId ${n}`),!1))}(t))return t}(e.dsn):S.X&&_.kg.warn("No DSN provided, client will not send events."),this._dsn){var t,n,r;let i=(t=this._dsn,n=e.tunnel,r=e._metadata?e._metadata.sdk:void 0,n||`${function(e){let t=e.protocol?`${e.protocol}:`:"",n=e.port?`:${e.port}`:"";return`${t}//${e.host}${n}${e.path?`/${e.path}`:""}/api/`}(t)}${t.projectId}/envelope/?${function(e,t){let n={sentry_version:"7"};return e.publicKey&&(n.sentry_key=e.publicKey),t&&(n.sentry_client=`${t.name}/${t.version}`),new URLSearchParams(n).toString()}(t,r)}`);this._transport=e.transport({tunnel:this._options.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...e.transportOptions,url:i})}}captureException(e,t,n){let r=(0,w.DM)();if((0,w.YO)(e))return S.X&&_.kg.log(eh),r;let i={event_id:r,...t};return this._process(this.eventFromException(e,i).then(e=>this._captureEvent(e,i,n))),i.event_id}captureMessage(e,t,n,r){let i={event_id:(0,w.DM)(),...n},s=(0,ed.Le)(e)?e:String(e),a=(0,ed.pt)(e)?this.eventFromMessage(s,t,i):this.eventFromException(e,i);return this._process(a.then(e=>this._captureEvent(e,i,r))),i.event_id}captureEvent(e,t,n){let r=(0,w.DM)();if(t?.originalException&&(0,w.YO)(t.originalException))return S.X&&_.kg.log(eh),r;let i={event_id:r,...t},s=e.sdkProcessingMetadata||{},a=s.capturedSpanScope,o=s.capturedSpanIsolationScope;return this._process(this._captureEvent(e,i,a||n,o)),i.event_id}captureSession(e){this.sendSession(e),(0,ea.CT)(e,{init:!1})}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(e){let t=this._transport;return t?(this.emit("flush"),this._isClientDoneProcessing(e).then(n=>t.flush(e).then(e=>n&&e))):(0,ef.WD)(!0)}close(e){return this.flush(e).then(e=>(this.getOptions().enabled=!1,this.emit("close"),e))}getEventProcessors(){return this._eventProcessors}addEventProcessor(e){this._eventProcessors.push(e)}init(){(this._isEnabled()||this._options.integrations.some(({name:e})=>e.startsWith("Spotlight")))&&this._setupIntegrations()}getIntegrationByName(e){return this._integrations[e]}addIntegration(e){let t=this._integrations[e.name];k(this,e,this._integrations),t||x(this,[e])}sendEvent(e,t={}){this.emit("beforeSendEvent",e,t);let n=function(e,t,n,r){var i;let s=es(n),a=e.type&&"replay_event"!==e.type?e.type:"event";(i=n?.sdk)&&(e.sdk=e.sdk||{},e.sdk.name=e.sdk.name||i.name,e.sdk.version=e.sdk.version||i.version,e.sdk.integrations=[...e.sdk.integrations||[],...i.integrations||[]],e.sdk.packages=[...e.sdk.packages||[],...i.packages||[]]);let o=function(e,t,n,r){let i=e.sdkProcessingMetadata?.dynamicSamplingContext;return{event_id:e.event_id,sent_at:new Date().toISOString(),...t&&{sdk:t},...!!n&&r&&{dsn:Y(r)},...i&&{trace:i}}}(e,s,r,t);return delete e.sdkProcessingMetadata,et(o,[[{type:a},e]])}(e,this._dsn,this._options._metadata,this._options.tunnel);for(let e of t.attachments||[])n=function(e,t){let[n,r]=e;return[n,[...r,t]]}(n,function(e){let t="string"==typeof e.data?er(e.data):e.data;return[{type:"attachment",length:t.length,filename:e.filename,content_type:e.contentType,attachment_type:e.attachmentType},t]}(e));let r=this.sendEnvelope(n);r&&r.then(t=>this.emit("afterSendEvent",e,t),null)}sendSession(e){let{release:t,environment:n=q.J}=this._options;if("aggregates"in e){let r=e.attrs||{};if(!r.release&&!t){S.X&&_.kg.warn(em);return}r.release=r.release||t,r.environment=r.environment||n,e.attrs=r}else{if(!e.release&&!t){S.X&&_.kg.warn(em);return}e.release=e.release||t,e.environment=e.environment||n}this.emit("beforeSendSession",e);let r=function(e,t,n,r){let i=es(n);return et({sent_at:new Date().toISOString(),...i&&{sdk:i},...!!r&&t&&{dsn:Y(t)}},["aggregates"in e?[{type:"sessions"},e]:[{type:"session"},e.toJSON()]])}(e,this._dsn,this._options._metadata,this._options.tunnel);this.sendEnvelope(r)}recordDroppedEvent(e,t,n=1){if(this._options.sendClientReports){let r=`${e}:${t}`;S.X&&_.kg.log(`Recording outcome: "${r}"${n>1?` (${n} times)`:""}`),this._outcomes[r]=(this._outcomes[r]||0)+n}}on(e,t){let n=this._hooks[e]=this._hooks[e]||[];return n.push(t),()=>{let e=n.indexOf(t);e>-1&&n.splice(e,1)}}emit(e,...t){let n=this._hooks[e];n&&n.forEach(e=>e(...t))}sendEnvelope(e){return(this.emit("beforeEnvelope",e),this._isEnabled()&&this._transport)?this._transport.send(e).then(null,e=>(S.X&&_.kg.error("Error while sending envelope:",e),e)):(S.X&&_.kg.error("Transport disabled"),(0,ef.WD)({}))}_setupIntegrations(){let{integrations:e}=this._options;this._integrations=function(e,t){let n={};return t.forEach(t=>{t&&k(e,t,n)}),n}(this,e),x(this,e)}_updateSessionFromEvent(e,t){let n="fatal"===t.level,r=!1,i=t.exception?.values;if(i)for(let e of(r=!0,i)){let t=e.mechanism;if(t?.handled===!1){n=!0;break}}let s="ok"===e.status;(s&&0===e.errors||s&&n)&&((0,ea.CT)(e,{...n&&{status:"crashed"},errors:e.errors||Number(r||n)}),this.captureSession(e))}_isClientDoneProcessing(e){return new ef.cW(t=>{let n=0,r=setInterval(()=>{0==this._numProcessing?(clearInterval(r),t(!0)):(n+=1,e&&n>=e&&(clearInterval(r),t(!1)))},1)})}_isEnabled(){return!1!==this.getOptions().enabled&&void 0!==this._transport}_prepareEvent(e,t,n,r){let i=this.getOptions(),s=Object.keys(this._integrations);return!t.integrations&&s?.length&&(t.integrations=s),this.emit("preprocessEvent",e,t),e.type||r.setLastEventId(e.event_id||t.event_id),(0,ec.R)(i,e,t,n,this,r).then(e=>{if(null===e)return e;this.emit("postprocessEvent",e,t),e.contexts={trace:(0,I.XX)(n),...e.contexts};let r=(0,W.CG)(this,n);return e.sdkProcessingMetadata={dynamicSamplingContext:r,...e.sdkProcessingMetadata},e})}_captureEvent(e,t={},n=(0,I.nZ)(),r=(0,I.aF)()){return S.X&&ex(e)&&_.kg.log(`Captured error event \`${T(e)[0]||"<unknown>"}\``),this._processEvent(e,t,n,r).then(e=>e.event_id,e=>{S.X&&(eS(e)?_.kg.log(e.message):eb(e)?_.kg.warn(e.message):_.kg.warn(e))})}_processEvent(e,t,n,r){let i=this.getOptions(),{sampleRate:s}=i,a=ek(e),o=ex(e),u=e.type||"error",c=`before send for type \`${u}\``,l=void 0===s?void 0:(0,eu.o)(s);if(o&&"number"==typeof l&&Math.random()>l)return this.recordDroppedEvent("sample_rate","error"),(0,ef.$2)(ev(`Discarding event because it's not included in the random sample (sampling rate = ${s})`));let p="replay_event"===u?"replay":u;return this._prepareEvent(e,t,n,r).then(e=>{if(null===e)throw this.recordDroppedEvent("event_processor",p),ev("An event processor returned `null`, will not send event.");return t.data&&!0===t.data.__sentry__?e:function(e,t){let n=`${t} must return \`null\` or a valid event.`;if((0,ed.J8)(e))return e.then(e=>{if(!(0,ed.PO)(e)&&null!==e)throw ey(n);return e},e=>{throw ey(`${t} rejected with ${e}`)});if(!(0,ed.PO)(e)&&null!==e)throw ey(n);return e}(function(e,t,n,r){let{beforeSend:i,beforeSendTransaction:s,beforeSendSpan:a}=t,o=n;if(ex(o)&&i)return i(o,r);if(ek(o)){if(a){let e=a(function(e){let{trace_id:t,parent_span_id:n,span_id:r,status:i,origin:s,data:a,op:o}=e.contexts?.trace??{};return{data:a??{},description:e.transaction,op:o,parent_span_id:n,span_id:r??"",start_timestamp:e.start_timestamp??0,status:i,timestamp:e.timestamp,trace_id:t??"",origin:s,profile_id:a?.[el.p6],exclusive_time:a?.[el.JQ],measurements:e.measurements,is_segment:!0}}(o));if(e?o=(0,eo.T)(n,{type:"transaction",timestamp:e.timestamp,start_timestamp:e.start_timestamp,transaction:e.description,contexts:{trace:{trace_id:e.trace_id,span_id:e.span_id,parent_span_id:e.parent_span_id,op:e.op,status:e.status,origin:e.origin,data:{...e.data,...e.profile_id&&{[el.p6]:e.profile_id},...e.exclusive_time&&{[el.JQ]:e.exclusive_time}}}},measurements:e.measurements}):(0,Z.R6)(),o.spans){let e=[];for(let t of o.spans){let n=a(t);n?e.push(n):((0,Z.R6)(),e.push(t))}o.spans=e}}if(s){if(o.spans){let e=o.spans.length;o.sdkProcessingMetadata={...n.sdkProcessingMetadata,spanCountBeforeProcessing:e}}return s(o,r)}}return o}(0,i,e,t),c)}).then(i=>{if(null===i){if(this.recordDroppedEvent("before_send",p),a){let t=1+(e.spans||[]).length;this.recordDroppedEvent("before_send","span",t)}throw ev(`${c} returned \`null\`, will not send event.`)}let s=n.getSession()||r.getSession();if(o&&s&&this._updateSessionFromEvent(s,i),a){let e=(i.sdkProcessingMetadata?.spanCountBeforeProcessing||0)-(i.spans?i.spans.length:0);e>0&&this.recordDroppedEvent("before_send","span",e)}let u=i.transaction_info;return a&&u&&i.transaction!==e.transaction&&(i.transaction_info={...u,source:"custom"}),this.sendEvent(i,t),i}).then(null,e=>{if(eS(e)||eb(e))throw e;throw this.captureException(e,{data:{__sentry__:!0},originalException:e}),ey(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${e}`)})}_process(e){this._numProcessing++,e.then(e=>(this._numProcessing--,e),e=>(this._numProcessing--,e))}_clearOutcomes(){let e=this._outcomes;return this._outcomes={},Object.entries(e).map(([e,t])=>{let[n,r]=e.split(":");return{reason:n,category:r,quantity:t}})}_flushOutcomes(){var e;S.X&&_.kg.log("Flushing outcomes...");let t=this._clearOutcomes();if(0===t.length){S.X&&_.kg.log("No outcomes to send");return}if(!this._dsn){S.X&&_.kg.log("No dsn provided, will not send outcomes");return}S.X&&_.kg.log("Sending outcomes:",t);let n=et((e=this._options.tunnel&&Y(this._dsn))?{dsn:e}:{},[[{type:"client_report"},{timestamp:(0,ep.yW)(),discarded_events:t}]]);this.sendEnvelope(n)}}function ex(e){return void 0===e.type}function ek(e){return"transaction"===e.type}function eT(e,t){let n=t??g.GLOBAL_OBJ._sentryClientToLogBufferMap?.get(e)??[];if(0===n.length)return;let r=e.getOptions(),i=function(e,t,n,r){let i={};return t?.sdk&&(i.sdk={name:t.sdk.name,version:t.sdk.version}),n&&r&&(i.dsn=Y(r)),et(i,[[{type:"log",item_count:e.length,content_type:"application/vnd.sentry.items.log+json"},{items:e}]])}(n,r._metadata,r.tunnel,e.getDsn());g.GLOBAL_OBJ._sentryClientToLogBufferMap?.set(e,[]),e.emit("flushLogs"),e.sendEnvelope(i)}function ew(e){e.user?.ip_address===void 0&&(e.user={...e.user,ip_address:"{{auto}}"})}function eO(e){"aggregates"in e?e.attrs?.ip_address===void 0&&(e.attrs={...e.attrs,ip_address:"{{auto}}"}):void 0===e.ipAddress&&(e.ipAddress="{{auto}}")}function e$(e,t){let n=eC(e,t),r={type:function(e){let t=e?.name;return!t&&eR(e)?e.message&&Array.isArray(e.message)&&2==e.message.length?e.message[0]:"WebAssembly.Exception":t}(t),value:function(e){let t=e?.message;return eR(e)?Array.isArray(e.message)&&2==e.message.length?e.message[1]:"wasm exception":t?t.error&&"string"==typeof t.error.message?t.error.message:t:"No error message"}(t)};return n.length&&(r.stacktrace={frames:n}),void 0===r.type&&""===r.value&&(r.value="Unrecoverable error caught"),r}function eA(e,t){return{exception:{values:[e$(e,t)]}}}function eC(e,t){let n=t.stacktrace||t.stack||"",r=t&&eL.test(t.message)?1:0,i="number"==typeof t.framesToPop?t.framesToPop:0;try{return e(n,r,i)}catch(e){}return[]}g.GLOBAL_OBJ._sentryClientToLogBufferMap=new WeakMap;let eL=/Minified React error #\d+;/i;function eR(e){return"undefined"!=typeof WebAssembly&&void 0!==WebAssembly.Exception&&e instanceof WebAssembly.Exception}function eI(e,t,n,r,i){let s;if((0,ed.VW)(t)&&t.error)return eA(e,t.error);if((0,ed.TX)(t)||(0,ed.fm)(t)){if("stack"in t)s=eA(e,t);else{let i=t.name||((0,ed.TX)(t)?"DOMError":"DOMException"),a=t.message?`${i}: ${t.message}`:i;s=eD(e,a,n,r),(0,w.Db)(s,a)}return"code"in t&&(s.tags={...s.tags,"DOMException.code":`${t.code}`}),s}return(0,ed.VZ)(t)?eA(e,t):((0,ed.PO)(t)||(0,ed.cO)(t)?s=function(e,t,n,r){let i=(0,I.s3)(),s=i?.getOptions().normalizeDepth,a=function(e){for(let t in e)if(Object.prototype.hasOwnProperty.call(e,t)){let n=e[t];if(n instanceof Error)return n}}(t),o={__serialized__:(0,ee.Qy)(t,s)};if(a)return{exception:{values:[e$(e,a)]},extra:o};let u={exception:{values:[{type:(0,ed.cO)(t)?t.constructor.name:r?"UnhandledRejection":"Error",value:function(e,{isUnhandledRejection:t}){let n=(0,D.zf)(e),r=t?"promise rejection":"exception";if((0,ed.VW)(e))return`Event \`ErrorEvent\` captured as ${r} with message \`${e.message}\``;if((0,ed.cO)(e)){let t=function(e){try{let t=Object.getPrototypeOf(e);return t?t.constructor.name:void 0}catch(e){}}(e);return`Event \`${t}\` (type=${e.type}) captured as ${r}`}return`Object captured as ${r} with keys: ${n}`}(t,{isUnhandledRejection:r})}]},extra:o};if(n){let t=eC(e,n);t.length&&(u.exception.values[0].stacktrace={frames:t})}return u}(e,t,n,i):(s=eD(e,t,n,r),(0,w.Db)(s,`${t}`,void 0)),(0,w.EG)(s,{synthetic:!0}),s)}function eD(e,t,n,r){let i={};if(r&&n){let r=eC(e,n);r.length&&(i.exception={values:[{value:t,stacktrace:{frames:r}}]}),(0,w.EG)(i,{synthetic:!0})}if((0,ed.Le)(t)){let{__sentry_template_string__:e,__sentry_template_values__:n}=t;return i.logentry={message:e,params:n},i}return i.message=t,i}let eP=g.GLOBAL_OBJ,ej=0;function eN(e,t={}){if("function"!=typeof e)return e;try{let t=e.__sentry_wrapped__;if(t){if("function"==typeof t)return t;return e}if((0,D.HK)(e))return e}catch(t){return e}let n=function(...n){try{let r=n.map(e=>eN(e,t));return e.apply(this,r)}catch(e){throw ej++,setTimeout(()=>{ej--}),(0,I.$e)(r=>{r.addEventProcessor(e=>(t.mechanism&&((0,w.Db)(e,void 0,void 0),(0,w.EG)(e,t.mechanism)),e.extra={...e.extra,arguments:n},e)),(0,b.Tb)(e)}),e}};try{for(let t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[t]=e[t])}catch{}(0,D.$Q)(n,e),(0,D.xp)(e,"__sentry_wrapped__",n);try{Object.getOwnPropertyDescriptor(n,"name").configurable&&Object.defineProperty(n,"name",{get:()=>e.name})}catch{}return n}class eF extends eE{constructor(e){let t={parentSpanIsAlwaysRootSpan:!0,...e};v(t,"browser",["browser"],eP.SENTRY_SDK_SOURCE||"npm"),super(t);let n=this,{sendDefaultPii:r,_experiments:i}=n._options,s=i?.enableLogs;t.sendClientReports&&eP.document&&eP.document.addEventListener("visibilitychange",()=>{"hidden"===eP.document.visibilityState&&(this._flushOutcomes(),s&&eT(n))}),s&&(n.on("flush",()=>{eT(n)}),n.on("afterCaptureLog",()=>{n._logFlushIdleTimeout&&clearTimeout(n._logFlushIdleTimeout),n._logFlushIdleTimeout=setTimeout(()=>{eT(n)},5e3)})),r&&(n.on("postprocessEvent",ew),n.on("beforeSendSession",eO))}eventFromException(e,t){return function(e,t,n,r){let i=eI(e,t,n?.syntheticException||void 0,r);return(0,w.EG)(i),i.level="error",n?.event_id&&(i.event_id=n.event_id),(0,ef.WD)(i)}(this._options.stackParser,e,t,this._options.attachStacktrace)}eventFromMessage(e,t="info",n){return function(e,t,n="info",r,i){let s=eD(e,t,r?.syntheticException||void 0,i);return s.level=n,r?.event_id&&(s.event_id=r.event_id),(0,ef.WD)(s)}(this._options.stackParser,e,t,n,this._options.attachStacktrace)}_prepareEvent(e,t,n,r){return e.platform=e.platform||"javascript",super._prepareEvent(e,t,n,r)}}let eM={},eB={};function eX(e,t){eM[e]=eM[e]||[],eM[e].push(t)}function eJ(e,t){if(!eB[e]){eB[e]=!0;try{t()}catch(t){H.X&&_.kg.error(`Error while instrumenting ${e}`,t)}}}function eH(e,t){let n=e&&eM[e];if(n)for(let r of n)try{r(t)}catch(t){H.X&&_.kg.error(`Error while triggering instrumentation handler.
Type: ${e}
Name: ${(0,N.$P)(r)}
Error:`,t)}}function eU(){"console"in g.GLOBAL_OBJ&&_.RU.forEach(function(e){e in g.GLOBAL_OBJ.console&&(0,D.hl)(g.GLOBAL_OBJ.console,e,function(t){return _.LD[e]=t,function(...t){eH("console",{args:t,level:e});let n=_.LD[e];n?.apply(g.GLOBAL_OBJ.console,t)}})})}function eG(e,t){let n="fetch";eX(n,e),eJ(n,()=>eq(void 0,t))}function eq(e,t=!1){(!t||function(){if("string"==typeof EdgeRuntime)return!0;if(!function(){if(!("fetch"in U))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch(e){return!1}}())return!1;if(G(U.fetch))return!0;let e=!1,t=U.document;if(t&&"function"==typeof t.createElement)try{let n=t.createElement("iframe");n.hidden=!0,t.head.appendChild(n),n.contentWindow?.fetch&&(e=G(n.contentWindow.fetch)),t.head.removeChild(n)}catch(e){H.X&&_.kg.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",e)}return e}())&&(0,D.hl)(g.GLOBAL_OBJ,"fetch",function(t){return function(...n){let r=Error(),{method:i,url:s}=function(e){if(0===e.length)return{method:"GET",url:""};if(2===e.length){let[t,n]=e;return{url:eY(t),method:ez(n,"method")?String(n.method).toUpperCase():"GET"}}let t=e[0];return{url:eY(t),method:ez(t,"method")?String(t.method).toUpperCase():"GET"}}(n),a={args:n,fetchData:{method:i,url:s},startTimestamp:1e3*(0,ep.ph)(),virtualError:r,headers:function(e){let[t,n]=e;try{if("object"==typeof n&&null!==n&&"headers"in n&&n.headers)return new Headers(n.headers);if((0,ed.gJ)(t))return new Headers(t.headers)}catch{}}(n)};return e||eH("fetch",{...a}),t.apply(g.GLOBAL_OBJ,n).then(async t=>(e?e(t):eH("fetch",{...a,endTimestamp:1e3*(0,ep.ph)(),response:t}),t),e=>{if(eH("fetch",{...a,endTimestamp:1e3*(0,ep.ph)(),error:e}),(0,ed.VZ)(e)&&void 0===e.stack&&(e.stack=r.stack,(0,D.xp)(e,"framesToPop",1)),e instanceof TypeError&&("Failed to fetch"===e.message||"Load failed"===e.message||"NetworkError when attempting to fetch resource."===e.message))try{let t=new URL(a.fetchData.url);e.message=`${e.message} (${t.host})`}catch{}throw e})}})}async function eW(e,t){if(e?.body){let n=e.body,r=n.getReader(),i=setTimeout(()=>{n.cancel().then(null,()=>{})},9e4),s=!0;for(;s;){let e;try{e=setTimeout(()=>{n.cancel().then(null,()=>{})},5e3);let{done:i}=await r.read();clearTimeout(e),i&&(t(),s=!1)}catch(e){s=!1}finally{clearTimeout(e)}}clearTimeout(i),r.releaseLock(),n.cancel().then(null,()=>{})}}function eZ(e){let t;try{t=e.clone()}catch{return}eW(t,()=>{eH("fetch-body-resolved",{endTimestamp:1e3*(0,ep.ph)(),response:e})})}function ez(e,t){return!!e&&"object"==typeof e&&!!e[t]}function eY(e){return"string"==typeof e?e:e?ez(e,"url")?e.url:e.toString?e.toString():"":""}function eV(e,t){let n=(0,I.s3)(),r=(0,I.aF)();if(!n)return;let{beforeBreadcrumb:i=null,maxBreadcrumbs:s=100}=n.getOptions();if(s<=0)return;let a={timestamp:(0,ep.yW)(),...e},o=i?(0,_.Cf)(()=>i(a,t)):a;null!==o&&(n.emit&&n.emit("beforeAddBreadcrumb",o,t),r.addBreadcrumb(o,s))}function eK(e){if(void 0!==e)return e>=400&&e<500?"warning":e>=500?"error":void 0}function eQ(e){return"isRelative"in e}function e0(e){if(!e)return{};let t=e.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!t)return{};let n=t[6]||"",r=t[8]||"";return{host:t[4],path:t[5],protocol:t[2],search:n,hash:r,relative:t[5]+n+r}}function e1(e){return e.split(/[?#]/,1)[0]}let e3=g.GLOBAL_OBJ;function e2(){if(!e3.document)return;let e=eH.bind(null,"dom"),t=e4(e,!0);e3.document.addEventListener("click",t,!1),e3.document.addEventListener("keypress",t,!1),["EventTarget","Node"].forEach(t=>{let n=e3[t]?.prototype;n?.hasOwnProperty?.("addEventListener")&&((0,D.hl)(n,"addEventListener",function(t){return function(n,r,i){if("click"===n||"keypress"==n)try{let r=this.__sentry_instrumentation_handlers__=this.__sentry_instrumentation_handlers__||{},s=r[n]=r[n]||{refCount:0};if(!s.handler){let r=e4(e);s.handler=r,t.call(this,n,r,i)}s.refCount++}catch(e){}return t.call(this,n,r,i)}}),(0,D.hl)(n,"removeEventListener",function(e){return function(t,n,r){if("click"===t||"keypress"==t)try{let n=this.__sentry_instrumentation_handlers__||{},i=n[t];i&&(i.refCount--,i.refCount<=0&&(e.call(this,t,i.handler,r),i.handler=void 0,delete n[t]),0===Object.keys(n).length&&delete this.__sentry_instrumentation_handlers__)}catch(e){}return e.call(this,t,n,r)}}))})}function e4(e,t=!1){return n=>{if(!n||n._sentryCaptured)return;let r=function(e){try{return e.target}catch(e){return null}}(n);if("keypress"===n.type&&(!r?.tagName||"INPUT"!==r.tagName&&"TEXTAREA"!==r.tagName&&!r.isContentEditable))return;(0,D.xp)(n,"_sentryCaptured",!0),r&&!r._sentryId&&(0,D.xp)(r,"_sentryId",(0,w.DM)());let o="keypress"===n.type?"input":n.type;!function(e){if(e.type!==s)return!1;try{if(!e.target||e.target._sentryId!==a)return!1}catch(e){}return!0}(n)&&(e({event:n,name:o,global:t}),s=n.type,a=r?r._sentryId:void 0),clearTimeout(i),i=e3.setTimeout(()=>{a=void 0,s=void 0},1e3)}}let e5="__sentry_xhr_v3__";function e9(e){eX("xhr",e),eJ("xhr",e8)}function e8(){if(!e3.XMLHttpRequest)return;let e=XMLHttpRequest.prototype;e.open=new Proxy(e.open,{apply(e,t,n){let r=Error(),i=1e3*(0,ep.ph)(),s=(0,ed.HD)(n[0])?n[0].toUpperCase():void 0,a=function(e){if((0,ed.HD)(e))return e;try{return e.toString()}catch{}}(n[1]);if(!s||!a)return e.apply(t,n);t[e5]={method:s,url:a,request_headers:{}},"POST"===s&&a.match(/sentry_key/)&&(t.__sentry_own_request__=!0);let o=()=>{let e=t[e5];if(e&&4===t.readyState){try{e.status_code=t.status}catch(e){}eH("xhr",{endTimestamp:1e3*(0,ep.ph)(),startTimestamp:i,xhr:t,virtualError:r})}};return"onreadystatechange"in t&&"function"==typeof t.onreadystatechange?t.onreadystatechange=new Proxy(t.onreadystatechange,{apply:(e,t,n)=>(o(),e.apply(t,n))}):t.addEventListener("readystatechange",o),t.setRequestHeader=new Proxy(t.setRequestHeader,{apply(e,t,n){let[r,i]=n,s=t[e5];return s&&(0,ed.HD)(r)&&(0,ed.HD)(i)&&(s.request_headers[r.toLowerCase()]=i),e.apply(t,n)}}),e.apply(t,n)}}),e.send=new Proxy(e.send,{apply(e,t,n){let r=t[e5];return r&&(void 0!==n[0]&&(r.body=n[0]),eH("xhr",{startTimestamp:1e3*(0,ep.ph)(),xhr:t})),e.apply(t,n)}})}function e7(e){let t="history";eX(t,e),eJ(t,e6)}function e6(){e3.addEventListener("popstate",()=>{let e=e3.location.href,t=o;o=e,t!==e&&eH("history",{from:t,to:e})}),"history"in U&&U.history&&((0,D.hl)(e3.history,"pushState",e),(0,D.hl)(e3.history,"replaceState",e));function e(e){return function(...t){let n=t.length>2?t[2]:void 0;if(n){let r=o,i=String(n);if(o=i,r===i)return e.apply(this,t);eH("history",{from:r,to:i})}return e.apply(this,t)}}}let te=(e={})=>{let t={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...e};return{name:"Breadcrumbs",setup(e){var n;t.console&&function(e){let t="console";eX(t,e),eJ(t,eU)}(function(t){var n;if((0,I.s3)()!==e)return;let r={category:"console",data:{arguments:t.args,logger:"console"},level:"warn"===(n=t.level)?"warning":["fatal","error","warning","log","info","debug"].includes(n)?n:"log",message:(0,O.nK)(t.args," ")};if("assert"===t.level){if(!1!==t.args[0])return;r.message=`Assertion failed: ${(0,O.nK)(t.args.slice(1)," ")||"console.assert"}`,r.data.arguments=t.args.slice(1)}eV(r,{input:t.args,level:t.level})}),t.dom&&(eX("dom",(n=t.dom,function(t){let r,i;if((0,I.s3)()!==e)return;let s="object"==typeof n?n.serializeAttribute:void 0,a="object"==typeof n&&"number"==typeof n.maxStringLength?n.maxStringLength:void 0;a&&a>1024&&(a=1024),"string"==typeof s&&(s=[s]);try{let e=t.event,n=e&&e.target?e.target:e;r=(0,J.Rt)(n,{keyAttrs:s,maxStringLength:a}),i=(0,J.iY)(n)}catch(e){r="<unknown>"}if(0===r.length)return;let o={category:`ui.${t.name}`,message:r};i&&(o.data={"ui.component_name":i}),eV(o,{event:t.event,name:t.name,global:t.global})})),eJ("dom",e2)),t.xhr&&e9(function(t){if((0,I.s3)()!==e)return;let{startTimestamp:n,endTimestamp:r}=t,i=t.xhr[e5];if(!n||!r||!i)return;let{method:s,url:a,status_code:o,body:u}=i,c={xhr:t.xhr,input:u,startTimestamp:n,endTimestamp:r},l={category:"xhr",data:{method:s,url:a,status_code:o},type:"http",level:eK(o)};e.emit("beforeOutgoingRequestBreadcrumb",l,c),eV(l,c)}),t.fetch&&eG(function(t){if((0,I.s3)()!==e)return;let{startTimestamp:n,endTimestamp:r}=t;if(!(!r||t.fetchData.url.match(/sentry_key/)&&"POST"===t.fetchData.method)){if(t.fetchData.method,t.fetchData.url,t.error){let i=t.fetchData,s={data:t.error,input:t.args,startTimestamp:n,endTimestamp:r},a={category:"fetch",data:i,level:"error",type:"http"};e.emit("beforeOutgoingRequestBreadcrumb",a,s),eV(a,s)}else{let i=t.response,s={...t.fetchData,status_code:i?.status};t.fetchData.request_body_size,t.fetchData.response_body_size,i?.status;let a={input:t.args,response:i,startTimestamp:n,endTimestamp:r},o={category:"fetch",data:s,type:"http",level:eK(s.status_code)};e.emit("beforeOutgoingRequestBreadcrumb",o,a),eV(o,a)}}}),t.history&&e7(function(t){if((0,I.s3)()!==e)return;let n=t.from,r=t.to,i=e0(eP.location.href),s=n?e0(n):void 0,a=e0(r);s?.path||(s=i),i.protocol===a.protocol&&i.host===a.host&&(r=a.relative),i.protocol===s.protocol&&i.host===s.host&&(n=s.relative),eV({category:"navigation",data:{from:n,to:r}})}),t.sentry&&e.on("beforeSendEvent",function(t){(0,I.s3)()===e&&eV({category:`sentry.${"transaction"===t.type?"transaction":"event"}`,event_id:t.event_id,level:t.level,message:(0,w.jH)(t)},{event:t})})}}},tt=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","BroadcastChannel","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","SharedWorker","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],tn=(e={})=>{let t={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...e};return{name:"BrowserApiErrors",setupOnce(){t.setTimeout&&(0,D.hl)(eP,"setTimeout",tr),t.setInterval&&(0,D.hl)(eP,"setInterval",tr),t.requestAnimationFrame&&(0,D.hl)(eP,"requestAnimationFrame",ti),t.XMLHttpRequest&&"XMLHttpRequest"in eP&&(0,D.hl)(XMLHttpRequest.prototype,"send",ts);let e=t.eventTarget;e&&(Array.isArray(e)?e:tt).forEach(ta)}}};function tr(e){return function(...t){let n=t[0];return t[0]=eN(n,{mechanism:{data:{function:(0,N.$P)(e)},handled:!1,type:"instrument"}}),e.apply(this,t)}}function ti(e){return function(t){return e.apply(this,[eN(t,{mechanism:{data:{function:"requestAnimationFrame",handler:(0,N.$P)(e)},handled:!1,type:"instrument"}})])}}function ts(e){return function(...t){let n=this;return["onload","onerror","onprogress","onreadystatechange"].forEach(e=>{e in n&&"function"==typeof n[e]&&(0,D.hl)(n,e,function(t){let n={mechanism:{data:{function:e,handler:(0,N.$P)(t)},handled:!1,type:"instrument"}},r=(0,D.HK)(t);return r&&(n.mechanism.data.handler=(0,N.$P)(r)),eN(t,n)})}),e.apply(this,t)}}function ta(e){let t=eP[e]?.prototype;t?.hasOwnProperty?.("addEventListener")&&((0,D.hl)(t,"addEventListener",function(t){return function(n,r,i){try{"function"==typeof r.handleEvent&&(r.handleEvent=eN(r.handleEvent,{mechanism:{data:{function:"handleEvent",handler:(0,N.$P)(r),target:e},handled:!1,type:"instrument"}}))}catch{}return t.apply(this,[n,eN(r,{mechanism:{data:{function:"addEventListener",handler:(0,N.$P)(r),target:e},handled:!1,type:"instrument"}}),i])}}),(0,D.hl)(t,"removeEventListener",function(e){return function(t,n,r){try{let i=n.__sentry_wrapped__;i&&e.call(this,t,i,r)}catch(e){}return e.call(this,t,n,r)}}))}let to=()=>({name:"BrowserSession",setupOnce(){void 0!==eP.document&&((0,b.yj)({ignoreDuration:!0}),(0,b.cg)(),e7(({from:e,to:t})=>{void 0!==e&&e!==t&&((0,b.yj)({ignoreDuration:!0}),(0,b.cg)())}))}}),tu=null;function tc(e){let t="error";eX(t,e),eJ(t,tl)}function tl(){tu=g.GLOBAL_OBJ.onerror,g.GLOBAL_OBJ.onerror=function(e,t,n,r,i){return eH("error",{column:r,error:i,line:n,msg:e,url:t}),!!tu&&tu.apply(this,arguments)},g.GLOBAL_OBJ.onerror.__SENTRY_INSTRUMENTED__=!0}let tp=null;function td(e){let t="unhandledrejection";eX(t,e),eJ(t,tf)}function tf(){tp=g.GLOBAL_OBJ.onunhandledrejection,g.GLOBAL_OBJ.onunhandledrejection=function(e){return eH("unhandledrejection",e),!tp||tp.apply(this,arguments)},g.GLOBAL_OBJ.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}let th=(e={})=>{let t={onerror:!0,onunhandledrejection:!0,...e};return{name:"GlobalHandlers",setupOnce(){Error.stackTraceLimit=50},setup(e){t.onerror&&tc(t=>{let{stackParser:n,attachStacktrace:r}=tm();if((0,I.s3)()!==e||ej>0)return;let{msg:i,url:s,line:a,column:o,error:u}=t,c=function(e,t,n,r){let i=e.exception=e.exception||{},s=i.values=i.values||[],a=s[0]=s[0]||{},o=a.stacktrace=a.stacktrace||{},u=o.frames=o.frames||[],c=(0,ed.HD)(t)&&t.length>0?t:(0,J.l4)();return 0===u.length&&u.push({colno:r,filename:c,function:N.Fi,in_app:!0,lineno:n}),e}(eI(n,u||i,void 0,r,!1),s,a,o);c.level="error",(0,b.eN)(c,{originalException:u,mechanism:{handled:!1,type:"onerror"}})}),t.onunhandledrejection&&td(t=>{let{stackParser:n,attachStacktrace:r}=tm();if((0,I.s3)()!==e||ej>0)return;let i=function(e){if((0,ed.pt)(e))return e;try{if("reason"in e)return e.reason;if("detail"in e&&"reason"in e.detail)return e.detail.reason}catch{}return e}(t),s=(0,ed.pt)(i)?{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(i)}`}]}}:eI(n,i,void 0,r,!0);s.level="error",(0,b.eN)(s,{originalException:i,mechanism:{handled:!1,type:"onunhandledrejection"}})})}}};function tm(){let e=(0,I.s3)();return e?.getOptions()||{stackParser:()=>[],attachStacktrace:!1}}let tg=()=>({name:"HttpContext",preprocessEvent(e){if(!eP.navigator&&!eP.location&&!eP.document)return;let t=e.request?.url||(0,J.l4)(),{referrer:n}=eP.document||{},{userAgent:r}=eP.navigator||{},i={...e.request?.headers,...n&&{Referer:n},...r&&{"User-Agent":r}},s={...e.request,...t&&{url:t},headers:i};e.request=s}});function t_(e,t){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,..."AggregateError"===e.type&&{is_exception_group:!0},exception_id:t}}function ty(e,t,n,r){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,type:"chained",source:t,exception_id:n,parent_id:r}}let tv=(e={})=>{let t=e.limit||5,n=e.key||"cause";return{name:"LinkedErrors",preprocessEvent(e,r,i){!function(e,t,n,r,i,s){if(!i.exception?.values||!s||!(0,ed.V9)(s.originalException,Error))return;let a=i.exception.values.length>0?i.exception.values[i.exception.values.length-1]:void 0;a&&(i.exception.values=function e(t,n,r,i,s,a,o,u){if(a.length>=r+1)return a;let c=[...a];if((0,ed.V9)(i[s],Error)){t_(o,u);let a=t(n,i[s]),l=c.length;ty(a,s,l,u),c=e(t,n,r,i[s],s,[a,...c],a,l)}return Array.isArray(i.errors)&&i.errors.forEach((i,a)=>{if((0,ed.V9)(i,Error)){t_(o,u);let l=t(n,i),p=c.length;ty(l,`errors[${a}]`,p,u),c=e(t,n,r,i,s,[l,...c],l,p)}}),c}(e,t,r,s.originalException,n,i.exception.values,a,0))}(e$,i.getOptions().stackParser,n,t,e,r)}}};function tb(e,t,n,r){let i={filename:e,function:"<anonymous>"===t?N.Fi:t,in_app:!0};return void 0!==n&&(i.lineno=n),void 0!==r&&(i.colno=r),i}let tS=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,tE=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,tx=/\((\S*)(?::(\d+))(?::(\d+))\)/,tk=[30,e=>{let t=tS.exec(e);if(t){let[,e,n,r]=t;return tb(e,N.Fi,+n,+r)}let n=tE.exec(e);if(n){if(n[2]&&0===n[2].indexOf("eval")){let e=tx.exec(n[2]);e&&(n[2]=e[1],n[3]=e[2],n[4]=e[3])}let[e,t]=tA(n[1]||N.Fi,n[2]);return tb(t,e,n[3]?+n[3]:void 0,n[4]?+n[4]:void 0)}}],tT=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,tw=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,tO=[50,e=>{let t=tT.exec(e);if(t){if(t[3]&&t[3].indexOf(" > eval")>-1){let e=tw.exec(t[3]);e&&(t[1]=t[1]||"eval",t[3]=e[1],t[4]=e[2],t[5]="")}let e=t[3],n=t[1]||N.Fi;return[n,e]=tA(n,e),tb(e,n,t[4]?+t[4]:void 0,t[5]?+t[5]:void 0)}}],t$=(0,N.pE)(tk,tO),tA=(e,t)=>{let n=-1!==e.indexOf("safari-extension"),r=-1!==e.indexOf("safari-web-extension");return n||r?[-1!==e.indexOf("@")?e.split("@")[0]:N.Fi,n?`safari-extension:${t}`:`safari-web-extension:${t}`]:[e,t]},tC=Symbol.for("SentryBufferFullError"),tL={};function tR(e,t=function(e){let t=tL[e];if(t)return t;let n=e3[e];if(G(n))return tL[e]=n.bind(e3);let r=e3.document;if(r&&"function"==typeof r.createElement)try{let t=r.createElement("iframe");t.hidden=!0,r.head.appendChild(t);let i=t.contentWindow;i?.[e]&&(n=i[e]),r.head.removeChild(t)}catch(e){}return n?tL[e]=n.bind(e3):n}("fetch")){let n=0,r=0;return function(e,t,n=function(e){let t=[];function n(e){return t.splice(t.indexOf(e),1)[0]||Promise.resolve(void 0)}return{$:t,add:function(r){if(!(void 0===e||t.length<e))return(0,ef.$2)(tC);let i=r();return -1===t.indexOf(i)&&t.push(i),i.then(()=>n(i)).then(null,()=>n(i).then(null,()=>{})),i},drain:function(e){return new ef.cW((n,r)=>{let i=t.length;if(!i)return n(!0);let s=setTimeout(()=>{e&&e>0&&n(!1)},e);t.forEach(e=>{(0,ef.WD)(e).then(()=>{--i||(clearTimeout(s),n(!0))},r)})})}}}(e.bufferSize||64)){let r={};return{send:function(i){let s=[];if(en(i,(t,n)=>{let i=ei[n];(function(e,t,n=Date.now()){return(e[t]||e.all||0)>n})(r,i)?e.recordDroppedEvent("ratelimit_backoff",i):s.push(t)}),0===s.length)return(0,ef.WD)({});let a=et(i[0],s),o=t=>{en(a,(n,r)=>{e.recordDroppedEvent(t,ei[r])})};return n.add(()=>t({body:function(e){let[t,n]=e,r=JSON.stringify(t);function i(e){"string"==typeof r?r="string"==typeof e?r+e:[er(r),e]:r.push("string"==typeof e?er(e):e)}for(let e of n){let[t,n]=e;if(i(`
${JSON.stringify(t)}
`),"string"==typeof n||n instanceof Uint8Array)i(n);else{let e;try{e=JSON.stringify(n)}catch(t){e=JSON.stringify((0,ee.Fv)(n))}i(e)}}return"string"==typeof r?r:function(e){let t=new Uint8Array(e.reduce((e,t)=>e+t.length,0)),n=0;for(let r of e)t.set(r,n),n+=r.length;return t}(r)}(a)}).then(e=>(void 0!==e.statusCode&&(e.statusCode<200||e.statusCode>=300)&&S.X&&_.kg.warn(`Sentry responded with status code ${e.statusCode} to sent event.`),r=function(e,{statusCode:t,headers:n},r=Date.now()){let i={...e},s=n?.["x-sentry-rate-limits"],a=n?.["retry-after"];if(s)for(let e of s.trim().split(",")){let[t,n,,,s]=e.split(":",5),a=parseInt(t,10),o=(isNaN(a)?60:a)*1e3;if(n)for(let e of n.split(";"))"metric_bucket"===e?(!s||s.split(";").includes("custom"))&&(i[e]=r+o):i[e]=r+o;else i.all=r+o}else a?i.all=r+function(e,t=Date.now()){let n=parseInt(`${e}`,10);if(!isNaN(n))return 1e3*n;let r=Date.parse(`${e}`);return isNaN(r)?6e4:r-t}(a,r):429===t&&(i.all=r+6e4);return i}(r,e),e),e=>{throw o("network_error"),S.X&&_.kg.error("Encountered error running transport request:",e),e})).then(e=>e,e=>{if(e===tC)return S.X&&_.kg.error("Skipped sending event because buffer is full."),o("queue_overflow"),(0,ef.WD)({});throw e})},flush:e=>n.drain(e)}}(e,function(i){let s=i.body.length;n+=s,r++;let a={body:i.body,method:"POST",referrerPolicy:"strict-origin",headers:e.headers,keepalive:n<=6e4&&r<15,...e.fetchOptions};if(!t)return tL.fetch=void 0,(0,ef.$2)("No fetch implementation available");try{return t(e.url,a).then(e=>(n-=s,r--,{statusCode:e.status,headers:{"x-sentry-rate-limits":e.headers.get("X-Sentry-Rate-Limits"),"retry-after":e.headers.get("Retry-After")}}))}catch(e){return tL.fetch=void 0,n-=s,r--,(0,ef.$2)(e)}})}function tI(e){return[C(),j(),tn(),te(),th(),tv(),F(),tg(),to()]}var tD=n(67294),tP=n(83454),tj=n(51072),tN=n(89366),tF=n(51824);class tM{constructor(e={}){this._traceId=e.traceId||(0,tF.H)(),this._spanId=e.spanId||(0,tF.M)()}spanContext(){return{spanId:this._spanId,traceId:this._traceId,traceFlags:Z.ve}}end(e){}setAttribute(e,t){return this}setAttributes(e){return this}setStatus(e){return this}updateName(e){return this}isRecording(){return!1}addEvent(e,t,n){return this}addLink(e){return this}addLinks(e){return this}recordException(e,t){}}var tB=n(69737),tX=n(60811);function tJ(e){if(!e||0===e.length)return;let t={};return e.forEach(e=>{let n=e.attributes||{},r=n[el.E1],i=n[el.Wb];"string"==typeof r&&"number"==typeof i&&(t[e.name]={value:i,unit:r})}),t}var tH=n(96515);class tU{constructor(e={}){this._traceId=e.traceId||(0,tF.H)(),this._spanId=e.spanId||(0,tF.M)(),this._startTime=e.startTimestamp||(0,ep.ph)(),this._links=e.links,this._attributes={},this.setAttributes({[el.S3]:"manual",[el.$J]:e.op,...e.attributes}),this._name=e.name,e.parentSpanId&&(this._parentSpanId=e.parentSpanId),"sampled"in e&&(this._sampled=e.sampled),e.endTimestamp&&(this._endTime=e.endTimestamp),this._events=[],this._isStandaloneSpan=e.isStandalone,this._endTime&&this._onSpanEnded()}addLink(e){return this._links?this._links.push(e):this._links=[e],this}addLinks(e){return this._links?this._links.push(...e):this._links=e,this}recordException(e,t){}spanContext(){let{_spanId:e,_traceId:t,_sampled:n}=this;return{spanId:e,traceId:t,traceFlags:n?Z.i0:Z.ve}}setAttribute(e,t){return void 0===t?delete this._attributes[e]:this._attributes[e]=t,this}setAttributes(e){return Object.keys(e).forEach(t=>this.setAttribute(t,e[t])),this}updateStartTime(e){this._startTime=(0,Z.$k)(e)}setStatus(e){return this._status=e,this}updateName(e){return this._name=e,this.setAttribute(el.Zj,"custom"),this}end(e){this._endTime||(this._endTime=(0,Z.$k)(e),function(e){if(!S.X)return;let{description:t="< unknown name >",op:n="< unknown op >"}=(0,Z.XU)(e),{spanId:r}=e.spanContext(),i=(0,Z.Gx)(e)===e,s=`[Tracing] Finishing "${n}" ${i?"root ":""}span "${t}" with ID ${r}`;_.kg.log(s)}(this),this._onSpanEnded())}getSpanJSON(){return{data:this._attributes,description:this._name,op:this._attributes[el.$J],parent_span_id:this._parentSpanId,span_id:this._spanId,start_timestamp:this._startTime,status:(0,Z._4)(this._status),timestamp:this._endTime,trace_id:this._traceId,origin:this._attributes[el.S3],profile_id:this._attributes[el.p6],exclusive_time:this._attributes[el.JQ],measurements:tJ(this._events),is_segment:this._isStandaloneSpan&&(0,Z.Gx)(this)===this||void 0,segment_id:this._isStandaloneSpan?(0,Z.Gx)(this).spanContext().spanId:void 0,links:(0,Z.FF)(this._links)}}isRecording(){return!this._endTime&&!!this._sampled}addEvent(e,t,n){S.X&&_.kg.log("[Tracing] Adding an event to span:",e);let r=tG(t)?t:n||(0,ep.ph)(),i=tG(t)?{}:t||{},s={name:e,time:(0,Z.$k)(r),attributes:i};return this._events.push(s),this}isStandaloneSpan(){return!!this._isStandaloneSpan}_onSpanEnded(){let e=(0,I.s3)();if(e&&e.emit("spanEnd",this),!(this._isStandaloneSpan||this===(0,Z.Gx)(this)))return;if(this._isStandaloneSpan){this._sampled?function(e){let t=(0,I.s3)();if(!t)return;let n=e[1];if(!n||0===n.length){t.recordDroppedEvent("before_send","span");return}t.sendEnvelope(e)}(function(e,t){let n=(0,W.jC)(e[0]),r=t?.getDsn(),i=t?.getOptions().tunnel,s={sent_at:new Date().toISOString(),...!!n.trace_id&&!!n.public_key&&{trace:n},...!!i&&r&&{dsn:Y(r)}},a=t?.getOptions().beforeSendSpan,o=a?e=>{let t=(0,Z.XU)(e);return a(t)||((0,Z.R6)(),t)}:Z.XU,u=[];for(let t of e){let e=o(t);e&&u.push([{type:"span"},e])}return et(s,u)}([this],e)):(S.X&&_.kg.log("[Tracing] Discarding standalone span because its trace was not chosen to be sampled."),e&&e.recordDroppedEvent("sample_rate","span"));return}let t=this._convertSpanToTransaction();t&&((0,tH.I)(this).scope||(0,I.nZ)()).captureEvent(t)}_convertSpanToTransaction(){if(!tq((0,Z.XU)(this)))return;this._name||(S.X&&_.kg.warn("Transaction has no name, falling back to `<unlabeled transaction>`."),this._name="<unlabeled transaction>");let{scope:e,isolationScope:t}=(0,tH.I)(this);if(!0!==this._sampled)return;let n=(0,Z.Dp)(this).filter(e=>e!==this&&!(e instanceof tU&&e.isStandaloneSpan())).map(e=>(0,Z.XU)(e)).filter(tq),r=this._attributes[el.Zj];delete this._attributes[el.xF],n.forEach(e=>{delete e.data[el.xF]});let i={contexts:{trace:(0,Z.HR)(this)},spans:n.length>1e3?n.sort((e,t)=>e.start_timestamp-t.start_timestamp).slice(0,1e3):n,start_timestamp:this._startTime,timestamp:this._endTime,transaction:this._name,type:"transaction",sdkProcessingMetadata:{capturedSpanScope:e,capturedSpanIsolationScope:t,dynamicSamplingContext:(0,W.jC)(this)},...r&&{transaction_info:{source:r}}},s=tJ(this._events);return s&&Object.keys(s).length&&(S.X&&_.kg.log("[Measurements] Adding measurements to transaction event",JSON.stringify(s,void 0,2)),i.measurements=s),i}}function tG(e){return e&&"number"==typeof e||e instanceof Date||Array.isArray(e)}function tq(e){return!!e.start_timestamp&&!!e.timestamp&&!!e.span_id&&!!e.trace_id}let tW="__SENTRY_SUPPRESS_TRACING__";function tZ(e){let t=tY();if(t.startInactiveSpan)return t.startInactiveSpan(e);let n=function(e){let t={isStandalone:(e.experimental||{}).standalone,...e};if(e.startTime){let n={...t};return n.startTimestamp=(0,Z.$k)(e.startTime),delete n.startTime,n}return t}(e),{forceTransaction:r,parentSpan:i}=e;return(e.scope?t=>(0,I.$e)(e.scope,t):void 0!==i?e=>tz(i,e):e=>e())(()=>{let t=(0,I.nZ)(),i=function(e){let t=(0,tN.Y)(e);if(!t)return;let n=(0,I.s3)();return(n?n.getOptions():{}).parentSpanIsAlwaysRootSpan?(0,Z.Gx)(t):t}(t);return e.onlyIfParent&&!i?new tM:function({parentSpan:e,spanArguments:t,forceTransaction:n,scope:r}){let i;if(!(0,tj.f)()){let r=new tM;if(n||!e){let e={sampled:"false",sample_rate:"0",transaction:t.name,...(0,W.jC)(r)};(0,W.Lh)(r,e)}return r}let s=(0,I.aF)();if(e&&!n)i=function(e,t,n){let{spanId:r,traceId:i}=e.spanContext(),s=!t.getScopeData().sdkProcessingMetadata[tW]&&(0,Z.Tt)(e),a=s?new tU({...n,parentSpanId:r,traceId:i,sampled:s}):new tM({traceId:i});(0,Z.j5)(e,a);let o=(0,I.s3)();return o&&(o.emit("spanStart",a),n.endTimestamp&&o.emit("spanEnd",a)),a}(e,r,t),(0,Z.j5)(e,i);else if(e){let n=(0,W.jC)(e),{traceId:s,spanId:a}=e.spanContext(),o=(0,Z.Tt)(e);i=tV({traceId:s,parentSpanId:a,...t},r,o),(0,W.Lh)(i,n)}else{let{traceId:e,dsc:n,parentSpanId:a,sampled:o}={...s.getPropagationContext(),...r.getPropagationContext()};i=tV({traceId:e,parentSpanId:a,...t},r,o),n&&(0,W.Lh)(i,n)}return!function(e){if(!S.X)return;let{description:t="< unknown name >",op:n="< unknown op >",parent_span_id:r}=(0,Z.XU)(e),{spanId:i}=e.spanContext(),s=(0,Z.Tt)(e),a=(0,Z.Gx)(e),o=a===e,u=`[Tracing] Starting ${s?"sampled":"unsampled"} ${o?"root ":""}span`,c=[`op: ${n}`,`name: ${t}`,`ID: ${i}`];if(r&&c.push(`parent ID: ${r}`),!o){let{op:e,description:t}=(0,Z.XU)(a);c.push(`root ID: ${a.spanContext().spanId}`),e&&c.push(`root op: ${e}`),t&&c.push(`root description: ${t}`)}_.kg.log(`${u}
  ${c.join("\n  ")}`)}(i),(0,tH.Y)(i,r,s),i}({parentSpan:i,spanArguments:n,forceTransaction:r,scope:t})})}function tz(e,t){let n=tY();return n.withActiveSpan?n.withActiveSpan(e,t):(0,I.$e)(n=>((0,tN.D)(n,e||void 0),t(n)))}function tY(){let e=(0,Q.cu)();return(0,tX.G)(e)}function tV(e,t,n){let r=(0,I.s3)(),i=r?.getOptions()||{},{name:s=""}=e,a={spanAttributes:{...e.attributes},spanName:s,parentSampled:n};r?.emit("beforeSampling",a,{decision:!1});let o=a.parentSampled??n,u=a.spanAttributes,c=t.getPropagationContext(),[l,p,d]=t.getScopeData().sdkProcessingMetadata[tW]?[!1]:function(e,t,n){let r,i;if(!(0,tj.f)(e))return[!1];"function"==typeof e.tracesSampler?(r=e.tracesSampler({...t,inheritOrSampleWith:e=>"number"==typeof t.parentSampleRate?t.parentSampleRate:"boolean"==typeof t.parentSampled?Number(t.parentSampled):e}),i=!0):void 0!==t.parentSampled?r=t.parentSampled:void 0!==e.tracesSampleRate&&(r=e.tracesSampleRate,i=!0);let s=(0,eu.o)(r);if(void 0===s)return S.X&&_.kg.warn(`[Tracing] Discarding root span because of invalid sample rate. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(r)} of type ${JSON.stringify(typeof r)}.`),[!1];if(!s)return S.X&&_.kg.log(`[Tracing] Discarding transaction because ${"function"==typeof e.tracesSampler?"tracesSampler returned 0 or false":"a negative sampling decision was inherited or tracesSampleRate is set to 0"}`),[!1,s,i];let a=n<s;return!a&&S.X&&_.kg.log(`[Tracing] Discarding transaction because it's not included in the random sample (sampling rate = ${Number(r)})`),[a,s,i]}(i,{name:s,parentSampled:o,attributes:u,parentSampleRate:(0,eu.o)(c.dsc?.sample_rate)},c.sampleRand),f=new tU({...e,attributes:{[el.Zj]:"custom",[el.TE]:void 0!==p&&d?p:void 0,...u},sampled:l});return!l&&r&&(S.X&&_.kg.log("[Tracing] Discarding root span because its trace was not chosen to be sampled."),r.recordDroppedEvent("sample_rate","transaction")),r&&r.emit("spanStart",f),f}let tK={idleTimeout:1e3,finalTimeout:3e4,childSpanTimeout:15e3};function tQ(e,t={}){let n;let r=new Map,i=!1,s="externalFinish",a=!t.disableAutoFinish,o=[],{idleTimeout:u=tK.idleTimeout,finalTimeout:c=tK.finalTimeout,childSpanTimeout:l=tK.childSpanTimeout,beforeSpanEnd:p}=t,d=(0,I.s3)();if(!d||!(0,tj.f)()){let e=new tM,t={sample_rate:"0",sampled:"false",...(0,W.jC)(e)};return(0,W.Lh)(e,t),e}let f=(0,I.nZ)(),h=(0,Z.HN)(),m=function(e){let t=tZ(e);return(0,tN.D)((0,I.nZ)(),t),S.X&&_.kg.log("[Tracing] Started span is an idle span"),t}(e);function g(){n&&(clearTimeout(n),n=void 0)}function y(e){g(),n=setTimeout(()=>{!i&&0===r.size&&a&&(s="idleTimeout",m.end(e))},u)}function v(e){n=setTimeout(()=>{!i&&a&&(s="heartbeatFailed",m.end(e))},l)}function b(e){i=!0,r.clear(),o.forEach(e=>e()),(0,tN.D)(f,h);let t=(0,Z.XU)(m),{start_timestamp:n}=t;if(!n)return;t.data[el.ju]||m.setAttribute(el.ju,s),_.kg.log(`[Tracing] Idle span "${t.op}" finished`);let a=(0,Z.Dp)(m).filter(e=>e!==m),l=0;a.forEach(t=>{t.isRecording()&&(t.setStatus({code:tB.jt,message:"cancelled"}),t.end(e),S.X&&_.kg.log("[Tracing] Cancelling span since span ended early",JSON.stringify(t,void 0,2)));let{timestamp:n=0,start_timestamp:r=0}=(0,Z.XU)(t),i=r<=e,s=n-r<=(c+u)/1e3;if(S.X){let e=JSON.stringify(t,void 0,2);i?s||_.kg.log("[Tracing] Discarding span since it finished after idle span final timeout",e):_.kg.log("[Tracing] Discarding span since it happened after idle span was finished",e)}(!s||!i)&&((0,Z.ed)(m,t),l++)}),l>0&&m.setAttribute("sentry.idle_span_discarded_spans",l)}return m.end=new Proxy(m.end,{apply(e,t,n){if(p&&p(m),t instanceof tM)return;let[r,...i]=n,s=r||(0,ep.ph)(),a=(0,Z.$k)(s),o=(0,Z.Dp)(m).filter(e=>e!==m);if(!o.length)return b(a),Reflect.apply(e,t,[a,...i]);let u=o.map(e=>(0,Z.XU)(e).timestamp).filter(e=>!!e),l=u.length?Math.max(...u):void 0,d=(0,Z.XU)(m).start_timestamp,f=Math.min(d?d+c/1e3:1/0,Math.max(d||-1/0,Math.min(a,l||1/0)));return b(f),Reflect.apply(e,t,[f,...i])}}),o.push(d.on("spanStart",e=>{if(!i&&e!==m&&!(0,Z.XU)(e).timestamp&&(0,Z.Dp)(m).includes(e)){var t;t=e.spanContext().spanId,g(),r.set(t,!0),v((0,ep.ph)()+l/1e3)}})),o.push(d.on("spanEnd",e=>{var t;i||(t=e.spanContext().spanId,r.has(t)&&r.delete(t),0===r.size&&y((0,ep.ph)()+u/1e3))})),o.push(d.on("idleSpanEnableAutoFinish",e=>{e===m&&(a=!0,y(),r.size&&v())})),t.disableAutoFinish||y(),setTimeout(()=>{i||(m.setStatus({code:tB.jt,message:"deadline_exceeded"}),s="finalTimeout",m.end())},c),m}let t0=!1;function t1(){let e=(0,Z.HN)(),t=e&&(0,Z.Gx)(e);if(t){let e="internal_error";S.X&&_.kg.log(`[Tracing] Root span: ${e} -> Global error occurred`),t.setStatus({code:tB.jt,message:e})}}t1.tag="sentry_tracingErrorCallback";var t3=n(94801);let t2=(e,t)=>e>t[1]?"poor":e>t[0]?"needs-improvement":"good",t4=(e,t,n,r)=>{let i,s;return a=>{t.value>=0&&(a||r)&&((s=t.value-(i||0))||void 0===i)&&(i=t.value,t.delta=s,t.rating=t2(t.value,n),e(t))}},t5=()=>`v4-${Date.now()}-${Math.floor(Math.random()*(9e12-1))+1e12}`,t9=(e=!0)=>{let t=e3.performance?.getEntriesByType?.("navigation")[0];if(!e||t&&t.responseStart>0&&t.responseStart<performance.now())return t},t8=()=>{let e=t9();return e?.activationStart||0},t7=(e,t)=>{let n=t9(),r="navigate";return n&&(e3.document?.prerendering||t8()>0?r="prerender":e3.document?.wasDiscarded?r="restore":n.type&&(r=n.type.replace(/_/g,"-"))),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:t5(),navigationType:r}},t6=(e,t,n)=>{try{if(PerformanceObserver.supportedEntryTypes.includes(e)){let r=new PerformanceObserver(e=>{Promise.resolve().then(()=>{t(e.getEntries())})});return r.observe(Object.assign({type:e,buffered:!0},n||{})),r}}catch(e){}},ne=e=>{let t=t=>{("pagehide"===t.type||e3.document?.visibilityState==="hidden")&&e(t)};e3.document&&(addEventListener("visibilitychange",t,!0),addEventListener("pagehide",t,!0))},nt=e=>{let t=!1;return()=>{t||(e(),t=!0)}},nn=-1,nr=()=>"hidden"!==e3.document.visibilityState||e3.document.prerendering?1/0:0,ni=e=>{"hidden"===e3.document.visibilityState&&nn>-1&&(nn="visibilitychange"===e.type?e.timeStamp:0,na())},ns=()=>{addEventListener("visibilitychange",ni,!0),addEventListener("prerenderingchange",ni,!0)},na=()=>{removeEventListener("visibilitychange",ni,!0),removeEventListener("prerenderingchange",ni,!0)},no=()=>(e3.document&&nn<0&&(nn=nr(),ns()),{get firstHiddenTime(){return nn}}),nu=e=>{e3.document?.prerendering?addEventListener("prerenderingchange",()=>e(),!0):e()},nc=[1800,3e3],nl=(e,t={})=>{nu(()=>{let n;let r=no(),i=t7("FCP"),s=t6("paint",e=>{e.forEach(e=>{"first-contentful-paint"===e.name&&(s.disconnect(),e.startTime<r.firstHiddenTime&&(i.value=Math.max(e.startTime-t8(),0),i.entries.push(e),n(!0)))})});s&&(n=t4(e,i,nc,t.reportAllChanges))})},np=[.1,.25],nd=(e,t={})=>{nl(nt(()=>{let n;let r=t7("CLS",0),i=0,s=[],a=e=>{e.forEach(e=>{if(!e.hadRecentInput){let t=s[0],n=s[s.length-1];i&&t&&n&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(i+=e.value,s.push(e)):(i=e.value,s=[e])}}),i>r.value&&(r.value=i,r.entries=s,n())},o=t6("layout-shift",a);o&&(n=t4(e,r,np,t.reportAllChanges),ne(()=>{a(o.takeRecords()),n(!0)}),setTimeout(n,0))}))},nf=[100,300],nh=(e,t={})=>{nu(()=>{let n;let r=no(),i=t7("FID"),s=e=>{e.startTime<r.firstHiddenTime&&(i.value=e.processingStart-e.startTime,i.entries.push(e),n(!0))},a=e=>{e.forEach(s)},o=t6("first-input",a);n=t4(e,i,nf,t.reportAllChanges),o&&ne(nt(()=>{a(o.takeRecords()),o.disconnect()}))})},nm=0,ng=1/0,n_=0,ny=e=>{e.forEach(e=>{e.interactionId&&(ng=Math.min(ng,e.interactionId),nm=(n_=Math.max(n_,e.interactionId))?(n_-ng)/7+1:0)})},nv=()=>u?nm:performance.interactionCount||0,nb=()=>{"interactionCount"in performance||u||(u=t6("event",ny,{type:"event",buffered:!0,durationThreshold:0}))},nS=[],nE=new Map,nx=()=>nv()-0,nk=()=>{let e=Math.min(nS.length-1,Math.floor(nx()/50));return nS[e]},nT=[],nw=e=>{if(nT.forEach(t=>t(e)),!(e.interactionId||"first-input"===e.entryType))return;let t=nS[nS.length-1],n=nE.get(e.interactionId);if(n||nS.length<10||t&&e.duration>t.latency){if(n)e.duration>n.latency?(n.entries=[e],n.latency=e.duration):e.duration===n.latency&&e.startTime===n.entries[0]?.startTime&&n.entries.push(e);else{let t={id:e.interactionId,latency:e.duration,entries:[e]};nE.set(t.id,t),nS.push(t)}nS.sort((e,t)=>t.latency-e.latency),nS.length>10&&nS.splice(10).forEach(e=>nE.delete(e.id))}},nO=e=>{let t=e3.requestIdleCallback||e3.setTimeout,n=-1;return e=nt(e),e3.document?.visibilityState==="hidden"?e():(n=t(e),ne(e)),n},n$=[200,500],nA=(e,t={})=>{"PerformanceEventTiming"in e3&&"interactionId"in PerformanceEventTiming.prototype&&nu(()=>{let n;nb();let r=t7("INP"),i=e=>{nO(()=>{e.forEach(nw);let t=nk();t&&t.latency!==r.value&&(r.value=t.latency,r.entries=t.entries,n())})},s=t6("event",i,{durationThreshold:null!=t.durationThreshold?t.durationThreshold:40});n=t4(e,r,n$,t.reportAllChanges),s&&(s.observe({type:"first-input",buffered:!0}),ne(()=>{i(s.takeRecords()),n(!0)}))})},nC=[2500,4e3],nL={},nR=(e,t={})=>{nu(()=>{let n;let r=no(),i=t7("LCP"),s=e=>{t.reportAllChanges||(e=e.slice(-1)),e.forEach(e=>{e.startTime<r.firstHiddenTime&&(i.value=Math.max(e.startTime-t8(),0),i.entries=[e],n())})},a=t6("largest-contentful-paint",s);if(a){n=t4(e,i,nC,t.reportAllChanges);let r=nt(()=>{nL[i.id]||(s(a.takeRecords()),a.disconnect(),nL[i.id]=!0,n(!0))});["keydown","click"].forEach(e=>{e3.document&&addEventListener(e,()=>nO(r),{once:!0,capture:!0})}),ne(r)}})},nI=[800,1800],nD=e=>{e3.document?.prerendering?nu(()=>nD(e)):e3.document?.readyState!=="complete"?addEventListener("load",()=>nD(e),!0):setTimeout(e,0)},nP=(e,t={})=>{let n=t7("TTFB"),r=t4(e,n,nI,t.reportAllChanges);nD(()=>{let e=t9();e&&(n.value=Math.max(e.responseStart-t8(),0),n.entries=[e],r(!0))})},nj={},nN={};function nF(e,t=!1){return nq("cls",e,nX,c,t)}function nM(e,t){return nW(e,t),nN[e]||(function(e){let t={};"event"===e&&(t.durationThreshold=0),t6(e,t=>{nB(e,{entries:t})},t)}(e),nN[e]=!0),nZ(e,t)}function nB(e,t){let n=nj[e];if(n?.length)for(let e of n)try{e(t)}catch(e){}}function nX(){return nd(e=>{nB("cls",{metric:e}),c=e},{reportAllChanges:!0})}function nJ(){return nh(e=>{nB("fid",{metric:e}),l=e})}function nH(){return nR(e=>{nB("lcp",{metric:e}),p=e},{reportAllChanges:!0})}function nU(){return nP(e=>{nB("ttfb",{metric:e}),d=e})}function nG(){return nA(e=>{nB("inp",{metric:e}),f=e})}function nq(e,t,n,r,i=!1){let s;return nW(e,t),nN[e]||(s=n(),nN[e]=!0),r&&t({metric:r}),nZ(e,t,i?s:void 0)}function nW(e,t){nj[e]=nj[e]||[],nj[e].push(t)}function nZ(e,t,n){return()=>{n&&n();let r=nj[e];if(!r)return;let i=r.indexOf(t);-1!==i&&r.splice(i,1)}}function nz(e){return"number"==typeof e&&isFinite(e)}function nY(e,t,n,{...r}){let i=(0,Z.XU)(e).start_timestamp;return i&&i>t&&"function"==typeof e.updateStartTime&&e.updateStartTime(t),tz(e,()=>{let e=tZ({startTime:t,...r});return e&&e.end(n),e})}function nV(e){let t;let n=(0,I.s3)();if(!n)return;let{name:r,transaction:i,attributes:s,startTime:a}=e,{release:o,environment:u,sendDefaultPii:c}=n.getOptions(),l=n.getIntegrationByName("Replay"),p=l?.getReplayId(),d=(0,I.nZ)(),f=d.getUser(),h=void 0!==f?f.email||f.id||f.ip_address:void 0;try{t=d.getScopeData().contexts.profile.profile_id}catch{}return tZ({name:r,attributes:{release:o,environment:u,user:h||void 0,profile_id:t||void 0,replay_id:p||void 0,transaction:i,"user_agent.original":e3.navigator?.userAgent,"client.address":c?"{{auto}}":void 0,...s},startTime:a,experimental:{standalone:!0}})}function nK(){return e3.addEventListener&&e3.performance}function nQ(e){return e/1e3}function n0(e){let t="unknown",n="unknown",r="";for(let i of e){if("/"===i){[t,n]=e.split("/");break}if(!isNaN(Number(i))){t="h"===r?"http":r,n=e.split(r)[1];break}r+=i}return r===e&&(t=r),{name:t,version:n}}let n1=0,n3={};function n2(e,t,n,r,i=n){let s=t["secureConnection"===n?"connectEnd":"fetch"===n?"domainLookupStart":`${n}End`],a=t[`${n}Start`];a&&s&&nY(e,r+nQ(a),r+nQ(s),{op:`browser.${i}`,name:t.name,attributes:{[el.S3]:"auto.ui.browser.metrics",..."redirect"===n&&null!=t.redirectCount?{"http.redirect_count":t.redirectCount}:{}}})}function n4(e,t,n,r){let i=t[n];null!=i&&i<2147483647&&(e[r]=i)}let n5=[],n9=new Map,n8={click:"click",pointerdown:"click",pointerup:"click",mousedown:"click",mouseup:"click",touchstart:"click",touchend:"click",mouseover:"hover",mouseout:"hover",mouseenter:"hover",mouseleave:"hover",pointerover:"hover",pointerout:"hover",pointerenter:"hover",pointerleave:"hover",dragstart:"drag",dragend:"drag",drag:"drag",dragenter:"drag",dragleave:"drag",dragover:"drag",drop:"drag",keydown:"press",keyup:"press",keypress:"press",input:"press"},n7="sentry_previous_trace";function n6(e){return 1===e.traceFlags}var re=n(98823);function rt(e={}){let t=(0,I.s3)();if(!(0,b._k)()||!t)return{};let n=(0,Q.cu)(),r=(0,tX.G)(n);if(r.getTraceData)return r.getTraceData(e);let i=(0,I.nZ)(),s=e.span||(0,Z.HN)(),a=s?(0,Z.Hb)(s):function(e){let{traceId:t,sampled:n,propagationSpanId:r}=e.getPropagationContext();return(0,t3.$p)(t,r,n)}(i),o=s?(0,W.jC)(s):(0,W.CG)(t,i),u=(0,re.IQ)(o);return t3.Ke.test(a)?{"sentry-trace":a,baggage:u}:(_.kg.warn("Invalid sentry-trace data. Cannot generate trace data"),{})}function rn(e){return e.split(",").some(e=>e.trim().startsWith(re.lq))}let rr=new WeakMap,ri=new Map,rs={traceFetch:!0,traceXHR:!0,enableHTTPTimings:!0,trackFetchStreamPerformance:!1};function ra(e){let{url:t}=(0,Z.XU)(e).data;if(!t||"string"!=typeof t)return;let n=nM("resource",({entries:r})=>{r.forEach(r=>{"resource"===r.entryType&&"initiatorType"in r&&"string"==typeof r.nextHopProtocol&&("fetch"===r.initiatorType||"xmlhttprequest"===r.initiatorType)&&r.name.endsWith(t)&&((function(e){let{name:t,version:n}=n0(e.nextHopProtocol),r=[];return(r.push(["network.protocol.version",n],["network.protocol.name",t]),(0,ep.Z1)())?[...r,["http.request.redirect_start",ro(e.redirectStart)],["http.request.fetch_start",ro(e.fetchStart)],["http.request.domain_lookup_start",ro(e.domainLookupStart)],["http.request.domain_lookup_end",ro(e.domainLookupEnd)],["http.request.connect_start",ro(e.connectStart)],["http.request.secure_connection_start",ro(e.secureConnectionStart)],["http.request.connection_end",ro(e.connectEnd)],["http.request.request_start",ro(e.requestStart)],["http.request.response_start",ro(e.responseStart)],["http.request.response_end",ro(e.responseEnd)]]:r})(r).forEach(t=>e.setAttribute(...t)),setTimeout(n))})})}function ro(e=0){return(((0,ep.Z1)()||performance.timeOrigin)+e)/1e3}function ru(e){try{return new URL(e,eP.location.origin).href}catch{return}}let rc={...tK,instrumentNavigation:!0,instrumentPageLoad:!0,markBackgroundSpan:!0,enableLongTask:!0,enableLongAnimationFrame:!0,enableInp:!0,linkPreviousTrace:"in-memory",consistentTraceSampling:!1,_experiments:{},...rs},rl=!1,rp=(e={})=>{rl&&(0,_.Cf)(()=>{console.warn("Multiple browserTracingIntegration instances are not supported.")}),rl=!0;let t=eP.document;t0||(t0=!0,tc(t1),td(t1));let{enableInp:n,enableLongTask:r,enableLongAnimationFrame:i,_experiments:{enableInteractions:s,enableStandaloneClsSpans:a},beforeStartSpan:o,idleTimeout:u,finalTimeout:c,childSpanTimeout:y,markBackgroundSpan:v,traceFetch:b,traceXHR:E,trackFetchStreamPerformance:x,shouldCreateSpanForRequest:k,enableHTTPTimings:T,instrumentPageLoad:w,instrumentNavigation:$,linkPreviousTrace:A,consistentTraceSampling:C,onRequestSpanStart:L}={...rc,...e},R=function({recordClsStandaloneSpans:e}){let t=nK();if(t&&(0,ep.Z1)()){t.mark&&e3.performance.mark("sentry-tracing-init");let n=nq("fid",({metric:e})=>{let t=e.entries[e.entries.length-1];if(!t)return;let n=nQ((0,ep.Z1)()),r=nQ(t.startTime);n3.fid={value:e.value,unit:"millisecond"},n3["mark.fid"]={value:n+r,unit:"second"}},nJ,l),r=function(e,t=!1){return nq("lcp",e,nH,p,t)}(({metric:e})=>{let t=e.entries[e.entries.length-1];t&&(n3.lcp={value:e.value,unit:"millisecond"},h=t)},!0),i=nq("ttfb",({metric:e})=>{e.entries[e.entries.length-1]&&(n3.ttfb={value:e.value,unit:"millisecond"})},nU,d),s=e?function(){let e,t,n=0;if(!function(){try{return PerformanceObserver.supportedEntryTypes.includes("layout-shift")}catch{return!1}}())return;let r=!1;function i(){r||(r=!0,t&&function(e,t,n){let r=nQ(((0,ep.Z1)()||0)+(t?.startTime||0)),i=(0,I.nZ)().getScopeData().transactionName,s=nV({name:t?(0,J.Rt)(t.sources[0]?.node):"Layout shift",transaction:i,attributes:{[el.S3]:"auto.http.browser.cls",[el.$J]:"ui.webvital.cls",[el.JQ]:t?.duration||0,"sentry.pageload.span_id":n},startTime:r});s&&(s.addEvent("cls",{[el.E1]:"",[el.Wb]:e}),s.end(r))}(n,e,t),s())}let s=nF(({metric:t})=>{let r=t.entries[t.entries.length-1];r&&(n=t.value,e=r)},!0);ne(()=>{i()}),setTimeout(()=>{let e=(0,I.s3)();if(!e)return;let n=e.on("startNavigationSpan",()=>{i(),n?.()}),r=(0,Z.HN)();if(r){let e=(0,Z.Gx)(r);"pageload"===(0,Z.XU)(e).op&&(t=e.spanContext().spanId)}},0)}():nF(({metric:e})=>{let t=e.entries[e.entries.length-1];t&&(n3.cls={value:e.value,unit:""},m=t)},!0);return()=>{n(),r(),i(),s?.()}}return()=>void 0}({recordClsStandaloneSpans:a||!1});n&&function(){if(nK()&&(0,ep.Z1)()){let e=nq("inp",({metric:e})=>{if(void 0==e.value)return;let t=e.entries.find(t=>t.duration===e.value&&n8[t.name]);if(!t)return;let{interactionId:n}=t,r=n8[t.name],i=nQ((0,ep.Z1)()+t.startTime),s=nQ(e.value),a=(0,Z.HN)(),o=a?(0,Z.Gx)(a):void 0,u=(null!=n?n9.get(n):void 0)||o,c=u?(0,Z.XU)(u).description:(0,I.nZ)().getScopeData().transactionName,l=nV({name:(0,J.Rt)(t.target),transaction:c,attributes:{[el.S3]:"auto.http.browser.inp",[el.$J]:`ui.interaction.${r}`,[el.JQ]:t.duration},startTime:i});l&&(l.addEvent("inp",{[el.E1]:"millisecond",[el.Wb]:e.value}),l.end(i+s))},nG,f)}}(),i&&g.GLOBAL_OBJ.PerformanceObserver&&PerformanceObserver.supportedEntryTypes&&PerformanceObserver.supportedEntryTypes.includes("long-animation-frame")?new PerformanceObserver(e=>{let t=(0,Z.HN)();if(t)for(let n of e.getEntries()){if(!n.scripts[0])continue;let e=nQ((0,ep.Z1)()+n.startTime),{start_timestamp:r,op:i}=(0,Z.XU)(t);if("navigation"===i&&r&&e<r)continue;let s=nQ(n.duration),a={[el.S3]:"auto.ui.browser.metrics"},{invoker:o,invokerType:u,sourceURL:c,sourceFunctionName:l,sourceCharPosition:p}=n.scripts[0];a["browser.script.invoker"]=o,a["browser.script.invoker_type"]=u,c&&(a["code.filepath"]=c),l&&(a["code.function"]=l),-1!==p&&(a["browser.script.source_char_position"]=p),nY(t,e,e+s,{name:"Main UI thread blocked",op:"ui.long-animation-frame",attributes:a})}}).observe({type:"long-animation-frame",buffered:!0}):r&&nM("longtask",({entries:e})=>{let t=(0,Z.HN)();if(!t)return;let{op:n,start_timestamp:r}=(0,Z.XU)(t);for(let i of e){let e=nQ((0,ep.Z1)()+i.startTime),s=nQ(i.duration);"navigation"===n&&r&&e<r||nY(t,e,e+s,{name:"Main UI thread blocked",op:"ui.long-task",attributes:{[el.S3]:"auto.ui.browser.metrics"}})}}),s&&nM("event",({entries:e})=>{let t=(0,Z.HN)();if(t){for(let n of e)if("click"===n.name){let e=nQ((0,ep.Z1)()+n.startTime),r=nQ(n.duration),i={name:(0,J.Rt)(n.target),op:`ui.interaction.${n.name}`,startTime:e,attributes:{[el.S3]:"auto.ui.browser.metrics"}},s=(0,J.iY)(n.target);s&&(i.attributes["ui.component_name"]=s),nY(t,e,e+r,i)}}});let P={name:void 0,source:void 0};function j(e,n){let r="pageload"===n.op,i=o?o(n):n,s=i.attributes||{};n.name!==i.name&&(s[el.Zj]="custom",i.attributes=s),P.name=i.name,P.source=s[el.Zj];let l=tQ(i,{idleTimeout:u,finalTimeout:c,childSpanTimeout:y,disableAutoFinish:r,beforeSpanEnd:t=>{var n;R(),function(e,t){let n=nK(),r=(0,ep.Z1)();if(!n?.getEntries||!r)return;let i=nQ(r),s=n.getEntries(),{op:a,start_timestamp:o}=(0,Z.XU)(e);if(s.slice(n1).forEach(t=>{let n=nQ(t.startTime),r=nQ(Math.max(0,t.duration));if("navigation"!==a||!o||!(i+n<o))switch(t.entryType){case"navigation":["unloadEvent","redirect","domContentLoadedEvent","loadEvent","connect"].forEach(n=>{n2(e,t,n,i)}),n2(e,t,"secureConnection",i,"TLS/SSL"),n2(e,t,"fetch",i,"cache"),n2(e,t,"domainLookup",i,"DNS"),function(e,t,n){let r=n+nQ(t.requestStart),i=n+nQ(t.responseEnd),s=n+nQ(t.responseStart);t.responseEnd&&(nY(e,r,i,{op:"browser.request",name:t.name,attributes:{[el.S3]:"auto.ui.browser.metrics"}}),nY(e,s,i,{op:"browser.response",name:t.name,attributes:{[el.S3]:"auto.ui.browser.metrics"}}))}(e,t,i);break;case"mark":case"paint":case"measure":{(function(e,t,n,r,i){let s=t9(!1),a=i+Math.max(n,nQ(s?s.requestStart:0)),o=i+n,u=o+r,c={[el.S3]:"auto.resource.browser.metrics"};a!==o&&(c["sentry.browser.measure_happened_before_request"]=!0,c["sentry.browser.measure_start_time"]=a),a<=u&&nY(e,a,u,{name:t.name,op:t.entryType,attributes:c})})(e,t,n,r,i);let s=no(),a=t.startTime<s.firstHiddenTime;"first-paint"===t.name&&a&&(n3.fp={value:t.startTime,unit:"millisecond"}),"first-contentful-paint"===t.name&&a&&(n3.fcp={value:t.startTime,unit:"millisecond"});break}case"resource":(function(e,t,n,r,i,s){if("xmlhttprequest"===t.initiatorType||"fetch"===t.initiatorType)return;let a=e0(n),o={[el.S3]:"auto.resource.browser.metrics"};n4(o,t,"transferSize","http.response_transfer_size"),n4(o,t,"encodedBodySize","http.response_content_length"),n4(o,t,"decodedBodySize","http.decoded_response_content_length");let u=t.deliveryType;null!=u&&(o["http.response_delivery_type"]=u);let c=t.renderBlockingStatus;c&&(o["resource.render_blocking_status"]=c),a.protocol&&(o["url.scheme"]=a.protocol.split(":").pop()),a.host&&(o["server.address"]=a.host),o["url.same_origin"]=n.includes(e3.location.origin);let{name:l,version:p}=n0(t.nextHopProtocol);o["network.protocol.name"]=l,o["network.protocol.version"]=p;let d=s+r;nY(e,d,d+i,{name:n.replace(e3.location.origin,""),op:t.initiatorType?`resource.${t.initiatorType}`:"resource.other",attributes:o})})(e,t,t.name,n,r,i)}}),n1=Math.max(s.length-1,0),function(e){let t=e3.navigator;if(!t)return;let n=t.connection;n&&(n.effectiveType&&e.setAttribute("effectiveConnectionType",n.effectiveType),n.type&&e.setAttribute("connectionType",n.type),nz(n.rtt)&&(n3["connection.rtt"]={value:n.rtt,unit:"millisecond"})),nz(t.deviceMemory)&&e.setAttribute("deviceMemory",`${t.deviceMemory} GB`),nz(t.hardwareConcurrency)&&e.setAttribute("hardwareConcurrency",String(t.hardwareConcurrency))}(e),"pageload"===a){(function(e){let t=t9(!1);if(!t)return;let{responseStart:n,requestStart:r}=t;r<=n&&(e["ttfb.requestTime"]={value:n-r,unit:"millisecond"})})(n3);let n=n3["mark.fid"];n&&n3.fid&&(nY(e,n.value,n.value+nQ(n3.fid.value),{name:"first input delay",op:"ui.action",attributes:{[el.S3]:"auto.ui.browser.metrics"}}),delete n3["mark.fid"]),"fcp"in n3&&t.recordClsOnPageloadSpan||delete n3.cls,Object.entries(n3).forEach(([e,t])=>{!function(e,t,n,r=(0,Z.HN)()){let i=r&&(0,Z.Gx)(r);i&&(S.X&&_.kg.log(`[Measurement] Setting measurement on root span: ${e} = ${t} ${n}`),i.addEvent(e,{[el.Wb]:t,[el.E1]:n}))}(e,t.value,t.unit)}),e.setAttribute("performance.timeOrigin",i),e.setAttribute("performance.activationStart",t8()),h&&(h.element&&e.setAttribute("lcp.element",(0,J.Rt)(h.element)),h.id&&e.setAttribute("lcp.id",h.id),h.url&&e.setAttribute("lcp.url",h.url.trim().slice(0,200)),null!=h.loadTime&&e.setAttribute("lcp.loadTime",h.loadTime),null!=h.renderTime&&e.setAttribute("lcp.renderTime",h.renderTime),e.setAttribute("lcp.size",h.size)),m?.sources&&m.sources.forEach((t,n)=>e.setAttribute(`cls.source.${n+1}`,(0,J.Rt)(t.node)))}h=void 0,m=void 0,n3={}}(t,{recordClsOnPageloadSpan:!a}),n=void 0,(0,D.xp)(e,rm,n);let r=(0,I.nZ)(),i=r.getPropagationContext();r.setPropagationContext({...i,traceId:l.spanContext().traceId,sampled:(0,Z.Tt)(l),dsc:(0,W.jC)(t)})}});function p(){t&&["interactive","complete"].includes(t.readyState)&&e.emit("idleSpanEnableAutoFinish",l)}(0,D.xp)(e,rm,l),r&&t&&(t.addEventListener("readystatechange",()=>{p()}),p())}return{name:"BrowserTracing",afterAllSetup(e){let t,r=(0,J.l4)();function i(){let t=e[rm];t&&!(0,Z.XU)(t).timestamp&&t.end()}if(e.on("startNavigationSpan",t=>{(0,I.s3)()===e&&(i(),(0,I.aF)().setPropagationContext({traceId:(0,tF.H)(),sampleRand:Math.random()}),(0,I.nZ)().setPropagationContext({traceId:(0,tF.H)(),sampleRand:Math.random()}),j(e,{op:"navigation",...t}))}),e.on("startPageLoadSpan",(t,n={})=>{if((0,I.s3)()!==e)return;i();let r=n.sentryTrace||rh("sentry-trace"),s=n.baggage||rh("baggage"),a=(0,t3.pT)(r,s);(0,I.nZ)().setPropagationContext(a),j(e,{op:"pageload",...t})}),"off"!==A&&function(e,{linkPreviousTrace:t,consistentTraceSampling:n}){let r="session-storage"===t,i=r?function(){try{let e=eP.sessionStorage?.getItem(n7);return JSON.parse(e)}catch(e){return}}():void 0;e.on("spanStart",e=>{if((0,Z.Gx)(e)!==e)return;let t=(0,I.nZ)().getPropagationContext();i=function(e,t,n){let r=(0,Z.XU)(t),i={spanContext:t.spanContext(),startTimestamp:r.start_timestamp,sampleRate:function(){try{return Number(n.dsc?.sample_rate)??Number(r.data?.[el.TE])}catch{return 0}}(),sampleRand:n.sampleRand};if(!e)return i;let s=e.spanContext;return s.traceId===r.trace_id?e:(Date.now()/1e3-e.startTimestamp<=3600&&(t.addLink({context:s,attributes:{[el.lH]:"previous_trace"}}),t.setAttribute("sentry.previous_trace",`${s.traceId}-${s.spanId}-${n6(s)?1:0}`)),i)}(i,e,t),r&&function(e){try{eP.sessionStorage.setItem(n7,JSON.stringify(e))}catch(e){}}(i)});let s=!0;n&&e.on("beforeSampling",e=>{if(!i)return;let t=(0,I.nZ)(),n=t.getPropagationContext();if(s&&n.parentSpanId){s=!1;return}t.setPropagationContext({...n,dsc:{...n.dsc,sample_rate:String(i.sampleRate),sampled:String(n6(i.spanContext))},sampleRand:i.sampleRand}),e.parentSampled=n6(i.spanContext),e.parentSampleRate=i.sampleRate,e.spanAttributes={...e.spanAttributes,[el.iT]:i.sampleRate}})}(e,{linkPreviousTrace:A,consistentTraceSampling:C}),eP.location){if(w){let t=(0,ep.Z1)();rd(e,{name:eP.location.pathname,startTime:t?t/1e3:void 0,attributes:{[el.Zj]:"url",[el.S3]:"auto.pageload.browser"}})}$&&e7(({to:t,from:n})=>{if(void 0===n&&r?.indexOf(t)!==-1){r=void 0;return}n!==t&&(r=void 0,rf(e,{name:eP.location.pathname,attributes:{[el.Zj]:"url",[el.S3]:"auto.navigation.browser"}}))})}v&&eP.document&&eP.document.addEventListener("visibilitychange",()=>{let e=(0,Z.HN)();if(!e)return;let t=(0,Z.Gx)(e);if(eP.document.hidden&&t){let{op:e,status:n}=(0,Z.XU)(t);n||t.setStatus({code:tB.jt,message:"cancelled"}),t.setAttribute("sentry.cancellation_reason","document.hidden"),t.end()}}),s&&eP.document&&addEventListener("click",()=>{let n=e[rm];!(n&&["navigation","pageload"].includes((0,Z.XU)(n).op))&&(t&&(t.setAttribute(el.ju,"interactionInterrupted"),t.end(),t=void 0),P.name&&(t=tQ({name:P.name,op:"ui.action.click",attributes:{[el.Zj]:P.source||"url"}},{idleTimeout:u,finalTimeout:c,childSpanTimeout:y})))},{once:!1,capture:!0}),n&&function(){let e=({entries:e})=>{let t=(0,Z.HN)(),n=t&&(0,Z.Gx)(t);e.forEach(e=>{if(!("duration"in e)||!n)return;let t=e.interactionId;if(!(null==t||n9.has(t))){if(n5.length>10){let e=n5.shift();n9.delete(e)}n5.push(t),n9.set(t,n)}})};nM("event",e),nM("first-input",e)}(),function(e,t){let{traceFetch:n,traceXHR:r,trackFetchStreamPerformance:i,shouldCreateSpanForRequest:s,enableHTTPTimings:a,tracePropagationTargets:o,onRequestSpanStart:u}={...rs,...t},c="function"==typeof s?s:e=>!0,l=e=>(function(e,t){let n=(0,J.l4)();if(n){let r,i;try{r=new URL(e,n),i=new URL(n).origin}catch(e){return!1}let s=r.origin===i;return t?(0,O.U0)(r.toString(),t)||s&&(0,O.U0)(r.pathname,t):s}{let n=!!e.match(/^\/(?!\/)/);return t?(0,O.U0)(e,t):n}})(e,o),p={};n&&(e.addEventProcessor(e=>("transaction"===e.type&&e.spans&&e.spans.forEach(e=>{if("http.client"===e.op){let t=ri.get(e.span_id);t&&(e.timestamp=t/1e3,ri.delete(e.span_id))}}),e)),i&&function(e){let t="fetch-body-resolved";eX(t,e),eJ(t,()=>eq(eZ))}(e=>{if(e.response){let t=rr.get(e.response);t&&e.endTimestamp&&ri.set(t,e.endTimestamp)}}),eG(e=>{let t=function(e,t,n,r,i="auto.http.browser"){if(!e.fetchData)return;let{method:s,url:a}=e.fetchData,o=(0,tj.f)()&&t(a);if(e.endTimestamp&&o){let t=e.fetchData.__span;if(!t)return;let n=r[t];n&&(function(e,t){if(t.response){(0,tB.Q0)(e,t.response.status);let n=t.response?.headers&&t.response.headers.get("content-length");if(n){let t=parseInt(n);t>0&&e.setAttribute("http.response_content_length",t)}}else t.error&&e.setStatus({code:tB.jt,message:"internal_error"});e.end()}(n,e),delete r[t]);return}let u=!!(0,Z.HN)(),c=o&&u?tZ(function(e,t,n){let r=function(e,t){let n=e.startsWith("/"),r=(void 0)??(n?"thismessage:/":void 0);try{if("canParse"in URL&&!URL.canParse(e,r))return;let t=new URL(e,r);if(n)return{isRelative:n,pathname:t.pathname,search:t.search,hash:t.hash};return t}catch{}}(e);return{name:r?`${t} ${function(e){if(eQ(e))return e.pathname;let t=new URL(e);return t.search="",t.hash="",["80","443"].includes(t.port)&&(t.port=""),t.password&&(t.password="%filtered%"),t.username&&(t.username="%filtered%"),t.toString()}(r)}`:t,attributes:function(e,t,n,r){let i={url:e,type:"fetch","http.method":n,[el.S3]:r,[el.$J]:"http.client"};return t&&(eQ(t)||(i["http.url"]=t.href,i["server.address"]=t.host),t.search&&(i["http.query"]=t.search),t.hash&&(i["http.fragment"]=t.hash)),i}(e,r,t,n)}}(a,s,i)):new tM;if(e.fetchData.__span=c.spanContext().spanId,r[c.spanContext().spanId]=c,n(e.fetchData.url)){let t=e.args[0],n=e.args[1]||{},r=function(e,t,n){let r=rt({span:n}),i=r["sentry-trace"],s=r.baggage;if(!i)return;let a=t.headers||((0,ed.gJ)(e)?e.headers:void 0);if(!a)return{...r};if("undefined"!=typeof Headers&&(0,ed.V9)(a,Headers)){let e=new Headers(a);if(e.get("sentry-trace")||e.set("sentry-trace",i),s){let t=e.get("baggage");t?rn(t)||e.set("baggage",`${t},${s}`):e.set("baggage",s)}return e}if(Array.isArray(a)){let e=[...a];a.find(e=>"sentry-trace"===e[0])||e.push(["sentry-trace",i]);let t=a.find(e=>"baggage"===e[0]&&rn(e[1]));return s&&!t&&e.push(["baggage",s]),e}{let e="sentry-trace"in a?a["sentry-trace"]:void 0,t="baggage"in a?a.baggage:void 0,n=t?Array.isArray(t)?[...t]:[t]:[],r=t&&(Array.isArray(t)?t.find(e=>rn(e)):rn(t));return s&&!r&&n.push(s),{...a,"sentry-trace":e??i,baggage:n.length>0?n.join(","):void 0}}}(t,n,(0,tj.f)()&&u?c:void 0);r&&(e.args[1]=n,n.headers=r)}let l=(0,I.s3)();if(l){let t={input:e.args,response:e.response,startTimestamp:e.startTimestamp,endTimestamp:e.endTimestamp};l.emit("beforeOutgoingRequestSpan",c,t)}return c}(e,c,l,p);if(e.response&&e.fetchData.__span&&rr.set(e.response,e.fetchData.__span),t){let n=ru(e.fetchData.url),r=n?e0(n).host:void 0;t.setAttributes({"http.url":n,"server.address":r}),a&&ra(t),u?.(t,{headers:e.headers})}})),r&&e9(e=>{let t=function(e,t,n,r){let i=e.xhr,s=i?.[e5];if(!i||i.__sentry_own_request__||!s)return;let{url:a,method:o}=s,u=(0,tj.f)()&&t(a);if(e.endTimestamp&&u){let e=i.__sentry_xhr_span_id__;if(!e)return;let t=r[e];t&&void 0!==s.status_code&&((0,tB.Q0)(t,s.status_code),t.end(),delete r[e]);return}let c=ru(a),l=c?e0(c):e0(a),p=e1(a),d=!!(0,Z.HN)(),f=u&&d?tZ({name:`${o} ${p}`,attributes:{url:a,type:"xhr","http.method":o,"http.url":c,"server.address":l?.host,[el.S3]:"auto.http.browser",[el.$J]:"http.client",...l?.search&&{"http.query":l?.search},...l?.hash&&{"http.fragment":l?.hash}}}):new tM;i.__sentry_xhr_span_id__=f.spanContext().spanId,r[i.__sentry_xhr_span_id__]=f,n(a)&&function(e,t){let{"sentry-trace":n,baggage:r}=rt({span:t});n&&function(e,t,n){let r=e.__sentry_xhr_v3__?.request_headers;if(!r?.["sentry-trace"])try{if(e.setRequestHeader("sentry-trace",t),n){let t=r?.baggage;t&&t.split(",").some(e=>e.trim().startsWith("sentry-"))||e.setRequestHeader("baggage",n)}}catch(e){}}(e,n,r)}(i,(0,tj.f)()&&d?f:void 0);let h=(0,I.s3)();return h&&h.emit("beforeOutgoingRequestSpan",f,e),f}(e,c,l,p);if(t){let n;a&&ra(t);try{n=new Headers(e.xhr.__sentry_xhr_v3__?.request_headers)}catch{}u?.(t,{headers:n})}})}(e,{traceFetch:b,traceXHR:E,trackFetchStreamPerformance:x,tracePropagationTargets:e.getOptions().tracePropagationTargets,shouldCreateSpanForRequest:k,enableHTTPTimings:T,onRequestSpanStart:L})}}};function rd(e,t,n){return e.emit("startPageLoadSpan",t,n),(0,I.nZ)().setTransactionName(t.name),e[rm]}function rf(e,t){return e.emit("startNavigationSpan",t),(0,I.nZ)().setTransactionName(t.name),e[rm]}function rh(e){let t=eP.document,n=t?.querySelector(`meta[name=${e}]`);return n?.getAttribute("content")||void 0}let rm="_sentry_idleSpan",rg="incomplete-app-router-transaction",r_={current:void 0},ry=g.GLOBAL_OBJ;function rv(e){try{return new URL(e,"http://example.com/").pathname}catch{return"/"}}let rb=new WeakSet;function rS(e,t,n){rb.has(t)||(rb.add(t),["back","forward","push","replace"].forEach(r=>{t?.[r]&&(t[r]=new Proxy(t[r],{apply(t,i,s){let a=rg,o={[el.$J]:"navigation",[el.S3]:"auto.navigation.nextjs.app_router_instrumentation",[el.Zj]:"url"};return"push"===r?(a=rv(s[0]),o["navigation.type"]="router.push"):"replace"===r?(a=rv(s[0]),o["navigation.type"]="router.replace"):"back"===r?o["navigation.type"]="router.back":"forward"===r&&(o["navigation.type"]="router.forward"),n.current=rf(e,{name:a,attributes:o}),t.apply(i,s)}}))}))}var rE=n(11163),rx=n(36096);let rk=rE.events?rE:rE.default,rT=/^(\S+:\\|\/?)([\s\S]*?)((?:\.{1,2}|[^/\\]+?|)(\.[^./\\]*|))(?:[/\\]*)$/;function rw(...e){let t="",n=!1;for(let r=e.length-1;r>=-1&&!n;r--){let i=r>=0?e[r]:"/";i&&(t=`${i}/${t}`,n="/"===i.charAt(0))}return t=(function(e,t){let n=0;for(let t=e.length-1;t>=0;t--){let r=e[t];"."===r?e.splice(t,1):".."===r?(e.splice(t,1),n++):n&&(e.splice(t,1),n--)}if(t)for(;n--;n)e.unshift("..");return e})(t.split("/").filter(e=>!!e),!n).join("/"),(n?"/":"")+t||"."}function rO(e){let t=0;for(;t<e.length&&""===e[t];t++);let n=e.length-1;for(;n>=0&&""===e[n];n--);return t>n?[]:e.slice(t,n-t+1)}let r$=(e={})=>{let t=e.root,n=e.prefix||"app:///",r="window"in g.GLOBAL_OBJ&&!!g.GLOBAL_OBJ.window,i=e.iteratee||function({isBrowser:e,root:t,prefix:n}){return r=>{if(!r.filename)return r;let i=/^[a-zA-Z]:\\/.test(r.filename)||r.filename.includes("\\")&&!r.filename.includes("/"),s=/^\//.test(r.filename);if(e){if(t){let e=r.filename;0===e.indexOf(t)&&(r.filename=e.replace(t,n))}}else if(i||s){let e;let s=i?r.filename.replace(/^[a-zA-Z]:/,"").replace(/\\/g,"/"):r.filename,a=t?function(e,t){e=rw(e).slice(1),t=rw(t).slice(1);let n=rO(e.split("/")),r=rO(t.split("/")),i=Math.min(n.length,r.length),s=i;for(let e=0;e<i;e++)if(n[e]!==r[e]){s=e;break}let a=[];for(let e=s;e<n.length;e++)a.push("..");return(a=a.concat(r.slice(s))).join("/")}(t,s):function(e){let t=e.length>1024?`<truncated>${e.slice(-1024)}`:e,n=rT.exec(t);return n?n.slice(1):[]}(s)[2]||"";r.filename=`${n}${a}`}return r}}({isBrowser:r,root:t,prefix:n});return{name:"RewriteFrames",processEvent(e){let t=e;return e.exception&&Array.isArray(e.exception.values)&&(t=function(e){try{return{...e,exception:{...e.exception,values:e.exception.values.map(e=>{var t;return{...e,...e.stacktrace&&{stacktrace:{...t=e.stacktrace,frames:t?.frames&&t.frames.map(e=>i(e))}}}})}}}catch(t){return e}}(t)),t}}},rA=({assetPrefix:e,basePath:t,rewriteFramesAssetPrefixPath:n,experimentalThirdPartyOriginStackFrames:r})=>({...r$({iteratee:i=>{if(r){let n="undefined"!=typeof window&&window.location?window.location.origin:"";if(i.filename?.startsWith(n)&&!i.filename.endsWith(".js"))return i;if(e)i.filename?.startsWith(e)&&(i.filename=i.filename.replace(e,"app://"));else if(t)try{let{origin:e}=new URL(i.filename);e===n&&(i.filename=i.filename?.replace(e,"app://").replace(t,""))}catch(e){}}else try{let{origin:e}=new URL(i.filename);i.filename=i.filename?.replace(e,"app://").replace(n,"")}catch(e){}return r?(i.filename?.includes("/_next")&&(i.filename=decodeURI(i.filename)),i.filename?.match(/\/_next\/static\/chunks\/(main-|main-app-|polyfills-|webpack-|framework-|framework\.)[0-9a-f]+\.js$/)&&(i.in_app=!1)):(i.filename?.startsWith("app:///_next")&&(i.filename=decodeURI(i.filename)),i.filename?.match(/^app:\/\/\/_next\/static\/chunks\/(main-|main-app-|polyfills-|webpack-|framework-|framework\.)[0-9a-f]+\.js$/)&&(i.in_app=!1)),i}}),name:"NextjsClientStackFrameNormalization"});var rC=n(83454);let rL=g.GLOBAL_OBJ;var rR=n(83454);let rI=!1,rD=g.GLOBAL_OBJ;globalThis._sentryRewritesTunnelPath=void 0,globalThis.SENTRY_RELEASE=void 0,globalThis._sentryBasePath=void 0,globalThis._sentryRewriteFramesAssetPrefixPath="",globalThis._sentryAssetPrefix=void 0,globalThis._sentryExperimentalThirdPartyOriginStackFrames=void 0,function(e){rI&&(0,_.Cf)(()=>{console.warn("[@sentry/nextjs] You are calling `Sentry.init()` more than once on the client. This can happen if you have both a `sentry.client.config.ts` and a `instrumentation-client.ts` file with `Sentry.init()` calls. It is recommended to call `Sentry.init()` once in `instrumentation-client.ts`.")}),rI=!0;let t={environment:function(e){let t=e?tP.env.NEXT_PUBLIC_VERCEL_ENV:tP.env.VERCEL_ENV;return t?`vercel-${t}`:void 0}(!0)||"production",defaultIntegrations:function(e){let t=tI(e);("undefined"==typeof __SENTRY_TRACING__||__SENTRY_TRACING__)&&t.push(function(e={}){let t=rp({...e,instrumentNavigation:!1,instrumentPageLoad:!1,onRequestSpanStart(...t){let[n,{headers:r}]=t;return r?.get("next-router-prefetch")&&n?.setAttribute("http.request.prefetch",!0),e.onRequestSpanStart?.(...t)}}),{instrumentPageLoad:n=!0,instrumentNavigation:r=!0}=e;return{...t,afterAllSetup(e){var i;if(r&&(i=e,eP.document.getElementById("__NEXT_DATA__")?rk.events.on("routeChangeStart",e=>{let t,n;let r=e1(e),s=function(e){let t=eP.__BUILD_MANIFEST?.sortedPages;if(t)return t.find(t=>{let n=function(e){let t=e.split("/"),n="";t[t.length-1]?.match(/^\[\[\.\.\..+\]\]$/)&&(t.pop(),n="(?:/(.+?))?");let r=t.map(e=>e.replace(/^\[\.\.\..+\]$/,"(.+?)").replace(/^\[.*\]$/,"([^/]+?)")).join("/");return RegExp(`^${r}${n}(?:/)?$`)}(t);return e.match(n)})}(r);s?(t=s,n="route"):(t=r,n="url"),rf(i,{name:t,attributes:{[el.$J]:"navigation",[el.S3]:"auto.navigation.nextjs.pages_router_instrumentation",[el.Zj]:n}})}):function(e){eP.addEventListener("popstate",()=>{r_.current?.isRecording()?(r_.current.updateName(eP.location.pathname),r_.current.setAttribute(el.Zj,"url")):r_.current=rf(e,{name:eP.location.pathname,attributes:{[el.S3]:"auto.navigation.nextjs.app_router_instrumentation",[el.Zj]:"url","navigation.type":"browser.popstate"}})});let t=!1,n=0,r=setInterval(()=>{n++;let i=ry?.next?.router??ry?.nd?.router;t||n>500?clearInterval(r):i&&(clearInterval(r),t=!0,rS(e,i,r_),["nd","next"].forEach(t=>{let n=ry[t];n&&(ry[t]=new Proxy(n,{set:(t,n,r)=>("router"===n&&"object"==typeof r&&null!==r&&rS(e,r,r_),t[n]=r,!0)}))}))},20)}(i)),t.afterAllSetup(e),n)eP.document.getElementById("__NEXT_DATA__")?function(e){let{route:t,params:n,sentryTrace:r,baggage:i}=function(){let e;let t=eP.document.getElementById("__NEXT_DATA__");if(t?.innerHTML)try{e=JSON.parse(t.innerHTML)}catch(e){rx.X&&_.kg.warn("Could not extract __NEXT_DATA__")}if(!e)return{};let n={},{page:r,query:i,props:s}=e;return n.route=r,n.params=i,s?.pageProps&&(n.sentryTrace=s.pageProps._sentryTraceData,n.baggage=s.pageProps._sentryBaggage),n}(),s=(0,re.XM)(i),a=t||eP.location.pathname;s?.["sentry-transaction"]&&"/_error"===a&&(a=(a=s["sentry-transaction"]).replace(/^(GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS|TRACE|CONNECT)\s+/i,""));let o=(0,ep.Z1)();rd(e,{name:a,startTime:o?o/1e3:void 0,attributes:{[el.$J]:"pageload",[el.S3]:"auto.pageload.nextjs.pages_router_instrumentation",[el.Zj]:t?"route":"url",...n&&e.getOptions().sendDefaultPii&&{...n}}},{sentryTrace:r,baggage:i})}(e):function(e){let t=(0,ep.Z1)();rd(e,{name:eP.location.pathname,startTime:t?t/1e3:void 0,attributes:{[el.$J]:"pageload",[el.S3]:"auto.pageload.nextjs.app_router_instrumentation",[el.Zj]:"url"}})}(e)}}}());let n=rD._sentryRewriteFramesAssetPrefixPath||"",r=rR.env._sentryAssetPrefix||rD._sentryAssetPrefix,i=rR.env._sentryBasePath||rD._sentryBasePath,s="true"===rR.env._experimentalThirdPartyOriginStackFrames||"true"===rD._experimentalThirdPartyOriginStackFrames;return t.push(rA({assetPrefix:r,basePath:i,rewriteFramesAssetPrefixPath:n,experimentalThirdPartyOriginStackFrames:s})),t}(e),release:rR.env._sentryRelease||rD._sentryRelease,...e};(function(e){let t=rC.env._sentryRewritesTunnelPath||rL._sentryRewritesTunnelPath;if(t&&e.dsn){let n=V(e.dsn);if(!n)return;let r=n.host.match(/^o(\d+)\.ingest(?:\.([a-z]{2}))?\.sentry\.io$/);if(r){let i=r[1],s=r[2],a=`${t}?o=${i}&p=${n.projectId}`;s&&(a+=`&r=${s}`),e.tunnel=a,rx.X&&_.kg.info(`Tunneling events to "${a}"`)}else rx.X&&_.kg.warn("Provided DSN is not a Sentry SaaS DSN. Will not tunnel events.")}})(t),v(t,"nextjs",["nextjs","react"]),function(e){let t={...e};v(t,"react"),(0,b.v)("react",{version:tD.version}),function(e={}){let t=function(e={}){return{defaultIntegrations:tI(),release:"string"==typeof __SENTRY_RELEASE__?__SENTRY_RELEASE__:eP.SENTRY_RELEASE?.id,sendClientReports:!0,...function(e){let t={};for(let n of Object.getOwnPropertyNames(e))void 0!==e[n]&&(t[n]=e[n]);return t}(e)}}(e);!(!t.skipBrowserExtensionCheck&&function(){let e=void 0!==eP.window&&eP;if(!e)return!1;let t=e.chrome?"chrome":"browser",n=e[t],r=n?.runtime?.id,i=(0,J.l4)()||"",s=!!r&&eP===eP.top&&["chrome-extension:","moz-extension:","ms-browser-extension:","safari-web-extension:"].some(e=>i.startsWith(`${e}//`)),a=void 0!==e.nw;return!!r&&!s&&!a}())&&function(e,t){!0===t.debug&&(S.X?_.kg.enable():(0,_.Cf)(()=>{console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.")})),(0,I.nZ)().update(t.initialScope);let n=new e(t);(0,I.nZ)().setClient(n),n.init()}(eF,{...t,stackParser:(0,N.Sq)(t.stackParser||t$),integrations:function(e){let t;let n=e.defaultIntegrations||[],r=e.integrations;if(n.forEach(e=>{e.isDefaultInstance=!0}),Array.isArray(r))t=[...n,...r];else if("function"==typeof r){let e=r(n);t=Array.isArray(e)?e:[e]}else t=n;return function(e){let t={};return e.forEach(e=>{let{name:n}=e,r=t[n];r&&!r.isDefaultInstance&&e.isDefaultInstance||(t[n]=e)}),Object.values(t)}(t)}(t),transport:t.transport||tR})}(t)}(t);let n=e=>"transaction"===e.type&&"/404"===e.transaction?null:e;n.id="NextClient404Filter",(0,b.Qy)(n);let r=e=>"transaction"===e.type&&e.transaction===rg?null:e;r.id="IncompleteTransactionFilter",(0,b.Qy)(r);let i=(e,t)=>{var n;return(n=t?.originalException,(0,ed.VZ)(n)&&"string"==typeof n.digest&&n.digest.startsWith("NEXT_REDIRECT;")||e.exception?.values?.[0]?.value==="NEXT_REDIRECT")?null:e};i.id="NextRedirectErrorFilter",(0,b.Qy)(i)}({dsn:"https://<EMAIL>/6746424",enabled:!0,tracesSampleRate:Number("0.01"),debug:!1,replaysOnErrorSampleRate:1,sampleRate:Number("0.05"),replaysSessionSampleRate:.1})},7443:function(e,t,n){"use strict";var r=n(83454);n(91479);var i=n(67294),s=i&&"object"==typeof i&&"default"in i?i:{default:i},a=void 0!==r&&r.env&&!0,o=function(e){return"[object String]"===Object.prototype.toString.call(e)},u=function(){function e(e){var t=void 0===e?{}:e,n=t.name,r=void 0===n?"stylesheet":n,i=t.optimizeForSpeed,s=void 0===i?a:i;c(o(r),"`name` must be a string"),this._name=r,this._deletedRulePlaceholder="#"+r+"-deleted-rule____{}",c("boolean"==typeof s,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=s,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var u=document.querySelector('meta[property="csp-nonce"]');this._nonce=u?u.getAttribute("content"):null}var t=e.prototype;return t.setOptimizeForSpeed=function(e){c("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),c(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},t.isOptimizeForSpeed=function(){return this._optimizeForSpeed},t.inject=function(){var e=this;if(c(!this._injected,"sheet already injected"),this._injected=!0,this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(a||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,n){return"number"==typeof n?e._serverSheet.cssRules[n]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),n},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},t.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},t.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},t.insertRule=function(e,t){if(c(o(e),"`insertRule` accepts only strings"),this._optimizeForSpeed){var n=this.getSheet();"number"!=typeof t&&(t=n.cssRules.length);try{n.insertRule(e,t)}catch(t){return a||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var r=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,r))}return this._rulesCount++},t.replaceRule=function(e,t){if(this._optimizeForSpeed){var n=this.getSheet();if(t.trim()||(t=this._deletedRulePlaceholder),!n.cssRules[e])return e;n.deleteRule(e);try{n.insertRule(t,e)}catch(r){a||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),n.insertRule(this._deletedRulePlaceholder,e)}}else{var r=this._tags[e];c(r,"old rule at index `"+e+"` not found"),r.textContent=t}return e},t.deleteRule=function(e){if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];c(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},t.flush=function(){this._injected=!1,this._rulesCount=0,this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]},t.cssRules=function(){var e=this;return this._tags.reduce(function(t,n){return n?t=t.concat(Array.prototype.map.call(e.getSheetForTag(n).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},t.makeStyleTag=function(e,t,n){t&&c(o(t),"makeStyleTag accepts only strings as second parameter");var r=document.createElement("style");this._nonce&&r.setAttribute("nonce",this._nonce),r.type="text/css",r.setAttribute("data-"+e,""),t&&r.appendChild(document.createTextNode(t));var i=document.head||document.getElementsByTagName("head")[0];return n?i.insertBefore(r,n):i.appendChild(r),r},function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(e.prototype,[{key:"length",get:function(){return this._rulesCount}}]),e}();function c(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var l=function(e){for(var t=5381,n=e.length;n;)t=33*t^e.charCodeAt(--n);return t>>>0},p={};function d(e,t){if(!t)return"jsx-"+e;var n=String(t),r=e+n;return p[r]||(p[r]="jsx-"+l(e+"-"+n)),p[r]}function f(e,t){var n=e+t;return p[n]||(p[n]=t.replace(/__jsx-style-dynamic-selector/g,e)),p[n]}var h=function(){function e(e){var t=void 0===e?{}:e,n=t.styleSheet,r=void 0===n?null:n,i=t.optimizeForSpeed,s=void 0!==i&&i;this._sheet=r||new u({name:"styled-jsx",optimizeForSpeed:s}),this._sheet.inject(),r&&"boolean"==typeof s&&(this._sheet.setOptimizeForSpeed(s),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var n=this.getIdAndRules(e),r=n.styleId,i=n.rules;if(r in this._instancesCounts){this._instancesCounts[r]+=1;return}var s=i.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[r]=s,this._instancesCounts[r]=1},t.remove=function(e){var t=this,n=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(n in this._instancesCounts,"styleId: `"+n+"` not found"),this._instancesCounts[n]-=1,this._instancesCounts[n]<1){var r=this._fromServer&&this._fromServer[n];r?(r.parentNode.removeChild(r),delete this._fromServer[n]):(this._indices[n].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[n]),delete this._instancesCounts[n]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],n=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return n[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,n;return t=this.cssRules(),void 0===(n=e)&&(n={}),t.map(function(e){var t=e[0],r=e[1];return s.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:n.nonce?n.nonce:void 0,dangerouslySetInnerHTML:{__html:r}})})},t.getIdAndRules=function(e){var t=e.children,n=e.dynamic,r=e.id;if(n){var i=d(r,n);return{styleId:i,rules:Array.isArray(t)?t.map(function(e){return f(i,e)}):[f(i,t)]}}return{styleId:d(r),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),m=i.createContext(null);m.displayName="StyleSheetContext";var g=s.default.useInsertionEffect||s.default.useLayoutEffect,_=new h;function y(e){var t=_||i.useContext(m);return t&&g(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)]),null}y.dynamic=function(e){return e.map(function(e){return d(e[0],e[1])}).join(" ")},t.style=y},40645:function(e,t,n){"use strict";e.exports=n(7443).style},90273:function(e,t,n){"use strict";n.d(t,{k:function(){return r}});let r=(0,n(67294).createContext)({attributes:{seo:void 0,metaTitleSuffix:void 0,engineVersion:void 0,serverVersion:void 0}})},55545:function(e,t,n){"use strict";n.r(t);var r=n(85893),i=n(82567),s=n.n(i),a=n(48371),o=n.n(a),u=n(40645),c=n.n(u),l=n(9008),p=n.n(l),d=n(11163),f=n(67294),h=n(90273);n(56976),t.default=e=>{let{Component:t,pageProps:i}=e,{global:a,altLangs:u}=i,l=(0,d.useRouter)().asPath.split("?")[0];return(0,f.useEffect)(()=>{(async()=>{await Promise.all([n.e(8999),n.e(8665)]).then(n.bind(n,8665))})()},[]),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(p(),{children:null==u?void 0:u.map(e=>(0,r.jsx)("link",{rel:"alternate",hrefLang:e,href:"https://www.maptiler.com"+("en"===e?"":"/".concat(e))+l,className:c().dynamic([["7872c881873233a9",[o().style.fontFamily,s().style.fontFamily]]])},e))}),(0,r.jsx)(c(),{id:"7872c881873233a9",dynamic:[o().style.fontFamily,s().style.fontFamily],children:":root{--bs-font-ubuntu:".concat(o().style.fontFamily,";--bs-font-sans-serif:").concat(s().style.fontFamily,", 'Segoe UI', sans-serif}")}),(0,r.jsx)(h.k.Provider,{value:a,children:(0,r.jsx)(t,{...i,className:c().dynamic([["7872c881873233a9",[o().style.fontFamily,s().style.fontFamily]]])+" "+(i&&null!=i.className&&i.className||"")})})]})}},91479:function(){},56976:function(){},82567:function(e){e.exports={style:{fontFamily:"'__Open_Sans_5a8d6f', '__Open_Sans_Fallback_5a8d6f'"},className:"__className_5a8d6f"}},48371:function(e){e.exports={style:{fontFamily:"'__ubuntu_8fe8a5', '__ubuntu_Fallback_8fe8a5'"},className:"__className_8fe8a5"}},77663:function(e){!function(){var t={229:function(e){var t,n,r,i=e.exports={};function s(){throw Error("setTimeout has not been defined")}function a(){throw Error("clearTimeout has not been defined")}function o(e){if(t===setTimeout)return setTimeout(e,0);if((t===s||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(n){try{return t.call(null,e,0)}catch(n){return t.call(this,e,0)}}}!function(){try{t="function"==typeof setTimeout?setTimeout:s}catch(e){t=s}try{n="function"==typeof clearTimeout?clearTimeout:a}catch(e){n=a}}();var u=[],c=!1,l=-1;function p(){c&&r&&(c=!1,r.length?u=r.concat(u):l=-1,u.length&&d())}function d(){if(!c){var e=o(p);c=!0;for(var t=u.length;t;){for(r=u,u=[];++l<t;)r&&r[l].run();l=-1,t=u.length}r=null,c=!1,function(e){if(n===clearTimeout)return clearTimeout(e);if((n===a||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(e);try{n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}(e)}}function f(e,t){this.fun=e,this.array=t}function h(){}i.nextTick=function(e){var t=Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];u.push(new f(e,t)),1!==u.length||c||o(d)},f.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=h,i.addListener=h,i.once=h,i.off=h,i.removeListener=h,i.removeAllListeners=h,i.emit=h,i.prependListener=h,i.prependOnceListener=h,i.listeners=function(e){return[]},i.binding=function(e){throw Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw Error("process.chdir is not supported")},i.umask=function(){return 0}}},n={};function r(e){var i=n[e];if(void 0!==i)return i.exports;var s=n[e]={exports:{}},a=!0;try{t[e](s,s.exports,r),a=!1}finally{a&&delete n[e]}return s.exports}r.ab="//";var i=r(229);e.exports=i}()},9008:function(e,t,n){e.exports=n(23867)},11163:function(e,t,n){e.exports=n(43079)},60811:function(e,t,n){"use strict";n.d(t,{G:function(){return p}});var r=n(13533),i=n(43927),s=n(24925);class a{constructor(e,t){let n,r;n=e||new i.s,r=t||new i.s,this._stack=[{scope:n}],this._isolationScope=r}withScope(e){let t;let n=this._pushScope();try{t=e(n)}catch(e){throw this._popScope(),e}return(0,s.J8)(t)?t.then(e=>(this._popScope(),e),e=>{throw this._popScope(),e}):(this._popScope(),t)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStackTop(){return this._stack[this._stack.length-1]}_pushScope(){let e=this.getScope().clone();return this._stack.push({client:this.getClient(),scope:e}),e}_popScope(){return!(this._stack.length<=1)&&!!this._stack.pop()}}function o(){let e=(0,r.cu)(),t=(0,r.qA)(e);return t.stack=t.stack||new a((0,r.YO)("defaultCurrentScope",()=>new i.s),(0,r.YO)("defaultIsolationScope",()=>new i.s))}function u(e){return o().withScope(e)}function c(e,t){let n=o();return n.withScope(()=>(n.getStackTop().scope=e,t(e)))}function l(e){return o().withScope(()=>e(o().getIsolationScope()))}function p(e){let t=(0,r.qA)(e);return t.acs?t.acs:{withIsolationScope:l,withScope:u,withSetScope:c,withSetIsolationScope:(e,t)=>l(t),getCurrentScope:()=>o().getScope(),getIsolationScope:()=>o().getIsolationScope()}}},13533:function(e,t,n){"use strict";n.d(t,{YO:function(){return o},cu:function(){return s},qA:function(){return a}});var r=n(49889),i=n(33280);function s(){return a(i.GLOBAL_OBJ),i.GLOBAL_OBJ}function a(e){let t=e.__SENTRY__=e.__SENTRY__||{};return t.version=t.version||r.J,t[r.J]=t[r.J]||{}}function o(e,t,n=i.GLOBAL_OBJ){let s=n.__SENTRY__=n.__SENTRY__||{},a=s[r.J]=s[r.J]||{};return a[e]||(a[e]=t())}},73243:function(e,t,n){"use strict";n.d(t,{J:function(){return r}});let r="production"},73076:function(e,t,n){"use strict";n.d(t,{$e:function(){return l},XX:function(){return d},aF:function(){return u},lW:function(){return c},nZ:function(){return o},s3:function(){return p}});var r=n(60811),i=n(13533),s=n(43927),a=n(51824);function o(){let e=(0,i.cu)();return(0,r.G)(e).getCurrentScope()}function u(){let e=(0,i.cu)();return(0,r.G)(e).getIsolationScope()}function c(){return(0,i.YO)("globalScope",()=>new s.s)}function l(...e){let t=(0,i.cu)(),n=(0,r.G)(t);if(2===e.length){let[t,r]=e;return t?n.withSetScope(t,r):n.withScope(r)}return n.withScope(e[0])}function p(){return o().getClient()}function d(e){let{traceId:t,parentSpanId:n,propagationSpanId:r}=e.getPropagationContext(),i={trace_id:t,span_id:r||(0,a.M)()};return n&&(i.parent_span_id=n),i}},94223:function(e,t,n){"use strict";n.d(t,{X:function(){return r}});let r=!1},39424:function(e,t,n){"use strict";n.d(t,{Qy:function(){return h},Tb:function(){return c},_k:function(){return f},cg:function(){return y},eN:function(){return l},v:function(){return p},yj:function(){return m},yl:function(){return d}});var r=n(73076),i=n(94223),s=n(10042),a=n(47515),o=n(17986),u=n(33280);function c(e,t){return(0,r.nZ)().captureException(e,(0,a.U0)(t))}function l(e,t){return(0,r.nZ)().captureEvent(e,t)}function p(e,t){(0,r.aF)().setContext(e,t)}async function d(e){let t=(0,r.s3)();return t?t.flush(e):(i.X&&o.kg.warn("Cannot flush events. No client defined."),Promise.resolve(!1))}function f(){let e=(0,r.s3)();return e?.getOptions().enabled!==!1&&!!e?.getTransport()}function h(e){(0,r.aF)().addEventProcessor(e)}function m(e){let t=(0,r.aF)(),n=(0,r.nZ)(),{userAgent:i}=u.GLOBAL_OBJ.navigator||{},a=(0,s.Hv)({user:n.getUser()||t.getUser(),...i&&{userAgent:i},...e}),o=t.getSession();return o?.status==="ok"&&(0,s.CT)(o,{status:"exited"}),g(),t.setSession(a),a}function g(){let e=(0,r.aF)(),t=(0,r.nZ)().getSession()||e.getSession();t&&(0,s.RJ)(t),_(),e.setSession()}function _(){let e=(0,r.aF)(),t=(0,r.s3)(),n=e.getSession();n&&t&&t.captureSession(n)}function y(e=!1){if(e){g();return}_()}},43927:function(e,t,n){"use strict";n.d(t,{s:function(){return d}});var r=n(10042),i=n(3613),s=n(89366),a=n(24925),o=n(17986),u=n(82305),c=n(51824),l=n(50027),p=n(59943);class d{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext={traceId:(0,c.H)(),sampleRand:Math.random()}}clone(){let e=new d;return e._breadcrumbs=[...this._breadcrumbs],e._tags={...this._tags},e._extra={...this._extra},e._contexts={...this._contexts},this._contexts.flags&&(e._contexts.flags={values:[...this._contexts.flags.values]}),e._user=this._user,e._level=this._level,e._session=this._session,e._transactionName=this._transactionName,e._fingerprint=this._fingerprint,e._eventProcessors=[...this._eventProcessors],e._attachments=[...this._attachments],e._sdkProcessingMetadata={...this._sdkProcessingMetadata},e._propagationContext={...this._propagationContext},e._client=this._client,e._lastEventId=this._lastEventId,(0,s.D)(e,(0,s.Y)(this)),e}setClient(e){this._client=e}setLastEventId(e){this._lastEventId=e}getClient(){return this._client}lastEventId(){return this._lastEventId}addScopeListener(e){this._scopeListeners.push(e)}addEventProcessor(e){return this._eventProcessors.push(e),this}setUser(e){return this._user=e||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this._session&&(0,r.CT)(this._session,{user:e}),this._notifyScopeListeners(),this}getUser(){return this._user}setTags(e){return this._tags={...this._tags,...e},this._notifyScopeListeners(),this}setTag(e,t){return this._tags={...this._tags,[e]:t},this._notifyScopeListeners(),this}setExtras(e){return this._extra={...this._extra,...e},this._notifyScopeListeners(),this}setExtra(e,t){return this._extra={...this._extra,[e]:t},this._notifyScopeListeners(),this}setFingerprint(e){return this._fingerprint=e,this._notifyScopeListeners(),this}setLevel(e){return this._level=e,this._notifyScopeListeners(),this}setTransactionName(e){return this._transactionName=e,this._notifyScopeListeners(),this}setContext(e,t){return null===t?delete this._contexts[e]:this._contexts[e]=t,this._notifyScopeListeners(),this}setSession(e){return e?this._session=e:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(e){if(!e)return this;let t="function"==typeof e?e(this):e,{tags:n,extra:r,user:i,contexts:s,level:o,fingerprint:u=[],propagationContext:c}=(t instanceof d?t.getScopeData():(0,a.PO)(t)?e:void 0)||{};return this._tags={...this._tags,...n},this._extra={...this._extra,...r},this._contexts={...this._contexts,...s},i&&Object.keys(i).length&&(this._user=i),o&&(this._level=o),u.length&&(this._fingerprint=u),c&&(this._propagationContext=c),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._session=void 0,(0,s.D)(this,void 0),this._attachments=[],this.setPropagationContext({traceId:(0,c.H)(),sampleRand:Math.random()}),this._notifyScopeListeners(),this}addBreadcrumb(e,t){let n="number"==typeof t?t:100;if(n<=0)return this;let r={timestamp:(0,p.yW)(),...e,message:e.message?(0,l.$G)(e.message,2048):e.message};return this._breadcrumbs.push(r),this._breadcrumbs.length>n&&(this._breadcrumbs=this._breadcrumbs.slice(-n),this._client?.recordDroppedEvent("buffer_overflow","log_item")),this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(e){return this._attachments.push(e),this}clearAttachments(){return this._attachments=[],this}getScopeData(){return{breadcrumbs:this._breadcrumbs,attachments:this._attachments,contexts:this._contexts,tags:this._tags,extra:this._extra,user:this._user,level:this._level,fingerprint:this._fingerprint||[],eventProcessors:this._eventProcessors,propagationContext:this._propagationContext,sdkProcessingMetadata:this._sdkProcessingMetadata,transactionName:this._transactionName,span:(0,s.Y)(this)}}setSDKProcessingMetadata(e){return this._sdkProcessingMetadata=(0,i.T)(this._sdkProcessingMetadata,e,2),this}setPropagationContext(e){return this._propagationContext=e,this}getPropagationContext(){return this._propagationContext}captureException(e,t){let n=t?.event_id||(0,u.DM)();if(!this._client)return o.kg.warn("No client configured on scope - will not capture exception!"),n;let r=Error("Sentry syntheticException");return this._client.captureException(e,{originalException:e,syntheticException:r,...t,event_id:n},this),n}captureMessage(e,t,n){let r=n?.event_id||(0,u.DM)();if(!this._client)return o.kg.warn("No client configured on scope - will not capture message!"),r;let i=Error(e);return this._client.captureMessage(e,t,{originalException:e,syntheticException:i,...n,event_id:r},this),r}captureEvent(e,t){let n=t?.event_id||(0,u.DM)();return this._client?this._client.captureEvent(e,{...t,event_id:n},this):o.kg.warn("No client configured on scope - will not capture event!"),n}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(e=>{e(this)}),this._notifyingListeners=!1)}}},31218:function(e,t,n){"use strict";n.d(t,{$J:function(){return a},E1:function(){return c},JQ:function(){return f},S3:function(){return o},TE:function(){return i},Wb:function(){return l},Zj:function(){return r},iT:function(){return s},ju:function(){return u},lH:function(){return h},p6:function(){return d},xF:function(){return p}});let r="sentry.source",i="sentry.sample_rate",s="sentry.previous_trace_sample_rate",a="sentry.op",o="sentry.origin",u="sentry.idle_span_finish_reason",c="sentry.measurement_unit",l="sentry.measurement_value",p="sentry.custom_span_name",d="sentry.profile_id",f="sentry.exclusive_time",h="sentry.link.type"},10042:function(e,t,n){"use strict";n.d(t,{CT:function(){return a},Hv:function(){return s},RJ:function(){return o}});var r=n(82305),i=n(59943);function s(e){let t=(0,i.ph)(),n={sid:(0,r.DM)(),init:!0,timestamp:t,started:t,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>({sid:`${n.sid}`,init:n.init,started:new Date(1e3*n.started).toISOString(),timestamp:new Date(1e3*n.timestamp).toISOString(),status:n.status,errors:n.errors,did:"number"==typeof n.did||"string"==typeof n.did?`${n.did}`:void 0,duration:n.duration,abnormal_mechanism:n.abnormal_mechanism,attrs:{release:n.release,environment:n.environment,ip_address:n.ipAddress,user_agent:n.userAgent}})};return e&&a(n,e),n}function a(e,t={}){if(!t.user||(!e.ipAddress&&t.user.ip_address&&(e.ipAddress=t.user.ip_address),e.did||t.did||(e.did=t.user.id||t.user.email||t.user.username)),e.timestamp=t.timestamp||(0,i.ph)(),t.abnormal_mechanism&&(e.abnormal_mechanism=t.abnormal_mechanism),t.ignoreDuration&&(e.ignoreDuration=t.ignoreDuration),t.sid&&(e.sid=32===t.sid.length?t.sid:(0,r.DM)()),void 0!==t.init&&(e.init=t.init),!e.did&&t.did&&(e.did=`${t.did}`),"number"==typeof t.started&&(e.started=t.started),e.ignoreDuration)e.duration=void 0;else if("number"==typeof t.duration)e.duration=t.duration;else{let t=e.timestamp-e.started;e.duration=t>=0?t:0}t.release&&(e.release=t.release),t.environment&&(e.environment=t.environment),!e.ipAddress&&t.ipAddress&&(e.ipAddress=t.ipAddress),!e.userAgent&&t.userAgent&&(e.userAgent=t.userAgent),"number"==typeof t.errors&&(e.errors=t.errors),t.status&&(e.status=t.status)}function o(e,t){let n={};t?n={status:t}:"ok"===e.status&&(n={status:"exited"}),a(e,n)}},72123:function(e,t,n){"use strict";n.d(t,{CG:function(){return h},Lh:function(){return d},jC:function(){return m}});var r=n(73243),i=n(73076),s=n(31218),a=n(51072),o=n(81585),u=n(98823),c=n(51150),l=n(96515);let p="_frozenDsc";function d(e,t){(0,c.xp)(e,p,t)}function f(e,t){let n=t.getOptions(),{publicKey:i}=t.getDsn()||{},s={environment:n.environment||r.J,release:n.release,public_key:i,trace_id:e};return t.emit("createDsc",s),s}function h(e,t){let n=t.getPropagationContext();return n.dsc||f(n.traceId,e)}function m(e){let t=(0,i.s3)();if(!t)return{};let n=(0,o.Gx)(e),r=(0,o.XU)(n),c=r.data,d=n.spanContext().traceState,h=d?.get("sentry.sample_rate")??c[s.TE]??c[s.iT];function m(e){return("number"==typeof h||"string"==typeof h)&&(e.sample_rate=`${h}`),e}let g=n[p];if(g)return m(g);let _=d?.get("sentry.dsc"),y=_&&(0,u.EN)(_);if(y)return m(y);let v=f(e.spanContext().traceId,t),b=c[s.Zj],S=r.description;return"url"!==b&&S&&(v.transaction=S),(0,a.f)()&&(v.sampled=String((0,o.Tt)(n)),v.sample_rand=d?.get("sentry.sample_rand")??l.I(n).scope?.getPropagationContext().sampleRand.toString()),m(v),t.emit("createDsc",v,n),v}},69737:function(e,t,n){"use strict";n.d(t,{OP:function(){return i},Q0:function(){return a},jt:function(){return s},pq:function(){return r}});let r=0,i=1,s=2;function a(e,t){e.setAttribute("http.response.status_code",t);let n=function(e){if(e<400&&e>=100)return{code:i};if(e>=400&&e<500)switch(e){case 401:return{code:s,message:"unauthenticated"};case 403:return{code:s,message:"permission_denied"};case 404:return{code:s,message:"not_found"};case 409:return{code:s,message:"already_exists"};case 413:return{code:s,message:"failed_precondition"};case 429:return{code:s,message:"resource_exhausted"};case 499:return{code:s,message:"cancelled"};default:return{code:s,message:"invalid_argument"}}if(e>=500&&e<600)switch(e){case 501:return{code:s,message:"unimplemented"};case 503:return{code:s,message:"unavailable"};case 504:return{code:s,message:"deadline_exceeded"};default:return{code:s,message:"internal_error"}}return{code:s,message:"unknown_error"}}(t);"unknown_error"!==n.message&&e.setStatus(n)}},96515:function(e,t,n){"use strict";n.d(t,{I:function(){return o},Y:function(){return a}});var r=n(51150);let i="_sentryScope",s="_sentryIsolationScope";function a(e,t,n){e&&((0,r.xp)(e,s,n),(0,r.xp)(e,i,t))}function o(e){return{scope:e[i],isolationScope:e[s]}}},98823:function(e,t,n){"use strict";n.d(t,{EN:function(){return u},IQ:function(){return c},XM:function(){return l},lq:function(){return a}});var r=n(23187),i=n(24925),s=n(17986);let a="sentry-",o=/^sentry-/;function u(e){let t=l(e);if(!t)return;let n=Object.entries(t).reduce((e,[t,n])=>(t.match(o)&&(e[t.slice(a.length)]=n),e),{});return Object.keys(n).length>0?n:void 0}function c(e){if(e)return function(e){if(0!==Object.keys(e).length)return Object.entries(e).reduce((e,[t,n],i)=>{let a=`${encodeURIComponent(t)}=${encodeURIComponent(n)}`,o=0===i?a:`${e},${a}`;return o.length>8192?(r.X&&s.kg.warn(`Not adding key: ${t} with val: ${n} to baggage header due to exceeding baggage size limits.`),e):o},"")}(Object.entries(e).reduce((e,[t,n])=>(n&&(e[`${a}${t}`]=n),e),{}))}function l(e){return e&&((0,i.HD)(e)||Array.isArray(e))?Array.isArray(e)?e.reduce((e,t)=>(Object.entries(p(t)).forEach(([t,n])=>{e[t]=n}),e),{}):p(e):void 0}function p(e){return e.split(",").map(e=>e.split("=").map(e=>decodeURIComponent(e.trim()))).reduce((e,[t,n])=>(t&&n&&(e[t]=n),e),{})}},70428:function(e,t,n){"use strict";n.d(t,{Rt:function(){return s},iY:function(){return o},l4:function(){return a}});var r=n(24925);let i=n(33280).GLOBAL_OBJ;function s(e,t={}){if(!e)return"<unknown>";try{let n,s=e,a=[],o=0,u=0,c=Array.isArray(t)?t:t.keyAttrs,l=!Array.isArray(t)&&t.maxStringLength||80;for(;s&&o++<5&&(n=function(e,t){let n=[];if(!e?.tagName)return"";if(i.HTMLElement&&e instanceof HTMLElement&&e.dataset){if(e.dataset.sentryComponent)return e.dataset.sentryComponent;if(e.dataset.sentryElement)return e.dataset.sentryElement}n.push(e.tagName.toLowerCase());let s=t?.length?t.filter(t=>e.getAttribute(t)).map(t=>[t,e.getAttribute(t)]):null;if(s?.length)s.forEach(e=>{n.push(`[${e[0]}="${e[1]}"]`)});else{e.id&&n.push(`#${e.id}`);let t=e.className;if(t&&(0,r.HD)(t))for(let e of t.split(/\s+/))n.push(`.${e}`)}for(let t of["aria-label","type","name","title","alt"]){let r=e.getAttribute(t);r&&n.push(`[${t}="${r}"]`)}return n.join("")}(s,c),"html"!==n&&(!(o>1)||!(u+3*a.length+n.length>=l)));)a.push(n),u+=n.length,s=s.parentNode;return a.reverse().join(" > ")}catch(e){return"<unknown>"}}function a(){try{return i.document.location.href}catch(e){return""}}function o(e){if(!i.HTMLElement)return null;let t=e;for(let e=0;e<5&&t;e++){if(t instanceof HTMLElement){if(t.dataset.sentryComponent)return t.dataset.sentryComponent;if(t.dataset.sentryElement)return t.dataset.sentryElement}t=t.parentNode}return null}},23187:function(e,t,n){"use strict";n.d(t,{X:function(){return r}});let r=!1},24925:function(e,t,n){"use strict";n.d(t,{Cy:function(){return _},HD:function(){return c},J8:function(){return g},Kj:function(){return m},Le:function(){return l},PO:function(){return d},TX:function(){return o},V9:function(){return y},VW:function(){return a},VZ:function(){return i},cO:function(){return f},fm:function(){return u},gJ:function(){return b},kK:function(){return h},pt:function(){return p},y1:function(){return v}});let r=Object.prototype.toString;function i(e){switch(r.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object WebAssembly.Exception]":return!0;default:return y(e,Error)}}function s(e,t){return r.call(e)===`[object ${t}]`}function a(e){return s(e,"ErrorEvent")}function o(e){return s(e,"DOMError")}function u(e){return s(e,"DOMException")}function c(e){return s(e,"String")}function l(e){return"object"==typeof e&&null!==e&&"__sentry_template_string__"in e&&"__sentry_template_values__"in e}function p(e){return null===e||l(e)||"object"!=typeof e&&"function"!=typeof e}function d(e){return s(e,"Object")}function f(e){return"undefined"!=typeof Event&&y(e,Event)}function h(e){return"undefined"!=typeof Element&&y(e,Element)}function m(e){return s(e,"RegExp")}function g(e){return!!(e?.then&&"function"==typeof e.then)}function _(e){return d(e)&&"nativeEvent"in e&&"preventDefault"in e&&"stopPropagation"in e}function y(e,t){try{return e instanceof t}catch(e){return!1}}function v(e){return!!("object"==typeof e&&null!==e&&(e.__isVue||e._isVue))}function b(e){return"undefined"!=typeof Request&&y(e,Request)}},17986:function(e,t,n){"use strict";n.d(t,{Cf:function(){return u},LD:function(){return o},RU:function(){return a},kg:function(){return c}});var r=n(13533),i=n(23187),s=n(33280);let a=["debug","info","warn","error","log","assert","trace"],o={};function u(e){if(!("console"in s.GLOBAL_OBJ))return e();let t=s.GLOBAL_OBJ.console,n={},r=Object.keys(o);r.forEach(e=>{let r=o[e];n[e]=t[e],t[e]=r});try{return e()}finally{r.forEach(e=>{t[e]=n[e]})}}let c=(0,r.YO)("logger",function(){let e=!1,t={enable:()=>{e=!0},disable:()=>{e=!1},isEnabled:()=>e};return i.X?a.forEach(n=>{t[n]=(...t)=>{e&&u(()=>{s.GLOBAL_OBJ.console[n](`Sentry Logger [${n}]:`,...t)})}}):a.forEach(e=>{t[e]=()=>void 0}),t})},82305:function(e,t,n){"use strict";n.d(t,{DM:function(){return s},Db:function(){return u},EG:function(){return c},YO:function(){return l},jH:function(){return o}});var r=n(51150),i=n(33280);function s(e=function(){let e=i.GLOBAL_OBJ;return e.crypto||e.msCrypto}()){let t=()=>16*Math.random();try{if(e?.randomUUID)return e.randomUUID().replace(/-/g,"");e?.getRandomValues&&(t=()=>{let t=new Uint8Array(1);return e.getRandomValues(t),t[0]})}catch(e){}return"10000000100040008000100000000000".replace(/[018]/g,e=>(e^(15&t())>>e/4).toString(16))}function a(e){return e.exception?.values?.[0]}function o(e){let{message:t,event_id:n}=e;if(t)return t;let r=a(e);return r?r.type&&r.value?`${r.type}: ${r.value}`:r.type||r.value||n||"<unknown>":n||"<unknown>"}function u(e,t,n){let r=e.exception=e.exception||{},i=r.values=r.values||[],s=i[0]=i[0]||{};s.value||(s.value=t||""),s.type||(s.type=n||"Error")}function c(e,t){let n=a(e);if(!n)return;let r=n.mechanism;if(n.mechanism={type:"generic",handled:!0,...r,...t},t&&"data"in t){let e={...r?.data,...t.data};n.mechanism.data=e}}function l(e){if(function(e){try{return e.__sentry_captured__}catch{}}(e))return!0;try{(0,r.xp)(e,"__sentry_captured__",!0)}catch(e){}return!1}},29218:function(e,t,n){"use strict";n.d(t,{Fv:function(){return a},Qy:function(){return function e(t,n=3,r=102400){let i=a(t,n);return~-encodeURI(JSON.stringify(i)).split(/%..|./).length>r?e(t,n-1,r):i}}});var r=n(24925),i=n(51150),s=n(39649);function a(e,t=100,n=Infinity){try{return function e(t,n,a=Infinity,o=Infinity,u=function(){let e=new WeakSet;return[function(t){return!!e.has(t)||(e.add(t),!1)},function(t){e.delete(t)}]}()){let[c,l]=u;if(null==n||["boolean","string"].includes(typeof n)||"number"==typeof n&&Number.isFinite(n))return n;let p=function(e,t){try{if("domain"===e&&t&&"object"==typeof t&&t._events)return"[Domain]";if("domainEmitter"===e)return"[DomainEmitter]";if("undefined"!=typeof global&&t===global)return"[Global]";if("undefined"!=typeof window&&t===window)return"[Window]";if("undefined"!=typeof document&&t===document)return"[Document]";if((0,r.y1)(t))return"[VueViewModel]";if((0,r.Cy)(t))return"[SyntheticEvent]";if("number"==typeof t&&!Number.isFinite(t))return`[${t}]`;if("function"==typeof t)return`[Function: ${(0,s.$P)(t)}]`;if("symbol"==typeof t)return`[${String(t)}]`;if("bigint"==typeof t)return`[BigInt: ${String(t)}]`;let n=function(e){let t=Object.getPrototypeOf(e);return t?.constructor?t.constructor.name:"null prototype"}(t);if(/^HTML(\w*)Element$/.test(n))return`[HTMLElement: ${n}]`;return`[object ${n}]`}catch(e){return`**non-serializable** (${e})`}}(t,n);if(!p.startsWith("[object "))return p;if(n.__sentry_skip_normalization__)return n;let d="number"==typeof n.__sentry_override_normalization_depth__?n.__sentry_override_normalization_depth__:a;if(0===d)return p.replace("object ","");if(c(n))return"[Circular ~]";if(n&&"function"==typeof n.toJSON)try{let t=n.toJSON();return e("",t,d-1,o,u)}catch(e){}let f=Array.isArray(n)?[]:{},h=0,m=(0,i.Sh)(n);for(let t in m){if(!Object.prototype.hasOwnProperty.call(m,t))continue;if(h>=o){f[t]="[MaxProperties ~]";break}let n=m[t];f[t]=e(t,n,d-1,o,u),h++}return l(n),f}("",e,t,n)}catch(e){return{ERROR:`**non-serializable** (${e})`}}}},51150:function(e,t,n){"use strict";n.d(t,{$Q:function(){return l},HK:function(){return p},Sh:function(){return d},hl:function(){return u},xp:function(){return c},zf:function(){return m}});var r=n(70428),i=n(23187),s=n(24925),a=n(17986),o=n(50027);function u(e,t,n){if(!(t in e))return;let r=e[t];if("function"!=typeof r)return;let s=n(r);"function"==typeof s&&l(s,r);try{e[t]=s}catch{i.X&&a.kg.log(`Failed to replace method "${t}" in object`,e)}}function c(e,t,n){try{Object.defineProperty(e,t,{value:n,writable:!0,configurable:!0})}catch(n){i.X&&a.kg.log(`Failed to add non-enumerable property "${t}" to object`,e)}}function l(e,t){try{let n=t.prototype||{};e.prototype=t.prototype=n,c(e,"__sentry_original__",t)}catch(e){}}function p(e){return e.__sentry_original__}function d(e){if((0,s.VZ)(e))return{message:e.message,name:e.name,stack:e.stack,...h(e)};if(!(0,s.cO)(e))return e;{let t={type:e.type,target:f(e.target),currentTarget:f(e.currentTarget),...h(e)};return"undefined"!=typeof CustomEvent&&(0,s.V9)(e,CustomEvent)&&(t.detail=e.detail),t}}function f(e){try{return(0,s.kK)(e)?(0,r.Rt)(e):Object.prototype.toString.call(e)}catch(e){return"<unknown>"}}function h(e){if("object"!=typeof e||null===e)return{};{let t={};for(let n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}}function m(e,t=40){let n=Object.keys(d(e));n.sort();let r=n[0];if(!r)return"[object has no keys]";if(r.length>=t)return(0,o.$G)(r,t);for(let e=n.length;e>0;e--){let r=n.slice(0,e).join(", ");if(!(r.length>t)){if(e===n.length)return r;return(0,o.$G)(r,t)}}return""}},51824:function(e,t,n){"use strict";n.d(t,{H:function(){return i},M:function(){return s}});var r=n(82305);function i(){return(0,r.DM)()}function s(){return(0,r.DM)().substring(16)}},39649:function(e,t,n){"use strict";n.d(t,{$P:function(){return l},Fi:function(){return r},Fr:function(){return p},Sq:function(){return o},pE:function(){return a}});let r="?",i=/\(error: (.*)\)/,s=/captureMessage|captureException/;function a(...e){let t=e.sort((e,t)=>e[0]-t[0]).map(e=>e[1]);return(e,n=0,a=0)=>{let o=[],c=e.split("\n");for(let e=n;e<c.length;e++){let n=c[e];if(n.length>1024)continue;let r=i.test(n)?n.replace(i,"$1"):n;if(!r.match(/\S*Error: /)){for(let e of t){let t=e(r);if(t){o.push(t);break}}if(o.length>=50+a)break}}return function(e){if(!e.length)return[];let t=Array.from(e);return/sentryWrapped/.test(u(t).function||"")&&t.pop(),t.reverse(),s.test(u(t).function||"")&&(t.pop(),s.test(u(t).function||"")&&t.pop()),t.slice(0,50).map(e=>({...e,filename:e.filename||u(t).filename,function:e.function||r}))}(o.slice(a))}}function o(e){return Array.isArray(e)?a(...e):e}function u(e){return e[e.length-1]||{}}let c="<anonymous>";function l(e){try{if(!e||"function"!=typeof e)return c;return e.name||c}catch(e){return c}}function p(e){let t=e.exception;if(t){let e=[];try{return t.values.forEach(t=>{t.stacktrace.frames&&e.push(...t.stacktrace.frames)}),e}catch(e){}}}},50027:function(e,t,n){"use strict";n.d(t,{$G:function(){return i},U0:function(){return a},nK:function(){return s}});var r=n(24925);function i(e,t=0){return"string"!=typeof e||0===t?e:e.length<=t?e:`${e.slice(0,t)}...`}function s(e,t){if(!Array.isArray(e))return"";let n=[];for(let t=0;t<e.length;t++){let i=e[t];try{(0,r.y1)(i)?n.push("[VueViewModel]"):n.push(String(i))}catch(e){n.push("[value cannot be serialized]")}}return n.join(t)}function a(e,t=[],n=!1){return t.some(t=>(function(e,t,n=!1){return!!(0,r.HD)(e)&&((0,r.Kj)(t)?t.test(e):!!(0,r.HD)(t)&&(n?e===t:e.includes(t)))})(e,t,n))}},52340:function(e,t,n){"use strict";n.d(t,{$2:function(){return o},WD:function(){return a},cW:function(){return u}});var r,i,s=n(24925);function a(e){return new u(t=>{t(e)})}function o(e){return new u((t,n)=>{n(e)})}(r=i||(i={}))[r.PENDING=0]="PENDING",r[r.RESOLVED=1]="RESOLVED",r[r.REJECTED=2]="REJECTED";class u{constructor(e){this._state=i.PENDING,this._handlers=[],this._runExecutor(e)}then(e,t){return new u((n,r)=>{this._handlers.push([!1,t=>{if(e)try{n(e(t))}catch(e){r(e)}else n(t)},e=>{if(t)try{n(t(e))}catch(e){r(e)}else r(e)}]),this._executeHandlers()})}catch(e){return this.then(e=>e,e)}finally(e){return new u((t,n)=>{let r,i;return this.then(t=>{i=!1,r=t,e&&e()},t=>{i=!0,r=t,e&&e()}).then(()=>{if(i){n(r);return}t(r)})})}_executeHandlers(){if(this._state===i.PENDING)return;let e=this._handlers.slice();this._handlers=[],e.forEach(e=>{e[0]||(this._state===i.RESOLVED&&e[1](this._value),this._state===i.REJECTED&&e[2](this._value),e[0]=!0)})}_runExecutor(e){let t=(e,t)=>{if(this._state===i.PENDING){if((0,s.J8)(t)){t.then(n,r);return}this._state=e,this._value=t,this._executeHandlers()}},n=e=>{t(i.RESOLVED,e)},r=e=>{t(i.REJECTED,e)};try{e(n,r)}catch(e){r(e)}}}},59943:function(e,t,n){"use strict";let r;n.d(t,{Z1:function(){return o},ph:function(){return a},yW:function(){return s}});var i=n(33280);function s(){return Date.now()/1e3}let a=function(){let{performance:e}=i.GLOBAL_OBJ;if(!e?.now)return s;let t=Date.now()-e.now(),n=void 0==e.timeOrigin?t:e.timeOrigin;return()=>(n+e.now())/1e3}();function o(){return r||(r=function(){let{performance:e}=i.GLOBAL_OBJ;if(!e?.now)return[void 0,"none"];let t=e.now(),n=Date.now(),r=e.timeOrigin?Math.abs(e.timeOrigin+t-n):36e5,s=e.timing?.navigationStart,a="number"==typeof s?Math.abs(s+t-n):36e5;return r<36e5||a<36e5?r<=a?[e.timeOrigin,"timeOrigin"]:[s,"navigationStart"]:[n,"dateNow"]}()),r[0]}},94801:function(e,t,n){"use strict";n.d(t,{$p:function(){return u},Ke:function(){return a},pT:function(){return o}});var r=n(79769),i=n(98823),s=n(51824);let a=RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");function o(e,t){let n=function(e){let t;if(!e)return;let n=e.match(a);if(n)return"1"===n[3]?t=!0:"0"===n[3]&&(t=!1),{traceId:n[1],parentSampled:t,parentSpanId:n[2]}}(e),o=(0,i.EN)(t);if(!n?.traceId)return{traceId:(0,s.H)(),sampleRand:Math.random()};let u=function(e,t){let n=(0,r.o)(t?.sample_rand);if(void 0!==n)return n;let i=(0,r.o)(t?.sample_rate);return i&&e?.parentSampled!==void 0?e.parentSampled?Math.random()*i:i+Math.random()*(1-i):Math.random()}(n,o);o&&(o.sample_rand=u.toString());let{traceId:c,parentSpanId:l,parentSampled:p}=n;return{traceId:c,parentSpanId:l,sampled:p,dsc:o||{},sampleRand:u}}function u(e=(0,s.H)(),t=(0,s.M)(),n){let r="";return void 0!==n&&(r=n?"-1":"-0"),`${e}-${t}${r}`}},49889:function(e,t,n){"use strict";n.d(t,{J:function(){return r}});let r="9.17.0"},33280:function(e,t,n){"use strict";n.d(t,{GLOBAL_OBJ:function(){return r}});let r=globalThis},51072:function(e,t,n){"use strict";n.d(t,{f:function(){return i}});var r=n(73076);function i(e){if("boolean"==typeof __SENTRY_TRACING__&&!__SENTRY_TRACING__)return!1;let t=e||r.s3()?.getOptions();return!!t&&(null!=t.tracesSampleRate||!!t.tracesSampler)}},3613:function(e,t,n){"use strict";n.d(t,{T:function(){return function e(t,n,r=2){if(!n||"object"!=typeof n||r<=0)return n;if(t&&0===Object.keys(n).length)return t;let i={...t};for(let t in n)Object.prototype.hasOwnProperty.call(n,t)&&(i[t]=e(i[t],n[t],r-1));return i}}})},79769:function(e,t,n){"use strict";function r(e){if("boolean"==typeof e)return Number(e);let t="string"==typeof e?parseFloat(e):e;if(!("number"!=typeof t||isNaN(t))&&!(t<0)&&!(t>1))return t}n.d(t,{o:function(){return r}})},47515:function(e,t,n){"use strict";let r,i,s;n.d(t,{U0:function(){return k},R:function(){return x}});var a=n(73243),o=n(73076),u=n(94223),c=n(24925),l=n(17986),p=n(52340),d=n(43927),f=n(33280),h=n(82305),m=n(29218),g=n(50027),_=n(59943),y=n(72123),v=n(3613),b=n(81585);function S(e,t){let{extra:n,tags:r,user:i,contexts:s,level:a,sdkProcessingMetadata:o,breadcrumbs:u,fingerprint:c,eventProcessors:l,attachments:p,propagationContext:d,transactionName:f,span:h}=t;E(e,"extra",n),E(e,"tags",r),E(e,"user",i),E(e,"contexts",s),e.sdkProcessingMetadata=(0,v.T)(e.sdkProcessingMetadata,o,2),a&&(e.level=a),f&&(e.transactionName=f),h&&(e.span=h),u.length&&(e.breadcrumbs=[...e.breadcrumbs,...u]),c.length&&(e.fingerprint=[...e.fingerprint,...c]),l.length&&(e.eventProcessors=[...e.eventProcessors,...l]),p.length&&(e.attachments=[...e.attachments,...p]),e.propagationContext={...e.propagationContext,...d}}function E(e,t,n){e[t]=(0,v.T)(e[t],n,1)}function x(e,t,n,v,E,x){let{normalizeDepth:k=3,normalizeMaxBreadth:T=1e3}=e,w={...t,event_id:t.event_id||n.event_id||(0,h.DM)(),timestamp:t.timestamp||(0,_.yW)()},O=n.integrations||e.integrations.map(e=>e.name);(function(e,t){let{environment:n,release:r,dist:i,maxValueLength:s=250}=t;e.environment=e.environment||n||a.J,!e.release&&r&&(e.release=r),!e.dist&&i&&(e.dist=i);let o=e.request;o?.url&&(o.url=(0,g.$G)(o.url,s))})(w,e),O.length>0&&(w.sdk=w.sdk||{},w.sdk.integrations=[...w.sdk.integrations||[],...O]),E&&E.emit("applyFrameMetadata",t),void 0===t.type&&function(e,t){let n=function(e){let t=f.GLOBAL_OBJ._sentryDebugIds;if(!t)return{};let n=Object.keys(t);return s&&n.length===i?s:(i=n.length,s=n.reduce((n,i)=>{r||(r={});let s=r[i];if(s)n[s[0]]=s[1];else{let s=e(i);for(let e=s.length-1;e>=0;e--){let a=s[e],o=a?.filename,u=t[i];if(o&&u){n[o]=u,r[i]=[o,u];break}}}return n},{}))}(t);e.exception?.values?.forEach(e=>{e.stacktrace?.frames?.forEach(e=>{e.filename&&(e.debug_id=n[e.filename])})})}(w,e.stackParser);let $=function(e,t){if(!t)return e;let n=e?e.clone():new d.s;return n.update(t),n}(v,n.captureContext);n.mechanism&&(0,h.EG)(w,n.mechanism);let A=E?E.getEventProcessors():[],C=(0,o.lW)().getScopeData();x&&S(C,x.getScopeData()),$&&S(C,$.getScopeData());let L=[...n.attachments||[],...C.attachments];return L.length&&(n.attachments=L),!function(e,t){let{fingerprint:n,span:r,breadcrumbs:i,sdkProcessingMetadata:s}=t;(function(e,t){let{extra:n,tags:r,user:i,contexts:s,level:a,transactionName:o}=t;Object.keys(n).length&&(e.extra={...n,...e.extra}),Object.keys(r).length&&(e.tags={...r,...e.tags}),Object.keys(i).length&&(e.user={...i,...e.user}),Object.keys(s).length&&(e.contexts={...s,...e.contexts}),a&&(e.level=a),o&&"transaction"!==e.type&&(e.transaction=o)})(e,t),r&&function(e,t){e.contexts={trace:(0,b.wy)(t),...e.contexts},e.sdkProcessingMetadata={dynamicSamplingContext:(0,y.jC)(t),...e.sdkProcessingMetadata};let n=(0,b.Gx)(t),r=(0,b.XU)(n).description;r&&!e.transaction&&"transaction"===e.type&&(e.transaction=r)}(e,r),e.fingerprint=e.fingerprint?Array.isArray(e.fingerprint)?e.fingerprint:[e.fingerprint]:[],n&&(e.fingerprint=e.fingerprint.concat(n)),e.fingerprint.length||delete e.fingerprint,function(e,t){let n=[...e.breadcrumbs||[],...t];e.breadcrumbs=n.length?n:void 0}(e,i),e.sdkProcessingMetadata={...e.sdkProcessingMetadata,...s}}(w,C),(function e(t,n,r,i=0){return new p.cW((s,a)=>{let o=t[i];if(null===n||"function"!=typeof o)s(n);else{let p=o({...n},r);u.X&&o.id&&null===p&&l.kg.log(`Event processor "${o.id}" dropped event`),(0,c.J8)(p)?p.then(n=>e(t,n,r,i+1).then(s)).then(null,a):e(t,p,r,i+1).then(s).then(null,a)}})})([...A,...C.eventProcessors],w,n).then(e=>(e&&function(e){let t={};if(e.exception?.values?.forEach(e=>{e.stacktrace?.frames?.forEach(e=>{e.debug_id&&(e.abs_path?t[e.abs_path]=e.debug_id:e.filename&&(t[e.filename]=e.debug_id),delete e.debug_id)})}),0===Object.keys(t).length)return;e.debug_meta=e.debug_meta||{},e.debug_meta.images=e.debug_meta.images||[];let n=e.debug_meta.images;Object.entries(t).forEach(([e,t])=>{n.push({type:"sourcemap",code_file:e,debug_id:t})})}(e),"number"==typeof k&&k>0)?function(e,t,n){if(!e)return null;let r={...e,...e.breadcrumbs&&{breadcrumbs:e.breadcrumbs.map(e=>({...e,...e.data&&{data:(0,m.Fv)(e.data,t,n)}}))},...e.user&&{user:(0,m.Fv)(e.user,t,n)},...e.contexts&&{contexts:(0,m.Fv)(e.contexts,t,n)},...e.extra&&{extra:(0,m.Fv)(e.extra,t,n)}};return e.contexts?.trace&&r.contexts&&(r.contexts.trace=e.contexts.trace,e.contexts.trace.data&&(r.contexts.trace.data=(0,m.Fv)(e.contexts.trace.data,t,n))),e.spans&&(r.spans=e.spans.map(e=>({...e,...e.data&&{data:(0,m.Fv)(e.data,t,n)}}))),e.contexts?.flags&&r.contexts&&(r.contexts.flags=(0,m.Fv)(e.contexts.flags,3,n)),r}(e,k,T):e)}function k(e){return e?e instanceof d.s||"function"==typeof e||Object.keys(e).some(e=>T.includes(e))?{captureContext:e}:e:void 0}let T=["user","level","extra","contexts","tags","fingerprint","propagationContext"]},89366:function(e,t,n){"use strict";n.d(t,{D:function(){return s},Y:function(){return a}});var r=n(51150);let i="_sentrySpan";function s(e,t){t?(0,r.xp)(e,i,t):delete e[i]}function a(e){return e[i]}},81585:function(e,t,n){"use strict";n.d(t,{$k:function(){return E},Dp:function(){return L},FF:function(){return S},Gx:function(){return R},HN:function(){return I},HR:function(){return y},Hb:function(){return b},R6:function(){return D},Tt:function(){return T},XU:function(){return k},_4:function(){return w},ed:function(){return C},i0:function(){return g},j5:function(){return A},ve:function(){return m},wy:function(){return v}});var r=n(60811),i=n(13533),s=n(73076),a=n(31218),o=n(69737),u=n(96515),c=n(17986),l=n(51150),p=n(51824),d=n(59943),f=n(94801),h=n(89366);let m=0,g=1,_=!1;function y(e){let{spanId:t,traceId:n}=e.spanContext(),{data:r,op:i,parent_span_id:s,status:a,origin:o,links:u}=k(e);return{parent_span_id:s,span_id:t,trace_id:n,data:r,op:i,status:a,origin:o,links:u}}function v(e){let{spanId:t,traceId:n,isRemote:r}=e.spanContext(),i=r?t:k(e).parent_span_id,s=(0,u.I)(e).scope;return{parent_span_id:i,span_id:r?s?.getPropagationContext().propagationSpanId||(0,p.M)():t,trace_id:n}}function b(e){let{traceId:t,spanId:n}=e.spanContext(),r=T(e);return(0,f.$p)(t,n,r)}function S(e){return e&&e.length>0?e.map(({context:{spanId:e,traceId:t,traceFlags:n,...r},attributes:i})=>({span_id:e,trace_id:t,sampled:n===g,attributes:i,...r})):void 0}function E(e){return"number"==typeof e?x(e):Array.isArray(e)?e[0]+e[1]/1e9:e instanceof Date?x(e.getTime()):(0,d.ph)()}function x(e){return e>9999999999?e/1e3:e}function k(e){if("function"==typeof e.getSpanJSON)return e.getSpanJSON();let{spanId:t,traceId:n}=e.spanContext();if(e.attributes&&e.startTime&&e.name&&e.endTime&&e.status){let{attributes:r,startTime:i,name:s,endTime:o,parentSpanId:u,status:c,links:l}=e;return{span_id:t,trace_id:n,data:r,description:s,parent_span_id:u,start_timestamp:E(i),timestamp:E(o)||void 0,status:w(c),op:r[a.$J],origin:r[a.S3],links:S(l)}}return{span_id:t,trace_id:n,start_timestamp:0,data:{}}}function T(e){let{traceFlags:t}=e.spanContext();return t===g}function w(e){return e&&e.code!==o.pq?e.code===o.OP?"ok":e.message||"unknown_error":void 0}let O="_sentryChildSpans",$="_sentryRootSpan";function A(e,t){let n=e[$]||e;(0,l.xp)(t,$,n),e[O]?e[O].add(t):(0,l.xp)(e,O,new Set([t]))}function C(e,t){e[O]&&e[O].delete(t)}function L(e){let t=new Set;return!function e(n){if(!t.has(n)&&T(n))for(let r of(t.add(n),n[O]?Array.from(n[O]):[]))e(r)}(e),Array.from(t)}function R(e){return e[$]||e}function I(){let e=(0,i.cu)(),t=(0,r.G)(e);return t.getActiveSpan?t.getActiveSpan():(0,h.Y)((0,s.nZ)())}function D(){_||((0,c.Cf)(()=>{console.warn("[Sentry] Returning null from `beforeSendSpan` is disallowed. To drop certain spans, configure the respective integrations directly.")}),_=!0)}},36096:function(e,t,n){"use strict";n.d(t,{X:function(){return r}});let r=!1}},function(e){var t=function(t){return e(e.s=t)};e.O(0,[9774,179],function(){return t(5620),t(6840),t(43079)}),_N_E=e.O()}]);