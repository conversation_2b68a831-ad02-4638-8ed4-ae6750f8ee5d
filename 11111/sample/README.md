# Weather 3D Volume Cloud Demo

## 📁 Cấu trúc thư mục

```
11111/
├── weather-3d-volume-demo.html        ← File chính để chạy
├── sample/
│   └── README.md                      ← File này
├── data/                              ← 10 ảnh JPG weather
│   ├── 090000.jpg
│   ├── 090600.jpg
│   ├── 091200.jpg
│   ├── 091800.jpg
│   ├── 092400.jpg
│   ├── 093000.jpg
│   ├── 093600.jpg
│   ├── 094200.jpg
│   ├── 094800.jpg
│   └── 095400.jpg
├── build/                             ← Three.js core
│   └── three.module.js
└── jsm/                               ← Three.js addons
    ├── controls/OrbitControls.js
    └── libs/lil-gui.module.min.js
```

## 🚀 Cách chạy

1. **Mở file HTML:**
   ```
   Mở file: 11111/weather-3d-volume-demo.html
   ```

2. **Chạy local server (khuyến nghị):**
   ```bash
   # Từ thư mục 11111/
   python -m http.server 8080
   # Hoặc
   npx serve .

   # Sau đó mở: http://localhost:8080/weather-3d-volume-demo.html
   ```

## 🎮 Tính năng

### **Volume Rendering từ 10 ảnh JPG:**
- Tự động load 10 ảnh weather từ thư mục `data/`
- Tạo 3D volume texture từ các ảnh
- Interpolation mượt mà giữa các time frame
- Weather colormap: Blue → Green → Yellow → Red

### **Interactive Controls:**

#### **Animation Panel:**
- **Opacity:** Điều chỉnh độ trong suốt (0-1)
- **Speed:** Tốc độ animation (0-5)
- **Time:** Thời gian hiện tại (0-9, tương ứng 10 ảnh)
- **Play/Pause:** Bật/tắt animation tự động
- **Reset:** Reset về thời gian 0

#### **Rendering Panel:**
- **Threshold:** Ngưỡng cho isosurface rendering (0-1)
- **Steps:** Số bước ray marching (32-128)
- **Style:** Volume hoặc Isosurface rendering

#### **Cut-off Panel:**
- **Min/Max:** Giới hạn giá trị hiển thị (0-1)

#### **Performance Panel:**
- **Scale:** Tỷ lệ kích thước volume (0.5-2)

### **Mouse Controls:**
- **Left click + drag:** Xoay camera
- **Right click + drag:** Pan camera
- **Mouse wheel:** Zoom in/out

## 🔧 Kỹ thuật

### **Volume Rendering:**
- **Ray Marching:** Kỹ thuật render volume 3D
- **3D Texture:** Lưu trữ dữ liệu weather 3D
- **Custom Shaders:** GLSL shaders cho volume rendering
- **Time Interpolation:** Smooth transition giữa các frame

### **Weather Data Processing:**
- **Image Loading:** Async loading 10 ảnh JPG
- **Grayscale Conversion:** Chuyển RGBA → Grayscale
- **3D Volume Assembly:** Ghép 10 ảnh thành volume 3D
- **Real-time Animation:** Update theo timeline

### **Performance Optimization:**
- **Adaptive Step Size:** Tối ưu ray marching
- **Early Ray Termination:** Dừng khi alpha >= 0.95
- **Configurable Quality:** Điều chỉnh steps và scale

## 🎯 Workflow

1. **Load 10 ảnh JPG** từ thư mục `data/`
2. **Convert thành grayscale** và tạo 3D texture
3. **Setup volume material** với custom shaders
4. **Render với ray marching** trong 3D space
5. **Animate** giữa các time frame
6. **Interactive controls** cho real-time adjustment

## 🐛 Troubleshooting

### **Không load được ảnh:**
- Kiểm tra 10 ảnh JPG có trong thư mục `11111/data/`
- Chạy qua local server thay vì mở file trực tiếp

### **Không hiển thị gì:**
- Mở Console (F12) để xem lỗi
- Kiểm tra đường dẫn Three.js modules
- Thử điều chỉnh Opacity và Threshold

### **Performance chậm:**
- Giảm Steps (32-64)
- Giảm Scale (0.5-1.0)
- Sử dụng browser hỗ trợ WebGL tốt

## 📝 Notes

- **Browser support:** Chrome, Firefox, Safari (cần WebGL)
- **File size:** 10 ảnh JPG nên < 10MB total
- **Resolution:** Ảnh nên có cùng kích thước
- **Format:** Chỉ hỗ trợ JPG/JPEG

## 🎨 Customization

### **Thay đổi colormap:**
Sửa function `applyColormap()` trong fragment shader

### **Thêm ảnh:**
- Thêm filename vào array `imageFilenames`
- Đặt ảnh vào thư mục `data/`

### **Điều chỉnh rendering:**
- Sửa uniforms trong `createVolumeMaterial()`
- Thay đổi shader code cho effects khác
