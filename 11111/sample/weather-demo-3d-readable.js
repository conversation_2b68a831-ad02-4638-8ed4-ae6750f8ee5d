/**
 * Weather Demo 3D - Readable Version
 * Volume rendering for weather data visualization
 * Based on Three.js volume rendering techniques
 */

// Import dependencies (would be handled by module system)
// import * as THREE from 'three';
// import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
// import { GUI } from 'three/addons/libs/lil-gui.module.min.js';

class WeatherDemo3D {
    constructor() {
        // Core Three.js components
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        
        // Weather-specific components
        this.weatherDataHandler = null;
        this.volumeLayer = null;
        this.material = null;
        this.mesh = null;
        
        // Configuration
        this.config = {
            opacity: 1.0,
            threshold: 0.5,
            colormap: 'temperature',
            renderStyle: 'volume', // 'volume' or 'isosurface'
            animationSpeed: 1.0,
            currentTime: 0
        };
        
        // Animation
        this.clock = new THREE.Clock();
        this.isAnimating = false;
        
        this.init();
    }
    
    init() {
        this.createScene();
        this.createCamera();
        this.createRenderer();
        this.createControls();
        this.createGUI();
        this.setupEventListeners();
        
        // Start the application
        this.loadWeatherData();
        this.animate();
    }
    
    createScene() {
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x000011);
        
        // Add ambient lighting
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        
        // Add directional light
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(1, 1, 1);
        this.scene.add(directionalLight);
    }
    
    createCamera() {
        const aspect = window.innerWidth / window.innerHeight;
        this.camera = new THREE.PerspectiveCamera(75, aspect, 0.1, 1000);
        this.camera.position.set(0, 0, 5);
    }
    
    createRenderer() {
        this.renderer = new THREE.WebGLRenderer({ 
            antialias: true,
            alpha: true 
        });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(window.devicePixelRatio);
        document.body.appendChild(this.renderer.domElement);
    }
    
    createControls() {
        this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        this.controls.enableZoom = true;
        this.controls.enablePan = true;
    }
    
    createGUI() {
        const gui = new GUI();
        
        // Rendering controls
        const renderFolder = gui.addFolder('Rendering');
        renderFolder.add(this.config, 'opacity', 0, 1, 0.01).onChange(() => this.updateUniforms());
        renderFolder.add(this.config, 'threshold', 0, 1, 0.01).onChange(() => this.updateUniforms());
        renderFolder.add(this.config, 'renderStyle', ['volume', 'isosurface']).onChange(() => this.updateUniforms());
        renderFolder.add(this.config, 'colormap', ['temperature', 'wind', 'pressure']).onChange(() => this.updateColormap());
        
        // Animation controls
        const animFolder = gui.addFolder('Animation');
        animFolder.add(this.config, 'animationSpeed', 0, 5, 0.1);
        animFolder.add(this, 'toggleAnimation').name('Play/Pause');
        animFolder.add(this.config, 'currentTime', 0, 24, 0.1).onChange(() => this.updateTime());
    }
    
    async loadWeatherData() {
        try {
            // Initialize weather data handler
            this.weatherDataHandler = new WeatherDataHandler();
            
            // Fetch latest weather data
            await this.weatherDataHandler.fetchLatest();
            
            // Create volume layer
            this.createVolumeLayer();
            
            console.log('Weather data loaded successfully');
        } catch (error) {
            console.error('Failed to load weather data:', error);
        }
    }
    
    createVolumeLayer() {
        // Get weather data
        const weatherData = this.weatherDataHandler.getTemperatureData();
        if (!weatherData) {
            console.error('No weather data available');
            return;
        }
        
        // Create 3D texture from weather data
        const texture3D = this.createWeatherTexture(weatherData);
        
        // Create volume material
        this.material = this.createVolumeMaterial(texture3D);
        
        // Create geometry and mesh
        const geometry = new THREE.BoxGeometry(2, 2, 2);
        this.mesh = new THREE.Mesh(geometry, this.material);
        this.scene.add(this.mesh);
    }
    
    createWeatherTexture(weatherData) {
        const { width, height, depth, data } = weatherData;
        
        // Create 3D texture
        const texture = new THREE.Data3DTexture(data, width, height, depth);
        texture.format = THREE.RedFormat;
        texture.type = THREE.FloatType;
        texture.minFilter = THREE.LinearFilter;
        texture.magFilter = THREE.LinearFilter;
        texture.wrapS = THREE.ClampToEdgeWrapping;
        texture.wrapT = THREE.ClampToEdgeWrapping;
        texture.wrapR = THREE.ClampToEdgeWrapping;
        texture.needsUpdate = true;
        
        return texture;
    }
    
    createVolumeMaterial(texture3D) {
        // Volume rendering shaders
        const vertexShader = this.getVolumeVertexShader();
        const fragmentShader = this.getVolumeFragmentShader();
        
        // Create material
        const material = new THREE.ShaderMaterial({
            uniforms: {
                u_data: { value: texture3D },
                u_size: { value: new THREE.Vector3(256, 256, 256) },
                u_clim: { value: new THREE.Vector2(0.0, 1.0) },
                u_renderstyle: { value: 0 }, // 0: volume, 1: isosurface
                u_renderthreshold: { value: 0.5 },
                u_opacity: { value: 1.0 },
                u_time: { value: 0.0 },
                u_colormap: { value: this.createColorTexture() }
            },
            vertexShader: vertexShader,
            fragmentShader: fragmentShader,
            side: THREE.BackSide,
            transparent: true
        });
        
        return material;
    }
    
    getVolumeVertexShader() {
        return `
            varying vec3 vOrigin;
            varying vec3 vDirection;
            
            void main() {
                vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);
                vOrigin = vec3(inverse(modelViewMatrix) * vec4(0.0, 0.0, 0.0, 1.0)).xyz;
                vDirection = position - vOrigin;
                gl_Position = projectionMatrix * mvPosition;
            }
        `;
    }
    
    getVolumeFragmentShader() {
        return `
            precision highp float;
            precision highp sampler3D;
            
            uniform sampler3D u_data;
            uniform sampler2D u_colormap;
            uniform vec3 u_size;
            uniform vec2 u_clim;
            uniform int u_renderstyle;
            uniform float u_renderthreshold;
            uniform float u_opacity;
            uniform float u_time;
            
            varying vec3 vOrigin;
            varying vec3 vDirection;
            
            vec2 hitBox(vec3 orig, vec3 dir) {
                vec3 box_min = vec3(-0.5);
                vec3 box_max = vec3(0.5);
                vec3 inv_dir = 1.0 / dir;
                vec3 tmin_tmp = (box_min - orig) * inv_dir;
                vec3 tmax_tmp = (box_max - orig) * inv_dir;
                vec3 tmin = min(tmin_tmp, tmax_tmp);
                vec3 tmax = max(tmin_tmp, tmax_tmp);
                float t0 = max(tmin.x, max(tmin.y, tmin.z));
                float t1 = min(tmax.x, min(tmax.y, tmax.z));
                return vec2(t0, t1);
            }
            
            float sampleVolume(vec3 texCoord) {
                return texture(u_data, texCoord + 0.5).r;
            }
            
            vec4 applyColormap(float intensity) {
                float normalizedIntensity = (intensity - u_clim.x) / (u_clim.y - u_clim.x);
                normalizedIntensity = clamp(normalizedIntensity, 0.0, 1.0);
                return texture2D(u_colormap, vec2(normalizedIntensity, 0.5));
            }
            
            void main() {
                vec3 rayDir = normalize(vDirection);
                vec2 bounds = hitBox(vOrigin, rayDir);
                
                if (bounds.x > bounds.y) discard;
                
                bounds.x = max(bounds.x, 0.0);
                
                vec3 p = vOrigin + bounds.x * rayDir;
                vec3 inc = 1.0 / abs(rayDir);
                float delta = min(inc.x, min(inc.y, inc.z)) / u_size.x;
                
                vec4 color = vec4(0.0);
                
                for (float t = bounds.x; t < bounds.y; t += delta) {
                    vec3 pos = vOrigin + t * rayDir;
                    float intensity = sampleVolume(pos);
                    
                    if (u_renderstyle == 0) {
                        // Volume rendering
                        vec4 src = applyColormap(intensity);
                        src.a *= u_opacity * delta * 100.0;
                        color.rgb = color.rgb + (1.0 - color.a) * src.a * src.rgb;
                        color.a = color.a + (1.0 - color.a) * src.a;
                        
                        if (color.a >= 0.95) break;
                    } else {
                        // Isosurface rendering
                        if (intensity > u_renderthreshold) {
                            color = applyColormap(intensity);
                            color.a = u_opacity;
                            break;
                        }
                    }
                }
                
                gl_FragColor = color;
            }
        `;
    }
    
    createColorTexture() {
        // Create a simple temperature colormap
        const width = 256;
        const data = new Uint8Array(width * 4);
        
        for (let i = 0; i < width; i++) {
            const t = i / (width - 1);
            // Blue to red gradient for temperature
            data[i * 4] = Math.floor(255 * t); // R
            data[i * 4 + 1] = Math.floor(255 * (1 - Math.abs(t - 0.5) * 2)); // G
            data[i * 4 + 2] = Math.floor(255 * (1 - t)); // B
            data[i * 4 + 3] = 255; // A
        }
        
        const texture = new THREE.DataTexture(data, width, 1, THREE.RGBAFormat);
        texture.needsUpdate = true;
        return texture;
    }
    
    updateUniforms() {
        if (!this.material) return;
        
        this.material.uniforms.u_clim.value.set(0.0, 1.0);
        this.material.uniforms.u_renderstyle.value = this.config.renderStyle === 'volume' ? 0 : 1;
        this.material.uniforms.u_renderthreshold.value = this.config.threshold;
        this.material.uniforms.u_opacity.value = this.config.opacity;
    }
    
    updateColormap() {
        if (!this.material) return;
        // Update colormap based on selected type
        this.material.uniforms.u_colormap.value = this.createColorTexture();
    }
    
    updateTime() {
        if (!this.material) return;
        this.material.uniforms.u_time.value = this.config.currentTime;
    }
    
    toggleAnimation() {
        this.isAnimating = !this.isAnimating;
    }
    
    setupEventListeners() {
        window.addEventListener('resize', () => this.onWindowResize());
    }
    
    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }
    
    animate() {
        requestAnimationFrame(() => this.animate());
        
        const deltaTime = this.clock.getDelta();
        
        // Update animation time
        if (this.isAnimating) {
            this.config.currentTime += deltaTime * this.config.animationSpeed;
            if (this.config.currentTime > 24) this.config.currentTime = 0;
            this.updateTime();
        }
        
        // Update controls
        this.controls.update();
        
        // Render
        this.renderer.render(this.scene, this.camera);
    }
}

// Weather Data Handler Class
class WeatherDataHandler {
    constructor() {
        this.data = null;
        this.apiKey = null;
        this.sessionId = null;
    }
    
    async fetchLatest(apiKey = null, sessionId = null) {
        this.apiKey = apiKey;
        this.sessionId = sessionId;
        
        try {
            // Simulate weather data fetching
            // In real implementation, this would fetch from MapTiler Weather API
            this.data = this.generateMockWeatherData();
            return this.data;
        } catch (error) {
            console.error('Failed to fetch weather data:', error);
            throw error;
        }
    }
    
    generateMockWeatherData() {
        const width = 64;
        const height = 64;
        const depth = 32;
        const size = width * height * depth;
        const data = new Float32Array(size);
        
        // Generate mock 3D weather data
        for (let z = 0; z < depth; z++) {
            for (let y = 0; y < height; y++) {
                for (let x = 0; x < width; x++) {
                    const index = z * width * height + y * width + x;
                    
                    // Create some interesting 3D patterns
                    const nx = (x / width) * 2 - 1;
                    const ny = (y / height) * 2 - 1;
                    const nz = (z / depth) * 2 - 1;
                    
                    const distance = Math.sqrt(nx * nx + ny * ny + nz * nz);
                    const noise = Math.sin(nx * 4) * Math.cos(ny * 4) * Math.sin(nz * 2);
                    
                    data[index] = Math.max(0, 1 - distance + noise * 0.3);
                }
            }
        }
        
        return {
            width,
            height,
            depth,
            data,
            metadata: {
                weather_variable: {
                    decoding: {
                        channels: 'r',
                        min: 0,
                        max: 1
                    }
                }
            }
        };
    }
    
    getTemperatureData() {
        return this.data;
    }
    
    getWindData() {
        return this.data;
    }
    
    hasData() {
        return this.data !== null;
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new WeatherDemo3D();
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { WeatherDemo3D, WeatherDataHandler };
}
