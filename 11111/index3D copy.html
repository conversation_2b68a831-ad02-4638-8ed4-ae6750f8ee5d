<!DOCTYPE html>
<html lang="en">
<head>
	<title>3D Cloud Volume Renderer (Large Box)</title>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">
	<style>
		body { color: #fff; font-family: Monospace; font-size: 13px; text-align: center; background-color: #050505; margin: 0; overflow: hidden; }
		#info { position: absolute; top: 10px; width: 100%; text-align: center; z-index: 100; display: block; color: white; }
		a { color: skyblue; }
	</style>
</head>

<body>
	<div id="info">
		3D Cloud Volume Renderer - Kích thước lớn
	</div>

	<script type="importmap">
		{
			"imports": {
				"three": "./build/three.module.js",
				"three/addons/": "./jsm/"
			}
		}
	</script>

	<script type="module">
		import * as THREE from 'three';
		import { GUI } from 'three/addons/libs/lil-gui.module.min.js';
		import { OrbitControls } from 'three/addons/controls/OrbitControls.js';

		let renderer, scene, camera, controls, material, mesh, clock;
		let volumeTextures = [];
        let texturesLoaded = false;

		const config = {
			renderSteps: 64, opacityFactor: 15.0, decodeMin: 0.0,
			decodeMax: 64.0, gradientSmooth: true, animationSpeed: 0.2,
            time: 0, play: true,
            clipMinX: 0.0, clipMaxX: 1.0, clipMinY: 0.0, clipMaxY: 1.0,
            clipMinZ: 0.0, clipMaxZ: 1.0,
		};

        const TILE_CONFIG = { columns: 3, rows: 4 };
        const DATA_URLS = [
            'data/090000.jpg', 'data/090600.jpg', 'data/091200.jpg', 'data/091800.jpg',
            'data/092400.jpg', 'data/093000.jpg', 'data/093600.jpg', 'data/094200.jpg',
            'data/094800.jpg', 'data/095400.jpg',
        ];

		init();

		function init() {
			scene = new THREE.Scene();
            clock = new THREE.Clock();
			renderer = new THREE.WebGLRenderer({ antialias: true });
			renderer.setPixelRatio(window.devicePixelRatio);
			renderer.setSize(window.innerWidth, window.innerHeight);
			document.body.appendChild(renderer.domElement);
			camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 0.1, 2000);
			// Kéo camera ra xa hơn để thấy được khối hộp lớn
            camera.position.set(0, 400, 700);
			controls = new OrbitControls(camera, renderer.domElement);
			controls.enablePan = false;
            // Hướng camera vào giữa khối hộp
            controls.target.set(0, 250, 0);
			controls.update();
			scene.add(new THREE.AmbientLight(0xcccccc, 0.5));
			const dirLight = new THREE.DirectionalLight(0xffffff, 0.8);
            dirLight.position.set(0.5, 1, 0.5);
			scene.add(dirLight);
            const floor = new THREE.Mesh( new THREE.PlaneGeometry(500, 500), new THREE.MeshStandardMaterial({ color: 0x333333, roughness: 0.8, metalness: 0.2 }) );
            floor.rotation.x = -Math.PI / 2;
            scene.add(floor);
            const grid = new THREE.GridHelper(500, 10, 0x555555, 0x555555); // Giảm số đường lưới cho đỡ rối
            grid.material.opacity = 0.5;
            grid.material.transparent = true;
            scene.add(grid);

            loadAllVolumes().then(([firstTexture, volumeSize]) => {
                const colorStops = [
                    { value: 0, color: [0, 0, 0, 0] }, { value: 5, color: [56, 0, 112, 20] }, { value: 10, color: [6, 0, 240, 40] },
                    { value: 15, color: [0, 108, 192, 60] }, { value: 20, color: [0, 160, 0, 80] }, { value: 25, color: [0, 190, 0, 110] },
                    { value: 30, color: [50, 216, 0, 130] }, { value: 35, color: [220, 220, 0, 150] }, { value: 40, color: [255, 176, 0, 170] },
                    { value: 45, color: [255, 132, 0, 190] }, { value: 50, color: [255, 50, 0, 210] }, { value: 55, color: [170, 0, 0, 240] },
                    { value: 60, color: [255, 255, 255, 255] },
                ];
                
                const MAX_STOPS_IN_SHADER = 16;
                let stopValues = colorStops.map(s => s.value);
                let stopColors = colorStops.map(s => new THREE.Vector4(s.color[0]/255, s.color[1]/255, s.color[2]/255, s.color[3]/255));
                while (stopValues.length < MAX_STOPS_IN_SHADER) {
                    stopValues.push(0);
                    stopColors.push(new THREE.Vector4(0, 0, 0, 0));
                }

				material = new THREE.ShaderMaterial({
					uniforms: {
						u_data_0: { value: firstTexture }, u_data_1: { value: firstTexture }, u_interpolation: { value: 0.0 },
						u_size: { value: new THREE.Vector3(volumeSize.width, volumeSize.height, volumeSize.depth) },
						u_render_steps: { value: config.renderSteps }, u_opacity_factor: { value: config.opacityFactor },
						u_decode_min: { value: config.decodeMin }, u_decode_max: { value: config.decodeMax },
                        u_num_color_stops: { value: colorStops.length }, u_color_stops_values: { value: stopValues },
                        u_color_stops_colors: { value: stopColors }, u_smooth_gradient: { value: config.gradientSmooth },
                        u_camera_pos: { value: camera.position },
                        u_modelMatrix: { value: new THREE.Matrix4() },
                        u_clip_box_min: { value: new THREE.Vector3(config.clipMinX, config.clipMinY, config.clipMinZ) },
                        u_clip_box_max: { value: new THREE.Vector3(config.clipMaxX, config.clipMaxY, config.clipMaxZ) },
					},
					vertexShader: VERTEX_SHADER,
					fragmentShader: FRAGMENT_SHADER,
					side: THREE.BackSide,
                    transparent: true,
                    depthWrite: false,
				});

                // *** FIX 1: Tăng kích thước hộp bằng sàn ***
                const boxSize = 500;
				const geometry = new THREE.BoxGeometry(boxSize, boxSize, boxSize);
                // Dịch chuyển lên để đáy hộp nằm trên sàn
				geometry.translate(0, boxSize / 2, 0);
				
                mesh = new THREE.Mesh(geometry, material);
				scene.add(mesh);
                
                texturesLoaded = true;
                setupGUI();
                animate();
			}).catch(error => {
                const infoDiv = document.getElementById('info');
                infoDiv.innerHTML = `Lỗi: Không thể tải dữ liệu. <br/> Mở Console (F12) để xem chi tiết.`;
                console.error("Lỗi khi tải dữ liệu volume:", error);
            });
			window.addEventListener('resize', onWindowResize);
		}
        
        function loadVolumeFromJPG(url, tileConfig) {
             return new Promise((resolve, reject) => {
                const loader = new THREE.TextureLoader();
                loader.load(url, (imageTexture) => {
                    const image = imageTexture.image;
                    const canvas = document.createElement('canvas');
                    canvas.width = image.width; canvas.height = image.height;
                    const context = canvas.getContext('2d', { willReadFrequently: true });
                    context.drawImage(image, 0, 0);
                    const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
                    const sliceWidth = canvas.width / tileConfig.columns;
                    const sliceHeight = canvas.height / tileConfig.rows;
                    const numSlices = tileConfig.columns * tileConfig.rows;
                    const volumeData = new Float32Array(sliceWidth * sliceHeight * numSlices);
                    for (let d = 0; d < numSlices; d++) {
                        const tileX = d % tileConfig.columns;
                        const tileY = Math.floor(d / tileConfig.columns);
                        for (let y = 0; y < sliceHeight; y++) {
                            for (let x = 0; x < sliceWidth; x++) {
                                const srcX = tileX * sliceWidth + x;
                                const srcY = tileY * sliceHeight + y;
                                const srcIndex = (srcY * canvas.width + srcX) * 4;
                                const destIndex = (d * sliceWidth * sliceHeight) + (y * sliceWidth) + x;
                                volumeData[destIndex] = imageData.data[srcIndex] / 255.0;
                            }
                        }
                    }
                    const texture = new THREE.Data3DTexture(volumeData, sliceWidth, sliceHeight, numSlices);
                    texture.format = THREE.RedFormat; texture.type = THREE.FloatType;
                    texture.minFilter = THREE.LinearFilter; texture.magFilter = THREE.LinearFilter;
                    texture.unpackAlignment = 1; texture.needsUpdate = true;
                    resolve({ texture, size: { width: sliceWidth, height: sliceHeight, depth: numSlices } });
                }, undefined, (err) => reject(`Could not load image: ${url}`));
            });
        }
        
        async function loadAllVolumes() {
            const promises = DATA_URLS.map(url => loadVolumeFromJPG(url, TILE_CONFIG));
            const results = await Promise.all(promises);
            volumeTextures = results.map(r => r.texture);
            return [volumeTextures[0], results[0].size];
        }

		function setupGUI() {
			const gui = new GUI();
            const renderFolder = gui.addFolder('Rendering');
            renderFolder.add(config, 'renderSteps', 8, 128, 1).onChange(updateUniforms);
			renderFolder.add(config, 'opacityFactor', 0, 50, 0.1).name('Opacity').onChange(updateUniforms);
            const dataFolder = gui.addFolder('Data Decoding');
            dataFolder.add(config, 'decodeMin', 0, 100, 1).name('Decode Min').onChange(updateUniforms);
            dataFolder.add(config, 'decodeMax', 0, 100, 1).name('Decode Max').onChange(updateUniforms);
            const clipFolder = gui.addFolder('Clipping Box');
            clipFolder.add(config, 'clipMinX', 0.0, 1.0, 0.01).name('Min X').onChange(updateUniforms);
            clipFolder.add(config, 'clipMaxX', 0.0, 1.0, 0.01).name('Max X').onChange(updateUniforms);
            clipFolder.add(config, 'clipMinY', 0.0, 1.0, 0.01).name('Min Y').onChange(updateUniforms);
            clipFolder.add(config, 'clipMaxY', 0.0, 1.0, 0.01).name('Max Y').onChange(updateUniforms);
            clipFolder.add(config, 'clipMinZ', 0.0, 1.0, 0.01).name('Min Z').onChange(updateUniforms);
            clipFolder.add(config, 'clipMaxZ', 0.0, 1.0, 0.01).name('Max Z').onChange(updateUniforms);
            const animFolder = gui.addFolder('Animation');
            animFolder.add(config, 'play').name('Play/Pause');
            animFolder.add(config, 'animationSpeed', 0, 1, 0.01).name('Speed');
            animFolder.add(config, 'time', 0, DATA_URLS.length - 1, 0.01).name('Timeline').listen();
		}
        
		function updateUniforms() {
            if (!material) return;
			material.uniforms.u_render_steps.value = config.renderSteps;
			material.uniforms.u_opacity_factor.value = config.opacityFactor;
			material.uniforms.u_decode_min.value = config.decodeMin;
            material.uniforms.u_decode_max.value = config.decodeMax;
            material.uniforms.u_clip_box_min.value.set(config.clipMinX, config.clipMinY, config.clipMinZ);
            material.uniforms.u_clip_box_max.value.set(config.clipMaxX, config.clipMaxY, config.clipMaxZ);
		}

		function onWindowResize() {
			camera.aspect = window.innerWidth / window.innerHeight;
			camera.updateProjectionMatrix();
			renderer.setSize(window.innerWidth, window.innerHeight);
		}

		function animate() {
			requestAnimationFrame(animate);
            if (texturesLoaded && config.play) {
                const delta = clock.getDelta();
                config.time += delta * config.animationSpeed * (DATA_URLS.length);
                if (config.time > DATA_URLS.length - 1) config.time = 0;
            }
            if (texturesLoaded && material) {
                const time = config.time;
                const index0 = Math.floor(time) % volumeTextures.length;
                const index1 = (index0 + 1) % volumeTextures.length;
                const interpolation = time - Math.floor(time);
                material.uniforms.u_data_0.value = volumeTextures[index0];
                material.uniforms.u_data_1.value = volumeTextures[index1];
                material.uniforms.u_interpolation.value = interpolation;
                material.uniforms.u_camera_pos.value.copy(camera.position);
                // *** FIX 2: Cập nhật ma trận modelMatrix mỗi frame ***
                material.uniforms.u_modelMatrix.value.copy(mesh.matrixWorld);
            }
			controls.update();
			renderer.render(scene, camera);
		}

        const VERTEX_SHADER = /* glsl */`
            // Sử dụng các biến mặc định của Three.js để đơn giản hóa
            varying vec3 v_local_position;
            void main() {
                v_local_position = position;
                gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
            }
        `;

        const FRAGMENT_SHADER = /* glsl */`
            precision highp float;
            precision highp sampler3D;
            
            // Lấy các uniform mặc định + uniform tùy chỉnh
            uniform vec3 u_camera_pos;
            uniform mat4 u_modelMatrix; // Vẫn cần uniform này
            uniform sampler3D u_data_0;
            uniform sampler3D u_data_1;
            uniform float u_interpolation;
            uniform int u_render_steps;
            uniform float u_opacity_factor;
            uniform float u_decode_min;
            uniform float u_decode_max;
            uniform vec3 u_clip_box_min;
            uniform vec3 u_clip_box_max;
            
            const int MAX_STOPS = 16;
            uniform int u_num_color_stops;
            uniform float u_color_stops_values[MAX_STOPS];
            uniform vec4 u_color_stops_colors[MAX_STOPS];
            uniform bool u_smooth_gradient;
            
            varying vec3 v_local_position;

            vec2 hitBox(vec3 orig, vec3 dir) {
                vec3 box_min = vec3(-0.5); vec3 box_max = vec3(0.5);
                vec3 inv_dir = 1.0 / dir;
                vec3 tmin_tmp = (box_min - orig) * inv_dir;
                vec3 tmax_tmp = (box_max - orig) * inv_dir;
                vec3 tmin = min(tmin_tmp, tmax_tmp);
                vec3 tmax = max(tmin_tmp, tmax_tmp);
                float t0 = max(tmin.x, max(tmin.y, tmin.z));
                float t1 = min(tmax.x, min(tmax.y, tmax.z));
                return vec2(t0, t1);
            }

            vec4 getColorFromGradient(float value) {
                if (value <= u_color_stops_values[0]) return u_color_stops_colors[0];
                for (int i = 1; i < MAX_STOPS; i++) {
                    if (i >= u_num_color_stops) break;
                    if (value <= u_color_stops_values[i]) {
                        float t = (value - u_color_stops_values[i-1]) / (u_color_stops_values[i] - u_color_stops_values[i-1]);
                        if (u_smooth_gradient) return mix(u_color_stops_colors[i-1], u_color_stops_colors[i], t);
                        else return u_color_stops_colors[i-1];
                    }
                }
                return u_color_stops_colors[u_num_color_stops - 1];
            }

            void main() {
                vec3 rayOrigin = (inverse(u_modelMatrix) * vec4(u_camera_pos, 1.0)).xyz;
                vec3 rayDir = normalize(v_local_position - rayOrigin);
                vec2 t_hit = hitBox(rayOrigin, rayDir);
                if (t_hit.x > t_hit.y) discard;
                t_hit.x = max(t_hit.x, 0.0);
                vec4 accumulatedColor = vec4(0.0);
                float stepSize = (t_hit.y - t_hit.x) / float(u_render_steps);

                for (int i = 0; i < u_render_steps; i++) {
                    vec3 p = rayOrigin + (t_hit.x + float(i) * stepSize) * rayDir;
                    vec3 texPos = p + 0.5;
                    if (texPos.x < u_clip_box_min.x || texPos.x > u_clip_box_max.x ||
                        texPos.y < u_clip_box_min.y || texPos.y > u_clip_box_max.y ||
                        texPos.z < u_clip_box_min.z || texPos.z > u_clip_box_max.z) {
                        continue;
                    }
                    float value0 = texture(u_data_0, texPos).r;
                    float value1 = texture(u_data_1, texPos).r;
                    float rawValue = mix(value0, value1, u_interpolation);
                    float decodedValue = u_decode_min + rawValue * (u_decode_max - u_decode_min);
                    vec4 sampleColor = getColorFromGradient(decodedValue);
                    sampleColor.a *= u_opacity_factor / float(u_render_steps);
                    accumulatedColor.rgb += sampleColor.rgb * sampleColor.a * (1.0 - accumulatedColor.a);
                    accumulatedColor.a += sampleColor.a * (1.0 - accumulatedColor.a);
                    if (accumulatedColor.a > 0.99) break;
                }
                gl_FragColor = accumulatedColor;
            }
        `;
	</script>
</body>
</html>