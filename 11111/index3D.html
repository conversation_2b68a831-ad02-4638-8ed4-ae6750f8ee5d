<!DOCTYPE html>
<html lang="vi">
<head>
	<title>Mây 3D từ ảnh Local (Sửa lỗi kích thước Texture)</title>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">
	<style>
		body {
			margin: 0;
			background-color: #050505;
			color: #fff;
			font-family: Monospace;
			font-size: 13px;
			line-height: 24px;
			overscroll-behavior: none;
		}
		a {
			color: #ff0;
			text-decoration: none;
		}
		#info {
			position: absolute;
			top: 10px;
			width: 100%;
			text-align: center;
			z-index: 100;
			display:block;
			color: white;
		}
	</style>
</head>

<body>
	<div id="info">
		Mô phỏng mây 3D từ ảnh local - Dùng chuột để xoay, zoom
	</div>

	<!-- Import map -->
	<script type="importmap">
		{
			"imports": {
				"three": "./build/three.module.js",
				"three/addons/": "./jsm/"
			}
		}
	</script>

	<script type="module">
		import * as THREE from 'three';
		import { OrbitControls } from 'three/addons/controls/OrbitControls.js';

		let renderer, scene, camera, controls, material;

        // --- Shaders (Giữ nguyên như phiên bản trước) ---
		const vertexShader = `
			varying vec3 vOrigin;
			varying vec3 vDirection;

			void main() {
				vOrigin = vec3( inverse( modelViewMatrix ) * vec4( 0, 0, 0, 1 ) ).xyz;
				vDirection = position - vOrigin;
				gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );
			}
		`;

		const fragmentShader = `
			precision highp float;
			precision highp sampler3D;

			uniform vec3 u_size;
			uniform vec2 u_clim;
			uniform sampler3D u_data;

			varying vec3 vOrigin;
			varying vec3 vDirection;
			
			vec2 hitBox( vec3 orig, vec3 dir ) {
				const vec3 box_min = vec3( - 0.5 );
				const vec3 box_max = vec3( 0.5 );
				vec3 inv_dir = 1.0 / dir;
				vec3 tmin_tmp = ( box_min - orig ) * inv_dir;
				vec3 tmax_tmp = ( box_max - orig ) * inv_dir;
				vec3 tmin = min( tmin_tmp, tmax_tmp );
				vec3 tmax = max( tmin_tmp, tmax_tmp );
				float t0 = max( tmin.x, max( tmin.y, tmin.z ) );
				float t1 = min( tmax.x, min( tmax.y, tmax.z ) );
				return vec2( t0, t1 );
			}

			float sample1( vec3 p ) {
				return texture( u_data, p ).r;
			}
			
			vec4 transferFunction( float value ) {
                float v = (value - u_clim[0]) / (u_clim[1] - u_clim[0]);
				if (v < 0.2) return vec4(0.0);
				float alpha = pow(v, 2.5) * 0.08;
				float intensity = v * 1.2;
				return vec4(intensity, intensity, intensity, alpha);
			}

			void main() {
				vec3 rayDir = normalize( vDirection );
				vec2 bounds = hitBox( vOrigin, rayDir );

				if ( bounds.x > bounds.y ) discard;
				bounds.x = max( bounds.x, 0.0 );

				vec3 p = vOrigin + bounds.x * rayDir;
				vec3 inc = 1.0 / abs( rayDir );
				float delta = min( inc.x, min( inc.y, inc.z ) );
				delta /= 150.0; 

				vec4 accum_color = vec4( 0.0 );

				for ( float t = bounds.x; t < bounds.y; t += delta ) {
					float density = sample1( p + 0.5 );
					vec4 color = transferFunction(density);
					if (color.a > 0.0) {
						accum_color.rgb += (1.0 - accum_color.a) * color.a * color.rgb;
						accum_color.a += (1.0 - accum_color.a) * color.a;
					}
					if ( accum_color.a >= 0.95 ) break;
					p += rayDir * delta;
				}

				gl_FragColor = accum_color;
				if ( gl_FragColor.a == 0.0 ) discard;
			}
		`;

		init();

		async function init() {
			scene = new THREE.Scene();

			// Renderer
			renderer = new THREE.WebGLRenderer({ antialias: true });
			renderer.setPixelRatio( window.devicePixelRatio );
			renderer.setSize( window.innerWidth, window.innerHeight );
			document.body.appendChild( renderer.domElement );

			// Camera
			camera = new THREE.PerspectiveCamera( 60, window.innerWidth / window.innerHeight, 0.1, 1000 );
			camera.position.set( 0, -200, 150 );
			camera.up.set( 0, 0, 1 );

			// Controls
			controls = new OrbitControls( camera, renderer.domElement );
			controls.enablePan = true;
			controls.target.set( 0, 0, 75 );
			controls.minDistance = 100;
			controls.maxDistance = 500;
			controls.update();
			
			// Floor
			const planeGeometry = new THREE.PlaneGeometry( 1000, 1000 );
			const planeMaterial = new THREE.MeshBasicMaterial( { color: 0x050505, side: THREE.DoubleSide } );
			const floor = new THREE.Mesh( planeGeometry, planeMaterial );
			scene.add( floor );
            
			// Light
			const ambientLight = new THREE.AmbientLight(0xffffff, 0.7);
			scene.add(ambientLight);

			// --- Tải ảnh và tạo Texture 3D ---
			console.log("Bắt đầu tải ảnh mây...");

			const imageFilenames = [
				'090000.jpg', '090600.jpg', '091200.jpg', '091800.jpg', '092400.jpg', 
				'093000.jpg', '093600.jpg', '094200.jpg', '094800.jpg', '095400.jpg'
			];

			const textureLoader = new THREE.TextureLoader();
			const promises = imageFilenames.map(filename => textureLoader.loadAsync(`./data/${filename}`));

			const textures = await Promise.all(promises);
			console.log("Tải ảnh thành công.");

			// --- Xử lý ảnh thành khối dữ liệu 3D (ĐÃ SỬA) ---
			console.log("Đang xử lý ảnh thành khối dữ liệu 3D...");

			const originalImage = textures[0].image;
			const MAX_TEXTURE_DIMENSION = 1024; // Đặt giới hạn kích thước, 1024 là mức an toàn

			let width = originalImage.width;
			let height = originalImage.height;
			const aspect = width / height;

			// **(THAY ĐỔI)**: Kiểm tra và tính toán lại kích thước nếu cần
			if (width > MAX_TEXTURE_DIMENSION || height > MAX_TEXTURE_DIMENSION) {
				console.warn(`Ảnh gốc quá lớn (${width}x${height}). Đang giảm kích thước về giới hạn ${MAX_TEXTURE_DIMENSION}px.`);
				if (width > height) {
					width = MAX_TEXTURE_DIMENSION;
					height = Math.floor(width / aspect);
				} else {
					height = MAX_TEXTURE_DIMENSION;
					width = Math.floor(height * aspect);
				}
				console.log(`Kích thước mới: ${width}x${height}`);
			}
			
			const depth = textures.length;
			const volumeData = new Uint8Array(width * height * depth);
			
			const canvas = document.createElement('canvas');
			canvas.width = width;
			canvas.height = height;
			const context = canvas.getContext('2d', { willReadFrequently: true });

			textures.forEach((texture, z_index) => {
				// **(THAY ĐỔI)**: Vẽ ảnh với kích thước mới đã được tính toán
				context.drawImage(texture.image, 0, 0, width, height); 
				const imageData = context.getImageData(0, 0, width, height);
				const data = imageData.data;

				for (let i = 0; i < data.length; i += 4) {
					const volumeIndex = (i / 4) + (z_index * width * height);
					volumeData[volumeIndex] = data[i]; // Lấy kênh R
				}
			});

			console.log("Xử lý dữ liệu 3D hoàn tất.");

			// Tạo Texture 3D
			const texture = new THREE.Data3DTexture(volumeData, width, height, depth);
			texture.format = THREE.RedFormat;
			texture.type = THREE.UnsignedByteType;
			texture.minFilter = texture.magFilter = THREE.LinearFilter;
			texture.unpackAlignment = 1;
			texture.needsUpdate = true;

			// --- Tạo Material và Mesh cho Volume ---
			const uniforms = {
				'u_data': { value: texture },
				'u_size': { value: new THREE.Vector3( width, height, depth ) },
				'u_clim': { value: new THREE.Vector2( 0.0, 1.0 ) }
			};

			material = new THREE.ShaderMaterial({
				uniforms: uniforms,
				vertexShader: vertexShader,
				fragmentShader: fragmentShader,
				side: THREE.BackSide, 
				transparent: true,
				depthWrite: false
			});

			// Tạo khối hộp để render
            // **(THAY ĐỔI)**: Scale kích thước của khối hộp cho phù hợp với tỉ lệ ảnh mới
			const boxScale = 150 / Math.max(width, height); 
			const geometry = new THREE.BoxGeometry(
				width * boxScale,
				height * boxScale,
				depth * 10 * boxScale // Giữ tỉ lệ chiều sâu
			); 
			geometry.translate( 0, 0, (depth * 10 * boxScale) / 2 );

			const mesh = new THREE.Mesh( geometry, material );
			scene.add( mesh );

			window.addEventListener( 'resize', onWindowResize );
			
			animate();
		}

		function onWindowResize() {
			camera.aspect = window.innerWidth / window.innerHeight;
			camera.updateProjectionMatrix();
			renderer.setSize( window.innerWidth, window.innerHeight );
		}

		function animate() {
			requestAnimationFrame( animate );
			controls.update();
			renderer.render( scene, camera );
		}
	</script>
</body>
</html>