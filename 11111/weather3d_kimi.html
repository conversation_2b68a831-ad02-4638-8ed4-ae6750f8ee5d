<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8" />
<title>Local Weather-3D Demo (MapTiler clone)</title>
<style>
    body{margin:0;overflow:hidden;background:#000;color:#fff;font-family:system-ui;}
    canvas{display:block}
    #info{position:absolute;left:10px;top:10px;font-size:.8em;pointer-events:none}
    #legend{
        position:fixed;left:10px;bottom:10px;
        background:#00000099;border-radius:4px;padding:4px 6px;
        font-size:.7em;white-space:nowrap
    }
    #legend td{width:40px;height:18px;text-align:center;border:1px solid #222;color:#fff}
</style>
</head>
<body>
<div id="info">Local Weather-3D  |  FPS:<span id="fps">--</span></div>

<!-- MapTiler-style color legend -->
<table id="legend">
<tr>
    <td style="background:#380070">5</td>
    <td style="background:#0600f0">10</td>
    <td style="background:#006cc0">15</td>
    <td style="background:#00a000">20</td>
    <td style="background:#00be00">25</td>
    <td style="background:#32d800">30</td>
    <td style="background:#dcdc00">35</td>
    <td style="background:#ffb000">40</td>
    <td style="background:#ff8400">45</td>
    <td style="background:#ff3200">50</td>
    <td style="background:#aa0000">55</td>
    <td style="background:#fff;color:#000">60</td>
</tr>
</table>

<!-- three.js & helpers -->
<script type="importmap">
{
    "imports": {
        "three": "./build/three.module.js",
        "three/addons/": "./jsm/"
    }
}
</script>

<script type="module">
import * as THREE from 'three';
import { OrbitControls } from 'three/addons/OrbitControls.js';
import { GUI } from 'three/addons/lil-gui.module.min.js';

const TILES_W = 4, TILES_H = 3;   // 4×3 = 12 elevation layers
const TEX_W   = 512, TEX_H = 512; // assumed source JPG size
const VOL_W   = 64, VOL_H = 64, VOL_D = 12;

/* ------------------------------------------
   1.  Scene / Camera / Renderer
-------------------------------------------*/
const scene    = new THREE.Scene();
scene.background = new THREE.Color(0x000000);
scene.fog = new THREE.Fog(0x000000, 50, 300);

const camera   = new THREE.PerspectiveCamera(60, innerWidth/innerHeight, .1, 1000);
camera.position.set(0, 30, 60);

const renderer = new THREE.WebGLRenderer({antialias:true});
renderer.setPixelRatio(devicePixelRatio);
renderer.setSize(innerWidth, innerHeight);
document.body.appendChild(renderer.domElement);

const controls = new OrbitControls(camera, renderer.domElement);
controls.enableDamping = true;
controls.dampingFactor = 0.05;
controls.maxPolarAngle = Math.PI / 2.1; // prevent going below floor

/* ------------------------------------------
   2.  Black floor plane
-------------------------------------------*/
const floor = new THREE.Mesh(
    new THREE.PlaneGeometry(200, 200),
    new THREE.MeshBasicMaterial({color:0x000000})
);
floor.rotation.x = -Math.PI/2;
floor.position.y = -15;
scene.add(floor);

/* ------------------------------------------
   3.  Load 10 JPG frames
-------------------------------------------*/
const frames = [
    'data/090000.jpg','data/090600.jpg','data/091200.jpg','data/091800.jpg',
    'data/092400.jpg','data/093000.jpg','data/093600.jpg','data/094200.jpg',
    'data/094800.jpg','data/095400.jpg'
];
const loader = new THREE.TextureLoader();
const textures = await Promise.all(frames.map(f=>loader.loadAsync(f)));

/* ------------------------------------------
   4.  Build 3-D volume from 2-D frame
-------------------------------------------*/
let frameIndex = 0;
let volumeTex  = null;

function rebuildVolume(index){
    const tex = textures[index];
    const canvas = document.createElement('canvas');
    const ctx    = canvas.getContext('2d');
    canvas.width = TEX_W; canvas.height = TEX_H;
    ctx.drawImage(tex.image, 0, 0);
    const imgData = ctx.getImageData(0,0,TEX_W,TEX_H).data;

    // slice size
    const tileW = TEX_W / TILES_W;
    const tileH = TEX_H / TILES_H;

    const data = new Float32Array(VOL_W * VOL_H * VOL_D);
    for (let z=0; z<VOL_D; ++z){
        // which tile?
        const tx = z % TILES_W;
        const ty = Math.floor(z / TILES_W);
        const offX = tx * tileW, offY = ty * tileH;
        for (let y=0; y<VOL_H; ++y){
            for (let x=0; x<VOL_W; ++x){
                // nearest sample
                const ix = Math.floor((x / VOL_W) * tileW) + offX;
                const iy = Math.floor((y / VOL_H) * tileH) + offY;
                const idx = (iy * TEX_W + ix) * 4;
                const v   = imgData[idx]; // R channel = intensity
                data[z*VOL_W*VOL_H + y*VOL_W + x] = v / 255.0;
            }
        }
    }
    if (volumeTex) volumeTex.dispose();
    volumeTex = new THREE.Data3DTexture(data, VOL_W, VOL_H, VOL_D);
    volumeTex.format = THREE.RedFormat;
    volumeTex.type   = THREE.FloatType;
    volumeTex.minFilter = volumeTex.magFilter = THREE.LinearFilter;
    volumeTex.unpackAlignment = 1;
    volumeTex.needsUpdate = true;
    if (volumeMesh) volumeMesh.material.uniforms.u_data.value = volumeTex;
}

/* ------------------------------------------
   5.  Volume shader (MapTiler-like)
-------------------------------------------*/
const vertShader = `
varying vec3 vOrigin;
varying vec3 vDirection;
void main(){
    vOrigin = vec3(inverse(modelMatrix * viewMatrix) * vec4(0,0,0,1));
    vDirection = position - vOrigin;
    gl_Position = projectionMatrix * modelViewMatrix * vec4(position,1.);
}`;

const fragShader = `
precision highp float;
precision highp sampler3D;
uniform sampler3D u_data;
uniform sampler2D u_cmdata;
uniform vec3 u_size;
uniform vec2 u_clim;
uniform float u_threshold;
uniform float u_opacity;
uniform int u_style;   // 0: volume, 1: iso
varying vec3 vOrigin;
varying vec3 vDirection;

vec2 hitBox(vec3 ro, vec3 rd){
    vec3 m = 1./rd, n = m*(ro+.5), k = abs(m)*.5;
    vec3 ta = -n - k, tb = -n + k;
    return vec2(max(ta.x,max(ta.y,ta.z)), min(tb.x,min(tb.y,tb.z)));
}
float sampleVolume(vec3 p){
    return texture(u_data, p+.5).r;
}
vec4 applyColormap(float v){
    v = clamp((v - u_clim.x)/(u_clim.y-u_clim.x),0.,1.);
    return texture2D(u_cmdata, vec2(v, .5));
}
void main(){
    vec3 ro = vOrigin, rd = normalize(vDirection);
    vec2 b = hitBox(ro, rd);
    if (b.x>b.y) discard;
    b.x = max(b.x, 0.);
    vec3 p = ro + b.x*rd;
    vec3 inc = 1./abs(rd);
    float delta = min(inc.x,min(inc.y,inc.z))/u_size.x * 1.5;
    vec4 col = vec4(0);
    for (float t=b.x; t<b.y; t+=delta){
        float v = sampleVolume(p);
        if (u_style==0){          // volume
            vec4 src = applyColormap(v);
            src.a *= u_opacity * delta * 200.;
            col.rgb = col.rgb + (1.-col.a)*src.a*src.rgb;
            col.a   = col.a   + (1.-col.a)*src.a;
            if (col.a>.95) break;
        }else{                    // isosurface
            if (v > u_threshold){
                col = applyColormap(v);
                col.a = u_opacity;
                break;
            }
        }
        p += rd*delta;
    }
    gl_FragColor = col;
}`;

/* ------------------------------------------
   6.  Create volume mesh
-------------------------------------------*/
let volumeMesh;
const cmTex = (()=>{
    const w = 256, data = new Uint8Array(w*4);
    for (let i=0;i<w;i++){
        const t=i/(w-1);
        data[i*4+0]=255*t;
        data[i*4+1]=255*(1.-2.*abs(t-.5));
        data[i*4+2]=255*(1.-t);
        data[i*4+3]=255;
    }
    const tex = new THREE.DataTexture(data,w,1,THREE.RGBAFormat);
    tex.needsUpdate=true;
    return tex;
})();

function createVolumeMesh(){
    const mat = new THREE.ShaderMaterial({
        uniforms:{
            u_data:       {value: volumeTex},
            u_cmdata:     {value: cmTex},
            u_size:       {value: new THREE.Vector3(VOL_W,VOL_H,VOL_D)},
            u_clim:       {value: new THREE.Vector2(0.02,0.85)},
            u_threshold:  {value: 0.3},
            u_opacity:    {value: 1.0},
            u_style:      {value: 0}
        },
        vertexShader: vertShader,
        fragmentShader: fragShader,
        side: THREE.BackSide,
        transparent: true
    });
    const geo = new THREE.BoxGeometry(40,40,20);
    volumeMesh = new THREE.Mesh(geo,mat);
    scene.add(volumeMesh);
}

/* ------------------------------------------
   7.  GUI
-------------------------------------------*/
const gui = new GUI();
const params = {opacity:1, threshold:0.3, speed:1, frame:0, play:false};
gui.add(params,'opacity',0,1).onChange(v=>volumeMesh.material.uniforms.u_opacity.value=v);
gui.add(params,'threshold',0,1).onChange(v=>volumeMesh.material.uniforms.u_threshold.value=v);
gui.add(params,'speed',0,3);
gui.add(params,'frame',{0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9}).onChange(rebuildVolume);
gui.add(params,'play');

/* ------------------------------------------
   8.  Animation loop
-------------------------------------------*/
const fpsEl = document.getElementById('fps');
let lastTime = performance.now();
let fps = 0;
function animate(t){
    requestAnimationFrame(animate);
    controls.update();
    renderer.render(scene,camera);

    // FPS
    fps = Math.round(1000/(t-lastTime));
    fpsEl.textContent = fps;
    lastTime = t;

    // auto-cycle frames
    if (params.play){
        const dt = (t-lastTime)/1000;
        params.frame = (params.frame + dt*params.speed) % 10;
        gui.controllersRecursive().find(c=>c.property==='frame').setValue(Math.floor(params.frame));
    }
}

/* ------------------------------------------
   9.  Startup
-------------------------------------------*/
rebuildVolume(0);
createVolumeMesh();
animate();

/* ------------------------------------------
   10.  Resize
-------------------------------------------*/
addEventListener('resize',()=>{
    camera.aspect = innerWidth/innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(innerWidth,innerHeight);
});
</script>
</body>
</html>