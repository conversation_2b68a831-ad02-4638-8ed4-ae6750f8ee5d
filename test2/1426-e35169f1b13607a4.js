try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="910bbaf5-47db-4a5c-9765-02ed7372ab6f",e._sentryDebugIdIdentifier="sentry-dbid-910bbaf5-47db-4a5c-9765-02ed7372ab6f")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1426],{17187:function(e){var t,n="object"==typeof Reflect?Reflect:null,i=n&&"function"==typeof n.apply?n.apply:function(e,t,n){return Function.prototype.apply.call(e,t,n)};t=n&&"function"==typeof n.ownKeys?n.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var a=Number.isNaN||function(e){return e!=e};function r(){r.init.call(this)}e.exports=r,e.exports.once=function(e,t){return new Promise(function(n,i){var a;function r(n){e.removeListener(t,o),i(n)}function o(){"function"==typeof e.removeListener&&e.removeListener("error",r),n([].slice.call(arguments))}m(e,t,o,{once:!0}),"error"!==t&&(a={once:!0},"function"==typeof e.on&&m(e,"error",r,a))})},r.EventEmitter=r,r.prototype._events=void 0,r.prototype._eventsCount=0,r.prototype._maxListeners=void 0;var o=10;function s(e){if("function"!=typeof e)throw TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function l(e){return void 0===e._maxListeners?r.defaultMaxListeners:e._maxListeners}function c(e,t,n,i){if(s(n),void 0===(r=e._events)?(r=e._events=Object.create(null),e._eventsCount=0):(void 0!==r.newListener&&(e.emit("newListener",t,n.listener?n.listener:n),r=e._events),o=r[t]),void 0===o)o=r[t]=n,++e._eventsCount;else if("function"==typeof o?o=r[t]=i?[n,o]:[o,n]:i?o.unshift(n):o.push(n),(a=l(e))>0&&o.length>a&&!o.warned){o.warned=!0;var a,r,o,c=Error("Possible EventEmitter memory leak detected. "+o.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");c.name="MaxListenersExceededWarning",c.emitter=e,c.type=t,c.count=o.length,console&&console.warn&&console.warn(c)}return e}function d(){if(!this.fired)return(this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0==arguments.length)?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function h(e,t,n){var i={fired:!1,wrapFn:void 0,target:e,type:t,listener:n},a=d.bind(i);return a.listener=n,i.wrapFn=a,a}function f(e,t,n){var i=e._events;if(void 0===i)return[];var a=i[t];return void 0===a?[]:"function"==typeof a?n?[a.listener||a]:[a]:n?function(e){for(var t=Array(e.length),n=0;n<t.length;++n)t[n]=e[n].listener||e[n];return t}(a):u(a,a.length)}function g(e){var t=this._events;if(void 0!==t){var n=t[e];if("function"==typeof n)return 1;if(void 0!==n)return n.length}return 0}function u(e,t){for(var n=Array(t),i=0;i<t;++i)n[i]=e[i];return n}function m(e,t,n,i){if("function"==typeof e.on)i.once?e.once(t,n):e.on(t,n);else if("function"==typeof e.addEventListener)e.addEventListener(t,function a(r){i.once&&e.removeEventListener(t,a),n(r)});else throw TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e)}Object.defineProperty(r,"defaultMaxListeners",{enumerable:!0,get:function(){return o},set:function(e){if("number"!=typeof e||e<0||a(e))throw RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");o=e}}),r.init=function(){(void 0===this._events||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},r.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||a(e))throw RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},r.prototype.getMaxListeners=function(){return l(this)},r.prototype.emit=function(e){for(var t=[],n=1;n<arguments.length;n++)t.push(arguments[n]);var a="error"===e,r=this._events;if(void 0!==r)a=a&&void 0===r.error;else if(!a)return!1;if(a){if(t.length>0&&(o=t[0]),o instanceof Error)throw o;var o,s=Error("Unhandled error."+(o?" ("+o.message+")":""));throw s.context=o,s}var l=r[e];if(void 0===l)return!1;if("function"==typeof l)i(l,this,t);else for(var c=l.length,d=u(l,c),n=0;n<c;++n)i(d[n],this,t);return!0},r.prototype.addListener=function(e,t){return c(this,e,t,!1)},r.prototype.on=r.prototype.addListener,r.prototype.prependListener=function(e,t){return c(this,e,t,!0)},r.prototype.once=function(e,t){return s(t),this.on(e,h(this,e,t)),this},r.prototype.prependOnceListener=function(e,t){return s(t),this.prependListener(e,h(this,e,t)),this},r.prototype.removeListener=function(e,t){var n,i,a,r,o;if(s(t),void 0===(i=this._events)||void 0===(n=i[e]))return this;if(n===t||n.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete i[e],i.removeListener&&this.emit("removeListener",e,n.listener||t));else if("function"!=typeof n){for(a=-1,r=n.length-1;r>=0;r--)if(n[r]===t||n[r].listener===t){o=n[r].listener,a=r;break}if(a<0)return this;0===a?n.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(n,a),1===n.length&&(i[e]=n[0]),void 0!==i.removeListener&&this.emit("removeListener",e,o||t)}return this},r.prototype.off=r.prototype.removeListener,r.prototype.removeAllListeners=function(e){var t,n,i;if(void 0===(n=this._events))return this;if(void 0===n.removeListener)return 0==arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==n[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete n[e]),this;if(0==arguments.length){var a,r=Object.keys(n);for(i=0;i<r.length;++i)"removeListener"!==(a=r[i])&&this.removeAllListeners(a);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=n[e]))this.removeListener(e,t);else if(void 0!==t)for(i=t.length-1;i>=0;i--)this.removeListener(e,t[i]);return this},r.prototype.listeners=function(e){return f(this,e,!0)},r.prototype.rawListeners=function(e){return f(this,e,!1)},r.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):g.call(e,t)},r.prototype.listenerCount=g,r.prototype.eventNames=function(){return this._eventsCount>0?t(this._events):[]}},9727:function(e,t,n){n.d(t,{SQ:function(){return o},UH:function(){return N},YW:function(){return U},jD:function(){return D},C:function(){return p},y8:function(){return g},g4:function(){return X},UY:function(){return G},vc:function(){return r},kj:function(){return T},aT:function(){return R},XC:function(){return es},HI:function(){return M},vE:function(){return I},DP:function(){return w},kn:function(){return d},o4:function(){return ee},x$:function(){return l},S3:function(){return c},gm:function(){return s},ul:function(){return Q},ik:function(){return h},yR:function(){return k},mA:function(){return Z},ai:function(){return x},m3:function(){return O},Qq:function(){return C},eS:function(){return f}});class i extends Map{#e=0;#t=new Map;#n=new Map;#i;#a;#r;constructor(e={}){if(super(),!(e.maxSize&&e.maxSize>0))throw TypeError("`maxSize` must be a number greater than 0");if("number"==typeof e.maxAge&&0===e.maxAge)throw TypeError("`maxAge` must be a number greater than 0");this.#i=e.maxSize,this.#a=e.maxAge||Number.POSITIVE_INFINITY,this.#r=e.onEviction}get __oldCache(){return this.#n}#o(e){if("function"==typeof this.#r)for(let[t,n]of e)this.#r(t,n.value)}#s(e,t){return!!("number"==typeof t.expiry&&t.expiry<=Date.now())&&("function"==typeof this.#r&&this.#r(e,t.value),this.delete(e))}#l(e,t){if(!1===this.#s(e,t))return t.value}#c(e,t){return t.expiry?this.#l(e,t):t.value}#d(e,t){let n=t.get(e);return this.#c(e,n)}#h(e,t){this.#t.set(e,t),this.#e++,this.#e>=this.#i&&(this.#e=0,this.#o(this.#n),this.#n=this.#t,this.#t=new Map)}#f(e,t){this.#n.delete(e),this.#h(e,t)}*#g(){for(let e of this.#n){let[t,n]=e;this.#t.has(t)||!1!==this.#s(t,n)||(yield e)}for(let e of this.#t){let[t,n]=e;!1===this.#s(t,n)&&(yield e)}}get(e){if(this.#t.has(e)){let t=this.#t.get(e);return this.#c(e,t)}if(this.#n.has(e)){let t=this.#n.get(e);if(!1===this.#s(e,t))return this.#f(e,t),t.value}}set(e,t,{maxAge:n=this.#a}={}){let i="number"==typeof n&&n!==Number.POSITIVE_INFINITY?Date.now()+n:void 0;return this.#t.has(e)?this.#t.set(e,{value:t,expiry:i}):this.#h(e,{value:t,expiry:i}),this}has(e){return this.#t.has(e)?!this.#s(e,this.#t.get(e)):!!this.#n.has(e)&&!this.#s(e,this.#n.get(e))}peek(e){return this.#t.has(e)?this.#d(e,this.#t):this.#n.has(e)?this.#d(e,this.#n):void 0}delete(e){let t=this.#t.delete(e);return t&&this.#e--,this.#n.delete(e)||t}clear(){this.#t.clear(),this.#n.clear(),this.#e=0}resize(e){if(!(e&&e>0))throw TypeError("`maxSize` must be a number greater than 0");let t=[...this.#g()],n=t.length-e;n<0?(this.#t=new Map(t),this.#n=new Map,this.#e=t.length):(n>0&&this.#o(t.slice(0,n)),this.#n=new Map(t.slice(n)),this.#t=new Map,this.#e=0),this.#i=e}*keys(){for(let[e]of this)yield e}*values(){for(let[,e]of this)yield e}*[Symbol.iterator](){for(let e of this.#t){let[t,n]=e;!1===this.#s(t,n)&&(yield[t,n.value])}for(let e of this.#n){let[t,n]=e;this.#t.has(t)||!1!==this.#s(t,n)||(yield[t,n.value])}}*entriesDescending(){let e=[...this.#t];for(let t=e.length-1;t>=0;--t){let[n,i]=e[t];!1===this.#s(n,i)&&(yield[n,i.value])}e=[...this.#n];for(let t=e.length-1;t>=0;--t){let[n,i]=e[t];this.#t.has(n)||!1!==this.#s(n,i)||(yield[n,i.value])}}*entriesAscending(){for(let[e,t]of this.#g())yield[e,t.value]}get size(){if(!this.#e)return this.#n.size;let e=0;for(let t of this.#n.keys())!this.#t.has(t)&&e++;return Math.min(this.#e+e,this.#i)}get maxSize(){return this.#i}entries(){return this.entriesAscending()}forEach(e,t=this){for(let[n,i]of this.entriesAscending())e.call(t,i,n,this)}get[Symbol.toStringTag](){return JSON.stringify([...this.entriesAscending()])}}class a{constructor(){this._apiKey="",this._fetch="undefined"!=typeof self?fetch.bind(self):"undefined"!=typeof global&&global.fetch?global.fetch:null,this.tileCacheSize=200}set apiKey(e){this._apiKey=e}get apiKey(){return this._apiKey}set fetch(e){this._fetch=e}get fetch(){return this._fetch}}let r=new a,o={AUTO:{code:null,flag:"auto",name:"Auto",latin:!1,isMode:!0,geocoding:!0},LATIN:{code:"latin",flag:"name:latin",name:"Latin",latin:!0,isMode:!1,geocoding:!1},NON_LATIN:{code:"nonlatin",flag:"name:nonlatin",name:"Non Latin",latin:!1,isMode:!1,geocoding:!1},LOCAL:{code:null,flag:"name",name:"Local",latin:!0,isMode:!1,geocoding:!1},ALBANIAN:{code:"sq",flag:"name:sq",name:"Albanian",latin:!0,isMode:!1,geocoding:!0},AMHARIC:{code:"am",flag:"name:am",name:"Amharic",latin:!1,isMode:!1,geocoding:!0},ARABIC:{code:"ar",flag:"name:ar",name:"Arabic",latin:!1,isMode:!1,geocoding:!0},ARMENIAN:{code:"hy",flag:"name:hy",name:"Armenian",latin:!1,isMode:!1,geocoding:!0},AZERBAIJANI:{code:"az",flag:"name:az",name:"Azerbaijani",latin:!0,isMode:!1,geocoding:!0},BASQUE:{code:"eu",flag:"name:eu",name:"Basque",latin:!0,isMode:!1,geocoding:!0},BELARUSIAN:{code:"be",flag:"name:be",name:"Belarusian",latin:!1,isMode:!1,geocoding:!0},BENGALI:{code:"bn",flag:"name:bn",name:"Bengali",latin:!0,isMode:!1,geocoding:!0},BOSNIAN:{code:"bs",flag:"name:bs",name:"Bosnian",latin:!0,isMode:!1,geocoding:!0},BRETON:{code:"br",flag:"name:br",name:"Breton",latin:!0,isMode:!1,geocoding:!0},BULGARIAN:{code:"bg",flag:"bg",name:"Bulgarian",latin:!1,isMode:!1,geocoding:!0},CATALAN:{code:"ca",flag:"name:ca",name:"Catalan",latin:!0,isMode:!1,geocoding:!0},CHINESE:{code:"zh",flag:"name:zh",name:"Chinese",latin:!1,isMode:!1,geocoding:!0},TRADITIONAL_CHINESE:{code:"zh-Hant",flag:"name:zh-Hant",name:"Chinese (traditional)",latin:!1,isMode:!1,geocoding:!1},SIMPLIFIED_CHINESE:{code:"zh-Hans",flag:"name:zh-Hans",name:"Chinese (simplified)",latin:!1,isMode:!1,geocoding:!1},CORSICAN:{code:"co",flag:"name:co",name:"Corsican",latin:!0,isMode:!1,geocoding:!0},CROATIAN:{code:"hr",flag:"name:hr",name:"Croatian",latin:!0,isMode:!1,geocoding:!0},CZECH:{code:"cs",flag:"name:cs",name:"Czech",latin:!0,isMode:!1,geocoding:!0},DANISH:{code:"da",flag:"name:da",name:"Danish",latin:!0,isMode:!1,geocoding:!0},DUTCH:{code:"nl",flag:"name:nl",name:"Dutch",latin:!0,isMode:!1,geocoding:!0},GERMAN:{code:"de",flag:"name:de",name:"German",latin:!0,isMode:!1,geocoding:!0},GREEK:{code:"el",flag:"name:el",name:"Greek",latin:!1,isMode:!1,geocoding:!0},ENGLISH:{code:"en",flag:"name:en",name:"English",latin:!0,isMode:!1,geocoding:!0},ESPERANTO:{code:"eo",flag:"name:eo",name:"Esperanto",latin:!0,isMode:!1,geocoding:!0},ESTONIAN:{code:"et",flag:"name:et",name:"Estonian",latin:!0,isMode:!1,geocoding:!0},FINNISH:{code:"fi",flag:"name:fi",name:"Finnish",latin:!0,isMode:!1,geocoding:!0},FRENCH:{code:"fr",flag:"name:fr",name:"French",latin:!0,isMode:!1,geocoding:!0},FRISIAN:{code:"fy",flag:"name:fy",name:"Frisian (West)",latin:!0,isMode:!1,geocoding:!0},GEORGIAN:{code:"ka",flag:"name:ka",name:"Georgian",latin:!1,isMode:!1,geocoding:!0},HEBREW:{code:"he",flag:"name:he",name:"Hebrew",latin:!1,isMode:!1,geocoding:!0},HINDI:{code:"hi",flag:"name:hi",name:"Hindi",latin:!1,isMode:!1,geocoding:!0},HUNGARIAN:{code:"hu",flag:"name:hu",name:"Hungarian",latin:!0,isMode:!1,geocoding:!0},ICELANDIC:{code:"is",flag:"name:is",name:"Icelandic",latin:!0,isMode:!1,geocoding:!0},INDONESIAN:{code:"id",flag:"name:id",name:"Indonesian",latin:!0,isMode:!1,geocoding:!0},IRISH:{code:"ga",flag:"name:ga",name:"Irish",latin:!0,isMode:!1,geocoding:!0},ITALIAN:{code:"it",flag:"name:it",name:"Italian",latin:!0,isMode:!1,geocoding:!0},JAPANESE:{code:"ja",flag:"name:ja",name:"Japanese",latin:!1,isMode:!1,geocoding:!0},JAPANESE_HIRAGANA:{code:"ja-Hira",flag:"name:ja-Hira",name:"Japanese Hiragana form",latin:!1,isMode:!1,geocoding:!1},JAPANESE_2018:{code:"ja-Latn",flag:"name:ja-Latn",name:"Japanese (Latin 2018)",latin:!0,isMode:!1,geocoding:!1},JAPANESE_KANA:{code:"ja_kana",flag:"name:ja_kana",name:"Japanese (Kana)",latin:!1,isMode:!1,geocoding:!1},JAPANESE_LATIN:{code:"ja_rm",flag:"name:ja_rm",name:"Japanese (Latin script)",latin:!0,isMode:!1,geocoding:!1},KANNADA:{code:"kn",flag:"name:kn",name:"Kannada",latin:!0,isMode:!1,geocoding:!0},KAZAKH:{code:"kk",flag:"name:kk",name:"Kazakh",latin:!1,isMode:!1,geocoding:!0},KOREAN:{code:"ko",flag:"name:ko",name:"Korean",latin:!1,isMode:!1,geocoding:!0},KOREAN_LATIN:{code:"ko-Latn",flag:"name:ko-Latn",name:"Korean (Latin script)",latin:!0,isMode:!1,geocoding:!1},KURDISH:{code:"ku",flag:"name:ku",name:"Kurdish",latin:!0,isMode:!1,geocoding:!0},CLASSICAL_LATIN:{code:"la",flag:"name:la",name:"Latin",latin:!0,isMode:!1,geocoding:!0},LATVIAN:{code:"lv",flag:"name:lv",name:"Latvian",latin:!0,isMode:!1,geocoding:!0},LITHUANIAN:{code:"lt",flag:"name:lt",name:"Lithuanian",latin:!0,isMode:!1,geocoding:!0},LUXEMBOURGISH:{code:"lb",flag:"name:lb",name:"Luxembourgish",latin:!0,isMode:!1,geocoding:!0},MACEDONIAN:{code:"mk",flag:"name:mk",name:"Macedonian",latin:!1,isMode:!1,geocoding:!0},MALAYALAM:{code:"ml",flag:"name:ml",name:"Malayalam",latin:!1,isMode:!1,geocoding:!0},MALTESE:{code:"mt",flag:"name:mt",name:"Maltese",latin:!0,isMode:!1,geocoding:!0},NORWEGIAN:{code:"no",flag:"name:no",name:"Norwegian",latin:!0,isMode:!1,geocoding:!0},OCCITAN:{code:"oc",flag:"name:oc",name:"Occitan",latin:!0,isMode:!1,geocoding:!0},PERSIAN:{code:"fa",flag:"name:fa",name:"Persian",latin:!1,isMode:!1,geocoding:!0},POLISH:{code:"pl",flag:"name:pl",name:"Polish",latin:!0,isMode:!1,geocoding:!0},PORTUGUESE:{code:"pt",flag:"name:pt",name:"Portuguese",latin:!0,isMode:!1,geocoding:!0},PUNJABI:{code:"pa",flag:"name:pa",name:"Punjabi",latin:!1,isMode:!1,geocoding:!0},WESTERN_PUNJABI:{code:"pnb",flag:"name:pnb",name:"Western Punjabi",latin:!1,isMode:!1,geocoding:!1},ROMANIAN:{code:"ro",flag:"name:ro",name:"Romanian",latin:!0,isMode:!1,geocoding:!0},ROMANSH:{code:"rm",flag:"name:rm",name:"Romansh",latin:!0,isMode:!1,geocoding:!0},RUSSIAN:{code:"ru",flag:"name:ru",name:"Russian",latin:!1,isMode:!1,geocoding:!0},SERBIAN_CYRILLIC:{code:"sr",flag:"name:sr",name:"Serbian (Cyrillic script)",latin:!1,isMode:!1,geocoding:!0},SERBIAN_LATIN:{code:"sr-Latn",flag:"name:sr-Latn",name:"Serbian (Latin script)",latin:!0,isMode:!1,geocoding:!1},SCOTTISH_GAELIC:{code:"gd",flag:"name:gd",name:"Scottish Gaelic",latin:!0,isMode:!1,geocoding:!0},SLOVAK:{code:"sk",flag:"name:sk",name:"Slovak",latin:!0,isMode:!1,geocoding:!0},SLOVENE:{code:"sl",flag:"name:sl",name:"Slovene",latin:!0,isMode:!1,geocoding:!0},SPANISH:{code:"es",flag:"name:es",name:"Spanish",latin:!0,isMode:!1,geocoding:!0},SWEDISH:{code:"sv",flag:"name:sv",name:"Swedish",latin:!0,isMode:!1,geocoding:!0},TAMIL:{code:"ta",flag:"name:ta",name:"Tamil",latin:!1,isMode:!1,geocoding:!0},TELUGU:{code:"te",flag:"name:te",name:"Telugu",latin:!1,isMode:!1,geocoding:!0},THAI:{code:"th",flag:"name:th",name:"Thai",latin:!1,isMode:!1,geocoding:!0},TURKISH:{code:"tr",flag:"name:tr",name:"Turkish",latin:!0,isMode:!1,geocoding:!0},UKRAINIAN:{code:"uk",flag:"name:uk",name:"Ukrainian",latin:!1,isMode:!1,geocoding:!0},VIETNAMESE:{code:"vi",flag:"name:vi",name:"Vietnamese (Latin script)",latin:!0,isMode:!1,geocoding:!0},WELSH:{code:"cy",flag:"name:cy",name:"Welsh",latin:!0,isMode:!1,geocoding:!0}};function s(e,t=o){return e in t?e[e]:null}function l(e,t=o){for(let n of Object.values(t))if(n.code===e)return n;return null}function c(e,t=o){for(let n of Object.values(t))if(n.flag===e)return n;return null}function d(){return"undefined"==typeof navigator?l(Intl.DateTimeFormat().resolvedOptions().locale.split("-")[0])??o.ENGLISH:Array.from(new Set(navigator.languages.map(e=>e.split("-")[0]))).map(e=>l(e)).filter(e=>e)[0]??o.ENGLISH}function h(e){return null!==e&&"object"==typeof e&&"code"in e&&"flag"in e&&"name"in e&&"latin"in e&&"isMode"in e&&"geocoding"in e&&("string"==typeof e.code||null===e.code)&&"string"==typeof e.flag&&"string"==typeof e.name&&"boolean"==typeof e.latin&&"boolean"==typeof e.isMode&&"boolean"==typeof e.geocoding}function f(e,t=o){return h(e)?c(e.flag,t):"string"!=typeof e?null:s(e,t)||l(e,t)||c(e,t)||null}function g(e,t,n=o){let i=f(e,n),a=f(t,n);return i&&a&&i.flag===a.flag}async function u(e,t={}){if(null===r.fetch)throw Error("The fetch function was not found. If on NodeJS < 18 please specify the fetch function with config.fetch");if(""===new URL(e).searchParams.get("key").trim())throw Error("The MapTiler Cloud API key is missing. Set it in `config.apiKey` or get one for free at https://maptiler.com");return r.fetch(e,t)}let m={maptilerApiURL:"https://api.maptiler.com/",mapStyle:"streets-v2"};Object.freeze(m);class p extends Error{constructor(e,t=""){super(`Call to enpoint ${e.url} failed with the status code ${e.status}. ${t}`),this.res=e}}let y={400:"Query too long / Invalid parameters",403:"Key is missing, invalid or restricted"};function v(e,t){let{language:n}=t;if(void 0===n)return;let i=Array.from(new Set((Array.isArray(n)?n:[n]).map(e=>(function(e){let t=e===o.AUTO.flag?d():"string"==typeof e?l(e):h(e)?e.flag===o.AUTO.flag?d():c(e.flag):null;return t?.geocoding?t.code:null})(e)).filter(e=>e))).join(",");e.set("language",i)}function A(e,t){let{apiKey:n,limit:i,types:a,excludeTypes:o}=t;e.set("key",n??r.apiKey),void 0!==i&&e.set("limit",String(i)),void 0!==a&&e.set("types",a.join(",")),void 0!==o&&e.set("excludeTypes",String(o)),v(e,t)}function L(e,t){A(e,t);let{bbox:n,proximity:i,country:a,fuzzyMatch:r,autocomplete:o}=t;void 0!==n&&e.set("bbox",n.join(",")),void 0!==i&&e.set("proximity","ip"===i?i:i.join(",")),void 0!==a&&e.set("country",a.join(",")),void 0!==r&&e.set("fuzzyMatch",r?"true":"false"),void 0!==o&&e.set("autocomplete",o?"true":"false")}let I={forward:async function(e,t={}){if("string"!=typeof e||0===e.trim().length)throw Error("The query must be a non-empty string");let n=new URL(`geocoding/${encodeURIComponent(e)}.json`,m.maptilerApiURL);L(n.searchParams,t);let i=await u(n.toString());if(!i.ok)throw new p(i,y[i.status]??"");return await i.json()},reverse:async function(e,t={}){if(!Array.isArray(e)||e.length<2)throw Error("The position must be an array of form [lng, lat].");let n=new URL(`geocoding/${e[0]},${e[1]}.json`,m.maptilerApiURL);A(n.searchParams,t);let i=await u(n.toString());if(!i.ok)throw new p(i,y[i.status]??"");return await i.json()},byId:async function(e,t={}){let n=new URL(`geocoding/${e}.json`,m.maptilerApiURL);n.searchParams.set("key",t.apiKey??r.apiKey),v(n.searchParams,t);let i=await u(n.toString());if(!i.ok)throw new p(i,y[i.status]??"");return await i.json()},batch:async function(e,t={}){if(!e.length)return[];let n=e.map(e=>encodeURIComponent(e)).join(";"),i=new URL(`geocoding/${n}.json`,m.maptilerApiURL);L(i.searchParams,t);let a=await u(i.toString());if(!a.ok)throw new p(a,y[a.status]??"");let r=await a.json();return 1===e.length?[r]:r}},S={403:"Key is missing, invalid or restricted"},w={info:async function(e={}){let t=new URL("geolocation/ip.json",m.maptilerApiURL);t.searchParams.set("key",e.apiKey??r.apiKey);let n=t.toString(),i=await u(n);if(!i.ok)throw new p(i,i.status in S?S[i.status]:"");return await i.json()}},E={403:"Key is missing, invalid or restricted"},T={search:async function(e,t={}){if("string"!=typeof e||0===e.trim().length)throw Error("The query must be a non-empty string");let n=new URL(`coordinates/search/${e}.json`,m.maptilerApiURL);n.searchParams.set("key",t.apiKey??r.apiKey),"limit"in t&&n.searchParams.set("limit",t.limit.toString()),"transformations"in t&&n.searchParams.set("transformations",t.transformations.toString()),"exports"in t&&n.searchParams.set("exports",t.exports.toString());let i=n.toString(),a=await u(i);if(!a.ok)throw new p(a,a.status in E?E[a.status]:"");return await a.json()},transform:async function(e,t={}){let n=(Array.isArray(e[0])?e:[e]).map(e=>`${e[0]},${e[1]}`).join(";"),i=new URL(`coordinates/transform/${n}.json`,m.maptilerApiURL);i.searchParams.set("key",t.apiKey??r.apiKey),"sourceCrs"in t&&i.searchParams.set("s_srs",t.sourceCrs.toString()),"targetCrs"in t&&i.searchParams.set("t_srs",t.targetCrs.toString()),"operations"in t&&i.searchParams.set("ops",(Array.isArray(t.operations)?t.operations:[t.operations]).join("|"));let a=i.toString(),o=await u(a);if(!o.ok)throw new p(o,o.status in E?E[o.status]:"");return await o.json()}},b={403:"Key is missing, invalid or restricted"},R={get:async function(e,t={}){if("string"!=typeof e||0===e.trim().length)throw Error("The data ID must be a non-empty string");let n=new URL(`data/${encodeURIComponent(e)}/features.json`,m.maptilerApiURL);n.searchParams.set("key",t.apiKey??r.apiKey);let i=n.toString(),a=await u(i);if(!a.ok)throw new p(a,a.status in b?b[a.status]:"");return await a.json()}};function M(e){let t;let n=e.trim();return n.startsWith("http://")||n.startsWith("https://")?n:null!==(t=/^maptiler:\/\/(.*)/.exec(n))?`https://api.maptiler.com/maps/${t[1]}/style.json`:`https://api.maptiler.com/maps/${n}/style.json`}class U{constructor(e,t,n,i,a,r,o=!1){this.name=e,this.variantType=t,this.id=n,this.referenceStyle=i,this.description=a,this.imageURL=r,this.deprecated=o}getName(){return this.name}getFullName(){return`${this.referenceStyle.getName()} ${this.name}`}getType(){return this.variantType}getId(){return this.id}getDescription(){return this.description}getReferenceStyle(){return this.referenceStyle}hasVariant(e){return this.referenceStyle.hasVariant(e)}getVariant(e){let t=this.referenceStyle.getVariant(e);return this.warnIfDeprecated(t),t}getVariants(){return this.referenceStyle.getVariants().filter(e=>e!==this).map(e=>(this.warnIfDeprecated(e),e))}getImageURL(){return this.imageURL}getExpandedStyleURL(){return M(this.getId())}warnIfDeprecated(e=this){if(!e.deprecated)return e;let t=e.getFullName();return console.warn(`Style "${t}" is deprecated and will be removed in a future version.`),e}}class D{constructor(e,t){this.name=e,this.id=t,this.variants={},this.orderedVariants=[]}getName(){return this.name}getId(){return this.id}addVariant(e){this.variants[e.getType()]=e,this.orderedVariants.push(e)}hasVariant(e){return e in this.variants}getVariant(e){return e in this.variants?this.variants[e]:this.orderedVariants[0]}getVariants(){return Object.values(this.variants)}getDefaultVariant(){return this.orderedVariants[0].warnIfDeprecated()}}let k=[{referenceStyleID:"STREETS",name:"Streets",description:"",variants:[{id:"streets-v2",name:"Default",variantType:"DEFAULT",description:"",imageURL:""},{id:"streets-v2-dark",name:"Dark",variantType:"DARK",description:"",imageURL:""},{id:"streets-v2-light",name:"Light",variantType:"LIGHT",description:"",imageURL:""},{id:"streets-v2-night",name:"Night",variantType:"NIGHT",description:"",imageURL:""},{id:"streets-v2-pastel",name:"Pastel",variantType:"PASTEL",description:"",imageURL:""}]},{referenceStyleID:"OUTDOOR",name:"Outdoor",description:"",variants:[{id:"outdoor-v2",name:"Default",variantType:"DEFAULT",description:"",imageURL:""},{id:"outdoor-v2-dark",name:"Dark",variantType:"DARK",description:"",imageURL:""}]},{referenceStyleID:"WINTER",name:"Winter",description:"",variants:[{id:"winter-v2",name:"Default",variantType:"DEFAULT",description:"",imageURL:""},{id:"winter-v2-dark",name:"Dark",variantType:"DARK",description:"",imageURL:""}]},{referenceStyleID:"SATELLITE",name:"Satellite",description:"",variants:[{id:"satellite",name:"Default",variantType:"DEFAULT",description:"",imageURL:""}]},{referenceStyleID:"HYBRID",name:"Hybrid",description:"",variants:[{id:"hybrid",deprecated:!0,name:"Default",variantType:"DEFAULT",description:"",imageURL:""}]},{referenceStyleID:"BASIC",name:"Basic",description:"",variants:[{id:"basic-v2",name:"Default",variantType:"DEFAULT",description:"",imageURL:""},{id:"basic-v2-dark",name:"Dark",variantType:"DARK",description:"",imageURL:""},{id:"basic-v2-light",name:"Light",variantType:"LIGHT",description:"",imageURL:""}]},{referenceStyleID:"BRIGHT",name:"Bright",description:"",variants:[{id:"bright-v2",name:"Default",variantType:"DEFAULT",description:"",imageURL:""},{id:"bright-v2-dark",name:"Dark",variantType:"DARK",description:"",imageURL:""},{id:"bright-v2-light",name:"Light",variantType:"LIGHT",description:"",imageURL:""},{id:"bright-v2-pastel",name:"Pastel",variantType:"PASTEL",description:"",imageURL:""}]},{referenceStyleID:"OPENSTREETMAP",name:"OpenStreetMap",description:"",variants:[{id:"openstreetmap",name:"Default",variantType:"DEFAULT",description:"",imageURL:""}]},{referenceStyleID:"TOPO",name:"Topo",description:"",variants:[{id:"topo-v2",name:"Default",variantType:"DEFAULT",description:"",imageURL:""},{id:"topo-v2-dark",name:"Dark",variantType:"DARK",description:"",imageURL:""},{id:"topo-v2-shiny",name:"Shiny",deprecated:!0,variantType:"SHINY",description:"",imageURL:""},{id:"topo-v2-pastel",name:"Pastel",variantType:"PASTEL",description:"",imageURL:""},{id:"topo-v2-topographique",name:"Topographique",variantType:"TOPOGRAPHIQUE",description:"",imageURL:""}]},{referenceStyleID:"VOYAGER",name:"Voyager",description:"",variants:[{id:"voyager-v2",name:"Default",deprecated:!0,variantType:"DEFAULT",description:"",imageURL:""},{id:"voyager-v2-darkmatter",name:"Darkmatter",deprecated:!0,variantType:"DARK",description:"",imageURL:""},{id:"voyager-v2-positron",name:"Positron",deprecated:!0,variantType:"LIGHT",description:"",imageURL:""},{id:"voyager-v2-vintage",name:"Vintage",deprecated:!0,variantType:"VINTAGE",description:"",imageURL:""}]},{referenceStyleID:"TONER",name:"Toner",description:"",variants:[{id:"toner-v2",name:"Default",variantType:"DEFAULT",description:"",imageURL:""},{id:"toner-v2-background",name:"Background",variantType:"BACKGROUND",deprecated:!0,description:"",imageURL:""},{id:"toner-v2-lite",name:"Lite",variantType:"LITE",description:"",imageURL:""},{id:"toner-v2-lines",name:"Lines",variantType:"LINES",deprecated:!0,description:"",imageURL:""}]},{referenceStyleID:"DATAVIZ",name:"Dataviz",description:"",variants:[{id:"dataviz",name:"Default",variantType:"DEFAULT",description:"",imageURL:""},{id:"dataviz-dark",name:"Dark",variantType:"DARK",description:"",imageURL:""},{id:"dataviz-light",name:"Light",variantType:"LIGHT",description:"",imageURL:""}]},{referenceStyleID:"BACKDROP",name:"Backdrop",description:"",variants:[{id:"backdrop",name:"Default",variantType:"DEFAULT",description:"",imageURL:""},{id:"backdrop-dark",name:"Dark",variantType:"DARK",description:"",imageURL:""},{id:"backdrop-light",name:"Light",variantType:"LIGHT",description:"",imageURL:""}]},{referenceStyleID:"OCEAN",name:"Ocean",description:"",variants:[{id:"ocean",name:"Default",variantType:"DEFAULT",description:"",imageURL:""}]},{referenceStyleID:"AQUARELLE",name:"Aquarelle",description:"Watercolor map for creative use",variants:[{id:"aquarelle",name:"Default",variantType:"DEFAULT",description:"",imageURL:""},{id:"aquarelle-dark",name:"Dark",variantType:"DARK",description:"",imageURL:""},{id:"aquarelle-vivid",name:"Vivid",variantType:"VIVID",description:"",imageURL:""}]},{referenceStyleID:"LANDSCAPE",name:"Landscape",description:"Terrain map for data overlays and visualisations",variants:[{id:"landscape",name:"Default",variantType:"DEFAULT",description:"",imageURL:""},{id:"landscape-dark",name:"Dark",variantType:"DARK",description:"",imageURL:""},{id:"landscape-vivid",name:"Vivid",variantType:"VIVID",description:"",imageURL:""}]}];function C(e){return e?"string"==typeof e||e instanceof String?e.trim().toLowerCase():e instanceof U?e.getId():e instanceof D?e.getDefaultVariant().getId():void 0:N[k[0].referenceStyleID].getDefaultVariant().getId()}let N=function(){let e={};for(let t=0;t<k.length;t+=1){let n=k[t],i=function(e){return new Proxy(e,{get:(t,n,i)=>t.hasVariant(n)?t.getVariant(n):n.toString().toUpperCase()===n?e.getDefaultVariant():Reflect.get(t,n,i)})}(new D(n.name,n.referenceStyleID));for(let e=0;e<n.variants.length;e+=1){let t=n.variants[e],a=new U(t.name,t.variantType,t.id,i,t.description,t.imageURL,t.deprecated);i.addVariant(a)}e[n.referenceStyleID]=i}return e}(),x={extractLineStrings:function(e){let t=[];function n(e){("LineString"===e.type||"MultiLineString"===e.type)&&t.push(e)}function i(e){e.geometry&&n(e.geometry)}return"Feature"===e.type?i(e):"FeatureCollection"===e.type?function e(t){for(let n of t.features)"Feature"===n.type?i(n):"FeatureCollection"===n.type&&e(n)}(e):n(e),t},simplify:function(e,t){return e.length<=2?e:function(e,t){let n=e.length-1,i=[e[0]];return!function e(t,n,i,a,r){let o=a,s;for(let e=n+1;e<i;e++){let a=function(e,t,n){let i=t[0],a=t[1],r=n[0]-i,o=n[1]-a;if(0!==r||0!==o){let t=((e[0]-i)*r+(e[1]-a)*o)/(r*r+o*o);t>1?(i=n[0],a=n[1]):t>0&&(i+=r*t,a+=o*t)}return(r=e[0]-i)*r+(o=e[1]-a)*o}(t[e],t[n],t[i]);a>o&&(s=e,o=a)}o>a&&(s-n>1&&e(t,n,s,a,r),r.push(t[s]),i-s>1&&e(t,s,i,a,r))}(e,0,n,t,i),i.push(e[n]),i}(e,void 0!==t?t*t:1)}};function P(e,t=!0){let n=`${e[0]},${e[1]}`;return 3===e.length&&t&&(n+=`,${e[2]}`),n}function _(e,t=3e3){let n=e.map(e=>e.join(",")).join("|"),i=5e-6;for(;n.length>t;)n=x.simplify(e,i).map(e=>`${e[0]},${e[1]}`).join("|"),i+=1e-5;return n}let O={centered:function(e,t,n={}){let i=C(n.style),a=n.hiDPI?"@2x":"",o=n.format??"png",s=~~(n.width??1024),l=~~(n.height??1024);n.hiDPI&&(s=~~(s/2),l=~~(l/2));let c=new URL(`maps/${encodeURIComponent(i)}/static/${e[0]},${e[1]},${t}/${s}x${l}${a}.${o}`,m.maptilerApiURL);if("attribution"in n&&c.searchParams.set("attribution",n.attribution.toString()),"markers"in n){let e="",t="markerIcon"in n;t&&(e+=`icon:${n.markerIcon}|`),t&&"markerAnchor"in n&&(e+=`anchor:${n.markerAnchor}|`),t&&n.hiDPI&&(e+="scale:2|"),e+=(Array.isArray(n.markers[0])?n.markers:[n.markers]).map(e=>P(e,!t)).join("|"),c.searchParams.set("markers",e)}if("path"in n){let e="";if(e+=`fill:${n.pathFillColor??"none"}|`,"pathStrokeColor"in n&&(e+=`stroke:${n.pathStrokeColor}|`),"pathWidth"in n){let t=n.pathWidth/(n.hiDPI?2:1);e+=`width:${t.toString()}|`}e+=_(n.path),c.searchParams.set("path",e)}return c.searchParams.set("key",n.apiKey??r.apiKey),c.toString()},bounded:function(e,t={}){let n=C(t.style),i=t.hiDPI?"@2x":"",a=t.format??"png",o=~~(t.width??1024),s=~~(t.height??1024);t.hiDPI&&(o=~~(o/2),s=~~(s/2));let l=new URL(`maps/${encodeURIComponent(n)}/static/${e[0]},${e[1]},${e[2]},${e[3]}/${o}x${s}${i}.${a}`,m.maptilerApiURL);if("attribution"in t&&l.searchParams.set("attribution",t.attribution.toString()),"padding"in t&&l.searchParams.set("padding",t.padding.toString()),"markers"in t){let e="",n="markerIcon"in t;n&&(e+=`icon:${t.markerIcon}|`),n&&"markerAnchor"in t&&(e+=`anchor:${t.markerAnchor}|`),n&&t.hiDPI&&(e+="scale:2|"),e+=(Array.isArray(t.markers[0])?t.markers:[t.markers]).map(e=>P(e,!n)).join("|"),l.searchParams.set("markers",e)}if("path"in t){let e="";if(e+=`fill:${t.pathFillColor??"none"}|`,"pathStrokeColor"in t&&(e+=`stroke:${t.pathStrokeColor}|`),"pathWidth"in t){let n=t.pathWidth/(t.hiDPI?2:1);e+=`width:${n.toString()}|`}e+=_(t.path),l.searchParams.set("path",e)}return l.searchParams.set("key",t.apiKey??r.apiKey),l.toString()},automatic:function(e={}){if(!("markers"in e)&&!("path"in e))throw Error("Automatic static maps require markers and/or path to be created.");let t=C(e.style),n=e.hiDPI?"@2x":"",i=e.format??"png",a=~~(e.width??1024),o=~~(e.height??1024);e.hiDPI&&(a=~~(a/2),o=~~(o/2));let s=new URL(`maps/${encodeURIComponent(t)}/static/auto/${a}x${o}${n}.${i}`,m.maptilerApiURL);if("attribution"in e&&s.searchParams.set("attribution",e.attribution.toString()),"padding"in e&&s.searchParams.set("padding",e.padding.toString()),"markers"in e){let t="",n="markerIcon"in e;n&&(t+=`icon:${e.markerIcon}|`),n&&"markerAnchor"in e&&(t+=`anchor:${e.markerAnchor}|`),n&&e.hiDPI&&(t+="scale:2|"),t+=(Array.isArray(e.markers[0])?e.markers:[e.markers]).map(e=>P(e,!n)).join("|"),s.searchParams.set("markers",t)}if("path"in e){let t="";if(t+=`fill:${e.pathFillColor??"none"}|`,"pathStrokeColor"in e&&(t+=`stroke:${e.pathStrokeColor}|`),"pathWidth"in e){let n=e.pathWidth/(e.hiDPI?2:1);t+=`width:${n.toString()}|`}t+=_(e.path),s.searchParams.set("path",t)}return s.searchParams.set("key",e.apiKey??r.apiKey),s.toString()}},j=2*Math.PI*6371008.8;function $(e){return(180+e)/360}function K(e){return(180-180/Math.PI*Math.log(Math.tan(Math.PI/4+e*Math.PI/360)))/360}function z(e){let t=V(e);return[$(t[0]),K(t[1])]}function F(e){return 360*e-180}function H(e){return 360/Math.PI*Math.atan(Math.exp((180-360*e)*Math.PI/180))-90}function B(e,t){let n=Math.PI/180,i=e[1]*n,a=t[1]*n;return 6371008.8*Math.acos(Math.min(Math.sin(i)*Math.sin(a)+Math.cos(i)*Math.cos(a)*Math.cos((t[0]-e[0])*n),1))}function V(e){let t=e[0],n=e[1],i=((t+180)%360+360)%360-180;return[-180===i?180:i,n]}function G(e){return j*Math.cos(e*Math.PI/180)}function W(e,t,n=!0){let i=2**t,a=[e[0]*i,e[1]*i];return n?[~~a[0],~~a[1]]:a}function J(e){return e*Math.PI/180}function q(e){return 180*e/Math.PI}let Z={EARTH_RADIUS:6371008.8,EARTH_CIRCUMFERENCE:j,longitudeToMercatorX:$,latitudeToMercatorY:K,wgs84ToMercator:z,mercatorXToLongitude:F,mercatorYToLatitude:H,mercatorToWgs84:function(e){return[F(e[0]),H(e[1])]},haversineDistanceWgs84:B,wrapWgs84:V,circumferenceAtLatitude:G,mercatorToTileIndex:W,wgs84ToTileIndex:function(e,t,n=!0){return W(z(e),t,n)},toRadians:J,toDegrees:q,haversineIntermediateWgs84:function(e,t,n){let i=B(e,t),a=J(e[0]),r=J(e[1]),o=J(t[0]),s=J(t[1]),l=i/6371008.8,c=Math.sin((1-n)*l)/Math.sin(l),d=Math.sin(n*l)/Math.sin(l),h=c*Math.cos(r)*Math.cos(a)+d*Math.cos(s)*Math.cos(o),f=c*Math.cos(r)*Math.sin(a)+d*Math.cos(s)*Math.sin(o);return[q(Math.atan2(f,h)),q(Math.atan2(c*Math.sin(r)+d*Math.sin(s),Math.sqrt(h*h+f*f)))]},haversineCumulatedDistanceWgs84:function(e){let t=Array(e.length);t[0]=0;let n=t.length;for(let i=1;i<n;i++)t[i]=B(e[i-1],e[i])+t[i-1];return t}},Y=null;function Q(){return Y||(Y=new i({maxSize:r.tileCacheSize})),Y}async function X(e){let t=new Blob([e]),n=await createImageBitmap(t),i=document.createElement("canvas"),a=i.getContext("2d");i.width=n.width,i.height=n.height,a.drawImage(n,0,0);let r=a.getImageData(0,0,i.width,i.height);return{pixels:r.data,width:i.width,height:i.height,components:r.data.length/(i.width*i.height)}}function ee(){if(r.bufferToPixelData)return r.bufferToPixelData;if("undefined"!=typeof window)return X;throw Error("An image file buffer to pixel data parser is necessary. Specify it in `config.bufferToPixelData`")}let et=null,en={403:"Key is missing, invalid or restricted"};async function ei(e){let t=new URL("tiles/terrain-rgb-v2/tiles.json",m.maptilerApiURL);t.searchParams.set("key",e);let n=t.toString(),i=await u(n);if(i.ok)return et=await i.json();if(!i.ok)throw new p(i,en[i.status]??"")}async function ea(e,t={}){let n=t.apiKey??r.apiKey;et||await ei(n);let i=ee(),a=et.tiles[0],o=Q(),s=et.maxzoom,l=~~(t.zoom??s);(l>s||l<0)&&(l=s);let c=e.map(e=>Z.wgs84ToTileIndex(e,l,!1)),d=c.map(e=>[~~e[0],~~e[1]]).map(e=>`terrain_${l.toString()}_${e[0].toString()}_${e[1].toString()}`),h=Array.from(new Set(d.filter(e=>!o.has(e)))).map(e=>e.split("_").slice(1)),f=h.map(e=>a.replace("{x}",e[1].toString()).replace("{y}",e[2].toString()).replace("{z}",e[0].toString())).map(e=>u(e)),g=(await Promise.allSettled(f)).map(e=>"fulfilled"===e.status?e.value:null).filter(e=>e),m=g.filter(e=>!e.ok);if(g.length!==f.length)throw Error("Some tiles could not be fetched.");if(m.length)throw new p(m[0],en[m[0].status]??"");let y=await Promise.all(g.map(e=>e.arrayBuffer()));if(!y.every(e=>e.byteLength>0))throw Error("Some tiles are not available.");(await Promise.all(y.map(e=>i(e)))).forEach((e,t)=>{let n=h[t],i=`terrain_${n[0].toString()}_${n[1].toString()}_${n[2].toString()}`;o.set(i,e)});let v=e.map((e,t)=>{let n=d[t],i=c[t],a=o.get(n),r=Math.min(Math.round(a.width*(i[0]%1)),a.width-1),s=(Math.min(Math.round(a.height*(i[1]%1)),a.height-1)*a.width+r)*a.components,l=a.pixels[s],h=a.pixels[s+1],f=a.pixels[s+2];return[e[0],e[1],~~(1e3*(-1e4+(65536*l+256*h+f)*.1))/1e3]});if(t.smoothingKernelSize){let e=2*~~(t.smoothingKernelSize/2)+1,n=v.map(e=>e[2]),i=~~(e/2);for(let t=i;t<n.length-i-1;t+=1){let a=0;for(let r=0;r<e;r+=1)a+=n[t-i+r];a/=e,v[t][2]=a}}return v}async function er(e,t={}){if("LineString"!==e.type)throw Error("The provided object is not a GeoJSON LineString");let n=structuredClone(e),i=await ea(n.coordinates,t);return n.coordinates=i,n}async function eo(e,t={}){if("MultiLineString"!==e.type)throw Error("The provided object is not a GeoJSON MultiLineString");let n=structuredClone(e),i=n.coordinates.map(e=>e.length),a=n.coordinates.flat(),r=await ea(a,t),o=[],s=0;for(let e of i)o.push(r.slice(s,s+e)),s+=e;return n.coordinates=o,n}let es={at:async function(e,t={}){let n;let i=t.apiKey??r.apiKey;et||await ei(i);let a=et.maxzoom,o=~~(t.zoom??a);(o>a||o<0)&&(o=a);let s=Z.wgs84ToTileIndex(e,o,!1),l=~~s[0],c=~~s[1];if(!et.tiles.length)throw Error("Terrain tileJSON tile list is empty.");let d=`terrain_${o.toString()}_${l.toString()}_${c.toString()}`,h=Q();if(h.has(d))n=h.get(d);else{let e=et.tiles[0].replace("{x}",l.toString()).replace("{y}",c.toString()).replace("{z}",o.toString()),t=await u(e);if(!t.ok)throw new p(t,en[t.status]??"");let i=await t.arrayBuffer(),a=ee();n=await a(i),h.set(d,n)}let f=~~(n.width*(s[0]%1)),g=(~~(n.height*(s[1]%1))*n.width+f)*n.components,m=n.pixels[g],y=n.pixels[g+1],v=n.pixels[g+2];return[e[0],e[1],-1e4+(65536*m+256*y+v)*.1]},batch:ea,fromLineString:er,fromMultiLineString:eo}},52300:function(e,t,n){let i;n.d(t,{DS:function(){return $}});var a=n(21876).Buffer;let r="3.7.7",o="function"==typeof a,s="function"==typeof TextDecoder?new TextDecoder:void 0,l="function"==typeof TextEncoder?new TextEncoder:void 0,c=Array.prototype.slice.call("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="),d=(i={},c.forEach((e,t)=>i[e]=t),i),h=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,f=String.fromCharCode.bind(String),g="function"==typeof Uint8Array.from?Uint8Array.from.bind(Uint8Array):e=>new Uint8Array(Array.prototype.slice.call(e,0)),u=e=>e.replace(/=/g,"").replace(/[+\/]/g,e=>"+"==e?"-":"_"),m=e=>e.replace(/[^A-Za-z0-9\+\/]/g,""),p=e=>{let t,n,i,a,r="",o=e.length%3;for(let o=0;o<e.length;){if((n=e.charCodeAt(o++))>255||(i=e.charCodeAt(o++))>255||(a=e.charCodeAt(o++))>255)throw TypeError("invalid character found");r+=c[(t=n<<16|i<<8|a)>>18&63]+c[t>>12&63]+c[t>>6&63]+c[63&t]}return o?r.slice(0,o-3)+"===".substring(o):r},y="function"==typeof btoa?e=>btoa(e):o?e=>a.from(e,"binary").toString("base64"):p,v=o?e=>a.from(e).toString("base64"):e=>{let t=[];for(let n=0,i=e.length;n<i;n+=4096)t.push(f.apply(null,e.subarray(n,n+4096)));return y(t.join(""))},A=(e,t=!1)=>t?u(v(e)):v(e),L=e=>{if(e.length<2){var t=e.charCodeAt(0);return t<128?e:t<2048?f(192|t>>>6)+f(128|63&t):f(224|t>>>12&15)+f(128|t>>>6&63)+f(128|63&t)}var t=65536+(e.charCodeAt(0)-55296)*1024+(e.charCodeAt(1)-56320);return f(240|t>>>18&7)+f(128|t>>>12&63)+f(128|t>>>6&63)+f(128|63&t)},I=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,S=e=>e.replace(I,L),w=o?e=>a.from(e,"utf8").toString("base64"):l?e=>v(l.encode(e)):e=>y(S(e)),E=(e,t=!1)=>t?u(w(e)):w(e),T=e=>E(e,!0),b=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,R=e=>{switch(e.length){case 4:var t=((7&e.charCodeAt(0))<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|63&e.charCodeAt(3))-65536;return f((t>>>10)+55296)+f((1023&t)+56320);case 3:return f((15&e.charCodeAt(0))<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2));default:return f((31&e.charCodeAt(0))<<6|63&e.charCodeAt(1))}},M=e=>e.replace(b,R),U=e=>{if(e=e.replace(/\s+/g,""),!h.test(e))throw TypeError("malformed base64.");e+="==".slice(2-(3&e.length));let t,n="",i,a;for(let r=0;r<e.length;)t=d[e.charAt(r++)]<<18|d[e.charAt(r++)]<<12|(i=d[e.charAt(r++)])<<6|(a=d[e.charAt(r++)]),n+=64===i?f(t>>16&255):64===a?f(t>>16&255,t>>8&255):f(t>>16&255,t>>8&255,255&t);return n},D="function"==typeof atob?e=>atob(m(e)):o?e=>a.from(e,"base64").toString("binary"):U,k=o?e=>g(a.from(e,"base64")):e=>g(D(e).split("").map(e=>e.charCodeAt(0))),C=e=>k(x(e)),N=o?e=>a.from(e,"base64").toString("utf8"):s?e=>s.decode(k(e)):e=>M(D(e)),x=e=>m(e.replace(/[-_]/g,e=>"-"==e?"+":"/")),P=e=>N(x(e)),_=e=>({value:e,enumerable:!1,writable:!0,configurable:!0}),O=function(){let e=(e,t)=>Object.defineProperty(String.prototype,e,_(t));e("fromBase64",function(){return P(this)}),e("toBase64",function(e){return E(this,e)}),e("toBase64URI",function(){return E(this,!0)}),e("toBase64URL",function(){return E(this,!0)}),e("toUint8Array",function(){return C(this)})},j=function(){let e=(e,t)=>Object.defineProperty(Uint8Array.prototype,e,_(t));e("toBase64",function(e){return A(this,e)}),e("toBase64URI",function(){return A(this,!0)}),e("toBase64URL",function(){return A(this,!0)})},$={version:r,VERSION:r,atob:D,atobPolyfill:U,btoa:y,btoaPolyfill:p,fromBase64:P,toBase64:E,encode:E,encodeURI:T,encodeURL:T,utob:S,btou:M,decode:P,isValid:e=>{if("string"!=typeof e)return!1;let t=e.replace(/\s+/g,"").replace(/={0,2}$/,"");return!/[^\s0-9a-zA-Z\+/]/.test(t)||!/[^\s0-9a-zA-Z\-_]/.test(t)},fromUint8Array:A,toUint8Array:C,extendString:O,extendUint8Array:j,extendBuiltins:()=>{O(),j()}}},88773:function(e,t,n){let i;n.d(t,{Z:function(){return s}});var a={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};let r=new Uint8Array(16),o=[];for(let e=0;e<256;++e)o.push((e+256).toString(16).slice(1));var s=function(e,t,n){if(a.randomUUID&&!t&&!e)return a.randomUUID();let s=(e=e||{}).random??e.rng?.()??function(){if(!i){if("undefined"==typeof crypto||!crypto.getRandomValues)throw Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");i=crypto.getRandomValues.bind(crypto)}return i(r)}();if(s.length<16)throw Error("Random bytes length must be >= 16");if(s[6]=15&s[6]|64,s[8]=63&s[8]|128,t){if((n=n||0)<0||n+16>t.length)throw RangeError(`UUID byte range ${n}:${n+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[n+e]=s[e];return t}return function(e,t=0){return(o[e[t+0]]+o[e[t+1]]+o[e[t+2]]+o[e[t+3]]+"-"+o[e[t+4]]+o[e[t+5]]+"-"+o[e[t+6]]+o[e[t+7]]+"-"+o[e[t+8]]+o[e[t+9]]+"-"+o[e[t+10]]+o[e[t+11]]+o[e[t+12]]+o[e[t+13]]+o[e[t+14]]+o[e[t+15]]).toLowerCase()}(s)}}}]);