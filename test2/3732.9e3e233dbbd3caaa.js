try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="6969ae08-8dcd-49f2-a5ac-de4967229e84",e._sentryDebugIdIdentifier="sentry-dbid-6969ae08-8dcd-49f2-a5ac-de4967229e84")}catch(e){}(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3732],{24533:function(e,t,s){"use strict";var a=s(85893),r=s(67221),l=s(41664),n=s.n(l),i=s(67294),o=s(68070);t.Z=()=>{let[e,t]=(0,i.useState)(!0),[s,l]=(0,i.useState)(!1);return(0,i.useEffect)(()=>{let e=setTimeout(()=>{l(!0)},7e3);return()=>clearTimeout(e)},[]),(0,a.jsx)(o.Z,{id:"cloud-add",className:"position-absolute bg-white rounded shadow bottom-0 end-0 text-center  cloud-add-bigger me-5 mb-3 d-none d-sm-block",children:!0==e&&(0,a.jsxs)(a.Fragment,{children:[!0==s&&(0,a.jsx)(r.Z,{id:"cloud-add-close",name:"clear",width:16,height:16,className:"end-0 m-1 mx-1 position-absolute top-0 cursor-pointer",onClick:()=>{t(!1)}}),(0,a.jsx)(n(),{href:"/cloud/",children:(0,a.jsx)("img",{loading:"lazy",className:"h-auto w-70 my-1 pt-1",src:"/img/maptiler-logo.svg",alt:"maptiler-logo"})}),!0==s&&(0,a.jsxs)("div",{id:"cloud-add-detail",className:"pb-1",children:[(0,a.jsx)("h5",{children:(0,a.jsx)("b",{children:"Try our maps API!"})}),(0,a.jsxs)("p",{className:"text-darker my-1",children:["Free plan ",(0,a.jsx)("br",{})," Commercial use 25$/mo. ",(0,a.jsx)("br",{})," Raster & Vector tiles ",(0,a.jsx)("br",{})," Your own map style"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("a",{className:"btn btn-primary mb-1",href:"https://cloud.maptiler.com/",children:"Start for free"}),(0,a.jsx)("p",{className:"m-0 pb-3",children:(0,a.jsx)(n(),{href:"/cloud/",children:"More about MapTiler Cloud"})})]})]})})}},83732:function(e,t,s){"use strict";s.r(t);var a=s(85893),r=s(67294),l=s(24533),n=s(14760);s(21006),t.default=e=>{let{data:t,navbar:s}=e,i=(0,r.useRef)(null),[o,d]=(0,r.useState)(!1),[c,u]=(0,r.useState)(!1),[h,m]=(0,r.useState)(335),[p,g]=(0,r.useState)(1),[b,x]=(0,r.useState)(""),f=(0,r.useRef)(null);return(0,r.useEffect)(()=>{if(!i.current)return n.config.apiKey="O7VbOY3zrXxBupgrQtdE",i.current=new n.Map({container:f.current,style:t.mapID,center:[t.longitude,t.latitude],zoom:t.zoom,hash:!0}),i.current.once("styledata",function(){d(!0)}),()=>{var e;null===(e=i.current)||void 0===e||e.remove()}},[t.latitude,t.longitude,t.mapID,t.mapId,t.style,t.zoom]),(0,r.useEffect)(()=>{if(o){let e={"hillshade-illumination-direction":h,"hillshade-exaggeration":p,"hillshade-highlight-color":c?"#FFFFFF":"hsla(141, 35%, 47%, 0.75)","hillshade-shadow-color":c?"#000000":"hsla(130, 43%, 11%, 0.9)","hillshade-accent-color":c?"#000000":"#dcf193"};Object.keys(e).forEach(function(t){i.current.setPaintProperty("hillshading",t,e[t])}),x('"paint": '+JSON.stringify(e,void 0,2))}},[c,h,p,o]),(0,a.jsxs)("div",{className:"map-wrap ".concat(t.fullscreen?s?"fullscreen-navbar":"fullscreen":""),children:[(0,a.jsx)("div",{className:"map-container",ref:f}),(0,a.jsx)("div",{className:"position-absolute top-0  bg-lighter rounded shadow mx-gutter my-2 p-gutter",children:(0,a.jsxs)("div",{className:"map-options",children:["Direction: ",(0,a.jsx)("br",{}),(0,a.jsx)("input",{type:"range",min:"0",max:"359",value:h,onChange:e=>{m(parseInt(e.target.value))},id:"dir"}),(0,a.jsx)("br",{}),"Exaggeration: ",(0,a.jsx)("br",{}),(0,a.jsx)("input",{type:"range",min:"0",max:"1",value:p,step:"0.05",onChange:e=>{g(parseFloat(e.target.value))},id:"exa"}),(0,a.jsx)("br",{}),"B&W:\xa0",(0,a.jsx)("input",{type:"checkbox",onChange:e=>{u(e.target.checked)},id:"bw"}),(0,a.jsx)("br",{})]})}),(0,a.jsx)("div",{className:"position-absolute bottom-0 bg-lighter rounded shadow mb-3 mx-gutter p-gutter",children:(0,a.jsx)("div",{className:"code-toolbar",children:(0,a.jsx)("pre",{id:"hillshading-code",children:(0,a.jsx)("code",{className:"language-json",children:b})})})}),(0,a.jsx)(l.Z,{})]})}},21006:function(){}}]);