try{let e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="31c127e4-f0a9-4538-af33-6b5ecc421e4d",e._sentryDebugIdIdentifier="sentry-dbid-31c127e4-f0a9-4538-af33-6b5ecc421e4d")}catch(e){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3751],{14760:function(e,t,r){let n;r.r(t),r.d(t,{AJAXError:function(){return oH},AttributionControl:function(){return eu},AttributionControlMLGL:function(){return oL},BoxZoomHandler:function(){return ey},BoxZoomHandlerMLGL:function(){return oM},CanvasSource:function(){return ee},CanvasSourceMLGL:function(){return oh},ColorRamp:function(){return ol},ColorRampCollection:function(){return os},CooperativeGesturesHandler:function(){return em},CooperativeGesturesHandlerMLGL:function(){return oE},DOMcreate:function(){return nx},DOMremove:function(){return nw},DoubleClickZoomHandler:function(){return o0},DragPanHandler:function(){return oX},DragRotateHandler:function(){return oY},EdgeInsets:function(){return oJ},Evented:function(){return oG},FullscreenControl:function(){return ed},FullscreenControlMLGL:function(){return o_},GeoJSONSource:function(){return et},GeoJSONSourceMLGL:function(){return om},GeolocateControl:function(){return es},GeolocateControlMLGL:function(){return ok},GeolocationType:function(){return nE},Hash:function(){return oK},ImageSource:function(){return er},ImageSourceMLGL:function(){return og},KeyboardHandler:function(){return eg},KeyboardHandlerMLGL:function(){return o$},Language:function(){return E},LngLat:function(){return oD},LngLatBounds:function(){return oU},LogoControl:function(){return ep},LogoControlMLGL:function(){return oC},Map:function(){return n$},MapMLGL:function(){return oc},MapMouseEvent:function(){return ew},MapMouseEventMLGL:function(){return oj},MapStyle:function(){return v.UH},MapStyleVariant:function(){return v.YW},MapTouchEvent:function(){return ex},MapTouchEventMLGL:function(){return oR},MapWheelEvent:function(){return eb},MapWheelEventMLGL:function(){return oP},MaptilerGeolocateControl:function(){return n_},MaptilerLogoControl:function(){return eS},MaptilerNavigationControl:function(){return nk},MaptilerProjectionControl:function(){return nM},MaptilerTerrainControl:function(){return nS},Marker:function(){return Y},MarkerMLGL:function(){return od},MercatorCoordinate:function(){return oB},NavigationControMLGL:function(){return oS},NavigationControl:function(){return el},Point:function(){return oZ},Popup:function(){return X},PopupMLGL:function(){return of},RasterDEMTileSource:function(){return eo},RasterDEMTileSourceMLGL:function(){return ob},RasterTileSource:function(){return en},RasterTileSourceMLGL:function(){return ov},ReferenceMapStyle:function(){return v.jD},ScaleControl:function(){return ec},ScaleControlMLGL:function(){return oA},ScrollZoomHandler:function(){return eh},ScrollZoomHandlerMLGL:function(){return oT},SdkConfig:function(){return R},ServiceError:function(){return v.C},Style:function(){return Q},StyleMLGL:function(){return oy},TerrainControMLGL:function(){return oz},TerrainControl:function(){return ef},TwoFingersTouchPitchHandler:function(){return ev},TwoFingersTouchPitchHandlerMLGL:function(){return oI},TwoFingersTouchRotateHandler:function(){return o2},TwoFingersTouchZoomHandler:function(){return o1},TwoFingersTouchZoomRotateHandler:function(){return oQ},VectorTileSource:function(){return ea},VectorTileSourceMLGL:function(){return ox},VideoSource:function(){return ei},VideoSourceMLGL:function(){return ow},addProtocol:function(){return at},addSourceType:function(){return o7},areSameLanguages:function(){return v.y8},bufferToPixelDataBrowser:function(){return v.g4},circumferenceAtLatitude:function(){return v.UY},clearPrewarmedResources:function(){return oW},config:function(){return j},configMLGL:function(){return oN},coordinates:function(){return v.kj},data:function(){return v.aT},displayWebGLContextLostWarning:function(){return Z},elevation:function(){return v.XC},expandMapStyle:function(){return v.HI},geocoding:function(){return v.vE},geolocation:function(){return v.DP},getAutoLanguage:function(){return v.kn},getBrowserLanguage:function(){return $},getBufferToPixelDataParser:function(){return v.o4},getLanguageInfoFromCode:function(){return v.x$},getLanguageInfoFromFlag:function(){return v.S3},getLanguageInfoFromKey:function(){return v.gm},getMapLibreVersion:function(){return oO},getMaxParallelImageRequests:function(){return o4},getRTLTextPluginStatus:function(){return oq},getTileCache:function(){return v.ul},getVersion:function(){return op},getWebGLSupportError:function(){return K},getWorkerCount:function(){return o5},getWorkerUrl:function(){return o6},gpx:function(){return nj},gpxOrKml:function(){return nQ},hasChildNodeWithName:function(){return nP},helpers:function(){return ou},importScriptInWorkers:function(){return ae},isLanguageInfo:function(){return v.ik},kml:function(){return nN},mapStylePresetList:function(){return v.yR},math:function(){return v.mA},misc:function(){return v.ai},prewarm:function(){return oV},removeProtocol:function(){return ar},setMaxParallelImageRequests:function(){return o8},setRTLTextPlugin:function(){return oF},setWorkerCount:function(){return o3},setWorkerUrl:function(){return o9},staticMaps:function(){return v.m3},str2xml:function(){return nI},styleToStyle:function(){return v.Qq},toLanguageInfo:function(){return v.eS},xml2str:function(){return nR}});var o,a,i,l,s,u,p,c,d,f,y,h,m,g=r(14231),v=r(9727),b=r(17187),x=r(88773),w=r(52300),S=Object.defineProperty,k=e=>{throw TypeError(e)},L=(e,t,r)=>t in e?S(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,C=(e,t,r)=>L(e,"symbol"!=typeof t?t+"":t,r),A=(e,t,r)=>t.has(e)||k("Cannot "+r),_=(e,t,r)=>(A(e,t,"read from private field"),r?r.call(e):t.get(e)),z=(e,t,r)=>t.has(e)?k("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),M=(e,t,r,n)=>(A(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r),T=(e,t,r)=>(A(e,t,"access private method"),r);let E={VISITOR:{code:null,flag:"visitor",name:"Visitor",latin:!0,isMode:!0,geocoding:!1},VISITOR_ENGLISH:{code:null,flag:"visitor_en",name:"Visitor English",latin:!0,isMode:!0,geocoding:!1},STYLE:{code:null,flag:"style",name:"Style",latin:!1,isMode:!0,geocoding:!1},STYLE_LOCK:{code:null,flag:"style_lock",name:"Style Lock",latin:!1,isMode:!0,geocoding:!1},...v.SQ};function $(){if(typeof navigator>"u"){let e=Intl.DateTimeFormat().resolvedOptions().locale.split("-")[0];return(0,v.x$)(e)||E.ENGLISH}return Array.from(new Set(navigator.languages.map(e=>e.split("-")[0]))).map(e=>(0,v.x$)(e)).filter(e=>e)[0]??E.LOCAL}let I={maptilerLogoURL:"https://api.maptiler.com/resources/logo.svg",maptilerURL:"https://www.maptiler.com/",maptilerApiHost:"api.maptiler.com",telemetryURL:"https://api.maptiler.com/metrics",rtlPluginURL:"https://cdn.maptiler.com/mapbox-gl-rtl-text/v0.2.3/mapbox-gl-rtl-text.min.js",primaryLanguage:E.STYLE,secondaryLanguage:E.LOCAL,terrainSourceURL:"https://api.maptiler.com/tiles/terrain-rgb-v2/tiles.json",terrainSourceId:"maptiler-terrain"};Object.freeze(I);let P=(0,x.Z)();class R extends b{constructor(){super(...arguments),C(this,"primaryLanguage",I.primaryLanguage),C(this,"secondaryLanguage"),C(this,"session",!0),C(this,"caching",!0),C(this,"telemetry",!0),C(this,"_unit","metric"),C(this,"_apiKey","")}set unit(e){this._unit=e,this.emit("unit",e)}get unit(){return this._unit}set apiKey(e){this._apiKey=e,v.vc.apiKey=e,this.emit("apiKey",e)}get apiKey(){return this._apiKey}set fetch(e){v.vc.fetch=e}get fetch(){return v.vc.fetch}}let j=new R,N="localcache_source",O="localcache",F="u">typeof caches,{addProtocol:q}=g;async function D(){return n||(n=await caches.open("maptiler_sdk")),n}let U=0;async function B(){let e=await D(),t=await e.keys();for(let r of t.slice(0,Math.max(t.length-1e3,0)))e.delete(r)}function G(e,t){let r=null;try{r=new URL(e)}catch{return{url:e}}return r.host===I.maptilerApiHost&&(r.searchParams.has("key")||r.searchParams.append("key",j.apiKey),j.session&&r.searchParams.append("mtsid",P)),{url:function(e,t){if(F&&j.caching&&j.session&&e.host===I.maptilerApiHost){if("Source"===t&&e.href.includes("tiles.json"))return e.href.replace("https://",`${N}://`);if("Tile"===t||"Glyphs"===t)return e.href.replace("https://",`${O}://`)}return e.href}(r,t)}}function H(e){return(t,r)=>{if(null!=e){let n=e(t,r),o=G((null==n?void 0:n.url)??"",r);return{...n,...o}}return G(t,r)}}function V(){return Math.random().toString(36).substring(2)}function W(e){return/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/gi.test(e)}function K(){return document.createElement("canvas").getContext("webgl2")?null:"u">typeof WebGL2RenderingContext?"Graphic rendering with WebGL2 has been disabled or is not supported by your graphic card. The map cannot be displayed.":"Your browser does not support graphic rendering with WebGL2. The map cannot be displayed."}function Z(e){let t=e.getContainer(),r=document.createElement("div");r.innerHTML="The WebGL context was lost.",r.classList.add("webgl-warning-div"),t.appendChild(r)}function J(e,t){return!(!Array.isArray(e)||2!==e.length||"get"!==e[0]||"string"!=typeof e[1]||t&&!e[1].startsWith("name:")||!t&&"name"!==e[1])}let Y=class extends g.Marker{addTo(e){return super.addTo(e)}};class X extends g.Popup{addTo(e){return super.addTo(e)}}class Q extends g.Style{constructor(e,t={}){super(e,t)}}class ee extends g.CanvasSource{onAdd(e){super.onAdd(e)}}class et extends g.GeoJSONSource{onAdd(e){super.onAdd(e)}}class er extends g.ImageSource{onAdd(e){super.onAdd(e)}}class en extends g.RasterTileSource{onAdd(e){super.onAdd(e)}}class eo extends g.RasterDEMTileSource{onAdd(e){super.onAdd(e)}}class ea extends g.VectorTileSource{onAdd(e){super.onAdd(e)}}class ei extends g.VideoSource{onAdd(e){super.onAdd(e)}}class el extends g.NavigationControl{onAdd(e){return super.onAdd(e)}}class es extends g.GeolocateControl{onAdd(e){return super.onAdd(e)}}class eu extends g.AttributionControl{onAdd(e){return super.onAdd(e)}}class ep extends g.LogoControl{onAdd(e){return super.onAdd(e)}}class ec extends g.ScaleControl{onAdd(e){return super.onAdd(e)}}class ed extends g.FullscreenControl{onAdd(e){return super.onAdd(e)}}class ef extends g.TerrainControl{onAdd(e){return super.onAdd(e)}}class ey extends g.BoxZoomHandler{constructor(e,t){super(e,t)}}class eh extends g.ScrollZoomHandler{constructor(e,t){super(e,t)}}class em extends g.CooperativeGesturesHandler{constructor(e,t){super(e,t)}}class eg extends g.KeyboardHandler{constructor(e){super(e)}}class ev extends g.TwoFingersTouchPitchHandler{constructor(e){super(e)}}class eb extends g.MapWheelEvent{constructor(e,t,r){super(e,t,r)}}class ex extends g.MapTouchEvent{constructor(e,t,r){super(e,t,r)}}class ew extends g.MapMouseEvent{constructor(e,t,r,n={}){super(e,t,r,n)}}class eS extends ep{constructor(e={}){super(e),C(this,"logoURL",""),C(this,"linkURL",""),this.logoURL=e.logoURL??I.maptilerLogoURL,this.linkURL=e.linkURL??I.maptilerURL}onAdd(e){this._map=e,this._compact=this.options.compact??!1,this._container=window.document.createElement("div"),this._container.className="maplibregl-ctrl";let t=window.document.createElement("a");return t.style.backgroundRepeat="no-repeat",t.style.cursor="pointer",t.style.display="block",t.style.height="23px",t.style.margin="0 0 -4px -4px",t.style.overflow="hidden",t.style.width="88px",t.style.backgroundImage=`url(${this.logoURL})`,t.style.backgroundSize="100px 30px",t.style.width="100px",t.style.height="30px",t.target="_blank",t.rel="noopener",t.href=this.linkURL,t.setAttribute("aria-label","MapTiler logo"),t.setAttribute("rel","noopener"),this._container.appendChild(t),this._container.style.display="block",this._map.on("resize",this._updateCompact),this._updateCompact(),this._container}}var ek={$version:8,$root:{version:{required:!0,type:"enum",values:[8]},name:{type:"string"},metadata:{type:"*"},center:{type:"array",value:"number"},centerAltitude:{type:"number"},zoom:{type:"number"},bearing:{type:"number",default:0,period:360,units:"degrees"},pitch:{type:"number",default:0,units:"degrees"},roll:{type:"number",default:0,units:"degrees"},state:{type:"state",default:{}},light:{type:"light"},sky:{type:"sky"},projection:{type:"projection"},terrain:{type:"terrain"},sources:{required:!0,type:"sources"},sprite:{type:"sprite"},glyphs:{type:"string"},transition:{type:"transition"},layers:{required:!0,type:"array",value:"layer"}},sources:{"*":{type:"source"}},source:["source_vector","source_raster","source_raster_dem","source_geojson","source_video","source_image"],source_vector:{type:{required:!0,type:"enum",values:{vector:{}}},url:{type:"string"},tiles:{type:"array",value:"string"},bounds:{type:"array",value:"number",length:4,default:[-180,-85.051129,180,85.051129]},scheme:{type:"enum",values:{xyz:{},tms:{}},default:"xyz"},minzoom:{type:"number",default:0},maxzoom:{type:"number",default:22},attribution:{type:"string"},promoteId:{type:"promoteId"},volatile:{type:"boolean",default:!1},"*":{type:"*"}},source_raster:{type:{required:!0,type:"enum",values:{raster:{}}},url:{type:"string"},tiles:{type:"array",value:"string"},bounds:{type:"array",value:"number",length:4,default:[-180,-85.051129,180,85.051129]},minzoom:{type:"number",default:0},maxzoom:{type:"number",default:22},tileSize:{type:"number",default:512,units:"pixels"},scheme:{type:"enum",values:{xyz:{},tms:{}},default:"xyz"},attribution:{type:"string"},volatile:{type:"boolean",default:!1},"*":{type:"*"}},source_raster_dem:{type:{required:!0,type:"enum",values:{"raster-dem":{}}},url:{type:"string"},tiles:{type:"array",value:"string"},bounds:{type:"array",value:"number",length:4,default:[-180,-85.051129,180,85.051129]},minzoom:{type:"number",default:0},maxzoom:{type:"number",default:22},tileSize:{type:"number",default:512,units:"pixels"},attribution:{type:"string"},encoding:{type:"enum",values:{terrarium:{},mapbox:{},custom:{}},default:"mapbox"},redFactor:{type:"number",default:1},blueFactor:{type:"number",default:1},greenFactor:{type:"number",default:1},baseShift:{type:"number",default:0},volatile:{type:"boolean",default:!1},"*":{type:"*"}},source_geojson:{type:{required:!0,type:"enum",values:{geojson:{}}},data:{required:!0,type:"*"},maxzoom:{type:"number",default:18},attribution:{type:"string"},buffer:{type:"number",default:128,maximum:512,minimum:0},filter:{type:"*"},tolerance:{type:"number",default:.375},cluster:{type:"boolean",default:!1},clusterRadius:{type:"number",default:50,minimum:0},clusterMaxZoom:{type:"number"},clusterMinPoints:{type:"number"},clusterProperties:{type:"*"},lineMetrics:{type:"boolean",default:!1},generateId:{type:"boolean",default:!1},promoteId:{type:"promoteId"}},source_video:{type:{required:!0,type:"enum",values:{video:{}}},urls:{required:!0,type:"array",value:"string"},coordinates:{required:!0,type:"array",length:4,value:{type:"array",length:2,value:"number"}}},source_image:{type:{required:!0,type:"enum",values:{image:{}}},url:{required:!0,type:"string"},coordinates:{required:!0,type:"array",length:4,value:{type:"array",length:2,value:"number"}}},layer:{id:{type:"string",required:!0},type:{type:"enum",values:{fill:{},line:{},symbol:{},circle:{},heatmap:{},"fill-extrusion":{},raster:{},hillshade:{},"color-relief":{},background:{}},required:!0},metadata:{type:"*"},source:{type:"string"},"source-layer":{type:"string"},minzoom:{type:"number",minimum:0,maximum:24},maxzoom:{type:"number",minimum:0,maximum:24},filter:{type:"filter"},layout:{type:"layout"},paint:{type:"paint"}},layout:["layout_fill","layout_line","layout_circle","layout_heatmap","layout_fill-extrusion","layout_symbol","layout_raster","layout_hillshade","layout_color-relief","layout_background"],layout_background:{visibility:{type:"enum",values:{visible:{},none:{}},default:"visible","property-type":"constant"}},layout_fill:{"fill-sort-key":{type:"number",expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},visibility:{type:"enum",values:{visible:{},none:{}},default:"visible","property-type":"constant"}},layout_circle:{"circle-sort-key":{type:"number",expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},visibility:{type:"enum",values:{visible:{},none:{}},default:"visible","property-type":"constant"}},layout_heatmap:{visibility:{type:"enum",values:{visible:{},none:{}},default:"visible","property-type":"constant"}},"layout_fill-extrusion":{visibility:{type:"enum",values:{visible:{},none:{}},default:"visible","property-type":"constant"}},layout_line:{"line-cap":{type:"enum",values:{butt:{},round:{},square:{}},default:"butt",expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"line-join":{type:"enum",values:{bevel:{},round:{},miter:{}},default:"miter",expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"line-miter-limit":{type:"number",default:2,requires:[{"line-join":"miter"}],expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"line-round-limit":{type:"number",default:1.05,requires:[{"line-join":"round"}],expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"line-sort-key":{type:"number",expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},visibility:{type:"enum",values:{visible:{},none:{}},default:"visible","property-type":"constant"}},layout_symbol:{"symbol-placement":{type:"enum",values:{point:{},line:{},"line-center":{}},default:"point",expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"symbol-spacing":{type:"number",default:250,minimum:1,units:"pixels",requires:[{"symbol-placement":"line"}],expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"symbol-avoid-edges":{type:"boolean",default:!1,expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"symbol-sort-key":{type:"number",expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"symbol-z-order":{type:"enum",values:{auto:{},"viewport-y":{},source:{}},default:"auto",expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"icon-allow-overlap":{type:"boolean",default:!1,requires:["icon-image",{"!":"icon-overlap"}],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"icon-overlap":{type:"enum",values:{never:{},always:{},cooperative:{}},requires:["icon-image"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"icon-ignore-placement":{type:"boolean",default:!1,requires:["icon-image"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"icon-optional":{type:"boolean",default:!1,requires:["icon-image","text-field"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"icon-rotation-alignment":{type:"enum",values:{map:{},viewport:{},auto:{}},default:"auto",requires:["icon-image"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"icon-size":{type:"number",default:1,minimum:0,units:"factor of the original icon size",requires:["icon-image"],expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"icon-text-fit":{type:"enum",values:{none:{},width:{},height:{},both:{}},default:"none",requires:["icon-image","text-field"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"icon-text-fit-padding":{type:"array",value:"number",length:4,default:[0,0,0,0],units:"pixels",requires:["icon-image","text-field",{"icon-text-fit":["both","width","height"]}],expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"icon-image":{type:"resolvedImage",tokens:!0,expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"icon-rotate":{type:"number",default:0,period:360,units:"degrees",requires:["icon-image"],expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"icon-padding":{type:"padding",default:[2],units:"pixels",requires:["icon-image"],expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"icon-keep-upright":{type:"boolean",default:!1,requires:["icon-image",{"icon-rotation-alignment":"map"},{"symbol-placement":["line","line-center"]}],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"icon-offset":{type:"array",value:"number",length:2,default:[0,0],requires:["icon-image"],expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"icon-anchor":{type:"enum",values:{center:{},left:{},right:{},top:{},bottom:{},"top-left":{},"top-right":{},"bottom-left":{},"bottom-right":{}},default:"center",requires:["icon-image"],expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"icon-pitch-alignment":{type:"enum",values:{map:{},viewport:{},auto:{}},default:"auto",requires:["icon-image"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-pitch-alignment":{type:"enum",values:{map:{},viewport:{},auto:{}},default:"auto",requires:["text-field"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-rotation-alignment":{type:"enum",values:{map:{},viewport:{},"viewport-glyph":{},auto:{}},default:"auto",requires:["text-field"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-field":{type:"formatted",default:"",tokens:!0,expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-font":{type:"array",value:"string",default:["Open Sans Regular","Arial Unicode MS Regular"],requires:["text-field"],expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-size":{type:"number",default:16,minimum:0,units:"pixels",requires:["text-field"],expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-max-width":{type:"number",default:10,minimum:0,units:"ems",requires:["text-field"],expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-line-height":{type:"number",default:1.2,units:"ems",requires:["text-field"],expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"text-letter-spacing":{type:"number",default:0,units:"ems",requires:["text-field"],expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-justify":{type:"enum",values:{auto:{},left:{},center:{},right:{}},default:"center",requires:["text-field"],expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-radial-offset":{type:"number",units:"ems",default:0,requires:["text-field"],"property-type":"data-driven",expression:{interpolated:!0,parameters:["zoom","feature"]}},"text-variable-anchor":{type:"array",value:"enum",values:{center:{},left:{},right:{},top:{},bottom:{},"top-left":{},"top-right":{},"bottom-left":{},"bottom-right":{}},requires:["text-field",{"symbol-placement":["point"]}],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-variable-anchor-offset":{type:"variableAnchorOffsetCollection",requires:["text-field",{"symbol-placement":["point"]}],expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-anchor":{type:"enum",values:{center:{},left:{},right:{},top:{},bottom:{},"top-left":{},"top-right":{},"bottom-left":{},"bottom-right":{}},default:"center",requires:["text-field",{"!":"text-variable-anchor"}],expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-max-angle":{type:"number",default:45,units:"degrees",requires:["text-field",{"symbol-placement":["line","line-center"]}],expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"text-writing-mode":{type:"array",value:"enum",values:{horizontal:{},vertical:{}},requires:["text-field",{"symbol-placement":["point"]}],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-rotate":{type:"number",default:0,period:360,units:"degrees",requires:["text-field"],expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-padding":{type:"number",default:2,minimum:0,units:"pixels",requires:["text-field"],expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"text-keep-upright":{type:"boolean",default:!0,requires:["text-field",{"text-rotation-alignment":"map"},{"symbol-placement":["line","line-center"]}],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-transform":{type:"enum",values:{none:{},uppercase:{},lowercase:{}},default:"none",requires:["text-field"],expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-offset":{type:"array",value:"number",units:"ems",length:2,default:[0,0],requires:["text-field",{"!":"text-radial-offset"}],expression:{interpolated:!0,parameters:["zoom","feature"]},"property-type":"data-driven"},"text-allow-overlap":{type:"boolean",default:!1,requires:["text-field",{"!":"text-overlap"}],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-overlap":{type:"enum",values:{never:{},always:{},cooperative:{}},requires:["text-field"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-ignore-placement":{type:"boolean",default:!1,requires:["text-field"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-optional":{type:"boolean",default:!1,requires:["text-field","icon-image"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},visibility:{type:"enum",values:{visible:{},none:{}},default:"visible","property-type":"constant"}},layout_raster:{visibility:{type:"enum",values:{visible:{},none:{}},default:"visible","property-type":"constant"}},layout_hillshade:{visibility:{type:"enum",values:{visible:{},none:{}},default:"visible","property-type":"constant"}},"layout_color-relief":{visibility:{type:"enum",values:{visible:{},none:{}},default:"visible","property-type":"constant"}},filter:{type:"array",value:"*"},filter_operator:{type:"enum",values:{"==":{},"!=":{},">":{},">=":{},"<":{},"<=":{},in:{},"!in":{},all:{},any:{},none:{},has:{},"!has":{}}},geometry_type:{type:"enum",values:{Point:{},LineString:{},Polygon:{}}},function:{expression:{type:"expression"},stops:{type:"array",value:"function_stop"},base:{type:"number",default:1,minimum:0},property:{type:"string",default:"$zoom"},type:{type:"enum",values:{identity:{},exponential:{},interval:{},categorical:{}},default:"exponential"},colorSpace:{type:"enum",values:{rgb:{},lab:{},hcl:{}},default:"rgb"},default:{type:"*",required:!1}},function_stop:{type:"array",minimum:0,maximum:24,value:["number","color"],length:2},expression:{type:"array",value:"*",minimum:1},light:{anchor:{type:"enum",default:"viewport",values:{map:{},viewport:{}},"property-type":"data-constant",transition:!1,expression:{interpolated:!1,parameters:["zoom"]}},position:{type:"array",default:[1.15,210,30],length:3,value:"number","property-type":"data-constant",transition:!0,expression:{interpolated:!0,parameters:["zoom"]}},color:{type:"color","property-type":"data-constant",default:"#ffffff",expression:{interpolated:!0,parameters:["zoom"]},transition:!0},intensity:{type:"number","property-type":"data-constant",default:.5,minimum:0,maximum:1,expression:{interpolated:!0,parameters:["zoom"]},transition:!0}},sky:{"sky-color":{type:"color","property-type":"data-constant",default:"#88C6FC",expression:{interpolated:!0,parameters:["zoom"]},transition:!0},"horizon-color":{type:"color","property-type":"data-constant",default:"#ffffff",expression:{interpolated:!0,parameters:["zoom"]},transition:!0},"fog-color":{type:"color","property-type":"data-constant",default:"#ffffff",expression:{interpolated:!0,parameters:["zoom"]},transition:!0},"fog-ground-blend":{type:"number","property-type":"data-constant",default:.5,minimum:0,maximum:1,expression:{interpolated:!0,parameters:["zoom"]},transition:!0},"horizon-fog-blend":{type:"number","property-type":"data-constant",default:.8,minimum:0,maximum:1,expression:{interpolated:!0,parameters:["zoom"]},transition:!0},"sky-horizon-blend":{type:"number","property-type":"data-constant",default:.8,minimum:0,maximum:1,expression:{interpolated:!0,parameters:["zoom"]},transition:!0},"atmosphere-blend":{type:"number","property-type":"data-constant",default:.8,minimum:0,maximum:1,expression:{interpolated:!0,parameters:["zoom"]},transition:!0}},terrain:{source:{type:"string",required:!0},exaggeration:{type:"number",minimum:0,default:1}},projection:{type:{type:"projectionDefinition",default:"mercator","property-type":"data-constant",transition:!1,expression:{interpolated:!0,parameters:["zoom"]}}},paint:["paint_fill","paint_line","paint_circle","paint_heatmap","paint_fill-extrusion","paint_symbol","paint_raster","paint_hillshade","paint_color-relief","paint_background"],paint_fill:{"fill-antialias":{type:"boolean",default:!0,expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"fill-opacity":{type:"number",default:1,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"fill-color":{type:"color",default:"#000000",transition:!0,requires:[{"!":"fill-pattern"}],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"fill-outline-color":{type:"color",transition:!0,requires:[{"!":"fill-pattern"},{"fill-antialias":!0}],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"fill-translate":{type:"array",value:"number",length:2,default:[0,0],transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"fill-translate-anchor":{type:"enum",values:{map:{},viewport:{}},default:"map",requires:["fill-translate"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"fill-pattern":{type:"resolvedImage",transition:!0,expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"cross-faded-data-driven"}},"paint_fill-extrusion":{"fill-extrusion-opacity":{type:"number",default:1,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"fill-extrusion-color":{type:"color",default:"#000000",transition:!0,requires:[{"!":"fill-extrusion-pattern"}],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"fill-extrusion-translate":{type:"array",value:"number",length:2,default:[0,0],transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"fill-extrusion-translate-anchor":{type:"enum",values:{map:{},viewport:{}},default:"map",requires:["fill-extrusion-translate"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"fill-extrusion-pattern":{type:"resolvedImage",transition:!0,expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"cross-faded-data-driven"},"fill-extrusion-height":{type:"number",default:0,minimum:0,units:"meters",transition:!0,expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"fill-extrusion-base":{type:"number",default:0,minimum:0,units:"meters",transition:!0,requires:["fill-extrusion-height"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"fill-extrusion-vertical-gradient":{type:"boolean",default:!0,transition:!1,expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"}},paint_line:{"line-opacity":{type:"number",default:1,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"line-color":{type:"color",default:"#000000",transition:!0,requires:[{"!":"line-pattern"}],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"line-translate":{type:"array",value:"number",length:2,default:[0,0],transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"line-translate-anchor":{type:"enum",values:{map:{},viewport:{}},default:"map",requires:["line-translate"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"line-width":{type:"number",default:1,minimum:0,transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"line-gap-width":{type:"number",default:0,minimum:0,transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"line-offset":{type:"number",default:0,transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"line-blur":{type:"number",default:0,minimum:0,transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"line-dasharray":{type:"array",value:"number",minimum:0,transition:!0,units:"line widths",requires:[{"!":"line-pattern"}],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"cross-faded"},"line-pattern":{type:"resolvedImage",transition:!0,expression:{interpolated:!1,parameters:["zoom","feature"]},"property-type":"cross-faded-data-driven"},"line-gradient":{type:"color",transition:!1,requires:[{"!":"line-dasharray"},{"!":"line-pattern"},{source:"geojson",has:{lineMetrics:!0}}],expression:{interpolated:!0,parameters:["line-progress"]},"property-type":"color-ramp"}},paint_circle:{"circle-radius":{type:"number",default:5,minimum:0,transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"circle-color":{type:"color",default:"#000000",transition:!0,expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"circle-blur":{type:"number",default:0,transition:!0,expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"circle-opacity":{type:"number",default:1,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"circle-translate":{type:"array",value:"number",length:2,default:[0,0],transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"circle-translate-anchor":{type:"enum",values:{map:{},viewport:{}},default:"map",requires:["circle-translate"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"circle-pitch-scale":{type:"enum",values:{map:{},viewport:{}},default:"map",expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"circle-pitch-alignment":{type:"enum",values:{map:{},viewport:{}},default:"viewport",expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"circle-stroke-width":{type:"number",default:0,minimum:0,transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"circle-stroke-color":{type:"color",default:"#000000",transition:!0,expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"circle-stroke-opacity":{type:"number",default:1,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"}},paint_heatmap:{"heatmap-radius":{type:"number",default:30,minimum:1,transition:!0,units:"pixels",expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"heatmap-weight":{type:"number",default:1,minimum:0,transition:!1,expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"heatmap-intensity":{type:"number",default:1,minimum:0,transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"heatmap-color":{type:"color",default:["interpolate",["linear"],["heatmap-density"],0,"rgba(0, 0, 255, 0)",.1,"royalblue",.3,"cyan",.5,"lime",.7,"yellow",1,"red"],transition:!1,expression:{interpolated:!0,parameters:["heatmap-density"]},"property-type":"color-ramp"},"heatmap-opacity":{type:"number",default:1,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"}},paint_symbol:{"icon-opacity":{type:"number",default:1,minimum:0,maximum:1,transition:!0,requires:["icon-image"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"icon-color":{type:"color",default:"#000000",transition:!0,requires:["icon-image"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"icon-halo-color":{type:"color",default:"rgba(0, 0, 0, 0)",transition:!0,requires:["icon-image"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"icon-halo-width":{type:"number",default:0,minimum:0,transition:!0,units:"pixels",requires:["icon-image"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"icon-halo-blur":{type:"number",default:0,minimum:0,transition:!0,units:"pixels",requires:["icon-image"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"icon-translate":{type:"array",value:"number",length:2,default:[0,0],transition:!0,units:"pixels",requires:["icon-image"],expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"icon-translate-anchor":{type:"enum",values:{map:{},viewport:{}},default:"map",requires:["icon-image","icon-translate"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"text-opacity":{type:"number",default:1,minimum:0,maximum:1,transition:!0,requires:["text-field"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"text-color":{type:"color",default:"#000000",transition:!0,overridable:!0,requires:["text-field"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"text-halo-color":{type:"color",default:"rgba(0, 0, 0, 0)",transition:!0,requires:["text-field"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"text-halo-width":{type:"number",default:0,minimum:0,transition:!0,units:"pixels",requires:["text-field"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"text-halo-blur":{type:"number",default:0,minimum:0,transition:!0,units:"pixels",requires:["text-field"],expression:{interpolated:!0,parameters:["zoom","feature","feature-state"]},"property-type":"data-driven"},"text-translate":{type:"array",value:"number",length:2,default:[0,0],transition:!0,units:"pixels",requires:["text-field"],expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"text-translate-anchor":{type:"enum",values:{map:{},viewport:{}},default:"map",requires:["text-field","text-translate"],expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"}},paint_raster:{"raster-opacity":{type:"number",default:1,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"raster-hue-rotate":{type:"number",default:0,period:360,transition:!0,units:"degrees",expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"raster-brightness-min":{type:"number",default:0,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"raster-brightness-max":{type:"number",default:1,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"raster-saturation":{type:"number",default:0,minimum:-1,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"raster-contrast":{type:"number",default:0,minimum:-1,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"raster-resampling":{type:"enum",values:{linear:{},nearest:{}},default:"linear",expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"raster-fade-duration":{type:"number",default:300,minimum:0,transition:!1,units:"milliseconds",expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"}},paint_hillshade:{"hillshade-illumination-direction":{type:"numberArray",default:335,minimum:0,maximum:359,transition:!1,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"hillshade-illumination-altitude":{type:"numberArray",default:45,minimum:0,maximum:90,transition:!1,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"hillshade-illumination-anchor":{type:"enum",values:{map:{},viewport:{}},default:"viewport",expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"},"hillshade-exaggeration":{type:"number",default:.5,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"hillshade-shadow-color":{type:"colorArray",default:"#000000",transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"hillshade-highlight-color":{type:"colorArray",default:"#FFFFFF",transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"hillshade-accent-color":{type:"color",default:"#000000",transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"hillshade-method":{type:"enum",values:{standard:{},basic:{},combined:{},igor:{},multidirectional:{}},default:"standard",expression:{interpolated:!1,parameters:["zoom"]},"property-type":"data-constant"}},"paint_color-relief":{"color-relief-opacity":{type:"number",default:1,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"color-relief-color":{type:"color",transition:!1,expression:{interpolated:!0,parameters:["elevation"]},"property-type":"color-ramp"}},paint_background:{"background-color":{type:"color",default:"#000000",transition:!0,requires:[{"!":"background-pattern"}],expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"},"background-pattern":{type:"resolvedImage",transition:!0,expression:{interpolated:!1,parameters:["zoom"]},"property-type":"cross-faded"},"background-opacity":{type:"number",default:1,minimum:0,maximum:1,transition:!0,expression:{interpolated:!0,parameters:["zoom"]},"property-type":"data-constant"}},transition:{duration:{type:"number",default:300,minimum:0,units:"milliseconds"},delay:{type:"number",default:0,minimum:0,units:"milliseconds"}},"property-type":{"data-driven":{type:"property-type"},"cross-faded":{type:"property-type"},"cross-faded-data-driven":{type:"property-type"},"color-ramp":{type:"property-type"},"data-constant":{type:"property-type"},constant:{type:"property-type"}},promoteId:{"*":{type:"string"}}};class eL{constructor(e,t,r,n){this.message=(e?`${e}: `:"")+r,n&&(this.identifier=n),null!=t&&t.__line__&&(this.line=t.__line__)}}function eC(e,...t){for(let r of t)for(let t in r)e[t]=r[t];return e}class eA extends Error{constructor(e,t){super(t),this.message=t,this.key=e}}class e_{constructor(e,t=[]){for(let[r,n]of(this.parent=e,this.bindings={},t))this.bindings[r]=n}concat(e){return new e_(this,e)}get(e){if(this.bindings[e])return this.bindings[e];if(this.parent)return this.parent.get(e);throw Error(`${e} not found in scope.`)}has(e){return!!this.bindings[e]||!!this.parent&&this.parent.has(e)}}let ez={kind:"null"},eM={kind:"number"},eT={kind:"string"},eE={kind:"boolean"},e$={kind:"color"},eI={kind:"projectionDefinition"},eP={kind:"object"},eR={kind:"value"},ej={kind:"collator"},eN={kind:"formatted"},eO={kind:"padding"},eF={kind:"colorArray"},eq={kind:"numberArray"},eD={kind:"resolvedImage"},eU={kind:"variableAnchorOffsetCollection"};function eB(e,t){return{kind:"array",itemType:e,N:t}}function eG(e){if("array"!==e.kind)return e.kind;{let t=eG(e.itemType);return"number"==typeof e.N?`array<${t}, ${e.N}>`:"value"===e.itemType.kind?"array":`array<${t}>`}}let eH=[ez,eM,eT,eE,e$,eI,eN,eP,eB(eR),eO,eq,eF,eD,eU];function eV(e,t){if("error"===t.kind)return null;if("array"===e.kind){if("array"===t.kind&&(0===t.N&&"value"===t.itemType.kind||!eV(e.itemType,t.itemType))&&("number"!=typeof e.N||e.N===t.N))return null}else{if(e.kind===t.kind)return null;if("value"===e.kind){for(let e of eH)if(!eV(e,t))return null}}return`Expected ${eG(e)} but found ${eG(t)} instead.`}function eW(e,t){return t.some(t=>t.kind===e.kind)}function eK(e,t){return t.some(t=>"null"===t?null===e:"array"===t?Array.isArray(e):"object"===t?e&&!Array.isArray(e)&&"object"==typeof e:t===typeof e)}function eZ(e,t){return"array"===e.kind&&"array"===t.kind?e.itemType.kind===t.itemType.kind&&"number"==typeof e.N:e.kind===t.kind}let eJ=4/29,eY=6/29,eX=6/29*3*(6/29),eQ=6/29*(6/29)*(6/29),e0=Math.PI/180,e1=180/Math.PI;function e2(e){return(e%=360)<0&&(e+=360),e}function e5([e,t,r,n]){let o,a;let i=e4((.2225045*(e=e3(e))+.7168786*(t=e3(t))+.0606169*(r=e3(r)))/1);e===t&&t===r?o=a=i:(o=e4((.4360747*e+.3850649*t+.1430804*r)/.96422),a=e4((.0139322*e+.0971045*t+.7141733*r)/.82521));let l=116*i-16;return[l<0?0:l,500*(o-i),200*(i-a),n]}function e3(e){return e<=.04045?e/12.92:Math.pow((e+.055)/1.055,2.4)}function e4(e){return e>eQ?Math.pow(e,1/3):e/eX+eJ}function e8([e,t,r,n]){let o=(e+16)/116,a=isNaN(t)?o:o+t/500,i=isNaN(r)?o:o-r/200;return o=1*e9(o),[e6(3.1338561*(a=.96422*e9(a))-1.6168667*o-.4906146*(i=.82521*e9(i))),e6(-.9787684*a+1.9161415*o+.033454*i),e6(.0719453*a-.2289914*o+1.4052427*i),n]}function e6(e){return(e=e<=.00304?12.92*e:1.055*Math.pow(e,1/2.4)-.055)<0?0:e>1?1:e}function e9(e){return e>eY?e*e*e:eX*(e-eJ)}let e7=Object.hasOwn||function(e,t){return Object.prototype.hasOwnProperty.call(e,t)};function te(e,t){return e7(e,t)?e[t]:void 0}function tt(e){return parseInt(e.padEnd(2,e),16)/255}function tr(e,t){return tn(t?e/100:e,0,1)}function tn(e,t,r){return Math.min(Math.max(t,e),r)}function to(e){return!e.some(Number.isNaN)}let ta={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]};function ti(e,t,r){return e.map((e,n)=>e+r*(t[n]-e))}class tl{constructor(e,t,r,n=1,o=!0){this.r=e,this.g=t,this.b=r,this.a=n,o||(this.r*=n,this.g*=n,this.b*=n,n||this.overwriteGetter("rgb",[e,t,r,n]))}static parse(e){if(e instanceof tl)return e;if("string"!=typeof e)return;let t=function(e){if("transparent"===(e=e.toLowerCase().trim()))return[0,0,0,0];let t=te(ta,e);if(t){let[e,r,n]=t;return[e/255,r/255,n/255,1]}if(e.startsWith("#")&&/^#(?:[0-9a-f]{3,4}|[0-9a-f]{6}|[0-9a-f]{8})$/.test(e)){let t=e.length<6?1:2,r=1;return[tt(e.slice(r,r+=t)),tt(e.slice(r,r+=t)),tt(e.slice(r,r+=t)),tt(e.slice(r,r+t)||"ff")]}if(e.startsWith("rgb")){let t=e.match(/^rgba?\(\s*([\de.+-]+)(%)?(?:\s+|\s*(,)\s*)([\de.+-]+)(%)?(?:\s+|\s*(,)\s*)([\de.+-]+)(%)?(?:\s*([,\/])\s*([\de.+-]+)(%)?)?\s*\)$/);if(t){let[e,r,n,o,a,i,l,s,u,p,c,d]=t,f=[o||" ",l||" ",p].join("");if("  "===f||"  /"===f||",,"===f||",,,"===f){let e=[n,i,u].join(""),t="%%%"===e?100:""===e?255:0;if(t){let e=[tn(+r/t,0,1),tn(+a/t,0,1),tn(+s/t,0,1),c?tr(+c,d):1];if(to(e))return e}}return}}let r=e.match(/^hsla?\(\s*([\de.+-]+)(?:deg)?(?:\s+|\s*(,)\s*)([\de.+-]+)%(?:\s+|\s*(,)\s*)([\de.+-]+)%(?:\s*([,\/])\s*([\de.+-]+)(%)?)?\s*\)$/);if(r){let[e,t,n,o,a,i,l,s,u]=r,p=[n||" ",a||" ",l].join("");if("  "===p||"  /"===p||",,"===p||",,,"===p){let e=[+t,tn(+o,0,100),tn(+i,0,100),s?tr(+s,u):1];if(to(e))return function([e,t,r,n]){function o(n){let o=(n+e/30)%12,a=t*Math.min(r,1-r);return r-a*Math.max(-1,Math.min(o-3,9-o,1))}return e=e2(e),t/=100,r/=100,[o(0),o(8),o(4),n]}(e)}}}(e);if(t)return new tl(...t,!1)}get rgb(){let{r:e,g:t,b:r,a:n}=this,o=n||1/0;return this.overwriteGetter("rgb",[e/o,t/o,r/o,n])}get hcl(){return this.overwriteGetter("hcl",function(e){let[t,r,n,o]=e5(e),a=Math.sqrt(r*r+n*n);return[Math.round(1e4*a)?e2(Math.atan2(n,r)*e1):NaN,a,t,o]}(this.rgb))}get lab(){return this.overwriteGetter("lab",e5(this.rgb))}overwriteGetter(e,t){return Object.defineProperty(this,e,{value:t}),t}toString(){let[e,t,r,n]=this.rgb;return`rgba(${[e,t,r].map(e=>Math.round(255*e)).join(",")},${n})`}static interpolate(e,t,r,n="rgb"){switch(n){case"rgb":{let[n,o,a,i]=ti(e.rgb,t.rgb,r);return new tl(n,o,a,i,!1)}case"hcl":{let n,o;let[a,i,l,s]=e.hcl,[u,p,c,d]=t.hcl;if(isNaN(a)||isNaN(u))isNaN(a)?isNaN(u)?n=NaN:(n=u,(1===l||0===l)&&(o=p)):(n=a,(1===c||0===c)&&(o=i));else{let e=u-a;u>a&&e>180?e-=360:u<a&&a-u>180&&(e+=360),n=a+r*e}let[f,y,h,m]=function([e,t,r,n]){return e8([r,Math.cos(e=isNaN(e)?0:e*e0)*t,Math.sin(e)*t,n])}([n,o??i+r*(p-i),l+r*(c-l),s+r*(d-s)]);return new tl(f,y,h,m,!1)}case"lab":{let[n,o,a,i]=e8(ti(e.lab,t.lab,r));return new tl(n,o,a,i,!1)}}}}tl.black=new tl(0,0,0,1),tl.white=new tl(1,1,1,1),tl.transparent=new tl(0,0,0,0),tl.red=new tl(1,0,0,1);class ts{constructor(e,t,r){e?this.sensitivity=t?"variant":"case":this.sensitivity=t?"accent":"base",this.locale=r,this.collator=new Intl.Collator(this.locale?this.locale:[],{sensitivity:this.sensitivity,usage:"search"})}compare(e,t){return this.collator.compare(e,t)}resolvedLocale(){return new Intl.Collator(this.locale?this.locale:[]).resolvedOptions().locale}}let tu=["bottom","center","top"];class tp{constructor(e,t,r,n,o,a){this.text=e,this.image=t,this.scale=r,this.fontStack=n,this.textColor=o,this.verticalAlign=a}}class tc{constructor(e){this.sections=e}static fromString(e){return new tc([new tp(e,null,null,null,null,null)])}isEmpty(){return 0===this.sections.length||!this.sections.some(e=>0!==e.text.length||e.image&&0!==e.image.name.length)}static factory(e){return e instanceof tc?e:tc.fromString(e)}toString(){return 0===this.sections.length?"":this.sections.map(e=>e.text).join("")}}class td{constructor(e){this.values=e.slice()}static parse(e){if(e instanceof td)return e;if("number"==typeof e)return new td([e,e,e,e]);if(Array.isArray(e)&&!(e.length<1||e.length>4)){for(let t of e)if("number"!=typeof t)return;switch(e.length){case 1:e=[e[0],e[0],e[0],e[0]];break;case 2:e=[e[0],e[1],e[0],e[1]];break;case 3:e=[e[0],e[1],e[2],e[1]]}return new td(e)}}toString(){return JSON.stringify(this.values)}static interpolate(e,t,r){return new td(ti(e.values,t.values,r))}}class tf{constructor(e){this.values=e.slice()}static parse(e){if(e instanceof tf)return e;if("number"==typeof e)return new tf([e]);if(Array.isArray(e)){for(let t of e)if("number"!=typeof t)return;return new tf(e)}}toString(){return JSON.stringify(this.values)}static interpolate(e,t,r){return new tf(ti(e.values,t.values,r))}}class ty{constructor(e){this.values=e.slice()}static parse(e){if(e instanceof ty)return e;if("string"==typeof e){let t=tl.parse(e);return t?new ty([t]):void 0}if(!Array.isArray(e))return;let t=[];for(let r of e){if("string"!=typeof r)return;let e=tl.parse(r);if(!e)return;t.push(e)}return new ty(t)}toString(){return JSON.stringify(this.values)}static interpolate(e,t,r,n="rgb"){let o=[];if(e.values.length!=t.values.length)throw Error(`colorArray: Arrays have mismatched length (${e.values.length} vs. ${t.values.length}), cannot interpolate.`);for(let a=0;a<e.values.length;a++)o.push(tl.interpolate(e.values[a],t.values[a],r,n));return new ty(o)}}class th extends Error{constructor(e){super(e),this.name="RuntimeError"}toJSON(){return this.message}}let tm=new Set(["center","left","right","top","bottom","top-left","top-right","bottom-left","bottom-right"]);class tg{constructor(e){this.values=e.slice()}static parse(e){if(e instanceof tg)return e;if(!(!Array.isArray(e)||e.length<1||e.length%2!=0)){for(let t=0;t<e.length;t+=2){let r=e[t],n=e[t+1];if("string"!=typeof r||!tm.has(r)||!Array.isArray(n)||2!==n.length||"number"!=typeof n[0]||"number"!=typeof n[1])return}return new tg(e)}}toString(){return JSON.stringify(this.values)}static interpolate(e,t,r){let n=e.values,o=t.values;if(n.length!==o.length)throw new th(`Cannot interpolate values of different length. from: ${e.toString()}, to: ${t.toString()}`);let a=[];for(let e=0;e<n.length;e+=2){if(n[e]!==o[e])throw new th(`Cannot interpolate values containing mismatched anchors. from[${e}]: ${n[e]}, to[${e}]: ${o[e]}`);a.push(n[e]);let[t,i]=n[e+1],[l,s]=o[e+1];a.push([t+r*(l-t),i+r*(s-i)])}return new tg(a)}}class tv{constructor(e){this.name=e.name,this.available=e.available}toString(){return this.name}static fromString(e){return e?new tv({name:e,available:!1}):null}}class tb{constructor(e,t,r){this.from=e,this.to=t,this.transition=r}static interpolate(e,t,r){return new tb(e,t,r)}static parse(e){return e instanceof tb?e:Array.isArray(e)&&3===e.length&&"string"==typeof e[0]&&"string"==typeof e[1]&&"number"==typeof e[2]?new tb(e[0],e[1],e[2]):"object"==typeof e&&"string"==typeof e.from&&"string"==typeof e.to&&"number"==typeof e.transition?new tb(e.from,e.to,e.transition):"string"==typeof e?new tb(e,e,1):void 0}}function tx(e,t,r,n){return"number"==typeof e&&e>=0&&e<=255&&"number"==typeof t&&t>=0&&t<=255&&"number"==typeof r&&r>=0&&r<=255?typeof n>"u"||"number"==typeof n&&n>=0&&n<=1?null:`Invalid rgba value [${[e,t,r,n].join(", ")}]: 'a' must be between 0 and 1.`:`Invalid rgba value [${("number"==typeof n?[e,t,r,n]:[e,t,r]).join(", ")}]: 'r', 'g', and 'b' must be between 0 and 255.`}function tw(e){if(null===e||"string"==typeof e||"boolean"==typeof e||"number"==typeof e||e instanceof tb||e instanceof tl||e instanceof ts||e instanceof tc||e instanceof td||e instanceof tf||e instanceof ty||e instanceof tg||e instanceof tv)return!0;if(Array.isArray(e)){for(let t of e)if(!tw(t))return!1;return!0}if("object"!=typeof e)return!1;for(let t in e)if(!tw(e[t]))return!1;return!0}function tS(e){if(null===e)return ez;if("string"==typeof e)return eT;if("boolean"==typeof e)return eE;if("number"==typeof e)return eM;if(e instanceof tl)return e$;if(e instanceof tb)return eI;if(e instanceof ts)return ej;if(e instanceof tc)return eN;if(e instanceof td)return eO;if(e instanceof tf)return eq;if(e instanceof ty)return eF;if(e instanceof tg)return eU;if(e instanceof tv)return eD;if(!Array.isArray(e))return eP;{let t;let r=e.length;for(let r of e){let e=tS(r);if(t){if(t===e)continue;t=eR;break}t=e}return eB(t||eR,r)}}function tk(e){let t=typeof e;return null===e?"":"string"===t||"number"===t||"boolean"===t?String(e):e instanceof tl||e instanceof tb||e instanceof tc||e instanceof td||e instanceof tf||e instanceof ty||e instanceof tg||e instanceof tv?e.toString():JSON.stringify(e)}class tL{constructor(e,t){this.type=e,this.value=t}static parse(e,t){if(2!==e.length)return t.error(`'literal' expression requires exactly one argument, but found ${e.length-1} instead.`);if(!tw(e[1]))return t.error("invalid value");let r=e[1],n=tS(r),o=t.expectedType;return"array"===n.kind&&0===n.N&&o&&"array"===o.kind&&("number"!=typeof o.N||0===o.N)&&(n=o),new tL(n,r)}evaluate(){return this.value}eachChild(){}outputDefined(){return!0}}let tC={string:eT,number:eM,boolean:eE,object:eP};class tA{constructor(e,t){this.type=e,this.args=t}static parse(e,t){if(e.length<2)return t.error("Expected at least one argument.");let r=1,n,o=e[0];if("array"===o){let o,a;if(e.length>2){let n=e[1];if("string"!=typeof n||!(n in tC)||"object"===n)return t.error('The item type argument of "array" must be one of string, number, boolean',1);o=tC[n],r++}else o=eR;if(e.length>3){if(null!==e[2]&&("number"!=typeof e[2]||e[2]<0||e[2]!==Math.floor(e[2])))return t.error('The length argument to "array" must be a positive integer literal',2);a=e[2],r++}n=eB(o,a)}else{if(!tC[o])throw Error(`Types doesn't contain name = ${o}`);n=tC[o]}let a=[];for(;r<e.length;r++){let n=t.parse(e[r],r,eR);if(!n)return null;a.push(n)}return new tA(n,a)}evaluate(e){for(let t=0;t<this.args.length;t++){let r=this.args[t].evaluate(e);if(!eV(this.type,tS(r)))return r;if(t===this.args.length-1)throw new th(`Expected value to be of type ${eG(this.type)}, but found ${eG(tS(r))} instead.`)}throw Error()}eachChild(e){this.args.forEach(e)}outputDefined(){return this.args.every(e=>e.outputDefined())}}let t_={"to-boolean":eE,"to-color":e$,"to-number":eM,"to-string":eT};class tz{constructor(e,t){this.type=e,this.args=t}static parse(e,t){if(e.length<2)return t.error("Expected at least one argument.");let r=e[0];if(!t_[r])throw Error(`Can't parse ${r} as it is not part of the known types`);if(("to-boolean"===r||"to-string"===r)&&2!==e.length)return t.error("Expected one argument.");let n=t_[r],o=[];for(let r=1;r<e.length;r++){let n=t.parse(e[r],r,eR);if(!n)return null;o.push(n)}return new tz(n,o)}evaluate(e){switch(this.type.kind){case"boolean":return!!this.args[0].evaluate(e);case"color":{let t,r;for(let n of this.args){if(t=n.evaluate(e),r=null,t instanceof tl)return t;if("string"==typeof t){let r=e.parseColor(t);if(r)return r}else if(Array.isArray(t)&&!(r=t.length<3||t.length>4?`Invalid rgba value ${JSON.stringify(t)}: expected an array containing either three or four numeric values.`:tx(t[0],t[1],t[2],t[3])))return new tl(t[0]/255,t[1]/255,t[2]/255,t[3])}throw new th(r||`Could not parse color from value '${"string"==typeof t?t:JSON.stringify(t)}'`)}case"padding":{let t;for(let r of this.args){t=r.evaluate(e);let n=td.parse(t);if(n)return n}throw new th(`Could not parse padding from value '${"string"==typeof t?t:JSON.stringify(t)}'`)}case"numberArray":{let t;for(let r of this.args){t=r.evaluate(e);let n=tf.parse(t);if(n)return n}throw new th(`Could not parse numberArray from value '${"string"==typeof t?t:JSON.stringify(t)}'`)}case"colorArray":{let t;for(let r of this.args){t=r.evaluate(e);let n=ty.parse(t);if(n)return n}throw new th(`Could not parse colorArray from value '${"string"==typeof t?t:JSON.stringify(t)}'`)}case"variableAnchorOffsetCollection":{let t;for(let r of this.args){t=r.evaluate(e);let n=tg.parse(t);if(n)return n}throw new th(`Could not parse variableAnchorOffsetCollection from value '${"string"==typeof t?t:JSON.stringify(t)}'`)}case"number":{let t=null;for(let r of this.args){if(null===(t=r.evaluate(e)))return 0;let n=Number(t);if(!isNaN(n))return n}throw new th(`Could not convert ${JSON.stringify(t)} to number.`)}case"formatted":return tc.fromString(tk(this.args[0].evaluate(e)));case"resolvedImage":return tv.fromString(tk(this.args[0].evaluate(e)));case"projectionDefinition":return this.args[0].evaluate(e);default:return tk(this.args[0].evaluate(e))}}eachChild(e){this.args.forEach(e)}outputDefined(){return this.args.every(e=>e.outputDefined())}}let tM=["Unknown","Point","LineString","Polygon"];class tT{constructor(){this.globals=null,this.feature=null,this.featureState=null,this.formattedSection=null,this._parseColorCache=new Map,this.availableImages=null,this.canonical=null}id(){return this.feature&&"id"in this.feature?this.feature.id:null}geometryType(){return this.feature?"number"==typeof this.feature.type?tM[this.feature.type]:this.feature.type:null}geometry(){return this.feature&&"geometry"in this.feature?this.feature.geometry:null}canonicalID(){return this.canonical}properties(){return this.feature&&this.feature.properties||{}}parseColor(e){let t=this._parseColorCache.get(e);return t||(t=tl.parse(e),this._parseColorCache.set(e,t)),t}}class tE{constructor(e,t,r=[],n,o=new e_,a=[]){this.registry=e,this.path=r,this.key=r.map(e=>`[${e}]`).join(""),this.scope=o,this.errors=a,this.expectedType=n,this._isConstant=t}parse(e,t,r,n,o={}){return t?this.concat(t,r,n)._parse(e,o):this._parse(e,o)}_parse(e,t){function r(e,t,r){return"assert"===r?new tA(t,[e]):"coerce"===r?new tz(t,[e]):e}if((null===e||"string"==typeof e||"boolean"==typeof e||"number"==typeof e)&&(e=["literal",e]),!Array.isArray(e))return typeof e>"u"?this.error("'undefined' value invalid. Use null instead."):"object"==typeof e?this.error('Bare objects invalid. Use ["literal", {...}] instead.'):this.error(`Expected an array, but found ${typeof e} instead.`);{if(0===e.length)return this.error('Expected an array with at least one element. If you wanted a literal array, use ["literal", []].');let n=e[0];if("string"!=typeof n)return this.error(`Expression name must be a string, but found ${typeof n} instead. If you wanted a literal array, use ["literal", [...]].`,0),null;let o=this.registry[n];if(o){let n=o.parse(e,this);if(!n)return null;if(this.expectedType){let e=this.expectedType,o=n.type;if(("string"===e.kind||"number"===e.kind||"boolean"===e.kind||"object"===e.kind||"array"===e.kind)&&"value"===o.kind)n=r(n,e,t.typeAnnotation||"assert");else if("projectionDefinition"===e.kind&&["string","array"].includes(o.kind)||["color","formatted","resolvedImage"].includes(e.kind)&&["value","string"].includes(o.kind)||["padding","numberArray"].includes(e.kind)&&["value","number","array"].includes(o.kind)||"colorArray"===e.kind&&["value","string","array"].includes(o.kind)||"variableAnchorOffsetCollection"===e.kind&&["value","array"].includes(o.kind))n=r(n,e,t.typeAnnotation||"coerce");else if(this.checkSubtype(e,o))return null}if(!(n instanceof tL)&&"resolvedImage"!==n.type.kind&&this._isConstant(n)){let e=new tT;try{n=new tL(n.type,n.evaluate(e))}catch(e){return this.error(e.message),null}}return n}return this.error(`Unknown expression "${n}". If you wanted a literal array, use ["literal", [...]].`,0)}}concat(e,t,r){let n="number"==typeof e?this.path.concat(e):this.path,o=r?this.scope.concat(r):this.scope;return new tE(this.registry,this._isConstant,n,t||null,o,this.errors)}error(e,...t){let r=`${this.key}${t.map(e=>`[${e}]`).join("")}`;this.errors.push(new eA(r,e))}checkSubtype(e,t){let r=eV(e,t);return r&&this.error(r),r}}class t${constructor(e,t){this.type=t.type,this.bindings=[].concat(e),this.result=t}evaluate(e){return this.result.evaluate(e)}eachChild(e){for(let t of this.bindings)e(t[1]);e(this.result)}static parse(e,t){if(e.length<4)return t.error(`Expected at least 3 arguments, but found ${e.length-1} instead.`);let r=[];for(let n=1;n<e.length-1;n+=2){let o=e[n];if("string"!=typeof o)return t.error(`Expected string, but found ${typeof o} instead.`,n);if(/[^a-zA-Z0-9_]/.test(o))return t.error("Variable names must contain only alphanumeric characters or '_'.",n);let a=t.parse(e[n+1],n+1);if(!a)return null;r.push([o,a])}let n=t.parse(e[e.length-1],e.length-1,t.expectedType,r);return n?new t$(r,n):null}outputDefined(){return this.result.outputDefined()}}class tI{constructor(e,t){this.type=t.type,this.name=e,this.boundExpression=t}static parse(e,t){if(2!==e.length||"string"!=typeof e[1])return t.error("'var' expression requires exactly one string literal argument.");let r=e[1];return t.scope.has(r)?new tI(r,t.scope.get(r)):t.error(`Unknown variable "${r}". Make sure "${r}" has been bound in an enclosing "let" expression before using it.`,1)}evaluate(e){return this.boundExpression.evaluate(e)}eachChild(){}outputDefined(){return!1}}class tP{constructor(e,t,r){this.type=e,this.index=t,this.input=r}static parse(e,t){if(3!==e.length)return t.error(`Expected 2 arguments, but found ${e.length-1} instead.`);let r=t.parse(e[1],1,eM),n=t.parse(e[2],2,eB(t.expectedType||eR));return r&&n?new tP(n.type.itemType,r,n):null}evaluate(e){let t=this.index.evaluate(e),r=this.input.evaluate(e);if(t<0)throw new th(`Array index out of bounds: ${t} < 0.`);if(t>=r.length)throw new th(`Array index out of bounds: ${t} > ${r.length-1}.`);if(t!==Math.floor(t))throw new th(`Array index must be an integer, but found ${t} instead.`);return r[t]}eachChild(e){e(this.index),e(this.input)}outputDefined(){return!1}}class tR{constructor(e,t){this.type=eE,this.needle=e,this.haystack=t}static parse(e,t){if(3!==e.length)return t.error(`Expected 2 arguments, but found ${e.length-1} instead.`);let r=t.parse(e[1],1,eR),n=t.parse(e[2],2,eR);return r&&n?eW(r.type,[eE,eT,eM,ez,eR])?new tR(r,n):t.error(`Expected first argument to be of type boolean, string, number or null, but found ${eG(r.type)} instead`):null}evaluate(e){let t=this.needle.evaluate(e),r=this.haystack.evaluate(e);if(!r)return!1;if(!eK(t,["boolean","string","number","null"]))throw new th(`Expected first argument to be of type boolean, string, number or null, but found ${eG(tS(t))} instead.`);if(!eK(r,["string","array"]))throw new th(`Expected second argument to be of type array or string, but found ${eG(tS(r))} instead.`);return r.indexOf(t)>=0}eachChild(e){e(this.needle),e(this.haystack)}outputDefined(){return!0}}class tj{constructor(e,t,r){this.type=eM,this.needle=e,this.haystack=t,this.fromIndex=r}static parse(e,t){if(e.length<=2||e.length>=5)return t.error(`Expected 3 or 4 arguments, but found ${e.length-1} instead.`);let r=t.parse(e[1],1,eR),n=t.parse(e[2],2,eR);if(!r||!n)return null;if(!eW(r.type,[eE,eT,eM,ez,eR]))return t.error(`Expected first argument to be of type boolean, string, number or null, but found ${eG(r.type)} instead`);if(4!==e.length)return new tj(r,n);{let o=t.parse(e[3],3,eM);return o?new tj(r,n,o):null}}evaluate(e){let t;let r=this.needle.evaluate(e),n=this.haystack.evaluate(e);if(!eK(r,["boolean","string","number","null"]))throw new th(`Expected first argument to be of type boolean, string, number or null, but found ${eG(tS(r))} instead.`);if(this.fromIndex&&(t=this.fromIndex.evaluate(e)),eK(n,["string"])){let e=n.indexOf(r,t);return -1===e?-1:[...n.slice(0,e)].length}if(eK(n,["array"]))return n.indexOf(r,t);throw new th(`Expected second argument to be of type array or string, but found ${eG(tS(n))} instead.`)}eachChild(e){e(this.needle),e(this.haystack),this.fromIndex&&e(this.fromIndex)}outputDefined(){return!1}}class tN{constructor(e,t,r,n,o,a){this.inputType=e,this.type=t,this.input=r,this.cases=n,this.outputs=o,this.otherwise=a}static parse(e,t){let r,n;if(e.length<5)return t.error(`Expected at least 4 arguments, but found only ${e.length-1}.`);if(e.length%2!=1)return t.error("Expected an even number of arguments.");t.expectedType&&"value"!==t.expectedType.kind&&(n=t.expectedType);let o={},a=[];for(let i=2;i<e.length-1;i+=2){let l=e[i],s=e[i+1];Array.isArray(l)||(l=[l]);let u=t.concat(i);if(0===l.length)return u.error("Expected at least one branch label.");for(let e of l){if("number"!=typeof e&&"string"!=typeof e)return u.error("Branch labels must be numbers or strings.");if("number"==typeof e&&Math.abs(e)>Number.MAX_SAFE_INTEGER)return u.error(`Branch labels must be integers no larger than ${Number.MAX_SAFE_INTEGER}.`);if("number"==typeof e&&Math.floor(e)!==e)return u.error("Numeric branch labels must be integer values.");if(r){if(u.checkSubtype(r,tS(e)))return null}else r=tS(e);if("u">typeof o[String(e)])return u.error("Branch labels must be unique.");o[String(e)]=a.length}let p=t.parse(s,i,n);if(!p)return null;n=n||p.type,a.push(p)}let i=t.parse(e[1],1,eR);if(!i)return null;let l=t.parse(e[e.length-1],e.length-1,n);return!l||"value"!==i.type.kind&&t.concat(1).checkSubtype(r,i.type)?null:new tN(r,n,i,o,a,l)}evaluate(e){let t=this.input.evaluate(e);return(tS(t)===this.inputType&&this.outputs[this.cases[t]]||this.otherwise).evaluate(e)}eachChild(e){e(this.input),this.outputs.forEach(e),e(this.otherwise)}outputDefined(){return this.outputs.every(e=>e.outputDefined())&&this.otherwise.outputDefined()}}class tO{constructor(e,t,r){this.type=e,this.branches=t,this.otherwise=r}static parse(e,t){let r;if(e.length<4)return t.error(`Expected at least 3 arguments, but found only ${e.length-1}.`);if(e.length%2!=0)return t.error("Expected an odd number of arguments.");t.expectedType&&"value"!==t.expectedType.kind&&(r=t.expectedType);let n=[];for(let o=1;o<e.length-1;o+=2){let a=t.parse(e[o],o,eE);if(!a)return null;let i=t.parse(e[o+1],o+1,r);if(!i)return null;n.push([a,i]),r=r||i.type}let o=t.parse(e[e.length-1],e.length-1,r);if(!o)return null;if(!r)throw Error("Can't infer output type");return new tO(r,n,o)}evaluate(e){for(let[t,r]of this.branches)if(t.evaluate(e))return r.evaluate(e);return this.otherwise.evaluate(e)}eachChild(e){for(let[t,r]of this.branches)e(t),e(r);e(this.otherwise)}outputDefined(){return this.branches.every(([e,t])=>t.outputDefined())&&this.otherwise.outputDefined()}}class tF{constructor(e,t,r,n){this.type=e,this.input=t,this.beginIndex=r,this.endIndex=n}static parse(e,t){if(e.length<=2||e.length>=5)return t.error(`Expected 3 or 4 arguments, but found ${e.length-1} instead.`);let r=t.parse(e[1],1,eR),n=t.parse(e[2],2,eM);if(!r||!n)return null;if(!eW(r.type,[eB(eR),eT,eR]))return t.error(`Expected first argument to be of type array or string, but found ${eG(r.type)} instead`);if(4!==e.length)return new tF(r.type,r,n);{let o=t.parse(e[3],3,eM);return o?new tF(r.type,r,n,o):null}}evaluate(e){let t;let r=this.input.evaluate(e),n=this.beginIndex.evaluate(e);if(this.endIndex&&(t=this.endIndex.evaluate(e)),eK(r,["string"]))return[...r].slice(n,t).join("");if(eK(r,["array"]))return r.slice(n,t);throw new th(`Expected first argument to be of type array or string, but found ${eG(tS(r))} instead.`)}eachChild(e){e(this.input),e(this.beginIndex),this.endIndex&&e(this.endIndex)}outputDefined(){return!1}}function tq(e,t){let r=e.length-1,n=0,o=r,a=0,i,l;for(;n<=o;)if(i=e[a=Math.floor((n+o)/2)],l=e[a+1],i<=t){if(a===r||t<l)return a;n=a+1}else if(i>t)o=a-1;else throw new th("Input is not a number.");return 0}class tD{constructor(e,t,r){for(let[n,o]of(this.type=e,this.input=t,this.labels=[],this.outputs=[],r))this.labels.push(n),this.outputs.push(o)}static parse(e,t){if(e.length-1<4)return t.error(`Expected at least 4 arguments, but found only ${e.length-1}.`);if((e.length-1)%2!=0)return t.error("Expected an even number of arguments.");let r=t.parse(e[1],1,eM);if(!r)return null;let n=[],o=null;t.expectedType&&"value"!==t.expectedType.kind&&(o=t.expectedType);for(let r=1;r<e.length;r+=2){let a=1===r?-1/0:e[r],i=e[r+1],l=r,s=r+1;if("number"!=typeof a)return t.error('Input/output pairs for "step" expressions must be defined using literal numeric values (not computed expressions) for the input values.',l);if(n.length&&n[n.length-1][0]>=a)return t.error('Input/output pairs for "step" expressions must be arranged with input values in strictly ascending order.',l);let u=t.parse(i,s,o);if(!u)return null;o=o||u.type,n.push([a,u])}return new tD(o,r,n)}evaluate(e){let t=this.labels,r=this.outputs;if(1===t.length)return r[0].evaluate(e);let n=this.input.evaluate(e);if(n<=t[0])return r[0].evaluate(e);let o=t.length;return n>=t[o-1]?r[o-1].evaluate(e):r[tq(t,n)].evaluate(e)}eachChild(e){for(let t of(e(this.input),this.outputs))e(t)}outputDefined(){return this.outputs.every(e=>e.outputDefined())}}var tU=(o=function(){if(i)return a;function e(e,t,r,n){this.cx=3*e,this.bx=3*(r-e)-this.cx,this.ax=1-this.cx-this.bx,this.cy=3*t,this.by=3*(n-t)-this.cy,this.ay=1-this.cy-this.by,this.p1x=e,this.p1y=t,this.p2x=r,this.p2y=n}return i=1,a=e,e.prototype={sampleCurveX:function(e){return((this.ax*e+this.bx)*e+this.cx)*e},sampleCurveY:function(e){return((this.ay*e+this.by)*e+this.cy)*e},sampleCurveDerivativeX:function(e){return(3*this.ax*e+2*this.bx)*e+this.cx},solveCurveX:function(e,t){if(void 0===t&&(t=1e-6),e<0)return 0;if(e>1)return 1;for(var r=e,n=0;n<8;n++){var o=this.sampleCurveX(r)-e;if(Math.abs(o)<t)return r;var a=this.sampleCurveDerivativeX(r);if(1e-6>Math.abs(a))break;r-=o/a}var i=0,l=1;for(r=e,n=0;n<20&&!(Math.abs((o=this.sampleCurveX(r))-e)<t);n++)e>o?i=r:l=r,r=(l-i)*.5+i;return r},solve:function(e,t){return this.sampleCurveY(this.solveCurveX(e,t))}},a}())&&o.__esModule&&Object.prototype.hasOwnProperty.call(o,"default")?o.default:o;class tB{constructor(e,t,r,n,o){for(let[a,i]of(this.type=e,this.operator=t,this.interpolation=r,this.input=n,this.labels=[],this.outputs=[],o))this.labels.push(a),this.outputs.push(i)}static interpolationFactor(e,t,r,n){let o=0;if("exponential"===e.name)o=tG(t,e.base,r,n);else if("linear"===e.name)o=tG(t,1,r,n);else if("cubic-bezier"===e.name){let a=e.controlPoints;o=new tU(a[0],a[1],a[2],a[3]).solve(tG(t,1,r,n))}return o}static parse(e,t){let[r,n,o,...a]=e;if(!Array.isArray(n)||0===n.length)return t.error("Expected an interpolation type expression.",1);if("linear"===n[0])n={name:"linear"};else if("exponential"===n[0]){let e=n[1];if("number"!=typeof e)return t.error("Exponential interpolation requires a numeric base.",1,1);n={name:"exponential",base:e}}else{if("cubic-bezier"!==n[0])return t.error(`Unknown interpolation type ${String(n[0])}`,1,0);let e=n.slice(1);if(4!==e.length||e.some(e=>"number"!=typeof e||e<0||e>1))return t.error("Cubic bezier interpolation requires four numeric arguments with values between 0 and 1.",1);n={name:"cubic-bezier",controlPoints:e}}if(e.length-1<4)return t.error(`Expected at least 4 arguments, but found only ${e.length-1}.`);if((e.length-1)%2!=0)return t.error("Expected an even number of arguments.");if(!(o=t.parse(o,2,eM)))return null;let i=[],l=null;("interpolate-hcl"===r||"interpolate-lab"===r)&&t.expectedType!=eF?l=e$:t.expectedType&&"value"!==t.expectedType.kind&&(l=t.expectedType);for(let e=0;e<a.length;e+=2){let r=a[e],n=a[e+1],o=e+3,s=e+4;if("number"!=typeof r)return t.error('Input/output pairs for "interpolate" expressions must be defined using literal numeric values (not computed expressions) for the input values.',o);if(i.length&&i[i.length-1][0]>=r)return t.error('Input/output pairs for "interpolate" expressions must be arranged with input values in strictly ascending order.',o);let u=t.parse(n,s,l);if(!u)return null;l=l||u.type,i.push([r,u])}return eZ(l,eM)||eZ(l,eI)||eZ(l,e$)||eZ(l,eO)||eZ(l,eq)||eZ(l,eF)||eZ(l,eU)||eZ(l,eB(eM))?new tB(l,r,n,o,i):t.error(`Type ${eG(l)} is not interpolatable.`)}evaluate(e){let t=this.labels,r=this.outputs;if(1===t.length)return r[0].evaluate(e);let n=this.input.evaluate(e);if(n<=t[0])return r[0].evaluate(e);let o=t.length;if(n>=t[o-1])return r[o-1].evaluate(e);let a=tq(t,n),i=t[a],l=t[a+1],s=tB.interpolationFactor(this.interpolation,n,i,l),u=r[a].evaluate(e),p=r[a+1].evaluate(e);switch(this.operator){case"interpolate":switch(this.type.kind){case"number":return u+s*(p-u);case"color":return tl.interpolate(u,p,s);case"padding":return td.interpolate(u,p,s);case"colorArray":return ty.interpolate(u,p,s);case"numberArray":return tf.interpolate(u,p,s);case"variableAnchorOffsetCollection":return tg.interpolate(u,p,s);case"array":return ti(u,p,s);case"projectionDefinition":return tb.interpolate(u,p,s)}case"interpolate-hcl":switch(this.type.kind){case"color":return tl.interpolate(u,p,s,"hcl");case"colorArray":return ty.interpolate(u,p,s,"hcl")}case"interpolate-lab":switch(this.type.kind){case"color":return tl.interpolate(u,p,s,"lab");case"colorArray":return ty.interpolate(u,p,s,"lab")}}}eachChild(e){for(let t of(e(this.input),this.outputs))e(t)}outputDefined(){return this.outputs.every(e=>e.outputDefined())}}function tG(e,t,r,n){let o=n-r,a=e-r;return 0===o?0:1===t?a/o:(Math.pow(t,a)-1)/(Math.pow(t,o)-1)}tl.interpolate,td.interpolate,tf.interpolate,ty.interpolate,tg.interpolate;class tH{constructor(e,t){this.type=e,this.args=t}static parse(e,t){if(e.length<2)return t.error("Expected at least one argument.");let r=null,n=t.expectedType;n&&"value"!==n.kind&&(r=n);let o=[];for(let n of e.slice(1)){let e=t.parse(n,1+o.length,r,void 0,{typeAnnotation:"omit"});if(!e)return null;r=r||e.type,o.push(e)}if(!r)throw Error("No output type");return n&&o.some(e=>eV(n,e.type))?new tH(eR,o):new tH(r,o)}evaluate(e){let t=null,r=0,n;for(let o of this.args)if(r++,(t=o.evaluate(e))&&t instanceof tv&&!t.available&&(n||(n=t.name),t=null,r===this.args.length&&(t=n)),null!==t)break;return t}eachChild(e){this.args.forEach(e)}outputDefined(){return this.args.every(e=>e.outputDefined())}}function tV(e,t){return"=="===e||"!="===e?"boolean"===t.kind||"string"===t.kind||"number"===t.kind||"null"===t.kind||"value"===t.kind:"string"===t.kind||"number"===t.kind||"value"===t.kind}function tW(e,t,r,n){return 0===n.compare(t,r)}function tK(e,t,r){let n="=="!==e&&"!="!==e;return class o{constructor(e,t,r){this.type=eE,this.lhs=e,this.rhs=t,this.collator=r,this.hasUntypedArgument="value"===e.type.kind||"value"===t.type.kind}static parse(e,t){if(3!==e.length&&4!==e.length)return t.error("Expected two or three arguments.");let r=e[0],a=t.parse(e[1],1,eR);if(!a)return null;if(!tV(r,a.type))return t.concat(1).error(`"${r}" comparisons are not supported for type '${eG(a.type)}'.`);let i=t.parse(e[2],2,eR);if(!i)return null;if(!tV(r,i.type))return t.concat(2).error(`"${r}" comparisons are not supported for type '${eG(i.type)}'.`);if(a.type.kind!==i.type.kind&&"value"!==a.type.kind&&"value"!==i.type.kind)return t.error(`Cannot compare types '${eG(a.type)}' and '${eG(i.type)}'.`);n&&("value"===a.type.kind&&"value"!==i.type.kind?a=new tA(i.type,[a]):"value"!==a.type.kind&&"value"===i.type.kind&&(i=new tA(a.type,[i])));let l=null;if(4===e.length){if("string"!==a.type.kind&&"string"!==i.type.kind&&"value"!==a.type.kind&&"value"!==i.type.kind)return t.error("Cannot use collator to compare non-string types.");if(!(l=t.parse(e[3],3,ej)))return null}return new o(a,i,l)}evaluate(o){let a=this.lhs.evaluate(o),i=this.rhs.evaluate(o);if(n&&this.hasUntypedArgument){let t=tS(a),r=tS(i);if(t.kind!==r.kind||!("string"===t.kind||"number"===t.kind))throw new th(`Expected arguments for "${e}" to be (string, string) or (number, number), but found (${t.kind}, ${r.kind}) instead.`)}if(this.collator&&!n&&this.hasUntypedArgument){let e=tS(a),r=tS(i);if("string"!==e.kind||"string"!==r.kind)return t(o,a,i)}return this.collator?r(o,a,i,this.collator.evaluate(o)):t(o,a,i)}eachChild(e){e(this.lhs),e(this.rhs),this.collator&&e(this.collator)}outputDefined(){return!0}}}let tZ=tK("==",function(e,t,r){return t===r},tW),tJ=tK("!=",function(e,t,r){return t!==r},function(e,t,r,n){return!tW(e,t,r,n)}),tY=tK("<",function(e,t,r){return t<r},function(e,t,r,n){return 0>n.compare(t,r)}),tX=tK(">",function(e,t,r){return t>r},function(e,t,r,n){return n.compare(t,r)>0}),tQ=tK("<=",function(e,t,r){return t<=r},function(e,t,r,n){return 0>=n.compare(t,r)}),t0=tK(">=",function(e,t,r){return t>=r},function(e,t,r,n){return n.compare(t,r)>=0});class t1{constructor(e,t,r){this.type=ej,this.locale=r,this.caseSensitive=e,this.diacriticSensitive=t}static parse(e,t){if(2!==e.length)return t.error("Expected one argument.");let r=e[1];if("object"!=typeof r||Array.isArray(r))return t.error("Collator options argument must be an object.");let n=t.parse(void 0!==r["case-sensitive"]&&r["case-sensitive"],1,eE);if(!n)return null;let o=t.parse(void 0!==r["diacritic-sensitive"]&&r["diacritic-sensitive"],1,eE);if(!o)return null;let a=null;return!r.locale||(a=t.parse(r.locale,1,eT))?new t1(n,o,a):null}evaluate(e){return new ts(this.caseSensitive.evaluate(e),this.diacriticSensitive.evaluate(e),this.locale?this.locale.evaluate(e):null)}eachChild(e){e(this.caseSensitive),e(this.diacriticSensitive),this.locale&&e(this.locale)}outputDefined(){return!1}}class t2{constructor(e,t,r,n,o){this.type=eT,this.number=e,this.locale=t,this.currency=r,this.minFractionDigits=n,this.maxFractionDigits=o}static parse(e,t){if(3!==e.length)return t.error("Expected two arguments.");let r=t.parse(e[1],1,eM);if(!r)return null;let n=e[2];if("object"!=typeof n||Array.isArray(n))return t.error("NumberFormat options argument must be an object.");let o=null;if(n.locale&&!(o=t.parse(n.locale,1,eT)))return null;let a=null;if(n.currency&&!(a=t.parse(n.currency,1,eT)))return null;let i=null;if(n["min-fraction-digits"]&&!(i=t.parse(n["min-fraction-digits"],1,eM)))return null;let l=null;return!n["max-fraction-digits"]||(l=t.parse(n["max-fraction-digits"],1,eM))?new t2(r,o,a,i,l):null}evaluate(e){return new Intl.NumberFormat(this.locale?this.locale.evaluate(e):[],{style:this.currency?"currency":"decimal",currency:this.currency?this.currency.evaluate(e):void 0,minimumFractionDigits:this.minFractionDigits?this.minFractionDigits.evaluate(e):void 0,maximumFractionDigits:this.maxFractionDigits?this.maxFractionDigits.evaluate(e):void 0}).format(this.number.evaluate(e))}eachChild(e){e(this.number),this.locale&&e(this.locale),this.currency&&e(this.currency),this.minFractionDigits&&e(this.minFractionDigits),this.maxFractionDigits&&e(this.maxFractionDigits)}outputDefined(){return!1}}class t5{constructor(e){this.type=eN,this.sections=e}static parse(e,t){if(e.length<2)return t.error("Expected at least one argument.");let r=e[1];if(!Array.isArray(r)&&"object"==typeof r)return t.error("First argument must be an image or text section.");let n=[],o=!1;for(let r=1;r<=e.length-1;++r){let a=e[r];if(o&&"object"==typeof a&&!Array.isArray(a)){o=!1;let e=null;if(a["font-scale"]&&!(e=t.parse(a["font-scale"],1,eM)))return null;let r=null;if(a["text-font"]&&!(r=t.parse(a["text-font"],1,eB(eT))))return null;let i=null;if(a["text-color"]&&!(i=t.parse(a["text-color"],1,e$)))return null;let l=null;if(a["vertical-align"]){if("string"==typeof a["vertical-align"]&&!tu.includes(a["vertical-align"]))return t.error(`'vertical-align' must be one of: 'bottom', 'center', 'top' but found '${a["vertical-align"]}' instead.`);if(!(l=t.parse(a["vertical-align"],1,eT)))return null}let s=n[n.length-1];s.scale=e,s.font=r,s.textColor=i,s.verticalAlign=l}else{let a=t.parse(e[r],1,eR);if(!a)return null;let i=a.type.kind;if("string"!==i&&"value"!==i&&"null"!==i&&"resolvedImage"!==i)return t.error("Formatted text type must be 'string', 'value', 'image' or 'null'.");o=!0,n.push({content:a,scale:null,font:null,textColor:null,verticalAlign:null})}}return new t5(n)}evaluate(e){return new tc(this.sections.map(t=>{let r=t.content.evaluate(e);return tS(r)===eD?new tp("",r,null,null,null,t.verticalAlign?t.verticalAlign.evaluate(e):null):new tp(tk(r),null,t.scale?t.scale.evaluate(e):null,t.font?t.font.evaluate(e).join(","):null,t.textColor?t.textColor.evaluate(e):null,t.verticalAlign?t.verticalAlign.evaluate(e):null)}))}eachChild(e){for(let t of this.sections)e(t.content),t.scale&&e(t.scale),t.font&&e(t.font),t.textColor&&e(t.textColor),t.verticalAlign&&e(t.verticalAlign)}outputDefined(){return!1}}class t3{constructor(e){this.type=eD,this.input=e}static parse(e,t){if(2!==e.length)return t.error("Expected two arguments.");let r=t.parse(e[1],1,eT);return r?new t3(r):t.error("No image name provided.")}evaluate(e){let t=this.input.evaluate(e),r=tv.fromString(t);return r&&e.availableImages&&(r.available=e.availableImages.indexOf(t)>-1),r}eachChild(e){e(this.input)}outputDefined(){return!1}}class t4{constructor(e){this.type=eM,this.input=e}static parse(e,t){if(2!==e.length)return t.error(`Expected 1 argument, but found ${e.length-1} instead.`);let r=t.parse(e[1],1);return r?"array"!==r.type.kind&&"string"!==r.type.kind&&"value"!==r.type.kind?t.error(`Expected argument of type string or array, but found ${eG(r.type)} instead.`):new t4(r):null}evaluate(e){let t=this.input.evaluate(e);if("string"==typeof t)return[...t].length;if(Array.isArray(t))return t.length;throw new th(`Expected value to be of type string or array, but found ${eG(tS(t))} instead.`)}eachChild(e){e(this.input)}outputDefined(){return!1}}function t8(e,t){let r=Math.pow(2,t.z);return[(e[0]/8192+t.x)/r*360-180,360/Math.PI*Math.atan(Math.exp((180-(e[1]/8192+t.y)/r*360)*Math.PI/180))-90]}function t6(e,t){e[0]=Math.min(e[0],t[0]),e[1]=Math.min(e[1],t[1]),e[2]=Math.max(e[2],t[0]),e[3]=Math.max(e[3],t[1])}function t9(e,t){return!(e[0]<=t[0]||e[2]>=t[2]||e[1]<=t[1]||e[3]>=t[3])}function t7(e,t,r,n){var o;let a=[t[0]-e[0],t[1]-e[1]];return 0!=(o=[n[0]-r[0],n[1]-r[1]])[0]*a[1]-o[1]*a[0]&&!!(rr(e,t,r,n)&&rr(r,n,e,t))}function re(e,t,r=!1){let n=!1;for(let i of t)for(let t=0;t<i.length-1;t++){var o,a;if(function(e,t,r){let n=e[0]-t[0],o=e[1]-t[1],a=e[0]-r[0],i=e[1]-r[1];return n*i-a*o==0&&n*a<=0&&o*i<=0}(e,i[t],i[t+1]))return r;o=i[t],a=i[t+1],o[1]>e[1]!=a[1]>e[1]&&e[0]<(a[0]-o[0])*(e[1]-o[1])/(a[1]-o[1])+o[0]&&(n=!n)}return n}function rt(e,t){for(let r of e)if(!re(r,t))return!1;for(let r=0;r<e.length-1;++r)if(function(e,t,r){for(let n of r)for(let r=0;r<n.length-1;++r)if(t7(e,t,n[r],n[r+1]))return!0;return!1}(e[r],e[r+1],t))return!1;return!0}function rr(e,t,r,n){let o=e[0]-r[0],a=e[1]-r[1],i=t[0]-r[0],l=t[1]-r[1],s=n[0]-r[0],u=n[1]-r[1],p=o*u-s*a,c=i*u-s*l;return p>0&&c<0||p<0&&c>0}function rn(e,t,r){let n=[];for(let o=0;o<e.length;o++){let a=[];for(let n=0;n<e[o].length;n++){let i=function(e,t){let r=(180+e[0])/360,n=(180-180/Math.PI*Math.log(Math.tan(Math.PI/4+e[1]*Math.PI/360)))/360,o=Math.pow(2,t.z);return[Math.round(r*o*8192),Math.round(n*o*8192)]}(e[o][n],r);t6(t,i),a.push(i)}n.push(a)}return n}function ro(e,t,r){let n=[];for(let o=0;o<e.length;o++){let a=rn(e[o],t,r);n.push(a)}return n}function ra(e,t,r,n){if(e[0]<r[0]||e[0]>r[2]){let t=.5*n,o=e[0]-r[0]>t?-n:r[0]-e[0]>t?n:0;0===o&&(o=e[0]-r[2]>t?-n:r[2]-e[0]>t?n:0),e[0]+=o}t6(t,e)}function ri(e,t,r,n){let o=8192*Math.pow(2,n.z),a=[8192*n.x,8192*n.y],i=[];for(let n of e)for(let e of n){let n=[e.x+a[0],e.y+a[1]];ra(n,t,r,o),i.push(n)}return i}function rl(e,t,r,n){let o=8192*Math.pow(2,n.z),a=[8192*n.x,8192*n.y],i=[];for(let r of e){let e=[];for(let n of r){let r=[n.x+a[0],n.y+a[1]];t6(t,r),e.push(r)}i.push(e)}if(t[2]-t[0]<=o/2)for(let e of(t[0]=t[1]=1/0,t[2]=t[3]=-1/0,i))for(let n of e)ra(n,t,r,o);return i}class rs{constructor(e,t){this.type=eE,this.geojson=e,this.geometries=t}static parse(e,t){if(2!==e.length)return t.error(`'within' expression requires exactly one argument, but found ${e.length-1} instead.`);if(tw(e[1])){let t=e[1];if("FeatureCollection"===t.type){let e=[];for(let r of t.features){let{type:t,coordinates:n}=r.geometry;"Polygon"===t&&e.push(n),"MultiPolygon"===t&&e.push(...n)}if(e.length)return new rs(t,{type:"MultiPolygon",coordinates:e})}else if("Feature"===t.type){let e=t.geometry.type;if("Polygon"===e||"MultiPolygon"===e)return new rs(t,t.geometry)}else if("Polygon"===t.type||"MultiPolygon"===t.type)return new rs(t,t)}return t.error("'within' expression requires valid geojson object that contains polygon geometry type.")}evaluate(e){if(null!=e.geometry()&&null!=e.canonicalID()){if("Point"===e.geometryType())return function(e,t){let r=[1/0,1/0,-1/0,-1/0],n=[1/0,1/0,-1/0,-1/0],o=e.canonicalID();if("Polygon"===t.type){let a=rn(t.coordinates,n,o),i=ri(e.geometry(),r,n,o);if(!t9(r,n))return!1;for(let e of i)if(!re(e,a))return!1}if("MultiPolygon"===t.type){let a=ro(t.coordinates,n,o),i=ri(e.geometry(),r,n,o);if(!t9(r,n))return!1;for(let e of i)if(!function(e,t){for(let r of t)if(re(e,r))return!0;return!1}(e,a))return!1}return!0}(e,this.geometries);if("LineString"===e.geometryType())return function(e,t){let r=[1/0,1/0,-1/0,-1/0],n=[1/0,1/0,-1/0,-1/0],o=e.canonicalID();if("Polygon"===t.type){let a=rn(t.coordinates,n,o),i=rl(e.geometry(),r,n,o);if(!t9(r,n))return!1;for(let e of i)if(!rt(e,a))return!1}if("MultiPolygon"===t.type){let a=ro(t.coordinates,n,o),i=rl(e.geometry(),r,n,o);if(!t9(r,n))return!1;for(let e of i)if(!function(e,t){for(let r of t)if(rt(e,r))return!0;return!1}(e,a))return!1}return!0}(e,this.geometries)}return!1}eachChild(){}outputDefined(){return!0}}class ru{constructor(e=[],t=(e,t)=>e<t?-1:e>t?1:0){if(this.data=e,this.length=this.data.length,this.compare=t,this.length>0)for(let e=(this.length>>1)-1;e>=0;e--)this._down(e)}push(e){this.data.push(e),this._up(this.length++)}pop(){if(0===this.length)return;let e=this.data[0],t=this.data.pop();return--this.length>0&&(this.data[0]=t,this._down(0)),e}peek(){return this.data[0]}_up(e){let{data:t,compare:r}=this,n=t[e];for(;e>0;){let o=e-1>>1,a=t[o];if(r(n,a)>=0)break;t[e]=a,e=o}t[e]=n}_down(e){let{data:t,compare:r}=this,n=this.length>>1,o=t[e];for(;e<n;){let n=(e<<1)+1,a=n+1;if(a<this.length&&0>r(t[a],t[n])&&(n=a),r(t[n],o)>=0)break;t[e]=t[n],e=n}t[e]=o}}let rp=1/298.257223563*(2-1/298.257223563),rc=Math.PI/180;class rd{constructor(e){let t=6378137*rc,r=Math.cos(e*rc),n=1/(1-rp*(1-r*r)),o=Math.sqrt(n);this.kx=t*o*r,this.ky=t*o*n*(1-rp)}distance(e,t){let r=this.wrap(e[0]-t[0])*this.kx,n=(e[1]-t[1])*this.ky;return Math.sqrt(r*r+n*n)}pointOnLine(e,t){let r=1/0,n,o,a,i;for(let l=0;l<e.length-1;l++){let s=e[l][0],u=e[l][1],p=this.wrap(e[l+1][0]-s)*this.kx,c=(e[l+1][1]-u)*this.ky,d=0;(0!==p||0!==c)&&((d=(this.wrap(t[0]-s)*this.kx*p+(t[1]-u)*this.ky*c)/(p*p+c*c))>1?(s=e[l+1][0],u=e[l+1][1]):d>0&&(s+=p/this.kx*d,u+=c/this.ky*d));let f=(p=this.wrap(t[0]-s)*this.kx)*p+(c=(t[1]-u)*this.ky)*c;f<r&&(r=f,n=s,o=u,a=l,i=d)}return{point:[n,o],index:a,t:Math.max(0,Math.min(1,i))}}wrap(e){for(;e<-180;)e+=360;for(;e>180;)e-=360;return e}}function rf(e,t){return t[0]-e[0]}function ry(e){return e[1]-e[0]+1}function rh(e,t){return e[1]>=e[0]&&e[1]<t}function rm(e,t){if(e[0]>e[1])return[null,null];let r=ry(e);if(t){if(2===r)return[e,null];let t=Math.floor(r/2);return[[e[0],e[0]+t],[e[0]+t,e[1]]]}if(1===r)return[e,null];let n=Math.floor(r/2)-1;return[[e[0],e[0]+n],[e[0]+n+1,e[1]]]}function rg(e,t){if(!rh(t,e.length))return[1/0,1/0,-1/0,-1/0];let r=[1/0,1/0,-1/0,-1/0];for(let n=t[0];n<=t[1];++n)t6(r,e[n]);return r}function rv(e){let t=[1/0,1/0,-1/0,-1/0];for(let r of e)for(let e of r)t6(t,e);return t}function rb(e){return e[0]!==-1/0&&e[1]!==-1/0&&e[2]!==1/0&&e[3]!==1/0}function rx(e,t,r){if(!rb(e)||!rb(t))return NaN;let n=0,o=0;return e[2]<t[0]&&(n=t[0]-e[2]),e[0]>t[2]&&(n=e[0]-t[2]),e[1]>t[3]&&(o=e[1]-t[3]),e[3]<t[1]&&(o=t[1]-e[3]),r.distance([0,0],[n,o])}function rw(e,t,r){let n=r.pointOnLine(t,e);return r.distance(e,n.point)}function rS(e,t,r,n,o){return Math.min(Math.min(rw(e,[r,n],o),rw(t,[r,n],o)),Math.min(rw(r,[e,t],o),rw(n,[e,t],o)))}function rk(e,t){for(let r of e)for(let e of r)if(re(e,t,!0))return!0;return!1}function rL(e,t,r,n,o,a){if(!a)return;let i=rx(rg(n,a),o,r);i<t&&e.push([i,a,[0,0]])}function rC(e,t,r,n,o,a,i){if(!a||!i)return;let l=rx(rg(n,a),rg(o,i),r);l<t&&e.push([l,a,i])}function rA(e,t,r,n,o=1/0){let a=Math.min(n.distance(e[0],r[0][0]),o);if(0===a)return a;let i=new ru([[0,[0,e.length-1],[0,0]]],rf),l=rv(r);for(;i.length>0;){let o=i.pop();if(o[0]>=a)continue;let s=o[1],u=t?50:100;if(ry(s)<=u){if(!rh(s,e.length))return NaN;if(t){let t=function(e,t,r,n){if(!rh(t,e.length))return NaN;for(let n=t[0];n<=t[1];++n)if(re(e[n],r,!0))return 0;let o=1/0;for(let a=t[0];a<t[1];++a){let t=e[a],i=e[a+1];for(let e of r)for(let r=0,a=e.length,l=a-1;r<a;l=r++){let a=e[l],s=e[r];if(t7(t,i,a,s))return 0;o=Math.min(o,rS(t,i,a,s,n))}}return o}(e,s,r,n);if(isNaN(t)||0===t)return t;a=Math.min(a,t)}else for(let t=s[0];t<=s[1];++t)if(0===(a=Math.min(a,function(e,t,r){if(re(e,t,!0))return 0;let n=1/0;for(let o of t){let t=o[0],a=o[o.length-1];if(t!==a&&0===(n=Math.min(n,rw(e,[a,t],r))))break;let i=r.pointOnLine(o,e);if(0===(n=Math.min(n,r.distance(e,i.point))))break}return n}(e[t],r,n))))return 0}else{let r=rm(s,t);rL(i,a,n,e,l,r[0]),rL(i,a,n,e,l,r[1])}}return a}function r_(e,t,r,n,o,a=1/0){let i=Math.min(a,o.distance(e[0],r[0]));if(0===i)return i;let l=new ru([[0,[0,e.length-1],[0,r.length-1]]],rf);for(;l.length>0;){let a=l.pop();if(a[0]>=i)continue;let s=a[1],u=a[2],p=t?50:100,c=n?50:100;if(ry(s)<=p&&ry(u)<=c){if(!rh(s,e.length)&&rh(u,r.length))return NaN;if(t&&n)i=Math.min(i,function(e,t,r,n,o){if(!(rh(t,e.length)&&rh(n,r.length)))return 1/0;let a=1/0;for(let i=t[0];i<t[1];++i){let t=e[i],l=e[i+1];for(let e=n[0];e<n[1];++e){let n=r[e],i=r[e+1];if(t7(t,l,n,i))return 0;a=Math.min(a,rS(t,l,n,i,o))}}return a}(e,s,r,u,o));else if(t&&!n){let t=e.slice(s[0],s[1]+1);for(let e=u[0];e<=u[1];++e)if(0===(i=Math.min(i,rw(r[e],t,o))))return i}else if(!t&&n){let t=r.slice(u[0],u[1]+1);for(let r=s[0];r<=s[1];++r)if(0===(i=Math.min(i,rw(e[r],t,o))))return i}else i=Math.min(i,function(e,t,r,n,o){if(!(rh(t,e.length)&&rh(n,r.length)))return NaN;let a=1/0;for(let i=t[0];i<=t[1];++i)for(let t=n[0];t<=n[1];++t)if(0===(a=Math.min(a,o.distance(e[i],r[t]))))return a;return a}(e,s,r,u,o))}else{let a=rm(s,t),p=rm(u,n);rC(l,i,o,e,r,a[0],p[0]),rC(l,i,o,e,r,a[0],p[1]),rC(l,i,o,e,r,a[1],p[0]),rC(l,i,o,e,r,a[1],p[1])}}return i}function rz(e){return"MultiPolygon"===e.type?e.coordinates.map(e=>({type:"Polygon",coordinates:e})):"MultiLineString"===e.type?e.coordinates.map(e=>({type:"LineString",coordinates:e})):"MultiPoint"===e.type?e.coordinates.map(e=>({type:"Point",coordinates:e})):[e]}class rM{constructor(e,t){this.type=eM,this.geojson=e,this.geometries=t}static parse(e,t){if(2!==e.length)return t.error(`'distance' expression requires exactly one argument, but found ${e.length-1} instead.`);if(tw(e[1])){let t=e[1];if("FeatureCollection"===t.type)return new rM(t,t.features.map(e=>rz(e.geometry)).flat());if("Feature"===t.type)return new rM(t,rz(t.geometry));if("type"in t&&"coordinates"in t)return new rM(t,rz(t))}return t.error("'distance' expression requires valid geojson object that contains polygon geometry type.")}evaluate(e){if(null!=e.geometry()&&null!=e.canonicalID()){if("Point"===e.geometryType())return function(e,t){let r=e.geometry(),n=r.flat().map(t=>t8([t.x,t.y],e.canonical));if(0===r.length)return NaN;let o=new rd(n[0][1]),a=1/0;for(let e of t){switch(e.type){case"Point":a=Math.min(a,r_(n,!1,[e.coordinates],!1,o,a));break;case"LineString":a=Math.min(a,r_(n,!1,e.coordinates,!0,o,a));break;case"Polygon":a=Math.min(a,rA(n,!1,e.coordinates,o,a))}if(0===a)break}return a}(e,this.geometries);if("LineString"===e.geometryType())return function(e,t){let r=e.geometry(),n=r.flat().map(t=>t8([t.x,t.y],e.canonical));if(0===r.length)return NaN;let o=new rd(n[0][1]),a=1/0;for(let e of t){switch(e.type){case"Point":a=Math.min(a,r_(n,!0,[e.coordinates],!1,o,a));break;case"LineString":a=Math.min(a,r_(n,!0,e.coordinates,!0,o,a));break;case"Polygon":a=Math.min(a,rA(n,!0,e.coordinates,o,a))}if(0===a)break}return a}(e,this.geometries);if("Polygon"===e.geometryType())return function(e,t){let r=e.geometry();if(0===r.length||0===r[0].length)return NaN;let n=(function(e,t){let r,n;if(e.length<=1)return[e];let o=[];for(let t of e){let e=function(e){let t=0;for(let r=0,n=e.length,o=n-1,a,i;r<n;o=r++)a=e[r],t+=((i=e[o]).x-a.x)*(a.y+i.y);return t}(t);0!==e&&(t.area=Math.abs(e),void 0===n&&(n=e<0),n===e<0?(r&&o.push(r),r=[t]):r.push(t))}return r&&o.push(r),o})(r).map(t=>t.map(t=>t.map(t=>t8([t.x,t.y],e.canonical)))),o=new rd(n[0][0][0][1]),a=1/0;for(let e of t)for(let t of n){switch(e.type){case"Point":a=Math.min(a,rA([e.coordinates],!1,t,o,a));break;case"LineString":a=Math.min(a,rA(e.coordinates,!0,t,o,a));break;case"Polygon":a=Math.min(a,function(e,t,r,n=1/0){let o=rv(e),a=rv(t);if(n!==1/0&&rx(o,a,r)>=n)return n;if(t9(o,a)){if(rk(e,t))return 0}else if(rk(t,e))return 0;let i=1/0;for(let n of e)for(let e=0,o=n.length,a=o-1;e<o;a=e++){let o=n[a],l=n[e];for(let e of t)for(let t=0,n=e.length,a=n-1;t<n;a=t++){let n=e[a],s=e[t];if(t7(o,l,n,s))return 0;i=Math.min(i,rS(o,l,n,s,r))}}return i}(t,e.coordinates,o,a))}if(0===a)return a}return a}(e,this.geometries)}return NaN}eachChild(){}outputDefined(){return!0}}class rT{constructor(e){this.type=eR,this.key=e}static parse(e,t){if(2!==e.length)return t.error(`Expected 1 argument, but found ${e.length-1} instead.`);let r=e[1];return null==r?t.error("Global state property must be defined."):"string"!=typeof r?t.error(`Global state property must be string, but found ${typeof e[1]} instead.`):new rT(r)}evaluate(e){var t;let r=null===(t=e.globals)||void 0===t?void 0:t.globalState;return r&&0!==Object.keys(r).length?te(r,this.key):null}eachChild(){}outputDefined(){return!1}}let rE={"==":tZ,"!=":tJ,">":tX,"<":tY,">=":t0,"<=":tQ,array:tA,at:tP,boolean:tA,case:tO,coalesce:tH,collator:t1,format:t5,image:t3,in:tR,"index-of":tj,interpolate:tB,"interpolate-hcl":tB,"interpolate-lab":tB,length:t4,let:t$,literal:tL,match:tN,number:tA,"number-format":t2,object:tA,slice:tF,step:tD,string:tA,"to-boolean":tz,"to-color":tz,"to-number":tz,"to-string":tz,var:tI,within:rs,distance:rM,"global-state":rT};class r${constructor(e,t,r,n){this.name=e,this.type=t,this._evaluate=r,this.args=n}evaluate(e){return this._evaluate(e,this.args)}eachChild(e){this.args.forEach(e)}outputDefined(){return!1}static parse(e,t){let r=e[0],n=r$.definitions[r];if(!n)return t.error(`Unknown expression "${r}". If you wanted a literal array, use ["literal", [...]].`,0);let o=Array.isArray(n)?n[0]:n.type,a=Array.isArray(n)?[[n[1],n[2]]]:n.overloads,i=a.filter(([t])=>!Array.isArray(t)||t.length===e.length-1),l=null;for(let[n,a]of i){l=new tE(t.registry,rj,t.path,null,t.scope);let i=[],s=!1;for(let t=1;t<e.length;t++){let r=e[t],o=Array.isArray(n)?n[t-1]:n.type,a=l.parse(r,1+i.length,o);if(!a){s=!0;break}i.push(a)}if(!s){if(Array.isArray(n)&&n.length!==i.length){l.error(`Expected ${n.length} arguments, but found ${i.length} instead.`);continue}for(let e=0;e<i.length;e++){let t=Array.isArray(n)?n[e]:n.type,r=i[e];l.concat(e+1).checkSubtype(t,r.type)}if(0===l.errors.length)return new r$(r,o,a,i)}}if(1===i.length)t.errors.push(...l.errors);else{let r=(i.length?i:a).map(([e])=>Array.isArray(e)?`(${e.map(eG).join(", ")})`:`(${eG(e.type)}...)`).join(" | "),n=[];for(let r=1;r<e.length;r++){let o=t.parse(e[r],1+n.length);if(!o)return null;n.push(eG(o.type))}t.error(`Expected arguments of type ${r}, but found (${n.join(", ")}) instead.`)}return null}static register(e,t){for(let r in r$.definitions=t,t)e[r]=r$}}function rI(e,[t,r,n,o]){t=t.evaluate(e),r=r.evaluate(e),n=n.evaluate(e);let a=o?o.evaluate(e):1,i=tx(t,r,n,a);if(i)throw new th(i);return new tl(t/255,r/255,n/255,a,!1)}function rP(e,t){let r=t[e];return typeof r>"u"?null:r}function rR(e){return{type:e}}function rj(e){if(e instanceof tI)return rj(e.boundExpression);if(e instanceof r$&&"error"===e.name||e instanceof t1||e instanceof rs||e instanceof rM||e instanceof rT)return!1;let t=e instanceof tz||e instanceof tA,r=!0;return e.eachChild(e=>{r=t?r&&rj(e):r&&e instanceof tL}),!!r&&rN(e)&&rF(e,["zoom","heatmap-density","elevation","line-progress","accumulated","is-supported-script"])}function rN(e){if(e instanceof r$&&("get"===e.name&&1===e.args.length||"feature-state"===e.name||"has"===e.name&&1===e.args.length||"properties"===e.name||"geometry-type"===e.name||"id"===e.name||/^filter-/.test(e.name))||e instanceof rs||e instanceof rM)return!1;let t=!0;return e.eachChild(e=>{t&&!rN(e)&&(t=!1)}),t}function rO(e){if(e instanceof r$&&"feature-state"===e.name)return!1;let t=!0;return e.eachChild(e=>{t&&!rO(e)&&(t=!1)}),t}function rF(e,t){if(e instanceof r$&&t.indexOf(e.name)>=0)return!1;let r=!0;return e.eachChild(e=>{r&&!rF(e,t)&&(r=!1)}),r}function rq(e){return{result:"success",value:e}}function rD(e){return{result:"error",value:e}}function rU(e){return"data-driven"===e["property-type"]||"cross-faded-data-driven"===e["property-type"]}function rB(e){return!!e.expression&&e.expression.parameters.indexOf("zoom")>-1}function rG(e){return!!e.expression&&e.expression.interpolated}function rH(e){return e instanceof Number?"number":e instanceof String?"string":e instanceof Boolean?"boolean":Array.isArray(e)?"array":null===e?"null":typeof e}function rV(e){return"object"==typeof e&&null!==e&&!Array.isArray(e)&&tS(e)===eP}r$.register(rE,{error:[{kind:"error"},[eT],(e,[t])=>{throw new th(t.evaluate(e))}],typeof:[eT,[eR],(e,[t])=>eG(tS(t.evaluate(e)))],"to-rgba":[eB(eM,4),[e$],(e,[t])=>{let[r,n,o,a]=t.evaluate(e).rgb;return[255*r,255*n,255*o,a]}],rgb:[e$,[eM,eM,eM],rI],rgba:[e$,[eM,eM,eM,eM],rI],has:{type:eE,overloads:[[[eT],(e,[t])=>t.evaluate(e) in e.properties()],[[eT,eP],(e,[t,r])=>t.evaluate(e) in r.evaluate(e)]]},get:{type:eR,overloads:[[[eT],(e,[t])=>rP(t.evaluate(e),e.properties())],[[eT,eP],(e,[t,r])=>rP(t.evaluate(e),r.evaluate(e))]]},"feature-state":[eR,[eT],(e,[t])=>rP(t.evaluate(e),e.featureState||{})],properties:[eP,[],e=>e.properties()],"geometry-type":[eT,[],e=>e.geometryType()],id:[eR,[],e=>e.id()],zoom:[eM,[],e=>e.globals.zoom],"heatmap-density":[eM,[],e=>e.globals.heatmapDensity||0],elevation:[eM,[],e=>e.globals.elevation||0],"line-progress":[eM,[],e=>e.globals.lineProgress||0],accumulated:[eR,[],e=>void 0===e.globals.accumulated?null:e.globals.accumulated],"+":[eM,rR(eM),(e,t)=>{let r=0;for(let n of t)r+=n.evaluate(e);return r}],"*":[eM,rR(eM),(e,t)=>{let r=1;for(let n of t)r*=n.evaluate(e);return r}],"-":{type:eM,overloads:[[[eM,eM],(e,[t,r])=>t.evaluate(e)-r.evaluate(e)],[[eM],(e,[t])=>-t.evaluate(e)]]},"/":[eM,[eM,eM],(e,[t,r])=>t.evaluate(e)/r.evaluate(e)],"%":[eM,[eM,eM],(e,[t,r])=>t.evaluate(e)%r.evaluate(e)],ln2:[eM,[],()=>Math.LN2],pi:[eM,[],()=>Math.PI],e:[eM,[],()=>Math.E],"^":[eM,[eM,eM],(e,[t,r])=>Math.pow(t.evaluate(e),r.evaluate(e))],sqrt:[eM,[eM],(e,[t])=>Math.sqrt(t.evaluate(e))],log10:[eM,[eM],(e,[t])=>Math.log(t.evaluate(e))/Math.LN10],ln:[eM,[eM],(e,[t])=>Math.log(t.evaluate(e))],log2:[eM,[eM],(e,[t])=>Math.log(t.evaluate(e))/Math.LN2],sin:[eM,[eM],(e,[t])=>Math.sin(t.evaluate(e))],cos:[eM,[eM],(e,[t])=>Math.cos(t.evaluate(e))],tan:[eM,[eM],(e,[t])=>Math.tan(t.evaluate(e))],asin:[eM,[eM],(e,[t])=>Math.asin(t.evaluate(e))],acos:[eM,[eM],(e,[t])=>Math.acos(t.evaluate(e))],atan:[eM,[eM],(e,[t])=>Math.atan(t.evaluate(e))],min:[eM,rR(eM),(e,t)=>Math.min(...t.map(t=>t.evaluate(e)))],max:[eM,rR(eM),(e,t)=>Math.max(...t.map(t=>t.evaluate(e)))],abs:[eM,[eM],(e,[t])=>Math.abs(t.evaluate(e))],round:[eM,[eM],(e,[t])=>{let r=t.evaluate(e);return r<0?-Math.round(-r):Math.round(r)}],floor:[eM,[eM],(e,[t])=>Math.floor(t.evaluate(e))],ceil:[eM,[eM],(e,[t])=>Math.ceil(t.evaluate(e))],"filter-==":[eE,[eT,eR],(e,[t,r])=>e.properties()[t.value]===r.value],"filter-id-==":[eE,[eR],(e,[t])=>e.id()===t.value],"filter-type-==":[eE,[eT],(e,[t])=>e.geometryType()===t.value],"filter-<":[eE,[eT,eR],(e,[t,r])=>{let n=e.properties()[t.value],o=r.value;return typeof n==typeof o&&n<o}],"filter-id-<":[eE,[eR],(e,[t])=>{let r=e.id(),n=t.value;return typeof r==typeof n&&r<n}],"filter->":[eE,[eT,eR],(e,[t,r])=>{let n=e.properties()[t.value],o=r.value;return typeof n==typeof o&&n>o}],"filter-id->":[eE,[eR],(e,[t])=>{let r=e.id(),n=t.value;return typeof r==typeof n&&r>n}],"filter-<=":[eE,[eT,eR],(e,[t,r])=>{let n=e.properties()[t.value],o=r.value;return typeof n==typeof o&&n<=o}],"filter-id-<=":[eE,[eR],(e,[t])=>{let r=e.id(),n=t.value;return typeof r==typeof n&&r<=n}],"filter->=":[eE,[eT,eR],(e,[t,r])=>{let n=e.properties()[t.value],o=r.value;return typeof n==typeof o&&n>=o}],"filter-id->=":[eE,[eR],(e,[t])=>{let r=e.id(),n=t.value;return typeof r==typeof n&&r>=n}],"filter-has":[eE,[eR],(e,[t])=>t.value in e.properties()],"filter-has-id":[eE,[],e=>null!==e.id()&&void 0!==e.id()],"filter-type-in":[eE,[eB(eT)],(e,[t])=>t.value.indexOf(e.geometryType())>=0],"filter-id-in":[eE,[eB(eR)],(e,[t])=>t.value.indexOf(e.id())>=0],"filter-in-small":[eE,[eT,eB(eR)],(e,[t,r])=>r.value.indexOf(e.properties()[t.value])>=0],"filter-in-large":[eE,[eT,eB(eR)],(e,[t,r])=>(function(e,t,r,n){for(;r<=n;){let o=r+n>>1;if(t[o]===e)return!0;t[o]>e?n=o-1:r=o+1}return!1})(e.properties()[t.value],r.value,0,r.value.length-1)],all:{type:eE,overloads:[[[eE,eE],(e,[t,r])=>t.evaluate(e)&&r.evaluate(e)],[rR(eE),(e,t)=>{for(let r of t)if(!r.evaluate(e))return!1;return!0}]]},any:{type:eE,overloads:[[[eE,eE],(e,[t,r])=>t.evaluate(e)||r.evaluate(e)],[rR(eE),(e,t)=>{for(let r of t)if(r.evaluate(e))return!0;return!1}]]},"!":[eE,[eE],(e,[t])=>!t.evaluate(e)],"is-supported-script":[eE,[eT],(e,[t])=>{let r=e.globals&&e.globals.isSupportedScript;return!r||r(t.evaluate(e))}],upcase:[eT,[eT],(e,[t])=>t.evaluate(e).toUpperCase()],downcase:[eT,[eT],(e,[t])=>t.evaluate(e).toLowerCase()],concat:[eT,rR(eR),(e,t)=>t.map(t=>tk(t.evaluate(e))).join("")],"resolved-locale":[eT,[ej],(e,[t])=>t.evaluate(e).resolvedLocale()]});class rW{constructor(e,t){this.expression=e,this._warningHistory={},this._evaluator=new tT,this._defaultValue=t?function(e){if("color"===e.type&&rV(e.default))return new tl(0,0,0,0);switch(e.type){case"color":return tl.parse(e.default)||null;case"padding":return td.parse(e.default)||null;case"numberArray":return tf.parse(e.default)||null;case"colorArray":return ty.parse(e.default)||null;case"variableAnchorOffsetCollection":return tg.parse(e.default)||null;case"projectionDefinition":return tb.parse(e.default)||null;default:return void 0===e.default?null:e.default}}(t):null,this._enumValues=t&&"enum"===t.type?t.values:null}evaluateWithoutErrorHandling(e,t,r,n,o,a){return this._evaluator.globals=e,this._evaluator.feature=t,this._evaluator.featureState=r,this._evaluator.canonical=n,this._evaluator.availableImages=o||null,this._evaluator.formattedSection=a,this.expression.evaluate(this._evaluator)}evaluate(e,t,r,n,o,a){this._evaluator.globals=e,this._evaluator.feature=t||null,this._evaluator.featureState=r||null,this._evaluator.canonical=n,this._evaluator.availableImages=o||null,this._evaluator.formattedSection=a||null;try{let e=this.expression.evaluate(this._evaluator);if(null==e||"number"==typeof e&&e!=e)return this._defaultValue;if(this._enumValues&&!(e in this._enumValues))throw new th(`Expected value to be one of ${Object.keys(this._enumValues).map(e=>JSON.stringify(e)).join(", ")}, but found ${JSON.stringify(e)} instead.`);return e}catch(e){return this._warningHistory[e.message]||(this._warningHistory[e.message]=!0,"u">typeof console&&console.warn(e.message)),this._defaultValue}}}function rK(e){return Array.isArray(e)&&e.length>0&&"string"==typeof e[0]&&e[0]in rE}function rZ(e,t){let r=new tE(rE,rj,[],t?function(e){let t={color:e$,string:eT,number:eM,enum:eT,boolean:eE,formatted:eN,padding:eO,numberArray:eq,colorArray:eF,projectionDefinition:eI,resolvedImage:eD,variableAnchorOffsetCollection:eU};return"array"===e.type?eB(t[e.value]||eR,e.length):t[e.type]}(t):void 0),n=r.parse(e,void 0,void 0,void 0,t&&"string"===t.type?{typeAnnotation:"coerce"}:void 0);return n?rq(new rW(n,t)):rD(r.errors)}class rJ{constructor(e,t){this.kind=e,this._styleExpression=t,this.isStateDependent="constant"!==e&&!rO(t.expression),this.globalStateRefs=rX(t.expression)}evaluateWithoutErrorHandling(e,t,r,n,o,a){return this._styleExpression.evaluateWithoutErrorHandling(e,t,r,n,o,a)}evaluate(e,t,r,n,o,a){return this._styleExpression.evaluate(e,t,r,n,o,a)}}class rY{constructor(e,t,r,n){this.kind=e,this.zoomStops=r,this._styleExpression=t,this.isStateDependent="camera"!==e&&!rO(t.expression),this.globalStateRefs=rX(t.expression),this.interpolationType=n}evaluateWithoutErrorHandling(e,t,r,n,o,a){return this._styleExpression.evaluateWithoutErrorHandling(e,t,r,n,o,a)}evaluate(e,t,r,n,o,a){return this._styleExpression.evaluate(e,t,r,n,o,a)}interpolationFactor(e,t,r){return this.interpolationType?tB.interpolationFactor(this.interpolationType,e,t,r):0}}function rX(e,t=new Set){return e instanceof rT&&t.add(e.key),e.eachChild(e=>{rX(e,t)}),t}function rQ(e){let t=e.key,r=e.value;return r?[new eL(t,r,"constants have been deprecated as of v8")]:[]}function r0(e){return e instanceof Number||e instanceof String||e instanceof Boolean?e.valueOf():e}function r1(e){if(Array.isArray(e))return e.map(r1);if(e instanceof Object&&!(e instanceof Number||e instanceof String||e instanceof Boolean)){let t={};for(let r in e)t[r]=r1(e[r]);return t}return r0(e)}function r2(e){let t=e.key,r=e.value,n=e.valueSpec||{},o=e.objectElementValidators||{},a=e.style,i=e.styleSpec,l=e.validateSpec,s=[],u=rH(r);if("object"!==u)return[new eL(t,r,`object expected, ${u} found`)];for(let e in r){let u;let p=e.split(".")[0],c=te(n,p)||n["*"];if(te(o,p))u=o[p];else if(te(n,p))u=l;else if(o["*"])u=o["*"];else if(n["*"])u=l;else{s.push(new eL(t,r[e],`unknown property "${e}"`));continue}s=s.concat(u({key:(t&&`${t}.`)+e,value:r[e],valueSpec:c,style:a,styleSpec:i,object:r,objectKey:e,validateSpec:l},r))}for(let e in n)o[e]||n[e].required&&void 0===n[e].default&&void 0===r[e]&&s.push(new eL(t,r,`missing required property "${e}"`));return s}function r5(e){let t=e.value,r=e.valueSpec,n=e.validateSpec,o=e.style,a=e.styleSpec,i=e.key,l=e.arrayElementValidator||n;if("array"!==rH(t))return[new eL(i,t,`array expected, ${rH(t)} found`)];if(r.length&&t.length!==r.length)return[new eL(i,t,`array length ${r.length} expected, length ${t.length} found`)];if(r["min-length"]&&t.length<r["min-length"])return[new eL(i,t,`array length at least ${r["min-length"]} expected, length ${t.length} found`)];let s={type:r.value,values:r.values};a.$version<7&&(s.function=r.function),"object"===rH(r.value)&&(s=r.value);let u=[];for(let r=0;r<t.length;r++)u=u.concat(l({array:t,arrayIndex:r,value:t[r],valueSpec:s,validateSpec:e.validateSpec,style:o,styleSpec:a,key:`${i}[${r}]`}));return u}function r3(e){let t=e.key,r=e.value,n=e.valueSpec,o=rH(r);return"number"===o&&r!=r&&(o="NaN"),"number"!==o?[new eL(t,r,`number expected, ${o} found`)]:"minimum"in n&&r<n.minimum?[new eL(t,r,`${r} is less than the minimum value ${n.minimum}`)]:"maximum"in n&&r>n.maximum?[new eL(t,r,`${r} is greater than the maximum value ${n.maximum}`)]:[]}function r4(e){let t=e.valueSpec,r=r0(e.value.type),n,o={},a,i,l="categorical"!==r&&void 0===e.value.property,s="array"===rH(e.value.stops)&&"array"===rH(e.value.stops[0])&&"object"===rH(e.value.stops[0][0]),u=r2({key:e.key,value:e.value,valueSpec:e.styleSpec.function,validateSpec:e.validateSpec,style:e.style,styleSpec:e.styleSpec,objectElementValidators:{stops:function(e){if("identity"===r)return[new eL(e.key,e.value,'identity function may not have a "stops" property')];let t=[],n=e.value;return t=t.concat(r5({key:e.key,value:n,valueSpec:e.valueSpec,validateSpec:e.validateSpec,style:e.style,styleSpec:e.styleSpec,arrayElementValidator:p})),"array"===rH(n)&&0===n.length&&t.push(new eL(e.key,n,"array must have at least one stop")),t},default:function(e){return e.validateSpec({key:e.key,value:e.value,valueSpec:t,validateSpec:e.validateSpec,style:e.style,styleSpec:e.styleSpec})}}});return"identity"===r&&l&&u.push(new eL(e.key,e.value,'missing required property "property"')),"identity"===r||e.value.stops||u.push(new eL(e.key,e.value,'missing required property "stops"')),"exponential"===r&&e.valueSpec.expression&&!rG(e.valueSpec)&&u.push(new eL(e.key,e.value,"exponential functions not supported")),e.styleSpec.$version>=8&&(l||rU(e.valueSpec)?l&&!rB(e.valueSpec)&&u.push(new eL(e.key,e.value,"zoom functions not supported")):u.push(new eL(e.key,e.value,"property functions not supported"))),("categorical"===r||s)&&void 0===e.value.property&&u.push(new eL(e.key,e.value,'"property" property is required')),u;function p(e){let r=[],n=e.value,l=e.key;if("array"!==rH(n))return[new eL(l,n,`array expected, ${rH(n)} found`)];if(2!==n.length)return[new eL(l,n,`array length 2 expected, length ${n.length} found`)];if(s){if("object"!==rH(n[0]))return[new eL(l,n,`object expected, ${rH(n[0])} found`)];if(void 0===n[0].zoom)return[new eL(l,n,"object stop key must have zoom")];if(void 0===n[0].value)return[new eL(l,n,"object stop key must have value")];if(i&&i>r0(n[0].zoom))return[new eL(l,n[0].zoom,"stop zoom values must appear in ascending order")];r0(n[0].zoom)!==i&&(i=r0(n[0].zoom),a=void 0,o={}),r=r.concat(r2({key:`${l}[0]`,value:n[0],valueSpec:{zoom:{}},validateSpec:e.validateSpec,style:e.style,styleSpec:e.styleSpec,objectElementValidators:{zoom:r3,value:c}}))}else r=r.concat(c({key:`${l}[0]`,value:n[0],validateSpec:e.validateSpec,style:e.style,styleSpec:e.styleSpec},n));return rK(r1(n[1]))?r.concat([new eL(`${l}[1]`,n[1],"expressions are not allowed in function stops.")]):r.concat(e.validateSpec({key:`${l}[1]`,value:n[1],valueSpec:t,validateSpec:e.validateSpec,style:e.style,styleSpec:e.styleSpec}))}function c(e,i){let l=rH(e.value),s=r0(e.value),u=null!==e.value?e.value:i;if(n){if(l!==n)return[new eL(e.key,u,`${l} stop domain type must match previous stop domain type ${n}`)]}else n=l;if("number"!==l&&"string"!==l&&"boolean"!==l)return[new eL(e.key,u,"stop domain value must be a number, string, or boolean")];if("number"!==l&&"categorical"!==r){let n=`number expected, ${l} found`;return rU(t)&&void 0===r&&(n+='\nIf you intended to use a categorical function, specify `"type": "categorical"`.'),[new eL(e.key,u,n)]}return"categorical"!==r||"number"!==l||isFinite(s)&&Math.floor(s)===s?"categorical"!==r&&"number"===l&&void 0!==a&&s<a?[new eL(e.key,u,"stop domain values must appear in ascending order")]:(a=s,"categorical"===r&&s in o?[new eL(e.key,u,"stop domain values must be unique")]:(o[s]=!0,[])):[new eL(e.key,u,`integer expected, found ${s}`)]}}function r8(e){let t=("property"===e.expressionContext?function(e,t){let r=rZ(e,t);if("error"===r.result)return r;let n=r.value.expression,o=rN(n);if(!o&&!rU(t))return rD([new eA("","data expressions not supported")]);let a=rF(n,["zoom"]);if(!a&&!rB(t))return rD([new eA("","zoom expressions not supported")]);let i=function e(t){let r=null;if(t instanceof t$)r=e(t.result);else if(t instanceof tH){for(let n of t.args)if(r=e(n))break}else(t instanceof tD||t instanceof tB)&&t.input instanceof r$&&"zoom"===t.input.name&&(r=t);return r instanceof eA||t.eachChild(t=>{let n=e(t);n instanceof eA?r=n:!r&&n?r=new eA("",'"zoom" expression may only be used as input to a top-level "step" or "interpolate" expression.'):r&&n&&r!==n&&(r=new eA("",'Only one zoom-based "step" or "interpolate" subexpression may be used in an expression.'))}),r}(n);if(!i&&!a)return rD([new eA("",'"zoom" expression may only be used as input to a top-level "step" or "interpolate" expression.')]);if(i instanceof eA)return rD([i]);if(i instanceof tB&&!rG(t))return rD([new eA("",'"interpolate" expressions cannot be used with this property')]);if(!i)return rq(o?new rJ("constant",r.value):new rJ("source",r.value));let l=i instanceof tB?i.interpolation:void 0;return rq(o?new rY("camera",r.value,i.labels,l):new rY("composite",r.value,i.labels,l))}:rZ)(r1(e.value),e.valueSpec);if("error"===t.result)return t.value.map(t=>new eL(`${e.key}${t.key}`,e.value,t.message));let r=t.value.expression||t.value._styleExpression.expression;if("property"===e.expressionContext&&"text-font"===e.propertyKey&&!r.outputDefined())return[new eL(e.key,e.value,`Invalid data expression for "${e.propertyKey}". Output values must be contained as literals within the expression.`)];if("property"===e.expressionContext&&"layout"===e.propertyType&&!rO(r))return[new eL(e.key,e.value,'"feature-state" data expressions are not supported with layout properties.')];if("filter"===e.expressionContext&&!rO(r))return[new eL(e.key,e.value,'"feature-state" data expressions are not supported with filters.')];if(e.expressionContext&&0===e.expressionContext.indexOf("cluster")){if(!rF(r,["zoom","feature-state"]))return[new eL(e.key,e.value,'"zoom" and "feature-state" expressions are not supported with cluster properties.')];if("cluster-initial"===e.expressionContext&&!rN(r))return[new eL(e.key,e.value,"Feature data expressions are not supported with initial expression part of cluster properties.")]}return[]}function r6(e){let t=e.key,r=e.value,n=rH(r);return"string"!==n?[new eL(t,r,`color expected, ${n} found`)]:tl.parse(String(r))?[]:[new eL(t,r,`color expected, "${r}" found`)]}function r9(e){let t=e.key,r=e.value,n=e.valueSpec,o=[];return Array.isArray(n.values)?-1===n.values.indexOf(r0(r))&&o.push(new eL(t,r,`expected one of [${n.values.join(", ")}], ${JSON.stringify(r)} found`)):-1===Object.keys(n.values).indexOf(r0(r))&&o.push(new eL(t,r,`expected one of [${Object.keys(n.values).join(", ")}], ${JSON.stringify(r)} found`)),o}function r7(e){return!function e(t){if(!0===t||!1===t)return!0;if(!Array.isArray(t)||0===t.length)return!1;switch(t[0]){case"has":return t.length>=2&&"$id"!==t[1]&&"$type"!==t[1];case"in":return t.length>=3&&("string"!=typeof t[1]||Array.isArray(t[2]));case"!in":case"!has":case"none":return!1;case"==":case"!=":case">":case">=":case"<":case"<=":return 3!==t.length||Array.isArray(t[1])||Array.isArray(t[2]);case"any":case"all":for(let r of t.slice(1))if(!e(r)&&"boolean"!=typeof r)return!1;return!0;default:return!0}}(r1(e.value))?function e(t){let r=t.value,n=t.key;if("array"!==rH(r))return[new eL(n,r,`array expected, ${rH(r)} found`)];let o=t.styleSpec,a,i=[];if(r.length<1)return[new eL(n,r,"filter array must have at least 1 element")];switch(i=i.concat(r9({key:`${n}[0]`,value:r[0],valueSpec:o.filter_operator,style:t.style,styleSpec:t.styleSpec})),r0(r[0])){case"<":case"<=":case">":case">=":r.length>=2&&"$type"===r0(r[1])&&i.push(new eL(n,r,`"$type" cannot be use with operator "${r[0]}"`));case"==":case"!=":3!==r.length&&i.push(new eL(n,r,`filter array for operator "${r[0]}" must have 3 elements`));case"in":case"!in":r.length>=2&&"string"!==(a=rH(r[1]))&&i.push(new eL(`${n}[1]`,r[1],`string expected, ${a} found`));for(let e=2;e<r.length;e++)a=rH(r[e]),"$type"===r0(r[1])?i=i.concat(r9({key:`${n}[${e}]`,value:r[e],valueSpec:o.geometry_type,style:t.style,styleSpec:t.styleSpec})):"string"!==a&&"number"!==a&&"boolean"!==a&&i.push(new eL(`${n}[${e}]`,r[e],`string, number, or boolean expected, ${a} found`));break;case"any":case"all":case"none":for(let o=1;o<r.length;o++)i=i.concat(e({key:`${n}[${o}]`,value:r[o],style:t.style,styleSpec:t.styleSpec}));break;case"has":case"!has":a=rH(r[1]),2!==r.length?i.push(new eL(n,r,`filter array for "${r[0]}" operator must have 2 elements`)):"string"!==a&&i.push(new eL(`${n}[1]`,r[1],`string expected, ${a} found`))}return i}(e):r8(eC({},e,{expressionContext:"filter",valueSpec:{value:"boolean"}}))}function ne(e,t){let r;let n=e.key,o=e.validateSpec,a=e.style,i=e.styleSpec,l=e.value,s=e.objectKey,u=i[`${t}_${e.layerType}`];if(!u)return[];let p=s.match(/^(.*)-transition$/);if("paint"===t&&p&&u[p[1]]&&u[p[1]].transition)return o({key:n,value:l,valueSpec:i.transition,style:a,styleSpec:i});let c=e.valueSpec||u[s];if(!c)return[new eL(n,l,`unknown property "${s}"`)];if("string"===rH(l)&&rU(c)&&!c.tokens&&(r=/^{([^}]+)}$/.exec(l)))return[new eL(n,l,`"${s}" does not support interpolation syntax
Use an identity property function instead: \`{ "type": "identity", "property": ${JSON.stringify(r[1])} }\`.`)];let d=[];return"symbol"===e.layerType&&("text-field"===s&&a&&!a.glyphs&&d.push(new eL(n,l,'use of "text-field" requires a style "glyphs" property')),"text-font"===s&&rV(r1(l))&&"identity"===r0(l.type)&&d.push(new eL(n,l,'"text-font" does not support identity functions'))),d.concat(o({key:e.key,value:l,valueSpec:c,style:a,styleSpec:i,expressionContext:"property",propertyType:t,propertyKey:s}))}function nt(e){return ne(e,"paint")}function nr(e){return ne(e,"layout")}function nn(e){let t=[],r=e.value,n=e.key,o=e.style,a=e.styleSpec;if("object"!==rH(r))return[new eL(n,r,`object expected, ${rH(r)} found`)];r.type||r.ref||t.push(new eL(n,r,'either "type" or "ref" is required'));let i=r0(r.type),l=r0(r.ref);if(r.id){let a=r0(r.id);for(let i=0;i<e.arrayIndex;i++){let e=o.layers[i];r0(e.id)===a&&t.push(new eL(n,r.id,`duplicate layer id "${r.id}", previously used at line ${e.id.__line__}`))}}if("ref"in r){let e;["type","source","source-layer","filter","layout"].forEach(e=>{e in r&&t.push(new eL(n,r[e],`"${e}" is prohibited for ref layers`))}),o.layers.forEach(t=>{r0(t.id)===l&&(e=t)}),e?e.ref?t.push(new eL(n,r.ref,"ref cannot reference another ref layer")):i=r0(e.type):t.push(new eL(n,r.ref,`ref layer "${l}" not found`))}else if("background"!==i){if(r.source){let e=o.sources&&o.sources[r.source],a=e&&r0(e.type);e?"vector"===a&&"raster"===i?t.push(new eL(n,r.source,`layer "${r.id}" requires a raster source`)):"raster-dem"!==a&&"hillshade"===i?t.push(new eL(n,r.source,`layer "${r.id}" requires a raster-dem source`)):"raster-dem"!==a&&"color-relief"===i?t.push(new eL(n,r.source,`layer "${r.id}" requires a raster-dem source`)):"raster"===a&&"raster"!==i?t.push(new eL(n,r.source,`layer "${r.id}" requires a vector source`)):"vector"!==a||r["source-layer"]?"raster-dem"===a&&"hillshade"!==i&&"color-relief"!==i?t.push(new eL(n,r.source,"raster-dem source can only be used with layer type 'hillshade' or 'color-relief'.")):"line"===i&&r.paint&&r.paint["line-gradient"]&&("geojson"!==a||!e.lineMetrics)&&t.push(new eL(n,r,`layer "${r.id}" specifies a line-gradient, which requires a GeoJSON source with \`lineMetrics\` enabled.`)):t.push(new eL(n,r,`layer "${r.id}" must specify a "source-layer"`)):t.push(new eL(n,r.source,`source "${r.source}" not found`))}else t.push(new eL(n,r,'missing required property "source"'))}return t=t.concat(r2({key:n,value:r,valueSpec:a.layer,style:e.style,styleSpec:e.styleSpec,validateSpec:e.validateSpec,objectElementValidators:{"*":()=>[],type:()=>e.validateSpec({key:`${n}.type`,value:r.type,valueSpec:a.layer.type,style:e.style,styleSpec:e.styleSpec,validateSpec:e.validateSpec,object:r,objectKey:"type"}),filter:r7,layout:e=>r2({layer:r,key:e.key,value:e.value,style:e.style,styleSpec:e.styleSpec,validateSpec:e.validateSpec,objectElementValidators:{"*":e=>nr(eC({layerType:i},e))}}),paint:e=>r2({layer:r,key:e.key,value:e.value,style:e.style,styleSpec:e.styleSpec,validateSpec:e.validateSpec,objectElementValidators:{"*":e=>nt(eC({layerType:i},e))}})}}))}function no(e){let t=e.value,r=e.key,n=rH(t);return"string"!==n?[new eL(r,t,`string expected, ${n} found`)]:[]}let na={promoteId:function({key:e,value:t}){if("string"===rH(t))return no({key:e,value:t});{let r=[];for(let n in t)r.push(...no({key:`${e}.${n}`,value:t[n]}));return r}}};function ni(e){let t;let r=e.value,n=e.key,o=e.styleSpec,a=e.style,i=e.validateSpec;if(!r.type)return[new eL(n,r,'"type" is required')];let l=r0(r.type);switch(l){case"vector":case"raster":return r2({key:n,value:r,valueSpec:o[`source_${l.replace("-","_")}`],style:e.style,styleSpec:o,objectElementValidators:na,validateSpec:i});case"raster-dem":return function(e){var t;let r=null!==(t=e.sourceName)&&void 0!==t?t:"",n=e.value,o=e.styleSpec,a=o.source_raster_dem,i=e.style,l=[],s=rH(n);if(void 0===n)return l;if("object"!==s)return l.push(new eL("source_raster_dem",n,`object expected, ${s} found`)),l;let u="custom"===r0(n.encoding),p=["redFactor","greenFactor","blueFactor","baseShift"],c=e.value.encoding?`"${e.value.encoding}"`:"Default";for(let t in n)!u&&p.includes(t)?l.push(new eL(t,n[t],`In "${r}": "${t}" is only valid when "encoding" is set to "custom". ${c} encoding found`)):a[t]?l=l.concat(e.validateSpec({key:t,value:n[t],valueSpec:a[t],validateSpec:e.validateSpec,style:i,styleSpec:o})):l.push(new eL(t,n[t],`unknown property "${t}"`));return l}({sourceName:n,value:r,style:e.style,styleSpec:o,validateSpec:i});case"geojson":if(t=r2({key:n,value:r,valueSpec:o.source_geojson,style:a,styleSpec:o,validateSpec:i,objectElementValidators:na}),r.cluster)for(let e in r.clusterProperties){let[o,a]=r.clusterProperties[e],i="string"==typeof o?[o,["accumulated"],["get",e]]:o;t.push(...r8({key:`${n}.${e}.map`,value:a,expressionContext:"cluster-map"})),t.push(...r8({key:`${n}.${e}.reduce`,value:i,expressionContext:"cluster-reduce"}))}return t;case"video":return r2({key:n,value:r,valueSpec:o.source_video,style:a,validateSpec:i,styleSpec:o});case"image":return r2({key:n,value:r,valueSpec:o.source_image,style:a,validateSpec:i,styleSpec:o});case"canvas":return[new eL(n,null,"Please use runtime APIs to add canvas sources, rather than including them in stylesheets.","source.canvas")];default:return r9({key:`${n}.type`,value:r.type,valueSpec:{values:["vector","raster","raster-dem","geojson","video","image"]}})}}function nl(e){let t=e.value,r=e.styleSpec,n=r.light,o=e.style,a=[],i=rH(t);if(void 0===t)return a;if("object"!==i)return a.concat([new eL("light",t,`object expected, ${i} found`)]);for(let i in t){let l=i.match(/^(.*)-transition$/);a=l&&n[l[1]]&&n[l[1]].transition?a.concat(e.validateSpec({key:i,value:t[i],valueSpec:r.transition,validateSpec:e.validateSpec,style:o,styleSpec:r})):n[i]?a.concat(e.validateSpec({key:i,value:t[i],valueSpec:n[i],validateSpec:e.validateSpec,style:o,styleSpec:r})):a.concat([new eL(i,t[i],`unknown property "${i}"`)])}return a}function ns(e){let t=e.value,r=e.styleSpec,n=r.sky,o=e.style,a=rH(t);if(void 0===t)return[];if("object"!==a)return[new eL("sky",t,`object expected, ${a} found`)];let i=[];for(let a in t)i=n[a]?i.concat(e.validateSpec({key:a,value:t[a],valueSpec:n[a],style:o,styleSpec:r})):i.concat([new eL(a,t[a],`unknown property "${a}"`)]);return i}function nu(e){let t=e.value,r=e.styleSpec,n=r.terrain,o=e.style,a=[],i=rH(t);if(void 0===t)return a;if("object"!==i)return a.concat([new eL("terrain",t,`object expected, ${i} found`)]);for(let i in t)a=n[i]?a.concat(e.validateSpec({key:i,value:t[i],valueSpec:n[i],validateSpec:e.validateSpec,style:o,styleSpec:r})):a.concat([new eL(i,t[i],`unknown property "${i}"`)]);return a}function np(e){let t=[],r=e.value,n=e.key;if(!Array.isArray(r))return no({key:n,value:r});{let o=[],a=[];for(let i in r){r[i].id&&o.includes(r[i].id)&&t.push(new eL(n,r,`all the sprites' ids must be unique, but ${r[i].id} is duplicated`)),o.push(r[i].id),r[i].url&&a.includes(r[i].url)&&t.push(new eL(n,r,`all the sprites' URLs must be unique, but ${r[i].url} is duplicated`)),a.push(r[i].url);let l={id:{type:"string",required:!0},url:{type:"string",required:!0}};t=t.concat(r2({key:`${n}[${i}]`,value:r[i],valueSpec:l,validateSpec:e.validateSpec}))}return t}}function nc(e){var t;return(t=e.value)&&t.constructor===Object?[]:[new eL(e.key,e.value,`object expected, ${rH(e.value)} found`)]}let nd={"*":()=>[],array:r5,boolean:function(e){let t=e.value,r=e.key,n=rH(t);return"boolean"!==n?[new eL(r,t,`boolean expected, ${n} found`)]:[]},number:r3,color:r6,constants:rQ,enum:r9,filter:r7,function:r4,layer:nn,object:r2,source:ni,light:nl,sky:ns,terrain:nu,projection:function(e){let t=e.value,r=e.styleSpec,n=r.projection,o=e.style,a=rH(t);if(void 0===t)return[];if("object"!==a)return[new eL("projection",t,`object expected, ${a} found`)];let i=[];for(let a in t)i=n[a]?i.concat(e.validateSpec({key:a,value:t[a],valueSpec:n[a],style:o,styleSpec:r})):i.concat([new eL(a,t[a],`unknown property "${a}"`)]);return i},projectionDefinition:function(e){var t;let r=e.key,n=e.value,o=rH(n=n instanceof String?n.valueOf():n);return"array"!==o||Array.isArray(t=n)&&3===t.length&&"string"==typeof t[0]&&"string"==typeof t[1]&&"number"==typeof t[2]||["interpolate","step","literal"].includes(n[0])?["array","string"].includes(o)?[]:[new eL(r,n,`projection expected, invalid type "${o}" found`)]:[new eL(r,n,`projection expected, invalid array ${JSON.stringify(n)} found`)]},string:no,formatted:function(e){return 0===no(e).length?[]:r8(e)},resolvedImage:function(e){return 0===no(e).length?[]:r8(e)},padding:function(e){let t=e.key,r=e.value;if("array"!==rH(r))return r3({key:t,value:r,valueSpec:{}});{if(r.length<1||r.length>4)return[new eL(t,r,`padding requires 1 to 4 values; ${r.length} values found`)];let n={type:"number"},o=[];for(let a=0;a<r.length;a++)o=o.concat(e.validateSpec({key:`${t}[${a}]`,value:r[a],validateSpec:e.validateSpec,valueSpec:n}));return o}},numberArray:function(e){let t=e.key,r=e.value;if("array"!==rH(r))return r3({key:t,value:r,valueSpec:{}});{let n={type:"number"};if(r.length<1)return[new eL(t,r,"array length at least 1 expected, length 0 found")];let o=[];for(let a=0;a<r.length;a++)o=o.concat(e.validateSpec({key:`${t}[${a}]`,value:r[a],validateSpec:e.validateSpec,valueSpec:n}));return o}},colorArray:function(e){let t=e.key,r=e.value;if("array"!==rH(r))return r6({key:t,value:r});{if(r.length<1)return[new eL(t,r,"array length at least 1 expected, length 0 found")];let e=[];for(let n=0;n<r.length;n++)e=e.concat(r6({key:`${t}[${n}]`,value:r[n]}));return e}},variableAnchorOffsetCollection:function(e){let t=e.key,r=e.value,n=rH(r),o=e.styleSpec;if("array"!==n||r.length<1||r.length%2!=0)return[new eL(t,r,"variableAnchorOffsetCollection requires a non-empty array of even length")];let a=[];for(let n=0;n<r.length;n+=2)a=(a=a.concat(r9({key:`${t}[${n}]`,value:r[n],valueSpec:o.layout_symbol["text-anchor"]}))).concat(r5({key:`${t}[${n+1}]`,value:r[n+1],valueSpec:{length:2,value:"number"},validateSpec:e.validateSpec,style:e.style,styleSpec:o}));return a},sprite:np,state:nc};function nf(e){let t=e.value,r=e.valueSpec,n=e.styleSpec;return e.validateSpec=nf,r.expression&&rV(r0(t))?r4(e):r.expression&&rK(r1(t))?r8(e):r.type&&nd[r.type]?nd[r.type](e):r2(eC({},e,{valueSpec:r.type?n[r.type]:r}))}function ny(e){let t=e.value,r=e.key,n=no(e);return n.length||(-1===t.indexOf("{fontstack}")&&n.push(new eL(r,t,'"glyphs" url must include a "{fontstack}" token')),-1===t.indexOf("{range}")&&n.push(new eL(r,t,'"glyphs" url must include a "{range}" token'))),n}function nh(e,t=ek){let r=[];return r=r.concat(nf({key:"",value:e,valueSpec:t.$root,styleSpec:t,style:e,validateSpec:nf,objectElementValidators:{glyphs:ny,"*":()=>[]}})),e.constants&&(r=r.concat(rQ({key:"constants",value:e.constants}))),ng(r)}function nm(e){return function(t){return e({...t,validateSpec:nf})}}function ng(e){return[].concat(e).sort((e,t)=>e.line-t.line)}function nv(e){return function(...t){return ng(e.apply(this,t))}}function nb(e){if(!e)return{style:v.UH[v.yR[0].referenceStyleID].getDefaultVariant().getExpandedStyleURL(),requiresUrlMonitoring:!1,isFallback:!0};if("string"==typeof e){let t=function(e){try{let t=JSON.parse(e),r=nh(t);return{isValidJSON:!0,isValidStyle:0===r.length,styleObject:0===r.length?t:null}}catch{return{isValidJSON:!1,isValidStyle:!1,styleObject:null}}}(e);return t.isValidStyle?{style:t.styleObject,requiresUrlMonitoring:!1,isFallback:!1}:t.isValidJSON?{style:v.UH[v.yR[0].referenceStyleID].getDefaultVariant().getExpandedStyleURL(),requiresUrlMonitoring:!1,isFallback:!0}:e.startsWith("http")?{style:e,requiresUrlMonitoring:!0,isFallback:!1}:e.toLowerCase().includes(".json")?{style:function(e){try{return new URL(e).href}catch{}return new URL(e,location.origin).href}(e),requiresUrlMonitoring:!0,isFallback:!1}:{style:(0,v.HI)(e),requiresUrlMonitoring:!0,isFallback:!1}}return e instanceof v.YW?{style:e.getExpandedStyleURL(),requiresUrlMonitoring:!1,isFallback:!1}:e instanceof v.jD?{style:e.getDefaultVariant().getExpandedStyleURL(),requiresUrlMonitoring:!1,isFallback:!1}:0===nh(e).length?{style:e,requiresUrlMonitoring:!1,isFallback:!1}:{style:v.UH[v.yR[0].referenceStyleID].getDefaultVariant().getExpandedStyleURL(),requiresUrlMonitoring:!1,isFallback:!0}}function nx(e,t,r){let n=window.document.createElement(e);return void 0!==t&&(n.className=t),r&&r.appendChild(n),n}function nw(e){e.parentNode&&e.parentNode.removeChild(e)}nh.source=nv(nm(ni)),nh.sprite=nv(nm(np)),nh.glyphs=nv(nm(ny)),nh.light=nv(nm(nl)),nh.sky=nv(nm(ns)),nh.terrain=nv(nm(nu)),nh.state=nv(nm(nc)),nh.layer=nv(nm(nn)),nh.filter=nv(nm(r7)),nh.paintProperty=nv(nm(nt)),nh.layoutProperty=nv(nm(nr));class nS{constructor(){C(this,"_map"),C(this,"_container"),C(this,"_terrainButton"),function(e,t){for(let r of e)"function"==typeof t[r]&&(t[r]=t[r].bind(t))}(["_toggleTerrain","_updateTerrainIcon"],this)}onAdd(e){return this._map=e,this._container=nx("div","maplibregl-ctrl maplibregl-ctrl-group"),this._terrainButton=nx("button","maplibregl-ctrl-terrain",this._container),nx("span","maplibregl-ctrl-icon",this._terrainButton).setAttribute("aria-hidden","true"),this._terrainButton.type="button",this._terrainButton.addEventListener("click",this._toggleTerrain),this._updateTerrainIcon(),this._map.on("terrain",this._updateTerrainIcon),this._container}onRemove(){nw(this._container),this._map.off("terrain",this._updateTerrainIcon),this._map=void 0}_toggleTerrain(){this._map.hasTerrain()?this._map.disableTerrain():this._map.enableTerrain(),this._updateTerrainIcon()}_updateTerrainIcon(){this._terrainButton.classList.remove("maplibregl-ctrl-terrain"),this._terrainButton.classList.remove("maplibregl-ctrl-terrain-enabled"),this._map.hasTerrain()?(this._terrainButton.classList.add("maplibregl-ctrl-terrain-enabled"),this._terrainButton.title=this._map._getUIString("TerrainControl.Disable")):(this._terrainButton.classList.add("maplibregl-ctrl-terrain"),this._terrainButton.title=this._map._getUIString("TerrainControl.Enable"))}}class nk extends el{constructor(e={}){super({showCompass:e.showCompass??!0,showZoom:e.showZoom??!0,visualizePitch:e.visualizePitch??!0}),C(this,"_rotateCompassArrow",()=>{let e=this._map.getBearing(),t=this._map.getPitch(),r=this.options.visualizePitch?`scale(${Math.min(1.5,1/Math.cos(Math.PI/180*t)**.5)}) rotateX(${Math.min(70,t)}deg) rotateZ(${-e}deg)`:`rotate(${-e}deg)`;this._compassIcon.style.transform=r}),this._compass&&(this._compass.removeEventListener("click",this._compass.clickFunction),this._compass.addEventListener("click",e=>{0===this._map.getPitch()?this._map.easeTo({pitch:Math.min(this._map.getMaxPitch(),80)}):this.options.visualizePitch?this._map.resetNorthPitch({},{originalEvent:e}):this._map.resetNorth({},{originalEvent:e})}))}_createButton(e,t){let r=super._createButton(e,t);return r.clickFunction=t,r}}let nL=g.Marker,nC=g.LngLat,nA=g.LngLatBounds;class n_ extends es{constructor(){super(...arguments),C(this,"lastUpdatedCenter",new nC(0,0)),C(this,"_updateCamera",e=>{var t;let r=new nC(e.coords.longitude,e.coords.latitude),n=e.coords.accuracy,o={bearing:this._map.getBearing(),...this.options.fitBoundsOptions,linear:!0},a=this._map.getZoom();a>((null==(t=this.options.fitBoundsOptions)?void 0:t.maxZoom)??30)&&(o.zoom=a),this._map.fitBounds(nA.fromLngLat(r,n),o,{geolocateSource:!0});let i=!1,l=()=>{i=!0};this._map.once("click",l),this._map.once("dblclick",l),this._map.once("dragstart",l),this._map.once("mousedown",l),this._map.once("touchstart",l),this._map.once("wheel",l),this._map.once("moveend",()=>{this._map.off("click",l),this._map.off("dblclick",l),this._map.off("dragstart",l),this._map.off("mousedown",l),this._map.off("touchstart",l),this._map.off("wheel",l),i||(this.lastUpdatedCenter=this._map.getCenter())})}),C(this,"_finishSetupUI",e=>{if(this._map){if(!1===e){let e=this._map._getUIString("GeolocateControl.LocationNotAvailable");this._geolocateButton.disabled=!0,this._geolocateButton.title=e,this._geolocateButton.setAttribute("aria-label",e)}else{let e=this._map._getUIString("GeolocateControl.FindMyLocation");this._geolocateButton.disabled=!1,this._geolocateButton.title=e,this._geolocateButton.setAttribute("aria-label",e)}this.options.trackUserLocation&&(this._geolocateButton.setAttribute("aria-pressed","false"),this._watchState="OFF"),this.options.showUserLocation&&(this._dotElement=nx("div","maplibregl-user-location-dot"),this._userLocationDotMarker=new nL({element:this._dotElement}),this._circleElement=nx("div","maplibregl-user-location-accuracy-circle"),this._accuracyCircleMarker=new nL({element:this._circleElement,pitchAlignment:"map"}),this.options.trackUserLocation&&(this._watchState="OFF"),this._map.on("move",this._onZoom)),this._geolocateButton.addEventListener("click",this.trigger.bind(this)),this._setup=!0,this.options.trackUserLocation&&this._map.on("moveend",e=>{let t=e.originalEvent&&"resize"===e.originalEvent.type,r=this.lastUpdatedCenter.distanceTo(this._map.getCenter());e.geolocateSource||"ACTIVE_LOCK"!==this._watchState||t||!(r>1)||(this._watchState="BACKGROUND",this._geolocateButton.classList.add("maplibregl-ctrl-geolocate-background"),this._geolocateButton.classList.remove("maplibregl-ctrl-geolocate-active"),this.fire(new Event("trackuserlocationend")))})}}),C(this,"_onZoom",()=>{this.options.showUserLocation&&this.options.showAccuracyCircle&&this._updateCircleRadius()})}_updateCircleRadius(){if("BACKGROUND"!==this._watchState&&"ACTIVE_LOCK"!==this._watchState)return;let e=[this._lastKnownPosition.coords.longitude,this._lastKnownPosition.coords.latitude],t=this._map.project(e),r=this._map.unproject([t.x,t.y]),n=this._map.unproject([t.x+20,t.y]),o=r.distanceTo(n)/20,a=Math.ceil(2*this._accuracy/o);this._circleElement.style.width=`${a}px`,this._circleElement.style.height=`${a}px`}_setErrorState(){switch(this._watchState){case"WAITING_ACTIVE":this._watchState="ACTIVE_ERROR",this._geolocateButton.classList.remove("maplibregl-ctrl-geolocate-active"),this._geolocateButton.classList.add("maplibregl-ctrl-geolocate-active-error");break;case"ACTIVE_LOCK":this._watchState="ACTIVE_ERROR",this._geolocateButton.classList.remove("maplibregl-ctrl-geolocate-active"),this._geolocateButton.classList.add("maplibregl-ctrl-geolocate-active-error"),this._geolocateButton.classList.add("maplibregl-ctrl-geolocate-waiting");break;case"BACKGROUND":this._watchState="BACKGROUND_ERROR",this._geolocateButton.classList.remove("maplibregl-ctrl-geolocate-background"),this._geolocateButton.classList.add("maplibregl-ctrl-geolocate-background-error"),this._geolocateButton.classList.add("maplibregl-ctrl-geolocate-waiting");break;case"ACTIVE_ERROR":case"BACKGROUND_ERROR":break;default:throw Error(`Unexpected watchState ${this._watchState}`)}}}class nz{constructor(e,t){z(this,f),z(this,l),C(this,"map"),z(this,s),z(this,u),z(this,p),z(this,c,!1),z(this,d),void 0!==e.style&&M(this,c,!0),M(this,l,{zoomAdjust:-4,position:"top-right",...t,forceNoAttributionControl:!0,attributionControl:!1,navigationControl:!1,geolocateControl:!1,maptilerLogo:!1,minimap:!1,hash:!1,pitchAdjust:!1,...e,containerStyle:{border:"1px solid #000",width:"400px",height:"300px",...e.containerStyle??{}}}),void 0!==e.lockZoom&&(_(this,l).minZoom=e.lockZoom,_(this,l).maxZoom=e.lockZoom)}setStyle(e,t){_(this,c)||this.map.setStyle(e,t),T(this,f,h).call(this)}addLayer(e,t){return _(this,c)||this.map.addLayer(e,t),T(this,f,h).call(this),this.map}moveLayer(e,t){return _(this,c)||this.map.moveLayer(e,t),T(this,f,h).call(this),this.map}removeLayer(e){return _(this,c)||this.map.removeLayer(e),T(this,f,h).call(this),this}setLayerZoomRange(e,t,r){return _(this,c)||this.map.setLayerZoomRange(e,t,r),T(this,f,h).call(this),this}setFilter(e,t,r){return _(this,c)||this.map.setFilter(e,t,r),T(this,f,h).call(this),this}setPaintProperty(e,t,r,n){return _(this,c)||this.map.setPaintProperty(e,t,r,n),T(this,f,h).call(this),this}setLayoutProperty(e,t,r,n){return _(this,c)||this.map.setLayoutProperty(e,t,r,n),T(this,f,h).call(this),this}setGlyphs(e,t){return _(this,c)||this.map.setGlyphs(e,t),T(this,f,h).call(this),this}onAdd(e){for(let[t,r]of(M(this,s,e),M(this,u,nx("div","maplibregl-ctrl maplibregl-ctrl-group")),Object.entries(_(this,l).containerStyle)))_(this,u).style.setProperty(t,r);return _(this,l).container=_(this,u),_(this,l).zoom=e.getZoom()+_(this,l).zoomAdjust,this.map=new n$(_(this,l)),this.map.once("style.load",()=>{this.map.resize()}),this.map.once("load",()=>{T(this,f,y).call(this,_(this,l).parentRect),M(this,d,T(this,f,m).call(this))}),_(this,u)}onRemove(){var e;null==(e=_(this,d))||e.call(this),nw(_(this,u))}}l=new WeakMap,s=new WeakMap,u=new WeakMap,p=new WeakMap,c=new WeakMap,d=new WeakMap,f=new WeakSet,y=function(e){void 0===e||void 0===e.linePaint&&void 0===e.fillPaint||(M(this,p,{type:"Feature",properties:{name:"parentRect"},geometry:{type:"Polygon",coordinates:[[[],[],[],[],[]]]}}),this.map.addSource("parentRect",{type:"geojson",data:_(this,p)}),(void 0!==e.lineLayout||void 0!==e.linePaint)&&this.map.addLayer({id:"parentRectOutline",type:"line",source:"parentRect",layout:{...e.lineLayout},paint:{"line-color":"#FFF","line-width":1,"line-opacity":.85,...e.linePaint}}),void 0!==e.fillPaint&&this.map.addLayer({id:"parentRectFill",type:"fill",source:"parentRect",layout:{},paint:{"fill-color":"#08F","fill-opacity":.135,...e.fillPaint}}),T(this,f,h).call(this))},h=function(){if(void 0===_(this,p))return;let{devicePixelRatio:e}=window,t=_(this,s).getCanvas(),r=t.width/e,n=t.height/e,o=_(this,s).unproject.bind(_(this,s)),a=o([0,0]),i=o([r,0]),l=o([0,n]),u=o([r,n]);_(this,p).geometry.coordinates=[[l.toArray(),u.toArray(),i.toArray(),a.toArray(),l.toArray()]];let c=this.map.getSource("parentRect");void 0!==c&&c.setData(_(this,p))},m=function(){let{pitchAdjust:e}=_(this,l),t=()=>{a("parent")},r=()=>{a("minimap")},n=()=>{_(this,s).on("move",t),this.map.on("move",r)},o=()=>{_(this,s).off("move",t),this.map.off("move",r)},a=t=>{o();let r="parent"===t?_(this,s):this.map,a="parent"===t?this.map:_(this,s),i=r.getCenter(),u=r.getZoom()+_(this,l).zoomAdjust*("parent"===t?1:-1),p=r.getBearing(),c=r.getPitch();a.jumpTo({center:i,zoom:u,bearing:p,pitch:e?c:0}),T(this,f,h).call(this),n()};return n(),()=>{o()}};class nM{constructor(){C(this,"map"),C(this,"container"),C(this,"projectionButton")}onAdd(e){return this.map=e,this.container=nx("div","maplibregl-ctrl maplibregl-ctrl-group"),this.projectionButton=nx("button","maplibregl-ctrl-projection",this.container),nx("span","maplibregl-ctrl-icon",this.projectionButton).setAttribute("aria-hidden","true"),this.projectionButton.type="button",this.projectionButton.addEventListener("click",this.toggleProjection.bind(this)),e.on("projectiontransition",this.updateProjectionIcon.bind(this)),this.updateProjectionIcon(),this.container}onRemove(){nw(this.container),this.map.off("projectiontransition",this.updateProjectionIcon),this.map=void 0}toggleProjection(){void 0===this.map.getProjection()&&this.map.setProjection({type:"mercator"}),this.map.isGlobeProjection()?this.map.enableMercatorProjection():this.map.enableGlobeProjection(),this.updateProjectionIcon()}updateProjectionIcon(){this.projectionButton.classList.remove("maplibregl-ctrl-projection-globe"),this.projectionButton.classList.remove("maplibregl-ctrl-projection-mercator"),this.map.isGlobeProjection()?(this.projectionButton.classList.add("maplibregl-ctrl-projection-mercator"),this.projectionButton.title="Enable Mercator projection"):(this.projectionButton.classList.add("maplibregl-ctrl-projection-globe"),this.projectionButton.title="Enable Globe projection")}}class nT{constructor(e,t=2e3){C(this,"map"),C(this,"registeredModules",new Set),this.map=e,setTimeout(async()=>{if(!j.telemetry)return;let e=this.preparePayload();try{(await fetch(e,{method:"POST"})).ok||console.warn("The metrics could not be sent to MapTiler Cloud")}catch(e){console.warn("The metrics could not be sent to MapTiler Cloud",e)}},Math.max(1e3,t))}registerModule(e,t){this.registeredModules.add(`${e}:${t}`)}preparePayload(){let e=new URL(I.telemetryURL);return e.searchParams.append("sdk","3.3.0"),e.searchParams.append("key",j.apiKey),e.searchParams.append("mtsid",P),e.searchParams.append("session",j.session?"1":"0"),e.searchParams.append("caching",j.caching?"1":"0"),e.searchParams.append("lang-updated",this.map.isLanguageUpdated()?"1":"0"),e.searchParams.append("terrain",this.map.getTerrain()?"1":"0"),e.searchParams.append("globe",this.map.isGlobeProjection()?"1":"0"),this.registeredModules.size>0&&e.searchParams.append("modules",Array.from(this.registeredModules).join("|")),e.href}}let nE={POINT:"POINT",COUNTRY:"COUNTRY"},n$=class e extends g.Map{constructor(e){(function(e){let t=K();if(!t)return;let r=null;if("string"==typeof e?r=document.getElementById(e):e instanceof HTMLElement&&(r=e),!r)throw Error("The Map container must be provided.");let n=document.createElement("div");throw n.innerHTML=t,n.classList.add("webgl-warning-div"),r.appendChild(n),Error(t)})(e.container),e.apiKey&&(j.apiKey=e.apiKey);let{style:t,requiresUrlMonitoring:r,isFallback:n}=nb(e.style);n&&console.warn("Invalid style. A style must be a valid URL to a style.json, a JSON string representing a valid StyleSpecification or a valid StyleSpecification object. Fallback to default MapTiler style."),j.apiKey||console.warn("MapTiler Cloud API key is not set. Visit https://maptiler.com and try Cloud for free!");let o=location.hash,a={compact:!1};e.customAttribution?a.customAttribution=e.customAttribution:e.attributionControl&&"object"==typeof e.attributionControl&&(a={...a,...e.attributionControl});let i={...e,style:t,maplibreLogo:!1,transformRequest:H(e.transformRequest),attributionControl:!0!==e.forceNoAttributionControl&&a};delete i.style,super(i),C(this,"options"),C(this,"telemetry"),C(this,"isTerrainEnabled",!1),C(this,"terrainExaggeration",1),C(this,"primaryLanguage"),C(this,"terrainGrowing",!1),C(this,"terrainFlattening",!1),C(this,"minimap"),C(this,"forceLanguageUpdate"),C(this,"languageAlwaysBeenStyle"),C(this,"isReady",!1),C(this,"terrainAnimationDuration",1e3),C(this,"monitoredStyleUrls"),C(this,"styleInProcess",!1),C(this,"curentProjection"),C(this,"originalLabelStyle",new window.Map),C(this,"isStyleLocalized",!1),C(this,"languageIsUpdated",!1),this.options=e,this.setStyle(t),r&&this.monitorStyleUrl(t);let l=()=>{let e="The distant style could not be loaded.";this.getStyle()?e+="Leaving the style as is.":(this.setStyle(v.UH.STREETS),e+=`Loading default MapTiler Cloud style "${v.UH.STREETS.getDefaultVariant().getId()}" as a fallback.`),console.warn(e)};if(this.on("style.load",()=>{this.styleInProcess=!1}),this.on("error",e=>{if(e.error instanceof g.AJAXError){let t=new URL(e.error.url);t.search="";let r=t.href;this.monitoredStyleUrls&&this.monitoredStyleUrls.has(r)&&(this.monitoredStyleUrls.delete(r),l());return}if(this.styleInProcess){l();return}}),j.caching&&!F&&console.warn("The cache API is only available in secure contexts. More info at https://developer.mozilla.org/en-US/docs/Web/API/Cache"),j.caching&&F&&(q(N,async(e,t)=>{if(!e.url)throw Error("");e.url=e.url.replace(`${N}://`,"https://"),e.signal=t.signal;let r=await fetch(e.url,e),n=await r.json();return n.tiles&&n.tiles.length>0&&(n.tiles[0]+=`&last-modified=${r.headers.get("Last-Modified")}`),{data:n,cacheControl:r.headers.get("Cache-Control"),expires:r.headers.get("Expires")}}),q(O,async(e,t)=>{if(!e.url)throw Error("");e.url=e.url.replace(`${O}://`,"https://");let r=new URL(e.url),n=new URL(r);n.searchParams.delete("mtsid"),n.searchParams.delete("key");let o=n.toString(),a=new URL(r);a.searchParams.delete("last-modified");let i=a.toString(),l=async e=>({data:await e.arrayBuffer(),cacheControl:e.headers.get("Cache-Control"),expires:e.headers.get("Expires")}),s=await D(),u=await s.match(o);if(u)return l(u);e.signal=t.signal;let p=await fetch(i,e);return p.status>=200&&p.status<300&&(s.put(o,p.clone()).catch(()=>{}),++U>100&&(B(),U=0)),l(p)})),typeof e.language>"u")this.primaryLanguage=j.primaryLanguage;else{let t=(0,v.eS)(e.language,E);this.primaryLanguage=t??j.primaryLanguage}this.forceLanguageUpdate=!(this.primaryLanguage===E.STYLE||this.primaryLanguage===E.STYLE_LOCK),this.languageAlwaysBeenStyle=this.primaryLanguage===E.STYLE,this.terrainExaggeration=e.terrainExaggeration??this.terrainExaggeration,this.curentProjection=e.projection,this.on("styledata",()=>{"mercator"===this.curentProjection?this.setProjection({type:"mercator"}):"globe"===this.curentProjection&&this.setProjection({type:"globe"})}),this.once("styledata",async()=>{let t;if(e.geolocate&&!e.center&&(!e.hash||!o)){try{if(e.geolocate===nE.COUNTRY){await this.fitToIpBounds();return}}catch(e){console.warn(e.message)}try{await this.centerOnIpPoint(e.zoom),t=this.getCameraHash()}catch(e){console.warn(e.message)}"granted"===(await navigator.permissions.query({name:"geolocation"})).state&&navigator.geolocation.getCurrentPosition(r=>{t===this.getCameraHash()&&(this.terrain?this.easeTo({center:[r.coords.longitude,r.coords.latitude],zoom:e.zoom||12,duration:2e3}):this.once("terrain",()=>{this.easeTo({center:[r.coords.longitude,r.coords.latitude],zoom:e.zoom||12,duration:2e3})}))},null,{maximumAge:864e5,timeout:5e3,enableHighAccuracy:!1})}}),this.on("styledata",()=>{this.setPrimaryLanguage(this.primaryLanguage)}),this.on("styledata",()=>{null===this.getTerrain()&&this.isTerrainEnabled&&this.enableTerrain(this.terrainExaggeration)}),this.once("load",async()=>{let t={logo:null};try{let e=Object.keys(this.style.sourceCaches).map(e=>this.getSource(e)).filter(e=>e&&"url"in e&&"string"==typeof e.url&&e.url.includes("tiles.json")),r=new URL(e[0].url);r.searchParams.has("key")||r.searchParams.append("key",j.apiKey),t=await (await fetch(r.href)).json()}catch{}if(!0!==e.forceNoAttributionControl){if("logo"in t&&t.logo){let r=t.logo;this.addControl(new eS({logoURL:r}),e.logoPosition)}else e.maptilerLogo&&this.addControl(new eS,e.logoPosition)}if(e.scaleControl){let t=!0===e.scaleControl||void 0===e.scaleControl?"bottom-right":e.scaleControl,r=new ec({unit:j.unit});this.addControl(r,t),j.on("unit",e=>{r.setUnit(e)})}if(!1!==e.navigationControl){let t=!0===e.navigationControl||void 0===e.navigationControl?"top-right":e.navigationControl;this.addControl(new nk,t)}if(!1!==e.geolocateControl){let t=!0===e.geolocateControl||void 0===e.geolocateControl?"top-right":e.geolocateControl;this.addControl(new n_({positionOptions:{enableHighAccuracy:!0,maximumAge:0,timeout:6e3},fitBoundsOptions:{maxZoom:15},trackUserLocation:!0,showAccuracyCircle:!0,showUserLocation:!0}),t)}if(e.terrainControl){let t=!0===e.terrainControl||void 0===e.terrainControl?"top-right":e.terrainControl;this.addControl(new nS,t)}if(e.projectionControl){let t=!0===e.projectionControl||void 0===e.projectionControl?"top-right":e.projectionControl;this.addControl(new nM,t)}if(e.fullscreenControl){let t=!0===e.fullscreenControl||void 0===e.fullscreenControl?"top-right":e.fullscreenControl;this.addControl(new ed({}),t)}this.isReady=!0,this.fire("ready",{target:this})});let s=!1,u=!1,p;this.once("ready",()=>{s=!0,u&&this.fire("loadWithTerrain",p)}),this.once("style.load",()=>{let{minimap:t}=e;if("object"==typeof t){let{zoom:r,center:n,style:o,language:a,apiKey:i,maptilerLogo:l,canvasContextAttributes:s,refreshExpiredTiles:u,maxBounds:p,scrollZoom:c,minZoom:d,maxZoom:f,boxZoom:y,locale:h,fadeDuration:m,crossSourceCollisions:g,clickTolerance:v,bounds:b,fitBoundsOptions:x,pixelRatio:w,validateStyle:S}=e;this.minimap=new nz(t,{zoom:r,center:n,style:o,language:a,apiKey:i,container:"null",maptilerLogo:l,canvasContextAttributes:s,refreshExpiredTiles:u,maxBounds:p,scrollZoom:c,minZoom:d,maxZoom:f,boxZoom:y,locale:h,fadeDuration:m,crossSourceCollisions:g,clickTolerance:v,bounds:b,fitBoundsOptions:x,pixelRatio:w,validateStyle:S}),this.addControl(this.minimap,t.position??"bottom-left")}else!0===t?(this.minimap=new nz({},e),this.addControl(this.minimap,"bottom-left")):void 0!==t&&!1!==t&&(this.minimap=new nz({},e),this.addControl(this.minimap,t))});let c=e=>{e.terrain&&(u=!0,p={type:"loadWithTerrain",target:this,terrain:e.terrain},this.off("terrain",c),s&&this.fire("loadWithTerrain",p))};this.on("terrain",c),e.terrain&&this.enableTerrain(e.terrainExaggeration??this.terrainExaggeration),this.once("load",()=>{this.getCanvas().addEventListener("webglcontextlost",e=>{if(!0===this._removed){console.warn("[webglcontextlost]","WebGL context lost after map removal. This is harmless.");return}console.warn("[webglcontextlost]","Unexpected loss of WebGL context!"),this.fire("webglContextLost",e)})}),this.telemetry=new nT(this)}recreate(){let t={center:this.getCenter(),zoom:this.getZoom(),bearing:this.getBearing(),pitch:this.getPitch()};this.remove(),Object.assign(this,new e({...this.options})),this.once("load",()=>{this.jumpTo(t)})}setTerrainAnimationDuration(e){this.terrainAnimationDuration=Math.max(e,0)}async onLoadAsync(){return new Promise(e=>{if(this.loaded()){e(this);return}this.once("load",()=>{e(this)})})}async onReadyAsync(){return new Promise(e=>{if(this.isReady){e(this);return}this.once("ready",()=>{e(this)})})}async onLoadWithTerrainAsync(){return new Promise(e=>{if(this.isReady&&this.terrain){e(this);return}this.once("loadWithTerrain",()=>{e(this)})})}monitorStyleUrl(e){typeof this.monitoredStyleUrls>"u"&&(this.monitoredStyleUrls=new Set);let t=new URL(e);t.search="",this.monitoredStyleUrls.add(t.href)}setStyle(e,t){var r;this.originalLabelStyle.clear(),null==(r=this.minimap)||r.setStyle(e),this.forceLanguageUpdate=!0,this.once("idle",()=>{this.forceLanguageUpdate=!1});let n=nb(e);if(n.requiresUrlMonitoring&&this.monitorStyleUrl(n.style),n.isFallback){if(this.getStyle())return console.warn("Invalid style. A style must be a valid URL to a style.json, a JSON string representing a valid StyleSpecification or a valid StyleSpecification object. Keeping the curent style instead."),this;console.warn("Invalid style. A style must be a valid URL to a style.json, a JSON string representing a valid StyleSpecification or a valid StyleSpecification object. Fallback to default MapTiler style.")}return this.styleInProcess=!0,super.setStyle(n.style,t),this}addLayer(e,t){var r;return null==(r=this.minimap)||r.addLayer(e,t),super.addLayer(e,t)}moveLayer(e,t){var r;return null==(r=this.minimap)||r.moveLayer(e,t),super.moveLayer(e,t)}removeLayer(e){var t;return null==(t=this.minimap)||t.removeLayer(e),super.removeLayer(e)}setLayerZoomRange(e,t,r){var n;return null==(n=this.minimap)||n.setLayerZoomRange(e,t,r),super.setLayerZoomRange(e,t,r)}setFilter(e,t,r){var n;return null==(n=this.minimap)||n.setFilter(e,t,r),super.setFilter(e,t,r)}setPaintProperty(e,t,r,n){var o;return null==(o=this.minimap)||o.setPaintProperty(e,t,r,n),super.setPaintProperty(e,t,r,n)}setLayoutProperty(e,t,r,n){var o;return null==(o=this.minimap)||o.setLayoutProperty(e,t,r,n),super.setLayoutProperty(e,t,r,n)}setGlyphs(e,t){var r;return null==(r=this.minimap)||r.setGlyphs(e,t),super.setGlyphs(e,t)}getStyleLanguage(){return this.style&&this.style.stylesheet&&this.style.stylesheet.metadata&&"object"==typeof this.style.stylesheet.metadata&&"maptiler:language"in this.style.stylesheet.metadata&&"string"==typeof this.style.stylesheet.metadata["maptiler:language"]?(0,v.S3)(this.style.stylesheet.metadata["maptiler:language"]):null}setLanguage(e){var t;null==(t=this.minimap)||t.map.setLanguage(e),this.onStyleReady(()=>{this.setPrimaryLanguage(e)})}setPrimaryLanguage(e){let t=this.getStyleLanguage(),r=(0,v.eS)(e,E);if(!r){console.warn(`The language "${r}" is not supported.`);return}if(!(r.flag===E.STYLE.flag&&t&&(t.flag===E.AUTO.flag||t.flag===E.VISITOR.flag))&&(r.flag!==E.STYLE.flag&&(this.languageAlwaysBeenStyle=!1),this.languageAlwaysBeenStyle||this.primaryLanguage===r&&!this.forceLanguageUpdate))return;if(this.primaryLanguage.flag===E.STYLE_LOCK.flag){console.warn("The language cannot be changed because this map has been instantiated with the STYLE_LOCK language flag.");return}this.primaryLanguage=r;let n=r;if(r.flag===E.STYLE.flag){if(!t){console.warn("The style has no default languages or has an invalid one.");return}n=t}let o=E.LOCAL.flag,a=["get",o];a=n.flag===E.VISITOR.flag?["case",["all",["has",o=$().flag],["has",E.LOCAL.flag]],["case",["==",["get",o],["get",E.LOCAL.flag]],["get",E.LOCAL.flag],["format",["get",o],{"font-scale":.8},`
`,["get",E.LOCAL.flag],{"font-scale":1.1}]],["get",E.LOCAL.flag]]:n.flag===E.VISITOR_ENGLISH.flag?["case",["all",["has",o=E.ENGLISH.flag],["has",E.LOCAL.flag]],["case",["==",["get",o],["get",E.LOCAL.flag]],["get",E.LOCAL.flag],["format",["get",o],{"font-scale":.8},`
`,["get",E.LOCAL.flag],{"font-scale":1.1}]],["get",E.LOCAL.flag]]:n.flag===E.AUTO.flag?["coalesce",["get",o=$().flag],["get",E.LOCAL.flag]]:n===E.LOCAL?["get",o=E.LOCAL.flag]:["coalesce",["get",o=n.flag],["get",E.LOCAL.flag]];let{layers:i}=this.getStyle(),l=0===this.originalLabelStyle.size;if(l){let e=function(e,t){let r=[];for(let n of e){if("symbol"!==n.type)continue;let{id:e,layout:o}=n;if(!o||!("text-field"in o))continue;let a=t.getLayoutProperty(e,"text-field");if(a){if("string"==typeof a){let e=function(e){var t;let r=/\{name(?::(?<language>\S+))?\}/g,n=[];for(;;){let o=r.exec(e);if(!o)break;let a=(null==(t=o.groups)?void 0:t.language)??null;n.push(a)}return n}(a);r.push(e)}else{let e=function(e){let t=[],r=structuredClone(e),n=e=>{if("string"!=typeof e)for(let o=0;o<e.length;o+=1){var r;let a=Array.isArray(r=e[o])&&2===r.length&&"get"===r[0]&&"string"==typeof r[1]?"name"===r[1].trim()?{isLanguage:!0,localization:null}:r[1].trim().startsWith("name:")?{isLanguage:!0,localization:r[1].trim().split(":").pop()}:null:null;a?t.push(a.localization):n(e[o])}};return n([r]),t}(a);r.push(e)}}}let n=r.flat(),o={unlocalized:0,localized:{}};for(let e of n)null===e?o.unlocalized+=1:(e in o.localized||(o.localized[e]=0),o.localized[e]+=1);return o}(i,this);this.isStyleLocalized=Object.keys(e.localized).length>0}for(let e of i){let t;if("symbol"!==e.type)continue;let r=this.getSource(e.source);if(!r||!("url"in r&&"string"==typeof r.url)||new URL(r.url).host!==I.maptilerApiHost)continue;let{id:n,layout:o}=e;if(o&&"text-field"in o){if(l?(t=this.getLayoutProperty(n,"text-field"),this.originalLabelStyle.set(n,t)):t=this.originalLabelStyle.get(n),"string"==typeof t){let{contains:e,exactMatch:r}=function(e,t){let r=t?/\{name:\S+\}/:/\{name\}/;return{contains:r.test(e),exactMatch:RegExp(`^${r.source}$`).test(e)}}(t,this.isStyleLocalized);if(!e)continue;if(r)this.setLayoutProperty(n,"text-field",a);else{let e=function(e,t,r){let n=e.split(r?/\{name:\S+\}/:/\{name\}/);return["concat",...n.flatMap((e,r)=>r===n.length-1?[e]:[e,t])]}(t,a,this.isStyleLocalized);this.setLayoutProperty(n,"text-field",e)}}else{let e=function(e,t,r){let n=structuredClone(e),o=e=>{if("string"!=typeof e)for(let n=0;n<e.length;n+=1)J(e[n],r)?e[n]=structuredClone(t):o(e[n])};return J(n,r)?t:(o(n),n)}(t,a,this.isStyleLocalized);this.setLayoutProperty(n,"text-field",e)}}}this.languageIsUpdated=!0}getPrimaryLanguage(){return this.primaryLanguage}getTerrainExaggeration(){return this.terrainExaggeration}hasTerrain(){return this.isTerrainEnabled}growTerrain(e){if(!this.terrain)return;let t=performance.now(),r=this.terrain.exaggeration,n=e-r,o=()=>{if(!this.terrain||this.terrainFlattening)return;let a=(performance.now()-t)/this.terrainAnimationDuration;a<.99?(this.terrain.exaggeration=r+(1-(1-a)**4)*n,requestAnimationFrame(o)):(this.terrainGrowing=!1,this.terrainFlattening=!1,this.terrain.exaggeration=e,this.fire("terrainAnimationStop",{terrain:this.terrain})),this._elevationFreeze=!1,this.triggerRepaint()};this.terrainGrowing||this.terrainFlattening||this.fire("terrainAnimationStart",{terrain:this.terrain}),this.terrainGrowing=!0,this.terrainFlattening=!1,requestAnimationFrame(o)}enableTerrain(e=this.terrainExaggeration){if(e<0){console.warn("Terrain exaggeration cannot be negative.");return}let t=r=>{this.terrain&&"data"===r.type&&"source"===r.dataType&&"source"in r&&"maptiler-terrain"===r.sourceId&&"raster-dem"===r.source.type&&r.isSourceLoaded&&(this.off("data",t),this.growTerrain(e))},r=()=>{this.isTerrainEnabled=!0,this.terrainExaggeration=e,this.on("data",t),this.addSource(I.terrainSourceId,{type:"raster-dem",url:I.terrainSourceURL}),this.setTerrain({source:I.terrainSourceId,exaggeration:0})};if(this.getTerrain()){this.isTerrainEnabled=!0,this.growTerrain(e);return}this.loaded()||this.isTerrainEnabled?r():this.once("load",()=>{this.getTerrain()&&this.getSource(I.terrainSourceId)||r()})}disableTerrain(){if(!this.terrain)return;this.isTerrainEnabled=!1;let e=performance.now(),t=this.terrain.exaggeration,r=()=>{if(!this.terrain||this.terrainGrowing)return;let n=(performance.now()-e)/this.terrainAnimationDuration;(this._elevationFreeze=!1,n<.99)?(this.terrain.exaggeration=t*(1-n)**4,requestAnimationFrame(r)):(this.terrain.exaggeration=0,this.terrainGrowing=!1,this.terrainFlattening=!1,this.setTerrain(),this.getSource(I.terrainSourceId)&&this.removeSource(I.terrainSourceId),this.fire("terrainAnimationStop",{terrain:null})),this.triggerRepaint()};this.terrainGrowing||this.terrainFlattening||this.fire("terrainAnimationStart",{terrain:this.terrain}),this.terrainGrowing=!1,this.terrainFlattening=!0,requestAnimationFrame(r)}setTerrainExaggeration(e,t=!0){!t&&this.terrain?(this.terrainExaggeration=e,this.terrain.exaggeration=e,this.triggerRepaint()):this.enableTerrain(e)}onStyleReady(e){this.isStyleLoaded()?e():this.once("styledata",()=>{e()})}async fitToIpBounds(){let e=await v.DP.info();this.fitBounds(e.country_bounds,{duration:0,padding:100})}async centerOnIpPoint(e){let t=await v.DP.info();this.jumpTo({center:[t.longitude??0,t.latitude??0],zoom:e||11})}getCameraHash(){let e=new Float32Array(5),t=this.getCenter();return e[0]=t.lng,e[1]=t.lat,e[2]=this.getZoom(),e[3]=this.getPitch(),e[4]=this.getBearing(),w.DS.fromUint8Array(new Uint8Array(e.buffer))}getSdkConfig(){return j}getMaptilerSessionId(){return P}setTransformRequest(e){return super.setTransformRequest(H(e)),this}isGlobeProjection(){let e=this.getProjection();return!!e&&"globe"===e.type}enableGlobeProjection(){!0!==this.isGlobeProjection()&&(this.setProjection({type:"globe"}),this.curentProjection="globe")}enableMercatorProjection(){!1!==this.isGlobeProjection()&&(this.setProjection({type:"mercator"}),this.curentProjection="mercator")}isLanguageUpdated(){return this.languageIsUpdated}};function nI(e){if("u">typeof DOMParser){let t=new DOMParser().parseFromString(e,"application/xml");if(t.querySelector("parsererror"))throw Error("The provided string is not valid XML");return t}throw Error("No XML parser found")}function nP(e,t){if(!e.hasChildNodes())return!1;for(let r of Array.from(e.childNodes)){let e=r.nodeName;if("string"==typeof e&&e.trim().toLowerCase()===t.toLowerCase())return!0}return!1}function nR(e){if("u">typeof XMLSerializer)return new XMLSerializer().serializeToString(e);throw Error("No XML serializer found")}function nj(e){let t="string"==typeof e?nI(e):e;if(!nP(t,"gpx"))throw Error("The XML document is not valid GPX");let r=nB(t,"trk"),n=nB(t,"rte"),o=nB(t,"wpt"),a={type:"FeatureCollection",features:[]};for(let e of Array.from(r)){let t=function(e){let t;let r=nB(e,"trkseg"),n=[],o=[],a=[];for(let e=0;e<r.length;e++)if(void 0!==(t=nF(r[e],"trkpt"))&&(t.line&&n.push(t.line),t.times&&t.times.length&&o.push(t.times),a.length||t.heartRates&&t.heartRates.length)){if(!a.length)for(let t=0;t<e;t++)a.push(Array(n[t].length).fill(null));t.heartRates&&t.heartRates.length?a.push(t.heartRates):a.push(Array(t.line.length).fill(null))}if(0===n.length)return;let i={...nD(e),...nq(nV(e,"extensions"))};return 0!==o.length&&(i.coordTimes=1===n.length?o[0]:o),0!==a.length&&(i.heartRates=1===n.length?a[0]:a),1===n.length?{type:"Feature",properties:i,geometry:{type:"LineString",coordinates:n[0]}}:{type:"Feature",properties:i,geometry:{type:"MultiLineString",coordinates:n}}}(e);t&&a.features.push(t)}for(let e of Array.from(n)){let t=function(e){let t=nF(e,"rtept");return void 0===t?void 0:{type:"Feature",properties:{...nD(e),...nq(nV(e,"extensions"))},geometry:{type:"LineString",coordinates:t.line}}}(e);t&&a.features.push(t)}for(let e of Array.from(o))a.features.push({type:"Feature",properties:{...nD(e),...nZ(e,["sym"])},geometry:{type:"Point",coordinates:nX(e).coordinates}});return a}function nN(e,t){let r=e;if("string"==typeof r&&(r=nI(r)),!nP(r,"kml"))throw Error("The XML document is not valid KML");let n={type:"FeatureCollection",features:[]},o={},a={},i={},l=nB(r,"Placemark"),s=nB(r,"Style"),u=nB(r,"StyleMap");for(let e of Array.from(s)){let r=nU(void 0!==t?t(e):nR(e)).toString(16);o[`#${nG(e,"id")}`]=r,a[r]=e}for(let e of Array.from(u)){o[`#${nG(e,"id")}`]=nU(void 0!==t?t(e):nR(e)).toString(16);let r=nB(e,"Pair"),n={};for(let e of Array.from(r))n[nK(nV(e,"key"))??""]=nK(nV(e,"styleUrl"));i[`#${nG(e,"id")}`]=n}for(let e of Array.from(l))n.features=n.features.concat(function(e,t,r,n){let o=function e(t){let r,n,o,a,i;let l=["Polygon","LineString","Point","Track","gx:Track"],s=[],u=[];if(null!==nV(t,"MultiGeometry"))return e(nV(t,"MultiGeometry"));if(null!==nV(t,"MultiTrack"))return e(nV(t,"MultiTrack"));if(null!==nV(t,"gx:MultiTrack"))return e(nV(t,"gx:MultiTrack"));for(o=0;o<l.length;o++)if(n=nB(t,l[o])){for(a=0;a<n.length;a++)if(r=n[a],"Point"===l[o])s.push({type:"Point",coordinates:nJ(nK(nV(r,"coordinates"))??"")});else if("LineString"===l[o])s.push({type:"LineString",coordinates:nY(nK(nV(r,"coordinates"))??"")});else if("Polygon"===l[o]){let e=nB(r,"LinearRing"),t=[];for(i=0;i<e.length;i++)t.push(nY(nK(nV(e[i],"coordinates"))??""));s.push({type:"Polygon",coordinates:t})}else if("Track"===l[o]||"gx:Track"===l[o]){let e=function(e){let t=nB(e,"coord"),r=[],n=[];for(let n of(0===t.length&&(t=nB(e,"gx:coord")),Array.from(t)))r.push(nW((nK(n)??"").split(" ")));for(let t of Array.from(nB(e,"when")))n.push(nK(t));return{coords:r,times:n}}(r);s.push({type:"LineString",coordinates:e.coords}),e.times.length&&u.push(e.times)}}return{geoms:s,coordTimes:u}}(e),a={},i=nK(nV(e,"name")),l=nK(nV(e,"address")),s=nK(nV(e,"description")),u=nV(e,"TimeSpan"),p=nV(e,"TimeStamp"),c=nV(e,"ExtendedData"),d=nV(e,"visibility"),f,y=nK(nV(e,"styleUrl")),h=nV(e,"LineStyle"),m=nV(e,"PolyStyle");if(!o.geoms.length)return[];if(i&&(a.name=i),l&&(a.address=l),y){y.startsWith("#")||(y=`#${y}`),a.styleUrl=y,t[y]&&(a.styleHash=t[y]),n[y]&&(a.styleMapHash=n[y],a.styleHash=t[n[y].normal??""]);let e=r[a.styleHash??""];if(e){h||(h=nV(e,"LineStyle")),m||(m=nV(e,"PolyStyle"));let t=nV(e,"IconStyle");if(t){let e=nV(t,"Icon");if(e){let t=nK(nV(e,"href"));t&&(a.icon=t)}}}}if(s&&(a.description=s),u){let e=nK(nV(u,"begin")),t=nK(nV(u,"end"));e&&t&&(a.timespan={begin:e,end:t})}if(null!==p&&(a.timestamp=nK(nV(p,"when"))??new Date().toISOString()),null!==h){let e=nO(nK(nV(h,"color"))),t=e[0],r=e[1],n=Number.parseFloat(nK(nV(h,"width"))??"");t&&(a.stroke=t),Number.isNaN(r)||(a["stroke-opacity"]=r),Number.isNaN(n)||(a["stroke-width"]=n)}if(m){let e=nO(nK(nV(m,"color"))),t=e[0],r=e[1],n=nK(nV(m,"fill")),o=nK(nV(m,"outline"));t&&(a.fill=t),Number.isNaN(r)||(a["fill-opacity"]=r),n&&(a["fill-opacity"]="1"===n?a["fill-opacity"]||1:0),o&&(a["stroke-opacity"]="1"===o?a["stroke-opacity"]||1:0)}if(c){let e=nB(c,"Data"),t=nB(c,"SimpleData");for(f=0;f<e.length;f++)a[e[f].getAttribute("name")??""]=nK(nV(e[f],"value"))??"";for(f=0;f<t.length;f++)a[t[f].getAttribute("name")??""]=nK(t[f])??""}null!==d&&(a.visibility=nK(d)??""),0!==o.coordTimes.length&&(a.coordTimes=1===o.coordTimes.length?o.coordTimes[0]:o.coordTimes);let g={type:"Feature",geometry:1===o.geoms.length?o.geoms[0]:{type:"GeometryCollection",geometries:o.geoms},properties:a};return nG(e,"id")&&(g.id=nG(e,"id")??void 0),[g]}(e,o,a,i));return n}function nO(e){if(null===e)return["#000000",1];let t="",r=1,n=e;return n.startsWith("#")&&(n=n.substring(1)),(6===n.length||3===n.length)&&(t=n),8===n.length&&(r=Number.parseInt(n.substring(0,2),16)/255,t=`#${n.substring(6,8)}${n.substring(4,6)}${n.substring(2,4)}`),[t??"#000000",r??1]}function nF(e,t){let r=nB(e,t),n=[],o=[],a=[],i=r.length;if(!(i<2)){for(let e=0;e<i;e++){let t=nX(r[e]);n.push(t.coordinates),t.time&&o.push(t.time),(t.heartRate||a.length)&&(0===a.length&&(a=Array(e).fill(null)),a.push(t.heartRate))}return{line:n,times:o,heartRates:a}}}function nq(e){let t={};if(e){let r=nV(e,"line");if(r){let e=nK(nV(r,"color")),n=Number.parseFloat(nK(nV(r,"opacity"))??"0"),o=Number.parseFloat(nK(nV(r,"width"))??"0");e&&(t.stroke=e),Number.isNaN(n)||(t["stroke-opacity"]=n),Number.isNaN(o)||(t["stroke-width"]=96*o/25.4)}}return t}function nD(e){let t=nZ(e,["name","cmt","desc","type","time","keywords"]),r=nB(e,"link");if(0!==r.length)for(let e of(t.links=[],Array.from(r))){let r={href:nG(e,"href"),...nZ(e,["text","type"])};t.links.push(r)}return t}function nU(e){let t=0;if(!e||!e.length)return t;for(let r=0;r<e.length;r++)t=(t<<5)-t+e.charCodeAt(r)|0;return t}function nB(e,t){return e.getElementsByTagName(t)}function nG(e,t){return e.getAttribute(t)}function nH(e,t){return Number.parseFloat(nG(e,t)??"0")}function nV(e,t){let r=nB(e,t);return r.length?r[0]:null}function nW(e){return e.map(Number.parseFloat).map(e=>Number.isNaN(e)?null:e)}function nK(e){return e&&e.normalize&&e.normalize(),e&&e.textContent}function nZ(e,t){let r,n;let o={};for(n=0;n<t.length;n++)(r=nV(e,t[n]))&&(o[t[n]]=nK(r)??"");return o}function nJ(e){return nW(e.replace(/\s*/g,"").split(","))}function nY(e){let t=e.replace(/^\s*|\s*$/g,"").split(/\s+/),r=[];for(let e of t)r.push(nJ(e));return r}function nX(e){let t;let r=[nH(e,"lon"),nH(e,"lat")],n=nV(e,"ele"),o=nV(e,"gpxtpx:hr")||nV(e,"hr"),a=nV(e,"time");return n&&(Number.isNaN(t=Number.parseFloat(nK(n)??"0"))||r.push(t)),{coordinates:r,time:a?nK(a):null,heartRate:null!==o?Number.parseFloat(nK(o)??"0"):null}}function nQ(e){let t=e;try{"string"==typeof t&&(t=nI(t))}catch{return null}try{return nj(t)}catch{}try{return nN(t)}catch{}return null}async function n0(e,t={}){let r=t.download??!1,n=await new Promise((t,r)=>{e.redraw(),e.once("idle",()=>{e.getCanvas().toBlob(e=>{if(!e){r(Error("Screenshot could not be created."));return}t(e)},"image/png")})});if(r){let e=t.filename??"maptiler_screenshot.png",r=document.createElement("a");r.style.display="none",document.body.appendChild(r),r.href=URL.createObjectURL(n),r.download=e,r.click(),setTimeout(()=>{document.body.removeChild(r),URL.revokeObjectURL(r.href)},0)}return n}let n1=[["#1D5B79","#468B97","#EF6262","#F3AA60"],["#614BC3","#33BBC5","#85E6C5","#C8FFE0"],["#461959","#7A316F","#CD6688","#AED8CC"],["#0079FF","#00DFA2","#F6FA70","#FF0060"],["#39B5E0","#A31ACB","#FF78F0","#F5EA5A"],["#37E2D5","#590696","#C70A80","#FBCB0A"],["#FFD36E","#FFF56D","#99FFCD","#9FB4FF"],["#00EAD3","#FFF5B7","#FF449F","#005F99"],["#10A19D","#540375","#FF7000","#FFBF00"]];function n2(){return n1[~~(Math.random()*n1.length)][~~(4*Math.random())]}function n5(){return`maptiler_source_${V()}`}function n3(){return`maptiler_layer_${V()}`}function n4(e,t){if(t<=e[0].zoom)return e[0].value;if(t>=e[e.length-1].zoom)return e[e.length-1].value;for(let r=0;r<e.length-1;r+=1)if(t>=e[r].zoom&&t<e[r+1].zoom){let n=e[r+1].zoom-e[r].zoom,o=(t-e[r].zoom)/n;return o*e[r+1].value+(1-o)*e[r].value}return 0}function n8(e){return["interpolate",["linear"],["zoom"],...e.flatMap(e=>[e.zoom,e.value])]}function n6(e){return["interpolate",["linear"],["zoom"],...e.flatMap(e=>[e.zoom,e.value])]}function n9(e,t){return"number"==typeof t&&"number"==typeof e?2*t+e:"number"==typeof t&&Array.isArray(e)?["interpolate",["linear"],["zoom"],...e.flatMap(e=>[e.zoom,2*t+e.value])]:"number"==typeof e&&Array.isArray(t)?["interpolate",["linear"],["zoom"],...t.flatMap(t=>[t.zoom,2*t.value+e])]:Array.isArray(e)&&Array.isArray(t)?["interpolate",["linear"],["zoom"],...Array.from(new Set([...e.map(e=>e.zoom),...t.map(e=>e.zoom)])).sort((e,t)=>e<t?-1:1).flatMap(r=>[r,2*n4(t,r)+n4(e,r)])]:0}function n7(e,t){return["interpolate",["linear"],["get",t],...e.flatMap(e=>[e.propertyValue,e.value])]}function oe(e){let t=e.trimStart(),r=Array.from(`${t}${" ".repeat(e.length-t.length)}`);if(!r.every(e=>" "===e||"_"===e))throw Error("A dash pattern must be composed only of whitespace and underscore characters.");if(!(r.some(e=>"_"===e)&&r.some(e=>" "===e)))throw Error("A dash pattern must contain at least one underscore and one whitespace character");let n=[1];for(let e=1;e<r.length;e+=1)r[e-1]===r[e]?n[n.length-1]+=1:n.push(1);return n}function ot(e,t){return["interpolate",["linear"],["get",t],...e.flatMap(e=>[e.value,e.color])]}function or(e,t,r=!0){return r?["interpolate",["linear"],["zoom"],0,["interpolate",["linear"],["get",t],...e.flatMap(e=>[e.value,.025*e.pointRadius])],2,["interpolate",["linear"],["get",t],...e.flatMap(e=>[e.value,.05*e.pointRadius])],4,["interpolate",["linear"],["get",t],...e.flatMap(e=>[e.value,.1*e.pointRadius])],8,["interpolate",["linear"],["get",t],...e.flatMap(e=>[e.value,.25*e.pointRadius])],16,["interpolate",["linear"],["get",t],...e.flatMap(e=>[e.value,e.pointRadius])]]:["interpolate",["linear"],["get",t],...e.flatMap(e=>[e.value,e.pointRadius])]}function on(e,t,r=!0){return r?["interpolate",["linear"],["zoom"],0,["interpolate",["linear"],["get",t],...e.flatMap(e=>[e.propertyValue,.025*e.value])],2,["interpolate",["linear"],["get",t],...e.flatMap(e=>[e.propertyValue,.05*e.value])],4,["interpolate",["linear"],["get",t],...e.flatMap(e=>[e.propertyValue,.1*e.value])],8,["interpolate",["linear"],["get",t],...e.flatMap(e=>[e.propertyValue,.25*e.value])],16,["interpolate",["linear"],["get",t],...e.flatMap(e=>[e.propertyValue,e.value])]]:["interpolate",["linear"],["get",t],...e.flatMap(e=>[e.propertyValue,e.value])]}function oo(e,t){return e.every(t=>t.color[3]===e[0].color[3])?e[0].color[3]?e[0].color[3]/255:1:["interpolate",["linear"],["get",t],...e.getRawColorStops().flatMap(e=>{let t=e.value,r=e.color;return[t,4===r.length?r[3]/255:1]})]}function oa(e,t=10){return["interpolate",["linear"],["heatmap-density"],...Array.from({length:t+1},(r,n)=>{let o=n/t;return[o,e.getColorHex(o)]}).flat()]}function oi(e){let t=e.toString(16);return 1===t.length?`0${t}`:t}class ol extends Array{constructor(e={}){super(),C(this,"min",0),C(this,"max",1),"min"in e&&(this.min=e.min),"max"in e&&(this.max=e.max),"stops"in e&&this.setStops(e.stops,{clone:!1})}static fromArrayDefinition(e){return new ol({stops:e.map(e=>({value:e[0],color:e[1]}))})}setStops(e,t={clone:!0}){let r=t.clone?this.clone():this;r.length=0;let n=Number.POSITIVE_INFINITY,o=Number.NEGATIVE_INFINITY;for(let t=0;t<e.length;t+=1)n=Math.min(n,e[t].value),o=Math.max(o,e[t].value),r.push({value:e[t].value,color:e[t].color.slice()});return r.sort((e,t)=>e.value<t.value?-1:1),this.min=n,this.max=o,r}scale(e,t,r={clone:!0}){let n=r.clone,o=this[0].value,a=this.at(-1).value-o,i=t-e,l=[];for(let t=0;t<this.length;t+=1){let r=(this[t].value-o)/a*i+e;n?l.push({value:r,color:this[t].color.slice()}):this[t].value=r}return n?new ol({stops:l}):this}at(e){return e<0?this[this.length+e]:this[e]}clone(){return new ol({stops:this.getRawColorStops()})}getRawColorStops(){let e=[];for(let t=0;t<this.length;t+=1)e.push({value:this[t].value,color:this[t].color});return e}reverse(e={clone:!0}){let t=e.clone?this.clone():this;for(let e=0;e<~~(t.length/2);e+=1){let r=t[e].color;t[e].color=t.at(-(e+1)).color,t.at(-(e+1)).color=r}return t}getBounds(){return{min:this.min,max:this.max}}getColor(e,t={smooth:!0}){if(e<=this[0].value)return this[0].color;if(e>=this.at(-1).value)return this.at(-1).color;for(let r=0;r<this.length-1;r+=1){if(e>this[r+1].value)continue;let n=this[r].color;if(!t.smooth)return n.slice();let o=this[r].value,a=this[r+1].value,i=this[r+1].color,l=(a-e)/(a-o);return n.map((e,t)=>Math.round(e*l+i[t]*(1-l)))}return[0,0,0]}getColorHex(e,t={smooth:!0,withAlpha:!1}){var r;return r=this.getColor(e,t),`#${oi(r[0])}${oi(r[1])}${oi(r[2])}${4===r.length?oi(r[3]):""}`}getColorRelative(e,t={smooth:!0}){let r=this.getBounds();return this.getColor(r.min+e*(r.max-r.min),t)}getCanvasStrip(e={horizontal:!0,size:512,smooth:!0}){let t=document.createElement("canvas");t.width=e.horizontal?e.size:1,t.height=e.horizontal?1:e.size;let r=t.getContext("2d");if(!r)throw Error("Canvs context is missing");let n=r.getImageData(0,0,t.width,t.height),o=n.data,a=e.size,i=this[0].value,l=(this.at(-1).value-i)/a;for(let t=0;t<a;t+=1){let r=this.getColor(i+t*l,{smooth:e.smooth});o[4*t]=r[0],o[4*t+1]=r[1],o[4*t+2]=r[2],o[4*t+3]=r.length>3?r[3]:255}return r.putImageData(n,0,0),t}resample(e,t=15){let r;let n=this.getBounds(),o=this.scale(0,1),a=1/(t-1);if("ease-in-square"===e)r=Array.from({length:t},(e,t)=>{let r=t*a,n=o.getColor(r**2);return{value:r,color:n}});else if("ease-out-square"===e)r=Array.from({length:t},(e,t)=>{let r=t*a,n=o.getColor(1-(1-r)**2);return{value:r,color:n}});else if("ease-out-sqrt"===e)r=Array.from({length:t},(e,t)=>{let r=t*a,n=o.getColor(r**.5);return{value:r,color:n}});else if("ease-in-sqrt"===e)r=Array.from({length:t},(e,t)=>{let r=t*a,n=o.getColor(1-(1-r)**.5);return{value:r,color:n}});else if("ease-out-exp"===e)r=Array.from({length:t},(e,t)=>{let r=t*a,n=o.getColor(1-2**(-10*r));return{value:r,color:n}});else if("ease-in-exp"===e)r=Array.from({length:t},(e,t)=>{let r=t*a,n=o.getColor(2**(10*r-10));return{value:r,color:n}});else throw Error("Invalid ressampling method.");return new ol({stops:r}).scale(n.min,n.max)}transparentStart(){let e=this.getRawColorStops();for(let t of(e.unshift({value:e[0].value,color:e[0].color.slice()}),e[1].value+=.001,e))3===t.color.length&&t.color.push(255);return e[0].color[3]=0,new ol({stops:e})}hasTransparentStart(){return 4===this[0].color.length&&0===this[0].color[3]}}let os={NULL:new ol({stops:[{value:0,color:[0,0,0,0]},{value:1,color:[0,0,0,0]}]}),GRAY:new ol({stops:[{value:0,color:[0,0,0]},{value:1,color:[255,255,255]}]}),JET:new ol({stops:[{value:0,color:[0,0,131]},{value:.125,color:[0,60,170]},{value:.375,color:[5,255,255]},{value:.625,color:[255,255,0]},{value:.875,color:[250,0,0]},{value:1,color:[128,0,0]}]}),HSV:new ol({stops:[{value:0,color:[255,0,0]},{value:.169,color:[253,255,2]},{value:.173,color:[247,255,2]},{value:.337,color:[0,252,4]},{value:.341,color:[0,252,10]},{value:.506,color:[1,249,255]},{value:.671,color:[2,0,253]},{value:.675,color:[8,0,253]},{value:.839,color:[255,0,251]},{value:.843,color:[255,0,245]},{value:1,color:[255,0,6]}]}),HOT:new ol({stops:[{value:0,color:[0,0,0]},{value:.3,color:[230,0,0]},{value:.6,color:[255,210,0]},{value:1,color:[255,255,255]}]}),SPRING:new ol({stops:[{value:0,color:[255,0,255]},{value:1,color:[255,255,0]}]}),SUMMER:new ol({stops:[{value:0,color:[0,128,102]},{value:1,color:[255,255,102]}]}),AUTOMN:new ol({stops:[{value:0,color:[255,0,0]},{value:1,color:[255,255,0]}]}),WINTER:new ol({stops:[{value:0,color:[0,0,255]},{value:1,color:[0,255,128]}]}),BONE:new ol({stops:[{value:0,color:[0,0,0]},{value:.376,color:[84,84,116]},{value:.753,color:[169,200,200]},{value:1,color:[255,255,255]}]}),COPPER:new ol({stops:[{value:0,color:[0,0,0]},{value:.804,color:[255,160,102]},{value:1,color:[255,199,127]}]}),GREYS:new ol({stops:[{value:0,color:[0,0,0]},{value:1,color:[255,255,255]}]}),YIGNBU:new ol({stops:[{value:0,color:[8,29,88]},{value:.125,color:[37,52,148]},{value:.25,color:[34,94,168]},{value:.375,color:[29,145,192]},{value:.5,color:[65,182,196]},{value:.625,color:[127,205,187]},{value:.75,color:[199,233,180]},{value:.875,color:[237,248,217]},{value:1,color:[255,255,217]}]}),GREENS:new ol({stops:[{value:0,color:[0,68,27]},{value:.125,color:[0,109,44]},{value:.25,color:[35,139,69]},{value:.375,color:[65,171,93]},{value:.5,color:[116,196,118]},{value:.625,color:[161,217,155]},{value:.75,color:[199,233,192]},{value:.875,color:[229,245,224]},{value:1,color:[247,252,245]}]}),YIORRD:new ol({stops:[{value:0,color:[128,0,38]},{value:.125,color:[189,0,38]},{value:.25,color:[227,26,28]},{value:.375,color:[252,78,42]},{value:.5,color:[253,141,60]},{value:.625,color:[254,178,76]},{value:.75,color:[254,217,118]},{value:.875,color:[255,237,160]},{value:1,color:[255,255,204]}]}),BLUERED:new ol({stops:[{value:0,color:[0,0,255]},{value:1,color:[255,0,0]}]}),RDBU:new ol({stops:[{value:0,color:[5,10,172]},{value:.35,color:[106,137,247]},{value:.5,color:[190,190,190]},{value:.6,color:[220,170,132]},{value:.7,color:[230,145,90]},{value:1,color:[178,10,28]}]}),PICNIC:new ol({stops:[{value:0,color:[0,0,255]},{value:.1,color:[51,153,255]},{value:.2,color:[102,204,255]},{value:.3,color:[153,204,255]},{value:.4,color:[204,204,255]},{value:.5,color:[255,255,255]},{value:.6,color:[255,204,255]},{value:.7,color:[255,153,255]},{value:.8,color:[255,102,204]},{value:.9,color:[255,102,102]},{value:1,color:[255,0,0]}]}),RAINBOW:new ol({stops:[{value:0,color:[150,0,90]},{value:.125,color:[0,0,200]},{value:.25,color:[0,25,255]},{value:.375,color:[0,152,255]},{value:.5,color:[44,255,150]},{value:.625,color:[151,255,0]},{value:.75,color:[255,234,0]},{value:.875,color:[255,111,0]},{value:1,color:[255,0,0]}]}),PORTLAND:new ol({stops:[{value:0,color:[12,51,131]},{value:.25,color:[10,136,186]},{value:.5,color:[242,211,56]},{value:.75,color:[242,143,56]},{value:1,color:[217,30,30]}]}),BLACKBODY:new ol({stops:[{value:0,color:[0,0,0]},{value:.2,color:[230,0,0]},{value:.4,color:[230,210,0]},{value:.7,color:[255,255,255]},{value:1,color:[160,200,255]}]}),EARTH:new ol({stops:[{value:0,color:[0,0,130]},{value:.1,color:[0,180,180]},{value:.2,color:[40,210,40]},{value:.4,color:[230,230,50]},{value:.6,color:[120,70,20]},{value:1,color:[255,255,255]}]}),ELECTRIC:new ol({stops:[{value:0,color:[0,0,0]},{value:.15,color:[30,0,100]},{value:.4,color:[120,0,100]},{value:.6,color:[160,90,0]},{value:.8,color:[230,200,0]},{value:1,color:[255,250,220]}]}),VIRIDIS:new ol({stops:[{value:0,color:[68,1,84]},{value:.13,color:[71,44,122]},{value:.25,color:[59,81,139]},{value:.38,color:[44,113,142]},{value:.5,color:[33,144,141]},{value:.63,color:[39,173,129]},{value:.75,color:[92,200,99]},{value:.88,color:[170,220,50]},{value:1,color:[253,231,37]}]}),INFERNO:new ol({stops:[{value:0,color:[0,0,4]},{value:.13,color:[31,12,72]},{value:.25,color:[85,15,109]},{value:.38,color:[136,34,106]},{value:.5,color:[186,54,85]},{value:.63,color:[227,89,51]},{value:.75,color:[249,140,10]},{value:.88,color:[249,201,50]},{value:1,color:[252,255,164]}]}),MAGMA:new ol({stops:[{value:0,color:[0,0,4]},{value:.13,color:[28,16,68]},{value:.25,color:[79,18,123]},{value:.38,color:[129,37,129]},{value:.5,color:[181,54,122]},{value:.63,color:[229,80,100]},{value:.75,color:[251,135,97]},{value:.88,color:[254,194,135]},{value:1,color:[252,253,191]}]}),PLASMA:new ol({stops:[{value:0,color:[13,8,135]},{value:.13,color:[75,3,161]},{value:.25,color:[125,3,168]},{value:.38,color:[168,34,150]},{value:.5,color:[203,70,121]},{value:.63,color:[229,107,93]},{value:.75,color:[248,148,65]},{value:.88,color:[253,195,40]},{value:1,color:[240,249,33]}]}),WARM:new ol({stops:[{value:0,color:[125,0,179]},{value:.13,color:[172,0,187]},{value:.25,color:[219,0,170]},{value:.38,color:[255,0,130]},{value:.5,color:[255,63,74]},{value:.63,color:[255,123,0]},{value:.75,color:[234,176,0]},{value:.88,color:[190,228,0]},{value:1,color:[147,255,0]}]}),COOL:new ol({stops:[{value:0,color:[125,0,179]},{value:.13,color:[116,0,218]},{value:.25,color:[98,74,237]},{value:.38,color:[68,146,231]},{value:.5,color:[0,204,197]},{value:.63,color:[0,247,146]},{value:.75,color:[0,255,88]},{value:.88,color:[40,255,8]},{value:1,color:[147,255,0]}]}),RAINBOW_SOFT:new ol({stops:[{value:0,color:[125,0,179]},{value:.1,color:[199,0,180]},{value:.2,color:[255,0,121]},{value:.3,color:[255,108,0]},{value:.4,color:[222,194,0]},{value:.5,color:[150,255,0]},{value:.6,color:[0,255,55]},{value:.7,color:[0,246,150]},{value:.8,color:[50,167,222]},{value:.9,color:[103,51,235]},{value:1,color:[124,0,186]}]}),BATHYMETRY:new ol({stops:[{value:0,color:[40,26,44]},{value:.13,color:[59,49,90]},{value:.25,color:[64,76,139]},{value:.38,color:[63,110,151]},{value:.5,color:[72,142,158]},{value:.63,color:[85,174,163]},{value:.75,color:[120,206,163]},{value:.88,color:[187,230,172]},{value:1,color:[253,254,204]}]}),CDOM:new ol({stops:[{value:0,color:[47,15,62]},{value:.13,color:[87,23,86]},{value:.25,color:[130,28,99]},{value:.38,color:[171,41,96]},{value:.5,color:[206,67,86]},{value:.63,color:[230,106,84]},{value:.75,color:[242,149,103]},{value:.88,color:[249,193,135]},{value:1,color:[254,237,176]}]}),CHLOROPHYLL:new ol({stops:[{value:0,color:[18,36,20]},{value:.13,color:[25,63,41]},{value:.25,color:[24,91,59]},{value:.38,color:[13,119,72]},{value:.5,color:[18,148,80]},{value:.63,color:[80,173,89]},{value:.75,color:[132,196,122]},{value:.88,color:[175,221,162]},{value:1,color:[215,249,208]}]}),DENSITY:new ol({stops:[{value:0,color:[54,14,36]},{value:.13,color:[89,23,80]},{value:.25,color:[110,45,132]},{value:.38,color:[120,77,178]},{value:.5,color:[120,113,213]},{value:.63,color:[115,151,228]},{value:.75,color:[134,185,227]},{value:.88,color:[177,214,227]},{value:1,color:[230,241,241]}]}),FREESURFACE_BLUE:new ol({stops:[{value:0,color:[30,4,110]},{value:.13,color:[47,14,176]},{value:.25,color:[41,45,236]},{value:.38,color:[25,99,212]},{value:.5,color:[68,131,200]},{value:.63,color:[114,156,197]},{value:.75,color:[157,181,203]},{value:.88,color:[200,208,216]},{value:1,color:[241,237,236]}]}),FREESURFACE_RED:new ol({stops:[{value:0,color:[60,9,18]},{value:.13,color:[100,17,27]},{value:.25,color:[142,20,29]},{value:.38,color:[177,43,27]},{value:.5,color:[192,87,63]},{value:.63,color:[205,125,105]},{value:.75,color:[216,162,148]},{value:.88,color:[227,199,193]},{value:1,color:[241,237,236]}]}),OXYGEN:new ol({stops:[{value:0,color:[64,5,5]},{value:.13,color:[106,6,15]},{value:.25,color:[144,26,7]},{value:.38,color:[168,64,3]},{value:.5,color:[188,100,4]},{value:.63,color:[206,136,11]},{value:.75,color:[220,174,25]},{value:.88,color:[231,215,44]},{value:1,color:[248,254,105]}]}),PAR:new ol({stops:[{value:0,color:[51,20,24]},{value:.13,color:[90,32,35]},{value:.25,color:[129,44,34]},{value:.38,color:[159,68,25]},{value:.5,color:[182,99,19]},{value:.63,color:[199,134,22]},{value:.75,color:[212,171,35]},{value:.88,color:[221,210,54]},{value:1,color:[225,253,75]}]}),PHASE:new ol({stops:[{value:0,color:[145,105,18]},{value:.13,color:[184,71,38]},{value:.25,color:[186,58,115]},{value:.38,color:[160,71,185]},{value:.5,color:[110,97,218]},{value:.63,color:[50,123,164]},{value:.75,color:[31,131,110]},{value:.88,color:[77,129,34]},{value:1,color:[145,105,18]}]}),SALINITY:new ol({stops:[{value:0,color:[42,24,108]},{value:.13,color:[33,50,162]},{value:.25,color:[15,90,145]},{value:.38,color:[40,118,137]},{value:.5,color:[59,146,135]},{value:.63,color:[79,175,126]},{value:.75,color:[120,203,104]},{value:.88,color:[193,221,100]},{value:1,color:[253,239,154]}]}),TEMPERATURE:new ol({stops:[{value:0,color:[4,35,51]},{value:.13,color:[23,51,122]},{value:.25,color:[85,59,157]},{value:.38,color:[129,79,143]},{value:.5,color:[175,95,130]},{value:.63,color:[222,112,101]},{value:.75,color:[249,146,66]},{value:.88,color:[249,196,65]},{value:1,color:[232,250,91]}]}),TURBIDITY:new ol({stops:[{value:0,color:[34,31,27]},{value:.13,color:[65,50,41]},{value:.25,color:[98,69,52]},{value:.38,color:[131,89,57]},{value:.5,color:[161,112,59]},{value:.63,color:[185,140,66]},{value:.75,color:[202,174,88]},{value:.88,color:[216,209,126]},{value:1,color:[233,246,171]}]}),VELOCITY_BLUE:new ol({stops:[{value:0,color:[17,32,64]},{value:.13,color:[35,52,116]},{value:.25,color:[29,81,156]},{value:.38,color:[31,113,162]},{value:.5,color:[50,144,169]},{value:.63,color:[87,173,176]},{value:.75,color:[149,196,189]},{value:.88,color:[203,221,211]},{value:1,color:[254,251,230]}]}),VELOCITY_GREEN:new ol({stops:[{value:0,color:[23,35,19]},{value:.13,color:[24,64,38]},{value:.25,color:[11,95,45]},{value:.38,color:[39,123,35]},{value:.5,color:[95,146,12]},{value:.63,color:[152,165,18]},{value:.75,color:[201,186,69]},{value:.88,color:[233,216,137]},{value:1,color:[255,253,205]}]}),CUBEHELIX:new ol({stops:[{value:0,color:[0,0,0]},{value:.07,color:[22,5,59]},{value:.13,color:[60,4,105]},{value:.2,color:[109,1,135]},{value:.27,color:[161,0,147]},{value:.33,color:[210,2,142]},{value:.4,color:[251,11,123]},{value:.47,color:[255,29,97]},{value:.53,color:[255,54,69]},{value:.6,color:[255,85,46]},{value:.67,color:[255,120,34]},{value:.73,color:[255,157,37]},{value:.8,color:[241,191,57]},{value:.87,color:[224,220,93]},{value:.93,color:[218,241,142]},{value:1,color:[227,253,198]}]}),CIVIDIS:new ol({stops:[{value:0,color:[0,32,77,255]},{value:.125,color:[5,54,110,255]},{value:.25,color:[65,77,108,255]},{value:.375,color:[97,100,111,255]},{value:.5,color:[125,124,121,255]},{value:.625,color:[156,149,120,255]},{value:.75,color:[190,175,111,255]},{value:.875,color:[225,204,94,255]},{value:1,color:[255,235,70,255]}]}),TURBO:new ol({stops:[{value:0,color:[48,18,59,255]},{value:.125,color:[70,107,227,255]},{value:.25,color:[40,187,236,255]},{value:.375,color:[49,242,153,255]},{value:.5,color:[162,252,60,255]},{value:.625,color:[237,208,58,255]},{value:.75,color:[251,128,34,255]},{value:.875,color:[210,49,5,255]},{value:1,color:[122,4,3,255]}]}),ROCKET:new ol({stops:[{value:0,color:[250,235,221,0]},{value:.133,color:[250,235,221,255]},{value:.266,color:[246,170,130,255]},{value:.4,color:[240,96,67,255]},{value:.533,color:[203,27,79,255]},{value:.666,color:[132,30,90,255]},{value:.8,color:[63,27,68,255]},{value:1,color:[3,5,26,255]}]}),MAKO:new ol({stops:[{value:0,color:[11,4,5,255]},{value:.125,color:[43,28,53,255]},{value:.25,color:[62,53,107,255]},{value:.375,color:[59,86,152,255]},{value:.5,color:[53,123,162,255]},{value:.625,color:[53,158,170,255]},{value:.75,color:[73,193,173,255]},{value:.875,color:[150,221,181,255]},{value:1,color:[222,245,229,255]}]})},ou=Object.freeze(Object.defineProperty({__proto__:null,addHeatmap:function(e,t){if(t.layerId&&e.getLayer(t.layerId))throw Error(`A layer already exists with the layer id: ${t.layerId}`);let r=t.sourceId??n5(),n=t.layerId??n3(),o=t.minzoom??0,a=t.maxzoom??23,i=t.zoomCompensation??!0,l=t.opacity??[{zoom:o,value:0},{zoom:o+.25,value:1},{zoom:a-.25,value:1},{zoom:a,value:0}],s=Array.isArray(t.colorRamp)?t.colorRamp:os.TURBO.transparentStart(),u=s.getBounds();(0!==u.min||1!==u.max)&&(s=s.scale(0,1)),s.hasTransparentStart()||(s=s.transparentStart());let p=t.intensity??[{zoom:0,value:.01},{zoom:4,value:.2},{zoom:16,value:1}],c=t.property??null,d=t.weight??1,f=1;c?"number"==typeof d?(f=d,"number"==typeof t.weight&&console.warn("The option `.property` is ignored when `.propertyValueWeights` is not of type `PropertyValueWeights`")):Array.isArray(d)?f=n7(d,c):console.warn("The option `.property` is ignored when `.propertyValueWeights` is not of type `PropertyValueWeights`"):"number"==typeof d?f=d:Array.isArray(d)&&console.warn("The options `.propertyValueWeights` can only be used when `.property` is provided.");let y=[{zoom:0,value:1.25},{zoom:2,value:2.5},{zoom:4,value:5},{zoom:8,value:12.5},{zoom:16,value:50}],h=t.radius??(i?y:10),m=1;if("number"==typeof h?m=h:Array.isArray(h)&&"zoom"in h[0]?m=n6(h):c&&Array.isArray(h)&&"propertyValue"in h[0]?m=on(h,c,i):!c&&Array.isArray(h)&&"propertyValue"in h[0]?(m=n6(y),console.warn("The option `.radius` can only be property-driven if the option `.property` is provided.")):m=n6(y),t.data&&!e.getSource(r)){let n=t.data;"string"==typeof n&&W(n)&&(n=`https://api.maptiler.com/data/${n}/features.json?key=${j.apiKey}`),e.addSource(r,{type:"geojson",data:n})}return e.addLayer({id:n,type:"heatmap",source:r,minzoom:o,maxzoom:a,paint:{"heatmap-weight":f,"heatmap-intensity":"number"==typeof p?p:n6(p),"heatmap-color":oa(s),"heatmap-radius":m,"heatmap-opacity":"number"==typeof l?l:n6(l)}},t.beforeId),{heatmapLayerId:n,heatmapSourceId:r}},addPoint:function(e,t){let r;if(t.layerId&&e.getLayer(t.layerId))throw Error(`A layer already exists with the layer id: ${t.layerId}`);let n=t.minPointRadius??10,o=t.maxPointRadius??50,a=t.cluster??!1,i=Array.isArray(t.pointColor)?t.pointColor:os.TURBO.scale(10,t.cluster?1e4:1e3).resample("ease-out-square"),l=i.getBounds(),s=t.sourceId??n5(),u=t.layerId??n3(),p=t.showLabel??a,c=t.alignOnViewport??!0,d=t.outline??!1,f=t.outlineOpacity??1,y=t.outlineWidth??1,h=t.outlineColor??"#FFFFFF",m=t.zoomCompensation??!0,g=t.minzoom??0,v=t.maxzoom??23;r="number"==typeof t.pointOpacity?t.pointOpacity:Array.isArray(t.pointOpacity)?n6(t.pointOpacity):t.cluster?oo(i,"point_count"):t.property?oo(i,t.property):n6([{zoom:g,value:0},{zoom:g+.25,value:1},{zoom:v-.25,value:1},{zoom:v,value:0}]);let b={pointLayerId:u,clusterLayerId:"",labelLayerId:"",pointSourceId:s};if(t.data&&!e.getSource(s)){let r=t.data;"string"==typeof r&&W(r)&&(r=`https://api.maptiler.com/data/${r}/features.json?key=${j.apiKey}`),e.addSource(s,{type:"geojson",data:r,cluster:a})}if(a){b.clusterLayerId=`${u}_cluster`;let a=Array.from({length:20},(e,t)=>{let r=l.min+t*(l.max-l.min)/19;return{value:r,pointRadius:n+(o-n)*(t/19)**.5,color:i.getColorHex(r)}});e.addLayer({id:b.clusterLayerId,type:"circle",source:s,filter:["has","point_count"],paint:{"circle-color":"string"==typeof t.pointColor?t.pointColor:ot(a,"point_count"),"circle-radius":"number"==typeof t.pointRadius?t.pointRadius:Array.isArray(t.pointRadius)?n6(t.pointRadius):or(a,"point_count",!1),"circle-pitch-alignment":c?"viewport":"map","circle-pitch-scale":"map","circle-opacity":r,...d&&{"circle-stroke-opacity":"number"==typeof f?f:n6(f),"circle-stroke-width":"number"==typeof y?y:n6(y),"circle-stroke-color":"string"==typeof h?h:n8(h)}},minzoom:g,maxzoom:v},t.beforeId),e.addLayer({id:b.pointLayerId,type:"circle",source:s,filter:["!",["has","point_count"]],paint:{"circle-pitch-alignment":c?"viewport":"map","circle-pitch-scale":"map","circle-color":"string"==typeof t.pointColor?t.pointColor:i.getColorHex(i.getBounds().min),"circle-radius":"number"==typeof t.pointRadius?t.pointRadius:Array.isArray(t.pointRadius)?n6(t.pointRadius):.75*a[0].pointRadius,"circle-opacity":r,...d&&{"circle-stroke-opacity":"number"==typeof f?f:n6(f),"circle-stroke-width":"number"==typeof y?y:n6(y),"circle-stroke-color":"string"==typeof h?h:n8(h)}},minzoom:g,maxzoom:v},t.beforeId)}else{let a="string"==typeof t.pointColor?t.pointColor:Array.isArray(t.pointColor)?t.pointColor.getColorHex(t.pointColor.getBounds().min):n2(),u="number"==typeof t.pointRadius?m?n6([{zoom:0,value:.025*t.pointRadius},{zoom:2,value:.05*t.pointRadius},{zoom:4,value:.1*t.pointRadius},{zoom:8,value:.25*t.pointRadius},{zoom:16,value:1*t.pointRadius}]):t.pointRadius:Array.isArray(t.pointRadius)?n6(t.pointRadius):m?n6([{zoom:0,value:.05*n},{zoom:2,value:.1*n},{zoom:4,value:.2*n},{zoom:8,value:.5*n},{zoom:16,value:1*n}]):n;if(t.property&&Array.isArray(t.pointColor)){let e=Array.from({length:20},(e,r)=>{let a=l.min+r*(l.max-l.min)/19;return{value:a,pointRadius:"number"==typeof t.pointRadius?t.pointRadius:n+(o-n)*(r/19)**.5,color:"string"==typeof t.pointColor?t.pointColor:i.getColorHex(a)}});a=ot(e,t.property),u=or(e,t.property,m)}e.addLayer({id:b.pointLayerId,type:"circle",source:s,layout:{"circle-sort-key":t.property?["/",1,["get",t.property]]:0},paint:{"circle-pitch-alignment":c?"viewport":"map","circle-pitch-scale":"map","circle-color":a,"circle-opacity":r,"circle-radius":u,...d&&{"circle-stroke-opacity":"number"==typeof f?f:n6(f),"circle-stroke-width":"number"==typeof y?y:n6(y),"circle-stroke-color":"string"==typeof h?h:n8(h)}},minzoom:g,maxzoom:v},t.beforeId)}if(!1!==p&&(t.cluster||t.property)){b.labelLayerId=`${u}_label`;let n=t.labelColor??"#fff",o=t.labelSize??12;e.addLayer({id:b.labelLayerId,type:"symbol",source:s,filter:["has",t.cluster?"point_count":t.property],layout:{"text-field":t.cluster?"{point_count_abbreviated}":`{${t.property}}`,"text-font":["Noto Sans Regular"],"text-size":o,"text-pitch-alignment":c?"viewport":"map","symbol-sort-key":["/",1,["get",t.cluster?"point_count":t.property]]},paint:{"text-color":n,"text-opacity":r},minzoom:g,maxzoom:v},t.beforeId)}return b},addPolygon:function(e,t){if(t.layerId&&e.getLayer(t.layerId))throw Error(`A layer already exists with the layer id: ${t.layerId}`);let r=t.sourceId??n5(),n=t.layerId??n3(),o={polygonLayerId:n,polygonOutlineLayerId:t.outline?`${n}_outline`:"",polygonSourceId:r};if(t.data&&!e.getSource(r)){let n=t.data;"string"==typeof n&&W(n)&&(n=`https://api.maptiler.com/data/${n}/features.json?key=${j.apiKey}`),e.addSource(r,{type:"geojson",data:n})}let a=t.outlineDashArray??null,i=t.outlineWidth??1,l=t.outlineColor??"#FFFFFF",s=t.outlineOpacity??1,u=t.outlineBlur??0,p=t.fillColor??n2(),c=t.fillOpacity??1,d=t.outlinePosition??"center",f=t.pattern??null;"string"==typeof a&&(a=oe(a));let y=(f=null)=>{if(e.addLayer({id:n,type:"fill",source:r,minzoom:t.minzoom??0,maxzoom:t.maxzoom??23,paint:{"fill-color":"string"==typeof p?p:n8(p),"fill-opacity":"number"==typeof c?c:n6(c),...f&&{"fill-pattern":f}}},t.beforeId),!0===t.outline){let n;n="inside"===d?"number"==typeof i?.5*i:n6(i.map(({zoom:e,value:t})=>({zoom:e,value:.5*t}))):"outside"===d?"number"==typeof i?-.5*i:n6(i.map(e=>({zoom:e.zoom,value:-.5*e.value}))):0,e.addLayer({id:o.polygonOutlineLayerId,type:"line",source:r,layout:{"line-join":t.outlineJoin??"round","line-cap":t.outlineCap??"butt"},minzoom:t.minzoom??0,maxzoom:t.maxzoom??23,paint:{"line-opacity":"number"==typeof s?s:n6(s),"line-color":"string"==typeof l?l:n8(l),"line-width":"number"==typeof i?i:n6(i),"line-blur":"number"==typeof u?u:n6(u),"line-offset":n,...a&&{"line-dasharray":a}}},t.beforeId)}};return f?e.hasImage(f)?y(f):e.loadImage(f).then(t=>{e.addImage(f,t.data),y(f)}).catch(e=>{console.error("Could not load the pattern image.",e.message),y()}):y(),o},addPolyline:async function(e,t,r={}){var n,o;if(!t.sourceId&&!t.data)throw Error("Creating a polyline layer requires an existing .sourceId or a valid .data property");let a=t.data;if("string"==typeof a){if(W(a))a=`https://api.maptiler.com/data/${t.data}/features.json?key=${j.apiKey}`;else if((null==(n=a.split(".").pop())?void 0:n.toLowerCase().trim())==="gpx")a=nj(await (await fetch(a,r)).text());else if((null==(o=a.split(".").pop())?void 0:o.toLowerCase().trim())==="kml")a=nN(await (await fetch(a,r)).text());else{let e=function(e){try{return JSON.parse(e)}catch{}return null}(a)??nQ(a);e&&(a=e)}if(!a)throw Error("Polyline data was provided as string but is incompatible with valid formats.")}return function(e,t){if(t.layerId&&e.getLayer(t.layerId))throw Error(`A layer already exists with the layer id: ${t.layerId}`);let r=t.sourceId??n5(),n=t.layerId??n3(),o={polylineLayerId:n,polylineOutlineLayerId:"",polylineSourceId:r};t.data&&!e.getSource(r)&&e.addSource(r,{type:"geojson",data:t.data});let a=t.lineWidth??3,i=t.lineColor??n2(),l=t.lineOpacity??1,s=t.lineBlur??0,u=t.lineGapWidth??0,p=t.lineDashArray??null,c=t.outlineWidth??1,d=t.outlineColor??"#FFFFFF",f=t.outlineOpacity??1,y=t.outlineBlur??0;if("string"==typeof p&&(p=oe(p)),!0===t.outline){let i=`${n}_outline`;o.polylineOutlineLayerId=i,e.addLayer({id:i,type:"line",source:r,layout:{"line-join":t.lineJoin??"round","line-cap":t.lineCap??"round"},minzoom:t.minzoom??0,maxzoom:t.maxzoom??23,paint:{"line-opacity":"number"==typeof f?f:n6(f),"line-color":"string"==typeof d?d:n8(d),"line-width":n9(a,c),"line-blur":"number"==typeof y?y:n6(y)}},t.beforeId)}return e.addLayer({id:n,type:"line",source:r,layout:{"line-join":t.lineJoin??"round","line-cap":t.lineCap??"round"},minzoom:t.minzoom??0,maxzoom:t.maxzoom??23,paint:{"line-opacity":"number"==typeof l?l:n6(l),"line-color":"string"==typeof i?i:n8(i),"line-width":"number"==typeof a?a:n6(a),"line-blur":"number"==typeof s?s:n6(s),"line-gap-width":"number"==typeof u?u:n6(u),...p&&{"line-dasharray":p}}},t.beforeId),o}(e,{...t,data:a})},colorDrivenByProperty:ot,colorPalettes:n1,computeRampedOutlineWidth:n9,dashArrayMaker:oe,generateRandomLayerName:n3,generateRandomSourceName:n5,getRandomColor:n2,heatmapIntensityFromColorRamp:oa,lerpZoomNumberValues:n4,opacityDrivenByProperty:oo,paintColorOptionsToPaintSpec:n8,radiusDrivenByProperty:or,radiusDrivenByPropertyHeatmap:on,rampedOptionsToLayerPaintSpec:n6,rampedPropertyValueWeight:n7,takeScreenshot:n0},Symbol.toStringTag,{value:"Module"}));function op(){return"3.3.0"}!function(){if(typeof window>"u")return;let e=g.getRTLTextPluginStatus();if("unavailable"===e||"requested"===e)try{g.setRTLTextPlugin(I.rtlPluginURL,!0)}catch{}}();let oc=g.Map,od=g.Marker,of=g.Popup,oy=g.Style,oh=g.CanvasSource,om=g.GeoJSONSource,og=g.ImageSource,ov=g.RasterTileSource,ob=g.RasterDEMTileSource,ox=g.VectorTileSource,ow=g.VideoSource,oS=g.NavigationControl,ok=g.GeolocateControl,oL=g.AttributionControl,oC=g.LogoControl,oA=g.ScaleControl,o_=g.FullscreenControl,oz=g.TerrainControl,oM=g.BoxZoomHandler,oT=g.ScrollZoomHandler,oE=g.CooperativeGesturesHandler,o$=g.KeyboardHandler,oI=g.TwoFingersTouchPitchHandler,oP=g.MapWheelEvent,oR=g.MapTouchEvent,oj=g.MapMouseEvent,oN=g.config,oO=g.getVersion,{setRTLTextPlugin:oF,getRTLTextPluginStatus:oq,LngLat:oD,LngLatBounds:oU,MercatorCoordinate:oB,Evented:oG,AJAXError:oH,prewarm:oV,clearPrewarmedResources:oW,Hash:oK,Point:oZ,EdgeInsets:oJ,DragRotateHandler:oY,DragPanHandler:oX,TwoFingersTouchZoomRotateHandler:oQ,DoubleClickZoomHandler:o0,TwoFingersTouchZoomHandler:o1,TwoFingersTouchRotateHandler:o2,getWorkerCount:o5,setWorkerCount:o3,getMaxParallelImageRequests:o4,setMaxParallelImageRequests:o8,getWorkerUrl:o6,setWorkerUrl:o9,addSourceType:o7,importScriptInWorkers:ae,addProtocol:at,removeProtocol:ar}=g}}]);