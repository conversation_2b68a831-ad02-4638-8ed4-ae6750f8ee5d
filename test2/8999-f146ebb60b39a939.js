try{let t="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:{},e=(new t.Error).stack;e&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[e]="4c42d1d1-5eba-4a9f-9122-ca8792993775",t._sentryDebugIdIdentifier="sentry-dbid-4c42d1d1-5eba-4a9f-9122-ca8792993775")}catch(t){}"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8999],{45704:function(t,e,n){n.d(e,{fi:function(){return y},kZ:function(){return Z}});var r=n(50400),o=n(82163),i=n(62057),a=n(62556),f=n(96333),u=n(4063),c=n(67252),s=n(60611),d=n(138),p=n(40583),l=n(31492),h=n(98552),m=n(87701),v={placement:"bottom",modifiers:[],strategy:"absolute"};function g(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];return!e.some(function(t){return!(t&&"function"==typeof t.getBoundingClientRect)})}function Z(t){void 0===t&&(t={});var e=t,n=e.defaultModifiers,Z=void 0===n?[]:n,y=e.defaultOptions,b=void 0===y?v:y;return function(t,e,n){void 0===n&&(n=b);var y,w,x={placement:"bottom",orderedModifiers:[],options:Object.assign({},v,b),modifiersData:{},elements:{reference:t,popper:e},attributes:{},styles:{}},O=[],k=!1,D={state:x,setOptions:function(n){var r,o,i,f,u,c="function"==typeof n?n(x.options):n;j(),x.options=Object.assign({},b,x.options,c),x.scrollParents={reference:(0,a.kK)(t)?(0,l.Z)(t):t.contextElement?(0,l.Z)(t.contextElement):[],popper:(0,l.Z)(e)};var s=(o=Object.keys(r=[].concat(Z,x.options.modifiers).reduce(function(t,e){var n=t[e.name];return t[e.name]=n?Object.assign({},n,e,{options:Object.assign({},n.options,e.options),data:Object.assign({},n.data,e.data)}):e,t},{})).map(function(t){return r[t]}),i=new Map,f=new Set,u=[],o.forEach(function(t){i.set(t.name,t)}),o.forEach(function(t){f.has(t.name)||function t(e){f.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach(function(e){if(!f.has(e)){var n=i.get(e);n&&t(n)}}),u.push(e)}(t)}),m.xs.reduce(function(t,e){return t.concat(u.filter(function(t){return t.phase===e}))},[]));return x.orderedModifiers=s.filter(function(t){return t.enabled}),x.orderedModifiers.forEach(function(t){var e=t.name,n=t.options,r=t.effect;if("function"==typeof r){var o=r({state:x,name:e,instance:D,options:void 0===n?{}:n});O.push(o||function(){})}}),D.update()},forceUpdate:function(){if(!k){var t,e,n,l,m,v,Z,y,b,w,O,j,E=x.elements,M=E.reference,L=E.popper;if(g(M,L)){;x.rects={reference:(e=(0,h.Z)(L),n="fixed"===x.options.strategy,l=(0,a.Re)(e),y=(0,a.Re)(e)&&(m=e.getBoundingClientRect(),v=(0,d.NM)(m.width)/e.offsetWidth||1,Z=(0,d.NM)(m.height)/e.offsetHeight||1,1!==v||1!==Z),b=(0,c.Z)(e),w=(0,r.Z)(M,y,n),O={scrollLeft:0,scrollTop:0},j={x:0,y:0},(l||!l&&!n)&&(("body"!==(0,f.Z)(e)||(0,s.Z)(b))&&(O=(t=e)!==(0,i.Z)(t)&&(0,a.Re)(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:(0,o.Z)(t)),(0,a.Re)(e)?(j=(0,r.Z)(e,!0),j.x+=e.clientLeft,j.y+=e.clientTop):b&&(j.x=(0,u.Z)(b))),{x:w.left+O.scrollLeft-j.x,y:w.top+O.scrollTop-j.y,width:w.width,height:w.height}),popper:(0,p.Z)(L)},x.reset=!1,x.placement=x.options.placement,x.orderedModifiers.forEach(function(t){return x.modifiersData[t.name]=Object.assign({},t.data)});for(var R=0;R<x.orderedModifiers.length;R++){if(!0===x.reset){x.reset=!1,R=-1;continue}var A=x.orderedModifiers[R],F=A.fn,P=A.options,V=void 0===P?{}:P,B=A.name;"function"==typeof F&&(x=F({state:x,options:V,name:B,instance:D})||x)}}}},update:(y=function(){return new Promise(function(t){D.forceUpdate(),t(x)})},function(){return w||(w=new Promise(function(t){Promise.resolve().then(function(){w=void 0,t(y())})})),w}),destroy:function(){j(),k=!0}};if(!g(t,e))return D;function j(){O.forEach(function(t){return t()}),O=[]}return D.setOptions(n).then(function(t){!k&&n.onFirstUpdate&&n.onFirstUpdate(t)}),D}}var y=Z()},94985:function(t,e,n){n.d(e,{Z:function(){return o}});var r=n(62556);function o(t,e){var n=e.getRootNode&&e.getRootNode();if(t.contains(e))return!0;if(n&&(0,r.Zq)(n)){var o=e;do{if(o&&t.isSameNode(o))return!0;o=o.parentNode||o.host}while(o)}return!1}},50400:function(t,e,n){n.d(e,{Z:function(){return f}});var r=n(62556),o=n(138),i=n(62057),a=n(67977);function f(t,e,n){void 0===e&&(e=!1),void 0===n&&(n=!1);var f=t.getBoundingClientRect(),u=1,c=1;e&&(0,r.Re)(t)&&(u=t.offsetWidth>0&&(0,o.NM)(f.width)/t.offsetWidth||1,c=t.offsetHeight>0&&(0,o.NM)(f.height)/t.offsetHeight||1);var s=((0,r.kK)(t)?(0,i.Z)(t):window).visualViewport,d=!(0,a.Z)()&&n,p=(f.left+(d&&s?s.offsetLeft:0))/u,l=(f.top+(d&&s?s.offsetTop:0))/c,h=f.width/u,m=f.height/c;return{width:h,height:m,top:l,right:p+h,bottom:l+m,left:p,x:p,y:l}}},43062:function(t,e,n){n.d(e,{Z:function(){return o}});var r=n(62057);function o(t){return(0,r.Z)(t).getComputedStyle(t)}},67252:function(t,e,n){n.d(e,{Z:function(){return o}});var r=n(62556);function o(t){return(((0,r.kK)(t)?t.ownerDocument:t.document)||window.document).documentElement}},40583:function(t,e,n){n.d(e,{Z:function(){return o}});var r=n(50400);function o(t){var e=(0,r.Z)(t),n=t.offsetWidth,o=t.offsetHeight;return 1>=Math.abs(e.width-n)&&(n=e.width),1>=Math.abs(e.height-o)&&(o=e.height),{x:t.offsetLeft,y:t.offsetTop,width:n,height:o}}},96333:function(t,e,n){n.d(e,{Z:function(){return r}});function r(t){return t?(t.nodeName||"").toLowerCase():null}},98552:function(t,e,n){n.d(e,{Z:function(){return s}});var r=n(62057),o=n(96333),i=n(43062),a=n(62556),f=n(95923),u=n(85918);function c(t){return(0,a.Re)(t)&&"fixed"!==(0,i.Z)(t).position?t.offsetParent:null}function s(t){for(var e,n=(0,r.Z)(t),s=c(t);s&&(e=s,["table","td","th"].indexOf((0,o.Z)(e))>=0)&&"static"===(0,i.Z)(s).position;)s=c(s);return s&&("html"===(0,o.Z)(s)||"body"===(0,o.Z)(s)&&"static"===(0,i.Z)(s).position)?n:s||function(t){var e=/firefox/i.test((0,u.Z)());if(/Trident/i.test((0,u.Z)())&&(0,a.Re)(t)&&"fixed"===(0,i.Z)(t).position)return null;var n=(0,f.Z)(t);for((0,a.Zq)(n)&&(n=n.host);(0,a.Re)(n)&&0>["html","body"].indexOf((0,o.Z)(n));){var r=(0,i.Z)(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||e&&"filter"===r.willChange||e&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}(t)||n}},95923:function(t,e,n){n.d(e,{Z:function(){return a}});var r=n(96333),o=n(67252),i=n(62556);function a(t){return"html"===(0,r.Z)(t)?t:t.assignedSlot||t.parentNode||((0,i.Zq)(t)?t.host:null)||(0,o.Z)(t)}},62057:function(t,e,n){n.d(e,{Z:function(){return r}});function r(t){if(null==t)return window;if("[object Window]"!==t.toString()){var e=t.ownerDocument;return e&&e.defaultView||window}return t}},82163:function(t,e,n){n.d(e,{Z:function(){return o}});var r=n(62057);function o(t){var e=(0,r.Z)(t);return{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}},4063:function(t,e,n){n.d(e,{Z:function(){return a}});var r=n(50400),o=n(67252),i=n(82163);function a(t){return(0,r.Z)((0,o.Z)(t)).left+(0,i.Z)(t).scrollLeft}},62556:function(t,e,n){n.d(e,{Re:function(){return i},Zq:function(){return a},kK:function(){return o}});var r=n(62057);function o(t){var e=(0,r.Z)(t).Element;return t instanceof e||t instanceof Element}function i(t){var e=(0,r.Z)(t).HTMLElement;return t instanceof e||t instanceof HTMLElement}function a(t){if("undefined"==typeof ShadowRoot)return!1;var e=(0,r.Z)(t).ShadowRoot;return t instanceof e||t instanceof ShadowRoot}},67977:function(t,e,n){n.d(e,{Z:function(){return o}});var r=n(85918);function o(){return!/^((?!chrome|android).)*safari/i.test((0,r.Z)())}},60611:function(t,e,n){n.d(e,{Z:function(){return o}});var r=n(43062);function o(t){var e=(0,r.Z)(t),n=e.overflow,o=e.overflowX,i=e.overflowY;return/auto|scroll|overlay|hidden/.test(n+i+o)}},31492:function(t,e,n){n.d(e,{Z:function(){return function t(e,n){void 0===n&&(n=[]);var u,c=function t(e){return["html","body","#document"].indexOf((0,i.Z)(e))>=0?e.ownerDocument.body:(0,a.Re)(e)&&(0,o.Z)(e)?e:t((0,r.Z)(e))}(e),s=c===(null==(u=e.ownerDocument)?void 0:u.body),d=(0,f.Z)(c),p=s?[d].concat(d.visualViewport||[],(0,o.Z)(c)?c:[]):c,l=n.concat(p);return s?l:l.concat(t((0,r.Z)(p)))}}});var r=n(95923),o=n(60611),i=n(96333),a=n(62556),f=n(62057)},87701:function(t,e,n){n.d(e,{BL:function(){return c},Ct:function(){return v},DH:function(){return w},F2:function(){return i},I:function(){return o},MS:function(){return D},N7:function(){return g},Pj:function(){return p},XM:function(){return b},YP:function(){return h},bw:function(){return m},cW:function(){return k},d7:function(){return f},ij:function(){return Z},iv:function(){return O},k5:function(){return l},mv:function(){return u},r5:function(){return y},t$:function(){return a},ut:function(){return s},wX:function(){return x},we:function(){return r},xs:function(){return j},zV:function(){return d}});var r="top",o="bottom",i="right",a="left",f="auto",u=[r,o,i,a],c="start",s="end",d="clippingParents",p="viewport",l="popper",h="reference",m=u.reduce(function(t,e){return t.concat([e+"-"+c,e+"-"+s])},[]),v=[].concat(u,[f]).reduce(function(t,e){return t.concat([e,e+"-"+c,e+"-"+s])},[]),g="beforeRead",Z="read",y="afterRead",b="beforeMain",w="main",x="afterMain",O="beforeWrite",k="write",D="afterWrite",j=[g,Z,y,b,w,x,O,k,D]},66896:function(t,e,n){var r=n(6206),o=n(40583),i=n(94985),a=n(98552),f=n(11516),u=n(57516),c=n(63293),s=n(33706),d=n(87701);e.Z={name:"arrow",enabled:!0,phase:"main",fn:function(t){var e,n,i=t.state,p=t.name,l=t.options,h=i.elements.arrow,m=i.modifiersData.popperOffsets,v=(0,r.Z)(i.placement),g=(0,f.Z)(v),Z=[d.t$,d.F2].indexOf(v)>=0?"height":"width";if(h&&m){var y=(e="function"==typeof(e=l.padding)?e(Object.assign({},i.rects,{placement:i.placement})):e,(0,c.Z)("number"!=typeof e?e:(0,s.Z)(e,d.mv))),b=(0,o.Z)(h),w="y"===g?d.we:d.t$,x="y"===g?d.I:d.F2,O=i.rects.reference[Z]+i.rects.reference[g]-m[g]-i.rects.popper[Z],k=m[g]-i.rects.reference[g],D=(0,a.Z)(h),j=D?"y"===g?D.clientHeight||0:D.clientWidth||0:0,E=y[w],M=j-b[Z]-y[x],L=j/2-b[Z]/2+(O/2-k/2),R=(0,u.u)(E,L,M);i.modifiersData[p]=((n={})[g]=R,n.centerOffset=R-L,n)}},effect:function(t){var e=t.state,n=t.options.element,r=void 0===n?"[data-popper-arrow]":n;null!=r&&("string"!=typeof r||(r=e.elements.popper.querySelector(r)))&&(0,i.Z)(e.elements.popper,r)&&(e.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]}},36531:function(t,e,n){var r=n(87701),o=n(98552),i=n(62057),a=n(67252),f=n(43062),u=n(6206),c=n(14943),s=n(138),d={top:"auto",right:"auto",bottom:"auto",left:"auto"};function p(t){var e,n,u,c,p,l,h,m=t.popper,v=t.popperRect,g=t.placement,Z=t.variation,y=t.offsets,b=t.position,w=t.gpuAcceleration,x=t.adaptive,O=t.roundOffsets,k=t.isFixed,D=y.x,j=void 0===D?0:D,E=y.y,M=void 0===E?0:E,L="function"==typeof O?O({x:j,y:M}):{x:j,y:M};j=L.x,M=L.y;var R=y.hasOwnProperty("x"),A=y.hasOwnProperty("y"),F=r.t$,P=r.we,V=window;if(x){var B=(0,o.Z)(m),I="clientHeight",W="clientWidth";B===(0,i.Z)(m)&&(B=(0,a.Z)(m),"static"!==(0,f.Z)(B).position&&"absolute"===b&&(I="scrollHeight",W="scrollWidth")),(g===r.we||(g===r.t$||g===r.F2)&&Z===r.ut)&&(P=r.I,M-=(k&&B===V&&V.visualViewport?V.visualViewport.height:B[I])-v.height,M*=w?1:-1),(g===r.t$||(g===r.we||g===r.I)&&Z===r.ut)&&(F=r.F2,j-=(k&&B===V&&V.visualViewport?V.visualViewport.width:B[W])-v.width,j*=w?1:-1)}var H=Object.assign({position:b},x&&d),N=!0===O?(e={x:j,y:M},n=(0,i.Z)(m),u=e.x,c=e.y,p=n.devicePixelRatio||1,{x:(0,s.NM)(u*p)/p||0,y:(0,s.NM)(c*p)/p||0}):{x:j,y:M};return(j=N.x,M=N.y,w)?Object.assign({},H,((h={})[P]=A?"0":"",h[F]=R?"0":"",h.transform=1>=(V.devicePixelRatio||1)?"translate("+j+"px, "+M+"px)":"translate3d("+j+"px, "+M+"px, 0)",h)):Object.assign({},H,((l={})[P]=A?M+"px":"",l[F]=R?j+"px":"",l.transform="",l))}e.Z={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(t){var e=t.state,n=t.options,r=n.gpuAcceleration,o=n.adaptive,i=n.roundOffsets,a=void 0===i||i,f={placement:(0,u.Z)(e.placement),variation:(0,c.Z)(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:void 0===r||r,isFixed:"fixed"===e.options.strategy};null!=e.modifiersData.popperOffsets&&(e.styles.popper=Object.assign({},e.styles.popper,p(Object.assign({},f,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:void 0===o||o,roundOffsets:a})))),null!=e.modifiersData.arrow&&(e.styles.arrow=Object.assign({},e.styles.arrow,p(Object.assign({},f,{offsets:e.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:a})))),e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-placement":e.placement})},data:{}}},82372:function(t,e,n){var r=n(62057),o={passive:!0};e.Z={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(t){var e=t.state,n=t.instance,i=t.options,a=i.scroll,f=void 0===a||a,u=i.resize,c=void 0===u||u,s=(0,r.Z)(e.elements.popper),d=[].concat(e.scrollParents.reference,e.scrollParents.popper);return f&&d.forEach(function(t){t.addEventListener("scroll",n.update,o)}),c&&s.addEventListener("resize",n.update,o),function(){f&&d.forEach(function(t){t.removeEventListener("scroll",n.update,o)}),c&&s.removeEventListener("resize",n.update,o)}},data:{}}},68855:function(t,e,n){n.d(e,{Z:function(){return d}});var r={left:"right",right:"left",bottom:"top",top:"bottom"};function o(t){return t.replace(/left|right|bottom|top/g,function(t){return r[t]})}var i=n(6206),a={start:"end",end:"start"};function f(t){return t.replace(/start|end/g,function(t){return a[t]})}var u=n(6486),c=n(14943),s=n(87701),d={name:"flip",enabled:!0,phase:"main",fn:function(t){var e=t.state,n=t.options,r=t.name;if(!e.modifiersData[r]._skip){for(var a=n.mainAxis,d=void 0===a||a,p=n.altAxis,l=void 0===p||p,h=n.fallbackPlacements,m=n.padding,v=n.boundary,g=n.rootBoundary,Z=n.altBoundary,y=n.flipVariations,b=void 0===y||y,w=n.allowedAutoPlacements,x=e.options.placement,O=(0,i.Z)(x)===x,k=h||(O||!b?[o(x)]:function(t){if((0,i.Z)(t)===s.d7)return[];var e=o(t);return[f(t),e,f(e)]}(x)),D=[x].concat(k).reduce(function(t,n){var r,o,a,f,d,p,l,h,Z,y,x,O;return t.concat((0,i.Z)(n)===s.d7?(o=(r={placement:n,boundary:v,rootBoundary:g,padding:m,flipVariations:b,allowedAutoPlacements:w}).placement,a=r.boundary,f=r.rootBoundary,d=r.padding,p=r.flipVariations,h=void 0===(l=r.allowedAutoPlacements)?s.Ct:l,0===(x=(y=(Z=(0,c.Z)(o))?p?s.bw:s.bw.filter(function(t){return(0,c.Z)(t)===Z}):s.mv).filter(function(t){return h.indexOf(t)>=0})).length&&(x=y),Object.keys(O=x.reduce(function(t,n){return t[n]=(0,u.Z)(e,{placement:n,boundary:a,rootBoundary:f,padding:d})[(0,i.Z)(n)],t},{})).sort(function(t,e){return O[t]-O[e]})):n)},[]),j=e.rects.reference,E=e.rects.popper,M=new Map,L=!0,R=D[0],A=0;A<D.length;A++){var F=D[A],P=(0,i.Z)(F),V=(0,c.Z)(F)===s.BL,B=[s.we,s.I].indexOf(P)>=0,I=B?"width":"height",W=(0,u.Z)(e,{placement:F,boundary:v,rootBoundary:g,altBoundary:Z,padding:m}),H=B?V?s.F2:s.t$:V?s.I:s.we;j[I]>E[I]&&(H=o(H));var N=o(H),T=[];if(d&&T.push(W[P]<=0),l&&T.push(W[H]<=0,W[N]<=0),T.every(function(t){return t})){R=F,L=!1;break}M.set(F,T)}if(L)for(var q=b?3:1,C=function(t){var e=D.find(function(e){var n=M.get(e);if(n)return n.slice(0,t).every(function(t){return t})});if(e)return R=e,"break"},$=q;$>0&&"break"!==C($);$--);e.placement!==R&&(e.modifiersData[r]._skip=!0,e.placement=R,e.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}}},19892:function(t,e,n){var r=n(87701),o=n(6486);function i(t,e,n){return void 0===n&&(n={x:0,y:0}),{top:t.top-e.height-n.y,right:t.right-e.width+n.x,bottom:t.bottom-e.height+n.y,left:t.left-e.width-n.x}}function a(t){return[r.we,r.F2,r.I,r.t$].some(function(e){return t[e]>=0})}e.Z={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(t){var e=t.state,n=t.name,r=e.rects.reference,f=e.rects.popper,u=e.modifiersData.preventOverflow,c=(0,o.Z)(e,{elementContext:"reference"}),s=(0,o.Z)(e,{altBoundary:!0}),d=i(c,r),p=i(s,f,u),l=a(d),h=a(p);e.modifiersData[n]={referenceClippingOffsets:d,popperEscapeOffsets:p,isReferenceHidden:l,hasPopperEscaped:h},e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-reference-hidden":l,"data-popper-escaped":h})}}},82122:function(t,e,n){var r=n(6206),o=n(87701);e.Z={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(t){var e=t.state,n=t.options,i=t.name,a=n.offset,f=void 0===a?[0,0]:a,u=o.Ct.reduce(function(t,n){var i,a,u,c,s,d;return t[n]=(i=e.rects,a=(0,r.Z)(n),u=[o.t$,o.we].indexOf(a)>=0?-1:1,s=(c="function"==typeof f?f(Object.assign({},i,{placement:n})):f)[0],d=c[1],s=s||0,d=(d||0)*u,[o.t$,o.F2].indexOf(a)>=0?{x:d,y:s}:{x:s,y:d}),t},{}),c=u[e.placement],s=c.x,d=c.y;null!=e.modifiersData.popperOffsets&&(e.modifiersData.popperOffsets.x+=s,e.modifiersData.popperOffsets.y+=d),e.modifiersData[i]=u}}},77421:function(t,e,n){var r=n(72581);e.Z={name:"popperOffsets",enabled:!0,phase:"read",fn:function(t){var e=t.state,n=t.name;e.modifiersData[n]=(0,r.Z)({reference:e.rects.reference,element:e.rects.popper,strategy:"absolute",placement:e.placement})},data:{}}},394:function(t,e,n){n.d(e,{Z:function(){return l}});var r=n(87701),o=n(6206),i=n(11516),a=n(57516),f=n(40583),u=n(98552),c=n(6486),s=n(14943),d=n(23607),p=n(138),l={name:"preventOverflow",enabled:!0,phase:"main",fn:function(t){var e=t.state,n=t.options,l=t.name,h=n.mainAxis,m=n.altAxis,v=n.boundary,g=n.rootBoundary,Z=n.altBoundary,y=n.padding,b=n.tether,w=void 0===b||b,x=n.tetherOffset,O=void 0===x?0:x,k=(0,c.Z)(e,{boundary:v,rootBoundary:g,padding:y,altBoundary:Z}),D=(0,o.Z)(e.placement),j=(0,s.Z)(e.placement),E=!j,M=(0,i.Z)(D),L="x"===M?"y":"x",R=e.modifiersData.popperOffsets,A=e.rects.reference,F=e.rects.popper,P="function"==typeof O?O(Object.assign({},e.rects,{placement:e.placement})):O,V="number"==typeof P?{mainAxis:P,altAxis:P}:Object.assign({mainAxis:0,altAxis:0},P),B=e.modifiersData.offset?e.modifiersData.offset[e.placement]:null,I={x:0,y:0};if(R){if(void 0===h||h){var W,H="y"===M?r.we:r.t$,N="y"===M?r.I:r.F2,T="y"===M?"height":"width",q=R[M],C=q+k[H],$=q-k[N],S=w?-F[T]/2:0,_=j===r.BL?A[T]:F[T],K=j===r.BL?-F[T]:-A[T],z=e.elements.arrow,U=w&&z?(0,f.Z)(z):{width:0,height:0},X=e.modifiersData["arrow#persistent"]?e.modifiersData["arrow#persistent"].padding:(0,d.Z)(),Y=X[H],G=X[N],J=(0,a.u)(0,A[T],U[T]),Q=E?A[T]/2-S-J-Y-V.mainAxis:_-J-Y-V.mainAxis,tt=E?-A[T]/2+S+J+G+V.mainAxis:K+J+G+V.mainAxis,te=e.elements.arrow&&(0,u.Z)(e.elements.arrow),tn=te?"y"===M?te.clientTop||0:te.clientLeft||0:0,tr=null!=(W=null==B?void 0:B[M])?W:0,to=(0,a.u)(w?(0,p.VV)(C,q+Q-tr-tn):C,q,w?(0,p.Fp)($,q+tt-tr):$);R[M]=to,I[M]=to-q}if(void 0!==m&&m){var ti,ta="x"===M?r.we:r.t$,tf="x"===M?r.I:r.F2,tu=R[L],tc="y"===L?"height":"width",ts=tu+k[ta],td=tu-k[tf],tp=-1!==[r.we,r.t$].indexOf(D),tl=null!=(ti=null==B?void 0:B[L])?ti:0,th=tp?ts:tu-A[tc]-F[tc]-tl+V.altAxis,tm=tp?tu+A[tc]+F[tc]-tl-V.altAxis:td,tv=w&&tp?(0,a.q)(th,tu,tm):(0,a.u)(w?th:ts,tu,w?tm:td);R[L]=tv,I[L]=tv-tu}e.modifiersData[l]=I}},requiresIfExists:["offset"]}},72581:function(t,e,n){n.d(e,{Z:function(){return f}});var r=n(6206),o=n(14943),i=n(11516),a=n(87701);function f(t){var e,n=t.reference,f=t.element,u=t.placement,c=u?(0,r.Z)(u):null,s=u?(0,o.Z)(u):null,d=n.x+n.width/2-f.width/2,p=n.y+n.height/2-f.height/2;switch(c){case a.we:e={x:d,y:n.y-f.height};break;case a.I:e={x:d,y:n.y+n.height};break;case a.F2:e={x:n.x+n.width,y:p};break;case a.t$:e={x:n.x-f.width,y:p};break;default:e={x:n.x,y:n.y}}var l=c?(0,i.Z)(c):null;if(null!=l){var h="y"===l?"height":"width";switch(s){case a.BL:e[l]=e[l]-(n[h]/2-f[h]/2);break;case a.ut:e[l]=e[l]+(n[h]/2-f[h]/2)}}return e}},6486:function(t,e,n){n.d(e,{Z:function(){return O}});var r=n(87701),o=n(62057),i=n(67252),a=n(4063),f=n(67977),u=n(43062),c=n(82163),s=n(138),d=n(31492),p=n(98552),l=n(62556),h=n(50400),m=n(95923),v=n(94985),g=n(96333);function Z(t){return Object.assign({},t,{left:t.x,top:t.y,right:t.x+t.width,bottom:t.y+t.height})}function y(t,e,n){var d,p,m,v,g,y,b,w,x,O;return e===r.Pj?Z(function(t,e){var n=(0,o.Z)(t),r=(0,i.Z)(t),u=n.visualViewport,c=r.clientWidth,s=r.clientHeight,d=0,p=0;if(u){c=u.width,s=u.height;var l=(0,f.Z)();(l||!l&&"fixed"===e)&&(d=u.offsetLeft,p=u.offsetTop)}return{width:c,height:s,x:d+(0,a.Z)(t),y:p}}(t,n)):(0,l.kK)(e)?((d=(0,h.Z)(e,!1,"fixed"===n)).top=d.top+e.clientTop,d.left=d.left+e.clientLeft,d.bottom=d.top+e.clientHeight,d.right=d.left+e.clientWidth,d.width=e.clientWidth,d.height=e.clientHeight,d.x=d.left,d.y=d.top,d):Z((p=(0,i.Z)(t),v=(0,i.Z)(p),g=(0,c.Z)(p),y=null==(m=p.ownerDocument)?void 0:m.body,b=(0,s.Fp)(v.scrollWidth,v.clientWidth,y?y.scrollWidth:0,y?y.clientWidth:0),w=(0,s.Fp)(v.scrollHeight,v.clientHeight,y?y.scrollHeight:0,y?y.clientHeight:0),x=-g.scrollLeft+(0,a.Z)(p),O=-g.scrollTop,"rtl"===(0,u.Z)(y||v).direction&&(x+=(0,s.Fp)(v.clientWidth,y?y.clientWidth:0)-b),{width:b,height:w,x:x,y:O}))}var b=n(72581),w=n(63293),x=n(33706);function O(t,e){void 0===e&&(e={});var n,o,a,f,c,O,k=e,D=k.placement,j=void 0===D?t.placement:D,E=k.strategy,M=void 0===E?t.strategy:E,L=k.boundary,R=void 0===L?r.zV:L,A=k.rootBoundary,F=void 0===A?r.Pj:A,P=k.elementContext,V=void 0===P?r.k5:P,B=k.altBoundary,I=k.padding,W=void 0===I?0:I,H=(0,w.Z)("number"!=typeof W?W:(0,x.Z)(W,r.mv)),N=V===r.k5?r.YP:r.k5,T=t.rects.popper,q=t.elements[void 0!==B&&B?N:V],C=(n=(0,l.kK)(q)?q:q.contextElement||(0,i.Z)(t.elements.popper),c=(f=[].concat("clippingParents"===R?(o=(0,d.Z)((0,m.Z)(n)),a=["absolute","fixed"].indexOf((0,u.Z)(n).position)>=0&&(0,l.Re)(n)?(0,p.Z)(n):n,(0,l.kK)(a)?o.filter(function(t){return(0,l.kK)(t)&&(0,v.Z)(t,a)&&"body"!==(0,g.Z)(t)}):[]):[].concat(R),[F]))[0],(O=f.reduce(function(t,e){var r=y(n,e,M);return t.top=(0,s.Fp)(r.top,t.top),t.right=(0,s.VV)(r.right,t.right),t.bottom=(0,s.VV)(r.bottom,t.bottom),t.left=(0,s.Fp)(r.left,t.left),t},y(n,c,M))).width=O.right-O.left,O.height=O.bottom-O.top,O.x=O.left,O.y=O.top,O),$=(0,h.Z)(t.elements.reference),S=(0,b.Z)({reference:$,element:T,strategy:"absolute",placement:j}),_=Z(Object.assign({},T,S)),K=V===r.k5?_:$,z={top:C.top-K.top+H.top,bottom:K.bottom-C.bottom+H.bottom,left:C.left-K.left+H.left,right:K.right-C.right+H.right},U=t.modifiersData.offset;if(V===r.k5&&U){var X=U[j];Object.keys(z).forEach(function(t){var e=[r.F2,r.I].indexOf(t)>=0?1:-1,n=[r.we,r.I].indexOf(t)>=0?"y":"x";z[t]+=X[n]*e})}return z}},33706:function(t,e,n){n.d(e,{Z:function(){return r}});function r(t,e){return e.reduce(function(e,n){return e[n]=t,e},{})}},6206:function(t,e,n){n.d(e,{Z:function(){return r}});function r(t){return t.split("-")[0]}},23607:function(t,e,n){n.d(e,{Z:function(){return r}});function r(){return{top:0,right:0,bottom:0,left:0}}},11516:function(t,e,n){n.d(e,{Z:function(){return r}});function r(t){return["top","bottom"].indexOf(t)>=0?"x":"y"}},14943:function(t,e,n){n.d(e,{Z:function(){return r}});function r(t){return t.split("-")[1]}},138:function(t,e,n){n.d(e,{Fp:function(){return r},NM:function(){return i},VV:function(){return o}});var r=Math.max,o=Math.min,i=Math.round},63293:function(t,e,n){n.d(e,{Z:function(){return o}});var r=n(23607);function o(t){return Object.assign({},(0,r.Z)(),t)}},85918:function(t,e,n){n.d(e,{Z:function(){return r}});function r(){var t=navigator.userAgentData;return null!=t&&t.brands&&Array.isArray(t.brands)?t.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}},57516:function(t,e,n){n.d(e,{q:function(){return i},u:function(){return o}});var r=n(138);function o(t,e,n){return(0,r.Fp)(t,(0,r.VV)(e,n))}function i(t,e,n){var r=o(t,e,n);return r>n?n:r}}}]);