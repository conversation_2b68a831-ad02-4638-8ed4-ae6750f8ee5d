<!DOCTYPE html>
<html lang="en">
	<head>
		<title>three.js webgpu - tone mapping</title>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">
		<link type="text/css" rel="stylesheet" href="main.css">
	</head>

	<body>
		<div id="info">
			<a href="https://threejs.org" target="_blank" rel="noopener">three.js</a> webgpu - tone mapping<br />
			Venice Mask by
			<a href="https://sketchfab.com/D.art" target="_blank" rel="noopener">DailyArt</a> is licensed under <a href="https://creativecommons.org/licenses/by-nc/4.0/" target="_blank" rel="noopener">CC Attribution-NonCommercial</a><br />
			<a href="https://hdrihaven.com/hdri/?h=venice_sunset" target="_blank" rel="noopener">Venice Sunset</a> from <a href="https://hdrihaven.com/" target="_blank" rel="noopener">HDRI Haven</a>
		</div>

		<script type="importmap">
			{
				"imports": {
					"three": "../build/three.webgpu.js",
					"three/webgpu": "../build/three.webgpu.js",
					"three/tsl": "../build/three.tsl.js",
					"three/addons/": "./jsm/"
				}
			}
		</script>

		<script type="module">

			import * as THREE from 'three';

			import { GUI } from 'three/addons/libs/lil-gui.module.min.js';
			import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
			import { GLTFLoader } from 'three/addons/loaders/GLTFLoader.js';
			import { DRACOLoader } from 'three/addons/loaders/DRACOLoader.js';
			import { RGBELoader } from 'three/addons/loaders/RGBELoader.js';

			let renderer, scene, camera, controls;
			let gui, guiExposure = null;

			const params = {
				exposure: 1.0,
				toneMapping: 'Neutral',
				blurriness: 0.3,
				intensity: 1.0,
			};

			const toneMappingOptions = {
				None: THREE.NoToneMapping,
				Linear: THREE.LinearToneMapping,
				Reinhard: THREE.ReinhardToneMapping,
				Cineon: THREE.CineonToneMapping,
				ACESFilmic: THREE.ACESFilmicToneMapping,
				AgX: THREE.AgXToneMapping,
				Neutral: THREE.NeutralToneMapping
			};

			init().catch( function ( err ) {

				console.error( err );

			} );

			async function init() {

				renderer = new THREE.WebGPURenderer( { antialias: true } );
				renderer.setPixelRatio( window.devicePixelRatio );
				renderer.setSize( window.innerWidth, window.innerHeight );
				renderer.setAnimationLoop( animate );
				document.body.appendChild( renderer.domElement );

				renderer.toneMapping = toneMappingOptions[ params.toneMapping ];
				renderer.toneMappingExposure = params.exposure;

				scene = new THREE.Scene();
				scene.backgroundBlurriness = params.blurriness;

				const light = new THREE.DirectionalLight( 0xfff3ee, 3 ); // simualte sun
				light.position.set( 1, 0.05, 0.7 );
				scene.add( light );

				// scene.add( new THREE.DirectionalLightHelper( light, 1, 0x000000 ) );

				camera = new THREE.PerspectiveCamera( 45, window.innerWidth / window.innerHeight, 0.01, 10 );
				camera.position.set( - 0.02, 0.03, 0.05 );

				controls = new OrbitControls( camera, renderer.domElement );
				controls.enablePan = false;
				controls.enableDamping = true;
				controls.minDistance = 0.03;
				controls.maxDistance = 0.2;
				controls.target.set( 0, 0.03, 0 );
				controls.update();

				const rgbeLoader = new RGBELoader()
					.setPath( 'textures/equirectangular/' );

				const dracoLoader = new DRACOLoader();
				dracoLoader.setDecoderPath( 'jsm/libs/draco/gltf/' );

				const gltfLoader = new GLTFLoader();
				gltfLoader.setDRACOLoader( dracoLoader );
				gltfLoader.setPath( 'models/gltf/' );

				const [ texture, gltf ] = await Promise.all( [
					rgbeLoader.loadAsync( 'venice_sunset_1k.hdr' ),
					gltfLoader.loadAsync( 'venice_mask.glb' ),
				] );

				// environment

				texture.mapping = THREE.EquirectangularReflectionMapping;

				scene.background = texture;
				scene.environment = texture;

				// model

				scene.add( gltf.scene );

				window.addEventListener( 'resize', onWindowResize );

				//

				gui = new GUI();
				const toneMappingFolder = gui.addFolder( 'Tone Mapping' );

				toneMappingFolder.add( params, 'toneMapping', Object.keys( toneMappingOptions ) )

					.name( 'type' )
					.onChange( function () {

						updateGUI( toneMappingFolder );

						renderer.toneMapping = toneMappingOptions[ params.toneMapping ];

					} );

				guiExposure = toneMappingFolder.add( params, 'exposure', 0, 2 )

					.onChange( function ( value ) {

						renderer.toneMappingExposure = value;

					} );

				const backgroundFolder = gui.addFolder( 'Background' );

				backgroundFolder.add( params, 'blurriness', 0, 1 )

					.onChange( function ( value ) {

						scene.backgroundBlurriness = value;

					} );

				backgroundFolder.add( params, 'intensity', 0, 1 )

					.onChange( function ( value ) {

						scene.backgroundIntensity = value;

					} );

				updateGUI( toneMappingFolder );

				gui.open();

			}

			function updateGUI() {

				if ( params.toneMapping === 'None' ) {

					guiExposure.hide();

				} else {

					guiExposure.show();

				}

			}

			function onWindowResize() {

				camera.aspect = window.innerWidth / window.innerHeight;

				camera.updateProjectionMatrix();

				renderer.setSize( window.innerWidth, window.innerHeight );

			}

			function animate() {

				controls.update();

				renderer.render( scene, camera );

			}

		</script>

	</body>
</html>
