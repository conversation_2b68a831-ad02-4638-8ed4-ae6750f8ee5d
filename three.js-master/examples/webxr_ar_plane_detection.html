<!DOCTYPE html>
<html lang="en">
	<head>
		<title>three.js ar - plane detection</title>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
		<link type="text/css" rel="stylesheet" href="main.css">
	</head>
	<body>

		<div id="info">
			<a href="https://threejs.org" target="_blank" rel="noopener">three.js</a> ar - plane detection<br/>
		</div>

		<script type="importmap">
			{
				"imports": {
					"three": "../build/three.module.js",
					"three/addons/": "./jsm/"
				}
			}
		</script>

		<script type="module">

			import * as THREE from 'three';
			import { ARButton } from 'three/addons/webxr/ARButton.js';
			import { XRPlanes } from 'three/addons/webxr/XRPlanes.js';

			//

			const renderer = new THREE.WebGLRenderer( { antialias: true, alpha: true } );
			renderer.setPixelRatio( window.devicePixelRatio );
			renderer.setSize( window.innerWidth, window.innerHeight );
			renderer.setAnimationLoop( animate );
			renderer.xr.enabled = true;
			document.body.appendChild( renderer.domElement );

			document.body.appendChild( ARButton.createButton( renderer, {
				requiredFeatures: [ 'plane-detection' ]
			} ) );

			window.addEventListener( 'resize', onWindowResize );

			//

			const scene = new THREE.Scene();

			const planes = new XRPlanes( renderer );
			scene.add( planes );

			const camera = new THREE.PerspectiveCamera( 70, window.innerWidth / window.innerHeight, 0.01, 20 );

			const light = new THREE.HemisphereLight( 0xffffff, 0xbbbbff, 3 );
			light.position.set( 0.5, 1, 0.25 );
			scene.add( light );

			//

			function onWindowResize() {

				camera.aspect = window.innerWidth / window.innerHeight;
				camera.updateProjectionMatrix();

				renderer.setSize( window.innerWidth, window.innerHeight );

			}

			function animate() {

				renderer.render( scene, camera );

			}

		</script>
	</body>
</html>
