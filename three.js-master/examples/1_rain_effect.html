<html lang="en">
	<head>
		<title>three.js - WebGPU - Compute Particles Rain (Visualized Area + Wind)</title>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0">
		<link type="text/css" rel="stylesheet" href="main.css">
	</head>
	<body>

		<div id="info">
			<a href="https://threejs.org" target="_blank" rel="noopener">Weather Studio</a> WebGPU - GPU Compute Rain and Thunder Storm
		</div>

		<script type="importmap">
			{
				"imports": {
					"three": "../build/three.webgpu.js",
					"three/webgpu": "../build/three.webgpu.js",
					"three/tsl": "../build/three.tsl.js",
					"three/addons/": "./jsm/"
				}
			}
		</script>

		<script type="module">

			import * as THREE from 'three';
			import { Fn, texture, uv, uint, instancedArray, positionWorld, billboarding, time, hash, deltaTime, vec2, vec3, instanceIndex, positionGeometry, If, uniform, fract, abs, select, mix, float, sin, cos } from 'three/tsl';

			import { OrbitControls } from 'three/addons/controls/OrbitControls.js';

			import Stats from 'three/addons/libs/stats.module.js';

			import { GUI } from 'three/addons/libs/lil-gui.module.min.js';

			import * as BufferGeometryUtils from 'three/addons/utils/BufferGeometryUtils.js';

			import { SkyMesh } from 'three/addons/objects/SkyMesh.js';

			const maxParticleCount = 50000;
			const instanceCount = maxParticleCount / 2;

			let camera, scene, renderer;
			let controls, stats;
			let computeParticles;

			let clock;

			// Sky system
			let sky, sun;

			// Lightning system
			let nextLightningTimeoutId = null;
			let activeLightning = [];

			// Glow ripple system
			let activeGlowRipples = [];

			let collisionCamera, collisionPosRT, collisionPosMaterial;

			// START: Thêm vào để trực quan hóa
			let rainAreaHelper, rainBox;
			// END: Thêm vào để trực quan hóa

			// Wind arrow
			let windArrow, windArrowGroup;

			// Rectangle drag gizmo with separate X and Z controls
			let rectangleDragGizmo;
			let isDragging = false;
			let dragMode = null; // 'x', 'z', or 'center'
			let dragStartPosition = new THREE.Vector3();
			let dragStartParams = { x: 0, z: 0 };
			const dragSensitivity = 0.3; // Giảm nhiều hơn để di chuyển chậm, dễ điều khiển

			// Radar
			let radarTexture;
			let radarPlane;
			
			// Floor
			let floor;



			// Parameters for GUI controls
			const params = {
				rainHeight: 67.0,  // Thay đổi từ 25.0 thành 67.0 (start height)
				rainAreaWidth: 300.0,  // Trả về 300.0 như ban đầu
				rainAreaDepth: 300.0,
				rainAreaX: 0.0,    // Vị trí X của rain area
				rainAreaZ: 0.0,    // Vị trí Z của rain area
				speedMultiplier: 1.0,
				rainColor: new THREE.Color( 0xfff700 ),  // Thay đổi từ 0x6699ff thành 0xfff700 (màu vàng)
				rainDropWidth: 0.35,  // Thay đổi từ 0.1 thành 0.35
				rainDropHeight: 2.6, // Thay đổi từ 2.0 thành 2.6
				rainLifetimePercent: 100, // % thời gian sống so với thời gian rơi hết
				gridSize: 10.0,
				floorColor: new THREE.Color( 0x000000 ),  // Thay đổi từ 0x4488ff thành 0x000000 (màu đen)
				gridLineColor: new THREE.Color( 0x202122 ),  // Thay đổi từ 0x2266cc thành 0x202122 (màu xám nhạt)
				floorOpacity: 1.0,  // Độ trong suốt của floor


				radarOpacity: 0.6,  // Độ trong suốt radar
				windDirection: 0.0,  // Hướng gió (0-360 độ)
				windSpeed: 0.0,      // Tốc độ gió (0-20 m/s) - mặc định = 0
				
				// Sky parameters
				turbidity: 10,
				rayleigh: 3,
				mieCoefficient: 0.005,
				mieDirectionalG: 0.7,
				elevation: 2,
				azimuth: 180,
				exposure: 0.5,

				// Lightning parameters
				lightningEnabled: false,
				lightningFrequency: 50, // ms max interval - giảm xuống để sét nhanh hơn
				lightningBaseColor: new THREE.Color(0xffffff),
				lightningGlowColor: new THREE.Color(0x87CEFA),
				lightningBranches: 3,
				lightningDisplacement: 30,
				lightningDuration: 150,
				lightningHorizontalEnabled: true, // lightning ngang trong mây

				// Glow ripple parameters
				glowRippleEnabled: true,
				glowRippleMaxDistance: 1000, // Tăng lên 1000 theo yêu cầu
				glowRippleFadeType: 'exponential', // 'linear', 'exponential', 'quadratic', 'smooth'
				glowRippleSpeed: 20, // Tăng lên 20 theo yêu cầu
				glowRippleAmplitude: 0.1, // Giảm amplitude để mượt hơn
				glowRippleIntensity: 0.3, // Tăng lên 0.3 theo yêu cầu
				glowRippleNumWaves: 3, // Giảm từ 5 xuống 3 waves
				glowRippleColor: new THREE.Color(0xffffff),

				// Cloud parameters
				cloudEnabled: false,
				cloudParticleCount: 3000,
				cloudSize: 0.5,
				cloudOpacity: 1.0, // Tăng lên để màu đậm hơn
				cloudColor: new THREE.Color(0xffffff),
				cloudTurbulence: 0.3,
				cloudMovementSpeed: 0.2,
				cloudAdditive: false, // Option để chuyển đổi blending mode
				
				// Scene control
				pauseScene: false // Option để dừng toàn bộ scene
			};

			// Store references to uniforms for updates
			let rainHeightUniform, rainAreaWidthUniform, rainAreaDepthUniform, rainAreaXUniform, rainAreaZUniform, speedMultiplierUniform, rainColorUniform, gridSizeUniform, floorColorUniform, gridLineColorUniform, rainDropWidthUniform, rainDropHeightUniform, rainLifetimePercentUniform;
			// <<< THÊM VÀO: Uniforms cho gió
			let windDirectionRadUniform, windSpeedUniform;
			let radarCenterUniform, radarSizeUniform, globalRadarTextureUniform;
			// Cloud system variables
			let cloudParticles, cloudComputeParticles;
			let cloudPositionBuffer, cloudVelocityBuffer, cloudLifetimeBuffer;
			let rainMaterial, rippleMaterial; // Store materials để update texture sau
			
			// Các biến cần thiết cho materials
			let rainIntensity, particlePos, positionBuffer, ripplePositionBuffer, rippleTime, rippleEffect;
			let rainParticles, rippleParticles; // Store particle objects để update material
			let radarMaskingApplied = false; // Flag để chỉ apply radar masking một lần
			
			// Global references để có thể access từ update functions
			let globalPositionBuffer, globalParticleStateBuffer, globalRipplePositionBuffer, globalRippleEffect, globalRippleTimeBuffer;
			
			// Debounce function - đợi user dừng 2s mới áp dụng
			let updateTimeout;
			function updateRainDropSize() {
				clearTimeout(updateTimeout);
				console.log('Đang đợi user dừng thay đổi... (2s)');
				updateTimeout = setTimeout(() => {
					if ( rainParticles ) {
						// Thay vì thay đổi geometry, dùng scale transform
						rainParticles.scale.set( params.rainDropWidth / 0.1, params.rainDropHeight / 2.0, 1 );
						console.log('✅ Đã áp dụng scale mới:', params.rainDropWidth / 0.1, 'x', params.rainDropHeight / 2.0);
					}
				}, 2000); // Delay 2 giây như yêu cầu
			}

			// Hàm update rain material với radar masking
			function updateRainMaterialWithRadar( radarTexture ) {
				console.log('updateRainMaterialWithRadar called:', {
					rainMaterial: !!rainMaterial,
					radarTexture: !!radarTexture,
					positionBuffer: !!positionBuffer,
					radarMaskingApplied: radarMaskingApplied
				});
				if ( !rainMaterial || !radarTexture || !globalPositionBuffer || radarMaskingApplied ) {
					console.log('updateRainMaterialWithRadar: Conditions not met, returning');
					return;
				}
				
				// Tạo material mới với radar masking
				const newRainMaterial = new THREE.MeshBasicNodeMaterial();
				const rainIntensity = uv().distance( vec2( .5, 0 ) ).oneMinus().mul( 3 ).exp().mul( .1 );
				const particlePos = globalPositionBuffer.toAttribute();
				
				// Tạo texture node từ radar texture
				const radarTextureNode = texture( radarTexture );
				
				// Hàm kiểm tra particle có nằm trong vùng radar có màu không
				const isInRadarMask = Fn( () => {
					// Lấy vị trí particle (x, z)
					const worldPos = vec2( particlePos.x, particlePos.z );
					
					// Chuyển đổi world position sang UV của radar - SAME LOGIC AS getRadarValue()
					const floorSize = float( 50000.0 );
					const radarSize = float( 2310.0 );
					
					// Normalize coordinates từ world space sang [0,1] - giống hệt getRadarValue()
					const normalizedX = worldPos.x.add( floorSize.div( 2 ) ).div( floorSize );
					const normalizedZ = worldPos.y.add( floorSize.div( 2 ) ).div( floorSize );
					
					// Chỉ kiểm tra trong vùng radar (căn giữa) - giống hệt getRadarValue()
					const radarStart = floorSize.sub( radarSize ).div( 2 ).div( floorSize );
					const radarEnd = floorSize.add( radarSize ).div( 2 ).div( floorSize );
					
					const inRadarBounds = normalizedX.greaterThanEqual( radarStart )
						.and( normalizedX.lessThanEqual( radarEnd ) )
						.and( normalizedZ.greaterThanEqual( radarStart ) )
						.and( normalizedZ.lessThanEqual( radarEnd ) );
					
					// Nếu không trong vùng radar, return 0
					return select( inRadarBounds, 
						Fn( () => {
							// Chuyển sang radar texture coordinates - giống hệt getRadarValue()
							const radarX = normalizedX.sub( radarStart ).div( radarEnd.sub( radarStart ) );
							const radarZ = normalizedZ.sub( radarStart ).div( radarEnd.sub( radarStart ) );
							
							// Sample texture (flip Y axis)
							const radarUV = vec2( radarX, radarZ.oneMinus() );
							const radarSample = radarTextureNode.sample( radarUV );
							const alpha = radarSample.a;
							
							// Trả về 1 nếu alpha > 0, ngược lại 0
							return alpha.greaterThan( 0.0 );
						} )(),
						0.0 // Ngoài vùng radar
					);
				} );
				
				// Opacity của mưa phụ thuộc vào việc có nằm trong radar mask không
				const radarMask = isInRadarMask();
				// Tăng opacity để dễ thấy hơn
				const finalOpacity = select( radarMask, float( 0.8 ), float( 0.0 ) );
				
				newRainMaterial.colorNode = rainIntensity.mul( rainColorUniform );
				// Billboarding
				newRainMaterial.vertexNode = billboarding( { position: particlePos } );
				newRainMaterial.opacityNode = finalOpacity;
				newRainMaterial.side = THREE.DoubleSide;
				newRainMaterial.forceSinglePass = true;
				newRainMaterial.depthWrite = false;
				newRainMaterial.depthTest = true;
				newRainMaterial.transparent = true;
				
				// Thay thế material cũ
				rainParticles.material = newRainMaterial;
				rainMaterial = newRainMaterial;
				
				console.log('Rain material updated with radar masking');
			}
			
			// Hàm update ripple material với radar masking
			function updateRippleMaterialWithRadar( radarTexture ) {
				if ( !rippleMaterial || !radarTexture || !globalRipplePositionBuffer || !globalRippleEffect || !globalRippleTimeBuffer ) return;
				
				// Tạo material mới với radar masking
				const newRippleMaterial = new THREE.MeshBasicNodeMaterial();
				
				// Tạo texture node từ radar texture
				const radarTextureNode = texture( radarTexture );
				
				const ripplePos = globalRipplePositionBuffer.toAttribute();
				const rippleInRadarMask = Fn( () => {
					const worldPos = vec2( ripplePos.x, ripplePos.z );
					
					// Same logic as getRadarValue() function
					const floorSize = float( 50000.0 );
					const radarSize = float( 2310.0 );
					
					const normalizedX = worldPos.x.add( floorSize.div( 2 ) ).div( floorSize );
					const normalizedZ = worldPos.y.add( floorSize.div( 2 ) ).div( floorSize );
					
					const radarStart = floorSize.sub( radarSize ).div( 2 ).div( floorSize );
					const radarEnd = floorSize.add( radarSize ).div( 2 ).div( floorSize );
					
					const inRadarBounds = normalizedX.greaterThanEqual( radarStart )
						.and( normalizedX.lessThanEqual( radarEnd ) )
						.and( normalizedZ.greaterThanEqual( radarStart ) )
						.and( normalizedZ.lessThanEqual( radarEnd ) );
					
					return select( inRadarBounds, 
						Fn( () => {
							const radarX = normalizedX.sub( radarStart ).div( radarEnd.sub( radarStart ) );
							const radarZ = normalizedZ.sub( radarStart ).div( radarEnd.sub( radarStart ) );
							
							const radarUV = vec2( radarX, radarZ.oneMinus() );
							const radarSample = radarTextureNode.sample( radarUV );
							return radarSample.a.greaterThan( 0.0 );
						} )(),
						0.0
					);
				} );
				
				// Tạo rippleTime từ global buffer
				const rippleTime = globalRippleTimeBuffer.element( instanceIndex ).x;
				const rippleOpacity = rippleTime.mul( .3 ).oneMinus().max( 0 ).mul( .5 );
				const radarMask = rippleInRadarMask();
				// Tăng opacity để dễ thấy hơn
				const rippleFinalOpacity = select( radarMask, rippleOpacity.mul( 3.0 ), float( 0.0 ) );
				
				newRippleMaterial.colorNode = globalRippleEffect();
				newRippleMaterial.positionNode = positionGeometry.add( globalRipplePositionBuffer.toAttribute() );
				newRippleMaterial.opacityNode = rippleFinalOpacity;
				newRippleMaterial.side = THREE.DoubleSide;
				newRippleMaterial.forceSinglePass = true;
				newRippleMaterial.depthWrite = false;
				newRippleMaterial.depthTest = true;
				newRippleMaterial.transparent = true;
				
				// Thay thế material cũ
				rippleParticles.material = newRippleMaterial;
				rippleMaterial = newRippleMaterial;
				
				// Set flag để chỉ apply một lần
				radarMaskingApplied = true;
				console.log('Ripple material updated with radar masking');
			}



			// Hàm cập nhật camera target và position theo rain area center
			let previousRainAreaX = 0;
			let previousRainAreaZ = 0;

			function updateCameraTarget() {
				if (controls) {
					// Tính offset di chuyển
					const deltaX = params.rainAreaX - previousRainAreaX;
					const deltaZ = params.rainAreaZ - previousRainAreaZ;
					
					// Di chuyển cả camera position và target cùng offset
					camera.position.x += deltaX;
					camera.position.z += deltaZ;
					
					controls.target.set(params.rainAreaX, 0, params.rainAreaZ);
					controls.update();
					
					// Cập nhật previous values
					previousRainAreaX = params.rainAreaX;
					previousRainAreaZ = params.rainAreaZ;
				}
			}



			// Sky system functions
			function initSky() {
				// Add Sky
				sky = new SkyMesh();
				sky.scale.setScalar( 450000 );
				scene.add( sky );

				sun = new THREE.Vector3();

				function updateSky() {
					sky.turbidity.value = params.turbidity;
					sky.rayleigh.value = params.rayleigh;
					sky.mieCoefficient.value = params.mieCoefficient;
					sky.mieDirectionalG.value = params.mieDirectionalG;

					const phi = THREE.MathUtils.degToRad( 90 - params.elevation );
					const theta = THREE.MathUtils.degToRad( params.azimuth );

					sun.setFromSphericalCoords( 1, phi, theta );
					sky.sunPosition.value.copy( sun );

					renderer.toneMappingExposure = params.exposure;
				}

				// Store updateSky function globally để GUI có thể access
				window.updateSky = updateSky;
				
				// Initial sky setup
				updateSky();
			}

			init();

			// START: Thêm vào để trực quan hóa
			function updateRainAreaHelper() {

				rainBox.setFromCenterAndSize(
					new THREE.Vector3( params.rainAreaX, params.rainHeight / 2, params.rainAreaZ ),
					new THREE.Vector3( params.rainAreaWidth, params.rainHeight, params.rainAreaDepth )
				);

				updateWindArrow(); // Cập nhật wind arrow khi rain area thay đổi

			}

			// Hàm cập nhật wind arrow và xoay mưa
			function updateWindArrow() {
				if ( windArrowGroup ) {
					// Đặt vị trí ở giữa rain area, độ cao = độ cao của rain area
					windArrowGroup.position.set( params.rainAreaX, params.rainHeight / 2, params.rainAreaZ );
					
					// SỬA ĐỔI: Đồng bộ với compute shader
					// Trong shader: vec2( sin(angle), cos(angle) ) -> vec3( x, 0, y )
					// 0° = +Z direction, 90° = +X direction
					const windRad = THREE.MathUtils.degToRad( params.windDirection );
					
					// Mũi tên chỉ theo hướng gió (điều chỉnh để khớp với compute shader)
					// Không cần trừ 90° nữa vì đã sửa trong compute shader
					windArrowGroup.rotation.y = windRad;

					// Cập nhật uniform cho shader
					if ( windDirectionRadUniform ) windDirectionRadUniform.value = windRad;
					if ( windSpeedUniform ) windSpeedUniform.value = params.windSpeed;
					
					// Scale theo tốc độ gió (base scale = 1 ở tốc độ 10 m/s)
					const scale = Math.max( 0.5, params.windSpeed / 10.0 );
					windArrowGroup.scale.setScalar( scale );
				}
				
				// Xoay toàn bộ rainParticles theo hướng gió
				if ( rainParticles ) {
					// Tính góc xoay dựa trên tốc độ gió - MẠNH HƠN
					const tiltAngle = ( params.windSpeed / 20.0 ) * 0.8; // Tăng từ 0.3 lên 0.8 (≈46 độ) để mạnh hơn
					
					// Xoay theo trục Z để tạo hiệu ứng nghiêng
					rainParticles.rotation.z = tiltAngle;
					
					// SỬA ĐỔI: Để rectangle nghiêng theo đúng hướng gió
					// Điều chỉnh để khớp với compute shader: 0° = +Z, 90° = +X
					// Trừ 90° để rectangle xoay khớp với hướng gió
					rainParticles.rotation.y = THREE.MathUtils.degToRad( params.windDirection ) - Math.PI / 2;
				}
				
				// Ripple KHÔNG xoay - chúng xuất hiện tại vị trí mưa chạm sàn
			}

			// Hàm tạo wind arrow
			function createWindArrow() {
				windArrowGroup = new THREE.Group();
				
				// Tạo mũi tên bằng cách kết hợp cone và cylinder
				const arrowLength = 50;
				const arrowRadius = 5;
				const coneHeight = 15;
				const coneRadius = 8;
				
				// Thân mũi tên (cylinder)
				const shaftGeometry = new THREE.CylinderGeometry( arrowRadius, arrowRadius, arrowLength - coneHeight, 8 );
				const shaftMaterial = new THREE.MeshStandardMaterial({ 
					color: 0x00ff00,  // Màu xanh lá
					transparent: true,
					opacity: 0.8
				});
				const shaft = new THREE.Mesh( shaftGeometry, shaftMaterial );
				shaft.position.z = arrowLength / 2 - coneHeight / 2; // Đặt thân mũi tên về phía sau
				shaft.rotation.x = Math.PI / 2; // Xoay để hướng theo trục Z
				windArrowGroup.add( shaft );
				
				// Đầu mũi tên (cone)
				const headGeometry = new THREE.ConeGeometry( coneRadius, coneHeight, 8 );
				const headMaterial = new THREE.MeshStandardMaterial({ 
					color: 0xff0000,  // Màu đỏ
					transparent: true,
					opacity: 0.9
				});
				const head = new THREE.Mesh( headGeometry, headMaterial );
				head.position.z = arrowLength / 2; // Đặt đầu mũi tên ở phía trước
				head.rotation.x = Math.PI / 2; // Xoay để hướng theo trục Z
				windArrowGroup.add( head );
				
				scene.add( windArrowGroup );
				updateWindArrow(); // Cập nhật vị trí và hướng ban đầu
			}
			// END: Thêm vào để trực quan hóa

			// Rectangle drag gizmo functions
			function createRectangleDragGizmo() {
				if (rectangleDragGizmo) {
					scene.remove(rectangleDragGizmo);
				}

				rectangleDragGizmo = new THREE.Group();

				const arrowLength = 50;
				const arrowRadius = 1.5;
				const coneHeight = 10;
				const coneRadius = 4;

				// X-axis arrow (RED) - horizontal movement
				const xArrowGroup = new THREE.Group();
				xArrowGroup.name = 'xArrow';

				// X arrow shaft
				const xShaftGeometry = new THREE.CylinderGeometry(arrowRadius, arrowRadius, arrowLength - coneHeight, 8);
				const xShaftMaterial = new THREE.MeshBasicMaterial({ 
					color: 0xff0000, // Pure red for X-axis
					transparent: true,
					opacity: 0.8
				});
				const xShaft = new THREE.Mesh(xShaftGeometry, xShaftMaterial);
				xShaft.position.x = (arrowLength - coneHeight) / 2;
				xShaft.rotation.z = -Math.PI / 2;
				xArrowGroup.add(xShaft);

				// X arrow head
				const xHeadGeometry = new THREE.ConeGeometry(coneRadius, coneHeight, 8);
				const xHeadMaterial = new THREE.MeshBasicMaterial({ 
					color: 0xcc0000, // Darker red
					transparent: true,
					opacity: 0.9
				});
				const xHead = new THREE.Mesh(xHeadGeometry, xHeadMaterial);
				xHead.position.x = arrowLength - coneHeight / 2;
				xHead.rotation.z = -Math.PI / 2;
				xArrowGroup.add(xHead);

				rectangleDragGizmo.add(xArrowGroup);

				// Z-axis arrow (BLUE) - depth movement  
				const zArrowGroup = new THREE.Group();
				zArrowGroup.name = 'zArrow';

				// Z arrow shaft
				const zShaftGeometry = new THREE.CylinderGeometry(arrowRadius, arrowRadius, arrowLength - coneHeight, 8);
				const zShaftMaterial = new THREE.MeshBasicMaterial({ 
					color: 0x0000ff, // Pure blue for Z-axis
					transparent: true,
					opacity: 0.8
				});
				const zShaft = new THREE.Mesh(zShaftGeometry, zShaftMaterial);
				zShaft.position.z = (arrowLength - coneHeight) / 2;
				zShaft.rotation.x = Math.PI / 2;
				zArrowGroup.add(zShaft);

				// Z arrow head
				const zHeadGeometry = new THREE.ConeGeometry(coneRadius, coneHeight, 8);
				const zHeadMaterial = new THREE.MeshBasicMaterial({ 
					color: 0x000099, // Darker blue
					transparent: true,
					opacity: 0.9
				});
				const zHead = new THREE.Mesh(zHeadGeometry, zHeadMaterial);
				zHead.position.z = arrowLength - coneHeight / 2;
				zHead.rotation.x = Math.PI / 2;
				zArrowGroup.add(zHead);

				rectangleDragGizmo.add(zArrowGroup);

				// Center sphere for free movement (XZ plane)
				const centerGeometry = new THREE.SphereGeometry(6, 12, 8);
				const centerMaterial = new THREE.MeshBasicMaterial({ 
					color: 0xffff00, // Yellow center
					transparent: true,
					opacity: 0.7
				});
				const centerSphere = new THREE.Mesh(centerGeometry, centerMaterial);
				centerSphere.name = 'center';
				rectangleDragGizmo.add(centerSphere);

				scene.add(rectangleDragGizmo);
				updateRectangleDragGizmo();
				
				console.log('Rectangle drag gizmo created with X/Z axes and center control');
			}
			
			function updateRectangleDragGizmo() {
				if (!rectangleDragGizmo) return;
				
				// Position at bottom-right corner of rectangle, slightly above ground for visibility
				const cornerX = params.rainAreaX + params.rainAreaWidth / 2;
				const cornerZ = params.rainAreaZ + params.rainAreaDepth / 2;
				rectangleDragGizmo.position.set(cornerX, 8, cornerZ);
			}

			// Rectangle drag mouse interaction with gizmo
			const gizmoRaycaster = new THREE.Raycaster();
			const gizmoMouse = new THREE.Vector2();
			let xDragPlane = new THREE.Plane(new THREE.Vector3(0, 0, 1), 0); // YZ plane for X movement
			let zDragPlane = new THREE.Plane(new THREE.Vector3(1, 0, 0), 0); // XY plane for Z movement  
			let freeDragPlane = new THREE.Plane(new THREE.Vector3(0, 1, 0), 0); // XZ plane for free movement

			function onGizmoMouseDown(event) {
				if (!rectangleDragGizmo) return;

				// Calculate mouse position in normalized device coordinates
				gizmoMouse.x = (event.clientX / window.innerWidth) * 2 - 1;
				gizmoMouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

				// Update the raycaster
				gizmoRaycaster.setFromCamera(gizmoMouse, camera);

				// Check for intersection with gizmo components
				const intersects = gizmoRaycaster.intersectObject(rectangleDragGizmo, true);

				if (intersects.length > 0) {
					const hitObject = intersects[0].object;
					const parentGroup = hitObject.parent;
					
					// Determine which component was clicked
					if (parentGroup && parentGroup.name === 'xArrow') {
						dragMode = 'x';
						document.body.style.cursor = 'ew-resize'; // horizontal resize cursor
						console.log('Started X-axis drag');
					} else if (parentGroup && parentGroup.name === 'zArrow') {
						dragMode = 'z'; 
						document.body.style.cursor = 'ns-resize'; // vertical resize cursor
						console.log('Started Z-axis drag');
					} else if (hitObject.name === 'center') {
						dragMode = 'center';
						document.body.style.cursor = 'move'; // move cursor
						console.log('Started free movement drag');
					}

					if (dragMode) {
						isDragging = true;
						dragStartPosition.copy(intersects[0].point);
						dragStartParams.x = params.rainAreaX;
						dragStartParams.z = params.rainAreaZ;
						
						// Position drag planes at current intersection point
						if (dragMode === 'x') {
							xDragPlane.setFromNormalAndCoplanarPoint(new THREE.Vector3(0, 0, 1), dragStartPosition);
						} else if (dragMode === 'z') {
							zDragPlane.setFromNormalAndCoplanarPoint(new THREE.Vector3(1, 0, 0), dragStartPosition);
						} else if (dragMode === 'center') {
							freeDragPlane.setFromNormalAndCoplanarPoint(new THREE.Vector3(0, 1, 0), dragStartPosition);
						}
						
						event.preventDefault();
					}
				}
			}

			function onGizmoMouseMove(event) {
				if (!isDragging || !dragMode) return;

				// Calculate mouse position
				gizmoMouse.x = (event.clientX / window.innerWidth) * 2 - 1;
				gizmoMouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

				// Update raycaster
				gizmoRaycaster.setFromCamera(gizmoMouse, camera);

				const intersectPoint = new THREE.Vector3();
				let validIntersection = false;

				// Use appropriate plane based on drag mode
				if (dragMode === 'x') {
					validIntersection = gizmoRaycaster.ray.intersectPlane(xDragPlane, intersectPoint);
					if (validIntersection) {
						// Only update X position, Z remains constant - with sensitivity control
						const deltaX = (intersectPoint.x - dragStartPosition.x) * dragSensitivity;
						params.rainAreaX = dragStartParams.x + deltaX;
						rainAreaXUniform.value = params.rainAreaX;
					}
				} else if (dragMode === 'z') {
					validIntersection = gizmoRaycaster.ray.intersectPlane(zDragPlane, intersectPoint);
					if (validIntersection) {
						// Only update Z position, X remains constant - with sensitivity control
						const deltaZ = (intersectPoint.z - dragStartPosition.z) * dragSensitivity;
						params.rainAreaZ = dragStartParams.z + deltaZ;
						rainAreaZUniform.value = params.rainAreaZ;
					}
				} else if (dragMode === 'center') {
					validIntersection = gizmoRaycaster.ray.intersectPlane(freeDragPlane, intersectPoint);
					if (validIntersection) {
						// Update both X and Z positions - with sensitivity control
						const deltaX = (intersectPoint.x - dragStartPosition.x) * dragSensitivity;
						const deltaZ = (intersectPoint.z - dragStartPosition.z) * dragSensitivity;
						params.rainAreaX = dragStartParams.x + deltaX;
						params.rainAreaZ = dragStartParams.z + deltaZ;
						rainAreaXUniform.value = params.rainAreaX;
						rainAreaZUniform.value = params.rainAreaZ;
					}
				}

				if (validIntersection) {
					// Update visuals
					updateRainAreaHelper();
					updateCameraTarget();
					updateRectangleDragGizmo();
					forceCloudRespawn();

					// Update GUI controllers
					if (window.gui) {
						window.gui.controllersRecursive().forEach(controller => {
							if (controller.property === 'rainAreaX' && controller.object === params) {
								controller.updateDisplay();
							}
							if (controller.property === 'rainAreaZ' && controller.object === params) {
								controller.updateDisplay();
							}
						});
					}

					console.log(`${dragMode.toUpperCase()} drag: (${params.rainAreaX.toFixed(1)}, ${params.rainAreaZ.toFixed(1)})`);
				}

				event.preventDefault();
			}

			function onGizmoMouseUp(event) {
				if (isDragging) {
					isDragging = false;
					dragMode = null;
					document.body.style.cursor = 'default';
					console.log('Stopped dragging gizmo');
				}
			}

			// Horizontal lightning trong mây - mịn hơn với smooth curves
			function createHorizontalLightningSegment(start, end, displacement, segments, depth = 0, maxDepth = 2) {
				const distance = start.distanceTo(end);
				
				if (depth >= maxDepth || distance < 3) {
					// Tạo smooth curve thay vì straight line
					createSmoothCurve(start, end, segments, 3);
					return;
				}

				// Đường chính với smooth path
				if (depth === 0) {
					// Level 0: Tạo đường chính smooth với ít displacement
					const midPoint = start.clone().add(end).multiplyScalar(0.5);
					const verySmallDisplacement = displacement * 0.05; // Giảm từ 0.1 xuống 0.05
					const perpendicular = new THREE.Vector3(
						(Math.random() - 0.5) * verySmallDisplacement,
						(Math.random() - 0.5) * verySmallDisplacement * 0.1,
						(Math.random() - 0.5) * verySmallDisplacement
					);
					midPoint.add(perpendicular);

					// Đường chính smooth: start -> midPoint -> end
					createSmoothCurve(start, midPoint, segments, 2);
					createSmoothCurve(midPoint, end, segments, 2);
					
					// Từ midpoint tạo ít nhánh hơn và smooth hơn
					const numBranches = 1 + Math.floor(Math.random() * 2); // Giảm từ 2-4 xuống 1-2
					for (let i = 0; i < numBranches; i++) {
						const branchLength = distance * (0.1 + Math.random() * 0.15); // Ngắn hơn: 10-25%
						const branchAngle = Math.random() * Math.PI * 2;
						
						const branchEnd = midPoint.clone().add(new THREE.Vector3(
							Math.cos(branchAngle) * branchLength,
							(Math.random() - 0.5) * branchLength * 0.15, // Ít Y variation hơn
							Math.sin(branchAngle) * branchLength
						));
						
						createSmoothBranch(midPoint, branchEnd, displacement * 0.2, segments);
					}
					return;
				}
				
				// Các level sau với smooth curves
				const midPoint = start.clone().add(end).multiplyScalar(0.5);
				const smoothDisplacement = displacement * 0.4; // Giảm displacement
				const perpendicular = new THREE.Vector3(
					(Math.random() - 0.5) * smoothDisplacement,
					(Math.random() - 0.5) * smoothDisplacement * 0.2,
					(Math.random() - 0.5) * smoothDisplacement
				);
				midPoint.add(perpendicular);

				createHorizontalLightningSegment(start, midPoint, displacement * 0.7, segments, depth + 1, maxDepth);
				createHorizontalLightningSegment(midPoint, end, displacement * 0.7, segments, depth + 1, maxDepth);
			}

			// Tạo nhánh smooth - không phân nhánh nữa
			function createSmoothBranch(start, end, displacement, segments) {
				const distance = start.distanceTo(end);
				
				if (distance < 2) {
					createSmoothCurve(start, end, segments, 2);
					return;
				}
				
				// Smooth branch với minimal displacement
				const midPoint = start.clone().add(end).multiplyScalar(0.5);
				const smoothDisplacement = displacement * 0.3; // Giảm displacement
				const perpendicular = new THREE.Vector3(
					(Math.random() - 0.5) * smoothDisplacement,
					(Math.random() - 0.5) * smoothDisplacement * 0.1,
					(Math.random() - 0.5) * smoothDisplacement
				);
				midPoint.add(perpendicular);
				
				// Smooth curves thay vì straight segments
				createSmoothCurve(start, midPoint, segments, 2);
				createSmoothCurve(midPoint, end, segments, 2);
			}

			// Lightning generation functions - RADAR BOUNDS AWARE
			function createLightningSegment(start, end, displacement, segments, depth = 0, maxDepth = 4) {
				const distance = start.distanceTo(end);
				
				if (depth >= maxDepth || distance < 5) {
					segments.push(start.clone(), end.clone());
					return;
				}

				// Reduced displacement to stay within bounds
				const midPoint = start.clone().add(end).multiplyScalar(0.5);
				const perpendicular = new THREE.Vector3(
					(Math.random() - 0.5) * displacement * 0.5, // Reduced by 50%
					0,
					(Math.random() - 0.5) * displacement * 0.5  // Reduced by 50%
				);
				midPoint.add(perpendicular);

				const nextDisplacement = displacement * 0.6;
				createLightningSegment(start, midPoint, nextDisplacement, segments, depth + 1, maxDepth);
				createLightningSegment(midPoint, end, nextDisplacement, segments, depth + 1, maxDepth);

				// Create branches ONLY if they stay within radar bounds
				if (depth < Math.min(params.lightningBranches, 2) && Math.random() < 0.2) { // Reduced probability
					const branchEnd = midPoint.clone();
					const branchDirection = new THREE.Vector3(
						(Math.random() - 0.5) * 1.5, // Reduced range
						-Math.random() * 0.3,       // Reduced vertical component
						(Math.random() - 0.5) * 1.5  // Reduced range
					).normalize();
					branchEnd.add(branchDirection.multiplyScalar(distance * 0.2)); // Reduced from 0.4 to 0.2
					
					// Only add branch if end point is still within valid area
					if (isInRadarMaskAndRectangle(branchEnd.x, branchEnd.z)) {
						createLightningSegment(midPoint, branchEnd, nextDisplacement, segments, depth + 1, maxDepth);
					}
				}
			}

			function isInRadarMaskAndRectangle(x, z) {
				// CHỈ kiểm tra radar mask, không kiểm tra rectangle nữa
				if (!radarTexture || !radarTexture.image) return false;

				const floorSize = 50000.0;
				const radarSize = 2310.0;
				
				const normalizedX = (x + floorSize / 2) / floorSize;
				const normalizedZ = (z + floorSize / 2) / floorSize;
				
				const radarStart = (floorSize - radarSize) / 2 / floorSize;
				const radarEnd = (floorSize + radarSize) / 2 / floorSize;
				
				// Must be within radar bounds first
				if (normalizedX < radarStart || normalizedX > radarEnd || 
					normalizedZ < radarStart || normalizedZ > radarEnd) {
					return false;
				}

				// Sample radar texture to check if there's actual radar data
				const canvas = document.createElement('canvas');
				const ctx = canvas.getContext('2d');
				canvas.width = radarTexture.image.width;
				canvas.height = radarTexture.image.height;
				ctx.drawImage(radarTexture.image, 0, 0);
				
				const radarX = (normalizedX - radarStart) / (radarEnd - radarStart);
				const radarZ = (normalizedZ - radarStart) / (radarEnd - radarStart);
				
				const pixelX = Math.floor(radarX * canvas.width);
				const pixelY = Math.floor((1.0 - radarZ) * canvas.height);
				
				const imageData = ctx.getImageData(pixelX, pixelY, 1, 1);
				const alpha = imageData.data[3];
				
				return alpha > 0; // CHỈ cần có radar data
			}

			async function getValidRainSpawnPositions() {
				const positions = [];
				
				// LUÔN SỬ DỤNG FALLBACK để tránh đọc buffer cũ sau khi di chuyển rectangle
				console.log('Using manual position generation to avoid stale buffer data after rectangle movement');
				
				// Tạo vị trí hợp lệ trong rectangle hiện tại
				console.log('Creating valid positions manually based on current rectangle position');
				
				const maxAttempts = 15; // Giảm xuống để nhanh hơn  
				let attempts = 0;
				
				while (positions.length < 5 && attempts < maxAttempts) { // Giảm xuống 5 positions
					// Tạo điểm trong rain area bounds (giống rain spawn logic)
					const spawnX = (Math.random() - 0.5) * params.rainAreaWidth + params.rainAreaX;
					const spawnZ = (Math.random() - 0.5) * params.rainAreaDepth + params.rainAreaZ;
					
					// Kiểm tra radar mask (logic giống rain 2-state)
					const floorSize = 50000.0;
					const radarSize = 2310.0;
					
					// Normalize coordinates từ world space sang [0,1]
					const normalizedX = (spawnX + floorSize / 2) / floorSize;
					const normalizedZ = (spawnZ + floorSize / 2) / floorSize;
					
					// Chỉ kiểm tra trong vùng radar (căn giữa)
					const radarStart = (floorSize - radarSize) / 2 / floorSize;
					const radarEnd = (floorSize + radarSize) / 2 / floorSize;
					
					const inRadarBounds = normalizedX >= radarStart && normalizedX <= radarEnd &&
										 normalizedZ >= radarStart && normalizedZ <= radarEnd;
					
					// Sample radar texture để kiểm tra có radar data không
					if (inRadarBounds && radarTexture && radarTexture.image) {
						const radarX = (normalizedX - radarStart) / (radarEnd - radarStart);
						const radarZ = (normalizedZ - radarStart) / (radarEnd - radarStart);
						
						const canvas = document.createElement('canvas');
						const ctx = canvas.getContext('2d');
						canvas.width = radarTexture.image.width;
						canvas.height = radarTexture.image.height;
						ctx.drawImage(radarTexture.image, 0, 0);
						
						const pixelX = Math.floor(radarX * canvas.width);
						const pixelY = Math.floor((1.0 - radarZ) * canvas.height);
						
						const imageData = ctx.getImageData(pixelX, pixelY, 1, 1);
						const alpha = imageData.data[3];
						
						// Nếu có radar data (alpha > 0) thì đây là vị trí hợp lệ
						if (alpha > 0) {
							positions.push({
								x: spawnX,
								y: params.rainHeight, // Spawn ở độ cao mưa
								z: spawnZ
							});
						}
					}
					
					attempts++;
				}
				
				return positions;
			}

			async function createLightning() {
				if (!params.lightningEnabled) return;

				// Random chọn loại lightning: vertical hoặc horizontal
				const isHorizontal = params.lightningHorizontalEnabled && Math.random() < 0.3; // 30% chance horizontal

				// Lấy vị trí rain particles ở trạng thái SPAWN (async)
				const spawnPositions = await getValidRainSpawnPositions();
				
				if (spawnPositions.length === 0) {
					// Không có rain spawn positions, fallback về logic cũ hoặc skip
					console.log('No valid rain spawn positions found, skipping lightning');
					scheduleLightning();
					return;
				}
				
				// Chọn ngẫu nhiên 1 vị trí spawn của rain
				const randomIndex = Math.floor(Math.random() * spawnPositions.length);
				const rainSpawn = spawnPositions[randomIndex];
				
				let startPoint, endPoint;

				if (isHorizontal) {
					// HORIZONTAL LIGHTNING trong mây - ngắn, nhiều nhánh, scale nhỏ
					console.log(`Horizontal lightning spawning at cloud level`);
					
					// Start và end ở cùng độ cao trong mây (75% chiều cao mưa)
					const cloudHeight = params.rainHeight * 0.75;
					startPoint = new THREE.Vector3(rainSpawn.x, cloudHeight, rainSpawn.z);
					
					// End point BÉ HƠN: chỉ 15-30m (giảm từ 30-60m)
					const horizontalDistance = 15 + Math.random() * 15;
					const angle = Math.random() * Math.PI * 2;
					endPoint = new THREE.Vector3(
						rainSpawn.x + Math.cos(angle) * horizontalDistance,
						cloudHeight + (Math.random() - 0.5) * 2, // Còn ít height variation hơn nữa
						rainSpawn.z + Math.sin(angle) * horizontalDistance
					);
				} else {
					// VERTICAL LIGHTNING (như cũ)
					console.log(`Vertical lightning spawning from rain position: (${rainSpawn.x.toFixed(1)}, ${rainSpawn.y.toFixed(1)}, ${rainSpawn.z.toFixed(1)})`);
					
					startPoint = new THREE.Vector3(rainSpawn.x, params.rainHeight, rainSpawn.z);
					endPoint = new THREE.Vector3(
						rainSpawn.x, // Không drift để đảm bảo trong radar
						-1,          // Floor level
						rainSpawn.z  // Không drift để đảm bảo trong radar
					);
				}

				if (!startPoint || !endPoint) {
					scheduleLightning();
					return;
				}

				const segments = [];
				
				if (isHorizontal) {
					// Horizontal lightning: nhiều nhánh rẽ, displacement lớn hơn
					const horizontalDisplacement = params.lightningDisplacement * 1.5; // Tăng displacement
					createHorizontalLightningSegment(startPoint, endPoint, horizontalDisplacement, segments);
				} else {
					// Vertical lightning: displacement nhỏ để giữ trong radar bounds
				const reducedDisplacement = Math.min(params.lightningDisplacement, 15); // Cap at 15
				createLightningSegment(startPoint, endPoint, reducedDisplacement, segments);
				}

				if (segments.length < 2) {
					scheduleLightning();
					return;
				}

				// Create geometry
				const geometry = new THREE.BufferGeometry();
				const positions = segments.flatMap(v => v.toArray());
				geometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));

				// Create LineBasicMaterial for WebGPU compatibility
				const mainMaterial = new THREE.LineBasicMaterial({
					color: params.lightningBaseColor,
					transparent: true,
					opacity: 0.0, // Start hidden, will be animated
					blending: THREE.AdditiveBlending,
					depthWrite: false,
					linewidth: 2 // Tăng độ dày line nếu supported
				});

				// Create glow lines (multiple layers for extreme brightness)
				const glowLines = [];
				for (let i = 0; i < 3; i++) { // Tăng từ 2 lên 3 layers
					const glowMaterial = new THREE.LineBasicMaterial({
						color: params.lightningGlowColor,
						transparent: true,
						opacity: 0.0, // Start hidden
						blending: THREE.AdditiveBlending,
						depthWrite: false,
						linewidth: 3 // Glow lines dày hơn
					});
					const glowLine = new THREE.LineSegments(geometry, glowMaterial);
					glowLines.push(glowLine);
				}

				const mainLightning = new THREE.LineSegments(geometry, mainMaterial);
				geometry.drawRange = { start: 0, count: 0 }; // Start with no vertices drawn
				
				mainLightning.userData = {
					startTime: Date.now(),
					totalPoints: positions.length / 3,
					drawCompleted: false,
					drawTimeoutId: null,
					material: mainMaterial,
					glowLines: glowLines,
					geometry: geometry,
					isHorizontal: isHorizontal // Track lightning type for ground effects
				};

				scene.add(mainLightning);
				glowLines.forEach(line => scene.add(line));
				activeLightning.push(mainLightning);

				// Create continuous glow tại điểm bắt đầu lightning
				createContinuousLightningGlow(startPoint, mainLightning);

				// Create spherical glow ripple effect
				createGlowRipple(startPoint, params.glowRippleMaxDistance);

				// Create additional secondary lightning bolts (1-2 random)
				createSecondaryLightning(startPoint, endPoint, isHorizontal);

				// Start animated drawing like in demo
				animateLightningDraw(mainLightning);

				scheduleLightning();
			}

			// Lightning drawing animation (từ demo)
			function animateLightningDraw(mainLightning) {
				// Pause lightning animation khi scene pause
				if (params.pauseScene) {
					// Track pause time để adjust startTime khi resume
					if (!mainLightning.userData.pauseStartTime) {
						mainLightning.userData.pauseStartTime = Date.now();
					}
					setTimeout(() => animateLightningDraw(mainLightning), 16); // Check lại sau 16ms
					return;
				}
				
				const now = Date.now();
				const userData = mainLightning.userData;
				
				// Adjust startTime khi resume từ pause
				if (userData.pauseStartTime) {
					const pauseDuration = Date.now() - userData.pauseStartTime;
					userData.startTime += pauseDuration;
					userData.pauseStartTime = null; // Reset pause tracking
				}
				
				const elapsed = now - userData.startTime;
				const drawDuration = 90; // Like in demo
				const progress = Math.min(1.0, elapsed / drawDuration);
				const targetCount = Math.min(userData.totalPoints, Math.ceil(userData.totalPoints * progress / 2) * 2);
				
				// Check if object still exists
				if (!mainLightning || !userData.geometry || !scene.getObjectById(mainLightning.id)) {
					if (userData.drawTimeoutId) clearTimeout(userData.drawTimeoutId);
					return;
				}
				
				// Update draw range for animated drawing
				userData.geometry.drawRange.count = targetCount;
				
				// Update opacity with flickering - MUCH BRIGHTER
				userData.material.opacity = 1.0; // Pure white core always full opacity
				userData.glowLines.forEach(line => {
					line.material.opacity = 0.85 + Math.random() * 0.15; // Tăng từ 0.5-0.65 lên 0.85-1.0
				});
				
				// Continue animation or trigger impact
				if (progress < 1.0) {
					userData.drawTimeoutId = setTimeout(() => animateLightningDraw(mainLightning), 16);
				} else if (!userData.drawCompleted) {
					userData.drawCompleted = true;
					userData.drawTimeoutId = null;
					flashImpact(mainLightning);
				}
			}

			// Flash impact và ground crackles (từ demo)
			function flashImpact(mainLightning) {
				const userData = mainLightning.userData;
				if (!mainLightning || !userData.material || !scene.getObjectById(mainLightning.id)) return;
				
				// Maximize opacity for flash - EXTREMELY BRIGHT
				userData.material.opacity = 1.0;
				userData.glowLines.forEach(line => {
					line.material.opacity = 1.0; // Full brightness for impact flash
				});
				
				// Create ground effects và impact sphere - CHỈ cho vertical lightning
				if (!userData.isHorizontal && userData.geometry && userData.geometry.attributes.position) {
					const positions = userData.geometry.attributes.position.array;
					if (positions.length >= 3) {
						// Ground crackles tại điểm chạm đất
						const endPointForGround = new THREE.Vector3(
							positions[positions.length - 3],
							-1, // Floor level
							positions[positions.length - 1]
						);
						createGroundCrackles(endPointForGround);
						
						// Impact sphere
						createImpactSphere(endPointForGround);
					}
				}
				
				// Schedule removal - với pause support
				const impactFlashDuration = 110;
				const postFlashDuration = 70;
				
				function scheduleRemoval(removalObj, delay, callback) {
					if (params.pauseScene) {
						// Track pause start time
						if (!removalObj.pauseStartTime) {
							removalObj.pauseStartTime = Date.now();
						}
						setTimeout(() => scheduleRemoval(removalObj, delay, callback), 50);
						return;
					}
					
					// Adjust startTime khi resume từ pause
					if (removalObj.pauseStartTime) {
						const pauseDuration = Date.now() - removalObj.pauseStartTime;
						removalObj.startTime += pauseDuration;
						removalObj.pauseStartTime = null;
					}
					
					// Tính thời gian thực tế đã trôi qua (KHÔNG tính thời gian pause)
					const actualElapsed = Date.now() - removalObj.startTime;
					const remaining = Math.max(0, delay - actualElapsed);
					if (remaining > 0) {
						setTimeout(callback, remaining);
					} else {
						callback();
					}
				}
				
				const removalObj = { startTime: Date.now() };
				scheduleRemoval(removalObj, impactFlashDuration, () => {
					const fadeObj = { startTime: Date.now() };
					scheduleRemoval(fadeObj, postFlashDuration, () => {
						removeLightning(mainLightning);
					});
				});
			}

			// Ground crackles (từ demo) - MUCH SMALLER (1/3 size)
			function createGroundCrackles(position) {
				const segments = [];
				const numBranches = 8; // Tăng từ 4 lên 8 nhánh
				const maxDepth = 2; // Further reduced from 3 to 2
				const displacement = 2; // Further reduced from 4 to 2
				
				// Generate segments recursively like in demo
				for (let i = 0; i < numBranches; i++) {
					const angle = (i / numBranches) * Math.PI * 2 + (Math.random() - 0.5) * (Math.PI / numBranches);
					const branchDirection = new THREE.Vector3(Math.cos(angle), 0, Math.sin(angle));
					const branchLength = 3 + Math.random() * 4; // Further reduced from 7+8 to 3+4
					const branchEndPoint = position.clone().add(branchDirection.multiplyScalar(branchLength));
					createLightningSegment(position, branchEndPoint, displacement, segments, 0, maxDepth);
				}
				
				if (segments.length === 0) return;
				
				// Create geometry and material
				const geometry = new THREE.BufferGeometry();
				const positions = segments.flatMap(v => v.toArray());
				geometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));
				const totalPoints = positions.length / 3;
				geometry.drawRange = { start: 0, count: 0 };
				
				const crackleMaterial = new THREE.LineBasicMaterial({
					color: 0xffffff, // Đổi từ sky blue sang pure white cho sáng hơn
					transparent: true,
					opacity: 1.0,
					blending: THREE.AdditiveBlending,
					depthWrite: false,
					linewidth: 2 // Tăng độ dày cho crackles
				});
				
				const crackle = new THREE.LineSegments(geometry, crackleMaterial);
				crackle.userData = {
					startTime: Date.now(),
					material: crackleMaterial,
					duration: 380, // Like in demo
					totalPoints: totalPoints,
					geometry: geometry
				};
				
				scene.add(crackle);
				activeLightning.push(crackle); // Use same array for cleanup
			}

			// ===== SPHERICAL GLOW RIPPLE SYSTEM =====
			
			// Tạo hiệu ứng glow ripple hình cầu lan tỏa từ điểm sấm chớp
			function createGlowRipple(startPosition, maxDistance) {
				if (!params.glowRippleEnabled || startPosition.y < params.rainHeight) return;
				
				console.log('🌐 Creating spherical glow ripple at:', startPosition);
				
				const rippleData = {
					startPosition: startPosition.clone(),
					startHeight: startPosition.y,
					minHeight: startPosition.y * (2/3), // Chỉ lan tới 2/3 chiều cao
					duration: 8000, // Tăng từ 2 giây lên 8 giây để chậm và lâu hơn
					startTime: Date.now(),
					spheres: [],
					isActive: true
				};
				
				// Tạo nhiều sphere layers cho hiệu ứng ripple waves
				const numLayers = params.glowRippleNumWaves;
				for (let i = 0; i < numLayers; i++) {
					const sphere = createSphericalRipple(startPosition, i, numLayers);
					if (sphere) {
						rippleData.spheres.push(sphere);
						scene.add(sphere);
					}
				}
				
				activeGlowRipples.push(rippleData);
				return rippleData;
			}
			
			// Tạo sphere 3D cho glow ripple
			function createSphericalRipple(position, layerIndex, totalLayers) {
				// Tạo sphere geometry với độ phân giải vừa phải để performance tốt
				const sphereGeometry = new THREE.SphereGeometry(1, 16, 16);
				
				// Tạo material với hiệu ứng blur mềm mại
				const sphereMaterial = new THREE.MeshBasicMaterial({
					color: params.glowRippleColor,
					transparent: true,
					opacity: 0.0, // Start hidden
					blending: THREE.AdditiveBlending,
					depthWrite: false,
					side: THREE.DoubleSide, // Render cả inside và outside
					alphaTest: 0.001 // Giúp tạo edge mềm mại hơn
				});
				
				const sphere = new THREE.Mesh(sphereGeometry, sphereMaterial);
				sphere.position.copy(position);
				sphere.scale.set(0.1, 0.1, 0.1); // Start very small
				
				// Store layer data
				sphere.userData = {
					layerIndex: layerIndex,
					totalLayers: totalLayers,
					startTime: Date.now() + (layerIndex * 200), // Tăng delay từ 80ms lên 200ms để mượt hơn
					baseOpacity: (1.0 - (layerIndex / totalLayers) * 0.6) * params.glowRippleIntensity * 0.1, // Giảm opacity để nhẹ mắt hơn
					geometry: sphereGeometry,
					material: sphereMaterial
				};
				
				return sphere;
			}
			
			// Update tất cả spherical glow ripples
			function updateGlowRipples() {
				if (params.pauseScene || !params.glowRippleEnabled) return;
				
				const now = Date.now();
				const toRemove = [];
				
				activeGlowRipples.forEach((rippleData, rippleIndex) => {
					if (!rippleData.isActive) {
						toRemove.push(rippleIndex);
						return;
					}
					
					let allSpheresFinished = true;
					
					// Update each sphere in the ripple
					rippleData.spheres.forEach((sphere, sphereIndex) => {
						const elapsed = now - sphere.userData.startTime;
						if (elapsed < 0) {
							allSpheresFinished = false;
							return; // Not started yet
						}
						
						const progress = elapsed / rippleData.duration;
						
						if (progress >= 1.0) {
							// Remove finished sphere
							if (scene.getObjectById(sphere.id)) {
								scene.remove(sphere);
								sphere.userData.geometry.dispose();
								sphere.userData.material.dispose();
							}
							return;
						}
						
						allSpheresFinished = false;
						
						// Tính toán bán kính hiện tại dựa trên progress
						const maxRadius = params.glowRippleMaxDistance;
						const currentRadius = progress * maxRadius;
						
						// Tính scale với ripple effect
						const rippleEffect = 1.0 + Math.sin(progress * Math.PI * params.glowRippleSpeed * 0.1) * params.glowRippleAmplitude;
						const finalRadius = currentRadius * rippleEffect;
						
						// Kiểm tra giới hạn chiều cao: sphere không được chạm tới minHeight
						const sphereBottomY = rippleData.startPosition.y - finalRadius;
						
						if (sphereBottomY <= rippleData.minHeight) {
							// Sphere đã chạm giới hạn 2/3, bắt đầu fade out nhanh
							const fadeProgress = (rippleData.minHeight - sphereBottomY) / (finalRadius * 0.2); // Fade trong 20% radius cuối
							const fadeOpacity = Math.max(0, 1 - fadeProgress * 2); // Fade nhanh gấp đôi
							
							sphere.scale.setScalar(finalRadius);
							sphere.material.opacity = sphere.userData.baseOpacity * fadeOpacity;
						} else {
							// Sphere vẫn trong giới hạn an toàn
							sphere.scale.setScalar(finalRadius);
							
							// Tính opacity với fade dựa trên progress và layer
							const fadeRate = calculateSphericalFadeRate(progress, params.glowRippleFadeType);
							const layerFade = 1.0 - (sphere.userData.layerIndex / sphere.userData.totalLayers) * 0.3;
							const rippleIntensity = Math.sin(progress * Math.PI * 2) * 0.1 + 0.9;
							
							const finalOpacity = sphere.userData.baseOpacity * fadeRate * layerFade * rippleIntensity;
							sphere.material.opacity = Math.max(0, Math.min(finalOpacity, 0.08)); // Giảm cap từ 0.4 xuống 0.08 để không chói mắt
						}
					});
					
					// Remove ripple nếu tất cả spheres đã finished
					if (allSpheresFinished) {
						toRemove.push(rippleIndex);
						rippleData.isActive = false;
					}
				});
				
				// Remove finished ripples
				toRemove.reverse().forEach(index => {
					activeGlowRipples.splice(index, 1);
				});
			}
			
			// Tính toán fade rate cho spherical ripple
			function calculateSphericalFadeRate(progress, fadeType) {
				switch(fadeType) {
					case 'linear':
						return Math.max(0, 1 - progress);
					case 'exponential':
						return Math.exp(-progress * 2.5);
					case 'quadratic':
						return Math.max(0, 1 - (progress * progress));
					case 'smooth':
						const t = Math.min(1, progress);
						return Math.max(0, 1 - (t * t * (3 - 2 * t))); // Smoothstep
					default:
						return Math.max(0, 1 - progress);
				}
			}

			// Continuous lightning glow - sáng liên tục tại điểm bắt đầu lightning
			function createContinuousLightningGlow(position, lightningObject) {
				const glowSize = 18;
				const glowColor = 0x1ffff0;
				
				// Tạo canvas texture với gradient tròn
				const canvas = document.createElement('canvas');
				const size = 64;
				canvas.width = canvas.height = size;
				const ctx = canvas.getContext('2d');
				
				const gradient = ctx.createRadialGradient(size/2, size/2, 0, size/2, size/2, size/2);
				gradient.addColorStop(0, 'rgba(255, 255, 255, 1.0)');
				gradient.addColorStop(0.3, 'rgba(255, 255, 255, 0.8)');
				gradient.addColorStop(0.7, 'rgba(255, 255, 255, 0.3)');
				gradient.addColorStop(1, 'rgba(255, 255, 255, 0.0)');
				
				ctx.fillStyle = gradient;
				ctx.fillRect(0, 0, size, size);
				
				const glowTexture = new THREE.CanvasTexture(canvas);
				
				const glowMat = new THREE.SpriteMaterial({
					map: glowTexture,
					color: glowColor,
					transparent: true,
					opacity: 1.0, // Bắt đầu với full opacity
					blending: THREE.AdditiveBlending,
					depthWrite: false
				});
				
				const glowSprite = new THREE.Sprite(glowMat);
				glowSprite.position.copy(position);
				glowSprite.scale.set(glowSize, glowSize, 1);
				scene.add(glowSprite);
				
				// Store reference trong lightning object để cleanup
				lightningObject.userData.glowSprite = glowSprite;
				lightningObject.userData.glowTexture = glowTexture;
				lightningObject.userData.glowMaterial = glowMat;
				
				console.log('🌟 Created continuous glow for lightning at', position);
			}

			// Tạo lightning phụ ngắn hơn, nhiều nhánh hơn tại cùng điểm spawn
			function createSecondaryLightning(mainStartPoint, mainEndPoint, isHorizontal) {
				const numSecondary = 1 + Math.floor(Math.random() * 2); // 1-2 lightning phụ
				console.log(`🔱 Creating ${numSecondary} secondary lightning bolts`);

				for (let i = 0; i < numSecondary; i++) {
					const segments = [];
					let startPoint, endPoint;

					if (isHorizontal) {
						// Secondary horizontal lightning
						startPoint = mainStartPoint.clone();
						
						// Ngắn hơn main lightning: 60-80% chiều dài
						const mainDistance = mainStartPoint.distanceTo(mainEndPoint);
						const secondaryDistance = mainDistance * (0.6 + Math.random() * 0.2);
						
						// Random direction khác main lightning
						const angle = Math.random() * Math.PI * 2;
						endPoint = startPoint.clone().add(new THREE.Vector3(
							Math.cos(angle) * secondaryDistance,
							(Math.random() - 0.5) * 3, // Random height variation
							Math.sin(angle) * secondaryDistance
						));
						
						// Nhiều nhánh hơn cho horizontal
						const displacement = params.lightningDisplacement * 2.0; // Tăng displacement
						createBranchyHorizontalSegment(startPoint, endPoint, displacement, segments, 0, 3); // More branches
					} else {
						// Secondary vertical lightning  
						startPoint = mainStartPoint.clone();
						
						// Ngắn hơn: chỉ đi 60-85% xuống ground
						const mainHeight = mainStartPoint.y - mainEndPoint.y;
						const secondaryHeight = mainHeight * (0.6 + Math.random() * 0.25);
						
						// Offset horizontal để không trùng main lightning
						const offsetRadius = 8 + Math.random() * 12; // 8-20m offset
						const offsetAngle = Math.random() * Math.PI * 2;
						endPoint = new THREE.Vector3(
							startPoint.x + Math.cos(offsetAngle) * offsetRadius,
							startPoint.y - secondaryHeight,
							startPoint.z + Math.sin(offsetAngle) * offsetRadius
						);
						
						// Nhiều nhánh hơn cho vertical
						const displacement = params.lightningDisplacement * 1.5;
						createBranchyVerticalSegment(startPoint, endPoint, displacement, segments, 0, 4); // More branches & depth
					}

					if (segments.length < 2) continue;

					// Create geometry for secondary lightning
					const geometry = new THREE.BufferGeometry();
					const positions = segments.flatMap(v => v.toArray());
					geometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));

					// Slightly dimmer than main lightning
					const secondaryMaterial = new THREE.LineBasicMaterial({
						color: params.lightningBaseColor,
						transparent: true,
						opacity: 0.0,
						blending: THREE.AdditiveBlending,
						depthWrite: false,
						linewidth: 1.5 // Thinner than main
					});

					// Fewer glow lines cho secondary
					const glowLines = [];
					for (let j = 0; j < 2; j++) { // Only 2 glow layers
						const glowMaterial = new THREE.LineBasicMaterial({
							color: params.lightningGlowColor,
							transparent: true,
							opacity: 0.0,
							blending: THREE.AdditiveBlending,
							depthWrite: false,
							linewidth: 2
						});
						const glowLine = new THREE.LineSegments(geometry, glowMaterial);
						glowLines.push(glowLine);
					}

					const secondaryLightning = new THREE.LineSegments(geometry, secondaryMaterial);
					geometry.drawRange = { start: 0, count: 0 };
					
					secondaryLightning.userData = {
						startTime: Date.now() + (i * 20), // Slight delay between secondary bolts
						totalPoints: positions.length / 3,
						drawCompleted: false,
						drawTimeoutId: null,
						material: secondaryMaterial,
						glowLines: glowLines,
						geometry: geometry,
						isSecondary: true, // Mark as secondary
						isHorizontal: isHorizontal
					};

					scene.add(secondaryLightning);
					glowLines.forEach(line => scene.add(line));
					activeLightning.push(secondaryLightning);

					// Start animation with slight delay
					setTimeout(() => {
						animateSecondaryLightningDraw(secondaryLightning);
					}, i * 25); // 25ms delay between each secondary
				}
			}

			// Tạo segment mịn với nhiều nhánh cho horizontal lightning
			function createBranchyHorizontalSegment(start, end, displacement, segments, depth = 0, maxDepth = 3) {
				const distance = start.distanceTo(end);
				
				if (depth >= maxDepth || distance < 2) {
					// Tạo smooth curve thay vì straight line
					createSmoothCurve(start, end, segments, 3); // 3 intermediate points
					return;
				}

				// Main path with reduced displacement for smoother look
				const midPoint = start.clone().add(end).multiplyScalar(0.5);
				const smoothDisplacement = displacement * 0.6; // Reduce displacement by 40%
				const perpendicular = new THREE.Vector3(
					(Math.random() - 0.5) * smoothDisplacement,
					(Math.random() - 0.5) * smoothDisplacement * 0.3,
					(Math.random() - 0.5) * smoothDisplacement
				);
				midPoint.add(perpendicular);

				// Main path segments với smooth curves
				createBranchyHorizontalSegment(start, midPoint, displacement * 0.8, segments, depth + 1, maxDepth);
				createBranchyHorizontalSegment(midPoint, end, displacement * 0.8, segments, depth + 1, maxDepth);

				// Reduced branching for cleaner look: 40% chance
				if (depth < maxDepth - 1 && Math.random() < 0.4) {
					const numBranches = 1; // Only 1 branch per level
					for (let i = 0; i < numBranches; i++) {
						const branchLength = distance * (0.25 + Math.random() * 0.3); // Shorter: 25-55%
						const branchAngle = Math.random() * Math.PI * 2;
						
						const branchEnd = midPoint.clone().add(new THREE.Vector3(
							Math.cos(branchAngle) * branchLength,
							(Math.random() - 0.5) * branchLength * 0.2, // Less Y variation
							Math.sin(branchAngle) * branchLength
						));
						
						createBranchyHorizontalSegment(midPoint, branchEnd, displacement * 0.4, segments, depth + 1, maxDepth);
					}
				}
			}

			// Tạo segment mịn với nhiều nhánh cho vertical lightning
			function createBranchyVerticalSegment(start, end, displacement, segments, depth = 0, maxDepth = 4) {
				const distance = start.distanceTo(end);
				
				if (depth >= maxDepth || distance < 3) {
					// Tạo smooth curve thay vì straight line
					createSmoothCurve(start, end, segments, 4); // 4 intermediate points for vertical
					return;
				}

				// Main path với reduced displacement
				const midPoint = start.clone().add(end).multiplyScalar(0.5);
				const smoothDisplacement = displacement * 0.5; // Reduce displacement by 50%
				const perpendicular = new THREE.Vector3(
					(Math.random() - 0.5) * smoothDisplacement,
					0,
					(Math.random() - 0.5) * smoothDisplacement
				);
				midPoint.add(perpendicular);

				// Main path segments với smooth transition
				createBranchyVerticalSegment(start, midPoint, displacement * 0.7, segments, depth + 1, maxDepth);
				createBranchyVerticalSegment(midPoint, end, displacement * 0.7, segments, depth + 1, maxDepth);

				// Reduced branching: 30% chance for cleaner look
				if (depth < maxDepth - 2 && Math.random() < 0.3) { // Start branching later
					const numBranches = 1; // Only 1 branch per level
					for (let i = 0; i < numBranches; i++) {
						const branchDirection = new THREE.Vector3(
							(Math.random() - 0.5) * 1.5, // Reduced randomness
							-Math.random() * 0.3, // Less downward bias
							(Math.random() - 0.5) * 1.5
						).normalize();
						
						const branchLength = distance * (0.15 + Math.random() * 0.25); // Shorter: 15-40%
						const branchEnd = midPoint.clone().add(branchDirection.multiplyScalar(branchLength));
						
						// Only add branch if it stays within reasonable bounds
						if (isInRadarMaskAndRectangle(branchEnd.x, branchEnd.z)) {
							createBranchyVerticalSegment(midPoint, branchEnd, displacement * 0.3, segments, depth + 1, maxDepth);
						}
					}
				}
			}

			// Tạo smooth curve giữa 2 điểm thay vì đường thẳng gấp khúc
			function createSmoothCurve(start, end, segments, numPoints = 3) {
				if (numPoints < 2) {
					segments.push(start.clone(), end.clone());
					return;
				}

				const points = [start.clone()];
				
				// Tạo intermediate points với smooth interpolation
				for (let i = 1; i < numPoints; i++) {
					const t = i / numPoints;
					
					// Linear interpolation
					const interpolated = start.clone().lerp(end, t);
					
					// Add small random offset để tạo natural lightning look
					// Nhưng offset nhỏ hơn để smooth hơn
					const maxOffset = start.distanceTo(end) * 0.1; // Only 10% of distance
					const randomOffset = new THREE.Vector3(
						(Math.random() - 0.5) * maxOffset,
						(Math.random() - 0.5) * maxOffset * 0.3, // Less Y variation
						(Math.random() - 0.5) * maxOffset
					);
					
					// Apply smoother offset using sine wave
					const smoothFactor = Math.sin(t * Math.PI); // Peak at middle, 0 at ends
					randomOffset.multiplyScalar(smoothFactor);
					
					interpolated.add(randomOffset);
					points.push(interpolated);
				}
				
				points.push(end.clone());
				
				// Add all segments
				for (let i = 0; i < points.length - 1; i++) {
					segments.push(points[i], points[i + 1]);
				}
			}

			// Animation cho secondary lightning (similar but dimmer)
			function animateSecondaryLightningDraw(secondaryLightning) {
				if (params.pauseScene) {
					if (!secondaryLightning.userData.pauseStartTime) {
						secondaryLightning.userData.pauseStartTime = Date.now();
					}
					setTimeout(() => animateSecondaryLightningDraw(secondaryLightning), 16);
					return;
				}
				
				const now = Date.now();
				const userData = secondaryLightning.userData;
				
				if (userData.pauseStartTime) {
					const pauseDuration = Date.now() - userData.pauseStartTime;
					userData.startTime += pauseDuration;
					userData.pauseStartTime = null;
				}
				
				const elapsed = now - userData.startTime;
				const drawDuration = 120; // Slightly slower than main lightning
				const progress = Math.min(1.0, elapsed / drawDuration);
				const targetCount = Math.min(userData.totalPoints, Math.ceil(userData.totalPoints * progress / 2) * 2);
				
				if (!secondaryLightning || !userData.geometry || !scene.getObjectById(secondaryLightning.id)) {
					if (userData.drawTimeoutId) clearTimeout(userData.drawTimeoutId);
					return;
				}
				
				userData.geometry.drawRange.count = targetCount;
				
				// Dimmer than main lightning
				userData.material.opacity = 0.8 + Math.random() * 0.2; // 80-100% opacity
				userData.glowLines.forEach(line => {
					line.material.opacity = 0.6 + Math.random() * 0.2; // 60-80% opacity
				});
				
				if (progress < 1.0) {
					userData.drawTimeoutId = setTimeout(() => animateSecondaryLightningDraw(secondaryLightning), 16);
				} else if (!userData.drawCompleted) {
					userData.drawCompleted = true;
					userData.drawTimeoutId = null;
					// Secondary lightning fade out naturally, no impact effects
					setTimeout(() => removeLightning(secondaryLightning), 200 + Math.random() * 300);
				}
			}

			// Impact sphere (từ demo) - MUCH SMALLER (1/3 size)
			function createImpactSphere(position) {
				const sphereGeo = new THREE.SphereGeometry(1, 16, 8);
				const sphereMat = new THREE.MeshBasicMaterial({
					color: 0xffffff,
					transparent: true,
					opacity: 0.95,
					blending: THREE.AdditiveBlending,
					depthWrite: false
				});
				
				const impactSphere = new THREE.Mesh(sphereGeo, sphereMat);
				impactSphere.position.copy(position);
				impactSphere.scale.set(0.1, 0.1, 0.1);
				scene.add(impactSphere);
				
				// Animate sphere scale
				const duration = 90 * 0.8;
				const maxSize = 2.5; // Further reduced from 4 to 2.5 (1/3 of original 8)
				let startTime = Date.now(); // CHANGED: const -> let để có thể modify
				
				function scaleAnim() {
					// Pause impact sphere animation khi scene pause
					if (params.pauseScene) {
						// Track pause time để adjust startTime khi resume
						if (!impactSphere.userData.pauseStartTime) {
							impactSphere.userData.pauseStartTime = Date.now();
						}
						requestAnimationFrame(scaleAnim); // Giữ nguyên frame hiện tại
						return;
					}
					
					// Adjust startTime khi resume từ pause
					if (impactSphere.userData && impactSphere.userData.pauseStartTime) {
						const pauseDuration = Date.now() - impactSphere.userData.pauseStartTime;
						startTime += pauseDuration;
						impactSphere.userData.pauseStartTime = null;
						console.log('🔧 Adjusted impact sphere timeline after pause:', pauseDuration + 'ms');
					}
					
					const elapsed = Date.now() - startTime;
					const progress = Math.min(1.0, elapsed / duration);
					const scale = maxSize * Math.sin(progress * Math.PI);
					
					if (impactSphere && scene.getObjectById(impactSphere.id)) {
						impactSphere.scale.set(scale, scale, scale);
						if (progress < 1.0) {
							requestAnimationFrame(scaleAnim);
						}
					}
				}
				
				// Initialize userData để track pause
				impactSphere.userData = {};
				scaleAnim();
				
				// Schedule removal - với pause support
				function scheduleImpactRemoval(removalObj, delay, callback) {
					if (params.pauseScene) {
						// Track pause start time
						if (!removalObj.pauseStartTime) {
							removalObj.pauseStartTime = Date.now();
						}
						setTimeout(() => scheduleImpactRemoval(removalObj, delay, callback), 50);
						return;
					}
					
					// Adjust startTime khi resume từ pause
					if (removalObj.pauseStartTime) {
						const pauseDuration = Date.now() - removalObj.pauseStartTime;
						removalObj.startTime += pauseDuration;
						removalObj.pauseStartTime = null;
					}
					
					const actualElapsed = Date.now() - removalObj.startTime;
					const remaining = Math.max(0, delay - actualElapsed);
					if (remaining > 0) {
						setTimeout(callback, remaining);
					} else {
						callback();
					}
				}
				
				const impactRemovalObj = { startTime: Date.now() };
				scheduleImpactRemoval(impactRemovalObj, 90, () => {
					if (scene.getObjectById(impactSphere.id)) {
						scene.remove(impactSphere);
						impactSphere.geometry.dispose();
						impactSphere.material.dispose();
					}
				});
			}

			// Remove lightning (từ demo)
			function removeLightning(mainLightning) {
				if (!mainLightning || !scene.getObjectById(mainLightning.id)) return;
				
				const userData = mainLightning.userData;
				scene.remove(mainLightning);
				
				if (userData.material) userData.material.dispose();
				if (userData.glowLines) {
					userData.glowLines.forEach(line => {
						if (scene.getObjectById(line.id)) {
							scene.remove(line);
							if (line.material) line.material.dispose();
						}
					});
				}
				if (userData.geometry) userData.geometry.dispose();
				if (userData.drawTimeoutId) clearTimeout(userData.drawTimeoutId);
				
				// Cleanup continuous glow
				if (userData.glowSprite && scene.getObjectById(userData.glowSprite.id)) {
					scene.remove(userData.glowSprite);
					console.log('🗑️ Removed continuous glow sprite');
				}
				if (userData.glowTexture) {
					userData.glowTexture.dispose();
				}
				if (userData.glowMaterial) {
					userData.glowMaterial.dispose();
				}
				
				// Remove from active list
				const index = activeLightning.indexOf(mainLightning);
				if (index > -1) activeLightning.splice(index, 1);
			}

			function scheduleLightning() {
				if (nextLightningTimeoutId) {
					clearTimeout(nextLightningTimeoutId);
					nextLightningTimeoutId = null;
				}
				
				if (params.lightningEnabled && !params.pauseScene) { // Không tạo lightning mới khi pause
					const interval = 100 + Math.random() * params.lightningFrequency;
					console.log('⚡ Scheduling next lightning in', interval + 'ms');
					nextLightningTimeoutId = setTimeout(() => {
						if (!params.pauseScene) { // Double check khi timeout trigger
							console.log('⚡ Timeout triggered, creating lightning...');
							createLightning().catch(error => {
								console.warn('Lightning creation failed:', error);
								// Retry sau 1 giây nếu có lỗi
								setTimeout(scheduleLightning, 1000);
							});
						} else {
							console.log('⚡ Timeout triggered but scene paused, rescheduling...');
							scheduleLightning(); // Reschedule nếu đang pause
						}
					}, interval);
				} else {
					console.log('⚡ Lightning scheduling stopped (disabled or paused)');
				}
			}

			// Force spawn lightning khi resume (nếu không có lightning nào đang hoạt động)
			function trySpawnLightningOnResume() {
				// Đợi một chút để pause state hoàn toàn update
				setTimeout(() => {
					if (params.lightningEnabled && !params.pauseScene) {
						console.log('🔍 Resume debug - Total active lightning:', activeLightning.length);
						
						// Debug: Log all active lightning types
						activeLightning.forEach((obj, i) => {
							if (obj.userData.duration) {
								console.log(`  ${i}: Crackle (duration: ${obj.userData.duration}ms)`);
							} else if (obj.userData.isSecondary) {
								console.log(`  ${i}: Secondary Lightning (drawing: ${!obj.userData.drawCompleted})`);
							} else {
								console.log(`  ${i}: Main Lightning (drawing: ${!obj.userData.drawCompleted})`);
							}
						});
						
						// Check main + secondary lightning (exclude crackles)
						const activeBolts = activeLightning.filter(obj => {
							return !obj.userData.duration; // Không phải crackle
						});
						
						// Tinh tế hơn: check nếu có lightning đang drawing
						const drawingLightning = activeBolts.filter(obj => {
							return !obj.userData.drawCompleted; // Đang drawing
						});
						
						console.log(`🔍 Active bolts: ${activeBolts.length}, Drawing: ${drawingLightning.length}`);
						
						// Clear any existing timeout để tránh conflict
						if (nextLightningTimeoutId) {
							console.log('🔹 Resume: Clearing existing timeout');
							clearTimeout(nextLightningTimeoutId);
							nextLightningTimeoutId = null;
						}
						
						// Spawn lightning nếu không có gì đang drawing
						if (drawingLightning.length === 0) {
							console.log('🔹 Resume: Force spawning new lightning');
							createLightning().catch(error => {
								console.warn('Resume lightning spawn failed:', error);
								// Restart scheduling ngay cả khi spawn fail
								scheduleLightning();
							});
						} else {
							console.log('🔹 Resume: Lightning still drawing, restarting scheduling only');
							// Restart scheduling để tiếp tục cycle
							scheduleLightning();
						}
					} else {
						console.log('🔹 Resume: Lightning disabled or still paused');
					}
				}, 300); // Tăng delay lên 300ms để đảm bảo state ổn định hơn
			}

			// Force all clouds to respawn immediately by setting lifetimes to max
			function forceCloudRespawn() {
				if (!params.cloudEnabled || !cloudComputeParticles) return;
				
				console.log('🔄 Force cloud respawn triggered');
				
				// Create compute that sets all lifetimes to exceed their max (force respawn)
				const forceRespawnCompute = Fn( () => {
					const cloudLifetime = cloudLifetimeBuffer.element( instanceIndex );
					// Set current lifetime > max lifetime to trigger immediate respawn
					cloudLifetime.x = cloudLifetime.y.add( 1.0 );
				} )().compute( params.cloudParticleCount );
				
				// Execute the force respawn compute
				renderer.compute( forceRespawnCompute );
			}

			// Cloud system initialization
			async function initCloudSystem() {
				if (!params.cloudEnabled) return;

				const cloudMaxParticleCount = params.cloudParticleCount;
				
				// Use global radar texture uniform
				const cloudRadarTextureUniform = globalRadarTextureUniform;

				// Create cloud buffers using instancedArray for TSL compatibility
				cloudPositionBuffer = instancedArray( cloudMaxParticleCount, 'vec3' );
				cloudVelocityBuffer = instancedArray( cloudMaxParticleCount, 'vec3' );
				cloudLifetimeBuffer = instancedArray( cloudMaxParticleCount, 'vec2' ); // [currentTime, maxLifetime]

				// Cloud initialization compute
				const cloudComputeInit = Fn( () => {
					const cloudPosition = cloudPositionBuffer.element( instanceIndex );
					const cloudVelocity = cloudVelocityBuffer.element( instanceIndex );
					const cloudLifetime = cloudLifetimeBuffer.element( instanceIndex );

					// Random position within rectangle at rain height range
					cloudPosition.x = hash( instanceIndex ).mul( rainAreaWidthUniform ).sub( rainAreaWidthUniform.mul( 0.5 ) ).add( rainAreaXUniform );
					// Y position: between 75% and 100% of rain height (within allowed range)
					const yRange = rainHeightUniform.mul( 0.25 ); // 1/4 of start height range
					cloudPosition.y = rainHeightUniform.sub( yRange ).add( hash( instanceIndex.add( 1000 ) ).mul( yRange ) );
					cloudPosition.z = hash( instanceIndex.add( 2000 ) ).mul( rainAreaDepthUniform ).sub( rainAreaDepthUniform.mul( 0.5 ) ).add( rainAreaZUniform );

					// Random slow drift velocity
					cloudVelocity.x = hash( instanceIndex.add( 3000 ) ).sub( 0.5 ).mul( 0.5 );
					cloudVelocity.y = hash( instanceIndex.add( 4000 ) ).sub( 0.5 ).mul( 0.2 );
					cloudVelocity.z = hash( instanceIndex.add( 5000 ) ).sub( 0.5 ).mul( 0.5 );

					// Random lifetime
					cloudLifetime.x = hash( instanceIndex.add( 6000 ) ).mul( 100 ); // Current time 0-100
					cloudLifetime.y = float( 50 ).add( hash( instanceIndex.add( 7000 ) ).mul( 50 ) ); // Max lifetime 50-100s
				} )().compute( cloudMaxParticleCount );

				// Cloud compute shader for movement and lifecycle
				const cloudComputeUpdate = Fn( () => {
					const cloudPosition = cloudPositionBuffer.element( instanceIndex );
					const cloudVelocity = cloudVelocityBuffer.element( instanceIndex );
					const cloudLifetime = cloudLifetimeBuffer.element( instanceIndex );

					// Update lifetime
					cloudLifetime.x = cloudLifetime.x.add( deltaTime );

					// Reset if lifetime exceeded
					If( cloudLifetime.x.greaterThan( cloudLifetime.y ), () => {
						// Respawn in rectangle within allowed Y range
						cloudPosition.x = hash( instanceIndex.add( time ) ).mul( rainAreaWidthUniform ).sub( rainAreaWidthUniform.mul( 0.5 ) ).add( rainAreaXUniform );
						// Respawn in allowed Y range: 75%-100% of start height
						const yRange = rainHeightUniform.mul( 0.25 );
						cloudPosition.y = rainHeightUniform.sub( yRange ).add( hash( instanceIndex.add( time.mul( 2 ) ) ).mul( yRange ) );
						cloudPosition.z = hash( instanceIndex.add( time.mul( 3 ) ) ).mul( rainAreaDepthUniform ).sub( rainAreaDepthUniform.mul( 0.5 ) ).add( rainAreaZUniform );

						// Reset lifetime
						cloudLifetime.x = float( 0 );
						cloudLifetime.y = float( 50 ).add( hash( instanceIndex.add( time.mul( 4 ) ) ).mul( 50 ) );

						// New random velocity
						cloudVelocity.x = hash( instanceIndex.add( time.mul( 5 ) ) ).sub( 0.5 ).mul( 0.5 );
						cloudVelocity.y = hash( instanceIndex.add( time.mul( 6 ) ) ).sub( 0.5 ).mul( 0.2 );
						cloudVelocity.z = hash( instanceIndex.add( time.mul( 7 ) ) ).sub( 0.5 ).mul( 0.5 );
					} );

					// Slow drift movement
					const baseMovement = cloudVelocity.mul( cloudMovementSpeedUniform );
					
					// Add turbulence if wind speed is high
					const turbulenceStrength = windSpeedUniform.div( 20 ).min( 1 ); // Max turbulence at 20 m/s wind
					const turbulence = vec3(
						sin( time.add( cloudPosition.x.mul( 0.1 ) ) ).mul( 0.1 ),
						sin( time.mul( 1.3 ).add( cloudPosition.z.mul( 0.1 ) ) ).mul( 0.05 ),
						cos( time.mul( 0.7 ).add( cloudPosition.x.mul( 0.1 ) ) ).mul( 0.1 )
					).mul( turbulenceStrength ).mul( cloudTurbulenceUniform );

					cloudPosition.addAssign( baseMovement.add( turbulence ).mul( deltaTime.mul( 10 ) ) );

					// Keep clouds within bounds (soft boundaries)
					const centerX = rainAreaXUniform;
					const centerZ = rainAreaZUniform;
					const boundaryX = rainAreaWidthUniform.div( 2 ).add( 50 );
					const boundaryZ = rainAreaDepthUniform.div( 2 ).add( 50 );

					// Limit Y movement: from startHeight down to startHeight - 1/4*startHeight
					const minY = rainHeightUniform.mul( 0.75 ); // 3/4 of start height = start height - 1/4*start height
					const maxY = rainHeightUniform.add( 10 ); // Slightly above start height

					If( cloudPosition.x.sub( centerX ).abs().greaterThan( boundaryX ), () => {
						cloudVelocity.x = cloudVelocity.x.negate();
					} );
					If( cloudPosition.z.sub( centerZ ).abs().greaterThan( boundaryZ ), () => {
						cloudVelocity.z = cloudVelocity.z.negate();
					} );
					If( cloudPosition.y.lessThan( minY ), () => {
						cloudVelocity.y = cloudVelocity.y.abs(); // Force upward movement
					} );
					If( cloudPosition.y.greaterThan( maxY ), () => {
						cloudVelocity.y = cloudVelocity.y.negate().mul( 0.5 ); // Gentle downward movement
					} );
				} );

				cloudComputeParticles = cloudComputeUpdate().compute( cloudMaxParticleCount );

				// Create uniforms for cloud parameters
				const cloudSizeUniform = uniform( params.cloudSize );
				const cloudOpacityUniform = uniform( params.cloudOpacity );
				const cloudColorUniform = uniform( params.cloudColor );
				const cloudTurbulenceUniform = uniform( params.cloudTurbulence );
				const cloudMovementSpeedUniform = uniform( params.cloudMovementSpeed );

				// Cloud material with radar mask integration
				const cloudMaterial = new THREE.MeshBasicNodeMaterial({
					depthWrite: false,
					blending: params.cloudAdditive ? THREE.AdditiveBlending : THREE.NormalBlending,
					transparent: true,
					side: THREE.DoubleSide
				});

				const cloudPos = cloudPositionBuffer.toAttribute();
				const cloudLife = cloudLifetimeBuffer.toAttribute();
				
				// Fade clouds based on lifetime (fade in/out)
				const normalizedLife = cloudLife.x.div( cloudLife.y );
				const fadeInOut = normalizedLife.lessThan( 0.1 )
					.select( normalizedLife.div( 0.1 ), // Fade in first 10%
						normalizedLife.greaterThan( 0.9 )
							.select( float( 1 ).sub( normalizedLife ).div( 0.1 ), // Fade out last 10%
								float( 1 ) ) ); // Full opacity in middle

				// Sample radar to modulate opacity (clouds only visible where there's radar data)
				const floorSize = float( 50000.0 );
				const radarSize = float( 2310.0 );
				
				const normalizedX = cloudPos.x.add( floorSize.div( 2 ) ).div( floorSize );
				const normalizedZ = cloudPos.z.add( floorSize.div( 2 ) ).div( floorSize );
				
				const radarStart = floorSize.sub( radarSize ).div( 2 ).div( floorSize );
				const radarEnd = floorSize.add( radarSize ).div( 2 ).div( floorSize );
				
				const radarX = normalizedX.sub( radarStart ).div( radarEnd.sub( radarStart ) );
				const radarZ = normalizedZ.sub( radarStart ).div( radarEnd.sub( radarStart ) );
				
				const radarUV = vec2( radarX, radarZ.oneMinus() );
				const radarSample = cloudRadarTextureUniform.sample( radarUV );
				
				// Final opacity combines lifetime fade and radar presence (RESTORE CORRECT LOGIC)
				const finalOpacity = fadeInOut.mul( radarSample.a.greaterThan( 0 ).select( cloudOpacityUniform, float( 0 ) ) );

				// Set up material like rain particles - use positionNode for positioning
				cloudMaterial.positionNode = positionGeometry.add( cloudPos );
				cloudMaterial.opacityNode = finalOpacity;
				cloudMaterial.colorNode = cloudColorUniform;
				
				// Scale each individual particle using scaleNode (NOT mesh scale)
				const particleScale = cloudSizeUniform;
				cloudMaterial.scaleNode = particleScale;
				
				console.log('Setting up cloud material with', cloudMaxParticleCount, 'particles');

				// Use sphere geometry for cloud particles - similar to rain system
				const cloudGeometry = new THREE.SphereGeometry( 1, 8, 6 ); // Low poly sphere for performance
				
				// Create regular mesh with instancedArray positioning like rain particles
				cloudParticles = new THREE.Mesh( cloudGeometry, cloudMaterial );
				cloudParticles.count = cloudMaxParticleCount;
				cloudParticles.frustumCulled = false;
				
				// NO mesh scale - individual particles scaled by scaleNode
				
				// Store uniforms globally for GUI updates
				window.cloudSizeUniform = cloudSizeUniform;
				window.cloudOpacityUniform = cloudOpacityUniform;
				window.cloudColorUniform = cloudColorUniform;
				window.cloudTurbulenceUniform = cloudTurbulenceUniform;
				window.cloudMovementSpeedUniform = cloudMovementSpeedUniform;
				
				if (params.cloudEnabled) {
					scene.add( cloudParticles );
					console.log('✅ Added cloud particles to scene');
					// Initialize cloud data
					renderer.computeAsync( cloudComputeInit );
					console.log('✅ Running cloud compute init');
				}

				console.log('Cloud system initialized with', cloudMaxParticleCount, 'particles');
				console.log('Cloud particles object:', cloudParticles);
			}

			async function init() {

				const { innerWidth, innerHeight } = window;

				camera = new THREE.PerspectiveCamera( 60, innerWidth / innerHeight, .1, 100000 );
				camera.position.set( 40, 8, 0 );
				camera.lookAt( 0, 0, 0 );

				scene = new THREE.Scene();
				// Background sẽ được thay thế bằng sky mesh

				const dirLight = new THREE.DirectionalLight( 0xffffff, 1.5 );
				dirLight.castShadow = true;
				dirLight.position.set( 3, 17, 17 );
				dirLight.castShadow = true;
				dirLight.shadow.camera.near = 1;
				dirLight.shadow.camera.far = 100000;
				dirLight.shadow.camera.right = 25;
				dirLight.shadow.camera.left = - 25;
				dirLight.shadow.camera.top = 25;
				dirLight.shadow.camera.bottom = - 25;
				dirLight.shadow.mapSize.width = 2048;
				dirLight.shadow.mapSize.height = 2048;
				dirLight.shadow.bias = - 0.01;

				scene.add( dirLight );
				scene.add( new THREE.AmbientLight( 0x808080 ) ); // Ambient light sáng hơn

				const hemiLight = new THREE.HemisphereLight( 0xffffff, 0x444444, 0.6 );
				hemiLight.position.set( 0, 20, 0 );
				scene.add( hemiLight );

				//

				const initialArea = Math.max( params.rainAreaWidth, params.rainAreaDepth ) / 2 + 50;
				collisionCamera = new THREE.OrthographicCamera( - initialArea, initialArea, initialArea, - initialArea, .1, 100000 );
				collisionCamera.position.y = 50;
				collisionCamera.lookAt( 0, 0, 0 );
				collisionCamera.layers.disableAll();
				collisionCamera.layers.enable( 1 );

				collisionPosRT = new THREE.RenderTarget( 1024, 1024 );
				collisionPosRT.texture.type = THREE.HalfFloatType;
				collisionPosRT.texture.magFilter = THREE.NearestFilter;
				collisionPosRT.texture.minFilter = THREE.NearestFilter;
				collisionPosRT.texture.generateMipmaps = false;

				collisionPosMaterial = new THREE.MeshBasicNodeMaterial();
				collisionPosMaterial.colorNode = positionWorld;

				//

				const positionBuffer = instancedArray( maxParticleCount, 'vec3' );
				const velocityBuffer = instancedArray( maxParticleCount, 'vec3' );
				const ripplePositionBuffer = instancedArray( maxParticleCount, 'vec3' );
				const rippleTimeBuffer = instancedArray( maxParticleCount, 'vec3' );
				// THÊM: Buffer trạng thái particle (0 = SPAWN, 1 = FLYING)
				const particleStateBuffer = instancedArray( maxParticleCount, 'float' );
				// THÊM: Buffer fade-in time cho hiệu ứng mượt mà khi spawn
				const fadeTimeBuffer = instancedArray( maxParticleCount, 'float' );
				
				// Store global references
				globalPositionBuffer = positionBuffer;
				globalParticleStateBuffer = particleStateBuffer;
				globalRipplePositionBuffer = ripplePositionBuffer;
				globalRippleTimeBuffer = rippleTimeBuffer;

				// uniform nodes for parameters
				rainHeightUniform = uniform( params.rainHeight );
				rainAreaWidthUniform = uniform( params.rainAreaWidth );
				rainAreaDepthUniform = uniform( params.rainAreaDepth );
				rainAreaXUniform = uniform( params.rainAreaX );
				rainAreaZUniform = uniform( params.rainAreaZ );
				speedMultiplierUniform = uniform( params.speedMultiplier );
				rainColorUniform = uniform( params.rainColor );
				rainDropWidthUniform = uniform( params.rainDropWidth );
				rainDropHeightUniform = uniform( params.rainDropHeight );
				rainLifetimePercentUniform = uniform( params.rainLifetimePercent / 100.0 );
				
				// <<< THÊM VÀO: Khởi tạo uniform cho gió
				windDirectionRadUniform = uniform( THREE.MathUtils.degToRad( params.windDirection ) );
				windSpeedUniform = uniform( params.windSpeed );

				// Radar uniforms cho shader
				radarCenterUniform = uniform( vec2( 0, 0 ) ); // Tâm radar trên floor (0,0)
				radarSizeUniform = uniform( vec2( 2310, 2310 ) ); // Kích thước radar trên floor
				
				// Tạo texture trống ban đầu cho radar (sẽ update sau khi load)
				const emptyCanvas = document.createElement('canvas');
				emptyCanvas.width = emptyCanvas.height = 1;
				const emptyCtx = emptyCanvas.getContext('2d');
				emptyCtx.fillStyle = 'rgba(0,0,0,0)';
				emptyCtx.fillRect(0, 0, 1, 1);
				const emptyTexture = new THREE.CanvasTexture(emptyCanvas);
				const radarTextureUniform = texture( emptyTexture );
				globalRadarTextureUniform = radarTextureUniform; // Store globally for cloud system

				// compute

				const randUint = () => uint( Math.random() * 0xFFFFFF );

				const computeInit = Fn( () => {

					const position = positionBuffer.element( instanceIndex );
					const velocity = velocityBuffer.element( instanceIndex );
					const rippleTime = rippleTimeBuffer.element( instanceIndex );
					const particleState = particleStateBuffer.element( instanceIndex );
					const fadeTime = fadeTimeBuffer.element( instanceIndex );

					// Khởi tạo tất cả particle ở trạng thái SPAWN
					particleState.assign( 0.0 );
					
					// Khởi tạo fade time = 0 (hoàn toàn trong suốt)
					fadeTime.assign( 0.0 );

					// Đặt vị trí ẩn ban đầu (sẽ spawn khi có radar texture)
					position.x = float( 99999.0 );
					position.y = float( 99999.0 );
					position.z = float( 99999.0 );

					// Vận tốc rơi ngẫu nhiên
					velocity.y = hash( instanceIndex ).mul( - .04 ).add( - .2 );

					rippleTime.x = 1000;

				} )().compute( maxParticleCount );

				//

				const computeUpdate = Fn( () => {

					const maxArea = rainAreaWidthUniform.max( rainAreaDepthUniform ).div( 2 ).add( 50 );
					const getCoord = ( pos ) => pos.add( maxArea ).div( maxArea.mul( 2 ) );

					const position = positionBuffer.element( instanceIndex );
					const velocity = velocityBuffer.element( instanceIndex );
					const ripplePosition = ripplePositionBuffer.element( instanceIndex );
					const rippleTime = rippleTimeBuffer.element( instanceIndex );
					const particleState = particleStateBuffer.element( instanceIndex );
					const fadeTime = fadeTimeBuffer.element( instanceIndex );

					// ===== LOGIC 2 TRẠNG THÁI =====
					
					If( particleState.equal( 0.0 ), () => {
						
						// ===== TRẠNG THÁI SPAWN =====
						// Thử tìm vị trí spawn hợp lệ trong radar mask
						
						const randX = hash( instanceIndex.add( time ) );
						const randZ = hash( instanceIndex.add( time.add( randUint() ) ) );
						
						const spawnX = randX.mul( rainAreaWidthUniform ).sub( rainAreaWidthUniform.mul( 0.5 ) ).add( rainAreaXUniform );
						const spawnZ = randZ.mul( rainAreaDepthUniform ).sub( rainAreaDepthUniform.mul( 0.5 ) ).add( rainAreaZUniform );
						
						// Kiểm tra vị trí spawn có trong radar mask không (dùng logic như getRadarValue())
						const floorSize = float( 50000.0 );
						const radarSize = float( 2310.0 );
						
						// Normalize coordinates từ world space sang [0,1]
						const normalizedX = spawnX.add( floorSize.div( 2 ) ).div( floorSize );
						const normalizedZ = spawnZ.add( floorSize.div( 2 ) ).div( floorSize );
						
						// Chỉ kiểm tra trong vùng radar (căn giữa)
						const radarStart = floorSize.sub( radarSize ).div( 2 ).div( floorSize );
						const radarEnd = floorSize.add( radarSize ).div( 2 ).div( floorSize );
						
						const inRadarBounds = normalizedX.greaterThanEqual( radarStart )
							.and( normalizedX.lessThanEqual( radarEnd ) )
							.and( normalizedZ.greaterThanEqual( radarStart ) )
							.and( normalizedZ.lessThanEqual( radarEnd ) );
						
						// Tính UV coordinates cho radar texture
						const radarX = normalizedX.sub( radarStart ).div( radarEnd.sub( radarStart ) );
						const radarZ = normalizedZ.sub( radarStart ).div( radarEnd.sub( radarStart ) );
						
						// Sample radar texture (flip Y axis như trong getRadarValue)
						const radarUV = vec2( radarX, radarZ.oneMinus() );
						const radarSample = radarTextureUniform.sample( radarUV );
						
						// Nếu vị trí trong radar bounds VÀ có radar (alpha > 0) thì spawn thành công
						If( inRadarBounds.and( radarSample.a.greaterThan( 0.0 ) ), () => {
							// Spawn particle tại vị trí hợp lệ
							position.x = spawnX;
							position.y = rainHeightUniform; // Start height
							position.z = spawnZ;
							
							// Reset fade time để bắt đầu fade-in
							fadeTime.assign( 0.0 );
							
							// Chuyển sang trạng thái FLYING
							particleState.assign( 1.0 );
						} );
						
						// Nếu không hợp lệ, giữ nguyên trạng thái SPAWN, thử lại frame sau
						
					} ).Else( () => {
						
						// ===== TRẠNG THÁI FLYING =====
						// Bay tự do theo gió + gravity, không bị giới hạn bởi radar/rectangle
						
						// Cập nhật fade time (fade-in trong 0.5 giây)
						fadeTime.addAssign( deltaTime );
						
						// **LIFETIME LOGIC**: Tính theo % thời gian rơi từ rain height xuống đất
						// Velocity units trong Three.js không phải real-world, cần scale
						// Estimate: rain từ height 50 nên rơi khoảng 5-10 giây với speedMultiplier=1
						const estimatedFallSpeed = rainHeightUniform.div( 8.0 ); // 8s baseline
						const actualFallSpeed = estimatedFallSpeed.mul( speedMultiplierUniform );
						
						// Thời gian rơi toàn bộ từ rainHeight xuống đất (y≈0)
						const totalFallTime = rainHeightUniform.div( actualFallSpeed );
						
						// Lifetime = % của thời gian rơi hết
						const maxLifetime = totalFallTime.mul( rainLifetimePercentUniform );
						
						// Nếu đã vượt quá maxLifetime thì despawn
						If( fadeTime.greaterThan( maxLifetime ), () => {
							// Despawn particle - reset về trạng thái SPAWN
							particleState.assign( 0.0 );
							fadeTime.assign( 0.0 );
							
							// Ẩn particle
							position.x = float( 99999.0 );
							position.y = float( 99999.0 );
							position.z = float( 99999.0 );
							
							// DEBUG: In thông tin lifetime (chỉ cho particle đầu tiên để tránh spam)
							// If( instanceIndex.equal( 0 ), () => {
							//     console.log('Particle despawned by lifetime:', fadeTime.value, '>', actualLifetime.value);
							// } );
						} );
						
						// Tính toán vận tốc gió
						const windDirectionVec = vec2( windDirectionRadUniform.sin(), windDirectionRadUniform.cos() );
						const windVelocityScale = 0.1;
						const windVelocity = vec3( windDirectionVec.x, 0, windDirectionVec.y ).mul( windSpeedUniform ).mul( windVelocityScale );
						
						// Vận tốc tổng = rơi + gió
						const totalVelocity = velocity.add( windVelocity );
						
						// Cập nhật vị trí (bay tự do)
						position.addAssign( totalVelocity.mul( speedMultiplierUniform ) );
						
						// Kiểm tra va chạm với sàn
						const collisionArea = texture( collisionPosRT.texture, getCoord( position.xz ) );
						const surfaceOffset = 1.05;
						const floorPosition = collisionArea.y.add( surfaceOffset );
						const ripplePivotOffsetY = - .9;
						
						If( position.y.add( ripplePivotOffsetY ).lessThan( floorPosition ), () => {
							
							// Tạo ripple tại vị trí va chạm
							ripplePosition.xz = position.xz;
							ripplePosition.y = floorPosition;
							rippleTime.x = 1;
							
							// Reset về trạng thái SPAWN để tìm vị trí spawn mới
							particleState.assign( 0.0 );
							
							// Reset fade time
							fadeTime.assign( 0.0 );
							
							// Ẩn particle cho đến khi spawn lại
							position.x = float( 99999.0 );
							position.y = float( 99999.0 );
							position.z = float( 99999.0 );
							
						} );
						
					} );

					// Cập nhật ripple (không thay đổi)
					rippleTime.x = rippleTime.x.add( deltaTime.mul( 4 ) );
					
					const rippleOnSurface = texture( collisionPosRT.texture, getCoord( ripplePosition.xz ) );
					const surfaceOffset = 1.05; // Khai báo lại cho ripple
					const rippleFloorArea = rippleOnSurface.y.add( surfaceOffset );
					
					If( ripplePosition.y.greaterThan( rippleFloorArea ), () => {
						rippleTime.x = 1000;
					} );

				} );

				computeParticles = computeUpdate().compute( maxParticleCount );

				// rain - material với fade-in effect

				rainMaterial = new THREE.MeshBasicNodeMaterial();
				const rainIntensity = uv().distance( vec2( .5, 0 ) ).oneMinus().mul( 3 ).exp().mul( .1 );
				const particlePos = positionBuffer.toAttribute();
				const particleFadeTime = fadeTimeBuffer.toAttribute();
				
				// Tính fade alpha: fade-in trong 0.5 giây, max opacity = 0.8
				const fadeAlpha = particleFadeTime.div( 0.5 ).min( 1.0 ).mul( 0.8 );
				
				rainMaterial.colorNode = rainIntensity.mul( rainColorUniform );
				rainMaterial.vertexNode = billboarding( { position: particlePos } );
				rainMaterial.opacityNode = fadeAlpha; // Opacity dựa trên fade time
				rainMaterial.side = THREE.DoubleSide;
				rainMaterial.forceSinglePass = true;
				rainMaterial.depthWrite = false;
				rainMaterial.depthTest = true;
				rainMaterial.transparent = true;

				// Sử dụng geometry cố định (base size), scale sẽ được áp dụng sau
				const rainGeometry = new THREE.PlaneGeometry( 0.1, 2.0 );
				rainParticles = new THREE.Mesh( rainGeometry, rainMaterial );
				rainParticles.count = instanceCount;
				rainParticles.frustumCulled = false; // Tắt frustum culling để không biến mất khi zoom
				
				// Áp dụng scale ban đầu
				rainParticles.scale.set( params.rainDropWidth / 0.1, params.rainDropHeight / 2.0, 1 );
				
				scene.add( rainParticles );

				// ripple

				const rippleTime = rippleTimeBuffer.element( instanceIndex ).x;

				const rippleEffect = Fn( () => {

					const center = uv().add( vec2( - .5 ) ).length().mul( 7 );
					const distance = rippleTime.sub( center );

					return distance.min( 1 ).sub( distance.max( 1 ).sub( 1 ) ).mul( 2.5 ); // Tăng intensity x2.5

				} );
				
				// Store global reference
				globalRippleEffect = rippleEffect;

				rippleMaterial = new THREE.MeshBasicNodeMaterial();
				rippleMaterial.colorNode = rippleEffect();
				rippleMaterial.positionNode = positionGeometry.add( ripplePositionBuffer.toAttribute() );
				rippleMaterial.opacityNode = rippleTime.mul( .3 ).oneMinus().max( 0 ).mul( 1.0 ); // Tăng opacity từ .5 lên 1.0
				rippleMaterial.side = THREE.DoubleSide;
				rippleMaterial.forceSinglePass = true;
				rippleMaterial.depthWrite = false;
				rippleMaterial.depthTest = true;
				rippleMaterial.transparent = true;

				// ripple geometry

				const surfaceRippleGeometry = new THREE.PlaneGeometry( 2.5, 2.5 );
				surfaceRippleGeometry.rotateX( - Math.PI / 2 );

				const xRippleGeometry = new THREE.PlaneGeometry( 1, 2 );
				xRippleGeometry.rotateY( - Math.PI / 2 );

				const zRippleGeometry = new THREE.PlaneGeometry( 1, 2 );

				const rippleGeometry = BufferGeometryUtils.mergeGeometries( [ surfaceRippleGeometry, xRippleGeometry, zRippleGeometry ] );

				rippleParticles = new THREE.Mesh( rippleGeometry, rippleMaterial );
				rippleParticles.count = instanceCount;
				rippleParticles.frustumCulled = false; // Tắt frustum culling
				scene.add( rippleParticles );

				// ===== CLOUD SYSTEM =====
				await initCloudSystem();

				// floor geometry với grid pattern (tăng kích thước)

				const floorGeometry = new THREE.PlaneGeometry( 50000, 50000 );
				floorGeometry.rotateX( - Math.PI / 2 );

				// Tạo material với grid pattern
				const gridMaterial = new THREE.MeshStandardNodeMaterial();

				// Tạo grid pattern bằng shader nodes
				gridSizeUniform = uniform( params.gridSize ); // Kích thước ô vuông
				floorColorUniform = uniform( params.floorColor );
				gridLineColorUniform = uniform( params.gridLineColor );

				const gridPattern = Fn( () => {
					const worldPos = positionWorld;
					const gridUV = worldPos.xz.div( gridSizeUniform );

					// Tạo grid lines
					const gridX = fract( gridUV.x );
					const gridZ = fract( gridUV.y );

					const lineWidth = 0.05;
					const gridLineX = gridX.lessThan( lineWidth ).or( gridX.greaterThan( 1.0 - lineWidth ) );
					const gridLineZ = gridZ.lessThan( lineWidth ).or( gridZ.greaterThan( 1.0 - lineWidth ) );

					const isLine = gridLineX.or( gridLineZ );

					return mix( floorColorUniform, gridLineColorUniform, isLine );
				} );

				gridMaterial.colorNode = gridPattern();
				gridMaterial.roughness = 0.7;
				gridMaterial.metalness = 0.0;

				floor = new THREE.Mesh( floorGeometry, gridMaterial );
				floor.position.y = -1; // Floor ở -1
				floor.receiveShadow = true;
				floor.renderOrder = 0; // Floor render đầu tiên
				floor.material.transparent = params.floorOpacity < 1.0;
				floor.material.opacity = params.floorOpacity;
				scene.add( floor );

				// Tạo radar plane
				const radarGeometry = new THREE.PlaneGeometry( 2310, 2310 );
				radarGeometry.rotateX( - Math.PI / 2 );

				// Load ảnh radar
				const textureLoader = new THREE.TextureLoader();
				radarTexture = textureLoader.load( 'radar_data/1.png', function(texture) {
					// Cập nhật radarTextureUniform cho compute shader
					radarTextureUniform.value = radarTexture;
					
					console.log('Radar texture loaded and uniform updated for 2-state logic!', {
						radarTexture: !!radarTexture,
						width: radarTexture.image.width,
						height: radarTexture.image.height
					});
				});

				const radarMaterial = new THREE.MeshBasicMaterial({ 
					map: radarTexture, 
					transparent: true,
					opacity: params.radarOpacity,
					depthWrite: false, // Tắt depth write để tránh z-fighting
					depthTest: false,  // Tắt depth test để tránh z-fighting
					polygonOffset: true,      // Bật polygon offset
					polygonOffsetFactor: -1,  // Đẩy về phía camera
					polygonOffsetUnits: -1    // Thêm offset units
				});

				radarPlane = new THREE.Mesh( radarGeometry, radarMaterial );
				radarPlane.position.y = 2; // Radar ở trên cao để tránh z-fighting hoàn toàn
				radarPlane.renderOrder = 1; // Render sau floor
				radarPlane.receiveShadow = false; // Không nhận bóng
				radarPlane.castShadow = false;    // Không đổ bóng
				scene.add( radarPlane );



				//



				// START: Thêm vào để trực quan hóa
				rainBox = new THREE.Box3();
				rainAreaHelper = new THREE.Box3Helper( rainBox, 0xffff00 ); // Màu vàng
				scene.add( rainAreaHelper );
				updateRainAreaHelper(); // Cập nhật lần đầu

				// Tạo wind arrow
				createWindArrow();
				// Tạo rectangle drag arrow
				createRectangleDragGizmo();
				// END: Thêm vào để trực quan hóa

				//

				clock = new THREE.Clock();

				//

				renderer = new THREE.WebGPURenderer( { antialias: true } );
				renderer.setPixelRatio( window.devicePixelRatio );
				renderer.setSize( window.innerWidth, window.innerHeight );
				renderer.setAnimationLoop( animate );
				renderer.toneMapping = THREE.ACESFilmicToneMapping;
				renderer.toneMappingExposure = params.exposure;
				document.body.appendChild( renderer.domElement );
				stats = new Stats();
				document.body.appendChild( stats.dom );

				//

				renderer.computeAsync( computeInit );

				//

				// Initialize sky system
				initSky();

				//

				controls = new OrbitControls( camera, renderer.domElement );
				controls.minDistance = 0.1;
				controls.maxDistance = Infinity; // Zoom vô hạn
				controls.update();

				//

				window.addEventListener( 'resize', onWindowResize );

				// Rectangle drag event listeners
				window.addEventListener( 'mousedown', onGizmoMouseDown );
				window.addEventListener( 'mousemove', onGizmoMouseMove );
				window.addEventListener( 'mouseup', onGizmoMouseUp );
				


				// gui

				const gui = new GUI();
				window.gui = gui; // Store for drag updates

				// ===== 1. GENERAL CONTROLS =====
				const generalFolder = gui.addFolder( 'General Controls' );
				generalFolder.add( params, 'rainHeight', 10, 100, 1 ).name( 'start height' ).onChange( (v) => {
					rainHeightUniform.value = v;
					updateRainAreaHelper();
					forceCloudRespawn();
				} );
				generalFolder.add( params, 'rainAreaWidth', 50, 500, 1 ).name( 'area width (X)' ).onChange( (v) => {
					rainAreaWidthUniform.value = v;
					const maxArea = Math.max( v, params.rainAreaDepth ) / 2 + 50;
					collisionCamera.left = -maxArea;
					collisionCamera.right = maxArea;
					collisionCamera.top = maxArea;
					collisionCamera.bottom = -maxArea;
					collisionCamera.updateProjectionMatrix();
					updateRainAreaHelper();
					forceCloudRespawn();
					updateRectangleDragGizmo();
				} );
				generalFolder.add( params, 'rainAreaDepth', 50, 500, 1 ).name( 'area depth (Z)' ).onChange( (v) => {
					rainAreaDepthUniform.value = v;
					const maxArea = Math.max( params.rainAreaWidth, v ) / 2 + 50;
					collisionCamera.left = -maxArea;
					collisionCamera.right = maxArea;
					collisionCamera.top = maxArea;
					collisionCamera.bottom = -maxArea;
					collisionCamera.updateProjectionMatrix();
					updateRainAreaHelper();
					forceCloudRespawn();
					updateRectangleDragGizmo();
				} );
				generalFolder.add( params, 'rainAreaX', -1000, 1000, 1 ).name( 'area center X' ).onChange( (v) => {
					rainAreaXUniform.value = v;
					updateRainAreaHelper();
					updateCameraTarget();
					forceCloudRespawn();
					updateRectangleDragGizmo();
				} );
				generalFolder.add( params, 'rainAreaZ', -1000, 1000, 1 ).name( 'area center Z' ).onChange( (v) => {
					rainAreaZUniform.value = v;
					updateRainAreaHelper();
					updateCameraTarget();
					forceCloudRespawn();
					updateRectangleDragGizmo();
				} );
				generalFolder.add( params, 'pauseScene' ).name( 'Pause Scene' ).onChange( (v) => {
					if (v) {
						console.log('🛑 Scene paused - Active lightning:', activeLightning.length);
						activeLightning.forEach((obj, i) => {
							if (obj.userData.duration) {
								console.log(`  Crackle ${i}: elapsed ${Date.now() - obj.userData.startTime}ms / ${obj.userData.duration}ms`);
							} else {
								console.log(`  Lightning ${i}: drawing...`);
							}
						});
					} else {
						console.log('▶️ Scene resumed - Active lightning:', activeLightning.length);
						if (nextLightningTimeoutId) {
							clearTimeout(nextLightningTimeoutId);
							nextLightningTimeoutId = null;
						}
						setTimeout(() => {
							if (params.lightningEnabled && !params.pauseScene) {
								const mainLightning = activeLightning.filter(obj => !obj.userData.duration && !obj.userData.isSecondary);
								console.log('▶️ Resume: Main lightning count:', mainLightning.length);
								if (mainLightning.length === 0) {
									console.log('▶️ Resume: Spawning lightning');
									createLightning().catch(() => scheduleLightning());
								} else {
									console.log('▶️ Resume: Just restart scheduling');
									scheduleLightning();
								}
							}
						}, 200);
					}
				} );
				generalFolder.open();

				// ===== 2. RAIN CONTROLS =====
				const rainFolder = gui.addFolder( 'Rain Controls' );
				rainFolder.add( rainParticles, 'count', 200, maxParticleCount, 1 ).name( 'drop count' ).onChange( ( v ) => rippleParticles.count = v );
				rainFolder.add( params, 'speedMultiplier', 0.0, 3, 0.05 ).name( 'fall speed' ).onChange( (v) => {
					speedMultiplierUniform.value = v;
				} );
				rainFolder.addColor( params, 'rainColor' ).name( 'drop color' ).onChange( (v) => {
					rainColorUniform.value.copy( v );
				} );
				rainFolder.add( params, 'rainDropWidth', 0.05, 1.0, 0.05 ).name( 'drop width' ).onChange( (v) => {
					rainDropWidthUniform.value = v;
					updateRainDropSize();
				} );
				rainFolder.add( params, 'rainDropHeight', 0.5, 5.0, 0.1 ).name( 'drop height' ).onChange( (v) => {
					rainDropHeightUniform.value = v;
					updateRainDropSize();
				} );
				rainFolder.add( params, 'rainLifetimePercent', 10, 100, 5 ).name( 'lifetime % of fall time' ).onChange( (v) => {
					rainLifetimePercentUniform.value = v / 100.0;
					console.log('🔧 Lifetime changed:', v + '%', '→ uniform:', v / 100.0);
				} );
				rainFolder.open();

				// ===== 3. RADAR CONTROLS =====
				const radarFolder = gui.addFolder( 'Radar Controls' );
				radarFolder.add( params, 'radarOpacity', 0.0, 1.0, 0.1 ).name( 'Radar Opacity' ).onChange( (v) => {
					if ( radarPlane && radarPlane.material ) {
						radarPlane.material.opacity = v;
					}
				} );
				radarFolder.open();

				// ===== 4. WIND CONTROLS =====
				const windFolder = gui.addFolder( 'Wind Controls' );
				windFolder.add( params, 'windDirection', 0, 360, 1 ).name( 'Wind Direction (°)' ).onChange( () => {
					updateWindArrow();
				} );
				windFolder.add( params, 'windSpeed', 0, 20, 0.1 ).name( 'Wind Speed (m/s)' ).onChange( () => {
					updateWindArrow();
				} );
				windFolder.open();

				// ===== 5. LIGHTNING CONTROLS =====
				const lightningFolder = gui.addFolder( 'Lightning Controls' );
				lightningFolder.add( params, 'lightningEnabled' ).name( 'Lightning Enabled' ).onChange( (v) => {
					if (v) {
						scheduleLightning();
					} else {
						if (nextLightningTimeoutId) {
							clearTimeout(nextLightningTimeoutId);
							nextLightningTimeoutId = null;
						}
					}
				} );
				lightningFolder.add( params, 'lightningFrequency', 50, 1000, 50 ).name( 'Max Interval (ms)' );
				lightningFolder.add( params, 'lightningBranches', 1, 6, 1 ).name( 'Max Branches' );
				lightningFolder.add( params, 'lightningDisplacement', 10, 100, 5 ).name( 'Displacement' );
				lightningFolder.add( params, 'lightningDuration', 50, 2000, 50 ).name( 'Duration (ms)' );
				lightningFolder.add( params, 'lightningHorizontalEnabled' ).name( 'Horizontal Lightning' );
				lightningFolder.addColor( params, 'lightningBaseColor' ).name( 'Lightning Color' );
				lightningFolder.addColor( params, 'lightningGlowColor' ).name( 'Glow Color' );
				
				// Glow Ripple controls
				lightningFolder.add( params, 'glowRippleEnabled' ).name( 'Sphere Ripple Enabled' );
				lightningFolder.add( params, 'glowRippleMaxDistance', 100, 1000, 50 ).name( 'Ripple Distance' );
				lightningFolder.add( params, 'glowRippleIntensity', 0.01, 0.3, 0.01 ).name( 'Ripple Intensity' );
				lightningFolder.add( params, 'glowRippleSpeed', 1, 20, 1 ).name( 'Ripple Speed' );
				lightningFolder.addColor( params, 'glowRippleColor' ).name( 'Ripple Color' );
				
				lightningFolder.open();

				// ===== 6. CLOUD CONTROLS =====
				const cloudFolder = gui.addFolder( 'Cloud Controls' );
				cloudFolder.add( params, 'cloudEnabled' ).name( 'Clouds Enabled' ).onChange( async (v) => {
					if (v) {
						await initCloudSystem();
					} else {
						if (cloudParticles) {
							scene.remove(cloudParticles);
							cloudParticles = null;
						}
					}
				} );
				cloudFolder.add( params, 'cloudParticleCount', 1000, 10000, 100 ).name( 'Particle Count' ).onChange( async () => {
					if (params.cloudEnabled) {
						if (cloudParticles) scene.remove(cloudParticles);
						await initCloudSystem();
					}
				} );
				cloudFolder.add( params, 'cloudOpacity', 0.0, 1.0, 0.05 ).name( 'Cloud Opacity' ).onChange( (v) => {
					if (window.cloudOpacityUniform) window.cloudOpacityUniform.value = v;
				} );
				cloudFolder.addColor( params, 'cloudColor' ).name( 'Cloud Color' ).onChange( (v) => {
					if (window.cloudColorUniform) window.cloudColorUniform.value.copy( v );
				} );
				cloudFolder.add( params, 'cloudTurbulence', 0.0, 1.0, 0.05 ).name( 'Turbulence' ).onChange( (v) => {
					if (window.cloudTurbulenceUniform) window.cloudTurbulenceUniform.value = v;
				} );
				cloudFolder.add( params, 'cloudMovementSpeed', 0.05, 1.0, 0.05 ).name( 'Movement Speed' ).onChange( (v) => {
					if (window.cloudMovementSpeedUniform) window.cloudMovementSpeedUniform.value = v;
				} );
				cloudFolder.add( params, 'cloudAdditive' ).name( 'Additive Blending' ).onChange( async (v) => {
					if (params.cloudEnabled) {
						if (cloudParticles) scene.remove(cloudParticles);
						await initCloudSystem();
					}
				} );
				cloudFolder.open();

				// ===== 7. CAMERA CONTROLS =====
				const cameraFolder = gui.addFolder( 'Camera Controls' );
				
				// Camera position (read-only)
				const cameraPos = { x: 0, y: 0, z: 0 };
				const cameraTarget = { x: 0, y: 0, z: 0 };
				const cameraRotation = { pitch: 0, yaw: 0 };
				
				// Update camera info display
				function updateCameraInfo() {
					cameraPos.x = Math.round(camera.position.x * 100) / 100;
					cameraPos.y = Math.round(camera.position.y * 100) / 100;
					cameraPos.z = Math.round(camera.position.z * 100) / 100;
					
					// Get camera target from controls
					if (controls && controls.target) {
						cameraTarget.x = Math.round(controls.target.x * 100) / 100;
						cameraTarget.y = Math.round(controls.target.y * 100) / 100;
						cameraTarget.z = Math.round(controls.target.z * 100) / 100;
					}
					
					// Calculate pitch and yaw from camera direction
					const direction = new THREE.Vector3();
					camera.getWorldDirection(direction);
					cameraRotation.pitch = Math.round(Math.asin(-direction.y) * 180 / Math.PI);
					cameraRotation.yaw = Math.round(Math.atan2(direction.x, direction.z) * 180 / Math.PI);
					
					// Refresh GUI displays
					for (let i in cameraFolder.__controllers) {
						cameraFolder.__controllers[i].updateDisplay();
					}
				}
				
				// Position displays (read-only)
				cameraFolder.add( cameraPos, 'x' ).name( 'Camera X' ).listen();
				cameraFolder.add( cameraPos, 'y' ).name( 'Camera Y' ).listen();
				cameraFolder.add( cameraPos, 'z' ).name( 'Camera Z' ).listen();
				
				// Target displays (read-only)
				cameraFolder.add( cameraTarget, 'x' ).name( 'Target X' ).listen();
				cameraFolder.add( cameraTarget, 'y' ).name( 'Target Y' ).listen();
				cameraFolder.add( cameraTarget, 'z' ).name( 'Target Z' ).listen();
				
				// Rotation displays (read-only)
				cameraFolder.add( cameraRotation, 'pitch' ).name( 'Pitch (°)' ).listen();
				cameraFolder.add( cameraRotation, 'yaw' ).name( 'Yaw (°)' ).listen();
				
				cameraFolder.open();
				
				// Update camera info every frame
				setInterval(updateCameraInfo, 100);

				// ===== 8. SKY CONTROLS =====
				const skyFolder = gui.addFolder( 'Sky Controls' );
				skyFolder.add( params, 'turbidity', 0.0, 20.0, 0.1 ).name( 'Turbidity' ).onChange( () => {
					window.updateSky();
				} );
				skyFolder.add( params, 'rayleigh', 0.0, 4, 0.001 ).name( 'Rayleigh' ).onChange( () => {
					window.updateSky();
				} );
				skyFolder.add( params, 'mieCoefficient', 0.0, 0.1, 0.001 ).name( 'Mie Coefficient' ).onChange( () => {
					window.updateSky();
				} );
				skyFolder.add( params, 'mieDirectionalG', 0.0, 1, 0.001 ).name( 'Mie Directional G' ).onChange( () => {
					window.updateSky();
				} );
				skyFolder.add( params, 'elevation', 0, 90, 0.1 ).name( 'Sun Elevation' ).onChange( () => {
					window.updateSky();
				} );
				skyFolder.add( params, 'azimuth', -180, 180, 0.1 ).name( 'Sun Azimuth' ).onChange( () => {
					window.updateSky();
				} );
				skyFolder.add( params, 'exposure', 0, 1, 0.0001 ).name( 'Exposure' ).onChange( () => {
					window.updateSky();
				} );
				skyFolder.open();

				// ===== 9. FLOOR CONTROLS =====
				const floorFolder = gui.addFolder( 'Floor Controls' );
				floorFolder.add( params, 'gridSize', 2, 50, 0.5 ).name( 'grid size' ).onChange( (v) => {
					gridSizeUniform.value = v;
				} );
				floorFolder.addColor( params, 'floorColor' ).name( 'floor color' ).onChange( (v) => {
					floorColorUniform.value.copy( v );
				} );
				floorFolder.addColor( params, 'gridLineColor' ).name( 'grid line color' ).onChange( (v) => {
					gridLineColorUniform.value.copy( v );
				} );
				floorFolder.add( params, 'floorOpacity', 0.0, 1.0, 0.1 ).name( 'floor opacity' ).onChange( (v) => {
					if ( floor && floor.material ) {
						floor.material.opacity = v;
						floor.material.transparent = v < 1.0;
					}
				} );
				floorFolder.open();

			}

			function onWindowResize() {

				const { innerWidth, innerHeight } = window;

				camera.aspect = innerWidth / innerHeight;
				camera.updateProjectionMatrix();

				renderer.setSize( innerWidth, innerHeight );

			}

			function animate() {

				stats.update();

				const delta = params.pauseScene ? 0 : clock.getDelta(); // Dừng time khi pause

				// Update lightning and ground crackles (like in demo)
				const currentTime = Date.now();
				const toRemove = [];
				
				activeLightning.forEach(lightningObj => {
					// Pause handling cho ground crackles
					if (params.pauseScene) {
						// Track pause start time cho crackles
						if (lightningObj.userData.duration && !lightningObj.userData.pauseStartTime) {
							lightningObj.userData.pauseStartTime = currentTime;
						}
						return; // Skip animation khi pause
					}
					
					// Adjust startTime cho crackles nếu vừa resume
					if (lightningObj.userData.pauseStartTime) {
						const pauseDuration = currentTime - lightningObj.userData.pauseStartTime;
						lightningObj.userData.startTime += pauseDuration;
						lightningObj.userData.pauseStartTime = null;
						console.log('🔧 Adjusted crackle timeline after pause:', pauseDuration + 'ms');
					}
					
					const elapsed = currentTime - lightningObj.userData.startTime;
					
					// Handle ground crackles (có duration property)
					if (lightningObj.userData.duration && lightningObj.userData.totalPoints) {
						const progress = Math.min(1.0, elapsed / lightningObj.userData.duration);
						
						if (progress >= 1.0) {
							// Remove finished crackle
							toRemove.push(lightningObj);
							scene.remove(lightningObj);
							if (lightningObj.userData.geometry) lightningObj.userData.geometry.dispose();
							if (lightningObj.userData.material) lightningObj.userData.material.dispose();
						} else {
							// Animate drawRange (first 60% of duration)
							const drawEndTime = lightningObj.userData.duration * 0.6;
							const drawProgress = Math.min(1.0, elapsed / drawEndTime);
							const targetCount = Math.min(lightningObj.userData.totalPoints, Math.ceil(lightningObj.userData.totalPoints * drawProgress / 2) * 2);
							lightningObj.userData.geometry.drawRange.count = targetCount;
							
							// Fade out (last 50% of duration)
							const fadeStartTime = lightningObj.userData.duration * 0.5;
							const fadeDuration = lightningObj.userData.duration * 0.5;
							const fadeProgress = Math.min(1.0, Math.max(0.0, (elapsed - fadeStartTime) / fadeDuration));
							lightningObj.userData.material.opacity = (1.0 - fadeProgress) * 1.0 * (0.9 + Math.random() * 0.1); // Sáng hơn
						}
					}
					
					// Animate continuous glow cho lightning bolts
					if (lightningObj.userData.glowSprite && lightningObj.userData.glowMaterial) {
						if (params.pauseScene) {
							// Giữ nguyên opacity khi pause
							return;
						}
						
						// Flickering effect
						const baseOpacity = 1.2;
						const flickering = 0.9 + Math.random() * 0.3;
						lightningObj.userData.glowMaterial.opacity = baseOpacity * flickering;
						
						// Scale pulsing
						const glowSize = 18;
						const time = elapsed * 0.005;
						const scale = glowSize * (1.0 + Math.sin(time) * 0.1);
						lightningObj.userData.glowSprite.scale.set(scale, scale, 1);
					}
					// Main lightning bolts are handled by their own animation timeline
				});
				
				// Remove finished effects - CHỈ khi không pause
				if (!params.pauseScene) {
				toRemove.forEach(obj => {
					const index = activeLightning.indexOf(obj);
					if (index > -1) activeLightning.splice(index, 1);
				});
				}

				// Update spherical glow ripples
				updateGlowRipples();

				
				// COMMENTED OUT: Không cần radar masking trong material nữa vì đã handle trong compute shader
				// Logic 2 trạng thái đã xử lý việc spawn trong radar ở compute shader
				/*
				if ( !radarMaskingApplied && radarTexture && globalPositionBuffer && globalRipplePositionBuffer && globalRippleEffect && globalRippleTimeBuffer ) {
					console.log('All conditions met, applying radar masking...');
					updateRainMaterialWithRadar( radarTexture );
					updateRippleMaterialWithRadar( radarTexture );
				} else if ( !radarMaskingApplied ) {
					console.log('Radar masking waiting for:', {
						radarTexture: !!radarTexture,
						globalPositionBuffer: !!globalPositionBuffer,
						globalRipplePositionBuffer: !!globalRipplePositionBuffer,
						globalRippleEffect: !!globalRippleEffect,
						globalRippleTimeBuffer: !!globalRippleTimeBuffer
					});
				}
				*/



				// position

				scene.overrideMaterial = collisionPosMaterial;
				renderer.setRenderTarget( collisionPosRT );
				renderer.render( scene, collisionCamera );

				// compute - CHỈ chạy khi không pause
				if (!params.pauseScene) {
				renderer.compute( computeParticles );
					
					// Cloud compute
					if (params.cloudEnabled && cloudComputeParticles) {
						renderer.compute( cloudComputeParticles );
					}
				}

				// result

				scene.overrideMaterial = null;
				renderer.setRenderTarget( null );
				renderer.render( scene, camera );

			}

		</script>
	</body>
</html>