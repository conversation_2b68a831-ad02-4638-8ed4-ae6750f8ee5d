{"root": true, "env": {"browser": true, "node": true, "es2018": true}, "parserOptions": {"ecmaVersion": 2018, "sourceType": "module"}, "extends": ["mdcs", "plugin:compat/recommended"], "plugins": ["html", "import"], "settings": {"polyfills": ["WebGL2RenderingContext"]}, "globals": {"__THREE_DEVTOOLS__": "readonly", "potpack": "readonly", "fflate": "readonly", "Stats": "readonly", "XRWebGLBinding": "readonly", "XRWebGLLayer": "readonly", "GPUShaderStage": "readonly", "GPUBufferUsage": "readonly", "GPUTextureUsage": "readonly", "GPUTexture": "readonly", "GPUMapMode": "readonly", "QUnit": "readonly", "Ammo": "readonly", "XRRigidTransform": "readonly", "XRMediaBinding": "readonly", "CodeMirror": "readonly", "esprima": "readonly", "jsonlint": "readonly", "VideoFrame": "readonly", "VideoDecoder": "readonly", "Float16Array": "readonly"}, "rules": {"no-throw-literal": ["error"], "quotes": ["error", "single"], "prefer-const": ["error", {"destructuring": "any", "ignoreReadBeforeAssign": false}], "no-irregular-whitespace": ["error"], "no-duplicate-imports": ["error"], "prefer-spread": "error", "valid-jsdoc": ["error", {"requireReturn": false, "requireReturnType": true, "requireParamDescription": false, "requireReturnDescription": false, "requireParamType": true, "preferType": {"Any": "any", "Boolean": "boolean", "Number": "number", "object": "Object", "String": "string"}}]}}