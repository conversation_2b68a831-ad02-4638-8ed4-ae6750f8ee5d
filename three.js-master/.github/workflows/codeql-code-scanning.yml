name: "Code<PERSON>"

on:
  push:
    branches: [ "dev" ]
  pull_request:
    # The branches below must be a subset of the branches above
    branches: [ "dev" ]
  schedule:
    - cron: '29 23 * * 0'
  workflow_dispatch:

jobs:
  analyze:
    name: Analyze
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write

    strategy:
      fail-fast: false
      matrix:
        language: [ 'javascript' ]

    steps:
    - name: Checkout repository
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4

    # Initializes the CodeQL tools for scanning.
    - name: Initialize CodeQL
      uses: github/codeql-action/init@39edc492dbe16b1465b0cafca41432d857bdb31a # v3
      with:
        languages: ${{ matrix.language }}
        config-file: ./.github/codeql-config.yml
        queries: security-and-quality

    - name: Autobuild
      uses: github/codeql-action/autobuild@39edc492dbe16b1465b0cafca41432d857bdb31a # v3

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@39edc492dbe16b1465b0cafca41432d857bdb31a # v3
      with:
        category: "/language:${{matrix.language}}"
