name: Read size

on:
  pull_request:
    paths:
      - 'src/**'
      - 'package.json'
      - 'utils/build/**'

# This workflow runs in a read-only environment. We can safely checkout
# the PR code here.
# Reference:
# https://securitylab.github.com/research/github-actions-preventing-pwn-requests/
permissions:
  contents: read

jobs:
  read-size:
    name: Tree-shaking
    runs-on: ubuntu-latest
    steps:
      - name: Git checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
      - name: Install Node
        uses: actions/setup-node@49933ea5288caeca8642d1e84afbd3f7d6820020 # v4
        with:
          node-version: 20
          cache: 'npm'
      - name: Install dependencies
        run: npm ci
      - name: Build
        run: npm run build
      - name: === Test tree-shaking ===
        run: npm run test-treeshake
      - name: Read bundle sizes
        id: read-size
        run: |
          WEBGL_FILESIZE=$(stat --format=%s build/three.module.min.js)
          gzip -k build/three.module.min.js
          WEBGL_FILESIZE_GZIP=$(stat --format=%s build/three.module.min.js.gz)
          WEBGL_TREESHAKEN=$(stat --format=%s test/treeshake/index.bundle.min.js)
          gzip -k test/treeshake/index.bundle.min.js
          WEBGL_TREESHAKEN_GZIP=$(stat --format=%s test/treeshake/index.bundle.min.js.gz)

          WEBGPU_FILESIZE=$(stat --format=%s build/three.webgpu.min.js)
          gzip -k build/three.webgpu.min.js
          WEBGPU_FILESIZE_GZIP=$(stat --format=%s build/three.webgpu.min.js.gz)
          WEBGPU_TREESHAKEN=$(stat --format=%s test/treeshake/index.webgpu.bundle.min.js)
          gzip -k test/treeshake/index.webgpu.bundle.min.js
          WEBGPU_TREESHAKEN_GZIP=$(stat --format=%s test/treeshake/index.webgpu.bundle.min.js.gz)

          WEBGPU_NODES_FILESIZE=$(stat --format=%s build/three.webgpu.nodes.min.js)
          gzip -k build/three.webgpu.nodes.min.js
          WEBGPU_NODES_FILESIZE_GZIP=$(stat --format=%s build/three.webgpu.nodes.min.js.gz)
          WEBGPU_NODES_TREESHAKEN=$(stat --format=%s test/treeshake/index.webgpu.nodes.bundle.min.js)
          gzip -k test/treeshake/index.webgpu.nodes.bundle.min.js
          WEBGPU_NODES_TREESHAKEN_GZIP=$(stat --format=%s test/treeshake/index.webgpu.nodes.bundle.min.js.gz)

          PR=${{ github.event.pull_request.number }}

          # write the output in a json file to upload it as artifact
          node -pe "JSON.stringify({ filesize: $WEBGL_FILESIZE, gzip: $WEBGL_FILESIZE_GZIP, treeshaken: $WEBGL_TREESHAKEN, treeshakenGzip: $WEBGL_TREESHAKEN_GZIP, filesize2: $WEBGPU_FILESIZE, gzip2: $WEBGPU_FILESIZE_GZIP, treeshaken2: $WEBGPU_TREESHAKEN, treeshakenGzip2: $WEBGPU_TREESHAKEN_GZIP, filesize3: $WEBGPU_NODES_FILESIZE, gzip3: $WEBGPU_NODES_FILESIZE_GZIP, treeshaken3: $WEBGPU_NODES_TREESHAKEN, treeshakenGzip3: $WEBGPU_NODES_TREESHAKEN_GZIP, pr: $PR })" > sizes.json
      - name: Upload artifact
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4
        with:
          name: sizes
          path: sizes.json
