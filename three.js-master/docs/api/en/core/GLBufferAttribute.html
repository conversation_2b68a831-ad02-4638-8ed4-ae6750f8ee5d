<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<base href="../../../" />
		<script src="page.js"></script>
		<link type="text/css" rel="stylesheet" href="page.css" />
	</head>
	<body>
		<h1>[name]</h1>

		<p class="desc">
			This buffer attribute class does not construct a VBO. Instead, it uses
			whatever VBO is passed in constructor and can later be altered via the
			`buffer` property.<br /><br />
			It is required to pass additional params alongside the VBO. Those are: the
			GL context, the GL data type, the number of components per vertex, the
			number of bytes per component, and the number of vertices.<br /><br />
			The most common use case for this class is when some kind of GPGPU
			calculation interferes or even produces the VBOs in question.
		</p>

		<h2>Examples</h2>
		<p>
			[example:webgl_buffergeometry_glbufferattribute Points with custom buffers]<br />
		</p>

		<h2>Constructor</h2>
		<h3>[name]( [param:WebGLBuffer buffer], [param:GLenum type], [param:Integer itemSize], [param:Integer elementSize], [param:Integer count], [param:Boolean normalized] )</h3>
		<p>
			`buffer` — Must be a
			[link:https://developer.mozilla.org/en-US/docs/Web/API/WebGLBuffer WebGLBuffer].
			<br />
			`type` — One of
			[link:https://developer.mozilla.org/en-US/docs/Web/API/WebGL_API/Constants#Data_types WebGL Data Types].
			<br />
			`itemSize` — The number of values of the array that should be associated
			with a particular vertex. For instance, if this attribute is storing a
			3-component vector (such as a position, normal, or color), then itemSize
			should be 3.
			<br />
			`elementSize` — 1, 2 or 4. The corresponding size (in bytes) for the given
			"type" param.
		</p>

		<ul>
			<li>gl.FLOAT: 4</li>
			<li>gl.UNSIGNED_SHORT: 2</li>
			<li>gl.SHORT: 2</li>
			<li>gl.UNSIGNED_INT: 4</li>
			<li>gl.INT: 4</li>
			<li>gl.BYTE: 1</li>
			<li>gl.UNSIGNED_BYTE: 1</li>
		</ul>
		`count` — The expected number of vertices in VBO.
		<br />
		`normalized` — (optional) Applies to integer data only.
			Indicates how the underlying data in the buffer maps to the values in the
			GLSL code. For instance, if [page:WebGLBuffer buffer] contains data of
			`gl.UNSIGNED_SHORT`, and [page:Boolean normalized] is true, the values `0 -
			+65535` in the buffer data will be mapped to 0.0f - +1.0f in the GLSL
			attribute. A `gl.SHORT` (signed) would map from -32768 - +32767 to -1.0f
			- +1.0f. If [page:Boolean normalized] is false, the values will be
			converted to floats unmodified, i.e. 32767 becomes 32767.0f.

		<h2>Properties</h2>

		<h3>[property:WebGLBuffer buffer]</h3>
		<p>
			The current
			[link:https://developer.mozilla.org/en-US/docs/Web/API/WebGLBuffer WebGLBuffer] instance.
		</p>

		<h3>[property:Integer count]</h3>
		<p>The expected number of vertices in VBO.</p>

		<h3>[property:Integer elementSize]</h3>
		<p>
			Stores the corresponding size in bytes for the current `type` property
			value.
		</p>
		<p>See above (constructor) for a list of known type sizes.</p>

		<h3>[property:Boolean isGLBufferAttribute]</h3>
		<p>Read-only. Always `true`.</p>

		<h3>[property:Integer itemSize]</h3>
		<p>How many values make up each item (vertex).</p>

		<h3>[property:String name]</h3>
		<p>
			Optional name for this attribute instance. Default is an empty string.
		</p>

		<h3>[property:Boolean needsUpdate]</h3>
		<p>
			Default is `false`. Setting this to true increments
			[page:GLBufferAttribute.version version].
		</p>

		<h3>[property:Boolean normalized]</h3>
		<p>
			Indicates how the underlying data in the buffer maps to the values in the
			GLSL shader code. See the constructor above for details.
		</p>

		<h3>[property:GLenum type]</h3>
		<p>
			A
			[link:https://developer.mozilla.org/en-US/docs/Web/API/WebGL_API/Constants#Data_types WebGL Data Type] describing the underlying VBO contents.
		</p>
		<p>
			Set this property together with `elementSize`. The recommended way is
			using the `setType` method.
		</p>

		<h3>[property:Integer version]</h3>
		<p>
			A version number, incremented every time the needsUpdate property is set
			to true.
		</p>

		<h2>Methods</h2>

		<h3>[method:this setBuffer]( buffer )</h3>
		<p>Sets the `buffer` property.</p>

		<h3>[method:this setType]( type, elementSize )</h3>
		<p>Sets the both `type` and `elementSize` properties.</p>

		<h3>[method:this setItemSize]( itemSize )</h3>
		<p>Sets the `itemSize` property.</p>

		<h3>[method:this setCount]( count )</h3>
		<p>Sets the `count` property.</p>

		<h2>Source</h2>
		<p>
			[link:https://github.com/mrdoob/three.js/blob/master/src/[path].js src/[path].js]
		</p>
	</body>
</html>
