<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<base href="../../../" />
		<script src="page.js"></script>
		<link type="text/css" rel="stylesheet" href="page.css" />
	</head>
	<body>
		<h1>[name]</h1>

		<p class="desc">An object with several loader utility functions.</p>

		<h2>Functions</h2>

		<h3>[method:String extractUrlBase]( [param:String url] )</h3>
		<p>[page:String url] — The url to extract the base url from.</p>
		<p>Extract the base from the URL.</p>

		<h3>
			[method:String resolveURL]( [param:String url], [param:String path] )
		</h3>
		<p>
			[page:String url] — The absolute or relative url resolve. [page:String path] — The base path for relative urls to be resolved against.
		</p>
		<p>
			Resolves relative urls against the given path. Absolute paths, data urls,
			and blob urls will be returned as is. Invalid urls will return an empty
			string.
		</p>

		<h2>Source</h2>
		<p>
			[link:https://github.com/mrdoob/three.js/blob/master/src/[path].js src/[path].js]
		</p>
	</body>
</html>
