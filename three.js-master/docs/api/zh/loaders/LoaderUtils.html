<!DOCTYPE html>
<html lang="zh">
	<head>
		<meta charset="utf-8" />
		<base href="../../../" />
		<script src="page.js"></script>
		<link type="text/css" rel="stylesheet" href="page.css" />
	</head>
	<body>
		<h1>[name]</h1>

		<p class="desc">具有多个实用的加载器函数功能的对象</p>

		<h2>函数</h2>

		<h3>[method:String extractUrlBase]( [param:String url] )</h3>
		<p>
		[page:String url] — 从基本URL中，进行提取的URL。
		</p>
		<p>
		从URL中提取基本信息。
		</p>


		<h2>源</h2>

		<p>
			[link:https://github.com/mrdoob/three.js/blob/master/src/[path].js src/[path].js]
		</p>
	</body>
</html>
