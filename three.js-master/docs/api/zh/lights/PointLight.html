<!DOCTYPE html>
<html lang="zh">
	<head>
		<meta charset="utf-8" />
		<base href="../../../" />
		<script src="page.js"></script>
		<link type="text/css" rel="stylesheet" href="page.css" />
	</head>
	<body>
		[page:Object3D] &rarr; [page:Light] &rarr;

		<h1>点光源（[name]）</h1>

		<p class="desc">
			从一个点向各个方向发射的光源。一个常见的例子是模拟一个灯泡发出的光。
			<br /><br />
			该光源可以投射阴影 - 跳转至 [page:PointLightShadow] 查看更多细节。
		</p>

		<h2>代码示例</h2>

		<code>
		const light = new THREE.PointLight( 0xff0000, 1, 100 );
		light.position.set( 50, 50, 50 );
		scene.add( light );
		</code>

		<h2>例子</h2>

		<p>
			[example:webgpu_lights_pointlights lights / pointlights ]<br />
			[example:webgl_effects_anaglyph effects / anaglyph ]<br />
			[example:webgl_geometry_text geometry / text ]<br />
			[example:webgl_lensflares lensflares ]
		</p>

		<h2>构造器（Constructor）</h2>

		<h3>[name]( [param:Color color], [param:Float intensity], [param:Number distance], [param:Float decay] )</h3>
		<p>
			[page:Color color] -（可选）一个表示颜色的 Color 的实例、字符串或数字，默认为一个白色（0xffffff）的 [page:Color Color] 对象。<br />
			[page:Float intensity] -（可选）光照强度。默认值为 1。<br />
			[page:Number distance] - 光源照射的最大距离。默认值为 0（无限远）。<br />
			[page:Float decay] - 沿着光照距离的衰退量。默认值为 2。<br /><br />

			创建一个新的点光源（PointLight）。
		</p>

		<h2>属性（Properties）</h2>
		<p>
			公共属性请查看基类 [page:Light Light]。
		</p>

		<h3>[property:Boolean castShadow]</h3>
		<p>
			此属性设置为 `true` 灯光将投射阴影。*注意*：这样做的代价比较高，需要通过调整让阴影看起来正确。
			查看 [page:PointLightShadow] 了解详细信息。
			默认值为 `false`。
		</p>

		<h3>[property:Float decay]</h3>
		<p>
			光线随着距离增加变暗的衰减量。默认值为 `2`。<br />
			在物理正确渲染的上下文中，不应更改默认值。
		</p>

		<h3>[property:Float distance]</h3>
		<p>
			当值为零时，光线将根据平方反比定律衰减到无限远。
			当值不为零时，光线会先按照平方反比定律衰减，直到距离截止点附近，然后线性衰减到 0。
		</p>
		<p>默认值为 `0.0`。</p>

		<h3>[property:Float intensity]</h3>
		<p>
			光源的强度。默认值为 `1`。<br />
			单位是坎德拉（cd）。<br /><br />
			改变该值会影响到 `power` 的值。
		</p>

		<h3>[property:Float power]</h3>
		<p>
			光源的功率。<br />
			单位为流明（lm）。<br /><br />
			改变该值会影响到 `intensity` 的值。
		</p>

		<h3>[property:PointLightShadow shadow]</h3>
		<p>
			[page:PointLightShadow] 对象，用与计算此光照的阴影。<br /><br />

			此对象的摄像机为：一个 [page:PerspectiveCamera.fov fov] 值为90度、[page:PerspectiveCamera.aspect aspect] 值为 1、
			[page:PerspectiveCamera.near near] 值为 0、[page:PerspectiveCamera.far far]
			值为 500 的透视摄像机（[page:PerspectiveCamera]）。
		</p>

		<h2>方法（Methods）</h2>
		<p>
			公共方法请查看基类 [page:Light Light]。
		</p>

		<h3>[method:undefined dispose]()</h3>
		<p>
			释放由该实例分配的 GPU 相关资源。 当这个实例不再在你的应用中使用时，调用这个方法。
		</p>

		<h3>[method:this copy]( [param:PointLight source] )</h3>
		<p>
			将所有属性的值从源 [page:PointLight source] 复制到此点光源对象。
		</p>

		<h2>Source</h2>

		<p>
			[link:https://github.com/mrdoob/three.js/blob/master/src/[path].js src/[path].js]
		</p>
	</body>
</html>
