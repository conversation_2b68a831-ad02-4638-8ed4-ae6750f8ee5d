<!DOCTYPE html>
<html lang="ar">
	<head>
		<meta charset="utf-8" />
		<base href="../../../" />
		<script src="page.js"></script>
		<link type="text/css" rel="stylesheet" href="page.css" />
	</head>
	<body class="rtl">
		<h1>[name]</h1>

		<p class="desc">كائن يحتوي على العديد من وظائف المحمل المساعدة.</p>

		<h2>الطرق (Methods)</h2>
	 	 
		<h3>[method:String extractUrlBase]( [param:String url] )</h3>
		<p>[page:String url] — عنوان url الذي سيتم استخراج العنوان الأساسي منه.</p>
		<p>استخراج الأساس من عنوان URL.</p>
	 
		<h3>
		[method:String resolveURL]( [param:String url], [param:String path] )
		</h3>
		<p>
		[page:String url] — عنوان url المطلق أو النسبي للحل. [page:String path] — المسار الأساسي لحل عناوين url النسبية ضده.
		</p>
		<p>
		يحل عناوين url النسبية ضد المسار المعطى. ستعاد المسارات المطلقة وعناوين url للبيانات
		وعناوين url للكتل كما هي. ستعود عناوين url غير صالحة
		سلسلة فارغة.
		</p>

		<h2>المصدر (Source)</h2>
		<p>
			[link:https://github.com/mrdoob/three.js/blob/master/src/[path].js src/[path].js]
		</p>
	</body>
</html>
