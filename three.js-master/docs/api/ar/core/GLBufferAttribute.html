<!DOCTYPE html>
<html lang="ar">
	<head>
		<meta charset="utf-8" />
		<base href="../../../" />
		<script src="page.js"></script>
		<link type="text/css" rel="stylesheet" href="page.css" />
	</head>
	<body class="rtl">
		<h1>[name]</h1>
	
		<p class="desc">
			لا يقوم هذا الفئة السمة المخزنة بإنشاء VBO. بدلاً من ذلك، يستخدم
			أي VBO يتم تمريره في المُنشئ ويمكن تغييره لاحقًا عبر خاصية
			`buffer`.<br /><br />
			من الضروري تمرير معلمات إضافية جنبًا إلى جنب مع VBO. هذه هي: GL
			context، نوع البيانات GL، عدد المكونات لكل رأس، عدد البايتات لكل
			مكون، وعدد الرؤوس.<br /><br />
			أكثر حالات الاستخدام شيوعًا لهذه الفئة هي عندما يتداخل نوع من
			حسابات GPGPU أو حتى ينتج VBOs المعنية.
		</p>

		<h2>Examples</h2>
		<p>
			[example:webgl_buffergeometry_glbufferattribute Points with custom buffers]<br />
		</p>

		<h2>المنشئ (Constructor)</h2>
		<h3>[name]( [param:WebGLBuffer buffer], [param:GLenum type], [param:Integer itemSize], [param:Integer elementSize], [param:Integer count], [param:Boolean normalized] )</h3>
		<p>
			`buffer` — يجب أن يكون
			[link:https://developer.mozilla.org/en-US/docs/Web/API/WebGLBuffer WebGLBuffer].
			<br />
			`type` — واحد من
			[link:https://developer.mozilla.org/en-US/docs/Web/API/WebGL_API/Constants#Data_types أنواع بيانات WebGL].
			<br />
			`itemSize` — عدد قيم المصفوفة التي يجب ربطها برأس معين. على سبيل
			المثال، إذا كانت هذه السمة تخزن متجهًا ثلاثي الأبعاد (مثل الموضع،
			 الطبيعي، أو اللون)، فيجب أن يكون itemSize هو 3.
			 <br />
			 `elementSize` — 1، 2 أو 4. الحجم المقابل (بالبايت) للمعلمة "type"
			 المعطاة.
		</p>
	
		<ul>
			<li>gl.FLOAT: 4</li>
			<li>gl.UNSIGNED_SHORT: 2</li>
			<li>gl.SHORT: 2</li>
			<li>gl.UNSIGNED_INT: 4</li>
			<li>gl.INT: 4</li>
			<li>gl.BYTE: 1</li>
			<li>gl.UNSIGNED_BYTE: 1</li>
		</ul>
		<p>`count` — عدد الرؤوس المتوقع في VBO.</p>
		<p>
		`normalized` — (optional) Applies to integer data only.
			Indicates how the underlying data in the buffer maps to the values in the
			GLSL code. For instance, if [page:WebGLBuffer buffer] contains data of
			`gl.UNSIGNED_SHORT`, and [page:Boolean normalized] is true, the values `0 -
			+65535` in the buffer data will be mapped to 0.0f - +1.0f in the GLSL
			attribute. A `gl.SHORT` (signed) would map from -32768 - +32767 to -1.0f
			- +1.0f. If [page:Boolean normalized] is false, the values will be
			converted to floats unmodified, i.e. 32767 becomes 32767.0f.
		</p>
	
		<h2>الخصائص (Properties)</h2>
	
		<h3>[property:WebGLBuffer buffer]</h3>
		<p>
			نسخة
			[link:https://developer.mozilla.org/en-US/docs/Web/API/WebGLBuffer WebGLBuffer] الحالية.
		</p>
	
		<h3>[property:Integer count]</h3>
		<p>عدد الرؤوس المتوقع في VBO.</p>
	
		<h3>[property:Integer elementSize]</h3>
		<p>
			يخزن الحجم المقابل بالبايت لقيمة خاصية `type` الحالية.
		</p>
		<p>انظر أعلاه (المُنشئ) لقائمة بأحجام الأنواع المعروفة.</p>
	
		<h3>[property:Boolean isGLBufferAttribute]</h3>
		<p>للقراءة فقط. دائمًا `true`.</p>
	
		<h3>[property:Integer itemSize]</h3>
		<p>كم عدد القيم التي تشكل كل عنصر (رأس).</p>
	
		<h3>[property:String name]</h3>
		<p>
			اسم اختياري لهذه الحالة من السمة. الافتراضي هو سلسلة فارغة.
		</p>
	
		<h3>[property:Boolean needsUpdate]</h3>
		<p>
			الافتراضي هو `false`. تعيين هذا إلى true يزاد
			[page:GLBufferAttribute.version version].
		</p>

		<h3>[property:Boolean normalized]</h3>
		<p>
			Indicates how the underlying data in the buffer maps to the values in the
			GLSL shader code. See the constructor above for details.
		</p>
	
		<h3>[property:GLenum type]</h3>
		<p>
			[link:https://developer.mozilla.org/en-US/docs/Web/API/WebGL_API/Constants#Data_types نوع بيانات WebGL] يصف محتوى VBO الأساسي.
		</p>
		<p>
			قم بتعيين هذه الخاصية معًا مع `elementSize`. الطريقة المستحسنة هي
			باستخدام طريقة `setType`.
		</p>
	
		<h3>[property:Integer version]</h3>
		<p>
			رقم إصدار، يزاد كل مرة يتم فيها تعيين خاصية needsUpdate على true.
		</p>
	
		<h2>الوظائف (Methods)</h2>
	
		<h3>[method:this setBuffer]( buffer )</h3>
		<p>تضبط خاصية `buffer`.</p>
	
		<h3>[method:this setType]( type, elementSize )</h3>
		<p>تضبط كلاً من خصائص `type` و `elementSize`.</p>
	
		<h3>[method:this setItemSize]( itemSize )</h3>
		<p>تضبط خاصية `itemSize`.</p>
	
		<h3>[method:this setCount]( count )</h3>
		<p>تضبط خاصية `count`.</p>

		<h2>المصدر (Source)</h2>
		<p>
			[link:https://github.com/mrdoob/three.js/blob/master/src/[path].js src/[path].js]
		</p>
	</body>
</html>
