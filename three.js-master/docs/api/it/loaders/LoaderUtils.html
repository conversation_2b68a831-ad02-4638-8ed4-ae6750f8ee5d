<!DOCTYPE html>
<html lang="it">
	<head>
		<meta charset="utf-8" />
		<base href="../../../" />
		<script src="page.js"></script>
		<link type="text/css" rel="stylesheet" href="page.css" />
	</head>
	<body>
		<h1>[name]</h1>

		<p class="desc">Un oggetto con diverse funzioni di utilità del loader.</p>

		<h2>Funzioni</h2>

		<h3>[method:String extractUrlBase]( [param:String url] )</h3>
		<p>
		[page:String url] —  La url da cui estrarre la url di base.
		</p>
		<p>
      Estrae la base dalla URL.
		</p>


		<h3>[method:String resolveURL]( [param:String url], [param:String path]  )</h3>
		<p>
		[page:String url] —  La risoluzione dell'url assoluto o relativo.<br />
		[page:String path] —  Il percorso base per le url relative da risolvere.
		</p>
		<p>
      Risolve gli url relativi rispetto al percorso dato. Percorsi assoluti, url di dati e url di blob verranno restituiti così come sono.
      Le url invalide verranno restituite come stringhe vuote. 
		</p>


		<h2>Source</h2>

		<p>
			[link:https://github.com/mrdoob/three.js/blob/master/src/[path].js src/[path].js]
		</p>
	</body>
</html>
