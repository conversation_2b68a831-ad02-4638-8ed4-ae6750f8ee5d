<!DOCTYPE html>
<html lang="it">
	<head>
		<meta charset="utf-8" />
		<base href="../../../" />
		<script src="page.js"></script>
		<link type="text/css" rel="stylesheet" href="page.css" />
	</head>
	<body>
		<h1>[name]</h1>

		<p class="desc">
      Questa classe di attributi del buffer non costruisce un VBO. Invece,
      utilizza qualsiasi VBO che gli viene passato nel costruttore e può essere
      successivamente alterato tramite la proprietà `buffer`.<br /><br />
      È necessario passare parametri aggiuntivi insieme a VBO. I quali sono:
      il contesto GL, il tipo di dati GL, il numero di componenti per vertice,
      il numero di byte per componente, e il numero di vertici. <br /><br />
      Il caso d'uso più comune per questa classe è quando un qualche tipo di
      calcolo GPGPU interferisce o addirittura produce i VBO in questione.
		</p>

		<h2>Examples</h2>
		<p>
			[example:webgl_buffergeometry_glbufferattribute Points with custom buffers]<br />
		</p>

		<h2>Costruttore</h2>
		<h3>[name]( [param:WebGLBuffer buffer], [param:GLenum type], [param:Integer itemSize], [param:Integer elementSize], [param:Integer count], [param:Boolean normalized] )</h3>
		<p>
		`buffer` — Deve essere un [link:https://developer.mozilla.org/en-US/docs/Web/API/WebGLBuffer WebGLBuffer].
		<br />
		`type` — Uno dei [link:https://developer.mozilla.org/en-US/docs/Web/API/WebGL_API/Constants#Data_types Tipi di dati WebGL].
		<br />
		`itemSize` — Il numero dei valori dell'array che devono essere associati con un particolare vertice. Ad esempio,
    se questo attributo memorizza un vettore a 3 componenti (come una posizione, una normale, un colore), allora itemSize dovrebbe essere 3.
		<br />
		`elementSize` — 1, 2 o 4. La dimensione corrispondente (in byte) per il parametro "type" passato.
		<ul>
			<li>gl.FLOAT: 4</li>
			<li>gl.UNSIGNED_SHORT: 2</li>
			<li>gl.SHORT: 2</li>
			<li>gl.UNSIGNED_INT: 4</li>
			<li>gl.INT: 4</li>
			<li>gl.BYTE: 1</li>
			<li>gl.UNSIGNED_BYTE: 1</li>
		</ul>
		`count` — Il numero previsto di vertici in VBO.
		<br />
		`normalized` — (optional) Applies to integer data only.
			Indicates how the underlying data in the buffer maps to the values in the
			GLSL code. For instance, if [page:WebGLBuffer buffer] contains data of
			`gl.UNSIGNED_SHORT`, and [page:Boolean normalized] is true, the values `0 -
			+65535` in the buffer data will be mapped to 0.0f - +1.0f in the GLSL
			attribute. A `gl.SHORT` (signed) would map from -32768 - +32767 to -1.0f
			- +1.0f. If [page:Boolean normalized] is false, the values will be
			converted to floats unmodified, i.e. 32767 becomes 32767.0f.
		</p>

		<h2>Proprietà</h2>

		<h3>[property:WebGLBuffer buffer]</h3>
		<p>
			L'istanza corrente di [link:https://developer.mozilla.org/en-US/docs/Web/API/WebGLBuffer WebGLBuffer].
		</p>

		<h3>[property:Integer count]</h3>
		<p>
      Il numero previsto di vertici in VBO.
		</p>

		<h3>[property:Integer elementSize]</h3>
		<p>
      Memorizza la dimensione corrispondente in byte per il valore della proprietà del `type` corrente.
		</p>
		<p>
      Vedi sopra (costruttore) per un elenco di dimensioni di type conosciute.
		</p>

		<h3>[property:Boolean isGLBufferAttribute]</h3>
		<p>
      Solo lettura. Sempre `true`.
		</p>

		<h3>[property:Integer itemSize]</h3>
		<p>
      Quanti valori compongono ogni elemento (vertice).
		</p>

		<h3>[property:String name]</h3>
		<p>
      Un nome opzionale per questa istanza dell'attributo. Il valore predefinito è una stringa vuota.
		</p>

		<h3>[property:Boolean needsUpdate]</h3>
		<p>
      Il valore predefinito è `false`. Impostando questo metodo a true incrementa la [page:GLBufferAttribute.version versione].
		</p>

		<h3>[property:Boolean normalized]</h3>
		<p>
			Indicates how the underlying data in the buffer maps to the values in the
			GLSL shader code. See the constructor above for details.
		</p>

		<h3>[property:GLenum type]</h3>
		<p>
      Un [link:https://developer.mozilla.org/en-US/docs/Web/API/WebGL_API/Constants#Data_types WebGL Data Type]
      che descrive i contenuti VBO.
		</p>
		<p>
      Imposta questa proprietà insieme a `elementSize`. Il modo consigliato è
      di usare il metodo `setType`.
		</p>

		<h3>[property:Integer version]</h3>
		<p>
      Un numero di versione, incrementato ogni volta che la proprietà needsUpdate è impostata a true.
		</p>

		<h2>Metodi</h2>

		<h3>[method:this setBuffer]( buffer ) </h3>
		<p>Imposta la proprietà `buffer`.</p>

		<h3>[method:this setType]( type, elementSize ) </h3>
		<p>Imposta entrambe le proprietà `type` e `elementSize`.</p>

		<h3>[method:this setItemSize]( itemSize ) </h3>
		<p>Imposta la proprietà `itemSize`.</p>

		<h3>[method:this setCount]( count ) </h3>
		<p>Imposta la proprietà `count`.</p>

		<h2>Source</h2>
		<p>
			[link:https://github.com/mrdoob/three.js/blob/master/src/[path].js src/[path].js]
		</p>
	</body>
</html>
