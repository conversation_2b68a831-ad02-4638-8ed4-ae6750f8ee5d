<!DOCTYPE html>
<html lang="ko">
	<head>
		<meta charset="utf-8" />
		<base href="../../../" />
		<script src="page.js"></script>
		<link type="text/css" rel="stylesheet" href="page.css" />
	</head>
	<body>
		<h1>[name]</h1>

		<p class="desc">
			이 버퍼 속성 클래스는 VBO를 생성하지 않습니다. 대신, VBO가 생성자로 전달되고 난 후의 모든 것을 사용하며 후에 *buffer* 속성으로 변경될 수 있습니다.<br /><br />
			VBO와 나란히 추가적인 파라미터 전달이 필요합니다. 해당 파라미터는 다음과 같습니다:
			GL 구조체, GL 데이터 타입, 꼭짓점 당 컴포넌트 갯수,
			컴포넌트 당 바이트 수, 꼭짓점의 수.<br /><br />
			이 클래스의 가장 일반적인 사용 사례는 어떤 종류의 GPGPU 계산이 해당 VBO를 방해하거나 심지어 생성하는 경우입니다.
		</p>

		<h2>Examples</h2>
		<p>
			[example:webgl_buffergeometry_glbufferattribute Points with custom buffers]<br />
		</p>

		<h2>생성자</h2>
		<h3>[name]( [param:WebGLBuffer buffer], [param:GLenum type], [param:Integer itemSize], [param:Integer elementSize], [param:Integer count], [param:Boolean normalized] )</h3>
		<p>
		*buffer* — 반드시 [link:https://developer.mozilla.org/en-US/docs/Web/API/WebGLBuffer WebGLBuffer]여야 합니다.
		<br />
		*type* — [link:https://developer.mozilla.org/en-US/docs/Web/API/WebGL_API/Constants#Data_types WebGL] 데이터 타입 중 하나.
		<br />
		*itemSize* — 특정 꼭짓점가 연관되어야 하는 배열의 값의 수. 예를 들어 이 속성이 3-컴포넌트 벡터(예: 위치, 법선 또는 색상)를 저장하는 경우 (
			itemSize는 3이어야 합니다.
		<br />
		*elementSize* — 1, 2 혹은 4. 할당된 "type" 파라미터에 상응하는 사이즈(바이트).
		<ul>
			<li>gl.FLOAT: 4</li>
			<li>gl.UNSIGNED_SHORT: 2</li>
			<li>gl.SHORT: 2</li>
			<li>gl.UNSIGNED_INT: 4</li>
			<li>gl.INT: 4</li>
			<li>gl.BYTE: 1</li>
			<li>gl.UNSIGNED_BYTE: 1</li>
		</ul>
		*count* — 예상되는 VBO의 꼭짓점 수.
		<br />
		*normalized* — (optional) Applies to integer data only.
			Indicates how the underlying data in the buffer maps to the values in the
			GLSL code. For instance, if [page:WebGLBuffer buffer] contains data of
			`gl.UNSIGNED_SHORT`, and [page:Boolean normalized] is true, the values `0 -
			+65535` in the buffer data will be mapped to 0.0f - +1.0f in the GLSL
			attribute. A `gl.SHORT` (signed) would map from -32768 - +32767 to -1.0f
			- +1.0f. If [page:Boolean normalized] is false, the values will be
			converted to floats unmodified, i.e. 32767 becomes 32767.0f.
		</p>

		<h2>프로퍼티</h2>

		<h3>[property:WebGLBuffer buffer]</h3>
		<p>
			현재의 [link:https://developer.mozilla.org/en-US/docs/Web/API/WebGLBuffer WebGLBuffer] 인스턴스.
		</p>

		<h3>[property:Integer count]</h3>
		<p>
			VBO의 꼭짓점 수.
		</p>

		<h3>[property:Integer elementSize]</h3>
		<p>
			현재의 *type* 속성 값에 맞는 바이트 사이즈를 저장.
		</p>
		<p>
			알려진 타입 크기 리스트는 위의 (생성자)를 참고.
		</p>

		<h3>[property:Boolean isGLBufferAttribute]</h3>
		<p>
			읽기 전용. 언제나 *true*입니다.
		</p>

		<h3>[property:Integer itemSize]</h3>
		<p>
			각 항목을 구성하는 값의 크기 (꼭짓점).
		</p>

		<h3>[property:String name]</h3>
		<p>
		이 속성 인스턴스의 임시 이름. 기본값은 빈 문자열입니다.
		</p>

		<h3>[property:Boolean needsUpdate]</h3>
		<p>
		기본값은 *false* 입니다. true로 설정하면 [page:GLBufferAttribute.version version]을 증가시킵니다.
		</p>

		<h3>[property:Boolean normalized]</h3>
		<p>
			Indicates how the underlying data in the buffer maps to the values in the
			GLSL shader code. See the constructor above for details.
		</p>

		<h3>[property:GLenum type]</h3>
		<p>
			기저의 VBO 컨텐츠를 묘사하는 [link:https://developer.mozilla.org/en-US/docs/Web/API/WebGL_API/Constants#Data_types WebGL Data Type]
			.
		</p>
		<p>
			*elementSize*와 함께 이 속성을 설정합니다. 추천하는 방법은 *setType* 메서드를 사용하는 것입니다.
		</p>

		<h3>[property:Integer version]</h3>
		<p>
		버전 넘버이며 needsUpdate 속성이 true가 될 때마다 증가합니다.
		</p>

		<h2>메서드</h2>

		<h3>[method:this setBuffer]( buffer ) </h3>
		<p>*buffer* 속성을 설정합니다.</p>

		<h3>[method:this setType]( type, elementSize ) </h3>
		<p>*type* 및 *elementSize* 속성을 설정합니다.</p>

		<h3>[method:this setItemSize]( itemSize ) </h3>
		<p>*itemSize* 속성을 설정합니다.</p>

		<h3>[method:this setCount]( count ) </h3>
		<p>*count* 속성을 설정합니다.</p>

		<h2>소스코드</h2>
		<p>
			[link:https://github.com/mrdoob/three.js/blob/master/src/[path].js src/[path].js]
		</p>
	</body>
</html>
