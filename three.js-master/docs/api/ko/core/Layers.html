<!DOCTYPE html>
<html lang="ko">
	<head>
		<meta charset="utf-8" />
		<base href="../../../" />
		<script src="page.js"></script>
		<link type="text/css" rel="stylesheet" href="page.css" />
	</head>
	<body>
		<h1>[name]</h1>

		<p class="desc">
			[page:Layers] 오브젝트는 [page:Object3D]를 1개에서 32개의 레이어에 0에서 31의 숫자로 할당합니다.
			- 내부적으로 레이어들은 [link:https://en.wikipedia.org/wiki/Mask_(computing) bit mask]로 저장되며, 기본값으로 모든
			Object3D들은 0번 레이어의 멤버입니다. <br /><br />

			가시성을 컨트롤하는데에 사용할 수 있습니다. - 카메라의 뷰가 렌더링될 때 오브젝트는 반드시 [page:Camera camera]와 가시 레이어를 공유해야합니다.<br /><br />

			[page:Object3D]로부터 상속받은 모든 클래스들은 [page:Object3D.layers] 프로퍼티를 가지고 있으며 이는 이 클래스의 인스턴스입니다.
		</p>

		<h2>예제</h2>

		<p>[example:webgpu_layers WebGPU / layers]</p>

		<h2>생성자</h2>


		<h3>[name]()</h3>
		<p>
			새 레이어 오브젝트를 만들고 최초 멤버 레이어 0을 설정합니다.
		</p>

		<h2>프로퍼티</h2>

		<h3>[property:Integer mask]</h3>
		<p>
			현재 레이어가 속해있는 32 레이어들의 비트 마스크를 저장하고 있습니다.
		</p>


		<h2>메서드</h2>

		<h3>[method:undefined disable]( [param:Integer layer] )</h3>
		<p>
			layer - 0부터 31까지의 정수.<br /><br />

			이 *layer*의 멤버 속성을 제거합니다.
		</p>

		<h3>[method:undefined enable]( [param:Integer layer] )</h3>
		<p>
			layer - 0부터 31까지의 정수.<br /><br />

			이 *layer*에 멤버 속성을 추가합니다.
		</p>

		<h3>[method:undefined set]( [param:Integer layer] )</h3>
		<p>
			layer - 0부터 31까지의 정수.<br /><br />

			*layer*의 멤버 속성을 설정하고, 다른 모든 레이어들의 멤버 속성을 제거합니다.
		</p>

		<h3>[method:Boolean test]( [param:Layers layers] )</h3>
		<p>
			layers - 레이어 오브젝트<br /><br />

			전달받은 *layers* 오브젝트가 적어도 1개 이상의 레이어를 가지고 있으면 true를 리턴합니다.
		</p>

		<h3>[method:Boolean isEnabled]( [param:Integer layer] )</h3>
		<p>
			layer - an integer from 0 to 31.<br /><br />

			Returns true if the given layer is enabled.
		</p>

		<h3>[method:undefined toggle]( [param:Integer layer] )</h3>
		<p>
			layer - 0부터 31까지의 정수.<br /><br />

			ㄴ*layer*의 멤버 속성을 전환합니다.
		</p>

		<h3>[method:undefined enableAll]()</h3>
		<p>
			모든 레이어에 멤버 속성을 추가합니다.
		</p>

		<h3>[method:undefined disableAll]()</h3>
		<p>
			모든 레이어의 멤버 속성을 제거합니다.
		</p>

		<h2>소스코드</h2>

		<p>
			[link:https://github.com/mrdoob/three.js/blob/master/src/[path].js src/[path].js]
		</p>
	</body>
</html>
